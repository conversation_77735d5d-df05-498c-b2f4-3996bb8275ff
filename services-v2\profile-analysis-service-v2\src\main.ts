/**
 * Profile Analysis Service V2 - Main Entry Point
 * 
 * Clean implementation with Database Per Service Pattern
 * Uses profile_analysis_service_v2 database only
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
  });

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Profile Analysis Service V2 API')
    .setDescription('Profile Analysis Service V2 - AI-powered social media profile analysis')
    .setVersion('2.0.0')
    .addBearerAuth()
    .addTag('analysis', 'Profile analysis operations')
    .addTag('insights', 'Analysis insights and recommendations')
    .addTag('metrics', 'Profile metrics and statistics')
    .addTag('health', 'Health check operations')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Start server
  const port = process.env.SERVICE_PORT || 4002;
  await app.listen(port);

  console.log(`🚀 Profile Analysis Service V2 is running on: http://localhost:${port}`);
  console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  console.log(`🏥 Health Check: http://localhost:${port}/health/simple`);
  console.log(`🧠 AI-Powered Profile Analysis Ready`);
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start Profile Analysis Service V2:', error);
  process.exit(1);
});
