/**
 * Metrics Interfaces
 */

/**
 * Metric tags interface
 */
export interface MetricTags {
  service?: string;
  version?: string;
  environment?: string;
  method?: string;
  endpoint?: string;
  statusCode?: string;
  operation?: string;
  entity?: string;
  [key: string]: string | number | boolean | undefined;
}

/**
 * Metric value interface
 */
export interface MetricValue {
  value: number;
  timestamp: number;
  tags?: MetricTags;
}

/**
 * Counter metric interface
 */
export interface CounterMetric {
  name: string;
  help: string;
  value: number;
  tags?: MetricTags;
}

/**
 * Gauge metric interface
 */
export interface GaugeMetric {
  name: string;
  help: string;
  value: number;
  tags?: MetricTags;
}

/**
 * Histogram metric interface
 */
export interface HistogramMetric {
  name: string;
  help: string;
  buckets: number[];
  values: number[];
  count: number;
  sum: number;
  tags?: MetricTags;
}

/**
 * Summary metric interface
 */
export interface SummaryMetric {
  name: string;
  help: string;
  quantiles: Record<number, number>;
  count: number;
  sum: number;
  tags?: MetricTags;
}

/**
 * Metric configuration interface
 */
export interface MetricConfiguration {
  enabled: boolean;
  prefix: string;
  defaultTags: MetricTags;
  collectors: MetricCollector[];
  exporters: MetricExporter[];
}

/**
 * Metric collector interface
 */
export interface MetricCollector {
  name: string;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
  help: string;
  labelNames?: string[];
  buckets?: number[];
  quantiles?: number[];
}

/**
 * Metric exporter interface
 */
export interface MetricExporter {
  type: 'prometheus' | 'statsd' | 'cloudwatch' | 'datadog';
  endpoint?: string;
  interval?: number;
  options?: Record<string, any>;
}

/**
 * Performance metric interface
 */
export interface PerformanceMetric {
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  tags?: MetricTags;
  metadata?: Record<string, any>;
}

/**
 * Business metric interface
 */
export interface BusinessMetric {
  name: string;
  value: number;
  unit: string;
  dimension: string;
  tags?: MetricTags;
  timestamp: number;
}

/**
 * System metric interface
 */
export interface SystemMetric {
  cpu: {
    usage: number;
    load: number[];
  };
  memory: {
    used: number;
    total: number;
    free: number;
    usage: number;
  };
  disk: {
    used: number;
    total: number;
    free: number;
    usage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  timestamp: number;
}

/**
 * Application metric interface
 */
export interface ApplicationMetric {
  requests: {
    total: number;
    rate: number;
    errors: number;
    errorRate: number;
  };
  response: {
    averageTime: number;
    p50: number;
    p95: number;
    p99: number;
  };
  database: {
    connections: number;
    queries: number;
    queryTime: number;
  };
  cache: {
    hits: number;
    misses: number;
    hitRate: number;
  };
  timestamp: number;
}
