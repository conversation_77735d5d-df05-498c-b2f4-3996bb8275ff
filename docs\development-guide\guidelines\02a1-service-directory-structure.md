# 🏗️ Service Directory Structure Standards

## Overview
Standardized directory structure for all microservices in the Social NFT Platform to ensure consistency and maintainability.

## Standard Service Structure

### Root Level Structure
```
service-name/
├── src/                          # Source code
├── tests/                        # Integration tests
├── config/                       # Configuration files
├── migrations/                   # Database migrations
├── scripts/                      # Utility scripts
├── docs/                         # Service documentation
├── Dockerfile                    # Container configuration
├── docker-compose.yml           # Local development
├── package.json                 # Dependencies and scripts
├── tsconfig.json                # TypeScript configuration
├── nest-cli.json                # NestJS CLI configuration
├── .env.example                 # Environment variables template
├── .gitignore                   # Git ignore rules
└── README.md                    # Service documentation
```

### Source Code Organization (Feature-Based)
```
src/
├── main.ts                      # Application entry point
├── app.module.ts                # Root application module
├── app.controller.ts            # Root controller
├── app.service.ts               # Root service
├── feature-1/                   # Business feature module
│   ├── controllers/
│   │   ├── feature-1.controller.ts
│   │   └── feature-1.controller.spec.ts
│   ├── services/
│   │   ├── feature-1.service.ts
│   │   └── feature-1.service.spec.ts
│   ├── repositories/
│   │   ├── feature-1.repository.ts
│   │   └── feature-1.repository.spec.ts
│   ├── dto/
│   │   ├── create-feature-1.dto.ts
│   │   ├── update-feature-1.dto.ts
│   │   └── feature-1-response.dto.ts
│   ├── entities/
│   │   └── feature-1.entity.ts
│   ├── interfaces/
│   │   └── feature-1.interface.ts
│   ├── guards/
│   │   └── feature-1.guard.ts
│   ├── interceptors/
│   │   └── feature-1.interceptor.ts
│   └── feature-1.module.ts
├── feature-2/                   # Another business feature
│   └── [same structure as feature-1]
└── shared/                      # Service-specific shared code
    ├── config/
    │   ├── database.config.ts
    │   ├── app.config.ts
    │   └── validation.config.ts
    ├── guards/
    │   ├── auth.guard.ts
    │   └── roles.guard.ts
    ├── interceptors/
    │   ├── logging.interceptor.ts
    │   ├── transform.interceptor.ts
    │   └── error.interceptor.ts
    ├── middleware/
    │   ├── correlation.middleware.ts
    │   └── request-logging.middleware.ts
    ├── decorators/
    │   ├── user.decorator.ts
    │   └── roles.decorator.ts
    ├── pipes/
    │   ├── validation.pipe.ts
    │   └── transform.pipe.ts
    ├── filters/
    │   ├── http-exception.filter.ts
    │   └── all-exceptions.filter.ts
    ├── utils/
    │   ├── crypto.util.ts
    │   ├── date.util.ts
    │   └── validation.util.ts
    └── constants/
        ├── error-codes.ts
        ├── event-types.ts
        └── app.constants.ts
```

## File Naming Conventions

### TypeScript Files
- **Controllers**: `feature-name.controller.ts`
- **Services**: `feature-name.service.ts`
- **Repositories**: `feature-name.repository.ts`
- **Entities**: `feature-name.entity.ts`
- **DTOs**: `create-feature-name.dto.ts`, `update-feature-name.dto.ts`
- **Interfaces**: `feature-name.interface.ts`
- **Modules**: `feature-name.module.ts`
- **Guards**: `feature-name.guard.ts`
- **Interceptors**: `feature-name.interceptor.ts`
- **Pipes**: `feature-name.pipe.ts`
- **Filters**: `feature-name.filter.ts`

### Test Files
- **Unit Tests**: `feature-name.service.spec.ts`
- **Integration Tests**: `feature-name.controller.spec.ts`
- **E2E Tests**: `feature-name.e2e-spec.ts`

### Configuration Files
- **Environment**: `.env.example`, `.env.development`, `.env.production`
- **TypeScript**: `tsconfig.json`, `tsconfig.build.json`
- **NestJS**: `nest-cli.json`
- **Package**: `package.json`
- **Docker**: `Dockerfile`, `docker-compose.yml`

## Directory Purpose Guidelines

### `/src` Directory
- Contains all application source code
- Organized by business features, not technical layers
- Each feature is self-contained with its own controllers, services, etc.

### `/tests` Directory
- Integration tests that span multiple features
- E2E tests for complete user journeys
- Performance and load tests
- Test utilities and fixtures

### `/config` Directory
- Environment-specific configuration files
- Database connection configurations
- External service configurations
- Feature flags and toggles

### `/migrations` Directory
- Database schema migrations
- Data migration scripts
- Migration rollback scripts
- Seed data scripts

### `/scripts` Directory
- Build and deployment scripts
- Database maintenance scripts
- Development utility scripts
- Data import/export scripts

### `/docs` Directory
- Service-specific documentation
- API documentation
- Architecture decisions
- Deployment guides

## AI Agent Implementation Rules

When creating service structure, AI agents must:

1. **Follow Feature-Based Organization**
   - Group related functionality together
   - Avoid technical layer separation (controllers/, services/, etc.)
   - Each feature should be independently testable

2. **Maintain Consistent Naming**
   - Use kebab-case for files and directories
   - Use descriptive, business-oriented names
   - Follow established naming patterns

3. **Include Required Files**
   - Every service must have package.json, tsconfig.json, Dockerfile
   - Every feature must have module, controller, service, and tests
   - Every entity must have corresponding DTOs

4. **Organize by Business Logic**
   - Features should represent business capabilities
   - Shared code should be truly reusable across features
   - Avoid deep nesting (max 3 levels)

5. **Ensure Testability**
   - Every file should have corresponding test file
   - Tests should be co-located with source code
   - Integration tests in separate directory

## Validation Checklist

Before completing service structure, verify:

- [ ] All required directories are present
- [ ] Naming conventions are followed consistently
- [ ] Feature modules are properly organized
- [ ] Shared code is appropriately placed
- [ ] Test files are co-located with source
- [ ] Configuration files are present
- [ ] Documentation structure is established
