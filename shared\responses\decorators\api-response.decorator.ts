/**
 * Standardized API Response Decorators
 * Provides consistent API documentation and response formatting
 */

import { applyDecorators, Type } from '@nestjs/common';
import {
  ApiResponse,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiUnprocessableEntityResponse,
  ApiInternalServerErrorResponse,
  ApiServiceUnavailableResponse,
  ApiTooManyRequestsResponse,
  ApiExtraModels,
  getSchemaPath,
} from '@nestjs/swagger';
import { BaseResponseDto } from '../dto/base-response.dto';
import { PaginatedResponseDto } from '../dto/paginated-response.dto';
import { ErrorResponseDto } from '../dto/error-response.dto';

/**
 * Standard success response decorator
 */
export const ApiStandardResponse = <TModel extends Type<any>>(
  model: TModel,
  description: string = 'Success'
) => {
  return applyDecorators(
    ApiExtraModels(BaseResponseDto, model),
    ApiOkResponse({
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(BaseResponseDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(model) },
            },
          },
        ],
      },
    })
  );
};

/**
 * Standard created response decorator
 */
export const ApiStandardCreatedResponse = <TModel extends Type<any>>(
  model: TModel,
  description: string = 'Resource created successfully'
) => {
  return applyDecorators(
    ApiExtraModels(BaseResponseDto, model),
    ApiCreatedResponse({
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(BaseResponseDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(model) },
            },
          },
        ],
      },
    })
  );
};

/**
 * Standard paginated response decorator
 */
export const ApiPaginatedResponse = <TModel extends Type<any>>(
  model: TModel,
  description: string = 'Paginated list retrieved successfully'
) => {
  return applyDecorators(
    ApiExtraModels(PaginatedResponseDto, model),
    ApiOkResponse({
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(PaginatedResponseDto) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(model) },
              },
            },
          },
        ],
      },
    })
  );
};

/**
 * Standard deleted response decorator
 */
export const ApiStandardDeletedResponse = (
  description: string = 'Resource deleted successfully'
) => {
  return applyDecorators(
    ApiExtraModels(BaseResponseDto),
    ApiOkResponse({
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(BaseResponseDto) },
          {
            properties: {
              data: {
                type: 'object',
                properties: {
                  deleted: { type: 'boolean', example: true },
                },
              },
            },
          },
        ],
      },
    })
  );
};

/**
 * Standard no content response decorator
 */
export const ApiStandardNoContentResponse = (
  description: string = 'No content'
) => {
  return applyDecorators(
    ApiNoContentResponse({
      description,
    })
  );
};

/**
 * Standard error responses decorator
 */
export const ApiStandardErrorResponses = () => {
  return applyDecorators(
    ApiExtraModels(ErrorResponseDto),
    ApiBadRequestResponse({
      description: 'Bad Request',
      type: ErrorResponseDto,
    }),
    ApiUnauthorizedResponse({
      description: 'Unauthorized',
      type: ErrorResponseDto,
    }),
    ApiForbiddenResponse({
      description: 'Forbidden',
      type: ErrorResponseDto,
    }),
    ApiNotFoundResponse({
      description: 'Not Found',
      type: ErrorResponseDto,
    }),
    ApiUnprocessableEntityResponse({
      description: 'Validation Error',
      type: ErrorResponseDto,
    }),
    ApiInternalServerErrorResponse({
      description: 'Internal Server Error',
      type: ErrorResponseDto,
    })
  );
};

/**
 * Complete API response documentation
 */
export const ApiCompleteResponse = <TModel extends Type<any>>(
  model: TModel,
  options: {
    operation: 'create' | 'read' | 'update' | 'delete' | 'list';
    description?: string;
    includeAuth?: boolean;
  }
) => {
  const decorators = [ApiStandardErrorResponses()];

  switch (options.operation) {
    case 'create':
      decorators.push(
        ApiStandardCreatedResponse(
          model,
          options.description || 'Resource created successfully'
        )
      );
      break;
    case 'read':
      decorators.push(
        ApiStandardResponse(
          model,
          options.description || 'Resource retrieved successfully'
        )
      );
      break;
    case 'update':
      decorators.push(
        ApiStandardResponse(
          model,
          options.description || 'Resource updated successfully'
        )
      );
      break;
    case 'delete':
      decorators.push(
        ApiStandardDeletedResponse(
          options.description || 'Resource deleted successfully'
        )
      );
      break;
    case 'list':
      decorators.push(
        ApiPaginatedResponse(
          model,
          options.description || 'Resources retrieved successfully'
        )
      );
      break;
  }

  return applyDecorators(...decorators);
};

/**
 * CRUD operation decorators
 */
export const ApiCreateResponse = <TModel extends Type<any>>(
  model: TModel,
  description?: string
) => ApiCompleteResponse(model, { operation: 'create', description });

export const ApiReadResponse = <TModel extends Type<any>>(
  model: TModel,
  description?: string
) => ApiCompleteResponse(model, { operation: 'read', description });

export const ApiUpdateResponse = <TModel extends Type<any>>(
  model: TModel,
  description?: string
) => ApiCompleteResponse(model, { operation: 'update', description });

export const ApiDeleteResponse = (description?: string) =>
  applyDecorators(
    ApiStandardDeletedResponse(description),
    ApiStandardErrorResponses()
  );

export const ApiListResponse = <TModel extends Type<any>>(
  model: TModel,
  description?: string
) => ApiCompleteResponse(model, { operation: 'list', description });

/**
 * Specific error response decorators
 */
export const ApiValidationErrorResponse = () => {
  return applyDecorators(
    ApiExtraModels(ErrorResponseDto),
    ApiUnprocessableEntityResponse({
      description: 'Validation Error',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ErrorResponseDto) },
          {
            properties: {
              error: {
                properties: {
                  validation: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        field: { type: 'string' },
                        message: { type: 'string' },
                        value: {},
                        constraints: { type: 'object' },
                      },
                    },
                  },
                },
              },
            },
          },
        ],
      },
    })
  );
};

export const ApiBusinessRuleErrorResponse = () => {
  return applyDecorators(
    ApiExtraModels(ErrorResponseDto),
    ApiUnprocessableEntityResponse({
      description: 'Business Rule Violation',
      type: ErrorResponseDto,
    })
  );
};

export const ApiRateLimitErrorResponse = () => {
  return applyDecorators(
    ApiExtraModels(ErrorResponseDto),
    ApiTooManyRequestsResponse({
      description: 'Rate Limit Exceeded',
      type: ErrorResponseDto,
    })
  );
};

export const ApiServiceUnavailableErrorResponse = () => {
  return applyDecorators(
    ApiExtraModels(ErrorResponseDto),
    ApiServiceUnavailableResponse({
      description: 'Service Unavailable',
      type: ErrorResponseDto,
    })
  );
};

/**
 * Authentication-aware response decorators
 */
export const ApiAuthenticatedResponse = <TModel extends Type<any>>(
  model: TModel,
  operation: 'create' | 'read' | 'update' | 'delete' | 'list',
  description?: string
) => {
  return applyDecorators(
    ApiCompleteResponse(model, { operation, description, includeAuth: true }),
    ApiUnauthorizedResponse({
      description: 'Authentication required',
      type: ErrorResponseDto,
    }),
    ApiForbiddenResponse({
      description: 'Insufficient permissions',
      type: ErrorResponseDto,
    })
  );
};

/**
 * Health check response decorator
 */
export const ApiHealthResponse = () => {
  return applyDecorators(
    ApiOkResponse({
      description: 'Health check successful',
      schema: {
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          data: {
            type: 'object',
            properties: {
              status: { type: 'string', example: 'healthy' },
              timestamp: { type: 'string', example: '2025-06-08T12:00:00.000Z' },
              uptime: { type: 'number', example: 3600 },
              version: { type: 'string', example: '1.0.0' },
              environment: { type: 'string', example: 'production' },
              database: {
                type: 'object',
                properties: {
                  status: { type: 'string', example: 'connected' },
                  responseTime: { type: 'number', example: 15 },
                },
              },
            },
          },
          correlationId: { type: 'string', example: 'health_1234567890_abcdef' },
          timestamp: { type: 'string', example: '2025-06-08T12:00:00.000Z' },
          service: { type: 'string', example: 'user-service' },
          version: { type: 'string', example: '1.0.0' },
        },
      },
    }),
    ApiServiceUnavailableResponse({
      description: 'Service unhealthy',
      type: ErrorResponseDto,
    })
  );
};
