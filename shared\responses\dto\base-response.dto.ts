/**
 * Base Response DTOs
 * Provides standardized response DTOs for API documentation
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsString, IsOptional, IsDateString, IsNumber, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ErrorCode } from '../interfaces/base-response.interface';

/**
 * Base Response DTO
 */
export class BaseResponseDto<T = any> {
  @ApiProperty({
    description: 'Indicates if the request was successful',
    example: true,
  })
  @IsBoolean()
  success: boolean;

  @ApiPropertyOptional({
    description: 'Response data',
  })
  @IsOptional()
  data?: T;

  @ApiPropertyOptional({
    description: 'Optional message',
    example: 'Operation completed successfully',
  })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiProperty({
    description: 'Unique correlation ID for request tracking',
    example: 'user-service_1234567890_abcdef',
  })
  @IsString()
  correlationId: string;

  @ApiProperty({
    description: 'Response timestamp in ISO format',
    example: '2025-06-08T12:00:00.000Z',
  })
  @IsDateString()
  timestamp: string;

  @ApiProperty({
    description: 'Service that generated the response',
    example: 'user-service',
  })
  @IsString()
  service: string;

  @ApiProperty({
    description: 'Service version',
    example: '1.0.0',
  })
  @IsString()
  version: string;
}

/**
 * Success Response DTO
 */
export class SuccessResponseDto<T = any> extends BaseResponseDto<T> {
  @ApiProperty({
    description: 'Always true for success responses',
    example: true,
  })
  success: true;

  @ApiProperty({
    description: 'Response data',
  })
  data: T;
}

/**
 * Validation Error DTO
 */
export class ValidationErrorDto {
  @ApiProperty({
    description: 'Field name with validation error',
    example: 'email',
  })
  @IsString()
  field: string;

  @ApiProperty({
    description: 'Validation error message',
    example: 'Email must be a valid email address',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Invalid value that caused the error',
    example: 'invalid-email',
  })
  @IsOptional()
  value?: any;

  @ApiPropertyOptional({
    description: 'Validation constraints that were violated',
    example: { isEmail: 'email must be an email' },
  })
  @IsOptional()
  constraints?: Record<string, string>;
}

/**
 * Error Details DTO
 */
export class ErrorDetailsDto {
  @ApiProperty({
    description: 'Error code',
    enum: ErrorCode,
    example: ErrorCode.VALIDATION_ERROR,
  })
  @IsEnum(ErrorCode)
  code: ErrorCode;

  @ApiProperty({
    description: 'Human-readable error message',
    example: 'Validation failed for the provided data',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Additional error details',
    example: { field: 'email', reason: 'Invalid format' },
  })
  @IsOptional()
  details?: any;

  @ApiPropertyOptional({
    description: 'Stack trace (development only)',
    example: 'Error: Validation failed\n    at ValidationPipe...',
  })
  @IsOptional()
  @IsString()
  stack?: string;

  @ApiPropertyOptional({
    description: 'Validation errors',
    type: [ValidationErrorDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidationErrorDto)
  validation?: ValidationErrorDto[];

  @ApiPropertyOptional({
    description: 'Additional error context',
    example: { userId: '123', operation: 'create_user' },
  })
  @IsOptional()
  context?: Record<string, any>;
}

/**
 * Error Response DTO
 */
export class ErrorResponseDto extends BaseResponseDto<never> {
  @ApiProperty({
    description: 'Always false for error responses',
    example: false,
  })
  success: false;

  @ApiProperty({
    description: 'Error details',
    type: ErrorDetailsDto,
  })
  @ValidateNested()
  @Type(() => ErrorDetailsDto)
  error: ErrorDetailsDto;

  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
  })
  @IsNumber()
  statusCode: number;
}

/**
 * Created Response DTO
 */
export class CreatedResponseDto<T = any> extends SuccessResponseDto<T> {
  @ApiProperty({
    description: 'Success message for created resource',
    example: 'Resource created successfully',
  })
  message: string;
}

/**
 * Updated Response DTO
 */
export class UpdatedResponseDto<T = any> extends SuccessResponseDto<T> {
  @ApiProperty({
    description: 'Success message for updated resource',
    example: 'Resource updated successfully',
  })
  message: string;
}

/**
 * Deleted Response DTO
 */
export class DeletedResponseDto extends SuccessResponseDto<{ deleted: boolean }> {
  @ApiProperty({
    description: 'Deletion confirmation',
    example: { deleted: true },
  })
  data: { deleted: boolean };

  @ApiProperty({
    description: 'Success message for deleted resource',
    example: 'Resource deleted successfully',
  })
  message: string;
}

/**
 * No Content Response DTO
 */
export class NoContentResponseDto extends SuccessResponseDto<null> {
  @ApiProperty({
    description: 'No content data',
    example: null,
  })
  data: null;

  @ApiProperty({
    description: 'No content message',
    example: 'No content',
  })
  message: string;
}

/**
 * Health Check Response DTO
 */
export class HealthCheckDataDto {
  @ApiProperty({
    description: 'Service health status',
    example: 'healthy',
    enum: ['healthy', 'unhealthy', 'degraded'],
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'Health check timestamp',
    example: '2025-06-08T12:00:00.000Z',
  })
  @IsDateString()
  timestamp: string;

  @ApiProperty({
    description: 'Service uptime in seconds',
    example: 3600,
  })
  @IsNumber()
  uptime: number;

  @ApiProperty({
    description: 'Service version',
    example: '1.0.0',
  })
  @IsString()
  version: string;

  @ApiProperty({
    description: 'Environment name',
    example: 'production',
  })
  @IsString()
  environment: string;

  @ApiPropertyOptional({
    description: 'Database health information',
    example: { status: 'connected', responseTime: 15 },
  })
  @IsOptional()
  database?: {
    status: string;
    responseTime: number;
  };

  @ApiPropertyOptional({
    description: 'Cache health information',
    example: { status: 'connected', responseTime: 5 },
  })
  @IsOptional()
  cache?: {
    status: string;
    responseTime: number;
  };

  @ApiPropertyOptional({
    description: 'External services health',
    example: { 'external-api': { status: 'connected', responseTime: 100 } },
  })
  @IsOptional()
  externalServices?: Record<string, {
    status: string;
    responseTime: number;
  }>;
}

/**
 * Health Check Response DTO
 */
export class HealthCheckResponseDto extends SuccessResponseDto<HealthCheckDataDto> {
  @ApiProperty({
    description: 'Health check data',
    type: HealthCheckDataDto,
  })
  @ValidateNested()
  @Type(() => HealthCheckDataDto)
  data: HealthCheckDataDto;
}

/**
 * Rate Limit Error Response DTO
 */
export class RateLimitErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({
    description: 'Rate limit error details',
    example: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Rate limit exceeded',
      context: { retryAfter: 60 },
    },
  })
  error: ErrorDetailsDto & {
    context: {
      retryAfter: number;
    };
  };
}

/**
 * Business Rule Violation Response DTO
 */
export class BusinessRuleViolationResponseDto extends ErrorResponseDto {
  @ApiProperty({
    description: 'Business rule violation details',
    example: {
      code: 'BUSINESS_RULE_VIOLATION',
      message: 'Business rule violation: Cannot delete user with active orders',
      context: { rule: 'active_orders_check', userId: '123' },
    },
  })
  error: ErrorDetailsDto & {
    context: {
      rule: string;
      [key: string]: any;
    };
  };
}

/**
 * Service Unavailable Response DTO
 */
export class ServiceUnavailableResponseDto extends ErrorResponseDto {
  @ApiProperty({
    description: 'Service unavailable error details',
    example: {
      code: 'SERVICE_UNAVAILABLE',
      message: 'Service user-service is currently unavailable',
      context: { service: 'user-service', retryAfter: 30 },
    },
  })
  error: ErrorDetailsDto & {
    context: {
      service: string;
      retryAfter?: number;
    };
  };
}
