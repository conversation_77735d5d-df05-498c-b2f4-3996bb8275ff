import { api } from '@/lib/api'
import {
  MarketplaceListing,
  MarketplaceOffer,
  MarketplaceTransaction,
  CreateListingRequest,
  UpdateListingRequest,
  CreateOfferRequest,
  PurchaseRequest,
  MarketplaceFilters,
  MarketplaceAnalytics,
  UserMarketplaceData,
  MarketplaceNotification,
  ListingStatus,
  OfferStatus,
  TransactionStatus
} from '@/types/marketplace.types'

export class MarketplaceService {
  // ===== LISTING OPERATIONS =====
  
  async getListings(filters?: MarketplaceFilters): Promise<{
    listings: MarketplaceListing[]
    total: number
    page: number
    limit: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get('/marketplace/listings', { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch marketplace listings:', error)
      throw new Error('Failed to load marketplace listings')
    }
  }

  async getListing(id: string): Promise<MarketplaceListing> {
    try {
      const response = await api.get(`/marketplace/listings/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch listing:', error)
      throw new Error('Failed to load listing details')
    }
  }

  async createListing(data: CreateListingRequest): Promise<MarketplaceListing> {
    try {
      const response = await api.post('/marketplace/listings', data)
      return response.data
    } catch (error) {
      console.error('Failed to create listing:', error)
      throw new Error('Failed to create marketplace listing')
    }
  }

  async updateListing(id: string, data: UpdateListingRequest): Promise<MarketplaceListing> {
    try {
      const response = await api.patch(`/marketplace/listings/${id}`, data)
      return response.data
    } catch (error) {
      console.error('Failed to update listing:', error)
      throw new Error('Failed to update listing')
    }
  }

  async cancelListing(id: string): Promise<void> {
    try {
      await api.patch(`/marketplace/listings/${id}/cancel`)
    } catch (error) {
      console.error('Failed to cancel listing:', error)
      throw new Error('Failed to cancel listing')
    }
  }

  async deleteListing(id: string): Promise<void> {
    try {
      await api.delete(`/marketplace/listings/${id}`)
    } catch (error) {
      console.error('Failed to delete listing:', error)
      throw new Error('Failed to delete listing')
    }
  }

  // ===== OFFER OPERATIONS =====

  async getOffers(listingId: string): Promise<MarketplaceOffer[]> {
    try {
      const response = await api.get(`/marketplace/listings/${listingId}/offers`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch offers:', error)
      throw new Error('Failed to load offers')
    }
  }

  async createOffer(data: CreateOfferRequest): Promise<MarketplaceOffer> {
    try {
      const response = await api.post('/marketplace/offers', data)
      return response.data
    } catch (error) {
      console.error('Failed to create offer:', error)
      throw new Error('Failed to create offer')
    }
  }

  async acceptOffer(offerId: string): Promise<MarketplaceTransaction> {
    try {
      const response = await api.patch(`/marketplace/offers/${offerId}/accept`)
      return response.data
    } catch (error) {
      console.error('Failed to accept offer:', error)
      throw new Error('Failed to accept offer')
    }
  }

  async rejectOffer(offerId: string): Promise<void> {
    try {
      await api.patch(`/marketplace/offers/${offerId}/reject`)
    } catch (error) {
      console.error('Failed to reject offer:', error)
      throw new Error('Failed to reject offer')
    }
  }

  async cancelOffer(offerId: string): Promise<void> {
    try {
      await api.patch(`/marketplace/offers/${offerId}/cancel`)
    } catch (error) {
      console.error('Failed to cancel offer:', error)
      throw new Error('Failed to cancel offer')
    }
  }

  // ===== TRANSACTION OPERATIONS =====

  async purchaseListing(data: PurchaseRequest): Promise<MarketplaceTransaction> {
    try {
      const response = await api.post('/marketplace/transactions/purchase', data)
      return response.data
    } catch (error) {
      console.error('Failed to purchase NFT:', error)
      throw new Error('Failed to complete purchase')
    }
  }

  async getTransaction(id: string): Promise<MarketplaceTransaction> {
    try {
      const response = await api.get(`/marketplace/transactions/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch transaction:', error)
      throw new Error('Failed to load transaction details')
    }
  }

  async getTransactions(filters?: {
    userId?: string
    type?: 'purchases' | 'sales'
    status?: TransactionStatus
    page?: number
    limit?: number
  }): Promise<{
    transactions: MarketplaceTransaction[]
    total: number
    page: number
    limit: number
  }> {
    try {
      const response = await api.get('/marketplace/transactions', { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch transactions:', error)
      throw new Error('Failed to load transactions')
    }
  }

  // ===== USER MARKETPLACE DATA =====

  async getUserMarketplaceData(userId?: string): Promise<UserMarketplaceData> {
    try {
      const endpoint = userId ? `/marketplace/users/${userId}` : '/marketplace/user/me'
      const response = await api.get(endpoint)
      return response.data
    } catch (error) {
      console.error('Failed to fetch user marketplace data:', error)
      throw new Error('Failed to load user marketplace data')
    }
  }

  async getUserListings(userId?: string, status?: ListingStatus): Promise<MarketplaceListing[]> {
    try {
      const endpoint = userId ? `/marketplace/users/${userId}/listings` : '/marketplace/user/me/listings'
      const response = await api.get(endpoint, { params: { status } })
      return response.data
    } catch (error) {
      console.error('Failed to fetch user listings:', error)
      throw new Error('Failed to load user listings')
    }
  }

  async getUserOffers(userId?: string, type?: 'made' | 'received'): Promise<MarketplaceOffer[]> {
    try {
      const endpoint = userId ? `/marketplace/users/${userId}/offers` : '/marketplace/user/me/offers'
      const response = await api.get(endpoint, { params: { type } })
      return response.data
    } catch (error) {
      console.error('Failed to fetch user offers:', error)
      throw new Error('Failed to load user offers')
    }
  }

  // ===== ANALYTICS =====

  async getMarketplaceAnalytics(timeframe?: '24h' | '7d' | '30d' | '90d'): Promise<MarketplaceAnalytics> {
    try {
      const response = await api.get('/marketplace/analytics', { params: { timeframe } })
      return response.data
    } catch (error) {
      console.error('Failed to fetch marketplace analytics:', error)
      throw new Error('Failed to load marketplace analytics')
    }
  }

  async getNFTMarketHistory(nftId: string): Promise<{
    listings: MarketplaceListing[]
    transactions: MarketplaceTransaction[]
    offers: MarketplaceOffer[]
    priceHistory: Array<{
      price: number
      currency: string
      date: string
      eventType: string
    }>
  }> {
    try {
      const response = await api.get(`/marketplace/nft/${nftId}/history`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch NFT market history:', error)
      throw new Error('Failed to load NFT market history')
    }
  }

  // ===== NOTIFICATIONS =====

  async getMarketplaceNotifications(): Promise<MarketplaceNotification[]> {
    try {
      const response = await api.get('/marketplace/notifications')
      return response.data
    } catch (error) {
      console.error('Failed to fetch marketplace notifications:', error)
      throw new Error('Failed to load notifications')
    }
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      await api.patch(`/marketplace/notifications/${notificationId}/read`)
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
      throw new Error('Failed to update notification')
    }
  }

  // ===== UTILITY METHODS =====

  async searchListings(query: string, filters?: Partial<MarketplaceFilters>): Promise<MarketplaceListing[]> {
    try {
      const response = await api.get('/marketplace/search', { 
        params: { q: query, ...filters } 
      })
      return response.data
    } catch (error) {
      console.error('Failed to search listings:', error)
      throw new Error('Failed to search marketplace')
    }
  }

  async getFeaturedListings(limit = 10): Promise<MarketplaceListing[]> {
    try {
      const response = await api.get('/marketplace/featured', { params: { limit } })
      return response.data
    } catch (error) {
      console.error('Failed to fetch featured listings:', error)
      throw new Error('Failed to load featured listings')
    }
  }

  async getTrendingListings(timeframe = '24h', limit = 10): Promise<MarketplaceListing[]> {
    try {
      const response = await api.get('/marketplace/trending', { 
        params: { timeframe, limit } 
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch trending listings:', error)
      throw new Error('Failed to load trending listings')
    }
  }

  async getRecentSales(limit = 20): Promise<MarketplaceTransaction[]> {
    try {
      const response = await api.get('/marketplace/recent-sales', { params: { limit } })
      return response.data
    } catch (error) {
      console.error('Failed to fetch recent sales:', error)
      throw new Error('Failed to load recent sales')
    }
  }

  // ===== PRICE ESTIMATION =====

  async estimateNFTPrice(nftId: string): Promise<{
    estimatedPrice: number
    currency: string
    confidence: number
    factors: Array<{
      factor: string
      impact: number
      description: string
    }>
  }> {
    try {
      const response = await api.get(`/marketplace/nft/${nftId}/price-estimate`)
      return response.data
    } catch (error) {
      console.error('Failed to estimate NFT price:', error)
      throw new Error('Failed to estimate price')
    }
  }
}

export const marketplaceService = new MarketplaceService()
