import { Controller, Get, Post, Param, Body, Res, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { ContractManagerService } from '../services/contract-manager.service';

@ApiTags('Contracts')
@Controller('contracts')
export class ContractController {
  private readonly logger = new Logger(ContractController.name);

  constructor(private readonly contractManagerService: ContractManagerService) {}

  @Get(':address')
  @ApiOperation({ summary: 'Get contract details', description: 'Get details of a specific smart contract' })
  @ApiParam({ name: 'address', description: 'Contract address' })
  @ApiResponse({ status: 200, description: 'Contract details retrieved successfully' })
  async getContract(@Param('address') address: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting contract details for: ${address}`);
      const result = await this.contractManagerService.getContract(address);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Contract retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Post(':address/call')
  @ApiOperation({ summary: 'Call contract method', description: 'Call a read-only method on a smart contract' })
  @ApiParam({ name: 'address', description: 'Contract address' })
  @ApiBody({
    description: 'Contract call data',
    schema: {
      type: 'object',
      properties: {
        method: { type: 'string', example: 'balanceOf' },
        parameters: { type: 'array', items: { type: 'string' } },
        network: { type: 'string', example: 'ethereum' }
      },
      required: ['method', 'network']
    }
  })
  @ApiResponse({ status: 200, description: 'Contract method called successfully' })
  async callContract(@Param('address') address: string, @Body() callData: any, @Res() res: Response) {
    try {
      this.logger.log(`Calling contract method: ${callData.method} on ${address}`);
      const result = await this.contractManagerService.callContract(address, callData);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Contract call failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }
}
