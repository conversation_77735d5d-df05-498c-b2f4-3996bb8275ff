// Performance Optimization Types for Social NFT Platform

export interface PerformanceMetric {
  id: string
  name: string
  value: number
  unit: string
  category: PerformanceCategory
  timestamp: string
  tags: Record<string, string>
  threshold?: PerformanceThreshold
  trend?: TrendDirection
  metadata?: Record<string, any>
}

export enum PerformanceCategory {
  RESPONSE_TIME = 'response_time',
  THROUGHPUT = 'throughput',
  ERROR_RATE = 'error_rate',
  RESOURCE_USAGE = 'resource_usage',
  CACHE_PERFORMANCE = 'cache_performance',
  DATABASE_PERFORMANCE = 'database_performance',
  NETWORK_PERFORMANCE = 'network_performance',
  USER_EXPERIENCE = 'user_experience',
  BUSINESS_METRICS = 'business_metrics'
}

export interface PerformanceThreshold {
  warning: number
  critical: number
  target?: number
}

export enum TrendDirection {
  IMPROVING = 'improving',
  DEGRADING = 'degrading',
  STABLE = 'stable'
}

export interface SystemPerformance {
  cpu: CPUMetrics
  memory: MemoryMetrics
  disk: DiskMetrics
  network: NetworkMetrics
  database: DatabaseMetrics
  cache: CacheMetrics
  application: ApplicationMetrics
  timestamp: string
}

export interface CPUMetrics {
  usage: number
  cores: number
  loadAverage: number[]
  processes: number
  threads: number
}

export interface MemoryMetrics {
  total: number
  used: number
  free: number
  percentage: number
  swap: {
    total: number
    used: number
    free: number
  }
}

export interface DiskMetrics {
  total: number
  used: number
  free: number
  percentage: number
  iops: {
    read: number
    write: number
  }
  latency: {
    read: number
    write: number
  }
}

export interface NetworkMetrics {
  bytesIn: number
  bytesOut: number
  packetsIn: number
  packetsOut: number
  connectionsActive: number
  requestsPerSecond: number
  bandwidth: {
    upload: number
    download: number
  }
}

export interface DatabaseMetrics {
  connections: {
    active: number
    idle: number
    total: number
  }
  queries: {
    total: number
    slow: number
    failed: number
  }
  performance: {
    queryTime: number
    lockTime: number
    indexHitRatio: number
  }
  storage: {
    size: number
    growth: number
  }
}

export interface CacheMetrics {
  hitRate: number
  missRate: number
  evictionRate: number
  memory: {
    used: number
    total: number
    percentage: number
  }
  operations: {
    gets: number
    sets: number
    deletes: number
  }
  latency: {
    get: number
    set: number
  }
}

export interface ApplicationMetrics {
  responseTime: {
    average: number
    p50: number
    p95: number
    p99: number
  }
  throughput: {
    requestsPerSecond: number
    transactionsPerSecond: number
  }
  errors: {
    rate: number
    count: number
    types: Record<string, number>
  }
  availability: {
    uptime: number
    downtime: number
    percentage: number
  }
}

export interface PerformanceAlert {
  id: string
  type: AlertType
  severity: AlertSeverity
  title: string
  description: string
  metric: string
  currentValue: number
  threshold: number
  timestamp: string
  status: AlertStatus
  actions: AlertAction[]
  metadata: Record<string, any>
}

export enum AlertType {
  THRESHOLD_EXCEEDED = 'threshold_exceeded',
  ANOMALY_DETECTED = 'anomaly_detected',
  TREND_DEGRADATION = 'trend_degradation',
  RESOURCE_EXHAUSTION = 'resource_exhaustion',
  SERVICE_UNAVAILABLE = 'service_unavailable'
}

export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum AlertStatus {
  ACTIVE = 'active',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
  SUPPRESSED = 'suppressed'
}

export interface AlertAction {
  type: ActionType
  label: string
  url?: string
  automated?: boolean
}

export enum ActionType {
  INVESTIGATE = 'investigate',
  SCALE_UP = 'scale_up',
  RESTART_SERVICE = 'restart_service',
  CLEAR_CACHE = 'clear_cache',
  OPTIMIZE_QUERY = 'optimize_query',
  CONTACT_TEAM = 'contact_team'
}

export interface OptimizationRecommendation {
  id: string
  type: OptimizationType
  priority: RecommendationPriority
  title: string
  description: string
  impact: ImpactAssessment
  implementation: ImplementationGuide
  metrics: string[]
  estimatedSavings: ResourceSavings
  complexity: ImplementationComplexity
  timeline: string
  status: RecommendationStatus
  createdAt: string
  updatedAt: string
}

export enum OptimizationType {
  CACHING = 'caching',
  DATABASE = 'database',
  NETWORK = 'network',
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  INFRASTRUCTURE = 'infrastructure',
  CODE = 'code',
  CONFIGURATION = 'configuration'
}

export enum RecommendationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface ImpactAssessment {
  performance: number
  cost: number
  reliability: number
  scalability: number
  userExperience: number
}

export interface ImplementationGuide {
  steps: ImplementationStep[]
  prerequisites: string[]
  risks: string[]
  rollbackPlan: string
  testing: string[]
}

export interface ImplementationStep {
  order: number
  title: string
  description: string
  estimatedTime: string
  resources: string[]
}

export interface ResourceSavings {
  cpu: number
  memory: number
  storage: number
  network: number
  cost: number
  responseTime: number
}

export enum ImplementationComplexity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  VERY_HIGH = 'very_high'
}

export enum RecommendationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  DEFERRED = 'deferred'
}

export interface CacheStrategy {
  type: CacheType
  ttl: number
  maxSize: number
  evictionPolicy: EvictionPolicy
  compression: boolean
  serialization: SerializationType
  tags: string[]
  invalidationRules: InvalidationRule[]
}

export enum CacheType {
  MEMORY = 'memory',
  REDIS = 'redis',
  CDN = 'cdn',
  BROWSER = 'browser',
  DATABASE = 'database'
}

export enum EvictionPolicy {
  LRU = 'lru',
  LFU = 'lfu',
  FIFO = 'fifo',
  TTL = 'ttl',
  RANDOM = 'random'
}

export enum SerializationType {
  JSON = 'json',
  MSGPACK = 'msgpack',
  PROTOBUF = 'protobuf',
  AVRO = 'avro'
}

export interface InvalidationRule {
  trigger: InvalidationTrigger
  pattern: string
  cascade: boolean
}

export enum InvalidationTrigger {
  TIME_BASED = 'time_based',
  EVENT_BASED = 'event_based',
  DEPENDENCY_CHANGE = 'dependency_change',
  MANUAL = 'manual'
}

export interface LoadTestResult {
  id: string
  name: string
  configuration: LoadTestConfig
  results: LoadTestMetrics
  status: TestStatus
  startTime: string
  endTime: string
  duration: number
  errors: TestError[]
  recommendations: string[]
}

export interface LoadTestConfig {
  targetUrl: string
  virtualUsers: number
  duration: number
  rampUpTime: number
  scenarios: TestScenario[]
  thresholds: PerformanceThreshold[]
}

export interface TestScenario {
  name: string
  weight: number
  requests: TestRequest[]
}

export interface TestRequest {
  method: string
  url: string
  headers: Record<string, string>
  body?: any
  assertions: TestAssertion[]
}

export interface TestAssertion {
  type: AssertionType
  property: string
  operator: string
  value: any
}

export enum AssertionType {
  RESPONSE_TIME = 'response_time',
  STATUS_CODE = 'status_code',
  RESPONSE_BODY = 'response_body',
  HEADER = 'header'
}

export interface LoadTestMetrics {
  requests: {
    total: number
    successful: number
    failed: number
    rate: number
  }
  responseTime: {
    min: number
    max: number
    average: number
    median: number
    p95: number
    p99: number
  }
  throughput: {
    requestsPerSecond: number
    bytesPerSecond: number
  }
  errors: {
    rate: number
    types: Record<string, number>
  }
  resources: {
    cpu: number
    memory: number
    network: number
  }
}

export enum TestStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface TestError {
  type: string
  message: string
  count: number
  percentage: number
  examples: string[]
}

// Request/Response Types
export interface GetPerformanceMetricsRequest {
  category?: PerformanceCategory
  timeRange?: TimeRange
  aggregation?: AggregationType
  limit?: number
  offset?: number
}

export interface TimeRange {
  start: string
  end: string
}

export enum AggregationType {
  AVERAGE = 'average',
  SUM = 'sum',
  MIN = 'min',
  MAX = 'max',
  COUNT = 'count',
  PERCENTILE = 'percentile'
}

export interface GetPerformanceMetricsResponse {
  metrics: PerformanceMetric[]
  summary: MetricsSummary
  totalCount: number
  hasMore: boolean
}

export interface MetricsSummary {
  averageResponseTime: number
  totalRequests: number
  errorRate: number
  availability: number
  trends: Record<string, TrendDirection>
}

export interface CreateOptimizationRequest {
  type: OptimizationType
  title: string
  description: string
  targetMetrics: string[]
  priority?: RecommendationPriority
  estimatedImpact?: Partial<ImpactAssessment>
}

export interface UpdateOptimizationRequest {
  id: string
  status?: RecommendationStatus
  progress?: number
  notes?: string
  actualImpact?: Partial<ImpactAssessment>
}

export interface RunLoadTestRequest {
  name: string
  configuration: LoadTestConfig
  scheduled?: boolean
  scheduleTime?: string
}

export interface GetSystemPerformanceResponse {
  current: SystemPerformance
  historical: SystemPerformance[]
  alerts: PerformanceAlert[]
  recommendations: OptimizationRecommendation[]
}
