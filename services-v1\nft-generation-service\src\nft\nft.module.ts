import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';

// Controllers
import { NftGenerationController } from './controllers/nft-generation.controller';
import { NftQueryController } from './controllers/nft-query.controller';
import { ImageProcessingController } from './controllers/image-processing.controller';
import { TemplateController } from './controllers/template.controller';

// Services
import { NftGenerationService } from './services/nft-generation.service';
import { NftQueryService } from './services/nft-query.service';
import { ImageProcessingService } from './services/image-processing.service';
import { TemplateService } from './services/template.service';
// Temporarily commented out complex business logic services
// import { ExternalStorageService } from './services/external-storage.service';
// import { NftCommandService } from './services/nft-command.service';
// import { NFTImageGeneratorService } from './services/nft-image-generator.service';
// import { ProfileNftGenerationService } from './services/profile-nft-generation.service';

@Module({
  imports: [
    HttpModule, // For external API calls
  ],
  controllers: [
    // NFT Generation Controllers
    NftGenerationController,
    NftQueryController,
    
    // Image Processing Controllers
    ImageProcessingController,
    TemplateController,
  ],
  providers: [
    // NFT Generation Services
    NftGenerationService,
    NftQueryService,

    // Image Processing Services
    ImageProcessingService,
    TemplateService,

    // Temporarily commented out complex business logic services
    // NftCommandService,
    // NFTImageGeneratorService,
    // ExternalStorageService,
    // ProfileNftGenerationService,
  ],
  exports: [
    // Export services for use in other modules
    NftGenerationService,
    NftQueryService,
    ImageProcessingService,
    TemplateService,

    // Temporarily commented out complex business logic services
    // NftCommandService,
    // NFTImageGeneratorService,
    // ExternalStorageService,
    // ProfileNftGenerationService,
  ],
})
export class NftModule {}
