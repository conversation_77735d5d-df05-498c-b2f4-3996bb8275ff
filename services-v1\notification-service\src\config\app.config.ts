import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IsNumber, IsString, IsArray, IsBoolean, validateSync } from 'class-validator';
import { plainToClass, Transform } from 'class-transformer';

export class AppConfigValidation {
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  PORT: number = 3008;

  @IsString()
  NODE_ENV: string = 'development';

  @IsString()
  DATABASE_URL: string = '';

  @IsArray()
  @Transform(({ value }) => value.split(',').map((origin: string) => origin.trim()))
  CORS_ORIGIN: string[] = ['http://localhost:3000', 'http://localhost:3010'];

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  CORS_CREDENTIALS: boolean = true;

  @IsString()
  SMTP_HOST: string = 'smtp.gmail.com';

  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  SMTP_PORT: number = 587;

  @IsString()
  SMTP_USER: string = '';

  @IsString()
  SMTP_PASS: string = '';

  @IsString()
  EMAIL_FROM: string = '<EMAIL>';

  @IsString()
  TWILIO_ACCOUNT_SID: string = '';

  @IsString()
  TWILIO_AUTH_TOKEN: string = '';

  @IsString()
  TWILIO_PHONE_NUMBER: string = '';

  @IsString()
  FIREBASE_PROJECT_ID: string = '';

  @IsString()
  FIREBASE_PRIVATE_KEY: string = '';

  @IsString()
  FIREBASE_CLIENT_EMAIL: string = '';

  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  NOTIFICATION_BATCH_SIZE: number = 50;

  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  NOTIFICATION_PROCESS_INTERVAL: number = 1000;

  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  NOTIFICATION_RETRY_ATTEMPTS: number = 3;

  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  NOTIFICATION_RETRY_DELAY: number = 5000;
}

@Injectable()
export class AppConfig {
  private readonly config: AppConfigValidation;

  constructor(private configService: ConfigService) {
    const config = plainToClass(AppConfigValidation, process.env, {
      enableImplicitConversion: true,
    });

    const errors = validateSync(config);
    if (errors.length > 0) {
      throw new Error(`Configuration validation failed: ${errors.toString()}`);
    }

    this.config = config;
  }

  get port(): number {
    return this.config.PORT;
  }

  get nodeEnv(): string {
    return this.config.NODE_ENV;
  }

  get isDevelopment(): boolean {
    return this.nodeEnv === 'development';
  }

  get isProduction(): boolean {
    return this.nodeEnv === 'production';
  }

  get databaseUrl(): string {
    return this.config.DATABASE_URL;
  }

  get cors() {
    return {
      origin: this.config.CORS_ORIGIN,
      credentials: this.config.CORS_CREDENTIALS,
    };
  }

  get email() {
    return {
      host: this.config.SMTP_HOST,
      port: this.config.SMTP_PORT,
      user: this.config.SMTP_USER,
      pass: this.config.SMTP_PASS,
      from: this.config.EMAIL_FROM,
    };
  }

  get sms() {
    return {
      accountSid: this.config.TWILIO_ACCOUNT_SID,
      authToken: this.config.TWILIO_AUTH_TOKEN,
      phoneNumber: this.config.TWILIO_PHONE_NUMBER,
    };
  }

  get push() {
    return {
      projectId: this.config.FIREBASE_PROJECT_ID,
      privateKey: this.config.FIREBASE_PRIVATE_KEY,
      clientEmail: this.config.FIREBASE_CLIENT_EMAIL,
    };
  }

  get notification() {
    return {
      batchSize: this.config.NOTIFICATION_BATCH_SIZE,
      processInterval: this.config.NOTIFICATION_PROCESS_INTERVAL,
      retryAttempts: this.config.NOTIFICATION_RETRY_ATTEMPTS,
      retryDelay: this.config.NOTIFICATION_RETRY_DELAY,
    };
  }
}
