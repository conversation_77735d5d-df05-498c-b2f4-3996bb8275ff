import { Module } from '@nestjs/common';
import { RateLimitService } from './rate-limit.service';
import { RateLimitController } from './rate-limit.controller';

/**
 * Rate Limit Module
 * 
 * Provides advanced rate limiting with sliding window algorithm,
 * per-user and per-service limits, and comprehensive monitoring.
 */
@Module({
  providers: [RateLimitService],
  controllers: [RateLimitController],
  exports: [RateLimitService],
})
export class RateLimitModule {}
