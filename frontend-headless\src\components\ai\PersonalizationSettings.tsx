'use client'

import React, { useState, useEffect } from 'react'
import {
  UserGroupIcon,
  CogIcon,
  EyeIcon,
  BellIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  PaintBrushIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
  TagIcon,
  ClockIcon,
  CheckIcon
} from '@heroicons/react/24/outline'
import {
  usePersonalizationProfile,
  useUpdatePersonalizationProfile
} from '@/hooks/useAI'
import {
  PersonalizationProfile,
  UserPreferences,
  TradingStyleType,
  RiskTolerance,
  InvestmentHorizon,
  TradingFrequency,
  InteractionStyle,
  ContentFormat,
  ContentComplexity,
  ContentLength,
  VisualStyle,
  NotificationFrequency,
  VisibilityLevel
} from '@/types/ai.types'

interface PersonalizationSettingsProps {
  userId: string
  className?: string
}

export default function PersonalizationSettings({
  userId,
  className = ''
}: PersonalizationSettingsProps) {
  const [activeSection, setActiveSection] = useState<'preferences' | 'trading' | 'content' | 'privacy' | 'notifications'>('preferences')
  const [hasChanges, setHasChanges] = useState(false)

  const { data: profile, isLoading } = usePersonalizationProfile(userId)
  const updateProfileMutation = useUpdatePersonalizationProfile()

  const [preferences, setPreferences] = useState<Partial<UserPreferences>>({})

  useEffect(() => {
    if (profile?.preferences) {
      setPreferences(profile.preferences)
    }
  }, [profile])

  const sections = [
    {
      id: 'preferences',
      name: 'General Preferences',
      icon: UserGroupIcon,
      description: 'NFT categories, price ranges, and interests'
    },
    {
      id: 'trading',
      name: 'Trading Style',
      icon: ChartBarIcon,
      description: 'Risk tolerance and investment preferences'
    },
    {
      id: 'content',
      name: 'Content Preferences',
      icon: PaintBrushIcon,
      description: 'Content format and complexity preferences'
    },
    {
      id: 'privacy',
      name: 'Privacy Settings',
      icon: ShieldCheckIcon,
      description: 'Data sharing and visibility controls'
    },
    {
      id: 'notifications',
      name: 'Notifications',
      icon: BellIcon,
      description: 'Notification timing and preferences'
    }
  ]

  const handlePreferenceChange = (key: keyof UserPreferences, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }))
    setHasChanges(true)
  }

  const handleSaveChanges = async () => {
    try {
      await updateProfileMutation.mutateAsync({
        userId,
        preferences
      })
      setHasChanges(false)
    } catch (error) {
      console.error('Failed to update preferences:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="md:col-span-2 h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <UserGroupIcon className="h-6 w-6 mr-2 text-blue-600" />
            Personalization Settings
          </h2>
          <p className="text-sm text-gray-600">
            Customize your AI experience and recommendations
          </p>
        </div>
        
        {hasChanges && (
          <button
            onClick={handleSaveChanges}
            disabled={updateProfileMutation.isPending}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            <CheckIcon className="h-4 w-4 mr-2" />
            {updateProfileMutation.isPending ? 'Saving...' : 'Save Changes'}
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Section Navigation */}
        <div className="space-y-2">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id as any)}
              className={`w-full text-left p-4 rounded-lg border transition-colors ${
                activeSection === section.id
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-3">
                <section.icon className="h-5 w-5" />
                <div>
                  <div className="text-sm font-medium">{section.name}</div>
                  <div className="text-xs text-gray-600">{section.description}</div>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Settings Content */}
        <div className="md:col-span-2 bg-white border border-gray-200 rounded-lg p-6">
          {/* General Preferences */}
          {activeSection === 'preferences' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">General Preferences</h3>
              
              {/* NFT Categories */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Preferred NFT Categories
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {['Art', 'Gaming', 'Music', 'Sports', 'Photography', 'Collectibles', 'Utility', 'Metaverse'].map((category) => (
                    <label key={category} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.nftCategories?.includes(category) || false}
                        onChange={(e) => {
                          const current = preferences.nftCategories || []
                          const updated = e.target.checked
                            ? [...current, category]
                            : current.filter(c => c !== category)
                          handlePreferenceChange('nftCategories', updated)
                        }}
                        className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{category}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Blockchains */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Preferred Blockchains
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {['Ethereum', 'Polygon', 'Solana', 'BSC', 'Avalanche', 'Arbitrum', 'Optimism', 'Base'].map((blockchain) => (
                    <label key={blockchain} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.blockchains?.includes(blockchain) || false}
                        onChange={(e) => {
                          const current = preferences.blockchains || []
                          const updated = e.target.checked
                            ? [...current, blockchain]
                            : current.filter(b => b !== blockchain)
                          handlePreferenceChange('blockchains', updated)
                        }}
                        className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{blockchain}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Price Ranges */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Preferred Price Ranges (ETH)
                </label>
                <div className="space-y-2">
                  {[
                    { min: 0, max: 0.1, label: '< 0.1 ETH' },
                    { min: 0.1, max: 1, label: '0.1 - 1 ETH' },
                    { min: 1, max: 10, label: '1 - 10 ETH' },
                    { min: 10, max: 100, label: '10 - 100 ETH' },
                    { min: 100, max: Infinity, label: '> 100 ETH' }
                  ].map((range) => (
                    <label key={`${range.min}-${range.max}`} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.priceRanges?.some(pr => pr.min === range.min && pr.max === range.max) || false}
                        onChange={(e) => {
                          const current = preferences.priceRanges || []
                          const updated = e.target.checked
                            ? [...current, { min: range.min, max: range.max, currency: 'ETH', preferred: true }]
                            : current.filter(pr => !(pr.min === range.min && pr.max === range.max))
                          handlePreferenceChange('priceRanges', updated)
                        }}
                        className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{range.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Trading Style */}
          {activeSection === 'trading' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Trading Style</h3>
              
              {/* Trading Style Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Trading Style
                </label>
                <div className="space-y-2">
                  {Object.values(TradingStyleType).map((style) => (
                    <label key={style} className="flex items-center">
                      <input
                        type="radio"
                        name="tradingStyle"
                        value={style}
                        checked={profile?.tradingStyle?.type === style}
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700 capitalize">{style.replace('_', ' ')}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Risk Tolerance */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Risk Tolerance
                </label>
                <div className="space-y-2">
                  {Object.values(RiskTolerance).map((risk) => (
                    <label key={risk} className="flex items-center">
                      <input
                        type="radio"
                        name="riskTolerance"
                        value={risk}
                        checked={profile?.tradingStyle?.riskTolerance === risk}
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700 capitalize">{risk.replace('_', ' ')}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Investment Horizon */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Investment Horizon
                </label>
                <div className="space-y-2">
                  {Object.values(InvestmentHorizon).map((horizon) => (
                    <label key={horizon} className="flex items-center">
                      <input
                        type="radio"
                        name="investmentHorizon"
                        value={horizon}
                        checked={profile?.tradingStyle?.investmentHorizon === horizon}
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700 capitalize">{horizon.replace('_', ' ')}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Content Preferences */}
          {activeSection === 'content' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Content Preferences</h3>
              
              {/* Content Formats */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Preferred Content Formats
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {Object.values(ContentFormat).map((format) => (
                    <label key={format} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.contentPreferences?.formats?.includes(format) || false}
                        onChange={(e) => {
                          const current = preferences.contentPreferences?.formats || []
                          const updated = e.target.checked
                            ? [...current, format]
                            : current.filter(f => f !== format)
                          handlePreferenceChange('contentPreferences', {
                            ...preferences.contentPreferences,
                            formats: updated
                          })
                        }}
                        className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700 capitalize">{format}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Content Complexity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Content Complexity
                </label>
                <div className="space-y-2">
                  {Object.values(ContentComplexity).map((complexity) => (
                    <label key={complexity} className="flex items-center">
                      <input
                        type="radio"
                        name="contentComplexity"
                        value={complexity}
                        checked={preferences.contentPreferences?.complexity === complexity}
                        onChange={(e) => {
                          handlePreferenceChange('contentPreferences', {
                            ...preferences.contentPreferences,
                            complexity: e.target.value as ContentComplexity
                          })
                        }}
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700 capitalize">{complexity}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Visual Style */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Visual Style Preferences
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {Object.values(VisualStyle).map((style) => (
                    <label key={style} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.contentPreferences?.visualStyle?.includes(style) || false}
                        onChange={(e) => {
                          const current = preferences.contentPreferences?.visualStyle || []
                          const updated = e.target.checked
                            ? [...current, style]
                            : current.filter(s => s !== style)
                          handlePreferenceChange('contentPreferences', {
                            ...preferences.contentPreferences,
                            visualStyle: updated
                          })
                        }}
                        className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700 capitalize">{style}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Privacy Settings */}
          {activeSection === 'privacy' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Privacy Settings</h3>
              
              {/* Visibility Settings */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Profile Visibility
                  </label>
                  <select
                    value={preferences.privacySettings?.profileVisibility || VisibilityLevel.PUBLIC}
                    onChange={(e) => {
                      handlePreferenceChange('privacySettings', {
                        ...preferences.privacySettings,
                        profileVisibility: e.target.value as VisibilityLevel
                      })
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Object.values(VisibilityLevel).map((level) => (
                      <option key={level} value={level}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Activity Visibility
                  </label>
                  <select
                    value={preferences.privacySettings?.activityVisibility || VisibilityLevel.PUBLIC}
                    onChange={(e) => {
                      handlePreferenceChange('privacySettings', {
                        ...preferences.privacySettings,
                        activityVisibility: e.target.value as VisibilityLevel
                      })
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Object.values(VisibilityLevel).map((level) => (
                      <option key={level} value={level}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Portfolio Visibility
                  </label>
                  <select
                    value={preferences.privacySettings?.portfolioVisibility || VisibilityLevel.FRIENDS}
                    onChange={(e) => {
                      handlePreferenceChange('privacySettings', {
                        ...preferences.privacySettings,
                        portfolioVisibility: e.target.value as VisibilityLevel
                      })
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Object.values(VisibilityLevel).map((level) => (
                      <option key={level} value={level}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Data Sharing */}
              <div className="space-y-3">
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Allow data sharing for personalization</span>
                  <input
                    type="checkbox"
                    checked={preferences.privacySettings?.dataSharing || false}
                    onChange={(e) => {
                      handlePreferenceChange('privacySettings', {
                        ...preferences.privacySettings,
                        dataSharing: e.target.checked
                      })
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Opt-in to analytics</span>
                  <input
                    type="checkbox"
                    checked={preferences.privacySettings?.analyticsOptIn || false}
                    onChange={(e) => {
                      handlePreferenceChange('privacySettings', {
                        ...preferences.privacySettings,
                        analyticsOptIn: e.target.checked
                      })
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Receive marketing communications</span>
                  <input
                    type="checkbox"
                    checked={preferences.privacySettings?.marketingOptIn || false}
                    onChange={(e) => {
                      handlePreferenceChange('privacySettings', {
                        ...preferences.privacySettings,
                        marketingOptIn: e.target.checked
                      })
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
              </div>
            </div>
          )}

          {/* Notifications */}
          {activeSection === 'notifications' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Notification Preferences</h3>
              
              {/* Notification Channels */}
              <div className="space-y-3">
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Email notifications</span>
                  <input
                    type="checkbox"
                    checked={preferences.notificationSettings?.email || false}
                    onChange={(e) => {
                      handlePreferenceChange('notificationSettings', {
                        ...preferences.notificationSettings,
                        email: e.target.checked
                      })
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Push notifications</span>
                  <input
                    type="checkbox"
                    checked={preferences.notificationSettings?.push || false}
                    onChange={(e) => {
                      handlePreferenceChange('notificationSettings', {
                        ...preferences.notificationSettings,
                        push: e.target.checked
                      })
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">In-app notifications</span>
                  <input
                    type="checkbox"
                    checked={preferences.notificationSettings?.inApp || false}
                    onChange={(e) => {
                      handlePreferenceChange('notificationSettings', {
                        ...preferences.notificationSettings,
                        inApp: e.target.checked
                      })
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
              </div>

              {/* Notification Frequency */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notification Frequency
                </label>
                <select
                  value={preferences.notificationSettings?.frequency || NotificationFrequency.DAILY}
                  onChange={(e) => {
                    handlePreferenceChange('notificationSettings', {
                      ...preferences.notificationSettings,
                      frequency: e.target.value as NotificationFrequency
                    })
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {Object.values(NotificationFrequency).map((freq) => (
                    <option key={freq} value={freq}>
                      {freq.charAt(0).toUpperCase() + freq.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Profile Summary */}
      {profile && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Personalization Profile</h3>
              <div className="text-xs text-gray-600 mt-1">
                Confidence Score: {profile.confidenceScore}% • 
                Last Updated: {new Date(profile.lastUpdated).toLocaleDateString()} •
                {profile.behaviorPatterns.length} behavior patterns identified
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">AI Personalization</div>
              <div className="text-xs text-green-600 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                Active & Learning
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
