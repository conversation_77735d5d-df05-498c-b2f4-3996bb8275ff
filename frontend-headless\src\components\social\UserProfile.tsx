'use client'

import React, { useState } from 'react'
import {
  UserIcon,
  Cog6ToothIcon,
  MapPinIcon,
  LinkIcon,
  CalendarIcon,
  CheckBadgeIcon,
  UserPlusIcon,
  UserMinusIcon,
  ChatBubbleLeftRightIcon,
  ShareIcon,
  EllipsisHorizontalIcon,
  TrophyIcon,
  HeartIcon,
  ChatBubbleOvalLeftIcon
} from '@heroicons/react/24/outline'
import {
  useFollowUser,
  useUnfollowUser,
  useFollowers,
  useFollowing,
  useUserPosts,
  useUpdateProfile,
  useUploadAvatar,
  useUploadCoverImage
} from '@/hooks/useSocial'
import { UserProfile as UserProfileType, BadgeRarity } from '@/types/social.types'
import SocialFeed from './SocialFeed'
import EditProfileModal from './EditProfileModal'

interface UserProfileProps {
  user: UserProfileType
  isOwnProfile: boolean
  className?: string
}

export default function UserProfile({
  user,
  isOwnProfile,
  className = ''
}: UserProfileProps) {
  const [activeTab, setActiveTab] = useState<'posts' | 'followers' | 'following' | 'achievements'>('posts')
  const [showEditProfile, setShowEditProfile] = useState(false)
  const [showFollowers, setShowFollowers] = useState(false)
  const [showFollowing, setShowFollowing] = useState(false)

  const followUserMutation = useFollowUser()
  const unfollowUserMutation = useUnfollowUser()
  const { data: followers } = useFollowers(user.id, 20, 0)
  const { data: following } = useFollowing(user.id, 20, 0)
  const { data: userPosts } = useUserPosts(user.id, 20, 0)

  const handleFollow = () => {
    followUserMutation.mutate(user.id)
  }

  const handleUnfollow = () => {
    if (confirm('Are you sure you want to unfollow this user?')) {
      unfollowUserMutation.mutate(user.id)
    }
  }

  const getBadgeColor = (rarity: BadgeRarity) => {
    switch (rarity) {
      case BadgeRarity.COMMON:
        return 'bg-gray-100 text-gray-800'
      case BadgeRarity.UNCOMMON:
        return 'bg-green-100 text-green-800'
      case BadgeRarity.RARE:
        return 'bg-blue-100 text-blue-800'
      case BadgeRarity.EPIC:
        return 'bg-purple-100 text-purple-800'
      case BadgeRarity.LEGENDARY:
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatJoinDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    })
  }

  const tabs = [
    { id: 'posts', name: 'Posts', count: user.socialStats.postsCount },
    { id: 'followers', name: 'Followers', count: user.socialStats.followersCount },
    { id: 'following', name: 'Following', count: user.socialStats.followingCount },
    { id: 'achievements', name: 'Achievements', count: user.achievements.length }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Cover Image */}
      <div className="relative">
        <div className="h-48 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg overflow-hidden">
          {user.coverImage && (
            <img
              src={user.coverImage}
              alt="Cover"
              className="w-full h-full object-cover"
            />
          )}
        </div>
        
        {/* Profile Picture */}
        <div className="absolute -bottom-16 left-6">
          <div className="relative">
            <img
              src={user.avatar || '/default-avatar.png'}
              alt={user.displayName}
              className="w-32 h-32 rounded-full border-4 border-white bg-white"
            />
            {user.isOnline && (
              <div className="absolute bottom-2 right-2 w-6 h-6 bg-green-500 border-2 border-white rounded-full"></div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="absolute bottom-4 right-4 flex items-center space-x-2">
          {isOwnProfile ? (
            <>
              <button
                onClick={() => setShowEditProfile(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Cog6ToothIcon className="h-4 w-4 mr-2" />
                Edit Profile
              </button>
            </>
          ) : (
            <>
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                Message
              </button>
              
              <button
                onClick={handleFollow}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <UserPlusIcon className="h-4 w-4 mr-2" />
                Follow
              </button>
              
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <EllipsisHorizontalIcon className="h-4 w-4" />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Profile Info */}
      <div className="pt-16 px-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">{user.displayName}</h1>
              {user.isVerified && (
                <CheckBadgeIcon className="h-6 w-6 text-blue-500" />
              )}
            </div>
            
            <p className="text-gray-600 mb-1">@{user.username}</p>
            
            {user.bio && (
              <p className="text-gray-900 mb-4">{user.bio}</p>
            )}

            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
              {user.location && (
                <div className="flex items-center">
                  <MapPinIcon className="h-4 w-4 mr-1" />
                  {user.location}
                </div>
              )}
              
              {user.website && (
                <div className="flex items-center">
                  <LinkIcon className="h-4 w-4 mr-1" />
                  <a
                    href={user.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-700"
                  >
                    {user.website.replace(/^https?:\/\//, '')}
                  </a>
                </div>
              )}
              
              <div className="flex items-center">
                <CalendarIcon className="h-4 w-4 mr-1" />
                Joined {formatJoinDate(user.joinedAt)}
              </div>
            </div>

            {/* Social Links */}
            {(user.twitterHandle || user.discordHandle || user.telegramHandle) && (
              <div className="flex items-center space-x-4 mb-4">
                {user.twitterHandle && (
                  <a
                    href={`https://twitter.com/${user.twitterHandle}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:text-blue-600"
                  >
                    Twitter
                  </a>
                )}
                {user.discordHandle && (
                  <span className="text-indigo-500">Discord: {user.discordHandle}</span>
                )}
                {user.telegramHandle && (
                  <a
                    href={`https://t.me/${user.telegramHandle}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:text-blue-600"
                  >
                    Telegram
                  </a>
                )}
              </div>
            )}
          </div>

          {/* Stats */}
          <div className="flex items-center space-x-6 text-center">
            <div>
              <div className="text-2xl font-bold text-gray-900">{user.socialStats.influenceScore}</div>
              <div className="text-sm text-gray-600">Influence</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{user.socialStats.reputationScore}</div>
              <div className="text-sm text-gray-600">Reputation</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{user.socialStats.nftCount}</div>
              <div className="text-sm text-gray-600">NFTs</div>
            </div>
          </div>
        </div>

        {/* Badges */}
        {user.badges.length > 0 && (
          <div className="mt-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Badges</h3>
            <div className="flex flex-wrap gap-2">
              {user.badges.slice(0, 6).map((badge) => (
                <div
                  key={badge.id}
                  className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getBadgeColor(badge.rarity)}`}
                  title={badge.description}
                >
                  <span className="mr-1">{badge.icon}</span>
                  {badge.name}
                </div>
              ))}
              {user.badges.length > 6 && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  +{user.badges.length - 6} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Engagement Stats */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <HeartIcon className="h-5 w-5 text-red-500" />
            </div>
            <div className="text-lg font-semibold text-gray-900">{user.socialStats.likesReceived}</div>
            <div className="text-sm text-gray-600">Likes Received</div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <ChatBubbleOvalLeftIcon className="h-5 w-5 text-blue-500" />
            </div>
            <div className="text-lg font-semibold text-gray-900">{user.socialStats.commentsReceived}</div>
            <div className="text-sm text-gray-600">Comments</div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <ShareIcon className="h-5 w-5 text-green-500" />
            </div>
            <div className="text-lg font-semibold text-gray-900">{user.socialStats.sharesReceived}</div>
            <div className="text-sm text-gray-600">Shares</div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <TrophyIcon className="h-5 w-5 text-yellow-500" />
            </div>
            <div className="text-lg font-semibold text-gray-900">{user.socialStats.totalEngagement}</div>
            <div className="text-sm text-gray-600">Total Engagement</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
              <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                {tab.count}
              </span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="px-6">
        {activeTab === 'posts' && (
          <SocialFeed userId={user.id} />
        )}

        {activeTab === 'followers' && (
          <div className="space-y-4">
            {followers?.followers.map((follower) => (
              <div key={follower.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <img
                    src={follower.avatar || '/default-avatar.png'}
                    alt={follower.displayName}
                    className="w-10 h-10 rounded-full"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{follower.displayName}</div>
                    <div className="text-xs text-gray-600">@{follower.username}</div>
                  </div>
                </div>
                
                <button className="text-sm text-blue-600 hover:text-blue-700">
                  View Profile
                </button>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'following' && (
          <div className="space-y-4">
            {following?.following.map((followedUser) => (
              <div key={followedUser.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <img
                    src={followedUser.avatar || '/default-avatar.png'}
                    alt={followedUser.displayName}
                    className="w-10 h-10 rounded-full"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{followedUser.displayName}</div>
                    <div className="text-xs text-gray-600">@{followedUser.username}</div>
                  </div>
                </div>
                
                <button className="text-sm text-blue-600 hover:text-blue-700">
                  View Profile
                </button>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'achievements' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {user.achievements.map((achievement) => (
              <div key={achievement.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-900">{achievement.name}</h3>
                    <p className="text-xs text-gray-600 mt-1">{achievement.description}</p>
                    
                    {achievement.isCompleted ? (
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Completed
                        </span>
                        {achievement.completedAt && (
                          <span className="text-xs text-gray-500 ml-2">
                            {new Date(achievement.completedAt).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    ) : (
                      <div className="mt-2">
                        <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                          <span>Progress</span>
                          <span>{achievement.progress}/{achievement.maxProgress}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Edit Profile Modal */}
      {showEditProfile && (
        <EditProfileModal
          user={user}
          onClose={() => setShowEditProfile(false)}
          onProfileUpdated={() => {
            setShowEditProfile(false)
            // Refresh profile data
          }}
        />
      )}
    </div>
  )
}
