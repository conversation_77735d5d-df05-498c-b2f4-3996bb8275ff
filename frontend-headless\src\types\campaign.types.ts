export enum CampaignType {
  SOCIAL_ENGAGEMENT = 'social_engagement',
  CONTENT_CREATION = 'content_creation',
  COMMUNITY_BUILDING = 'community_building',
  TRADING_ACTIVITY = 'trading_activity',
  REFERRAL_PROGRAM = 'referral_program',
  MILESTONE_ACHIEVEMENT = 'milestone_achievement',
  SEASONAL_EVENT = 'seasonal_event',
  BRAND_COLLABORATION = 'brand_collaboration',
  USER_ACQUISITION = 'user_acquisition',
  ENGAGEMENT = 'engagement',
  RETENTION = 'retention'
}

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum RewardType {
  NFT_GENERATION = 'nft_generation',
  TOKEN_REWARD = 'token_reward',
  POINTS = 'points',
  BADGE = 'badge',
  MARKETPLACE_CREDIT = 'marketplace_credit',
  EXCLUSIVE_ACCESS = 'exclusive_access',
  PHYSICAL_ITEM = 'physical_item'
}

export enum ParticipationStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  DISQUALIFIED = 'disqualified',
  WITHDRAWN = 'withdrawn'
}

export enum SubmissionStatus {
  PENDING = 'pending',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  REQUIRES_REVISION = 'requires_revision'
}

export interface Campaign {
  id: string
  projectId: string
  name: string
  description: string
  type: CampaignType
  status: CampaignStatus
  
  // Timeline
  startDate: string
  endDate: string
  createdAt: string
  updatedAt: string
  
  // Configuration
  maxParticipants?: number
  minAge?: number
  geoRestrictions?: string[]
  tags: string[]
  priority: number
  featured: boolean
  
  // Visual
  bannerImage?: string
  thumbnailImage?: string
  
  // Requirements and Rewards
  requirements: CampaignRequirement[]
  rewards: CampaignReward[]
  
  // NFT Generation Settings
  nftSettings: NFTGenerationSettings
  
  // Analytics
  participantCount: number
  completionRate: number
  totalRewardsDistributed: number
  
  // Metadata
  metadata: Record<string, any>
  createdBy: string
  
  // Related Data
  project?: {
    id: string
    name: string
    description: string
    ownerName: string
  }
  
  participations?: CampaignParticipation[]
  analytics?: CampaignAnalytics
}

export interface CampaignRequirement {
  id: string
  campaignId: string
  type: RequirementType
  title: string
  description: string
  isRequired: boolean
  order: number
  configuration: RequirementConfiguration
  validationRules: ValidationRule[]
  points: number
  createdAt: string
  updatedAt: string
}

export enum RequirementType {
  TWITTER_FOLLOW = 'twitter_follow',
  TWITTER_RETWEET = 'twitter_retweet',
  TWITTER_LIKE = 'twitter_like',
  TWITTER_COMMENT = 'twitter_comment',
  TWITTER_PROFILE_UPDATE = 'twitter_profile_update',
  DISCORD_JOIN = 'discord_join',
  TELEGRAM_JOIN = 'telegram_join',
  WEBSITE_VISIT = 'website_visit',
  EMAIL_SIGNUP = 'email_signup',
  WALLET_CONNECT = 'wallet_connect',
  NFT_HOLD = 'nft_hold',
  TOKEN_HOLD = 'token_hold',
  CUSTOM_TASK = 'custom_task',
  CONTENT_CREATION = 'content_creation',
  REFERRAL = 'referral'
}

export interface RequirementConfiguration {
  // Twitter-specific
  twitterUsername?: string
  tweetUrl?: string
  hashtags?: string[]
  mentions?: string[]
  
  // Discord/Telegram
  serverUrl?: string
  serverId?: string
  
  // Website
  websiteUrl?: string
  timeOnSite?: number
  
  // Blockchain
  contractAddress?: string
  tokenAmount?: number
  nftCollectionAddress?: string
  
  // Custom
  customInstructions?: string
  submissionType?: 'text' | 'image' | 'video' | 'link'
  
  // Validation
  autoValidation?: boolean
  manualReview?: boolean
}

export interface ValidationRule {
  field: string
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'regex'
  value: any
  errorMessage: string
}

export interface CampaignReward {
  id: string
  campaignId: string
  type: RewardType
  title: string
  description: string
  value: number
  currency?: string
  quantity: number
  distributedCount: number
  configuration: RewardConfiguration
  eligibilityCriteria: EligibilityCriteria
  createdAt: string
  updatedAt: string
}

export interface RewardConfiguration {
  // NFT Generation
  nftTemplate?: string
  rarityWeights?: Record<string, number>
  customAttributes?: Record<string, any>
  
  // Token Rewards
  tokenAddress?: string
  tokenSymbol?: string
  
  // Marketplace Credits
  creditAmount?: number
  expirationDate?: string
  
  // Physical Items
  shippingRequired?: boolean
  shippingRegions?: string[]
  
  // Access Rewards
  accessLevel?: string
  accessDuration?: number
}

export interface EligibilityCriteria {
  minPoints?: number
  requiredTasks?: string[]
  participationDuration?: number
  customCriteria?: Record<string, any>
}

export interface NFTGenerationSettings {
  enabled: boolean
  style: NFTStyle
  theme: string
  rarityThresholds: RarityThresholds
  customization: NFTCustomization
  blockchain: string
  contractAddress?: string
  autoMint: boolean
  batchSize: number
  generationDelay: number
}

export enum NFTStyle {
  REALISTIC = 'realistic',
  CARTOON = 'cartoon',
  ABSTRACT = 'abstract',
  PIXEL_ART = 'pixel_art',
  MINIMALIST = 'minimalist',
  CYBERPUNK = 'cyberpunk',
  FANTASY = 'fantasy',
  CUSTOM = 'custom'
}

export interface RarityThresholds {
  common: number
  rare: number
  epic: number
  legendary: number
  mythic: number
}

export interface NFTCustomization {
  backgroundColors: string[]
  primaryColors: string[]
  accentColors: string[]
  fonts: string[]
  effects: string[]
  overlays: string[]
  customPrompts: string[]
}

export interface CampaignParticipation {
  id: string
  campaignId: string
  userId: string
  status: ParticipationStatus
  joinedAt: string
  completedAt?: string
  totalPoints: number
  completedRequirements: string[]
  submissions: CampaignSubmission[]
  rewards: ParticipationReward[]
  metadata: Record<string, any>
  
  // User Info
  user?: {
    id: string
    username: string
    displayName: string
    avatar?: string
    twitterHandle?: string
  }
  
  // Campaign Info
  campaign?: {
    id: string
    name: string
    type: CampaignType
    status: CampaignStatus
  }
}

export interface CampaignSubmission {
  id: string
  participationId: string
  requirementId: string
  status: SubmissionStatus
  content: SubmissionContent
  submittedAt: string
  reviewedAt?: string
  reviewedBy?: string
  reviewNotes?: string
  points: number
  metadata: Record<string, any>
}

export interface SubmissionContent {
  type: 'text' | 'image' | 'video' | 'link' | 'file'
  value: string
  additionalData?: Record<string, any>
}

export interface ParticipationReward {
  id: string
  participationId: string
  rewardId: string
  status: 'pending' | 'distributed' | 'claimed' | 'expired'
  distributedAt?: string
  claimedAt?: string
  transactionHash?: string
  metadata: Record<string, any>
}

export interface CampaignAnalytics {
  campaignId: string
  
  // Participation Metrics
  totalParticipants: number
  activeParticipants: number
  completedParticipants: number
  dropoffRate: number
  
  // Engagement Metrics
  averageCompletionTime: number
  averagePointsPerParticipant: number
  requirementCompletionRates: Record<string, number>
  
  // Reward Metrics
  totalRewardsDistributed: number
  rewardDistributionByType: Record<RewardType, number>
  averageRewardValue: number
  
  // Time Series Data
  participationOverTime: Array<{
    date: string
    participants: number
    completions: number
  }>
  
  // Geographic Data
  participantsByRegion: Record<string, number>
  
  // Social Media Metrics
  socialEngagement: {
    tweets: number
    retweets: number
    likes: number
    comments: number
    reach: number
    impressions: number
  }
  
  // Performance Indicators
  roi: number
  costPerParticipant: number
  conversionRate: number
  viralityScore: number
}

export interface CreateCampaignRequest {
  projectId: string
  name: string
  description: string
  type: CampaignType
  startDate: string
  endDate: string
  maxParticipants?: number
  minAge?: number
  geoRestrictions?: string[]
  tags: string[]
  priority?: number
  featured?: boolean
  bannerImage?: string
  requirements: Omit<CampaignRequirement, 'id' | 'campaignId' | 'createdAt' | 'updatedAt'>[]
  rewards: Omit<CampaignReward, 'id' | 'campaignId' | 'distributedCount' | 'createdAt' | 'updatedAt'>[]
  nftSettings: NFTGenerationSettings
  metadata?: Record<string, any>
}

export interface UpdateCampaignRequest {
  name?: string
  description?: string
  endDate?: string
  maxParticipants?: number
  tags?: string[]
  priority?: number
  featured?: boolean
  bannerImage?: string
  nftSettings?: Partial<NFTGenerationSettings>
  metadata?: Record<string, any>
}

export interface CampaignFilters {
  search?: string
  type?: CampaignType[]
  status?: CampaignStatus[]
  projectId?: string
  createdBy?: string
  tags?: string[]
  featured?: boolean
  dateRange?: {
    start: string
    end: string
  }
  participantRange?: {
    min: number
    max: number
  }
  sortBy?: 'name' | 'created_at' | 'start_date' | 'participant_count' | 'completion_rate'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface CampaignTemplate {
  id: string
  name: string
  description: string
  type: CampaignType
  category: string
  tags: string[]
  requirements: Omit<CampaignRequirement, 'id' | 'campaignId' | 'createdAt' | 'updatedAt'>[]
  rewards: Omit<CampaignReward, 'id' | 'campaignId' | 'distributedCount' | 'createdAt' | 'updatedAt'>[]
  nftSettings: NFTGenerationSettings
  estimatedDuration: number
  difficulty: 'easy' | 'medium' | 'hard'
  popularity: number
  createdAt: string
  updatedAt: string
}
