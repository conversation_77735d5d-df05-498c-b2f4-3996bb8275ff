import { Controller, Get, Post, Put, Delete, Body, Param, Query, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { TemplateService } from '../services/template.service';

@ApiTags('templates')
@Controller('templates')
export class TemplateController {
  private readonly logger = new Logger(TemplateController.name);

  constructor(private readonly templateService: TemplateService) {}

  @Get()
  @ApiOperation({ summary: 'List notification templates' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by category' })
  @ApiQuery({ name: 'active', required: false, description: 'Filter by active status' })
  async listTemplates(
    @Query('category') category?: string,
    @Query('active') active?: string
  ) {
    try {
      this.logger.log('Listing notification templates');

      const isActive = active ? active === 'true' : undefined;
      const templates = await this.templateService.listTemplates(category, isActive);

      return {
        success: true,
        data: {
          templates,
          count: templates.length,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to list templates: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve templates',
        details: error.message,
      };
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get template by ID' })
  @ApiResponse({ status: 200, description: 'Template retrieved successfully' })
  @ApiParam({ name: 'id', description: 'Template ID' })
  async getTemplate(@Param('id') id: string) {
    try {
      this.logger.log(`Getting template: ${id}`);

      const template = await this.templateService.getTemplate(id);

      if (!template) {
        return {
          success: false,
          error: 'Template not found',
        };
      }

      return {
        success: true,
        data: template,
      };
    } catch (error) {
      this.logger.error(`Failed to get template: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve template',
        details: error.message,
      };
    }
  }

  @Post()
  @ApiOperation({ summary: 'Create notification template' })
  @ApiResponse({ status: 201, description: 'Template created successfully' })
  async createTemplate(@Body() templateData: any) {
    try {
      this.logger.log('Creating notification template', { name: templateData.name });

      const template = await this.templateService.createTemplate(templateData);

      return {
        success: true,
        data: template,
      };
    } catch (error) {
      this.logger.error(`Failed to create template: ${error.message}`);
      return {
        success: false,
        error: 'Failed to create template',
        details: error.message,
      };
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update notification template' })
  @ApiResponse({ status: 200, description: 'Template updated successfully' })
  @ApiParam({ name: 'id', description: 'Template ID' })
  async updateTemplate(@Param('id') id: string, @Body() updates: any) {
    try {
      this.logger.log(`Updating template: ${id}`);

      const template = await this.templateService.updateTemplate(id, updates);

      return {
        success: true,
        data: template,
      };
    } catch (error) {
      this.logger.error(`Failed to update template: ${error.message}`);
      return {
        success: false,
        error: 'Failed to update template',
        details: error.message,
      };
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete notification template' })
  @ApiResponse({ status: 200, description: 'Template deleted successfully' })
  @ApiParam({ name: 'id', description: 'Template ID' })
  async deleteTemplate(@Param('id') id: string) {
    try {
      this.logger.log(`Deleting template: ${id}`);

      const success = await this.templateService.deleteTemplate(id);

      return {
        success,
        message: success ? 'Template deleted successfully' : 'Failed to delete template',
      };
    } catch (error) {
      this.logger.error(`Failed to delete template: ${error.message}`);
      return {
        success: false,
        error: 'Failed to delete template',
        details: error.message,
      };
    }
  }
}
