# 🔐 Authentication Patterns Standardization

**Standardizing authentication, authorization, and security patterns across all services**

## 📋 Current Authentication Issues

### **Issues Identified:**
1. **Inconsistent JWT Module Setup** across services
2. **Duplicate GatewayAuthMiddleware** in each service (should be shared)
3. **Mixed Authentication Patterns** (some services have JWT guards, others don't)
4. **Inconsistent Permission Decorators** and RBAC implementation
5. **Hardcoded JWT secrets** in some configurations
6. **No standardized authentication interfaces**
7. **Inconsistent security headers** and CORS configuration
8. **Mixed authorization patterns** (guards vs decorators)

### **Impact:**
- Security vulnerabilities due to inconsistent implementations
- Difficult to maintain authentication logic across services
- No centralized authentication management
- Inconsistent user experience across services
- Hard to implement enterprise security features

## 🎯 Standardization Objectives

1. **Unified Authentication Architecture**: Consistent authentication patterns across all services
2. **Centralized Security Components**: Shared guards, decorators, and middleware
3. **Standardized JWT Handling**: Consistent token validation and management
4. **Role-Based Access Control (RBAC)**: Unified permission system
5. **Security Headers Standardization**: Consistent security middleware
6. **Enterprise Security Features**: Audit logging, session management, MFA support

## 🏗️ Standardized Authentication Architecture

### **Authentication Layer Structure**
```
shared/auth/                           # Shared authentication components
├── interfaces/                        # Authentication interfaces
│   ├── auth.interface.ts             # Core authentication interfaces
│   ├── user.interface.ts             # User and session interfaces
│   └── permission.interface.ts       # Permission and role interfaces
├── guards/                           # Authentication guards
│   ├── jwt-auth.guard.ts            # JWT authentication guard
│   ├── rbac.guard.ts                # Role-based access control guard
│   └── api-key.guard.ts             # API key authentication guard
├── decorators/                       # Authentication decorators
│   ├── auth.decorator.ts            # Authentication decorators
│   ├── permissions.decorator.ts     # Permission decorators
│   └── user.decorator.ts            # User context decorator
├── middleware/                       # Security middleware
│   ├── gateway-auth.middleware.ts   # Gateway authentication
│   ├── security-headers.middleware.ts # Security headers
│   └── rate-limit.middleware.ts     # Rate limiting
├── strategies/                       # Passport strategies
│   ├── jwt.strategy.ts              # JWT strategy
│   └── api-key.strategy.ts          # API key strategy
├── services/                         # Authentication services
│   ├── auth.service.ts              # Core authentication service
│   ├── token.service.ts             # Token management service
│   └── permission.service.ts        # Permission management service
└── auth.module.ts                    # Standardized authentication module
```

## 🔧 Core Authentication Interfaces

### **User and Session Interfaces**
```typescript
// shared/auth/interfaces/user.interface.ts
export interface AuthenticatedUser {
  id: string;
  email: string;
  username?: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  lastLoginAt?: Date;
  sessionId?: string;
}

export interface UserSession {
  sessionId: string;
  userId: string;
  deviceInfo?: DeviceInfo;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  expiresAt: Date;
  isActive: boolean;
}

export interface DeviceInfo {
  deviceId?: string;
  deviceType: 'web' | 'mobile' | 'api';
  platform?: string;
  browser?: string;
}
```

### **Permission and Role Interfaces**
```typescript
// shared/auth/interfaces/permission.interface.ts
export enum Permission {
  // User Management
  USER_READ = 'user:read',
  USER_WRITE = 'user:write',
  USER_DELETE = 'user:delete',
  USER_ADMIN = 'user:admin',
  
  // NFT Management
  NFT_READ = 'nft:read',
  NFT_WRITE = 'nft:write',
  NFT_DELETE = 'nft:delete',
  NFT_ADMIN = 'nft:admin',
  
  // Project Management
  PROJECT_READ = 'project:read',
  PROJECT_WRITE = 'project:write',
  PROJECT_DELETE = 'project:delete',
  PROJECT_ADMIN = 'project:admin',
  
  // Marketplace
  MARKETPLACE_READ = 'marketplace:read',
  MARKETPLACE_WRITE = 'marketplace:write',
  MARKETPLACE_ADMIN = 'marketplace:admin',
  
  // System Administration
  SYSTEM_READ = 'system:read',
  SYSTEM_ADMIN = 'system:admin',
  AUDIT_READ = 'audit:read',
}

export enum Role {
  GUEST = 'guest',
  USER = 'user',
  CREATOR = 'creator',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

export interface RolePermissions {
  role: Role;
  permissions: Permission[];
  description: string;
  isSystemRole: boolean;
}
```

## 🛡️ Standardized Authentication Guards

### **JWT Authentication Guard**
```typescript
// shared/auth/guards/jwt-auth.guard.ts
@Injectable()
export class StandardizedJwtAuthGuard implements CanActivate {
  constructor(
    private readonly authService: AuthService,
    private readonly reflector: Reflector,
    private readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is public
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // Extract and validate JWT token
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('Authentication token required');
    }

    // Validate token and get user
    const user = await this.authService.validateToken(token);
    if (!user) {
      throw new UnauthorizedException('Invalid or expired token');
    }

    // Add user to request context
    request.user = user;
    return true;
  }
}
```

### **RBAC Guard**
```typescript
// shared/auth/guards/rbac.guard.ts
@Injectable()
export class StandardizedRBACGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly permissionService: PermissionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Get required permissions from decorator
    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>('permissions', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true; // No specific permissions required
    }

    // Get user from request
    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;

    if (!user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Check permissions
    const hasPermission = await this.permissionService.hasAnyPermission(
      user,
      requiredPermissions
    );

    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions');
    }

    return true;
  }
}
```

## 🎨 Standardized Authentication Decorators

### **Permission Decorators**
```typescript
// shared/auth/decorators/permissions.decorator.ts
export const RequirePermissions = (permissions: Permission[]) =>
  SetMetadata('permissions', permissions);

export const RequireAnyPermission = (...permissions: Permission[]) =>
  SetMetadata('permissions', permissions);

export const Public = () => SetMetadata('isPublic', true);

export const Authenticated = () => RequirePermissions([]);

// Convenience decorators for common permissions
export const RequireUserRead = () => RequirePermissions([Permission.USER_READ]);
export const RequireUserWrite = () => RequirePermissions([Permission.USER_WRITE]);
export const RequireUserAdmin = () => RequirePermissions([Permission.USER_ADMIN]);

export const RequireNFTRead = () => RequirePermissions([Permission.NFT_READ]);
export const RequireNFTWrite = () => RequirePermissions([Permission.NFT_WRITE]);
export const RequireNFTAdmin = () => RequirePermissions([Permission.NFT_ADMIN]);

export const RequireSystemAdmin = () => RequirePermissions([Permission.SYSTEM_ADMIN]);
```

### **User Context Decorator**
```typescript
// shared/auth/decorators/user.decorator.ts
export const CurrentUser = createParamDecorator(
  (data: keyof AuthenticatedUser | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;

    return data ? user?.[data] : user;
  },
);
```

## 🔒 Security Middleware Standardization

### **Security Headers Middleware**
```typescript
// shared/auth/middleware/security-headers.middleware.ts
@Injectable()
export class SecurityHeadersMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Remove server information
    res.removeHeader('X-Powered-By');
    
    next();
  }
}
```

## 📦 Standardized Authentication Module

### **Authentication Module**
```typescript
// shared/auth/auth.module.ts
@Global()
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN') || '24h',
        },
      }),
      inject: [ConfigService],
    }),
    PassportModule,
  ],
  providers: [
    // Guards
    StandardizedJwtAuthGuard,
    StandardizedRBACGuard,
    
    // Strategies
    JwtStrategy,
    
    // Services
    AuthService,
    TokenService,
    PermissionService,
    
    // Global guards
    {
      provide: APP_GUARD,
      useClass: StandardizedJwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: StandardizedRBACGuard,
    },
  ],
  exports: [
    StandardizedJwtAuthGuard,
    StandardizedRBACGuard,
    AuthService,
    TokenService,
    PermissionService,
    JwtModule,
  ],
})
export class StandardizedAuthModule {}
```

## 🚀 Implementation Plan

### **Phase 1: Create Shared Authentication Infrastructure**
1. Create shared authentication interfaces and enums
2. Implement standardized guards and decorators
3. Create shared authentication services
4. Implement security middleware

### **Phase 2: Standardize Service Authentication**
1. Update all services to use shared authentication components
2. Remove duplicate authentication code
3. Standardize JWT module configuration
4. Update controllers to use standardized decorators

### **Phase 3: Implement RBAC System**
1. Create permission management system
2. Implement role-based access control
3. Add permission validation across services
4. Create admin interfaces for permission management

### **Phase 4: Security Enhancements**
1. Implement session management
2. Add audit logging for authentication events
3. Implement rate limiting and security headers
4. Add multi-factor authentication support

## 📊 Benefits of Standardization

1. **Security Consistency**: All services use the same security patterns
2. **Maintainability**: Centralized authentication logic
3. **Enterprise Features**: RBAC, audit logging, session management
4. **Developer Experience**: Consistent APIs and decorators
5. **Security Compliance**: Standardized security headers and practices
6. **Scalability**: Easy to add new authentication methods

## 🔍 Next Steps

1. **Create Shared Authentication Infrastructure**
2. **Implement Standardized Guards and Decorators**
3. **Update All Service Modules**
4. **Test Authentication Standardization**
5. **Implement RBAC System**
