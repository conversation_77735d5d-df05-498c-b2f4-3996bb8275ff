/**
 * Base Configuration Interface
 * Defines the standard configuration structure for all services
 */

import { 
  Environment, 
  ServiceEnvironment, 
  LogLevel, 
  LogFormat, 
  ServiceType,
  DataClassification 
} from './environment.enum';

/**
 * Base configuration interface that all services must implement
 */
export interface BaseConfig {
  // ===== SERVICE IDENTIFICATION =====
  serviceName: string;
  serviceVersion: string;
  servicePort: number;
  serviceType: ServiceType;

  // ===== ENVIRONMENT CONFIGURATION =====
  nodeEnv: Environment;
  serviceEnvironment: ServiceEnvironment;
  useMockServices: boolean;

  // ===== SECURITY CONFIGURATION =====
  jwtSecret: string;
  jwtExpiresIn: string;
  jwtRefreshExpiresIn: string;
  gatewaySecret: string;
  allowDirectAccess: boolean;
  trustedIPs: string[];
  enableGatewayAuth: boolean;

  // ===== DATABASE CONFIGURATION =====
  databaseUrl: string;
  dbHost: string;
  dbPort: number;
  dbUsername: string;
  dbPassword: string;
  dbDatabase: string;

  // ===== CACHING CONFIGURATION =====
  redisHost: string;
  redisPort: number;
  redisPassword?: string;
  redisUrl: string;
  enableRedisCache: boolean;
  cacheTtl: number;

  // ===== LOGGING CONFIGURATION =====
  logLevel: LogLevel;
  logFormat: LogFormat;
  enableDebugLogging: boolean;

  // ===== DEVELOPMENT FLAGS =====
  enableCors: boolean;
  enableSwagger: boolean;
  enableHotReload: boolean;
  enablePerformanceMonitoring: boolean;

  // ===== MONITORING & OBSERVABILITY =====
  enableMetrics: boolean;
  metricsPort: number;
  enableHealthChecks: boolean;
  healthCheckInterval: number;

  // ===== ENTERPRISE FEATURES =====
  enableAuditLogging: boolean;
  enableEventSourcing: boolean;
  enableCorrelationTracking: boolean;
  dataClassification: DataClassification;
  retentionPolicy: string;

  // ===== FEATURE FLAGS =====
  enableRateLimiting: boolean;
  enableApiVersioning: boolean;
}

/**
 * Database configuration interface
 */
export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  url: string;
  ssl?: boolean;
  maxConnections?: number;
  connectionTimeout?: number;
  queryTimeout?: number;
}

/**
 * Security configuration interface
 */
export interface SecurityConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  jwtRefreshExpiresIn: string;
  gatewaySecret: string;
  allowDirectAccess: boolean;
  trustedIPs: string[];
  enableGatewayAuth: boolean;
  corsOrigins: string[];
  enableCsrf: boolean;
  enableHelmet: boolean;
}

/**
 * External services configuration interface
 */
export interface ExternalServicesConfig {
  apiGatewayUrl: string;
  userServiceUrl: string;
  profileAnalysisServiceUrl: string;
  nftGenerationServiceUrl: string;
  blockchainServiceUrl: string;
  projectServiceUrl: string;
  marketplaceServiceUrl: string;
  notificationServiceUrl: string;
  analyticsServiceUrl: string;
}

/**
 * Cache configuration interface
 */
export interface CacheConfig {
  host: string;
  port: number;
  password?: string;
  url: string;
  enabled: boolean;
  ttl: number;
  maxMemory?: string;
  evictionPolicy?: string;
}

/**
 * Logging configuration interface
 */
export interface LoggingConfig {
  level: LogLevel;
  format: LogFormat;
  enableDebugLogging: boolean;
  enableFileLogging: boolean;
  logDirectory?: string;
  maxFileSize?: string;
  maxFiles?: number;
  enableConsoleLogging: boolean;
}

/**
 * Monitoring configuration interface
 */
export interface MonitoringConfig {
  enableMetrics: boolean;
  metricsPort: number;
  enableHealthChecks: boolean;
  healthCheckInterval: number;
  enableTracing: boolean;
  tracingEndpoint?: string;
  enableAlerting: boolean;
  alertingWebhook?: string;
}

/**
 * Feature flags configuration interface
 */
export interface FeatureFlagsConfig {
  enableRateLimiting: boolean;
  enableApiVersioning: boolean;
  enableCaching: boolean;
  enableCompression: boolean;
  enableRequestLogging: boolean;
  enableResponseCaching: boolean;
  enableSwagger: boolean;
  enableCors: boolean;
}

/**
 * Service-specific configuration interface
 * Services can extend this for their specific needs
 */
export interface ServiceSpecificConfig {
  [key: string]: any;
}

/**
 * Complete service configuration interface
 */
export interface ServiceConfig extends BaseConfig {
  database: DatabaseConfig;
  security: SecurityConfig;
  externalServices: ExternalServicesConfig;
  cache: CacheConfig;
  logging: LoggingConfig;
  monitoring: MonitoringConfig;
  featureFlags: FeatureFlagsConfig;
  serviceSpecific: ServiceSpecificConfig;
}

/**
 * Configuration validation result
 */
export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Configuration metadata
 */
export interface ConfigMetadata {
  lastUpdated: Date;
  version: string;
  environment: Environment;
  serviceEnvironment: ServiceEnvironment;
  configSource: string;
}
