#!/bin/bash

# Complete Pattern Validation Script
# Validates all 6 phases of enterprise standardization across all services

set -e

echo "🔍 Complete Enterprise Standardization Validation"
echo "================================================="
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Services to validate
SERVICES=(
    "api-gateway"
    "user-service"
    "profile-analysis-service"
    "nft-generation-service"
    "blockchain-service"
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Function to print colored output
print_phase() {
    echo -e "${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((PASSED_CHECKS++))
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((WARNING_CHECKS++))
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    ((FAILED_CHECKS++))
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Function to validate file exists
validate_file_exists() {
    local file_path="$1"
    local description="$2"
    
    ((TOTAL_CHECKS++))
    if [ -f "$file_path" ]; then
        print_success "$description exists: $file_path"
        return 0
    else
        print_error "$description missing: $file_path"
        return 1
    fi
}

# Function to validate directory exists
validate_directory_exists() {
    local dir_path="$1"
    local description="$2"
    
    ((TOTAL_CHECKS++))
    if [ -d "$dir_path" ]; then
        print_success "$description exists: $dir_path"
        return 0
    else
        print_error "$description missing: $dir_path"
        return 1
    fi
}

# Function to validate file contains pattern
validate_file_contains() {
    local file_path="$1"
    local pattern="$2"
    local description="$3"
    
    ((TOTAL_CHECKS++))
    if [ -f "$file_path" ] && grep -q "$pattern" "$file_path"; then
        print_success "$description found in $file_path"
        return 0
    else
        print_error "$description not found in $file_path"
        return 1
    fi
}

# Function to validate service structure
validate_service_structure() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    echo ""
    print_info "Validating service: $service_name"
    echo "$(printf '-%.0s' {1..30})"
    
    # Check if service directory exists
    if [ ! -d "$service_dir" ]; then
        print_error "Service directory not found: $service_dir"
        return 1
    fi
    
    # Phase 1: Environment Variables
    validate_file_exists "$service_dir/.env.example" "Environment example file"
    
    # Phase 2: Configuration
    validate_directory_exists "$service_dir/src/config" "Configuration directory"
    validate_file_exists "$service_dir/src/config/configuration.module.ts" "Configuration module"
    validate_file_exists "$service_dir/src/config/configuration.service.ts" "Configuration service"
    
    # Phase 3: Authentication
    validate_directory_exists "$service_dir/src/auth" "Authentication directory"
    validate_file_exists "$service_dir/src/auth/auth.module.ts" "Authentication module"
    
    # Phase 4: Responses
    validate_directory_exists "$service_dir/src/responses" "Responses directory"
    validate_file_exists "$service_dir/src/responses/responses.module.ts" "Responses module"
    validate_file_exists "$service_dir/src/responses/services/response.service.ts" "Response service"
    
    # Phase 5: Logging
    validate_directory_exists "$service_dir/src/logging" "Logging directory"
    validate_file_exists "$service_dir/src/logging/logging.module.ts" "Logging module"
    validate_file_exists "$service_dir/src/logging/services/service-logger.service.ts" "Service logger"
    
    # Phase 6: Data Layer
    validate_directory_exists "$service_dir/src/data" "Data directory"
    validate_file_exists "$service_dir/src/data/data.module.ts" "Data module"
    validate_file_exists "$service_dir/src/data/services/database.service.ts" "Database service"
    
    # Health endpoints
    validate_directory_exists "$service_dir/src/health" "Health directory"
    
    # App module integration
    validate_file_contains "$service_dir/src/app.module.ts" "ServiceConfigurationModule" "Configuration module import"
    validate_file_contains "$service_dir/src/app.module.ts" "ServiceAuthModule" "Auth module import"
    validate_file_contains "$service_dir/src/app.module.ts" "ServiceResponseModule" "Response module import"
    validate_file_contains "$service_dir/src/app.module.ts" "ServiceLoggingModule" "Logging module import"
    validate_file_contains "$service_dir/src/app.module.ts" "ServiceDataModule" "Data module import"
}

# Create logs directory
mkdir -p logs/validation

# Log file for this run
LOG_FILE="logs/validation/pattern_validation_$(date +%Y%m%d_%H%M%S).log"
echo "📝 Logging to: $LOG_FILE"
echo ""

# Start validation
echo "$(date): Starting Complete Pattern Validation" >> "$LOG_FILE"

# Phase 1: Validate Shared Infrastructure
print_phase "🏗️ Validating Shared Infrastructure"

# Shared auth
validate_directory_exists "shared/auth" "Shared auth directory"
validate_file_exists "shared/auth/interfaces/auth.interface.ts" "Auth interfaces"
validate_file_exists "shared/auth/services/jwt.service.ts" "JWT service"
validate_file_exists "shared/auth/guards/jwt-auth.guard.ts" "JWT auth guard"
validate_file_exists "shared/auth/guards/roles.guard.ts" "Roles guard"
validate_file_exists "shared/auth/guards/permissions.guard.ts" "Permissions guard"

# Shared responses
validate_directory_exists "shared/responses" "Shared responses directory"
validate_file_exists "shared/responses/interfaces/base-response.interface.ts" "Response interfaces"
validate_file_exists "shared/responses/services/response.service.ts" "Response service"
validate_file_exists "shared/responses/interceptors/response-transform.interceptor.ts" "Response interceptor"

# Shared logging
validate_directory_exists "shared/logging" "Shared logging directory"
validate_file_exists "shared/logging/interfaces/logger.interface.ts" "Logger interfaces"
validate_file_exists "shared/logging/services/structured-logger.service.ts" "Structured logger"
validate_file_exists "shared/logging/services/metrics.service.ts" "Metrics service"

# Shared data
validate_directory_exists "shared/data" "Shared data directory"
validate_file_exists "shared/data/interfaces/repository.interface.ts" "Repository interfaces"
validate_file_exists "shared/data/services/prisma.service.ts" "Prisma service"
validate_file_exists "shared/data/repositories/base.repository.ts" "Base repository"

# Phase 2: Validate Platform Configuration
print_phase "📋 Validating Platform Configuration"

validate_file_exists ".env.example" "Platform environment example"
validate_file_exists "package.json" "Package.json"

# Check package.json for required scripts
validate_file_contains "package.json" "validate:all" "Validation scripts"
validate_file_contains "package.json" "test:all" "Test scripts"
validate_file_contains "package.json" "health:all" "Health check scripts"

# Phase 3: Validate All Services
print_phase "🔍 Validating All Services"

for service_name in "${SERVICES[@]}"; do
    validate_service_structure "$service_name" 2>&1 | tee -a "$LOG_FILE"
done

# Phase 4: Validate Documentation
print_phase "📚 Validating Documentation"

validate_file_exists "docs/ENTERPRISE_STANDARDIZATION_GUIDE.md" "Enterprise standardization guide"
validate_file_exists "docs/DEVELOPMENT_RULES_AND_ENFORCEMENT.md" "Development rules"
validate_file_exists "docs/AI_AGENT_CONFIGURATION.md" "AI agent configuration"
validate_file_exists "docs/TROUBLESHOOTING_AND_FAQ.md" "Troubleshooting guide"
validate_file_exists "docs/examples/COMPLETE_IMPLEMENTATION_EXAMPLES.md" "Implementation examples"

# Validate phase-specific documentation
validate_file_exists "docs/configuration/environment-variables-standardization.md" "Environment variables guide"
validate_file_exists "docs/configuration/application-configuration-standardization.md" "Configuration guide"
validate_file_exists "docs/configuration/authentication-standardization.md" "Authentication guide"
validate_file_exists "docs/configuration/api-response-standardization.md" "API response guide"
validate_file_exists "docs/configuration/logging-monitoring-standardization.md" "Logging guide"
validate_file_exists "docs/configuration/data-layer-standardization.md" "Data layer guide"

# Phase 5: Validate Scripts and Tools
print_phase "🛠️ Validating Scripts and Tools"

validate_file_exists "scripts/implement-environment-standardization.sh" "Environment implementation script"
validate_file_exists "scripts/implement-configuration-standardization.sh" "Configuration implementation script"
validate_file_exists "scripts/implement-authentication-standardization.sh" "Authentication implementation script"
validate_file_exists "scripts/implement-response-standardization.sh" "Response implementation script"
validate_file_exists "scripts/implement-logging-standardization.sh" "Logging implementation script"
validate_file_exists "scripts/implement-data-layer-standardization.sh" "Data layer implementation script"
validate_file_exists "scripts/implement-all-standardization.sh" "Complete implementation script"

# Phase 6: Run Additional Validations
print_phase "🔧 Running Additional Validations"

# Check for forbidden patterns
echo ""
print_info "Checking for forbidden patterns..."

# Check for direct process.env usage (should use ConfigService)
if find services -name "*.ts" -not -path "*/node_modules/*" -exec grep -l "process\.env\." {} \; | head -5; then
    print_warning "Found direct process.env usage - should use ConfigService"
else
    print_success "No direct process.env usage found"
fi

# Check for console.log usage (should use structured logging)
if find services -name "*.ts" -not -path "*/node_modules/*" -exec grep -l "console\.log\|console\.error\|console\.warn" {} \; | head -5; then
    print_warning "Found console logging - should use structured logging"
else
    print_success "No console logging found"
fi

# Check for direct Prisma usage (should use repository pattern)
if find services -name "*.ts" -not -path "*/node_modules/*" -not -path "*/data/services/*" -exec grep -l "this\.prisma\." {} \; | head -5; then
    print_warning "Found direct Prisma usage - should use repository pattern"
else
    print_success "No direct Prisma usage found outside data services"
fi

# Generate final report
echo ""
print_phase "📊 Validation Summary Report"

echo "🔍 Validation Complete!" | tee -a "$LOG_FILE"
echo ""
echo "📊 Validation Statistics:" | tee -a "$LOG_FILE"
echo "========================" | tee -a "$LOG_FILE"
echo "Total Checks: $TOTAL_CHECKS" | tee -a "$LOG_FILE"
echo "Passed: $PASSED_CHECKS" | tee -a "$LOG_FILE"
echo "Failed: $FAILED_CHECKS" | tee -a "$LOG_FILE"
echo "Warnings: $WARNING_CHECKS" | tee -a "$LOG_FILE"
echo ""

# Calculate success rate
if [ $TOTAL_CHECKS -gt 0 ]; then
    SUCCESS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    echo "Success Rate: $SUCCESS_RATE%" | tee -a "$LOG_FILE"
else
    echo "Success Rate: N/A" | tee -a "$LOG_FILE"
fi

echo ""

# Determine overall status
if [ $FAILED_CHECKS -eq 0 ]; then
    if [ $WARNING_CHECKS -eq 0 ]; then
        print_success "All patterns validated successfully! 🎉"
        echo "✅ Enterprise standardization is fully compliant" | tee -a "$LOG_FILE"
    else
        print_warning "Validation completed with warnings"
        echo "⚠️  Some minor issues found - review warnings above" | tee -a "$LOG_FILE"
    fi
    exit 0
else
    print_error "Validation failed with $FAILED_CHECKS errors"
    echo "❌ Enterprise standardization has compliance issues" | tee -a "$LOG_FILE"
    echo "🔧 Please fix the errors above and run validation again" | tee -a "$LOG_FILE"
    exit 1
fi
