import { Injectable, Logger } from '@nestjs/common';
import { StandardizedConfigService } from '@shared';
import { HttpService } from '@nestjs/axios';
import { 
  HealthIndicatorResult, 
  HealthIndicator, 
  HealthCheckError 
} from '@nestjs/terminus';
import { firstValueFrom, timeout, catchError } from 'rxjs';
import { of } from 'rxjs';

@Injectable()
export class GatewayHealthService extends HealthIndicator {
  private readonly logger = new Logger(GatewayHealthService.name);

  constructor(
    private readonly configService: StandardizedConfigService,
    private readonly httpService: HttpService,
  ) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    const isHealthy = true; // Gateway is always healthy if it can respond
    
    const result = this.getStatus(key, isHealthy, {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString(),
    });

    if (isHealthy) {
      this.logger.debug(`Gateway health check passed for ${key}`);
      return result;
    }

    this.logger.error(`Gateway health check failed for ${key}`);
    throw new HealthCheckError('Gateway health check failed', result);
  }

  async checkAllServices(): Promise<HealthIndicatorResult> {
    const services = {
      user: this.configService.get<string>('USER_SERVICE_URL'),
      project: this.configService.get<string>('PROJECT_SERVICE_URL'),
      nftGenerator: this.configService.get<string>('NFT_GENERATOR_SERVICE_URL'),
      blockchain: this.configService.get<string>('BLOCKCHAIN_SERVICE_URL'),
      marketplace: this.configService.get<string>('MARKETPLACE_SERVICE_URL'),
      analytics: this.configService.get<string>('ANALYTICS_SERVICE_URL'),
      profileAnalysis: this.configService.get<string>('PROFILE_ANALYSIS_SERVICE_URL'),
    };

    const serviceResults = {};
    let allHealthy = true;

    for (const [serviceName, serviceUrl] of Object.entries(services)) {
      if (serviceUrl) {
        try {
          const isHealthy = await this.checkServiceHealth(serviceUrl);
          serviceResults[serviceName] = {
            status: isHealthy ? 'up' : 'down',
            url: serviceUrl,
            timestamp: new Date().toISOString(),
          };
          
          if (!isHealthy) {
            allHealthy = false;
          }
        } catch (error) {
          serviceResults[serviceName] = {
            status: 'down',
            url: serviceUrl,
            error: error.message,
            timestamp: new Date().toISOString(),
          };
          allHealthy = false;
        }
      } else {
        serviceResults[serviceName] = {
          status: 'not_configured',
          timestamp: new Date().toISOString(),
        };
      }
    }

    const result = this.getStatus('services', allHealthy, serviceResults);

    if (allHealthy) {
      this.logger.log('All services health check passed');
      return result;
    }

    this.logger.warn('Some services health check failed', serviceResults);
    // Don't throw error for services - gateway can work with some services down
    return result;
  }

  private async checkServiceHealth(serviceUrl: string): Promise<boolean> {
    try {
      const healthUrl = `${serviceUrl}/api/health/simple`;
      
      const response$ = this.httpService.get(healthUrl).pipe(
        timeout(5000),
        catchError((error) => {
          this.logger.debug(`Health check failed for ${serviceUrl}: ${error.message}`);
          return of({ data: null, status: 0 });
        })
      );

      const response = await firstValueFrom(response$);
      
      const isHealthy = response.status === 200 && response.data;
      
      this.logger.debug(`Health check for ${serviceUrl}: ${isHealthy ? 'healthy' : 'unhealthy'}`);
      
      return isHealthy;
    } catch (error) {
      this.logger.debug(`Health check error for ${serviceUrl}: ${error.message}`);
      return false;
    }
  }
}
