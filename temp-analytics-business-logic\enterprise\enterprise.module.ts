// Enterprise Analytics Module - Enhanced with Analytics Services
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AnalyticsCommandController } from './controllers/analytics-command.controller';
import { AnalyticsQueryController } from './controllers/analytics-query.controller';
import { PrismaService } from './shared/prisma.service';

// Enhanced analytics module
import { AnalyticsModule } from '../analytics/analytics.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    }),
    // Enhanced analytics services
    AnalyticsModule,
  ],
  controllers: [
    AnalyticsCommandController,
    AnalyticsQueryController
  ],
  providers: [PrismaService],
  exports: [PrismaService]
})
export class EnterpriseModule {}
