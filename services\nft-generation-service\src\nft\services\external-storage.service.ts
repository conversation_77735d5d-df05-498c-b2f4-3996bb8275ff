import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import FormData from 'form-data';

export interface NFTMetadata {
  name: string;
  description: string;
  image: string;
  attributes: Array<{
    trait_type: string;
    value: string | number;
  }>;
  external_url?: string;
  animation_url?: string;
  background_color?: string;
}

export interface StorageUploadResult {
  success: boolean;
  metadataUri?: string;
  imageUri?: string;
  ipfsHash?: string;
  error?: string;
}

export interface ImageUploadResult {
  success: boolean;
  imageUri?: string;
  ipfsHash?: string;
  error?: string;
}

@Injectable()
export class ExternalStorageService {
  private readonly logger = new Logger(ExternalStorageService.name);
  private readonly nftStorageApiKey: string;
  private readonly pinataApiKey: string;
  private readonly pinataSecretKey: string;
  private readonly ipfsProvider: string;

  constructor(private readonly configService: ConfigService) {
    this.nftStorageApiKey = this.configService.get<string>('NFT_STORAGE_API_KEY');
    this.pinataApiKey = this.configService.get<string>('PINATA_API_KEY');
    this.pinataSecretKey = this.configService.get<string>('PINATA_SECRET_API_KEY');
    this.ipfsProvider = this.configService.get<string>('IPFS_PROVIDER', 'nft.storage');
    
    this.logger.log(`External Storage Service initialized with provider: ${this.ipfsProvider}`);
  }

  /**
   * Upload NFT image to external storage (NFT.Storage or Pinata)
   */
  async uploadImage(imageBuffer: Buffer, filename: string): Promise<ImageUploadResult> {
    try {
      this.logger.log(`Uploading image: ${filename} using ${this.ipfsProvider}`);

      if (this.ipfsProvider === 'nft.storage') {
        return await this.uploadImageToNFTStorage(imageBuffer, filename);
      } else if (this.ipfsProvider === 'pinata') {
        return await this.uploadImageToPinata(imageBuffer, filename);
      } else {
        throw new BadRequestException(`Unsupported IPFS provider: ${this.ipfsProvider}`);
      }
    } catch (error) {
      this.logger.error(`Failed to upload image: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Upload NFT metadata to external storage
   */
  async uploadMetadata(metadata: NFTMetadata): Promise<StorageUploadResult> {
    try {
      this.logger.log(`Uploading metadata for NFT: ${metadata.name} using ${this.ipfsProvider}`);

      if (this.ipfsProvider === 'nft.storage') {
        return await this.uploadMetadataToNFTStorage(metadata);
      } else if (this.ipfsProvider === 'pinata') {
        return await this.uploadMetadataToPinata(metadata);
      } else {
        throw new BadRequestException(`Unsupported IPFS provider: ${this.ipfsProvider}`);
      }
    } catch (error) {
      this.logger.error(`Failed to upload metadata: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Upload image to NFT.Storage
   */
  private async uploadImageToNFTStorage(imageBuffer: Buffer, filename: string): Promise<ImageUploadResult> {
    if (!this.nftStorageApiKey) {
      throw new BadRequestException('NFT.Storage API key not configured');
    }

    const formData = new FormData();
    formData.append('file', imageBuffer, {
      filename,
      contentType: 'image/png'
    });

    const response = await axios.post('https://api.nft.storage/upload', formData, {
      headers: {
        'Authorization': `Bearer ${this.nftStorageApiKey}`,
        ...formData.getHeaders()
      }
    });

    if (response.data.ok) {
      const ipfsHash = response.data.value.cid;
      const imageUri = `https://nftstorage.link/ipfs/${ipfsHash}`;
      
      this.logger.log(`Image uploaded to NFT.Storage: ${imageUri}`);
      
      return {
        success: true,
        imageUri,
        ipfsHash
      };
    } else {
      throw new Error(`NFT.Storage upload failed: ${response.data.error?.message}`);
    }
  }

  /**
   * Upload metadata to NFT.Storage
   */
  private async uploadMetadataToNFTStorage(metadata: NFTMetadata): Promise<StorageUploadResult> {
    if (!this.nftStorageApiKey) {
      throw new BadRequestException('NFT.Storage API key not configured');
    }

    const metadataBuffer = Buffer.from(JSON.stringify(metadata, null, 2));
    const formData = new FormData();
    formData.append('file', metadataBuffer, {
      filename: 'metadata.json',
      contentType: 'application/json'
    });

    const response = await axios.post('https://api.nft.storage/upload', formData, {
      headers: {
        'Authorization': `Bearer ${this.nftStorageApiKey}`,
        ...formData.getHeaders()
      }
    });

    if (response.data.ok) {
      const ipfsHash = response.data.value.cid;
      const metadataUri = `https://nftstorage.link/ipfs/${ipfsHash}`;
      
      this.logger.log(`Metadata uploaded to NFT.Storage: ${metadataUri}`);
      
      return {
        success: true,
        metadataUri,
        ipfsHash
      };
    } else {
      throw new Error(`NFT.Storage metadata upload failed: ${response.data.error?.message}`);
    }
  }

  /**
   * Upload image to Pinata
   */
  private async uploadImageToPinata(imageBuffer: Buffer, filename: string): Promise<ImageUploadResult> {
    if (!this.pinataApiKey || !this.pinataSecretKey) {
      throw new BadRequestException('Pinata API credentials not configured');
    }

    const formData = new FormData();
    formData.append('file', imageBuffer, {
      filename,
      contentType: 'image/png'
    });

    const pinataMetadata = JSON.stringify({
      name: filename,
      keyvalues: {
        type: 'nft-image',
        uploadedAt: new Date().toISOString()
      }
    });
    formData.append('pinataMetadata', pinataMetadata);

    const response = await axios.post('https://api.pinata.cloud/pinning/pinFileToIPFS', formData, {
      headers: {
        'pinata_api_key': this.pinataApiKey,
        'pinata_secret_api_key': this.pinataSecretKey,
        ...formData.getHeaders()
      }
    });

    if (response.data.IpfsHash) {
      const ipfsHash = response.data.IpfsHash;
      const imageUri = `https://gateway.pinata.cloud/ipfs/${ipfsHash}`;
      
      this.logger.log(`Image uploaded to Pinata: ${imageUri}`);
      
      return {
        success: true,
        imageUri,
        ipfsHash
      };
    } else {
      throw new Error(`Pinata upload failed: ${JSON.stringify(response.data)}`);
    }
  }

  /**
   * Upload metadata to Pinata
   */
  private async uploadMetadataToPinata(metadata: NFTMetadata): Promise<StorageUploadResult> {
    if (!this.pinataApiKey || !this.pinataSecretKey) {
      throw new BadRequestException('Pinata API credentials not configured');
    }

    const response = await axios.post('https://api.pinata.cloud/pinning/pinJSONToIPFS', {
      pinataContent: metadata,
      pinataMetadata: {
        name: `${metadata.name}-metadata`,
        keyvalues: {
          type: 'nft-metadata',
          nftName: metadata.name,
          uploadedAt: new Date().toISOString()
        }
      }
    }, {
      headers: {
        'pinata_api_key': this.pinataApiKey,
        'pinata_secret_api_key': this.pinataSecretKey,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.IpfsHash) {
      const ipfsHash = response.data.IpfsHash;
      const metadataUri = `https://gateway.pinata.cloud/ipfs/${ipfsHash}`;
      
      this.logger.log(`Metadata uploaded to Pinata: ${metadataUri}`);
      
      return {
        success: true,
        metadataUri,
        ipfsHash
      };
    } else {
      throw new Error(`Pinata metadata upload failed: ${JSON.stringify(response.data)}`);
    }
  }

  /**
   * Get storage provider status
   */
  async getStorageStatus(): Promise<any> {
    return {
      provider: this.ipfsProvider,
      configured: this.isConfigured(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Check if storage provider is properly configured
   */
  private isConfigured(): boolean {
    if (this.ipfsProvider === 'nft.storage') {
      return !!this.nftStorageApiKey;
    } else if (this.ipfsProvider === 'pinata') {
      return !!(this.pinataApiKey && this.pinataSecretKey);
    }
    return false;
  }
}
