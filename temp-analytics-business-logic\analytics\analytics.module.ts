import { Module } from '@nestjs/common';

// Analytics services
import { EventTrackingService } from './services/event-tracking.service';
import { UserBehaviorService } from './services/user-behavior.service';
import { PerformanceAnalyticsService } from './services/performance-analytics.service';

@Module({
  providers: [
    EventTrackingService,
    UserBehaviorService,
    PerformanceAnalyticsService,
  ],
  exports: [
    EventTrackingService,
    UserBehaviorService,
    PerformanceAnalyticsService,
  ],
})
export class AnalyticsModule {}
