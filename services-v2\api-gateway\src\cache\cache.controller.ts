import { Controller, Get, Post, Delete, Param } from '@nestjs/common';
import { 
  ResponseService, 
  RequirePermissions, 
  Permission, 
  Public 
} from '../../shared';
import { CacheService } from './cache.service';

/**
 * Cache Controller
 * 
 * Provides REST API for cache monitoring and management.
 */
@Controller('cache')
export class CacheController {
  constructor(
    private readonly cacheService: CacheService,
    private readonly responseService: ResponseService
  ) {}

  /**
   * Get cache statistics
   */
  @Get('stats')
  @RequirePermissions(Permission.ADMIN)
  async getStats() {
    const stats = this.cacheService.getStats();
    return this.responseService.success(stats, 'Cache statistics retrieved');
  }

  /**
   * Get cache entries (for debugging)
   */
  @Get('entries')
  @RequirePermissions(Permission.ADMIN)
  async getEntries() {
    const entries = this.cacheService.getEntries();
    return this.responseService.success(entries, 'Cache entries retrieved');
  }

  /**
   * Check if key exists in cache
   */
  @Get('exists/:key')
  @RequirePermissions(Permission.ADMIN)
  async checkKey(@Param('key') key: string) {
    const exists = await this.cacheService.has(key);
    return this.responseService.success(
      { key, exists },
      `Key ${key} ${exists ? 'exists' : 'does not exist'} in cache`
    );
  }

  /**
   * Delete specific cache entry
   */
  @Delete('entries/:key')
  @RequirePermissions(Permission.ADMIN)
  async deleteEntry(@Param('key') key: string) {
    const deleted = await this.cacheService.delete(key);
    
    if (deleted) {
      return this.responseService.success(
        { key, deleted: true },
        `Cache entry ${key} deleted successfully`
      );
    } else {
      return this.responseService.notFound('Cache entry', key);
    }
  }

  /**
   * Clear all cache entries
   */
  @Post('clear')
  @RequirePermissions(Permission.ADMIN)
  async clearCache() {
    await this.cacheService.clear();
    return this.responseService.success(
      { action: 'clear', scope: 'all' },
      'All cache entries have been cleared'
    );
  }

  /**
   * Health check for cache system
   */
  @Get('health')
  @Public()
  async health() {
    const healthStatus = this.cacheService.getHealthStatus();
    return this.responseService.success(healthStatus, 'Cache system is healthy');
  }
}
