import {
  <PERSON>,
  Get,
  Param,
  Query,
  <PERSON>s,
  HttpStatus,
  ParseIntPipe,
  DefaultValuePipe,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { CampaignQueryService } from '../services/campaign-query.service';

@ApiTags('Campaigns')
@Controller('campaigns')
export class CampaignQueryController {
  private readonly logger = new Logger(CampaignQueryController.name);

  constructor(private readonly campaignQueryService: CampaignQueryService) {}

  @Get()
  @ApiOperation({
    summary: 'Get campaigns list',
    description: 'Retrieve campaigns with filtering and pagination'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'projectId', required: false, description: 'Filter by project ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiResponse({ status: 200, description: 'Campaigns list retrieved successfully' })
  async getCampaigns(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('projectId') projectId: string,
    @Query('status') status: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting campaigns list - page: ${page}, limit: ${limit}`);
      
      const query = { page, limit, projectId, status };
      const result = await this.campaignQueryService.getCampaigns(query);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Campaigns list retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get campaign by ID',
    description: 'Retrieve campaign details by ID'
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: 200, description: 'Campaign retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  async getCampaignById(@Param('id') id: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting campaign by ID: ${id}`);
      
      const result = await this.campaignQueryService.getCampaignById(id);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Campaign retrieval failed for ID ${id}: ${error.message}`, error.stack);
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
