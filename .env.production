# Production Environment Configuration
# Use this configuration for production deployment (REAL SERVICES ONLY)

NODE_ENV=production
USE_MOCK_SERVICES=false

# API Gateway Configuration
API_GATEWAY_PORT=3010

# Production Service URLs (Real Services Only)
TWITTER_SERVICE_URL=http://localhost:3002
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
NFT_STORAGE_SERVICE_URL=http://localhost:3006
USER_SERVICE_URL=http://localhost:3011
PROJECT_SERVICE_URL=http://localhost:3005
ANALYTICS_SERVICE_URL=http://localhost:3001

# Database Configuration (Production Database)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111

# Service-specific Database Names
USER_DB_NAME=user_service_
PROJECT_DB_NAME=project_service
ANALYTICS_DB_NAME=analytics_service

# JWT Configuration (Use strong secret in production)
JWT_SECRET=your-production-secret-key-here
JWT_EXPIRES_IN=24h

# External API Configuration (Production APIs)
TWITTER_API_KEY=your-production-twitter-api-key
TWITTER_API_SECRET=your-production-twitter-api-secret
TWITTER_BEARER_TOKEN=your-production-twitter-bearer-token

BLOCKCHAIN_RPC_URL=your-production-blockchain-rpc-url
BLOCKCHAIN_PRIVATE_KEY=your-production-blockchain-private-key

NFT_STORAGE_API_KEY=your-production-nft-storage-api-key

# Logging Configuration (Production)
LOG_LEVEL=warn
LOG_FORMAT=json

# Production Security Settings
ENABLE_CORS=false
ENABLE_SWAGGER=false
ENABLE_DEBUG_LOGGING=false

# Rate Limiting (Production)
THROTTLE_TTL=60000
THROTTLE_LIMIT=100

# SSL/TLS Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# Monitoring and Health Checks
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
MONITORING_ENDPOINT=/metrics
