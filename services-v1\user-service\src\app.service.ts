import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo() {
    return {
      name: 'User Service',
      version: '1.0.0',
      description: 'Industry Standard Microservices Implementation',
      architecture: 'Independent Microservice',
      dependencies: 'Service-Local Only',
      features: [
        'User Management',
        'Authentication & Authorization',
        'Twitter Integration',
        'Real-time WebSocket Support',
        'Health Monitoring',
        'API Documentation',
      ],
      timestamp: new Date().toISOString(),
    };
  }

  getStatus() {
    return {
      status: 'healthy',
      service: 'user-service',
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      architecture: 'Industry Standard Microservice',
      sharedDependencies: 'None',
    };
  }
}
