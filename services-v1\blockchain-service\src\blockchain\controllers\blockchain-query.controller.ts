import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Query,
  <PERSON><PERSON>,
  HttpStatus,
  ParseIntPipe,
  DefaultValuePipe,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { BlockchainQueryService } from '../services/blockchain-query.service';

@ApiTags('Blockchain')
@Controller('blockchain')
export class BlockchainQueryController {
  private readonly logger = new Logger(BlockchainQueryController.name);

  constructor(private readonly blockchainQueryService: BlockchainQueryService) {}

  @Get('networks')
  @ApiOperation({
    summary: 'Get supported blockchain networks',
    description: 'Retrieve list of supported blockchain networks and their status'
  })
  @ApiResponse({ status: 200, description: 'Networks retrieved successfully' })
  async getNetworks(@Res() res: Response) {
    try {
      this.logger.log('Getting supported blockchain networks');
      
      const result = await this.blockchainQueryService.getNetworks();
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Networks retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('network/:networkId/status')
  @ApiOperation({
    summary: 'Get network status',
    description: 'Get the current status and health of a specific blockchain network'
  })
  @ApiParam({ name: 'networkId', description: 'Network ID (ethereum, polygon, etc.)' })
  @ApiResponse({ status: 200, description: 'Network status retrieved successfully' })
  async getNetworkStatus(@Param('networkId') networkId: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting network status for: ${networkId}`);
      
      const result = await this.blockchainQueryService.getNetworkStatus(networkId);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Network status retrieval failed for ${networkId}: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('contracts')
  @ApiOperation({
    summary: 'Get deployed smart contracts',
    description: 'Retrieve list of deployed smart contracts and their details'
  })
  @ApiQuery({ name: 'network', required: false, description: 'Filter by network' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by contract type' })
  @ApiResponse({ status: 200, description: 'Contracts retrieved successfully' })
  async getContracts(
    @Query('network') network: string,
    @Query('type') type: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log('Getting deployed smart contracts');
      
      const filters = { network, type };
      const result = await this.blockchainQueryService.getContracts(filters);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Contracts retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('gas-price/:networkId')
  @ApiOperation({
    summary: 'Get current gas price',
    description: 'Get the current gas price for a specific network'
  })
  @ApiParam({ name: 'networkId', description: 'Network ID' })
  @ApiResponse({ status: 200, description: 'Gas price retrieved successfully' })
  async getGasPrice(@Param('networkId') networkId: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting gas price for network: ${networkId}`);
      
      const result = await this.blockchainQueryService.getGasPrice(networkId);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Gas price retrieval failed for ${networkId}: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('balance/:address')
  @ApiOperation({
    summary: 'Get wallet balance',
    description: 'Get the balance of a specific wallet address'
  })
  @ApiParam({ name: 'address', description: 'Wallet address' })
  @ApiQuery({ name: 'network', required: false, description: 'Network to check (default: ethereum)' })
  @ApiResponse({ status: 200, description: 'Balance retrieved successfully' })
  async getBalance(
    @Param('address') address: string,
    @Query('network', new DefaultValuePipe('ethereum')) network: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting balance for address: ${address} on network: ${network}`);
      
      const result = await this.blockchainQueryService.getBalance(address, network);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Balance retrieval failed for ${address}: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
