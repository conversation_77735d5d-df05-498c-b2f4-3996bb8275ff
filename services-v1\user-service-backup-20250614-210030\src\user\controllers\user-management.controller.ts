import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  <PERSON>ers,
  Res,
  HttpStatus,
  Logger,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { UserManagementService, CreateUserRequest, UpdateUserRequest } from '../services/user-management.service';
import { UserProfileService, ProfileStatsUpdate } from '../services/user-profile.service';

@ApiTags('User Management')
@Controller('users')
export class UserManagementController {
  private readonly logger = new Logger(UserManagementController.name);

  constructor(
    private readonly userManagement: UserManagementService,
    private readonly userProfile: UserProfileService
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new user account' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 409, description: 'User already exists' })
  async createUser(
    @Body() createUserRequest: CreateUserRequest,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 User creation request received`, { correlationId, email: createUserRequest.email });

      const user = await this.userManagement.createUser(createUserRequest);

      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: user,
        message: 'User created successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ User creation failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('already exists') ? HttpStatus.CONFLICT : 
                        error.message.includes('Validation failed') ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get(':userId')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(
    @Param('userId') userId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 Get user request received`, { correlationId, userId });

      const user = await this.userManagement.getUserById(userId);
      
      if (!user) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          error: 'User not found',
          correlationId
        });
      }

      return res.status(HttpStatus.OK).json({
        success: true,
        data: user,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get user failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('email/:email')
  @ApiOperation({ summary: 'Get user by email' })
  @ApiResponse({ status: 200, description: 'User retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserByEmail(
    @Param('email') email: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 Get user by email request received`, { correlationId, email });

      const user = await this.userManagement.getUserByEmail(email);
      
      if (!user) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          error: 'User not found',
          correlationId
        });
      }

      return res.status(HttpStatus.OK).json({
        success: true,
        data: user,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get user by email failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('username/:username')
  @ApiOperation({ summary: 'Get user by username' })
  @ApiResponse({ status: 200, description: 'User retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserByUsername(
    @Param('username') username: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 Get user by username request received`, { correlationId, username });

      const user = await this.userManagement.getUserByUsername(username);
      
      if (!user) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          error: 'User not found',
          correlationId
        });
      }

      return res.status(HttpStatus.OK).json({
        success: true,
        data: user,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get user by username failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Put(':userId')
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async updateUser(
    @Param('userId') userId: string,
    @Body() updateRequest: UpdateUserRequest,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 Update user request received`, { correlationId, userId });

      const user = await this.userManagement.updateUser(userId, updateRequest);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: user,
        message: 'User updated successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Update user failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        error.message.includes('Validation failed') ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Put(':userId/password')
  @ApiOperation({ summary: 'Change user password' })
  @ApiResponse({ status: 200, description: 'Password changed successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Invalid current password' })
  async changePassword(
    @Param('userId') userId: string,
    @Body() passwordData: { currentPassword: string; newPassword: string },
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🔒 Change password request received`, { correlationId, userId });

      await this.userManagement.changePassword(
        userId,
        passwordData.currentPassword,
        passwordData.newPassword
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Password changed successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Change password failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        error.message.includes('incorrect') ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Put(':userId/stats')
  @ApiOperation({ summary: 'Update user stats (internal use)' })
  @ApiResponse({ status: 200, description: 'Stats updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUserStats(
    @Param('userId') userId: string,
    @Body() statsUpdate: ProfileStatsUpdate,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Update user stats request received`, { correlationId, userId });

      await this.userProfile.updateUserStats(userId, statsUpdate);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'User stats updated successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Update user stats failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all users (admin only)' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  async getAllUsers(
    @Query('limit') limit: string,
    @Query('offset') offset: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👥 Get all users request received`, { correlationId });

      const result = await this.userManagement.getAllUsers(
        parseInt(limit) || 100,
        parseInt(offset) || 0
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        data: result,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get all users failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('search/:query')
  @ApiOperation({ summary: 'Search users' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  async searchUsers(
    @Param('query') query: string,
    @Query('limit') limit: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🔍 Search users request received`, { correlationId, query });

      const users = await this.userManagement.searchUsers(query, parseInt(limit) || 20);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          query,
          users,
          count: users.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Search users failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Delete(':userId')
  @ApiOperation({ summary: 'Delete user account (admin only)' })
  @ApiResponse({ status: 200, description: 'User deleted successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async deleteUser(
    @Param('userId') userId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🗑️ Delete user request received`, { correlationId, userId });

      await this.userManagement.deleteUser(userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'User deleted successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Delete user failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for user service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          service: 'user-service',
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        },
        correlationId
      });
    } catch (error) {
      return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: 'Service unavailable',
        correlationId: headers['x-correlation-id']
      });
    }
  }
}
