import { Controller, All, Req, Res, Get } from '@nestjs/common';
import { Request, Response } from 'express';
import { 
  ResponseService, 
  RequirePermissions, 
  Permission, 
  Public 
} from '../../../../../shared';
import { ProxyV2Service, ProxyRequest } from './proxy-v2.service';

/**
 * Enhanced Proxy Controller V2
 * 
 * Handles all incoming requests and routes them to appropriate services
 * using enterprise-grade proxy features.
 */
@Controller()
export class ProxyV2Controller {
  constructor(
    private readonly proxyService: ProxyV2Service,
    private readonly responseService: ResponseService
  ) {}

  /**
   * Proxy all user service requests
   */
  @All('users/*')
  @Public() // Authentication handled by target service
  async proxyToUserService(@Req() req: Request, @Res() res: Response) {
    const proxyRequest = this.buildProxyRequest(req);
    const response = await this.proxyService.proxyToUserService(proxyRequest);
    return this.sendProxyResponse(res, response);
  }

  /**
   * Proxy all profile analysis service requests
   */
  @All('profile/*')
  @Public()
  async proxyToProfileService(@Req() req: Request, @Res() res: Response) {
    const proxyRequest = this.buildProxyRequest(req);
    const response = await this.proxyService.proxyToProfileService(proxyRequest);
    return this.sendProxyResponse(res, response);
  }

  /**
   * Proxy all NFT generation service requests
   */
  @All('nft/*')
  @Public()
  async proxyToNFTService(@Req() req: Request, @Res() res: Response) {
    const proxyRequest = this.buildProxyRequest(req);
    const response = await this.proxyService.proxyToNFTService(proxyRequest);
    return this.sendProxyResponse(res, response);
  }

  /**
   * Proxy all blockchain service requests
   */
  @All('blockchain/*')
  @Public()
  async proxyToBlockchainService(@Req() req: Request, @Res() res: Response) {
    const proxyRequest = this.buildProxyRequest(req);
    const response = await this.proxyService.proxyToBlockchainService(proxyRequest);
    return this.sendProxyResponse(res, response);
  }

  /**
   * Proxy all marketplace service requests
   */
  @All('marketplace/*')
  @Public()
  async proxyToMarketplaceService(@Req() req: Request, @Res() res: Response) {
    const proxyRequest = this.buildProxyRequest(req);
    const response = await this.proxyService.proxyToMarketplaceService(proxyRequest);
    return this.sendProxyResponse(res, response);
  }

  /**
   * Get proxy statistics
   */
  @Get('proxy/stats')
  @RequirePermissions(Permission.ADMIN)
  async getProxyStats() {
    const stats = await this.proxyService.getProxyStats();
    return this.responseService.success(stats, 'Proxy statistics retrieved');
  }

  /**
   * Health check for the proxy system
   */
  @Get('proxy/health')
  @Public()
  async proxyHealth() {
    const stats = await this.proxyService.getProxyStats();
    
    const healthStatus = {
      status: 'healthy',
      proxy: {
        totalServices: stats.totalServices,
        healthyServices: stats.healthyServices,
        serviceHealthRate: stats.totalServices > 0 ? 
          ((stats.healthyServices / stats.totalServices) * 100).toFixed(2) + '%' : '0%',
      },
      instances: {
        total: stats.totalInstances,
        healthy: stats.healthyInstances,
        healthRate: stats.totalInstances > 0 ? 
          ((stats.healthyInstances / stats.totalInstances) * 100).toFixed(2) + '%' : '0%',
      },
      circuitBreakers: stats.circuitBreakers,
      timestamp: new Date().toISOString(),
    };

    return this.responseService.success(healthStatus, 'Proxy system is healthy');
  }

  /**
   * Build proxy request from Express request
   */
  private buildProxyRequest(req: Request): ProxyRequest {
    // Extract the service path (remove the service prefix)
    let url = req.url;
    const pathSegments = url.split('/').filter(Boolean);
    
    if (pathSegments.length > 0) {
      // Remove the service name from the path and reconstruct
      pathSegments.shift(); // Remove service name (users, profile, etc.)
      url = '/' + pathSegments.join('/');
      
      // Preserve query string
      if (req.url.includes('?')) {
        const queryString = req.url.split('?')[1];
        url += '?' + queryString;
      }
    }

    return {
      method: req.method,
      url,
      headers: req.headers as Record<string, string>,
      body: req.body,
      query: req.query as Record<string, string>,
      correlationId: req.headers['x-correlation-id'] as string || 
                    req.headers['correlation-id'] as string,
    };
  }

  /**
   * Send proxy response back to client
   */
  private sendProxyResponse(res: Response, proxyResponse: any): void {
    // Set correlation ID header
    res.setHeader('x-correlation-id', proxyResponse.correlationId);
    res.setHeader('x-proxy-service', proxyResponse.service);
    res.setHeader('x-proxy-duration', proxyResponse.duration.toString());
    
    // Set other headers from the response
    if (proxyResponse.headers) {
      Object.entries(proxyResponse.headers).forEach(([key, value]) => {
        // Skip headers that shouldn't be forwarded
        if (!['content-length', 'transfer-encoding', 'connection'].includes(key.toLowerCase())) {
          res.setHeader(key, value as string);
        }
      });
    }

    res.status(proxyResponse.status).json(proxyResponse.data);
  }
}
