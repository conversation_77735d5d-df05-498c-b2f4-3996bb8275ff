// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Command Model (Write operations)
model UserCommand {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?
  lastName  String?
  avatar    String?
  bio       String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Twitter integration
  twitterId       String?  @unique
  twitterUsername String?
  twitterData     Json?
  provider        String?
  displayName     String?
  profileImage    String?
  providerData    Json?

  // Authentication fields
  lastLoginAt           DateTime?
  failedLoginAttempts   Int       @default(0)
  lockedUntil          DateTime?
  passwordChangedAt    DateTime?
  isEmailVerified      Boolean   @default(false)
  isProfileComplete    Boolean   @default(false)
  lastSeenAt           DateTime?
  location             String?
  website              String?
  interests            String[]

  // User roles and permissions
  roles       String[] @default(["user"])
  permissions String[] @default([])
  role        String   @default("user")

  // Profile and analytics
  profileData Json?
  analytics   Json?

  // Audit fields
  createdBy String?
  updatedBy String?
  version   Int     @default(1)

  // Online status
  isOnline Boolean @default(false)

  @@map("user_commands")
}

// User Query Model (Read operations)
model UserQuery {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  firstName String?
  lastName  String?
  avatar    String?
  bio       String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Twitter integration
  twitterId         String?  @unique
  twitterUsername   String?
  twitterData       Json?
  displayName       String?
  profileImage      String?
  twitterFollowers  Int?
  twitterVerified   Boolean  @default(false)
  location          String?
  lastActivityAt    DateTime?

  // Authentication fields
  isEmailVerified      Boolean   @default(false)
  isProfileComplete    Boolean   @default(false)

  // User roles and permissions
  roles       String[] @default(["user"])
  permissions String[] @default([])
  role        String   @default("user")

  // Profile and analytics
  profileData Json?
  analytics   Json?

  // Audit fields
  lastUpdated DateTime?

  // Additional user fields
  totalNfts Int @default(0)
  website   String?

  @@map("user_queries")
}

// Legacy User Model (for compatibility)
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName   String?
  lastName    String?
  displayName String?
  avatar      String?
  bio         String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Twitter integration
  twitterId       String?  @unique
  twitterUsername String?
  twitterData     Json?

  // User roles and permissions
  roles       String[] @default(["user"])
  permissions String[] @default([])

  // Profile and analytics
  profileData Json?
  analytics   Json?

  // Relations
  participations     CampaignParticipation[]
  submissions        RequirementSubmission[]
  rewardsEarned      CampaignRewardEarned[]
  nfts               NFT[]
  marketplaceListings MarketplaceListing[]
  buyerTransactions  MarketplaceTransaction[] @relation("BuyerTransactions")
  sellerTransactions MarketplaceTransaction[] @relation("SellerTransactions")

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("sessions")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  resource  String
  details   Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  @@map("audit_logs")
}

// Campaign Models
model Campaign {
  id          String   @id @default(cuid())
  title       String
  name        String
  description String?
  status      String   @default("active")
  startDate   DateTime
  endDate     DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Additional fields for business logic
  type            String?
  tags            String[]
  featured        Boolean  @default(false)
  priority        Int      @default(0)
  bannerImage     String?
  maxParticipants Int?
  minAge          Int?
  geoRestrictions String[]
  metadata        Json?
  createdBy       String?

  // Relations
  participations CampaignParticipation[]
  requirements   CampaignRequirement[]
  rewards        CampaignReward[]
  nfts           NFT[]

  @@map("campaigns")
}

model CampaignParticipation {
  id         String   @id @default(cuid())
  campaignId String
  userId     String
  status     String   @default("active")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Additional fields for business logic
  joinedAt      DateTime @default(now())
  referralCode  String?
  acceptedTerms Boolean  @default(false)
  totalPoints   Int      @default(0)
  completedAt   DateTime?

  // Relations
  user         User                    @relation(fields: [userId], references: [id])
  campaign     Campaign                @relation(fields: [campaignId], references: [id])
  submissions  RequirementSubmission[]
  rewardsEarned CampaignRewardEarned[]

  // Unique constraint for user-campaign combination
  @@unique([userId, campaignId], name: "userId_campaignId")
  @@map("campaign_participations")
}

model CampaignRequirement {
  id         String   @id @default(cuid())
  campaignId String
  type       String
  target     String?
  minValue   Float?
  maxValue   Float?
  mandatory  Boolean  @default(true)
  points     Int      @default(0)
  details    Json?
  createdAt  DateTime @default(now())

  // Relations
  campaign Campaign @relation(fields: [campaignId], references: [id])

  @@map("campaign_requirements")
}

model CampaignReward {
  id         String   @id @default(cuid())
  campaignId String
  type       String
  name       String
  description String?
  value      String
  rarity     String?
  maxQuantity Int?
  minPointsRequired Int @default(0)
  metadata   Json?
  details    Json?
  createdAt  DateTime @default(now())

  // Relations
  campaign Campaign @relation(fields: [campaignId], references: [id])
  rewardsEarned CampaignRewardEarned[]

  @@map("campaign_rewards")
}

model CampaignRewardEarned {
  id              String   @id @default(cuid())
  userId          String
  rewardId        String
  participationId String?
  status          String   @default("earned")
  earnedAt        DateTime @default(now())
  claimedAt       DateTime?
  claimed         Boolean  @default(false)
  metadata        Json?

  // Relations
  user          User                   @relation(fields: [userId], references: [id])
  reward        CampaignReward         @relation(fields: [rewardId], references: [id])
  participation CampaignParticipation? @relation(fields: [participationId], references: [id])

  @@map("campaign_rewards_earned")
}

model RequirementSubmission {
  id              String   @id @default(cuid())
  userId          String
  requirementId   String
  participationId String?
  requirementType String?
  submissionValue String?
  proofUrls        String[]
  notes            String?
  metadata         Json?
  status           String   @default("submitted")
  data             Json?
  submittedAt      DateTime @default(now())
  pointsAwarded    Int?
  reviewedAt       DateTime?
  reviewerComments String?
  feedback         String?

  // Relations
  user         User                   @relation(fields: [userId], references: [id])
  participation CampaignParticipation? @relation(fields: [participationId], references: [id])

  @@map("requirement_submissions")
}

// NFT Models
model NFT {
  id          String   @id @default(cuid())
  tokenId     String   @unique
  name        String
  description String?
  imageUrl    String?
  metadata    Json?
  ownerId     String
  campaignId  String?
  status      String   @default("active")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Additional fields for business logic
  rarity           String?
  blockchain       String   @default("ethereum")
  contractAddress  String?
  engagementScore  Float?
  userId           String?
  externalNftId    String?
  transactionHash  String?
  generationParams Json?
  updatedBy        String?
  ownerAddress     String?
  createdBy        String?
  mintedAt         DateTime?

  // Relations
  user     User      @relation(fields: [ownerId], references: [id])
  campaign Campaign? @relation(fields: [campaignId], references: [id])
  marketplaceListings MarketplaceListing[]

  @@map("nfts")
}

// Marketplace Models
model MarketplaceListing {
  id        String   @id @default(cuid())
  nftId     String
  sellerId  String
  price     String
  currency  String   @default("ETH")
  status    String   @default("active")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Additional fields for business logic
  title             String?
  description       String?
  viewCount         Int      @default(0)
  listingType       String   @default("fixed")
  tags              String[]
  startingPrice     String?
  externalListingId String?
  updatedBy         String?
  reservePrice      String?
  buyNowPrice       String?
  acceptOffers      Boolean  @default(true)
  minOfferAmount    String?
  metadata          Json?
  expiresAt         DateTime?
  createdBy         String?

  // Relations
  nft          NFT                        @relation(fields: [nftId], references: [id])
  seller       User                       @relation(fields: [sellerId], references: [id])
  transactions MarketplaceTransaction[]

  @@map("marketplace_listings")
}

model MarketplaceTransaction {
  id          String   @id @default(cuid())
  listingId   String
  buyerId     String
  sellerId    String
  price       String
  currency    String   @default("ETH")
  status      String   @default("completed")
  completedAt DateTime @default(now())

  // Additional fields for business logic
  type        String   @default("sale")
  createdAt   DateTime @default(now())

  // Relations
  listing MarketplaceListing @relation(fields: [listingId], references: [id])
  buyer   User               @relation("BuyerTransactions", fields: [buyerId], references: [id])
  seller  User               @relation("SellerTransactions", fields: [sellerId], references: [id])

  @@map("marketplace_transactions")
}

// Communication Models
model Notification {
  id        String   @id @default(cuid())
  userId    String
  type      String
  title     String
  message   String
  data      Json?
  read      Boolean  @default(false)
  isRead    Boolean  @default(false)
  priority  String   @default("normal")
  icon      String?
  actionUrl String?
  actionText String?
  expiresAt DateTime?
  readAt    DateTime?
  createdAt DateTime @default(now())

  @@map("notifications")
}

model ActivityFeed {
  id          String   @id @default(cuid())
  userId      String
  type        String
  title       String
  description String?
  data        Json?
  entityType  String?
  entityId    String?
  metadata    Json?
  createdAt   DateTime @default(now())

  @@map("activity_feeds")
}

model ChatMessage {
  id           String    @id @default(cuid())
  senderId     String
  content      String
  type         String    @default("text")
  data         Json?
  createdAt    DateTime  @default(now())

  // Additional fields for business logic
  roomId       String?
  messageType  String?
  attachments  Json?
  editedAt     DateTime?
  isEdited     Boolean   @default(false)
  replyToId    String?
  metadata     Json?

  @@map("chat_messages")
}

// Analytics Models
model SearchAnalytics {
  id            String   @id @default(cuid())
  userId        String?
  query         String
  type          String
  results       Int      @default(0)
  resultCount   Int      @default(0)
  executionTime Float?
  filters       String?
  sortCriteria  String?
  timestamp     DateTime @default(now())
  createdAt     DateTime @default(now())

  @@map("search_analytics")
}
