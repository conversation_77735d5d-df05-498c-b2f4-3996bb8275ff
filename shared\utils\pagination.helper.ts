/**
 * Pagination Helper Utility
 */

import { PaginationMeta } from '../responses/interfaces/base-response.interface';

export class PaginationHelper {
  /**
   * Calculate pagination metadata
   */
  static calculateMeta(page: number, limit: number, total: number): PaginationMeta {
    const totalPages = Math.ceil(total / limit);
    
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Calculate skip value for database queries
   */
  static calculateSkip(page: number, limit: number): number {
    return (page - 1) * limit;
  }

  /**
   * Validate pagination parameters
   */
  static validateParams(page?: number, limit?: number): { page: number; limit: number } {
    const validatedPage = Math.max(1, page || 1);
    const validatedLimit = Math.min(Math.max(1, limit || 20), 100);
    
    return {
      page: validatedPage,
      limit: validatedLimit,
    };
  }
}
