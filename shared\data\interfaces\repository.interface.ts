/**
 * Base Repository Interfaces
 * Defines the standard repository pattern for all services
 */

/**
 * Base repository interface for CRUD operations
 */
export interface IBaseRepository<T, CreateDto, UpdateDto> {
  // Basic CRUD operations
  findById(id: string, options?: FindOptions): Promise<T | null>;
  findMany(criteria?: FindCriteria<T>, options?: FindOptions): Promise<T[]>;
  findOne(criteria: FindCriteria<T>, options?: FindOptions): Promise<T | null>;
  create(data: CreateDto, options?: CreateOptions): Promise<T>;
  update(id: string, data: UpdateDto, options?: UpdateOptions): Promise<T>;
  delete(id: string, options?: DeleteOptions): Promise<boolean>;
  
  // Bulk operations
  createMany(data: CreateDto[], options?: CreateManyOptions): Promise<T[]>;
  updateMany(criteria: FindCriteria<T>, data: Partial<UpdateDto>, options?: UpdateManyOptions): Promise<number>;
  deleteMany(criteria: FindCriteria<T>, options?: DeleteManyOptions): Promise<number>;
  
  // Advanced queries
  count(criteria?: FindCriteria<T>): Promise<number>;
  exists(criteria: FindCriteria<T>): Promise<boolean>;
  paginate(criteria?: FindCriteria<T>, pagination?: PaginationOptions): Promise<PaginatedResult<T>>;
  
  // Transaction support
  withTransaction<R>(fn: (repository: this) => Promise<R>): Promise<R>;
  
  // Health and monitoring
  healthCheck(): Promise<HealthCheckResult>;
}

/**
 * CQRS repository interface
 */
export interface ICQRSRepository<TCommand, TQuery, CreateDto, UpdateDto> {
  // Command side (write operations)
  command: ICommandRepository<TCommand, CreateDto, UpdateDto>;
  
  // Query side (read operations)
  query: IQueryRepository<TQuery>;
  
  // Event sourcing
  getEvents(aggregateId: string): Promise<DomainEvent[]>;
  saveEvents(aggregateId: string, events: DomainEvent[]): Promise<void>;
  
  // Projection management
  rebuildProjection(aggregateId: string): Promise<void>;
  rebuildAllProjections(): Promise<void>;
}

/**
 * Command repository interface (write side)
 */
export interface ICommandRepository<T, CreateDto, UpdateDto> extends IBaseRepository<T, CreateDto, UpdateDto> {
  // Command-specific operations
  executeCommand(command: Command): Promise<CommandResult>;
  validateCommand(command: Command): Promise<ValidationResult>;
  
  // Event sourcing
  appendEvent(aggregateId: string, event: DomainEvent): Promise<void>;
  getVersion(aggregateId: string): Promise<number>;
}

/**
 * Query repository interface (read side)
 */
export interface IQueryRepository<T> {
  // Read operations only
  findById(id: string, options?: FindOptions): Promise<T | null>;
  findMany(criteria?: FindCriteria<T>, options?: FindOptions): Promise<T[]>;
  findOne(criteria: FindCriteria<T>, options?: FindOptions): Promise<T | null>;
  count(criteria?: FindCriteria<T>): Promise<number>;
  exists(criteria: FindCriteria<T>): Promise<boolean>;
  paginate(criteria?: FindCriteria<T>, pagination?: PaginationOptions): Promise<PaginatedResult<T>>;
  
  // Advanced queries
  search(query: SearchQuery): Promise<SearchResult<T>>;
  aggregate(pipeline: AggregationPipeline): Promise<AggregationResult>;
  
  // Caching
  findCached(key: string, fallback: () => Promise<T>): Promise<T>;
  invalidateCache(pattern: string): Promise<void>;
}

/**
 * Find options interface
 */
export interface FindOptions {
  include?: Record<string, boolean | FindOptions>;
  select?: Record<string, boolean>;
  orderBy?: Record<string, SortDirection> | Array<Record<string, SortDirection>>;
  cache?: CacheOptions;
  timeout?: number;
  distinct?: string[];
}

/**
 * Find criteria interface
 */
export interface FindCriteria<T> {
  where?: WhereClause<T>;
  include?: Record<string, boolean | FindOptions>;
  select?: Record<string, boolean>;
  orderBy?: Record<string, SortDirection> | Array<Record<string, SortDirection>>;
  skip?: number;
  take?: number;
  cursor?: any;
}

/**
 * Where clause interface
 */
export interface WhereClause<T> extends Record<string, any> {
  AND?: WhereClause<T>[];
  OR?: WhereClause<T>[];
  NOT?: WhereClause<T>;
  
  // String operations
  contains?: string;
  startsWith?: string;
  endsWith?: string;
  mode?: 'default' | 'insensitive';
  
  // Numeric operations
  gt?: number;
  gte?: number;
  lt?: number;
  lte?: number;
  
  // Array operations
  in?: any[];
  notIn?: any[];
  
  // Date operations
  equals?: Date;
  before?: Date;
  after?: Date;
  between?: [Date, Date];
  
  // Null operations
  isNull?: boolean;
  isNotNull?: boolean;
}

/**
 * Sort direction enumeration
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Create options interface
 */
export interface CreateOptions {
  include?: Record<string, boolean | FindOptions>;
  select?: Record<string, boolean>;
  skipDuplicates?: boolean;
  transaction?: any;
  timeout?: number;
}

/**
 * Update options interface
 */
export interface UpdateOptions {
  include?: Record<string, boolean | FindOptions>;
  select?: Record<string, boolean>;
  where?: any;
  transaction?: any;
  timeout?: number;
}

/**
 * Delete options interface
 */
export interface DeleteOptions {
  where?: any;
  transaction?: any;
  timeout?: number;
}

/**
 * Create many options interface
 */
export interface CreateManyOptions {
  skipDuplicates?: boolean;
  transaction?: any;
  timeout?: number;
}

/**
 * Update many options interface
 */
export interface UpdateManyOptions {
  transaction?: any;
  timeout?: number;
}

/**
 * Delete many options interface
 */
export interface DeleteManyOptions {
  transaction?: any;
  timeout?: number;
}

/**
 * Pagination options interface
 */
export interface PaginationOptions {
  page?: number;
  limit?: number;
  cursor?: string;
  offset?: number;
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  data: T[];
  pagination: PaginationMeta;
  filters?: any;
  sorting?: any;
}

/**
 * Pagination metadata interface
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  nextPage?: number;
  prevPage?: number;
  nextCursor?: string;
  prevCursor?: string;
  offset?: number;
}

/**
 * Cache options interface
 */
export interface CacheOptions {
  key?: string;
  ttl?: number;
  tags?: string[];
  invalidateOn?: string[];
  namespace?: string;
}

/**
 * Health check result interface
 */
export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  details: Record<string, any>;
  timestamp: Date;
}

/**
 * Search query interface
 */
export interface SearchQuery {
  query: string;
  fields?: string[];
  filters?: Record<string, any>;
  sort?: Record<string, SortDirection>;
  pagination?: PaginationOptions;
  fuzzy?: boolean;
  highlight?: boolean;
}

/**
 * Search result interface
 */
export interface SearchResult<T> {
  data: T[];
  total: number;
  took: number;
  highlights?: Record<string, string[]>;
  facets?: Record<string, FacetResult>;
}

/**
 * Facet result interface
 */
export interface FacetResult {
  buckets: Array<{
    key: string;
    count: number;
  }>;
}

/**
 * Aggregation pipeline interface
 */
export interface AggregationPipeline {
  stages: AggregationStage[];
}

/**
 * Aggregation stage interface
 */
export interface AggregationStage {
  type: 'match' | 'group' | 'sort' | 'limit' | 'skip' | 'project' | 'unwind';
  params: Record<string, any>;
}

/**
 * Aggregation result interface
 */
export interface AggregationResult {
  data: any[];
  metadata: {
    totalStages: number;
    executionTime: number;
    documentsExamined: number;
    documentsReturned: number;
  };
}

/**
 * Domain event interface
 */
export interface DomainEvent {
  id: string;
  aggregateId: string;
  eventType: string;
  eventVersion: string;
  eventData: Record<string, any>;
  metadata: EventMetadata;
  timestamp: Date;
}

/**
 * Event metadata interface
 */
export interface EventMetadata {
  correlationId?: string;
  causationId?: string;
  userId?: string;
  source: string;
  version: string;
}

/**
 * Command interface
 */
export interface Command {
  id: string;
  type: string;
  aggregateId: string;
  payload: Record<string, any>;
  metadata: CommandMetadata;
  timestamp: Date;
}

/**
 * Command metadata interface
 */
export interface CommandMetadata {
  correlationId?: string;
  userId?: string;
  source: string;
  version: string;
  expectedVersion?: number;
}

/**
 * Command result interface
 */
export interface CommandResult {
  success: boolean;
  aggregateId: string;
  version: number;
  events: DomainEvent[];
  error?: string;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}
