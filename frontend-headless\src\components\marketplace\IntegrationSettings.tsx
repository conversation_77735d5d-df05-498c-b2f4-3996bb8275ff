'use client'

import React, { useState } from 'react'
import {
  Cog6ToothIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import {
  useCampaignIntegrationSettings,
  useUpdateCampaignIntegrationSettings,
  useToggleAutoListing
} from '@/hooks/useMarketplaceIntegration'
import { MarketplaceCategory } from '@/types/marketplace-integration.types'

interface IntegrationSettingsProps {
  campaignId?: string
  className?: string
}

export default function IntegrationSettings({
  campaignId,
  className = ''
}: IntegrationSettingsProps) {
  const { data: settings, isLoading } = useCampaignIntegrationSettings(campaignId || '')
  const updateSettingsMutation = useUpdateCampaignIntegrationSettings()
  const toggleAutoListingMutation = useToggleAutoListing()

  const [localSettings, setLocalSettings] = useState(settings)

  React.useEffect(() => {
    if (settings) {
      setLocalSettings(settings)
    }
  }, [settings])

  const handleSaveSettings = async () => {
    if (!campaignId || !localSettings) return

    try {
      await updateSettingsMutation.mutateAsync({
        campaignId,
        settings: localSettings
      })
    } catch (error) {
      console.error('Failed to update settings:', error)
    }
  }

  const handleToggleAutoListing = async () => {
    if (!campaignId || !settings) return

    try {
      await toggleAutoListingMutation.mutateAsync({
        campaignId,
        enable: !settings.autoListOnCompletion
      })
    } catch (error) {
      console.error('Failed to toggle auto listing:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!campaignId) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <Cog6ToothIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No campaign selected</h3>
        <p className="mt-1 text-sm text-gray-500">
          Select a campaign to configure integration settings.
        </p>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h2 className="text-lg font-medium text-gray-900">Integration Settings</h2>
        <p className="text-sm text-gray-600">Configure marketplace integration for your campaign</p>
      </div>

      {localSettings && (
        <div className="space-y-6">
          {/* Auto Listing */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Auto Listing</h3>
                <p className="text-sm text-gray-600">Automatically list campaign on marketplace when completed</p>
              </div>
              <button
                onClick={handleToggleAutoListing}
                disabled={toggleAutoListingMutation.isPending}
                className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 ${
                  localSettings.autoListOnCompletion ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                    localSettings.autoListOnCompletion ? 'translate-x-5' : 'translate-x-0'
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Default Settings */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Default Listing Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Category
                </label>
                <select
                  value={localSettings.defaultCategory}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    defaultCategory: e.target.value as MarketplaceCategory
                  } : prev)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {Object.values(MarketplaceCategory).map((category) => (
                    <option key={category} value={category}>
                      {category.replace('_', ' ').toUpperCase()}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Royalty (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={localSettings.defaultRoyalty}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    defaultRoyalty: parseFloat(e.target.value)
                  } : prev)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Base Price
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={localSettings.basePrice}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    basePrice: parseFloat(e.target.value)
                  } : prev)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pricing Strategy
                </label>
                <select
                  value={localSettings.pricingStrategy}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    pricingStrategy: e.target.value as any
                  } : prev)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="fixed">Fixed Price</option>
                  <option value="auction">Auction</option>
                  <option value="dynamic">Dynamic Pricing</option>
                </select>
              </div>
            </div>
          </div>

          {/* Visibility Settings */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Visibility Settings</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">Public Listing</label>
                  <p className="text-sm text-gray-500">Make listing visible to all users</p>
                </div>
                <input
                  type="checkbox"
                  checked={localSettings.isPublicListing}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    isPublicListing: e.target.checked
                  } : prev)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">Allow Early Access</label>
                  <p className="text-sm text-gray-500">Allow participants early access before public listing</p>
                </div>
                <input
                  type="checkbox"
                  checked={localSettings.allowEarlyAccess}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    allowEarlyAccess: e.target.checked
                  } : prev)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Quality Controls */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quality Controls</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quality Threshold (1-10)
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  step="0.1"
                  value={localSettings.qualityThreshold}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    qualityThreshold: parseFloat(e.target.value)
                  } : prev)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Only NFTs above this quality score will be listed
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">Require Manual Approval</label>
                  <p className="text-sm text-gray-500">Manually review all listings before publishing</p>
                </div>
                <input
                  type="checkbox"
                  checked={localSettings.requireManualApproval}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    requireManualApproval: e.target.checked
                  } : prev)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Notifications */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Notifications</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">Notify on Listing</label>
                  <p className="text-sm text-gray-500">Get notified when items are listed</p>
                </div>
                <input
                  type="checkbox"
                  checked={localSettings.notifyOnListing}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    notifyOnListing: e.target.checked
                  } : prev)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">Notify on Sale</label>
                  <p className="text-sm text-gray-500">Get notified when items are sold</p>
                </div>
                <input
                  type="checkbox"
                  checked={localSettings.notifyOnSale}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    notifyOnSale: e.target.checked
                  } : prev)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">Notify on Milestone</label>
                  <p className="text-sm text-gray-500">Get notified when milestones are reached</p>
                </div>
                <input
                  type="checkbox"
                  checked={localSettings.notifyOnMilestone}
                  onChange={(e) => setLocalSettings(prev => prev ? {
                    ...prev,
                    notifyOnMilestone: e.target.checked
                  } : prev)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={handleSaveSettings}
              disabled={updateSettingsMutation.isPending}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              {updateSettingsMutation.isPending ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckIcon className="h-4 w-4 mr-2" />
                  Save Settings
                </>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
