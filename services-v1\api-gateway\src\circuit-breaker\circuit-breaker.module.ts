import { Module } from '@nestjs/common';
import { CircuitBreakerService } from './circuit-breaker.service';
import { CircuitBreakerController } from './circuit-breaker.controller';

/**
 * Circuit Breaker Module
 * 
 * Provides circuit breaker pattern implementation for fault tolerance.
 * Prevents cascading failures with bulkhead isolation.
 */
@Module({
  providers: [CircuitBreakerService],
  controllers: [CircuitBreakerController],
  exports: [CircuitBreakerService],
})
export class CircuitBreakerModule {}
