/**
 * User Service V2 - App Module
 * 
 * Implements shared infrastructure following SHARED-INFRASTRUCTURE-IMPLEMENTATION-PLAN.md
 * Step 1.3: Update App Module with setupBusinessService
 */

import { Module } from '@nestjs/common';
import { setupBusinessService } from '../../../shared';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    // Shared Infrastructure Setup (as per implementation plan)
    setupBusinessService('user-service-v2', '2.0.0'),
    
    // Business Logic Modules
    UsersModule,
    AuthModule,
    HealthModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor() {
    console.log('🏗️ User Service V2 initialized with shared infrastructure');
    console.log('🗄️ Database: user_service_v2 (Database Per Service Pattern)');
    console.log('🔧 Features: Auth, Logging, Responses, Config, Database, Error Handling');
  }
}
