import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';

// Controllers
import { MarketplaceQueryController } from './controllers/marketplace-query.controller';
import { MarketplaceCommandController } from './controllers/marketplace-command.controller';
import { ListingController } from './controllers/listing.controller';
import { AuctionController } from './controllers/auction.controller';
import { OfferController } from './controllers/offer.controller';
import { TransactionController } from './controllers/transaction.controller';

// Services
import { MarketplaceQueryService } from './services/marketplace-query.service';
import { MarketplaceCommandService } from './services/marketplace-command.service';
import { ListingManagementService } from './services/listing-management.service';
import { AuctionManagementService } from './services/auction-management.service';
import { OfferManagementService } from './services/offer-management.service';
import { TransactionProcessingService } from './services/transaction-processing.service';
import { MarketplaceAnalyticsService } from './services/marketplace-analytics.service';
import { TradingService } from './services/trading.service';
import { PaymentService } from './services/payment.service';

@Module({
  imports: [
    HttpModule, // For external API calls
  ],
  controllers: [
    // Core Marketplace Controllers
    MarketplaceQueryController,
    MarketplaceCommandController,
    
    // Feature Controllers
    ListingController,
    AuctionController,
    OfferController,
    TransactionController,
  ],
  providers: [
    // Core Marketplace Services
    MarketplaceQueryService,
    MarketplaceCommandService,
    
    // Feature Services
    ListingManagementService,
    AuctionManagementService,
    OfferManagementService,
    TransactionProcessingService,
    MarketplaceAnalyticsService,
    TradingService,
    PaymentService,
  ],
  exports: [
    // Export services for use in other modules
    MarketplaceQueryService,
    MarketplaceCommandService,
    ListingManagementService,
    AuctionManagementService,
    OfferManagementService,
    TransactionProcessingService,
    MarketplaceAnalyticsService,
    TradingService,
    PaymentService,
  ],
})
export class MarketplaceModule {}
