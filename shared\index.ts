/**
 * Shared Infrastructure - Main Export Index
 * 
 * Exports all shared infrastructure components for easy import in microservices
 * Implements the Microservice Chassis Pattern for standardized cross-cutting concerns
 */

// Main Module
export { SharedInfrastructureModule, SharedInfrastructureConfig } from './shared-infrastructure.module';
export type { SharedInfrastructureOptions } from './shared-infrastructure.module';

// Authentication Infrastructure
export { StandardizedJwtAuthGuard, OptionalJwtAuthGuard, ApiKeyAuthGuard } from './auth/guards/jwt-auth.guard';
export { StandardizedRBACGuard } from './auth/guards/rbac.guard';
export { Public, RequirePermissions, CurrentUser, Roles } from './auth/decorators/auth.decorator';
export { 
  AuthenticatedUser, 
  Permission, 
  Role, 
  AuthContext,
  JWTPayload,
  AuthGuardOptions 
} from './auth/interfaces/auth.interface';

// Response Infrastructure
export { ResponseService } from './responses/services/response.service';
export { ResponseTransformInterceptor } from './responses/interceptors/response-transform.interceptor';
export { ResponseBuilder } from './responses/utils/response-builder.util';
export {
  BaseResponse,
  SuccessResponse,
  ErrorResponse,
  PaginatedResponse,
  ListResponse,
  CreatedResponse,
  UpdatedResponse,
  DeletedResponse,
  HealthCheckResponse,
  ErrorCode,
  ValidationError,
  PaginationMeta,
  SortingOptions,
  FilterOptions,
  ResponseBuilderOptions,
  ErrorDetails,
  BulkOperationResponse,
  AsyncOperationResponse,
  FileUploadResponse,
  SearchResponse
} from './responses/interfaces/base-response.interface';

// Logging Infrastructure
export { ServiceLoggerService, BusinessOutcome, SecurityEventType, SecuritySeverity } from './logging/services/service-logger.service';
export { StructuredLoggerService } from './logging/services/structured-logger.service';
export { LoggingInterceptor } from './logging/interceptors/logging.interceptor';
export { MetricsService } from './logging/services/metrics.service';
export {
  LogContext,
  LogLevel,
  LogEntry,
  BusinessContext,

  PerformanceMetrics,
  AuditLog,
  SecurityLog,
  ErrorLog,
  LoggerConfiguration,
  LoggerTransport
} from './logging/interfaces/logging.interface';

// Configuration Infrastructure
export { StandardizedConfigService } from './config/services/standardized-config.service';
export type {
  ServiceConfig,
  DatabaseConfig,
  JWTConfig,
  RedisConfig,
  APIGatewayConfig,
  SecurityConfig,
  MonitoringConfig,
  ConfigurationOptions
} from './config/services/standardized-config.service';

// Data Infrastructure
export { StandardizedPrismaService } from './data/services/standardized-prisma.service';
export type { QueryMetrics, ConnectionStatus } from './data/services/standardized-prisma.service';

// Error Handling
export { GlobalExceptionFilter } from './filters/global-exception.filter';



// Validation Infrastructure
export { ValidationPipe } from '@nestjs/common';

// Utilities
export { PaginationHelper } from './utils/pagination.helper';
export { ValidationHelper } from './utils/validation.helper';
export { DateHelper } from './utils/date.helper';
export { SecurityHelper } from './utils/security.helper';

// Constants
export * from './constants/response.constants';

// Metrics Infrastructure
export * from './metrics/interfaces/metrics.interface';

/**
 * Shared Infrastructure Quick Setup Helpers
 */

/**
 * Quick setup for API Gateway service
 */
export const setupAPIGateway = (serviceName: string, version: string = '1.0.0') => {
  return SharedInfrastructureModule.forRoot({
    serviceName,
    version,
    enableAuth: true,
    enableResponseTransform: true,
    enableLogging: true,
    enableDatabase: false,
    enableGlobalErrorHandling: true,
  });
};

/**
 * Quick setup for business service (with database)
 */
export const setupBusinessService = (serviceName: string, version: string = '1.0.0', authServiceProvider?: any) => {
  return SharedInfrastructureModule.forRoot({
    serviceName,
    version,
    enableAuth: true,
    enableResponseTransform: true,
    enableLogging: true,
    enableDatabase: true,
    enableGlobalErrorHandling: true,
    authServiceProvider,
  });
};

/**
 * Quick setup for utility service (minimal features)
 */
export const setupUtilityService = (serviceName: string, version: string = '1.0.0') => {
  return SharedInfrastructureModule.forRoot({
    serviceName,
    version,
    enableAuth: false,
    enableResponseTransform: true,
    enableLogging: true,
    enableDatabase: false,
    enableGlobalErrorHandling: true,
  });
};

/**
 * Quick setup for testing
 */
export const setupTesting = (serviceName: string = 'test-service') => {
  return SharedInfrastructureModule.forTesting({
    serviceName,
    version: '1.0.0-test',
  });
};

/**
 * Common Decorators for Controllers
 */
export const CommonControllerDecorators = {
  /**
   * Standard authenticated endpoint
   */
  Authenticated: () => (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // Apply authentication guard
    // Reflect.defineMetadata('guards', [StandardizedJwtAuthGuard], target, propertyKey);
  },

  /**
   * Public endpoint (no authentication required)
   */
  PublicEndpoint: () => (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // Apply public decorator
    // Public()(target, propertyKey, descriptor);
  },

  /**
   * Admin only endpoint
   */
  AdminOnly: () => (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // Apply role guard
    // RequirePermissions(Permission.ADMIN)(target, propertyKey, descriptor);
  },
};

/**
 * Common Response Helpers
 */
export const CommonResponses = {
  /**
   * Standard success response
   */
  success: <T>(data: T, message?: string) => ({
    success: true,
    data,
    message: message || 'Operation completed successfully',
  }),

  /**
   * Standard error response
   */
  error: (message: string, code?: any) => ({
    success: false,
    error: {
      code: code || 'INTERNAL_SERVER_ERROR',
      message,
    },
  }),

  /**
   * Standard not found response
   */
  notFound: (resource: string, id?: string) => ({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `${resource}${id ? ` with ID ${id}` : ''} not found`,
    },
  }),

  /**
   * Standard validation error response
   */
  validationError: (errors: any[]) => ({
    success: false,
    error: {
      code: 'VALIDATION_ERROR',
      message: 'Validation failed',
      validation: errors,
    },
  }),
};

/**
 * Common Logging Helpers
 */
export const CommonLogging = {
  /**
   * Log business event
   */
  logBusinessEvent: (
    logger: any,
    domain: string,
    action: string,
    outcome: any,
    context?: any
  ) => {
    logger.logBusinessEvent(domain, action, outcome, context);
  },

  /**
   * Log security event
   */
  logSecurityEvent: (
    logger: any,
    eventType: any,
    severity: any,
    description: string,
    context?: any
  ) => {
    logger.logSecurityEvent(eventType, severity, description, context);
  },

  /**
   * Log performance metric
   */
  logPerformance: (
    logger: any,
    operation: string,
    duration: number,
    success: boolean,
    context?: any
  ) => {
    logger.logPerformanceMetric(operation, duration, success, context);
  },
};

/**
 * Type Guards
 */
export const TypeGuards = {
  /**
   * Check if response is standardized
   */
  isStandardizedResponse: (data: any): data is any => {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.success === 'boolean' &&
      data.correlationId &&
      data.timestamp &&
      data.service &&
      data.version
    );
  },

  /**
   * Check if response is error response
   */
  isErrorResponse: (data: any): data is any => {
    return (
      TypeGuards.isStandardizedResponse(data) &&
      data.success === false &&
      data.error
    );
  },

  /**
   * Check if response is success response
   */
  isSuccessResponse: (data: any): data is any => {
    return (
      TypeGuards.isStandardizedResponse(data) &&
      data.success === true &&
      data.data !== undefined
    );
  },
};

/**
 * Constants
 */
export const SharedConstants = {
  /**
   * Default pagination limits
   */
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 20,
    MAX_LIMIT: 100,
  },

  /**
   * Default timeouts
   */
  TIMEOUTS: {
    DEFAULT_REQUEST: 30000,
    DEFAULT_DATABASE: 10000,
    DEFAULT_CACHE: 5000,
  },

  /**
   * Security constants
   */
  SECURITY: {
    DEFAULT_RATE_LIMIT: 100,
    DEFAULT_RATE_WINDOW: 60000,
    MIN_PASSWORD_LENGTH: 8,
    JWT_DEFAULT_EXPIRY: '24h',
  },
};

/**
 * Version information
 */
export const SharedInfrastructureVersion = {
  version: '1.0.0',
  buildDate: new Date().toISOString(),
  features: [
    'Authentication & Authorization',
    'Response Standardization',
    'Structured Logging',
    'Configuration Management',
    'Database Integration',
    'Error Handling',
    'Performance Monitoring',
    'Security Events',
  ],
};

// Re-export commonly used NestJS decorators for convenience
export {
  Controller,
  Get,
  Post,
  Put,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  Headers,
  UseGuards,
  UseInterceptors,
  UsePipes,
  Injectable,
  Module,
} from '@nestjs/common';
