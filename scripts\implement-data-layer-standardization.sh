#!/bin/bash

# Implement Data Layer Standardization
# This script standardizes data access patterns and repository implementations across all services

set -e

echo "🗄️ Implementing Data Layer Standardization"
echo "=========================================="

# Services to standardize
SERVICES=(
    "api-gateway"
    "user-service"
    "profile-analysis-service"
    "nft-generation-service"
    "blockchain-service"
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Function to create standardized data module for service
create_service_data_module() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    echo "🔧 Creating standardized data module for $service_name..."
    
    # Check if service directory exists
    if [ ! -d "$service_dir" ]; then
        echo "❌ Service directory not found: $service_dir"
        return 1
    fi
    
    # Create data directory structure
    mkdir -p "$service_dir/src/data"
    mkdir -p "$service_dir/src/data/repositories"
    mkdir -p "$service_dir/src/data/services"
    mkdir -p "$service_dir/src/data/dto"
    
    # Create service-specific data module
    cat > "$service_dir/src/data/data.module.ts" << 'EOF'
import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Shared data components
import { StandardizedPrismaService } from '../../../../shared/data/services/prisma.service';

// Service-specific data services
import { DatabaseService } from './services/database.service';
import { RepositoryFactory } from './services/repository-factory.service';
import { DataHealthService } from './services/data-health.service';

// Import service-specific repositories
// Note: These would be imported based on the service's domain models

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    // Core data services
    StandardizedPrismaService,
    DatabaseService,
    RepositoryFactory,
    DataHealthService,
    
    // Provide services for injection
    {
      provide: 'DATABASE_SERVICE',
      useClass: DatabaseService,
    },
    {
      provide: 'REPOSITORY_FACTORY',
      useClass: RepositoryFactory,
    },
    {
      provide: 'DATA_HEALTH_SERVICE',
      useClass: DataHealthService,
    },
    {
      provide: 'PRISMA_SERVICE',
      useClass: StandardizedPrismaService,
    },
  ],
  exports: [
    StandardizedPrismaService,
    DatabaseService,
    RepositoryFactory,
    DataHealthService,
    'DATABASE_SERVICE',
    'REPOSITORY_FACTORY',
    'DATA_HEALTH_SERVICE',
    'PRISMA_SERVICE',
  ],
})
export class ServiceDataModule {}
EOF

    # Create database service implementation
    cat > "$service_dir/src/data/services/database.service.ts" << 'EOF'
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StandardizedPrismaService } from '../../../../../shared/data/services/prisma.service';
import { ServiceLoggerService } from '../../../logging/services/service-logger.service';
import { MetricsService } from '../../../../../shared/logging/services/metrics.service';
import {
  BusinessOutcome,
  SecurityEventType,
  SecuritySeverity,
  SecurityOutcome,
} from '../../../../../shared/logging/interfaces/logger.interface';

@Injectable()
export class DatabaseService implements OnModuleInit {
  private readonly logger = new Logger(DatabaseService.name);
  private readonly serviceName: string;

  constructor(
    private readonly prisma: StandardizedPrismaService,
    private readonly serviceLogger: ServiceLoggerService,
    private readonly metricsService: MetricsService,
    private readonly configService: ConfigService,
  ) {
    this.serviceName = this.configService.get<string>('SERVICE_NAME') || 'unknown-service';
  }

  async onModuleInit() {
    await this.initializeDatabase();
  }

  /**
   * Initialize database connection and perform health checks
   */
  private async initializeDatabase(): Promise<void> {
    try {
      this.serviceLogger.logOperationStart('database_initialization');

      // Perform initial health check
      const healthResult = await this.prisma.healthCheck();
      
      if (healthResult.status === 'healthy') {
        this.serviceLogger.logBusinessEvent(
          'database',
          'initialization',
          BusinessOutcome.SUCCESS,
          {
            responseTime: healthResult.responseTime,
            details: healthResult.details,
          }
        );

        this.logger.log(`✅ Database initialized successfully for ${this.serviceName}`);
      } else {
        throw new Error(`Database health check failed: ${JSON.stringify(healthResult.details)}`);
      }

      this.serviceLogger.logOperationComplete('database_initialization', 0, true);
    } catch (error) {
      this.serviceLogger.logOperationComplete('database_initialization', 0, false, {
        error: error.message,
      });

      this.serviceLogger.logSecurityEvent(
        SecurityEventType.CONFIGURATION_CHANGE,
        SecuritySeverity.HIGH,
        SecurityOutcome.FAILURE,
        {
          details: {
            component: 'database',
            error: error.message,
          },
        }
      );

      this.logger.error(`❌ Database initialization failed for ${this.serviceName}:`, error);
      throw error;
    }
  }

  /**
   * Execute raw SQL query with monitoring
   */
  async executeQuery<T>(query: string, params?: any[]): Promise<T[]> {
    const startTime = Date.now();
    
    try {
      this.serviceLogger.logOperationStart('database_raw_query', {
        metadata: {
          query: query.substring(0, 100), // Truncate for logging
          paramCount: params?.length || 0,
        },
      });

      const result = await this.prisma.queryWithMetrics<T>(query, params);
      const duration = Date.now() - startTime;

      this.serviceLogger.logDatabaseOperation('raw_query', 'custom', duration, true, {
        query: query.substring(0, 100),
        resultCount: result.length,
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.serviceLogger.logDatabaseOperation('raw_query', 'custom', duration, false, {
        query: query.substring(0, 100),
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Execute operation within transaction
   */
  async executeInTransaction<T>(
    operation: (prisma: any) => Promise<T>,
    options?: {
      maxWait?: number;
      timeout?: number;
      isolationLevel?: any;
    }
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      this.serviceLogger.logOperationStart('database_transaction', {
        metadata: { options },
      });

      const result = await this.prisma.transactionWithMetrics(operation, options);
      const duration = Date.now() - startTime;

      this.serviceLogger.logDatabaseOperation('transaction', 'multiple', duration, true, {
        options,
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.serviceLogger.logDatabaseOperation('transaction', 'multiple', duration, false, {
        error: error.message,
        options,
      });
      throw error;
    }
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'unhealthy' | 'degraded';
    responseTime: number;
    details: any;
  }> {
    return this.prisma.healthCheck();
  }

  /**
   * Get database connection status
   */
  isConnected(): boolean {
    return this.prisma.isConnected();
  }

  /**
   * Get service name
   */
  getServiceName(): string {
    return this.serviceName;
  }

  /**
   * Get Prisma client for direct access (use sparingly)
   */
  getPrismaClient(): StandardizedPrismaService {
    return this.prisma;
  }
}
EOF

    # Create repository factory service
    cat > "$service_dir/src/data/services/repository-factory.service.ts" << 'EOF'
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StandardizedPrismaService } from '../../../../../shared/data/services/prisma.service';
import { ServiceLoggerService } from '../../../logging/services/service-logger.service';
import { MetricsService } from '../../../../../shared/logging/services/metrics.service';

@Injectable()
export class RepositoryFactory {
  private readonly logger = new Logger(RepositoryFactory.name);
  private readonly repositories = new Map<string, any>();

  constructor(
    private readonly prisma: StandardizedPrismaService,
    private readonly serviceLogger: ServiceLoggerService,
    private readonly metricsService: MetricsService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Get or create repository instance
   */
  getRepository<T>(repositoryClass: new (...args: any[]) => T): T {
    const className = repositoryClass.name;
    
    if (!this.repositories.has(className)) {
      this.logger.debug(`Creating repository instance: ${className}`);
      
      const repository = new repositoryClass(
        this.prisma,
        this.serviceLogger,
        this.metricsService,
        this.configService,
      );
      
      this.repositories.set(className, repository);
      
      this.serviceLogger.getStructuredLogger().info(`Repository created: ${className}`, {
        metadata: {
          repository: className,
          timestamp: new Date().toISOString(),
        },
      });
    }

    return this.repositories.get(className);
  }

  /**
   * Register repository instance
   */
  registerRepository<T>(name: string, repository: T): void {
    this.repositories.set(name, repository);
    this.logger.debug(`Repository registered: ${name}`);
  }

  /**
   * Get all registered repositories
   */
  getAllRepositories(): Map<string, any> {
    return new Map(this.repositories);
  }

  /**
   * Clear all repositories (useful for testing)
   */
  clearRepositories(): void {
    this.repositories.clear();
    this.logger.debug('All repositories cleared');
  }

  /**
   * Get repository health status
   */
  async getRepositoriesHealthStatus(): Promise<Record<string, any>> {
    const healthStatus: Record<string, any> = {};

    for (const [name, repository] of this.repositories.entries()) {
      try {
        if (repository.healthCheck && typeof repository.healthCheck === 'function') {
          healthStatus[name] = await repository.healthCheck();
        } else {
          healthStatus[name] = {
            status: 'unknown',
            message: 'Health check not implemented',
          };
        }
      } catch (error) {
        healthStatus[name] = {
          status: 'unhealthy',
          error: error.message,
        };
      }
    }

    return healthStatus;
  }
}
EOF

    # Create data health service
    cat > "$service_dir/src/data/services/data-health.service.ts" << 'EOF'
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseService } from './database.service';
import { RepositoryFactory } from './repository-factory.service';
import { HealthMonitoringService } from '../../../logging/services/health-monitoring.service';
import { HealthStatus } from '../../../../../shared/logging/interfaces/metrics.interface';

@Injectable()
export class DataHealthService {
  private readonly logger = new Logger(DataHealthService.name);
  private readonly serviceName: string;

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly repositoryFactory: RepositoryFactory,
    private readonly healthMonitoring: HealthMonitoringService,
    private readonly configService: ConfigService,
  ) {
    this.serviceName = this.configService.get<string>('SERVICE_NAME') || 'unknown-service';
  }

  /**
   * Perform comprehensive data layer health check
   */
  async performHealthCheck(): Promise<{
    status: 'healthy' | 'unhealthy' | 'degraded';
    components: Record<string, any>;
    overall: {
      responseTime: number;
      timestamp: string;
    };
  }> {
    const startTime = Date.now();
    const components: Record<string, any> = {};
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';

    try {
      // Database health check
      const dbHealth = await this.databaseService.getHealthStatus();
      components.database = dbHealth;

      if (dbHealth.status !== 'healthy') {
        overallStatus = dbHealth.status === 'unhealthy' ? 'unhealthy' : 'degraded';
      }

      // Record database health metric
      this.healthMonitoring.recordDatabaseHealth(
        dbHealth.status as HealthStatus,
        dbHealth.responseTime,
        dbHealth.details
      );

      // Repository health checks
      const repoHealth = await this.repositoryFactory.getRepositoriesHealthStatus();
      components.repositories = repoHealth;

      // Check repository health status
      for (const [name, health] of Object.entries(repoHealth)) {
        if (health.status === 'unhealthy') {
          overallStatus = 'unhealthy';
        } else if (health.status === 'degraded' && overallStatus === 'healthy') {
          overallStatus = 'degraded';
        }
      }

      // Connection status
      components.connection = {
        status: this.databaseService.isConnected() ? 'connected' : 'disconnected',
        service: this.serviceName,
      };

      if (!this.databaseService.isConnected()) {
        overallStatus = 'unhealthy';
      }

      const responseTime = Date.now() - startTime;

      // Record overall service health
      this.healthMonitoring.recordServiceHealth(
        overallStatus as HealthStatus,
        {
          components,
          responseTime,
        }
      );

      return {
        status: overallStatus,
        components,
        overall: {
          responseTime,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error('Data health check failed:', error);

      // Record unhealthy status
      this.healthMonitoring.recordServiceHealth(HealthStatus.UNHEALTHY, {
        error: error.message,
        components,
      });

      return {
        status: 'unhealthy',
        components: {
          ...components,
          error: {
            message: error.message,
            timestamp: new Date().toISOString(),
          },
        },
        overall: {
          responseTime: Date.now() - startTime,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Get database metrics
   */
  async getDatabaseMetrics(): Promise<Record<string, any>> {
    try {
      const dbHealth = await this.databaseService.getHealthStatus();
      const repoHealth = await this.repositoryFactory.getRepositoriesHealthStatus();

      return {
        database: {
          status: dbHealth.status,
          responseTime: dbHealth.responseTime,
          details: dbHealth.details,
        },
        repositories: repoHealth,
        connection: {
          status: this.databaseService.isConnected() ? 'connected' : 'disconnected',
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Test database connectivity
   */
  async testConnectivity(): Promise<boolean> {
    try {
      const health = await this.databaseService.getHealthStatus();
      return health.status === 'healthy';
    } catch (error) {
      this.logger.error('Connectivity test failed:', error);
      return false;
    }
  }
}
EOF

    # Create example repository implementation
    cat > "$service_dir/src/data/repositories/example.repository.ts" << 'EOF'
import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../../shared/data/repositories/base.repository';
import { StandardizedPrismaService } from '../../../../../shared/data/services/prisma.service';
import { ServiceLoggerService } from '../../../logging/services/service-logger.service';
import { MetricsService } from '../../../../../shared/logging/services/metrics.service';
import { ConfigService } from '@nestjs/config';

// Example DTOs - replace with actual service DTOs
interface ExampleEntity {
  id: string;
  name: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateExampleDto {
  name: string;
  description: string;
}

interface UpdateExampleDto {
  name?: string;
  description?: string;
}

@Injectable()
export class ExampleRepository extends BaseRepository<ExampleEntity, CreateExampleDto, UpdateExampleDto> {
  constructor(
    prisma: StandardizedPrismaService,
    serviceLogger: ServiceLoggerService,
    metricsService: MetricsService,
    configService: ConfigService,
  ) {
    super(prisma, serviceLogger, metricsService, configService, 'Example');
  }

  /**
   * Get the Prisma model delegate
   */
  protected getModel(): any {
    // Replace 'example' with actual model name from your Prisma schema
    // return this.prisma.example;
    
    // For demonstration purposes, return a mock object
    return {
      findUnique: async (args: any) => null,
      findMany: async (args: any) => [],
      findFirst: async (args: any) => null,
      create: async (args: any) => ({ id: '1', ...args.data, createdAt: new Date(), updatedAt: new Date() }),
      update: async (args: any) => ({ id: args.where.id, ...args.data, updatedAt: new Date() }),
      delete: async (args: any) => ({ id: args.where.id }),
      updateMany: async (args: any) => ({ count: 0 }),
      deleteMany: async (args: any) => ({ count: 0 }),
      count: async (args: any) => 0,
    };
  }

  /**
   * Transform entity to DTO
   */
  protected toDto(entity: any): ExampleEntity {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  /**
   * Transform create DTO to entity data
   */
  protected toCreateData(dto: CreateExampleDto): any {
    return {
      name: dto.name,
      description: dto.description,
    };
  }

  /**
   * Transform update DTO to entity data
   */
  protected toUpdateData(dto: UpdateExampleDto): any {
    const data: any = {};
    if (dto.name !== undefined) data.name = dto.name;
    if (dto.description !== undefined) data.description = dto.description;
    return data;
  }

  /**
   * Custom repository methods can be added here
   */
  async findByName(name: string): Promise<ExampleEntity | null> {
    return this.findOne({
      where: { name } as any,
    });
  }

  async findActiveExamples(): Promise<ExampleEntity[]> {
    return this.findMany({
      where: { 
        // Add your active condition here
        // active: true 
      } as any,
    });
  }
}
EOF

    # Create data module index
    cat > "$service_dir/src/data/index.ts" << 'EOF'
export * from './data.module';
export * from './services/database.service';
export * from './services/repository-factory.service';
export * from './services/data-health.service';
export * from './repositories/example.repository';
EOF

    echo "✅ Data module created for $service_name"
}

# Function to update app.module.ts to use standardized data layer
update_app_module_data() {
    local service_name="$1"
    local service_dir="services/$service_name"
    local app_module="$service_dir/src/app.module.ts"
    
    echo "🔧 Updating app.module.ts data configuration for $service_name..."
    
    # Create backup
    cp "$app_module" "$app_module.data_backup.$(date +%Y%m%d_%H%M%S)"
    
    # Update imports section to include data module
    sed -i '/import.*ServiceLoggingModule/a import { ServiceDataModule } from '\''./data/data.module'\'';' "$app_module"
    
    # Update imports array to include ServiceDataModule
    sed -i '/ServiceLoggingModule,/a \    ServiceDataModule,' "$app_module"
    
    echo "✅ app.module.ts updated for $service_name"
}

# Function to create health endpoint with data health checks
create_data_health_endpoint() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    echo "🔧 Creating data health endpoint for $service_name..."
    
    # Create health controller if it doesn't exist
    mkdir -p "$service_dir/src/health"
    
    cat > "$service_dir/src/health/data-health.controller.ts" << 'EOF'
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ApiHealthResponse } from '../../../../shared/responses/decorators/api-response.decorator';
import { DataHealthService } from '../data/services/data-health.service';
import { ResponseService } from '../responses/services/response.service';

@ApiTags('Health - Data Layer')
@Controller('health/data')
export class DataHealthController {
  constructor(
    private readonly dataHealthService: DataHealthService,
    private readonly responseService: ResponseService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Check data layer health' })
  @ApiHealthResponse()
  async checkDataHealth() {
    const healthResult = await this.dataHealthService.performHealthCheck();
    
    if (healthResult.status === 'healthy') {
      return this.responseService.success(healthResult, 'Data layer is healthy');
    } else {
      return this.responseService.error(
        `Data layer is ${healthResult.status}`,
        'SERVICE_DEGRADED' as any,
        healthResult
      );
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Get data layer metrics' })
  @ApiHealthResponse()
  async getDataMetrics() {
    const metrics = await this.dataHealthService.getDatabaseMetrics();
    return this.responseService.success(metrics, 'Data metrics retrieved');
  }

  @Get('connectivity')
  @ApiOperation({ summary: 'Test database connectivity' })
  @ApiHealthResponse()
  async testConnectivity() {
    const isConnected = await this.dataHealthService.testConnectivity();
    
    if (isConnected) {
      return this.responseService.success(
        { connected: true },
        'Database connectivity test passed'
      );
    } else {
      return this.responseService.error(
        'Database connectivity test failed',
        'DATABASE_ERROR' as any
      );
    }
  }
}
EOF

    echo "✅ Data health endpoint created for $service_name"
}

# Main execution
echo "🚀 Starting data layer standardization..."

for service_name in "${SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service_name..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service_name" ]; then
        echo "❌ Service directory not found: services/$service_name"
        continue
    fi
    
    # Create data module
    create_service_data_module "$service_name"
    
    # Update app.module.ts
    update_app_module_data "$service_name"
    
    # Create data health endpoint
    create_data_health_endpoint "$service_name"
    
    echo "✅ $service_name data layer standardization complete"
done

echo ""
echo "🎉 Data Layer Standardization Implementation Completed!"
echo "====================================================="
echo ""
echo "📋 Summary:"
echo "- Created standardized data modules for ${#SERVICES[@]} services"
echo "- Implemented enterprise Prisma service with monitoring"
echo "- Added repository pattern with base implementations"
echo "- Created database health monitoring services"
echo "- Added data layer health endpoints"
echo ""
echo "📁 Files Created:"
echo "services/[service]/src/data/data.module.ts                      # Data module"
echo "services/[service]/src/data/services/database.service.ts        # Database service"
echo "services/[service]/src/data/services/repository-factory.service.ts # Repository factory"
echo "services/[service]/src/data/services/data-health.service.ts     # Data health service"
echo "services/[service]/src/data/repositories/example.repository.ts  # Example repository"
echo "services/[service]/src/health/data-health.controller.ts         # Data health endpoint"
echo ""
echo "🔍 Next Steps:"
echo "1. Update Prisma schema files for each service"
echo "2. Implement actual repository classes for domain models"
echo "3. Test database connections and health endpoints"
echo "4. Configure database connection strings"
echo "5. Run database migrations"
echo ""
echo "⚠️  Important Notes:"
echo "• All services now have standardized Prisma service with monitoring"
echo "• Repository pattern provides consistent data access across services"
echo "• Database health monitoring tracks performance and connectivity"
echo "• Transaction management is built into the base repository"
echo "• Query performance is automatically monitored and logged"
