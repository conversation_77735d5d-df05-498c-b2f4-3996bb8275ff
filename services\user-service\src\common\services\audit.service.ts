import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

export interface AuditLogData {
  userId?: string;
  action: string;
  resource: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  entityType?: string;
  entityId?: string;
  newValues?: any;
  oldValues?: any;
  correlationId?: string;
  metadata?: any;
  timestamp?: Date;
  sessionId?: string;
  changedFields?: string[];
}

@Injectable()
export class AuditService {
  constructor(private prisma: PrismaService) {}

  async log(data: AuditLogData): Promise<void> {
    try {
      await this.prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          resource: data.resource,
          details: data.details,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
        },
      });
    } catch (error) {
      console.error('Failed to create audit log:', error);
    }
  }

  async logAction(data: {
    userId?: string;
    action: string;
    resource: string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
    entityType?: string;
    entityId?: string;
    newValues?: any;
    oldValues?: any;
    correlationId?: string;
    metadata?: any;
    timestamp?: Date;
    sessionId?: string;
    changedFields?: string[];
  }): Promise<void> {
    await this.log(data);
  }

  async logUserAction(
    userId: string,
    action: string,
    resource: string,
    details?: any,
    request?: any
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource,
      details,
      ipAddress: request?.ip,
      userAgent: request?.headers?.['user-agent'],
    });
  }

  async getAuditLogs(filters: {
    userId?: string;
    action?: string;
    resource?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }) {
    const where: any = {};

    if (filters.userId) where.userId = filters.userId;
    if (filters.action) where.action = filters.action;
    if (filters.resource) where.resource = filters.resource;
    if (filters.startDate || filters.endDate) {
      where.createdAt = {};
      if (filters.startDate) where.createdAt.gte = filters.startDate;
      if (filters.endDate) where.createdAt.lte = filters.endDate;
    }

    return this.prisma.auditLog.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: filters.limit || 100,
      skip: filters.offset || 0,
    });
  }
}
