import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { nftEvolutionService } from '@/services/nftEvolutionService'
import {
  NFTEvolution,
  EvolutionTrigger,
  EvolutionTemplate,
  EvolutionAnalytics,
  CreateEvolutionRequest,
  UpdateEvolutionRequest,
  EvolutionSearchRequest,
  TraitChange,
  MetadataChange,
  VisualChange
} from '@/types/nft-evolution.types'

// ===== EVOLUTION MANAGEMENT HOOKS =====

export function useEvolution(id: string) {
  return useQuery({
    queryKey: ['evolution', id],
    queryFn: () => nftEvolutionService.getEvolution(id),
    enabled: !!id,
    staleTime: 300000, // 5 minutes
    refetchInterval: 30000, // 30 seconds for active evolutions
  })
}

export function useNFTEvolutions(nftId: string) {
  return useQuery({
    queryKey: ['nft-evolutions', nftId],
    queryFn: () => nftEvolutionService.getNFTEvolutions(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

export function useSearchEvolutions(request: EvolutionSearchRequest) {
  return useQuery({
    queryKey: ['search-evolutions', request],
    queryFn: () => nftEvolutionService.searchEvolutions(request),
    enabled: !!(request.nftIds?.length || request.triggerTypes?.length || request.statuses?.length),
    staleTime: 300000,
  })
}

export function useCreateEvolution() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: CreateEvolutionRequest) => 
      nftEvolutionService.createEvolution(request),
    onSuccess: (newEvolution) => {
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', newEvolution.nftId] })
      queryClient.invalidateQueries({ queryKey: ['evolution-analytics'] })
      queryClient.setQueryData(['evolution', newEvolution.id], newEvolution)
    },
    onError: (error: any) => {
      console.error('Failed to create evolution:', error)
    },
  })
}

export function useUpdateEvolution() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, request }: { id: string; request: UpdateEvolutionRequest }) =>
      nftEvolutionService.updateEvolution(id, request),
    onSuccess: (updatedEvolution, { id }) => {
      queryClient.setQueryData(['evolution', id], updatedEvolution)
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to update evolution:', error)
    },
  })
}

export function useCancelEvolution() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftEvolutionService.cancelEvolution(id),
    onSuccess: (updatedEvolution, id) => {
      queryClient.setQueryData(['evolution', id], updatedEvolution)
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to cancel evolution:', error)
    },
  })
}

export function useApproveEvolution() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftEvolutionService.approveEvolution(id),
    onSuccess: (updatedEvolution, id) => {
      queryClient.setQueryData(['evolution', id], updatedEvolution)
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to approve evolution:', error)
    },
  })
}

export function useRejectEvolution() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) =>
      nftEvolutionService.rejectEvolution(id, reason),
    onSuccess: (updatedEvolution, { id }) => {
      queryClient.setQueryData(['evolution', id], updatedEvolution)
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to reject evolution:', error)
    },
  })
}

// ===== EVOLUTION TRIGGERS HOOKS =====

export function useEvolutionTriggers() {
  return useQuery({
    queryKey: ['evolution-triggers'],
    queryFn: () => nftEvolutionService.getTriggers(),
    staleTime: 600000, // 10 minutes
  })
}

export function useEvolutionTrigger(id: string) {
  return useQuery({
    queryKey: ['evolution-trigger', id],
    queryFn: () => nftEvolutionService.getTrigger(id),
    enabled: !!id,
    staleTime: 600000,
  })
}

export function useAvailableTriggersForNFT(nftId: string) {
  return useQuery({
    queryKey: ['available-triggers', nftId],
    queryFn: () => nftEvolutionService.getAvailableTriggersForNFT(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

export function useCreateTrigger() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (trigger: Omit<EvolutionTrigger, 'id' | 'createdAt' | 'updatedAt' | 'currentExecutions'>) =>
      nftEvolutionService.createTrigger(trigger),
    onSuccess: (newTrigger) => {
      queryClient.invalidateQueries({ queryKey: ['evolution-triggers'] })
      queryClient.setQueryData(['evolution-trigger', newTrigger.id], newTrigger)
    },
    onError: (error: any) => {
      console.error('Failed to create trigger:', error)
    },
  })
}

export function useUpdateTrigger() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<EvolutionTrigger> }) =>
      nftEvolutionService.updateTrigger(id, updates),
    onSuccess: (updatedTrigger, { id }) => {
      queryClient.setQueryData(['evolution-trigger', id], updatedTrigger)
      queryClient.invalidateQueries({ queryKey: ['evolution-triggers'] })
    },
    onError: (error: any) => {
      console.error('Failed to update trigger:', error)
    },
  })
}

export function useDeleteTrigger() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftEvolutionService.deleteTrigger(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: ['evolution-trigger', id] })
      queryClient.invalidateQueries({ queryKey: ['evolution-triggers'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete trigger:', error)
    },
  })
}

export function useToggleTrigger() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, activate }: { id: string; activate: boolean }) =>
      activate 
        ? nftEvolutionService.activateTrigger(id)
        : nftEvolutionService.deactivateTrigger(id),
    onSuccess: (updatedTrigger, { id }) => {
      queryClient.setQueryData(['evolution-trigger', id], updatedTrigger)
      queryClient.invalidateQueries({ queryKey: ['evolution-triggers'] })
    },
    onError: (error: any) => {
      console.error('Failed to toggle trigger:', error)
    },
  })
}

// ===== EVOLUTION TEMPLATES HOOKS =====

export function useEvolutionTemplates() {
  return useQuery({
    queryKey: ['evolution-templates'],
    queryFn: () => nftEvolutionService.getTemplates(),
    staleTime: 600000,
  })
}

export function useEvolutionTemplate(id: string) {
  return useQuery({
    queryKey: ['evolution-template', id],
    queryFn: () => nftEvolutionService.getTemplate(id),
    enabled: !!id,
    staleTime: 600000,
  })
}

export function useCreateTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (template: Omit<EvolutionTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>) =>
      nftEvolutionService.createTemplate(template),
    onSuccess: (newTemplate) => {
      queryClient.invalidateQueries({ queryKey: ['evolution-templates'] })
      queryClient.setQueryData(['evolution-template', newTemplate.id], newTemplate)
    },
    onError: (error: any) => {
      console.error('Failed to create template:', error)
    },
  })
}

export function useUpdateTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<EvolutionTemplate> }) =>
      nftEvolutionService.updateTemplate(id, updates),
    onSuccess: (updatedTemplate, { id }) => {
      queryClient.setQueryData(['evolution-template', id], updatedTemplate)
      queryClient.invalidateQueries({ queryKey: ['evolution-templates'] })
    },
    onError: (error: any) => {
      console.error('Failed to update template:', error)
    },
  })
}

export function useDeleteTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftEvolutionService.deleteTemplate(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: ['evolution-template', id] })
      queryClient.invalidateQueries({ queryKey: ['evolution-templates'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete template:', error)
    },
  })
}

export function useApplyTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ templateId, nftId, customizations }: { 
      templateId: string; 
      nftId: string; 
      customizations?: Record<string, any> 
    }) => nftEvolutionService.applyTemplate(templateId, nftId, customizations),
    onSuccess: (newEvolution) => {
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', newEvolution.nftId] })
      queryClient.setQueryData(['evolution', newEvolution.id], newEvolution)
    },
    onError: (error: any) => {
      console.error('Failed to apply template:', error)
    },
  })
}

// ===== TRAIT MANAGEMENT HOOKS =====

export function useUpdateTraits() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ evolutionId, traitChanges }: { evolutionId: string; traitChanges: TraitChange[] }) =>
      nftEvolutionService.updateTraits(evolutionId, traitChanges),
    onSuccess: (updatedEvolution, { evolutionId }) => {
      queryClient.setQueryData(['evolution', evolutionId], updatedEvolution)
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to update traits:', error)
    },
  })
}

export function useValidateTraitChanges() {
  return useMutation({
    mutationFn: ({ nftId, traitChanges }: { nftId: string; traitChanges: TraitChange[] }) =>
      nftEvolutionService.validateTraitChanges(nftId, traitChanges),
    onError: (error: any) => {
      console.error('Failed to validate trait changes:', error)
    },
  })
}

export function usePreviewTraitChanges() {
  return useMutation({
    mutationFn: ({ nftId, traitChanges }: { nftId: string; traitChanges: TraitChange[] }) =>
      nftEvolutionService.previewTraitChanges(nftId, traitChanges),
    onError: (error: any) => {
      console.error('Failed to preview trait changes:', error)
    },
  })
}

// ===== METADATA MANAGEMENT HOOKS =====

export function useUpdateMetadata() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ evolutionId, metadataChanges }: { evolutionId: string; metadataChanges: MetadataChange[] }) =>
      nftEvolutionService.updateMetadata(evolutionId, metadataChanges),
    onSuccess: (updatedEvolution, { evolutionId }) => {
      queryClient.setQueryData(['evolution', evolutionId], updatedEvolution)
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to update metadata:', error)
    },
  })
}

export function useMetadataHistory(nftId: string) {
  return useQuery({
    queryKey: ['metadata-history', nftId],
    queryFn: () => nftEvolutionService.getMetadataHistory(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

export function useRollbackMetadata() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ nftId, version }: { nftId: string; version: string }) =>
      nftEvolutionService.rollbackMetadata(nftId, version),
    onSuccess: (updatedEvolution) => {
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
      queryClient.invalidateQueries({ queryKey: ['metadata-history', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to rollback metadata:', error)
    },
  })
}

export function useValidateMetadata() {
  return useMutation({
    mutationFn: ({ nftId, metadata }: { nftId: string; metadata: Record<string, any> }) =>
      nftEvolutionService.validateMetadata(nftId, metadata),
    onError: (error: any) => {
      console.error('Failed to validate metadata:', error)
    },
  })
}

// ===== VISUAL CHANGES HOOKS =====

export function useUpdateVisuals() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ evolutionId, visualChanges }: { evolutionId: string; visualChanges: VisualChange[] }) =>
      nftEvolutionService.updateVisuals(evolutionId, visualChanges),
    onSuccess: (updatedEvolution, { evolutionId }) => {
      queryClient.setQueryData(['evolution', evolutionId], updatedEvolution)
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to update visuals:', error)
    },
  })
}

export function useGenerateVisualPreview() {
  return useMutation({
    mutationFn: ({ nftId, visualChanges }: { nftId: string; visualChanges: VisualChange[] }) =>
      nftEvolutionService.generateVisualPreview(nftId, visualChanges),
    onError: (error: any) => {
      console.error('Failed to generate visual preview:', error)
    },
  })
}

export function useRegenerateVisuals() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ evolutionId, elementIds }: { evolutionId: string; elementIds: string[] }) =>
      nftEvolutionService.regenerateVisuals(evolutionId, elementIds),
    onSuccess: (updatedEvolution, { evolutionId }) => {
      queryClient.setQueryData(['evolution', evolutionId], updatedEvolution)
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to regenerate visuals:', error)
    },
  })
}

// ===== EVOLUTION HISTORY HOOKS =====

export function useEvolutionHistory(nftId: string) {
  return useQuery({
    queryKey: ['evolution-history', nftId],
    queryFn: () => nftEvolutionService.getEvolutionHistory(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

export function useEvolutionTimeline(nftId: string) {
  return useQuery({
    queryKey: ['evolution-timeline', nftId],
    queryFn: () => nftEvolutionService.getEvolutionTimeline(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

export function useRevertEvolution() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (evolutionId: string) => nftEvolutionService.revertEvolution(evolutionId),
    onSuccess: (updatedEvolution) => {
      queryClient.setQueryData(['evolution', updatedEvolution.id], updatedEvolution)
      queryClient.invalidateQueries({ queryKey: ['nft-evolutions', updatedEvolution.nftId] })
      queryClient.invalidateQueries({ queryKey: ['evolution-history', updatedEvolution.nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to revert evolution:', error)
    },
  })
}

// ===== ANALYTICS HOOKS =====

export function useEvolutionAnalytics(nftId: string, timeframe: string = '30d') {
  return useQuery({
    queryKey: ['evolution-analytics', nftId, timeframe],
    queryFn: () => nftEvolutionService.getEvolutionAnalytics(nftId, timeframe),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

export function useGlobalEvolutionStats() {
  return useQuery({
    queryKey: ['global-evolution-stats'],
    queryFn: () => nftEvolutionService.getGlobalEvolutionStats(),
    staleTime: 600000,
  })
}

export function useEvolutionPredictions(nftId: string) {
  return useQuery({
    queryKey: ['evolution-predictions', nftId],
    queryFn: () => nftEvolutionService.getEvolutionPredictions(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

// ===== UTILITY HOOKS =====

export function useSimulateEvolution() {
  return useMutation({
    mutationFn: ({ nftId, triggerId, customizations }: { 
      nftId: string; 
      triggerId: string; 
      customizations?: Record<string, any> 
    }) => nftEvolutionService.simulateEvolution(nftId, triggerId, customizations),
    onError: (error: any) => {
      console.error('Failed to simulate evolution:', error)
    },
  })
}

export function useEstimateEvolutionCosts() {
  return useMutation({
    mutationFn: ({ nftId, triggerId }: { nftId: string; triggerId: string }) =>
      nftEvolutionService.estimateEvolutionCosts(nftId, triggerId),
    onError: (error: any) => {
      console.error('Failed to estimate evolution costs:', error)
    },
  })
}

export function useExportEvolutionData() {
  return useMutation({
    mutationFn: ({ nftId, format }: { nftId: string; format?: 'json' | 'csv' }) =>
      nftEvolutionService.exportEvolutionData(nftId, format),
    onError: (error: any) => {
      console.error('Failed to export evolution data:', error)
    },
  })
}
