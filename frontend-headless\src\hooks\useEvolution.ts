import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { evolutionService } from '@/services/evolutionService'
import {
  EvolutionTimeline,
  EvolutionEvent,
  EvolutionAnalytics,
  EvolutionFilters,
  EvolutionStats,
  CreateEvolutionEventRequest,
  UpdateEvolutionRequest,
  EvolutionInsights,
  PredictedEvolution,
  EvolutionEventType,
  EvolutionTriggerType
} from '@/types/evolution.types'

// ===== EVOLUTION TIMELINE HOOKS =====

export function useEvolutionTimeline(nftId: string, filters?: EvolutionFilters) {
  return useQuery({
    queryKey: ['evolution', 'timeline', nftId, filters],
    queryFn: () => evolutionService.getEvolutionTimeline(nftId, filters),
    enabled: !!nftId,
    staleTime: 300000, // 5 minutes
    refetchOnWindowFocus: false,
  })
}

export function useEvolutionEvents(nftId: string, filters?: EvolutionFilters) {
  return useQuery({
    queryKey: ['evolution', 'events', nftId, filters],
    queryFn: () => evolutionService.getEvolutionEvents(nftId, filters),
    enabled: !!nftId,
    staleTime: 60000, // 1 minute
  })
}

export function useEvolutionEvent(eventId: string) {
  return useQuery({
    queryKey: ['evolution', 'event', eventId],
    queryFn: () => evolutionService.getEvolutionEvent(eventId),
    enabled: !!eventId,
    staleTime: 300000,
  })
}

// ===== EVOLUTION ANALYTICS HOOKS =====

export function useEvolutionAnalytics(nftId: string, timeframe?: '7d' | '30d' | '90d' | '1y') {
  return useQuery({
    queryKey: ['evolution', 'analytics', nftId, timeframe],
    queryFn: () => evolutionService.getEvolutionAnalytics(nftId, timeframe),
    enabled: !!nftId,
    staleTime: 600000, // 10 minutes
  })
}

export function useEvolutionStats(nftId: string) {
  return useQuery({
    queryKey: ['evolution', 'stats', nftId],
    queryFn: () => evolutionService.getEvolutionStats(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

export function useEvolutionInsights(nftId: string) {
  return useQuery({
    queryKey: ['evolution', 'insights', nftId],
    queryFn: () => evolutionService.getEvolutionInsights(nftId),
    enabled: !!nftId,
    staleTime: 600000,
  })
}

// ===== EVOLUTION PREDICTIONS HOOKS =====

export function usePredictedEvolution(nftId: string) {
  return useQuery({
    queryKey: ['evolution', 'prediction', nftId],
    queryFn: () => evolutionService.getPredictedEvolution(nftId),
    enabled: !!nftId,
    staleTime: 1800000, // 30 minutes
  })
}

export function useUpdateEvolutionPrediction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (nftId: string) => evolutionService.updateEvolutionPrediction(nftId),
    onSuccess: (prediction, nftId) => {
      queryClient.setQueryData(['evolution', 'prediction', nftId], prediction)
    },
    onError: (error: any) => {
      console.error('Failed to update evolution prediction:', error)
    },
  })
}

// ===== EVOLUTION TRIGGER HOOKS =====

export function useTriggerEvolution() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ nftId, data }: { nftId: string; data: UpdateEvolutionRequest }) => 
      evolutionService.triggerEvolution(nftId, data),
    onSuccess: (evolutionEvent, { nftId }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['evolution', 'timeline', nftId] })
      queryClient.invalidateQueries({ queryKey: ['evolution', 'events', nftId] })
      queryClient.invalidateQueries({ queryKey: ['evolution', 'analytics', nftId] })
      queryClient.invalidateQueries({ queryKey: ['evolution', 'stats', nftId] })
      queryClient.invalidateQueries({ queryKey: ['evolution', 'insights', nftId] })
      queryClient.invalidateQueries({ queryKey: ['nft', 'collection'] })
      queryClient.invalidateQueries({ queryKey: ['nft', 'details', nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to trigger evolution:', error)
    },
  })
}

export function useUpdateNFTScore() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ nftId, newScore, trigger }: { nftId: string; newScore: number; trigger: any }) => 
      evolutionService.updateNFTScore(nftId, newScore, trigger),
    onSuccess: (evolutionEvent, { nftId }) => {
      // Invalidate all evolution-related queries
      queryClient.invalidateQueries({ queryKey: ['evolution'] })
      queryClient.invalidateQueries({ queryKey: ['nft'] })
    },
    onError: (error: any) => {
      console.error('Failed to update NFT score:', error)
    },
  })
}

export function useCreateEvolutionEvent() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateEvolutionEventRequest) => evolutionService.createEvolutionEvent(data),
    onSuccess: (evolutionEvent) => {
      queryClient.invalidateQueries({ 
        queryKey: ['evolution', 'events', evolutionEvent.nftId] 
      })
      queryClient.invalidateQueries({ 
        queryKey: ['evolution', 'timeline', evolutionEvent.nftId] 
      })
    },
    onError: (error: any) => {
      console.error('Failed to create evolution event:', error)
    },
  })
}

// ===== COMPARATIVE ANALYTICS HOOKS =====

export function useEvolutionComparison(nftIds: string[]) {
  return useQuery({
    queryKey: ['evolution', 'comparison', nftIds],
    queryFn: () => evolutionService.getEvolutionComparison(nftIds),
    enabled: nftIds.length > 0,
    staleTime: 600000,
  })
}

export function useGlobalEvolutionStats() {
  return useQuery({
    queryKey: ['evolution', 'global', 'stats'],
    queryFn: () => evolutionService.getGlobalEvolutionStats(),
    staleTime: 1800000, // 30 minutes
  })
}

// ===== EVOLUTION NOTIFICATIONS HOOKS =====

export function useEvolutionNotifications(userId?: string) {
  return useQuery({
    queryKey: ['evolution', 'notifications', userId],
    queryFn: () => evolutionService.getEvolutionNotifications(userId),
    staleTime: 60000,
    refetchInterval: 300000, // 5 minutes
  })
}

export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (notificationId: string) => evolutionService.markNotificationAsRead(notificationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['evolution', 'notifications'] })
    },
    onError: (error: any) => {
      console.error('Failed to mark notification as read:', error)
    },
  })
}

// ===== EVOLUTION HISTORY HOOKS =====

export function useEvolutionHistory(filters?: {
  userId?: string
  campaignId?: string
  eventType?: EvolutionEventType
  triggerType?: EvolutionTriggerType
  dateRange?: { start: string; end: string }
  page?: number
  limit?: number
}) {
  return useQuery({
    queryKey: ['evolution', 'history', filters],
    queryFn: () => evolutionService.getEvolutionHistory(filters),
    staleTime: 300000,
  })
}

export function useExportEvolutionData() {
  return useMutation({
    mutationFn: ({ nftId, format }: { nftId: string; format: 'json' | 'csv' | 'pdf' }) => 
      evolutionService.exportEvolutionData(nftId, format),
    onError: (error: any) => {
      console.error('Failed to export evolution data:', error)
    },
  })
}

// ===== EVOLUTION MILESTONES HOOKS =====

export function useEvolutionMilestones(nftId: string) {
  return useQuery({
    queryKey: ['evolution', 'milestones', nftId],
    queryFn: () => evolutionService.getEvolutionMilestones(nftId),
    enabled: !!nftId,
    staleTime: 600000,
  })
}

export function useClaimMilestoneReward() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ nftId, milestoneId }: { nftId: string; milestoneId: string }) => 
      evolutionService.claimMilestoneReward(nftId, milestoneId),
    onSuccess: (result, { nftId }) => {
      queryClient.invalidateQueries({ queryKey: ['evolution', 'milestones', nftId] })
    },
    onError: (error: any) => {
      console.error('Failed to claim milestone reward:', error)
    },
  })
}

// ===== UTILITY HOOKS =====

export function useSearchEvolutionEvents(query: string, filters?: EvolutionFilters) {
  return useQuery({
    queryKey: ['evolution', 'search', query, filters],
    queryFn: () => evolutionService.searchEvolutionEvents(query, filters),
    enabled: !!query && query.length > 2,
    staleTime: 300000,
  })
}

export function useEvolutionLeaderboard(timeframe = '30d', limit = 50) {
  return useQuery({
    queryKey: ['evolution', 'leaderboard', timeframe, limit],
    queryFn: () => evolutionService.getEvolutionLeaderboard(timeframe, limit),
    staleTime: 600000,
  })
}

// ===== COMBINED HOOKS FOR COMPLEX OPERATIONS =====

export function useNFTEvolutionData(nftId: string) {
  const timeline = useEvolutionTimeline(nftId)
  const analytics = useEvolutionAnalytics(nftId)
  const stats = useEvolutionStats(nftId)
  const insights = useEvolutionInsights(nftId)
  const prediction = usePredictedEvolution(nftId)
  const milestones = useEvolutionMilestones(nftId)

  return {
    timeline: timeline.data,
    analytics: analytics.data,
    stats: stats.data,
    insights: insights.data,
    prediction: prediction.data,
    milestones: milestones.data,
    isLoading: timeline.isLoading || analytics.isLoading || stats.isLoading,
    isError: timeline.isError || analytics.isError || stats.isError,
    error: timeline.error || analytics.error || stats.error,
    refetch: () => {
      timeline.refetch()
      analytics.refetch()
      stats.refetch()
      insights.refetch()
      prediction.refetch()
      milestones.refetch()
    }
  }
}
