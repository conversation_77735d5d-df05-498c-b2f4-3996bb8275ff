# 🤖 AI Agent Core Development Principles

## Overview
These guidelines ensure consistent, high-quality code generation and development practices across all AI agents working on the Social NFT Platform.

## 1. Code Quality Standards

### A. TypeScript Best Practices
```typescript
// ✅ GOOD: Explicit types and interfaces
interface UserCreateRequest {
  username: string;
  email: string;
  password: string;
}

class UserService {
  async createUser(request: UserCreateRequest): Promise<User> {
    // Implementation
  }
}

// ❌ BAD: Any types and unclear interfaces
class UserService {
  async createUser(data: any): Promise<any> {
    // Implementation
  }
}
```

### B. Naming Conventions
- **Files**: kebab-case (`user-service.controller.ts`)
- **Classes**: PascalCase (`UserService`)
- **Methods**: camelCase (`createUser`)
- **Constants**: SCREAMING_SNAKE_CASE (`MAX_RETRY_ATTEMPTS`)
- **Interfaces**: PascalCase with 'I' prefix (`IUserRepository`)

### C. Error Handling Standards
```typescript
// ✅ GOOD: Structured error handling
try {
  const result = await this.userService.createUser(userData);
  return {
    success: true,
    data: result,
    message: 'User created successfully'
  };
} catch (error) {
  this.logger.error('Failed to create user', {
    error: error.message,
    stack: error.stack,
    userData: { ...userData, password: '[REDACTED]' }
  });

  throw new BadRequestException({
    message: 'Failed to create user',
    code: 'USER_CREATION_FAILED',
    details: error.message
  });
}
```

## 2. Architecture Patterns

### A. Feature-Based Organization
```
service/
├── src/
│   ├── authentication/           # Feature module
│   │   ├── controllers/
│   │   ├── services/
│   │   ├── repositories/
│   │   ├── dto/
│   │   ├── entities/
│   │   └── tests/
│   ├── profile-management/       # Another feature
│   └── shared/                   # Shared utilities
```

### B. Dependency Injection Pattern
```typescript
// ✅ GOOD: Proper DI with interfaces
@Injectable()
export class UserService {
  constructor(
    @Inject('USER_REPOSITORY')
    private readonly userRepository: IUserRepository,
    private readonly logger: LoggerService,
    private readonly eventBus: EventBusService
  ) {}
}
```

### C. Repository Pattern
```typescript
// ✅ GOOD: Abstract repository interface
export interface IUserRepository {
  findById(id: string): Promise<User | null>;
  create(userData: CreateUserDto): Promise<User>;
  update(id: string, userData: UpdateUserDto): Promise<User>;
  delete(id: string): Promise<void>;
}

@Injectable()
export class UserRepository implements IUserRepository {
  // Implementation
}
```

## 3. Testing Requirements

### A. Test Structure
```typescript
// ✅ GOOD: Comprehensive test structure
describe('UserService', () => {
  let service: UserService;
  let repository: jest.Mocked<IUserRepository>;
  let logger: jest.Mocked<LoggerService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: 'USER_REPOSITORY',
          useValue: createMockRepository(),
        },
        {
          provide: LoggerService,
          useValue: createMockLogger(),
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get('USER_REPOSITORY');
    logger = module.get(LoggerService);
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      // Arrange
      const userData = { username: 'test', email: '<EMAIL>' };
      const expectedUser = { id: '1', ...userData };
      repository.create.mockResolvedValue(expectedUser);

      // Act
      const result = await service.createUser(userData);

      // Assert
      expect(result).toEqual(expectedUser);
      expect(repository.create).toHaveBeenCalledWith(userData);
    });

    it('should handle creation errors', async () => {
      // Test error scenarios
    });
  });
});
```

### B. Test Coverage Requirements
- **Unit Tests**: Minimum 80% coverage
- **Integration Tests**: All API endpoints
- **E2E Tests**: Critical user journeys
- **Performance Tests**: Load and stress testing

## 4. Documentation Standards

### A. Code Documentation
```typescript
/**
 * Creates a new user in the system
 *
 * @param userData - User creation data
 * @returns Promise<User> - Created user object
 * @throws BadRequestException - When validation fails
 * @throws ConflictException - When user already exists
 *
 * @example
 * ```typescript
 * const user = await userService.createUser({
 *   username: 'john_doe',
 *   email: '<EMAIL>',
 *   password: 'securePassword123'
 * });
 * ```
 */
async createUser(userData: CreateUserDto): Promise<User> {
  // Implementation
}
```

### B. API Documentation
- Use OpenAPI/Swagger for all endpoints
- Include request/response examples
- Document error codes and messages
- Provide authentication requirements

## 5. Security Guidelines

### A. Input Validation
```typescript
// ✅ GOOD: Comprehensive validation
export class CreateUserDto {
  @IsString()
  @Length(3, 50)
  @Matches(/^[a-zA-Z0-9_]+$/)
  username: string;

  @IsEmail()
  @Length(5, 100)
  email: string;

  @IsString()
  @Length(8, 128)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  password: string;
}
```

### B. Data Sanitization
```typescript
// ✅ GOOD: Sanitize sensitive data in logs
this.logger.info('User created', {
  userId: user.id,
  username: user.username,
  // Never log passwords or sensitive data
});
```

## 6. Performance Guidelines

### A. Database Queries
```typescript
// ✅ GOOD: Optimized queries with relations
const users = await this.userRepository.find({
  relations: ['profile'],
  where: { isActive: true },
  take: 10,
  skip: offset,
  order: { createdAt: 'DESC' }
});
```

### B. Caching Strategy
```typescript
// ✅ GOOD: Implement caching for expensive operations
@Cacheable('user-profile', 300) // 5 minutes TTL
async getUserProfile(userId: string): Promise<UserProfile> {
  return this.userRepository.findProfileById(userId);
}
```

## 7. MANDATORY: Development Documentation Rule

### A. Issue Documentation Requirement
**EVERY AI agent MUST document all issues and solutions during development.**

```markdown
MANDATORY RULE: When any issue is encountered and resolved:
1. Document the issue and solution immediately
2. Write to file in docs/development/ directory
3. Update the documentation index (docs/development/README.md)
4. Follow the established documentation format
```

### B. Documentation Triggers
Document when encountering:
- Technical errors and their solutions
- Configuration problems and fixes
- Integration challenges and resolutions
- Performance issues and optimizations
- Security concerns and mitigations
- Any troubleshooting process

### C. Documentation Format
```markdown
# [Issue Category] - [Brief Description]

## Issue Description
- **Date:** YYYY-MM-DD
- **Component:** Service/Frontend/Database/etc.
- **Severity:** Critical/High/Medium/Low
- **Error Message:** Exact error text

## Solution Implemented
- Step-by-step solution
- Code changes made
- Configuration updates

## Verification
- How solution was tested
- Confirmation of fix

## Prevention
- How to avoid in future
- Best practices established
```

### D. File Management
- **Location:** `docs/development/`
- **Naming:** Descriptive with category prefix
- **Index Update:** Always update `docs/development/README.md`
- **Cross-Reference:** Link related issues

**Reference:** See `docs/guidelines/DEVELOPMENT_DOCUMENTATION_RULE.md` for complete details.

## AI Agent Compliance Checklist

When generating code, AI agents must verify:

- [ ] TypeScript types are explicit and correct
- [ ] Error handling is comprehensive
- [ ] Logging includes appropriate context
- [ ] Tests are included for new functionality
- [ ] Documentation follows JSDoc standards
- [ ] Security best practices are applied
- [ ] Performance considerations are addressed
- [ ] Code follows established patterns
- [ ] **ALL ISSUES AND SOLUTIONS ARE DOCUMENTED** ⭐ **MANDATORY**
- [ ] **Documentation index is updated** ⭐ **MANDATORY**
