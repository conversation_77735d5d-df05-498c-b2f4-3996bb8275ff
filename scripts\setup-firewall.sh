#!/bin/bash

# System-Level Firewall Configuration Script
# Blocks direct access to microservice ports while allowing API Gateway

set -e

echo "🔥 Setting up system-level firewall rules"
echo "========================================"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

# Backup existing rules
echo "📋 Backing up existing iptables rules..."
iptables-save > /etc/iptables/rules.backup.$(date +%Y%m%d_%H%M%S)

# Flush existing rules
echo "🧹 Flushing existing iptables rules..."
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X
iptables -t mangle -F
iptables -t mangle -X

# Set default policies
echo "🔒 Setting default policies..."
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback traffic
echo "🔄 Allowing loopback traffic..."
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# Allow established and related connections
echo "🔗 Allowing established connections..."
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# Allow SSH (adjust port if needed)
SSH_PORT=${SSH_PORT:-22}
echo "🔑 Allowing SSH on port $SSH_PORT..."
iptables -A INPUT -p tcp --dport $SSH_PORT -j ACCEPT

# Allow HTTP and HTTPS
echo "🌐 Allowing HTTP/HTTPS traffic..."
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Allow API Gateway
echo "🚪 Allowing API Gateway on port 3010..."
iptables -A INPUT -p tcp --dport 3010 -j ACCEPT

# Block direct service access from external sources
echo "🚫 Blocking direct service access from external sources..."
SERVICE_PORTS="3002,3003,3004,3005,3006,3008,3009,3011"

# Block external access to service ports
iptables -A INPUT -p tcp -m multiport --dports $SERVICE_PORTS -s ! 127.0.0.1 -j DROP

# Allow localhost access to services (for health checks and internal communication)
echo "✅ Allowing localhost access to services..."
iptables -A INPUT -p tcp -m multiport --dports $SERVICE_PORTS -s 127.0.0.1 -j ACCEPT

# Allow private network access (adjust subnet as needed)
PRIVATE_SUBNET=${PRIVATE_SUBNET:-"10.0.0.0/8,**********/12,***********/16"}
echo "🏠 Allowing private network access from $PRIVATE_SUBNET..."
IFS=',' read -ra SUBNETS <<< "$PRIVATE_SUBNET"
for subnet in "${SUBNETS[@]}"; do
    iptables -A INPUT -p tcp -m multiport --dports $SERVICE_PORTS -s "$subnet" -j ACCEPT
done

# Allow Docker network access (if using Docker)
if command -v docker &> /dev/null; then
    echo "🐳 Allowing Docker network access..."
    DOCKER_NETWORKS=$(docker network ls --format "{{.Name}}" | grep -v "bridge\|host\|none")
    for network in $DOCKER_NETWORKS; do
        SUBNET=$(docker network inspect "$network" --format "{{range .IPAM.Config}}{{.Subnet}}{{end}}" 2>/dev/null || echo "")
        if [ -n "$SUBNET" ]; then
            iptables -A INPUT -p tcp -m multiport --dports $SERVICE_PORTS -s "$SUBNET" -j ACCEPT
        fi
    done
fi

# Allow database access (PostgreSQL)
DB_PORT=${DB_PORT:-5432}
echo "🗄️  Configuring database access on port $DB_PORT..."
iptables -A INPUT -p tcp --dport $DB_PORT -s 127.0.0.1 -j ACCEPT
for subnet in "${SUBNETS[@]}"; do
    iptables -A INPUT -p tcp --dport $DB_PORT -s "$subnet" -j ACCEPT
done

# Allow Redis access
REDIS_PORT=${REDIS_PORT:-6379}
echo "📦 Configuring Redis access on port $REDIS_PORT..."
iptables -A INPUT -p tcp --dport $REDIS_PORT -s 127.0.0.1 -j ACCEPT
for subnet in "${SUBNETS[@]}"; do
    iptables -A INPUT -p tcp --dport $REDIS_PORT -s "$subnet" -j ACCEPT
done

# Log dropped packets (optional, for debugging)
if [ "${LOG_DROPPED:-false}" = "true" ]; then
    echo "📝 Enabling logging for dropped packets..."
    iptables -A INPUT -j LOG --log-prefix "DROPPED: " --log-level 4
fi

# Save rules
echo "💾 Saving iptables rules..."
if [ -f /etc/debian_version ]; then
    # Debian/Ubuntu
    iptables-save > /etc/iptables/rules.v4
    echo "✅ Rules saved to /etc/iptables/rules.v4"
elif [ -f /etc/redhat-release ]; then
    # RHEL/CentOS
    service iptables save
    echo "✅ Rules saved via iptables service"
else
    # Generic
    iptables-save > /etc/iptables.rules
    echo "✅ Rules saved to /etc/iptables.rules"
fi

# Create restore script
echo "📜 Creating restore script..."
cat > /usr/local/bin/restore-firewall.sh << 'EOF'
#!/bin/bash
# Restore firewall rules on boot

if [ -f /etc/iptables/rules.v4 ]; then
    iptables-restore < /etc/iptables/rules.v4
elif [ -f /etc/iptables.rules ]; then
    iptables-restore < /etc/iptables.rules
fi
EOF

chmod +x /usr/local/bin/restore-firewall.sh

# Set up automatic restore on boot
if systemctl list-unit-files | grep -q iptables-persistent; then
    echo "🔄 iptables-persistent is already installed"
elif command -v systemctl &> /dev/null; then
    echo "🔄 Setting up systemd service for firewall restore..."
    cat > /etc/systemd/system/firewall-restore.service << 'EOF'
[Unit]
Description=Restore Firewall Rules
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/restore-firewall.sh
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl enable firewall-restore.service
    echo "✅ Firewall restore service enabled"
fi

# Display current rules
echo ""
echo "📋 Current iptables rules:"
echo "========================="
iptables -L -n --line-numbers

echo ""
echo "🎉 Firewall configuration completed!"
echo ""
echo "📊 Summary:"
echo "• SSH access: Port $SSH_PORT ✅"
echo "• HTTP/HTTPS: Ports 80/443 ✅"
echo "• API Gateway: Port 3010 ✅"
echo "• Service ports: $SERVICE_PORTS (localhost/private only) 🔒"
echo "• Database: Port $DB_PORT (localhost/private only) 🔒"
echo "• Redis: Port $REDIS_PORT (localhost/private only) 🔒"
echo ""
echo "⚠️  IMPORTANT:"
echo "• Test your connections before logging out"
echo "• Keep a backup SSH session open"
echo "• Rules will be restored automatically on reboot"
echo ""
echo "🧪 Test commands:"
echo "# Test API Gateway (should work):"
echo "curl http://localhost:3010/health"
echo ""
echo "# Test direct service access (should be blocked from external):"
echo "curl http://your-server-ip:3005/"
echo ""
echo "# Test from localhost (should work):"
echo "curl http://localhost:3005/health"
