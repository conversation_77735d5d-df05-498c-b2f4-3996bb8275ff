'use client'

import React, { useState } from 'react'
import {
  RectangleStackIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  PauseIcon,
  StarIcon,
  ShareIcon,
  HeartIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import {
  useUpdateCampaignListing,
  useDeleteCampaignListing,
  usePublishListing,
  useUnpublishListing,
  useFeatureListing
} from '@/hooks/useMarketplaceIntegration'
import { 
  CampaignMarketplaceListing, 
  MarketplaceFilters, 
  MarketplaceStatus,
  SortOption 
} from '@/types/marketplace-integration.types'

interface MarketplaceListingsProps {
  listings: CampaignMarketplaceListing[]
  total: number
  isLoading: boolean
  filters: MarketplaceFilters
  onFiltersChange: (filters: MarketplaceFilters) => void
  onViewListing?: (id: string) => void
  onCreateListing?: () => void
  className?: string
}

export default function MarketplaceListings({
  listings,
  total,
  isLoading,
  filters,
  onFiltersChange,
  onViewListing,
  onCreateListing,
  className = ''
}: MarketplaceListingsProps) {
  const [selectedListings, setSelectedListings] = useState<string[]>([])

  const updateListingMutation = useUpdateCampaignListing()
  const deleteListingMutation = useDeleteCampaignListing()
  const publishListingMutation = usePublishListing()
  const unpublishListingMutation = useUnpublishListing()
  const featureListingMutation = useFeatureListing()

  const handleStatusChange = async (id: string, status: MarketplaceStatus) => {
    try {
      if (status === MarketplaceStatus.LIVE) {
        await publishListingMutation.mutateAsync(id)
      } else if (status === MarketplaceStatus.PAUSED) {
        await unpublishListingMutation.mutateAsync(id)
      } else {
        await updateListingMutation.mutateAsync({ id, data: { status } })
      }
    } catch (error) {
      console.error('Failed to update listing status:', error)
    }
  }

  const handleDeleteListing = async (id: string) => {
    if (confirm('Are you sure you want to delete this listing?')) {
      try {
        await deleteListingMutation.mutateAsync(id)
      } catch (error) {
        console.error('Failed to delete listing:', error)
      }
    }
  }

  const handleFeatureListing = async (id: string) => {
    try {
      await featureListingMutation.mutateAsync({ id, duration: 7 }) // Feature for 7 days
    } catch (error) {
      console.error('Failed to feature listing:', error)
    }
  }

  const getStatusColor = (status: MarketplaceStatus) => {
    switch (status) {
      case MarketplaceStatus.LIVE: return 'text-green-600 bg-green-100'
      case MarketplaceStatus.DRAFT: return 'text-gray-600 bg-gray-100'
      case MarketplaceStatus.PENDING_REVIEW: return 'text-yellow-600 bg-yellow-100'
      case MarketplaceStatus.APPROVED: return 'text-blue-600 bg-blue-100'
      case MarketplaceStatus.REJECTED: return 'text-red-600 bg-red-100'
      case MarketplaceStatus.PAUSED: return 'text-orange-600 bg-orange-100'
      case MarketplaceStatus.COMPLETED: return 'text-purple-600 bg-purple-100'
      case MarketplaceStatus.ARCHIVED: return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const sortOptions = [
    { value: SortOption.NEWEST, label: 'Newest First' },
    { value: SortOption.OLDEST, label: 'Oldest First' },
    { value: SortOption.PRICE_LOW_HIGH, label: 'Price: Low to High' },
    { value: SortOption.PRICE_HIGH_LOW, label: 'Price: High to Low' },
    { value: SortOption.MOST_POPULAR, label: 'Most Popular' },
    { value: SortOption.MOST_VIEWED, label: 'Most Viewed' }
  ]

  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: MarketplaceStatus.DRAFT, label: 'Draft' },
    { value: MarketplaceStatus.LIVE, label: 'Live' },
    { value: MarketplaceStatus.PAUSED, label: 'Paused' },
    { value: MarketplaceStatus.COMPLETED, label: 'Completed' }
  ]

  const currentPage = filters.page || 1
  const limit = filters.limit || 20
  const totalPages = Math.ceil(total / limit)

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header and Filters */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div>
          <h2 className="text-lg font-medium text-gray-900">My Marketplace Listings</h2>
          <p className="text-sm text-gray-600">{total} total listings</p>
        </div>

        <div className="flex items-center space-x-4">
          {/* Status Filter */}
          <select
            value={filters.status?.[0] || ''}
            onChange={(e) => onFiltersChange({
              ...filters,
              status: e.target.value ? [e.target.value as MarketplaceStatus] : undefined,
              page: 1
            })}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={filters.sortBy || SortOption.NEWEST}
            onChange={(e) => onFiltersChange({
              ...filters,
              sortBy: e.target.value as SortOption,
              page: 1
            })}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          {/* Create Listing Button */}
          {onCreateListing && (
            <button
              onClick={onCreateListing}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              New Listing
            </button>
          )}
        </div>
      </div>

      {/* Listings Grid */}
      {listings.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {listings.map((listing) => (
              <div
                key={listing.id}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:border-gray-300 hover:shadow-md transition-all"
              >
                <div className="relative">
                  <img
                    src={listing.coverImage}
                    alt={listing.title}
                    className="w-full h-48 object-cover"
                  />
                  
                  {/* Status Badge */}
                  <div className="absolute top-3 left-3">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(listing.status)}`}>
                      {listing.status.replace('_', ' ')}
                    </span>
                  </div>

                  {/* Featured Badge */}
                  {listing.isFeatured && (
                    <div className="absolute top-3 right-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <StarIcon className="h-3 w-3 mr-1" />
                        Featured
                      </span>
                    </div>
                  )}

                  {/* Quick Actions Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onViewListing?.(listing.id)}
                        className="p-2 bg-white rounded-full text-gray-700 hover:text-blue-600"
                        title="View Details"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      
                      <button
                        className="p-2 bg-white rounded-full text-gray-700 hover:text-green-600"
                        title="Edit Listing"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>

                      {listing.status === MarketplaceStatus.LIVE ? (
                        <button
                          onClick={() => handleStatusChange(listing.id, MarketplaceStatus.PAUSED)}
                          disabled={unpublishListingMutation.isPending}
                          className="p-2 bg-white rounded-full text-gray-700 hover:text-orange-600 disabled:opacity-50"
                          title="Pause Listing"
                        >
                          <PauseIcon className="h-4 w-4" />
                        </button>
                      ) : (
                        <button
                          onClick={() => handleStatusChange(listing.id, MarketplaceStatus.LIVE)}
                          disabled={publishListingMutation.isPending}
                          className="p-2 bg-white rounded-full text-gray-700 hover:text-green-600 disabled:opacity-50"
                          title="Publish Listing"
                        >
                          <PlayIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-1">{listing.title}</h3>
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">{listing.description}</p>
                  
                  {/* Campaign Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div>
                      <span className="text-gray-500">Participants:</span>
                      <span className="font-medium ml-1">{listing.campaignData.participantCount}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">NFTs:</span>
                      <span className="font-medium ml-1">{listing.campaignData.totalNFTsGenerated}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Completion:</span>
                      <span className="font-medium ml-1">{(listing.campaignData.completionRate * 100).toFixed(1)}%</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Quality:</span>
                      <span className="font-medium ml-1">{listing.campaignData.averageQuality.toFixed(1)}/10</span>
                    </div>
                  </div>

                  {/* Engagement Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <EyeIcon className="h-4 w-4" />
                        <span>{listing.views.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <HeartIcon className="h-4 w-4" />
                        <span>{listing.favorites.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <ShareIcon className="h-4 w-4" />
                        <span>{listing.shares.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Price and Actions */}
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-lg font-bold text-gray-900">
                        {listing.basePrice} {listing.currency}
                      </div>
                      <div className="text-xs text-gray-500">
                        {listing.soldCount}/{listing.totalSupply} sold
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {!listing.isFeatured && (
                        <button
                          onClick={() => handleFeatureListing(listing.id)}
                          disabled={featureListingMutation.isPending}
                          className="p-1 text-gray-400 hover:text-yellow-600 disabled:opacity-50"
                          title="Feature Listing"
                        >
                          <StarIcon className="h-4 w-4" />
                        </button>
                      )}

                      <button
                        onClick={() => handleDeleteListing(listing.id)}
                        disabled={deleteListingMutation.isPending}
                        className="p-1 text-gray-400 hover:text-red-600 disabled:opacity-50"
                        title="Delete Listing"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((currentPage - 1) * limit) + 1} to {Math.min(currentPage * limit, total)} of {total} results
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => onFiltersChange({ ...filters, page: currentPage - 1 })}
                  disabled={currentPage <= 1}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeftIcon className="h-4 w-4 mr-1" />
                  Previous
                </button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <button
                        key={page}
                        onClick={() => onFiltersChange({ ...filters, page })}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          page === currentPage
                            ? 'bg-blue-600 text-white'
                            : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    )
                  })}
                </div>

                <button
                  onClick={() => onFiltersChange({ ...filters, page: currentPage + 1 })}
                  disabled={currentPage >= totalPages}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                  <ChevronRightIcon className="h-4 w-4 ml-1" />
                </button>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <RectangleStackIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No listings found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {filters.status ? 'No listings match your current filters.' : 'Create your first marketplace listing to get started.'}
          </p>
          {onCreateListing && (
            <div className="mt-6">
              <button
                onClick={onCreateListing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Listing
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
