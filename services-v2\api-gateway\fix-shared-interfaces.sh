#!/bin/bash

echo "🔧 Fixing shared infrastructure interface usage..."

# Fix Permission.SYSTEM_ADMIN to Permission.SYSTEM_ADMIN
find src -name "*.ts" -type f -exec sed -i "s|Permission.SYSTEM_ADMIN|Permission.SYSTEM_ADMIN|g" {} \;

# Fix BusinessOutcome usage
find src -name "*.ts" -type f -exec sed -i "s|'SUCCESS'|BusinessOutcome.SUCCESS|g" {} \;
find src -name "*.ts" -type f -exec sed -i "s|'FAILURE'|BusinessOutcome.FAILURE|g" {} \;

# Fix metrics.observe to metrics.histogram
find src -name "*.ts" -type f -exec sed -i "s|this.metrics.observe|this.metrics.histogram|g" {} \;

echo "✅ Basic fixes applied. Manual fixes needed for MetricTags and LogContext."
