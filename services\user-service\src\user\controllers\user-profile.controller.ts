import {
  <PERSON>,
  Get,
  Put,
  Body,
  Param,
  Query,
  Headers,
  <PERSON><PERSON>,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import {
  UserProfileService,
  ProfileUpdateRequest,
} from '../services/user-profile.service';
import { UserPreferences } from '../services/user-management.service';

@ApiTags('User Profiles')
@Controller('profiles')
export class UserProfileController {
  private readonly logger = new Logger(UserProfileController.name);

  constructor(private readonly userProfile: UserProfileService) {}

  @Get(':userId')
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserProfile(
    @Param('userId') userId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 Get user profile request received`, { correlationId, userId });

      const profile = await this.userProfile.getUserProfile(userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: profile,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get user profile failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('public/:username')
  @ApiOperation({ summary: 'Get public profile by username' })
  @ApiResponse({ status: 200, description: 'Public profile retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found or profile is private' })
  async getPublicProfile(
    @Param('username') username: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🌐 Get public profile request received`, { correlationId, username });

      const publicProfile = await this.userProfile.getPublicProfile(username);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: publicProfile,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get public profile failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') || error.message.includes('private') ? 
                        HttpStatus.NOT_FOUND : HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Put(':userId')
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async updateProfile(
    @Param('userId') userId: string,
    @Body() updateRequest: ProfileUpdateRequest,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`✏️ Update profile request received`, { correlationId, userId });

      const updatedProfile = await this.userProfile.updateProfile(userId, updateRequest);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: updatedProfile,
        message: 'Profile updated successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Update profile failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        error.message.includes('Validation failed') ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Put(':userId/preferences')
  @ApiOperation({ summary: 'Update user preferences' })
  @ApiResponse({ status: 200, description: 'Preferences updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updatePreferences(
    @Param('userId') userId: string,
    @Body() preferences: Partial<UserPreferences>,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`⚙️ Update preferences request received`, { correlationId, userId });

      const updatedUser = await this.userProfile.updatePreferences(userId, preferences);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: updatedUser,
        message: 'Preferences updated successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Update preferences failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get(':userId/activity')
  @ApiOperation({ summary: 'Get user activity feed' })
  @ApiResponse({ status: 200, description: 'Activity feed retrieved successfully' })
  async getUserActivity(
    @Param('userId') userId: string,
    @Query('limit') limit: string,
    @Query('offset') offset: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Get user activity request received`, { correlationId, userId });

      const activities = await this.userProfile.getUserActivity(
        userId,
        parseInt(limit) || 50,
        parseInt(offset) || 0
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          activities,
          count: activities.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get user activity failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get(':userId/achievements')
  @ApiOperation({ summary: 'Get user achievements' })
  @ApiResponse({ status: 200, description: 'Achievements retrieved successfully' })
  async getUserAchievements(
    @Param('userId') userId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🏆 Get user achievements request received`, { correlationId, userId });

      const achievementData = await this.userProfile.getUserAchievements(userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: achievementData,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get user achievements failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('search/:query')
  @ApiOperation({ summary: 'Search user profiles' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  async searchProfiles(
    @Param('query') query: string,
    @Query('location') location: string,
    @Query('minScore') minScore: string,
    @Query('hasNFTs') hasNFTs: string,
    @Query('limit') limit: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🔍 Search profiles request received`, { correlationId, query });

      const filters: any = {};
      if (location) filters.location = location;
      if (minScore) filters.minScore = parseInt(minScore);
      if (hasNFTs) filters.hasNFTs = hasNFTs === 'true';

      const profiles = await this.userProfile.searchUsers(
        query,
        Object.keys(filters).length > 0 ? filters : undefined,
        parseInt(limit) || 20
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          query,
          filters,
          profiles,
          count: profiles.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Search profiles failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('leaderboard/:type')
  @ApiOperation({ summary: 'Get leaderboard' })
  @ApiResponse({ status: 200, description: 'Leaderboard retrieved successfully' })
  async getLeaderboard(
    @Param('type') type: 'score' | 'nfts' | 'analyses',
    @Query('limit') limit: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🏆 Get leaderboard request received`, { correlationId, type });

      const leaderboard = await this.userProfile.getLeaderboard(
        type,
        parseInt(limit) || 50
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          type,
          leaderboard,
          count: leaderboard.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get leaderboard failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for profile service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          service: 'user-profile-service',
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        },
        correlationId
      });
    } catch (error) {
      return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: 'Service unavailable',
        correlationId: headers['x-correlation-id']
      });
    }
  }
}
