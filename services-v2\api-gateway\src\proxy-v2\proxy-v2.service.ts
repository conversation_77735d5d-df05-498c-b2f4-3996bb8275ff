import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ServiceLoggerService, MetricsService } from '../../shared';
import { ServiceDiscoveryService } from '../service-discovery/service-discovery.service';
import { CircuitBreakerService } from '../circuit-breaker/circuit-breaker.service';

export interface ProxyRequest {
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  query?: Record<string, string>;
  correlationId?: string;
}

export interface ProxyResponse {
  success: boolean;
  data?: any;
  status: number;
  headers?: Record<string, string>;
  error?: string;
  correlationId: string;
  service: string;
  duration: number;
}

/**
 * Enhanced Proxy Service V2
 * 
 * Enterprise-grade proxy service with:
 * - Service discovery integration
 * - Circuit breaker protection
 * - Load balancing
 * - Comprehensive monitoring
 * - Correlation ID tracking
 * 
 * @example
 * ```typescript
 * const response = await proxyService.proxyToService('user-service', request);
 * ```
 */
@Injectable()
export class ProxyV2Service {
  constructor(
    private readonly logger: ServiceLoggerService,
    private readonly metrics: MetricsService,
    private readonly serviceDiscovery: ServiceDiscoveryService,
    private readonly circuitBreaker: CircuitBreakerService
  ) {}

  /**
   * Proxy request to any service using service discovery
   */
  async proxyToService(serviceName: string, request: ProxyRequest): Promise<ProxyResponse> {
    const startTime = Date.now();
    const correlationId = request.correlationId || this.generateCorrelationId();
    
    this.logger.logBusinessEvent('api-gateway', 'proxy-request', 'SUCCESS', {
      correlationId,
      business: {
        domain: 'api-gateway',
        entity: 'proxy-request',
        action: 'proxy-request',
        outcome: 'SUCCESS',
        attributes: {
          serviceName,
          method: request.method,
          url: request.url,
        },
      },
    });

    try {
      // Get healthy service instance
      const instance = await this.serviceDiscovery.selectInstance(serviceName);
      if (!instance) {
        throw new HttpException(
          `No healthy instances available for service: ${serviceName}`,
          HttpStatus.SERVICE_UNAVAILABLE
        );
      }

      // Execute request with circuit breaker protection
      const response = await this.circuitBreaker.execute(
        serviceName,
        () => this.executeRequest(instance.url, request, correlationId),
        {
          failureThreshold: 5,
          resetTimeout: 60000,
          bulkheadEnabled: true,
          maxConcurrentRequests: 20,
        }
      );

      const duration = Date.now() - startTime;

      // Log successful proxy
      this.logger.logBusinessEvent('api-gateway', 'proxy-success', 'SUCCESS', {
        correlationId,
        business: {
          domain: 'api-gateway',
          entity: 'proxy-response',
          action: 'proxy-success',
          outcome: 'SUCCESS',
          attributes: {
            serviceName,
            instanceId: instance.id,
            status: response.status,
            duration,
          },
        },
        performance: {
          startTime,
          duration,
        },
      });

      // Record metrics
      this.metrics.observe('proxy_request_duration', duration, {
        service: serviceName,
        method: request.method,
        status: response.status.toString(),
      });

      this.metrics.increment('proxy_requests_total', {
        service: serviceName,
        method: request.method,
        status: response.success ? 'success' : 'error',
      });

      return {
        ...response,
        correlationId,
        service: serviceName,
        duration,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.logBusinessEvent('api-gateway', 'proxy-error', 'FAILURE', {
        correlationId,
        business: {
          domain: 'api-gateway',
          entity: 'proxy-response',
          action: 'proxy-error',
          outcome: 'FAILURE',
          attributes: {
            serviceName,
            error: error.message,
            duration,
          },
        },
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        performance: {
          startTime,
          duration,
        },
      });

      // Record error metrics
      this.metrics.observe('proxy_request_duration', duration, {
        service: serviceName,
        method: request.method,
        status: 'error',
      });

      this.metrics.increment('proxy_requests_total', {
        service: serviceName,
        method: request.method,
        status: 'error',
      });

      this.metrics.increment('proxy_errors_total', {
        service: serviceName,
        error_type: error.name,
      });

      return {
        success: false,
        status: error.status || HttpStatus.SERVICE_UNAVAILABLE,
        error: error.message,
        correlationId,
        service: serviceName,
        duration,
        data: {
          error: 'Service request failed',
          message: error.message,
          service: serviceName,
          correlationId,
        },
      };
    }
  }

  /**
   * Proxy to User Service
   */
  async proxyToUserService(request: ProxyRequest): Promise<ProxyResponse> {
    return this.proxyToService('user-service', request);
  }

  /**
   * Proxy to Profile Analysis Service
   */
  async proxyToProfileService(request: ProxyRequest): Promise<ProxyResponse> {
    return this.proxyToService('profile-analysis-service', request);
  }

  /**
   * Proxy to NFT Generation Service
   */
  async proxyToNFTService(request: ProxyRequest): Promise<ProxyResponse> {
    return this.proxyToService('nft-generation-service', request);
  }

  /**
   * Proxy to Blockchain Service
   */
  async proxyToBlockchainService(request: ProxyRequest): Promise<ProxyResponse> {
    return this.proxyToService('blockchain-service', request);
  }

  /**
   * Proxy to Marketplace Service
   */
  async proxyToMarketplaceService(request: ProxyRequest): Promise<ProxyResponse> {
    return this.proxyToService('marketplace-service', request);
  }

  /**
   * Proxy to Project Service
   */
  async proxyToProjectService(request: ProxyRequest): Promise<ProxyResponse> {
    return this.proxyToService('project-service', request);
  }

  /**
   * Proxy to Analytics Service
   */
  async proxyToAnalyticsService(request: ProxyRequest): Promise<ProxyResponse> {
    return this.proxyToService('analytics-service', request);
  }

  /**
   * Proxy to Notification Service
   */
  async proxyToNotificationService(request: ProxyRequest): Promise<ProxyResponse> {
    return this.proxyToService('notification-service', request);
  }

  /**
   * Execute HTTP request to service instance
   */
  private async executeRequest(
    serviceUrl: string, 
    request: ProxyRequest, 
    correlationId: string
  ): Promise<ProxyResponse> {
    const fullUrl = `${serviceUrl}${request.url}`;
    
    // Prepare headers with correlation ID
    const headers = {
      ...request.headers,
      'x-correlation-id': correlationId,
      'x-forwarded-by': 'api-gateway',
    };
    
    // Remove problematic headers
    delete headers['host'];
    delete headers['content-length'];
    delete headers['connection'];

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const response = await fetch(fullUrl, {
        method: request.method,
        headers,
        body: request.body ? JSON.stringify(request.body) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const responseData = await response.text();
      let parsedData;
      
      try {
        parsedData = JSON.parse(responseData);
      } catch {
        parsedData = responseData;
      }

      return {
        success: response.ok,
        data: parsedData,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        correlationId,
        service: 'unknown',
        duration: 0, // Will be set by caller
      };

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new HttpException('Request timeout', HttpStatus.REQUEST_TIMEOUT);
      }
      
      throw new HttpException(
        `Service request failed: ${error.message}`,
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }

  /**
   * Generate correlation ID for request tracking
   */
  private generateCorrelationId(): string {
    return `gw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get proxy statistics
   */
  async getProxyStats(): Promise<any> {
    const services = this.serviceDiscovery.getAllServices();
    const circuits = this.circuitBreaker.getAllCircuitStats();
    
    const stats = {
      totalServices: services.size,
      healthyServices: 0,
      totalInstances: 0,
      healthyInstances: 0,
      circuitBreakers: {
        total: circuits.size,
        closed: 0,
        halfOpen: 0,
        open: 0,
      },
    };

    // Calculate service health
    for (const [serviceName, instances] of services.entries()) {
      stats.totalInstances += instances.length;
      const healthy = instances.filter(i => i.health === 'healthy').length;
      stats.healthyInstances += healthy;
      
      if (healthy > 0) {
        stats.healthyServices++;
      }
    }

    // Calculate circuit breaker states
    for (const [, circuitStats] of circuits.entries()) {
      switch (circuitStats.state) {
        case 'closed':
          stats.circuitBreakers.closed++;
          break;
        case 'half-open':
          stats.circuitBreakers.halfOpen++;
          break;
        case 'open':
          stats.circuitBreakers.open++;
          break;
      }
    }

    return stats;
  }
}
