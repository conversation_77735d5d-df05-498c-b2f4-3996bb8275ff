'use client'

import React, { useState, Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon, CurrencyDollarIcon, TagIcon, ClockIcon } from '@heroicons/react/24/outline'
import { NFT } from '@/types/nft.types'
import { CreateListingRequest, ListingType, PaymentMethod } from '@/types/marketplace.types'
import { useCreateListing, useNFTPriceEstimate } from '@/hooks/useMarketplace'

interface CreateListingModalProps {
  nft: NFT | null
  isOpen: boolean
  onClose: () => void
  onSuccess?: (listing: any) => void
}

export default function CreateListingModal({
  nft,
  isOpen,
  onClose,
  onSuccess
}: CreateListingModalProps) {
  const [formData, setFormData] = useState<Partial<CreateListingRequest>>({
    listingType: ListingType.FIXED_PRICE,
    currency: PaymentMethod.ETH,
    acceptOffers: true,
    title: '',
    description: '',
    tags: []
  })
  const [tagInput, setTagInput] = useState('')

  const createListingMutation = useCreateListing()
  const { data: priceEstimate } = useNFTPriceEstimate(nft?.id || '')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!nft || !formData.price || !formData.title) {
      return
    }

    try {
      const listingData: CreateListingRequest = {
        nftId: nft.id,
        listingType: formData.listingType!,
        price: formData.price,
        currency: formData.currency!,
        title: formData.title,
        description: formData.description,
        tags: formData.tags,
        acceptOffers: formData.acceptOffers,
        minOfferAmount: formData.minOfferAmount,
        auctionEndTime: formData.auctionEndTime,
        startingPrice: formData.startingPrice,
        reservePrice: formData.reservePrice,
        buyNowPrice: formData.buyNowPrice
      }

      const result = await createListingMutation.mutateAsync(listingData)
      onSuccess?.(result)
      onClose()
      resetForm()
    } catch (error) {
      console.error('Failed to create listing:', error)
    }
  }

  const resetForm = () => {
    setFormData({
      listingType: ListingType.FIXED_PRICE,
      currency: PaymentMethod.ETH,
      acceptOffers: true,
      title: '',
      description: '',
      tags: []
    })
    setTagInput('')
  }

  const addTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }))
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  if (!nft) return null

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <TagIcon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <Dialog.Title as="h3" className="text-lg font-semibold text-gray-900">
                        List NFT for Sale
                      </Dialog.Title>
                      <p className="text-sm text-gray-600">{nft.name}</p>
                    </div>
                  </div>
                  <button
                    onClick={handleClose}
                    className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="p-6 space-y-6">
                  {/* NFT Preview */}
                  <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <img
                      src={nft.imageUrl || '/api/placeholder/80/80'}
                      alt={nft.name}
                      className="w-20 h-20 rounded-lg object-cover"
                    />
                    <div>
                      <h4 className="font-semibold text-gray-900">{nft.name}</h4>
                      <p className="text-sm text-gray-600">@{nft.twitterHandle}</p>
                      <p className="text-sm text-gray-500">Score: {nft.currentScore} • {nft.rarity}</p>
                    </div>
                  </div>

                  {/* Listing Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Listing Type
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      <button
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, listingType: ListingType.FIXED_PRICE }))}
                        className={`p-3 border rounded-lg text-left transition-colors ${
                          formData.listingType === ListingType.FIXED_PRICE
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <div className="font-medium">Fixed Price</div>
                        <div className="text-sm text-gray-500">Sell at a set price</div>
                      </button>
                      <button
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, listingType: ListingType.AUCTION }))}
                        className={`p-3 border rounded-lg text-left transition-colors ${
                          formData.listingType === ListingType.AUCTION
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <div className="font-medium">Auction</div>
                        <div className="text-sm text-gray-500">Sell to highest bidder</div>
                      </button>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {formData.listingType === ListingType.AUCTION ? 'Starting Price' : 'Price'}
                      </label>
                      <div className="relative">
                        <CurrencyDollarIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="number"
                          step="0.001"
                          min="0"
                          value={formData.price || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) }))}
                          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0.00"
                          required
                        />
                      </div>
                      {priceEstimate && (
                        <p className="text-xs text-gray-500 mt-1">
                          Estimated: {priceEstimate.estimatedPrice} {priceEstimate.currency}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Currency
                      </label>
                      <select
                        value={formData.currency}
                        onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value as PaymentMethod }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      >
                        {Object.values(PaymentMethod).map(currency => (
                          <option key={currency} value={currency}>{currency}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Auction-specific fields */}
                  {formData.listingType === ListingType.AUCTION && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Reserve Price (Optional)
                        </label>
                        <input
                          type="number"
                          step="0.001"
                          min="0"
                          value={formData.reservePrice || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, reservePrice: parseFloat(e.target.value) }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Auction Duration
                        </label>
                        <select
                          onChange={(e) => {
                            const hours = parseInt(e.target.value)
                            const endTime = new Date(Date.now() + hours * 60 * 60 * 1000).toISOString()
                            setFormData(prev => ({ ...prev, auctionEndTime: endTime }))
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">Select duration</option>
                          <option value="24">24 hours</option>
                          <option value="72">3 days</option>
                          <option value="168">7 days</option>
                          <option value="336">14 days</option>
                        </select>
                      </div>
                    </div>
                  )}

                  {/* Title and Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Title
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter listing title"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description (Optional)
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Describe your NFT..."
                    />
                  </div>

                  {/* Tags */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tags (Optional)
                    </label>
                    <div className="flex space-x-2 mb-2">
                      <input
                        type="text"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Add a tag"
                      />
                      <button
                        type="button"
                        onClick={addTag}
                        className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                      >
                        Add
                      </button>
                    </div>
                    {formData.tags && formData.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {formData.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() => removeTag(tag)}
                              className="ml-1 text-blue-600 hover:text-blue-800"
                            >
                              <XMarkIcon className="h-3 w-3" />
                            </button>
                          </span>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Offers */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Accept Offers</label>
                      <p className="text-xs text-gray-500">Allow buyers to make offers below your asking price</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.acceptOffers}
                        onChange={(e) => setFormData(prev => ({ ...prev, acceptOffers: e.target.checked }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {/* Minimum Offer Amount */}
                  {formData.acceptOffers && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Minimum Offer Amount (Optional)
                      </label>
                      <input
                        type="number"
                        step="0.001"
                        min="0"
                        value={formData.minOfferAmount || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, minOfferAmount: parseFloat(e.target.value) }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="0.00"
                      />
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={handleClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={createListingMutation.isPending || !formData.price || !formData.title}
                      className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {createListingMutation.isPending ? 'Creating...' : 'Create Listing'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
