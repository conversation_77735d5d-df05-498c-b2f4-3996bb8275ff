import { Injectable } from '@nestjs/common';
import { ServiceLoggerService, MetricsService } from '../../shared';
import { ServiceInstance } from '../service-discovery/service-discovery.service';

export interface LoadBalancerConfig {
  strategy: 'round-robin' | 'weighted' | 'least-connections' | 'random';
  healthCheckEnabled: boolean;
  stickySession: boolean;
}

export interface ConnectionStats {
  instanceId: string;
  activeConnections: number;
  totalConnections: number;
  lastUsed: Date;
}

/**
 * Load Balancer Service
 * 
 * Provides intelligent load balancing across service instances
 * with health-aware routing and connection tracking.
 * 
 * @example
 * ```typescript
 * const instance = await loadBalancer.selectInstance(instances, 'weighted');
 * ```
 */
@Injectable()
export class LoadBalancerService {
  private connectionStats = new Map<string, ConnectionStats>();
  private roundRobinCounters = new Map<string, number>();

  constructor(
    private readonly logger: ServiceLoggerService,
    private readonly metrics: MetricsService
  ) {}

  /**
   * Select the best instance using specified strategy
   */
  async selectInstance(
    instances: ServiceInstance[],
    strategy: LoadBalancerConfig['strategy'] = 'round-robin',
    serviceName?: string
  ): Promise<ServiceInstance | null> {
    if (instances.length === 0) {
      return null;
    }

    // Filter healthy instances
    const healthyInstances = instances.filter(instance => instance.health === 'healthy');
    
    if (healthyInstances.length === 0) {
      this.logger.warn(`No healthy instances available for load balancing`, {
        totalInstances: instances.length,
        serviceName,
      });
      return null;
    }

    let selectedInstance: ServiceInstance;

    switch (strategy) {
      case 'weighted':
        selectedInstance = this.selectWeightedInstance(healthyInstances);
        break;
      case 'least-connections':
        selectedInstance = this.selectLeastConnectionsInstance(healthyInstances);
        break;
      case 'random':
        selectedInstance = this.selectRandomInstance(healthyInstances);
        break;
      case 'round-robin':
      default:
        selectedInstance = this.selectRoundRobinInstance(healthyInstances, serviceName || 'default');
        break;
    }

    // Update connection stats
    this.updateConnectionStats(selectedInstance.id, 'selected');

    // Record metrics
    this.metrics.increment('load_balancer_selections_total', {
      strategy,
      service: serviceName || 'unknown',
      instance_id: selectedInstance.id,
    });

    this.logger.debug(`Load balancer selected instance`, {
      strategy,
      serviceName,
      instanceId: selectedInstance.id,
      instanceUrl: selectedInstance.url,
    });

    return selectedInstance;
  }

  /**
   * Record connection start for an instance
   */
  recordConnectionStart(instanceId: string): void {
    this.updateConnectionStats(instanceId, 'start');
    
    this.metrics.increment('load_balancer_connections_total', {
      instance_id: instanceId,
      action: 'start',
    });
  }

  /**
   * Record connection end for an instance
   */
  recordConnectionEnd(instanceId: string): void {
    this.updateConnectionStats(instanceId, 'end');
    
    this.metrics.increment('load_balancer_connections_total', {
      instance_id: instanceId,
      action: 'end',
    });
  }

  /**
   * Get connection statistics for all instances
   */
  getConnectionStats(): Map<string, ConnectionStats> {
    return new Map(this.connectionStats);
  }

  /**
   * Get connection statistics for a specific instance
   */
  getInstanceConnectionStats(instanceId: string): ConnectionStats | undefined {
    return this.connectionStats.get(instanceId);
  }

  /**
   * Reset connection statistics
   */
  resetConnectionStats(instanceId?: string): void {
    if (instanceId) {
      this.connectionStats.delete(instanceId);
    } else {
      this.connectionStats.clear();
    }

    this.logger.logBusinessEvent('load-balancer', 'reset-stats', 'SUCCESS', {
      business: {
        domain: 'load-balancer',
        entity: 'connection-stats',
        action: 'reset-stats',
        outcome: 'SUCCESS',
        attributes: {
          instanceId: instanceId || 'all',
        },
      },
    });
  }

  /**
   * Get load balancer health status
   */
  getHealthStatus(): any {
    const stats = Array.from(this.connectionStats.values());
    const totalConnections = stats.reduce((sum, stat) => sum + stat.activeConnections, 0);
    const totalInstances = stats.length;

    return {
      status: 'healthy',
      totalInstances,
      totalActiveConnections: totalConnections,
      averageConnectionsPerInstance: totalInstances > 0 ? 
        (totalConnections / totalInstances).toFixed(2) : '0',
      connectionStats: stats,
      timestamp: new Date().toISOString(),
    };
  }

  private selectRoundRobinInstance(instances: ServiceInstance[], serviceName: string): ServiceInstance {
    const counter = this.roundRobinCounters.get(serviceName) || 0;
    const selectedIndex = counter % instances.length;
    
    this.roundRobinCounters.set(serviceName, counter + 1);
    
    return instances[selectedIndex];
  }

  private selectWeightedInstance(instances: ServiceInstance[]): ServiceInstance {
    const totalWeight = instances.reduce((sum, instance) => sum + instance.metadata.weight, 0);
    
    if (totalWeight === 0) {
      return instances[0];
    }

    let random = Math.random() * totalWeight;
    
    for (const instance of instances) {
      random -= instance.metadata.weight;
      if (random <= 0) {
        return instance;
      }
    }
    
    return instances[instances.length - 1]; // Fallback
  }

  private selectLeastConnectionsInstance(instances: ServiceInstance[]): ServiceInstance {
    let selectedInstance = instances[0];
    let minConnections = this.getActiveConnections(selectedInstance.id);

    for (const instance of instances.slice(1)) {
      const connections = this.getActiveConnections(instance.id);
      if (connections < minConnections) {
        minConnections = connections;
        selectedInstance = instance;
      }
    }

    return selectedInstance;
  }

  private selectRandomInstance(instances: ServiceInstance[]): ServiceInstance {
    const randomIndex = Math.floor(Math.random() * instances.length);
    return instances[randomIndex];
  }

  private getActiveConnections(instanceId: string): number {
    const stats = this.connectionStats.get(instanceId);
    return stats ? stats.activeConnections : 0;
  }

  private updateConnectionStats(instanceId: string, action: 'selected' | 'start' | 'end'): void {
    let stats = this.connectionStats.get(instanceId);
    
    if (!stats) {
      stats = {
        instanceId,
        activeConnections: 0,
        totalConnections: 0,
        lastUsed: new Date(),
      };
      this.connectionStats.set(instanceId, stats);
    }

    switch (action) {
      case 'selected':
        stats.lastUsed = new Date();
        break;
      case 'start':
        stats.activeConnections++;
        stats.totalConnections++;
        stats.lastUsed = new Date();
        break;
      case 'end':
        stats.activeConnections = Math.max(0, stats.activeConnections - 1);
        break;
    }

    // Update metrics
    this.metrics.gauge('load_balancer_active_connections', stats.activeConnections, {
      instance_id: instanceId,
    });

    this.metrics.gauge('load_balancer_total_connections', stats.totalConnections, {
      instance_id: instanceId,
    });
  }
}
