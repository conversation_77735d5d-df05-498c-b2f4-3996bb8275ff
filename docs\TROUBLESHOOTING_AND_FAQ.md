# 🔧 Troubleshooting and FAQ Guide

**Common Issues and Solutions for Social NFT Platform Enterprise Standards**

## 🎯 Quick Reference

### **Emergency Fixes**
```bash
# Fix all standardization issues
npm run fix:all

# Validate current compliance
npm run validate:standards

# Reset to clean state
npm run reset:standards
```

---

## 🚨 CRITICAL ISSUES

### **1. Environment Variables Not Loading**

#### **Problem:**
```
Error: Configuration validation failed
Cannot read property 'SERVICE_NAME' of undefined
```

#### **Solution:**
```bash
# 1. Check .env file exists
ls -la .env

# 2. Validate environment variables
npm run env:validate

# 3. Copy from example if missing
cp .env.example .env

# 4. Restart application
npm run start:dev
```

#### **Prevention:**
```typescript
// Always validate in configuration
export class AppConfig {
  @IsString()
  @IsNotEmpty()
  serviceName: string; // Will fail if missing
}
```

### **2. Authentication Guards Not Working**

#### **Problem:**
```
Error: Cannot resolve dependency JwtAuthGuard
Unauthorized access to protected endpoint
```

#### **Solution:**
```typescript
// 1. Ensure proper imports
import { JwtAuthGuard } from '../../../shared/auth/guards/jwt-auth.guard';

// 2. Check module imports
@Module({
  imports: [ServiceAuthModule], // Must be imported
  // ...
})

// 3. Verify JWT secret
JWT_SECRET=your-super-secret-jwt-key
```

### **3. Database Connection Failures**

#### **Problem:**
```
Error: Can't reach database server
PrismaClientInitializationError
```

#### **Solution:**
```bash
# 1. Check database URL
echo $DATABASE_URL

# 2. Test connection
npm run db:test

# 3. Reset connection
npm run db:reset

# 4. Check Prisma service
npm run prisma:status
```

---

## ❓ FREQUENTLY ASKED QUESTIONS

### **Q1: How do I add a new environment variable?**

**A:** Follow these steps:
1. Add to platform `.env` file
2. Add to service `.env.example`
3. Update configuration schema
4. Validate and restart

```typescript
// Add to configuration schema
export class AppConfig {
  @IsString()
  @IsOptional()
  newVariable?: string;
}
```

### **Q2: How do I create a new service endpoint?**

**A:** Use our standardized pattern:
```typescript
@Controller('entities')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class EntityController {
  @Get()
  @RequirePermissions(Permission.ENTITY_READ)
  async findAll(@Query() query: any) {
    const result = await this.entityService.findAll(query);
    return this.responseService.paginated(result.data, result.pagination);
  }
}
```

### **Q3: How do I add custom logging?**

**A:** Use ServiceLoggerService:
```typescript
// Business event
this.serviceLogger.logBusinessEvent(
  'user',
  'registration',
  BusinessOutcome.SUCCESS,
  { userId: user.id }
);

// Security event
this.serviceLogger.logSecurityEvent(
  SecurityEventType.AUTHENTICATION,
  SecuritySeverity.MEDIUM,
  SecurityOutcome.FAILURE
);
```

### **Q4: How do I implement custom repository methods?**

**A:** Extend BaseRepository:
```typescript
@Injectable()
export class UserRepository extends BaseRepository<User, CreateUserDto, UpdateUserDto> {
  // Custom methods
  async findByEmail(email: string): Promise<User | null> {
    return this.findOne({ where: { email } });
  }
  
  async findActiveUsers(): Promise<User[]> {
    return this.findMany({ where: { active: true } });
  }
}
```

### **Q5: How do I handle errors properly?**

**A:** Use ResponseService:
```typescript
try {
  const result = await this.service.operation();
  return this.responseService.success(result);
} catch (error) {
  if (error instanceof ValidationError) {
    return this.responseService.validationError(error.details);
  }
  return this.responseService.error('Operation failed', ErrorCode.INTERNAL_SERVER_ERROR);
}
```

---

## 🔍 DEBUGGING GUIDES

### **Configuration Issues**

#### **Debug Steps:**
```bash
# 1. Check configuration loading
npm run config:debug

# 2. Validate schemas
npm run config:validate

# 3. Check environment
npm run env:check

# 4. Test configuration service
npm run test:config
```

#### **Common Fixes:**
```typescript
// Missing validation
@IsString()
@IsNotEmpty()
serviceName: string;

// Wrong type conversion
@Transform(({ value }) => parseInt(value))
port: number;

// Missing optional decorator
@IsOptional()
optionalField?: string;
```

### **Authentication Issues**

#### **Debug Steps:**
```bash
# 1. Check JWT token
npm run auth:debug-token

# 2. Validate permissions
npm run auth:check-permissions

# 3. Test guards
npm run test:auth-guards
```

#### **Common Fixes:**
```typescript
// Missing guards
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)

// Wrong permission
@RequirePermissions(Permission.USER_READ) // Not USER_WRITE

// Missing role
@RequireRoles(Role.USER, Role.ADMIN)
```

### **Response Format Issues**

#### **Debug Steps:**
```bash
# 1. Check response interceptor
npm run response:debug

# 2. Validate response format
npm run response:validate

# 3. Test response service
npm run test:responses
```

#### **Common Fixes:**
```typescript
// Missing response service
return this.responseService.success(data);

// Wrong response method
return this.responseService.created(data); // Not success

// Missing correlation ID
// Automatically handled by interceptor
```

### **Logging Issues**

#### **Debug Steps:**
```bash
# 1. Check log output
npm run logs:debug

# 2. Validate log format
npm run logs:validate

# 3. Test structured logging
npm run test:logging
```

#### **Common Fixes:**
```typescript
// Missing structured logger
constructor(private serviceLogger: ServiceLoggerService) {}

// Wrong logging method
this.serviceLogger.logBusinessEvent(...); // Not console.log

// Missing context
this.serviceLogger.logOperationStart('operation', { metadata });
```

### **Database Issues**

#### **Debug Steps:**
```bash
# 1. Check database connection
npm run db:health

# 2. Validate Prisma schema
npm run prisma:validate

# 3. Test repository
npm run test:repository
```

#### **Common Fixes:**
```typescript
// Missing repository pattern
constructor(private userRepository: UserRepository) {}

// Direct Prisma access
return this.userRepository.findById(id); // Not this.prisma.user.findUnique

// Missing transaction
await this.repository.withTransaction(async (repo) => {
  // Multi-operation logic
});
```

---

## 🛠️ DIAGNOSTIC TOOLS

### **Automated Diagnostics**
```bash
# Run complete diagnostic
npm run diagnose:all

# Check specific phase
npm run diagnose:env
npm run diagnose:config
npm run diagnose:auth
npm run diagnose:responses
npm run diagnose:logging
npm run diagnose:data
```

### **Health Checks**
```bash
# Service health
curl http://localhost:3001/health

# Data health
curl http://localhost:3001/health/data

# Authentication health
curl http://localhost:3001/health/auth
```

### **Validation Scripts**
```bash
# Validate all patterns
npm run validate:patterns

# Check compliance
npm run check:compliance

# Generate compliance report
npm run report:compliance
```

---

## 🔧 COMMON FIXES

### **1. Import Resolution Issues**

#### **Problem:**
```
Cannot resolve module '../../../shared/auth/guards/jwt-auth.guard'
```

#### **Solution:**
```typescript
// Use absolute imports from project root
import { JwtAuthGuard } from 'shared/auth/guards/jwt-auth.guard';

// Or check tsconfig paths
{
  "compilerOptions": {
    "paths": {
      "shared/*": ["shared/*"]
    }
  }
}
```

### **2. Dependency Injection Issues**

#### **Problem:**
```
Nest can't resolve dependencies of the UserService
```

#### **Solution:**
```typescript
// Ensure proper module imports
@Module({
  imports: [
    ServiceDataModule,    // For repositories
    ServiceLoggingModule, // For logging
    ServiceAuthModule,    // For authentication
  ],
  providers: [UserService],
})
```

### **3. Validation Errors**

#### **Problem:**
```
ValidationError: serviceName should not be empty
```

#### **Solution:**
```typescript
// Check environment variable
SERVICE_NAME=user-service

// Verify configuration class
export class AppConfig {
  @IsString()
  @IsNotEmpty()
  serviceName: string;
}
```

### **4. Response Format Issues**

#### **Problem:**
```
Response missing correlation ID
```

#### **Solution:**
```typescript
// Ensure response interceptor is applied
@Module({
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor,
    },
  ],
})
```

### **5. Database Connection Issues**

#### **Problem:**
```
PrismaClientInitializationError: Can't reach database
```

#### **Solution:**
```bash
# Check database URL format
DATABASE_URL="postgresql://user:password@localhost:5432/database"

# Test connection
npm run db:test

# Reset Prisma client
npm run prisma:generate
```

---

## 📊 PERFORMANCE TROUBLESHOOTING

### **Slow Database Queries**

#### **Diagnosis:**
```bash
# Check slow query logs
npm run logs:slow-queries

# Analyze query performance
npm run db:analyze
```

#### **Solutions:**
```typescript
// Add database indexes
// Check Prisma schema for missing indexes

// Optimize queries
const users = await this.userRepository.findMany({
  where: { active: true },
  select: { id: true, name: true }, // Only select needed fields
});

// Use pagination
const result = await this.userRepository.paginate(criteria, { page: 1, limit: 20 });
```

### **Memory Issues**

#### **Diagnosis:**
```bash
# Check memory usage
npm run monitor:memory

# Profile application
npm run profile:memory
```

#### **Solutions:**
```typescript
// Implement proper pagination
// Use streaming for large datasets
// Clear unused references
// Optimize caching strategies
```

### **High CPU Usage**

#### **Diagnosis:**
```bash
# Check CPU usage
npm run monitor:cpu

# Profile CPU usage
npm run profile:cpu
```

#### **Solutions:**
```typescript
// Optimize algorithms
// Use caching for expensive operations
// Implement rate limiting
// Use background jobs for heavy tasks
```

---

## 🚨 EMERGENCY PROCEDURES

### **Service Down**
1. Check health endpoints
2. Review error logs
3. Verify database connection
4. Check environment variables
5. Restart service if needed

### **Database Issues**
1. Check database connectivity
2. Review connection pool status
3. Check for long-running queries
4. Verify database credentials
5. Restart database if needed

### **Authentication Failures**
1. Verify JWT secret
2. Check token expiration
3. Review permission configuration
4. Validate user roles
5. Check authentication service

### **Performance Degradation**
1. Check resource usage
2. Review slow query logs
3. Monitor cache hit rates
4. Check external service status
5. Scale resources if needed

---

## 📞 SUPPORT CONTACTS

### **Technical Issues**
- **Lead Developer**: For pattern and architecture questions
- **DevOps Team**: For deployment and infrastructure issues
- **Database Team**: For database-related problems

### **Emergency Contacts**
- **On-Call Engineer**: For critical production issues
- **Security Team**: For security-related incidents
- **Management**: For escalation of critical issues

---

## 📚 ADDITIONAL RESOURCES

### **Documentation**
- [Enterprise Standardization Guide](./ENTERPRISE_STANDARDIZATION_GUIDE.md)
- [Development Rules](./DEVELOPMENT_RULES_AND_ENFORCEMENT.md)
- [AI Agent Configuration](./AI_AGENT_CONFIGURATION.md)

### **Tools and Scripts**
- [Diagnostic Scripts](../scripts/diagnostics/)
- [Validation Tools](../tools/validation/)
- [Monitoring Dashboards](../monitoring/)

### **Training Materials**
- [Developer Onboarding](../training/onboarding.md)
- [Best Practices Guide](../training/best-practices.md)
- [Code Examples](../examples/)

---

**Remember: When in doubt, follow the established patterns. If patterns don't cover your use case, ask for guidance before implementing custom solutions.**
