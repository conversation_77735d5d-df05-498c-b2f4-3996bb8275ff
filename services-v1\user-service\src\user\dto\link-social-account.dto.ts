import { IsString, IsOptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum SocialPlatform {
  TWITTER = 'twitter',
  FARCASTER = 'farcaster',
  LENS = 'lens',
}

export class LinkSocialAccountDto {
  @ApiProperty({
    description: 'Social platform to link',
    enum: SocialPlatform,
    example: SocialPlatform.TWITTER,
  })
  @IsEnum(SocialPlatform, { message: 'Platform must be one of: twitter, farcaster, lens' })
  platform: SocialPlatform;

  @ApiProperty({
    description: 'Username on the social platform',
    example: 'johndoe',
    maxLength: 100,
  })
  @IsString()
  @MaxLength(100, { message: 'Username must not exceed 100 characters' })
  username: string;

  @ApiPropertyOptional({
    description: 'User ID on the social platform',
    example: '*********',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'User ID must not exceed 100 characters' })
  platformUserId?: string;

  @ApiPropertyOptional({
    description: 'Access token for API access',
  })
  @IsOptional()
  @IsString()
  accessToken?: string;

  @ApiPropertyOptional({
    description: 'Refresh token for token renewal',
  })
  @IsOptional()
  @IsString()
  refreshToken?: string;

  @ApiPropertyOptional({
    description: 'Whether the account is verified on the platform',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isVerified?: boolean;

  @ApiPropertyOptional({
    description: 'Additional metadata from the platform',
    example: { followerCount: 1000, followingCount: 500 },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UnlinkSocialAccountDto {
  @ApiProperty({
    description: 'Social platform to unlink',
    enum: SocialPlatform,
    example: SocialPlatform.TWITTER,
  })
  @IsEnum(SocialPlatform, { message: 'Platform must be one of: twitter, farcaster, lens' })
  platform: SocialPlatform;
}

export class UpdateSocialAccountDto {
  @ApiPropertyOptional({
    description: 'Updated username on the platform',
    example: 'johndoe_updated',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Username must not exceed 100 characters' })
  username?: string;

  @ApiPropertyOptional({
    description: 'Updated access token',
  })
  @IsOptional()
  @IsString()
  accessToken?: string;

  @ApiPropertyOptional({
    description: 'Updated refresh token',
  })
  @IsOptional()
  @IsString()
  refreshToken?: string;

  @ApiPropertyOptional({
    description: 'Updated verification status',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isVerified?: boolean;

  @ApiPropertyOptional({
    description: 'Updated metadata',
    example: { followerCount: 1200, followingCount: 600 },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}
