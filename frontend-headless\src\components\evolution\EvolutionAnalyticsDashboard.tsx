'use client'

import React, { useState } from 'react'
import {
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  SparklesIcon,
  ClockIcon,
  TrophyIcon,
  ArrowPathIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { useEvolutionAnalytics, useEvolutionStats, useEvolutionInsights } from '@/hooks/useEvolution'
import { EvolutionImpact } from '@/types/evolution.types'
import { NFTRarity } from '@/types/nft.types'

interface EvolutionAnalyticsDashboardProps {
  nftId: string
  timeframe?: '7d' | '30d' | '90d' | '1y'
  className?: string
}

export default function EvolutionAnalyticsDashboard({
  nftId,
  timeframe = '30d',
  className = ''
}: EvolutionAnalyticsDashboardProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe)

  const { data: analytics, isLoading: analyticsLoading, refetch: refetchAnalytics } = useEvolutionAnalytics(nftId, selectedTimeframe)
  const { data: stats, isLoading: statsLoading } = useEvolutionStats(nftId)
  const { data: insights, isLoading: insightsLoading } = useEvolutionInsights(nftId)

  const isLoading = analyticsLoading || statsLoading || insightsLoading

  const getRarityColor = (rarity: NFTRarity) => {
    switch (rarity) {
      case NFTRarity.MYTHIC: return 'text-purple-600 bg-purple-100'
      case NFTRarity.LEGENDARY: return 'text-yellow-600 bg-yellow-100'
      case NFTRarity.EPIC: return 'text-purple-500 bg-purple-100'
      case NFTRarity.RARE: return 'text-blue-500 bg-blue-100'
      case NFTRarity.COMMON: return 'text-gray-500 bg-gray-100'
      default: return 'text-gray-500 bg-gray-100'
    }
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'positive': return <TrendingUpIcon className="h-5 w-5 text-green-500" />
      case 'negative': return <TrendingDownIcon className="h-5 w-5 text-red-500" />
      default: return <InformationCircleIcon className="h-5 w-5 text-blue-500" />
    }
  }

  const getImpactColor = (impact: EvolutionImpact) => {
    switch (impact) {
      case EvolutionImpact.LEGENDARY: return 'text-purple-600 bg-purple-100'
      case EvolutionImpact.MAJOR: return 'text-red-600 bg-red-100'
      case EvolutionImpact.MODERATE: return 'text-yellow-600 bg-yellow-100'
      case EvolutionImpact.MINOR: return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <ChartBarIcon className="h-5 w-5 mr-2 text-gray-500" />
              Evolution Analytics
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              Comprehensive analysis of NFT evolution patterns and performance
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value as '7d' | '30d' | '90d' | '1y')}
              className="text-sm border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
              <option value="1y">Last Year</option>
            </select>
            
            <button
              onClick={() => refetchAnalytics()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh analytics"
            >
              <ArrowPathIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
            <div className="flex items-center">
              <SparklesIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-900">Total Evolutions</p>
                <p className="text-2xl font-bold text-blue-600">
                  {analytics?.totalEvolutions || stats?.totalEvents || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
            <div className="flex items-center">
              <TrendingUpIcon className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-900">Evolution Velocity</p>
                <p className="text-2xl font-bold text-green-600">
                  {analytics?.evolutionVelocity?.toFixed(1) || '0.0'}/mo
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
            <div className="flex items-center">
              <ChartBarIcon className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-purple-900">Avg Score Gain</p>
                <p className="text-2xl font-bold text-purple-600">
                  +{analytics?.averageScoreGain?.toFixed(1) || '0.0'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Rarity Progression */}
        {analytics?.rarityProgression && analytics.rarityProgression.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <TrophyIcon className="h-5 w-5 mr-2 text-gray-500" />
              Rarity Progression
            </h4>
            <div className="space-y-3">
              {analytics.rarityProgression.map((progression, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRarityColor(progression.rarity)}`}>
                      {progression.rarity.charAt(0).toUpperCase() + progression.rarity.slice(1)}
                    </span>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Achieved on {new Date(progression.achievedAt).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-gray-500">
                        Score: {progression.scoreAtAchievement} • {progression.daysToAchieve} days to achieve
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{progression.evolutionCount} evolutions</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Evolution Patterns */}
        {analytics?.evolutionPatterns && analytics.evolutionPatterns.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Evolution Patterns</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {analytics.evolutionPatterns.map((pattern, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-gray-900">{pattern.pattern}</h5>
                    <span className="text-sm text-gray-500">{pattern.frequency}x</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{pattern.description}</p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Avg Impact: {pattern.averageImpact.toFixed(1)}</span>
                    <span>{pattern.examples.length} examples</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Milestones */}
        {analytics?.milestones && analytics.milestones.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Recent Milestones</h4>
            <div className="space-y-2">
              {analytics.milestones.slice(0, 5).map((milestone, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-center space-x-3">
                    <TrophyIcon className="h-5 w-5 text-yellow-600" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{milestone.name}</p>
                      <p className="text-xs text-gray-600">{milestone.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {new Date(milestone.achievedAt).toLocaleDateString()}
                    </p>
                    {milestone.badge && (
                      <span className="inline-block mt-1 px-2 py-1 bg-yellow-200 text-yellow-800 text-xs rounded-full">
                        {milestone.badge}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Insights */}
        {insights?.insights && insights.insights.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Key Insights</h4>
            <div className="space-y-3">
              {insights.insights.slice(0, 3).map((insight, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  {getInsightIcon(insight.type)}
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{insight.title}</p>
                    <p className="text-sm text-gray-600 mt-1">{insight.description}</p>
                    <span className={`inline-block mt-2 px-2 py-1 rounded-full text-xs font-medium ${getImpactColor(insight.impact)}`}>
                      {insight.impact} impact
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recommendations */}
        {insights?.recommendations && insights.recommendations.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Recommendations</h4>
            <div className="space-y-3">
              {insights.recommendations.slice(0, 3).map((recommendation, index) => (
                <div key={index} className="border-l-4 border-blue-400 bg-blue-50 p-4">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm font-medium text-blue-900">{recommendation.action}</p>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      recommendation.priority === 'high' ? 'bg-red-100 text-red-800' :
                      recommendation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {recommendation.priority} priority
                    </span>
                  </div>
                  <p className="text-sm text-blue-800 mb-2">{recommendation.description}</p>
                  <div className="flex items-center justify-between text-xs text-blue-600">
                    <span>Expected: {recommendation.expectedImpact}</span>
                    <span>Timeframe: {recommendation.timeframe}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Comparative Ranking */}
        {analytics?.comparativeRanking && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Performance Ranking</h4>
            <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-indigo-600">#{analytics.comparativeRanking.overallRank}</p>
                  <p className="text-sm text-gray-600">Overall Rank</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-600">{analytics.comparativeRanking.percentile}%</p>
                  <p className="text-sm text-gray-600">Percentile</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-600">#{analytics.comparativeRanking.evolutionSpeedRank}</p>
                  <p className="text-sm text-gray-600">Speed Rank</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">#{analytics.comparativeRanking.scoreGrowthRank}</p>
                  <p className="text-sm text-gray-600">Growth Rank</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
