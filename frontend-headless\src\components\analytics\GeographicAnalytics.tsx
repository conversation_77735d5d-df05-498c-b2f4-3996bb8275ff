'use client'

import React from 'react'
import {
  GlobeAltIcon,
  MapIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import { useGeographicAnalytics, useMarketAnalysis } from '@/hooks/useAnalytics'

interface GeographicAnalyticsProps {
  campaignId: string
  className?: string
}

export default function GeographicAnalytics({
  campaignId,
  className = ''
}: GeographicAnalyticsProps) {
  const { data: geographic, isLoading: geoLoading } = useGeographicAnalytics(campaignId)
  const { data: market, isLoading: marketLoading } = useMarketAnalysis(campaignId)

  if (geoLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="h-64 bg-gray-200 rounded mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h2 className="text-lg font-medium text-gray-900">Geographic Analytics</h2>
        <p className="text-sm text-gray-600">Geographic distribution and market analysis</p>
      </div>

      {/* World Map Placeholder */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Global Participation Map</h3>
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <MapIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">Interactive world map</p>
            <p className="text-xs text-gray-500">Geographic visualization would be implemented here</p>
          </div>
        </div>
      </div>

      {/* Geographic Breakdown */}
      {geographic && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Top Countries */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Countries</h3>
            <div className="space-y-3">
              {geographic.participantsByCountry.slice(0, 10).map((country, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{country.country}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${country.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-12 text-right">
                      {country.participants.toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Regions */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Regions</h3>
            <div className="space-y-3">
              {geographic.participantsByRegion.slice(0, 10).map((region, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{region.region}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${region.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-12 text-right">
                      {region.participants.toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Conversion by Geography */}
      {geographic && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Conversion Rates by Country</h3>
          <div className="space-y-3">
            {geographic.conversionByCountry.slice(0, 10).map((country, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-gray-900">{country.country}</div>
                  <div className="text-xs text-gray-600">
                    {country.participants.toLocaleString()} participants
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {(country.conversionRate * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {country.conversions} conversions
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Market Opportunities */}
      {market && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Market Opportunities</h3>
          <div className="space-y-4">
            {market.growthOpportunities.slice(0, 5).map((opportunity, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900">{opportunity.market}</h4>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    opportunity.effort === 'low' ? 'bg-green-100 text-green-700' :
                    opportunity.effort === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  }`}>
                    {opportunity.effort} effort
                  </span>
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  Opportunity Score: {opportunity.opportunity.toFixed(1)}/10
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  Timeline: {opportunity.timeline}
                </div>
                <p className="text-sm text-gray-700">{opportunity.description}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
