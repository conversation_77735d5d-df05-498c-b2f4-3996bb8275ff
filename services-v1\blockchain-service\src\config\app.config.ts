import { IsString, <PERSON>N<PERSON><PERSON>, IsEnum, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

enum Environment {
  DEVELOPMENT = 'development',
  PRODUCTION = 'production',
  TEST = 'test',
}

enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

enum LogFormat {
  JSON = 'json',
  SIMPLE = 'simple',
  COMBINED = 'combined',
}

export class AppConfig {
  @IsString()
  SERVICE_NAME: string = 'blockchain-service';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  SERVICE_PORT: number = 3004;

  @IsEnum(Environment)
  NODE_ENV: Environment = Environment.DEVELOPMENT;

  @IsString()
  DATABASE_URL: string;

  @IsString()
  JWT_SECRET: string;

  @IsString()
  JWT_EXPIRES_IN: string = '24h';

  @IsString()
  API_PREFIX: string = 'api';

  @IsEnum(LogLevel)
  LOG_LEVEL: LogLevel = LogLevel.INFO;

  @IsEnum(LogFormat)
  LOG_FORMAT: LogFormat = LogFormat.JSON;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  HEALTH_CHECK_TIMEOUT: number = 5000;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  HEALTH_CHECK_INTERVAL: number = 30000;

  @IsString()
  CORS_ORIGIN: string = '*';

  // Blockchain Configuration
  @IsString()
  @IsOptional()
  ETHEREUM_RPC_URL?: string;

  @IsString()
  @IsOptional()
  ETHEREUM_TESTNET_RPC_URL?: string;

  @IsString()
  @IsOptional()
  POLYGON_RPC_URL?: string;

  @IsString()
  @IsOptional()
  POLYGON_TESTNET_RPC_URL?: string;

  @IsString()
  @IsOptional()
  NFT_CONTRACT_ADDRESS?: string;

  @IsString()
  @IsOptional()
  MARKETPLACE_CONTRACT_ADDRESS?: string;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @IsOptional()
  DEFAULT_GAS_LIMIT?: number = 500000;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @IsOptional()
  DEFAULT_GAS_PRICE?: number = 20000000000;

  @IsString()
  @IsOptional()
  ADMIN_PRIVATE_KEY?: string;

  @IsString()
  @IsOptional()
  MINTER_PRIVATE_KEY?: string;

  // External service URLs (Industry Standard - Service Discovery)
  @IsString()
  @IsOptional()
  USER_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  PROJECT_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  NFT_GENERATION_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  MARKETPLACE_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  ANALYTICS_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  PROFILE_ANALYSIS_SERVICE_URL?: string;

  // Validation method
  static validate(config: Record<string, unknown>): AppConfig {
    const validatedConfig = new AppConfig();
    Object.assign(validatedConfig, config);
    return validatedConfig;
  }
}
