/**
 * Auth Service - User Service V2
 * 
 * Authentication business logic using shared infrastructure
 */

import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import {
  StandardizedPrismaService,
  ServiceLoggerService,
  BusinessOutcome,
  SecurityEventType,
  SecuritySeverity,
} from '../../../../../shared';
import { LoginDto } from '../dto/login.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly prisma: StandardizedPrismaService,
    private readonly jwtService: JwtService,
    private readonly logger: ServiceLoggerService,
  ) {}

  async login(loginDto: LoginDto, ipAddress?: string, userAgent?: string) {
    return this.prisma.executeWithMetrics('userLogin', async () => {
      const { email, password } = loginDto;

      // Find user by email
      const user = await this.prisma.user.findUnique({
        where: { email },
        include: {
          preferences: true,
        },
      });

      if (!user) {
        this.logger.logSecurityEvent(
          SecurityEventType.AUTHENTICATION,
          SecuritySeverity.MEDIUM,
          'Login attempt with non-existent email',
          {
            metadata: { email, ipAddress, userAgent },
          },
        );
        
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check if user is active
      if (!user.isActive) {
        this.logger.logSecurityEvent(
          SecurityEventType.AUTHENTICATION,
          SecuritySeverity.MEDIUM,
          'Login attempt with inactive account',
          {
            metadata: { userId: user.id, email, ipAddress, userAgent },
          },
        );
        
        throw new UnauthorizedException('Account is deactivated');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        this.logger.logSecurityEvent(
          SecurityEventType.AUTHENTICATION,
          SecuritySeverity.MEDIUM,
          'Login attempt with invalid password',
          {
            metadata: { userId: user.id, email, ipAddress, userAgent },
          },
        );
        
        throw new UnauthorizedException('Invalid credentials');
      }

      // Generate JWT tokens
      const payload = {
        sub: user.id,
        email: user.email,
        username: user.username,
        permissions: this.getUserPermissions(user),
      };

      const accessToken = this.jwtService.sign(payload);
      const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

      // Create session
      const session = await this.prisma.userSession.create({
        data: {
          userId: user.id,
          token: refreshToken,
          ipAddress,
          userAgent,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      });

      // Update last login
      await this.prisma.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() },
      });

      // Log successful login
      this.logger.logBusinessEvent(
        'auth',
        'login',
        BusinessOutcome.SUCCESS,
        {
          business: {
            entity: 'user',
            entityId: user.id,
            attributes: { email: user.email },
          },
          metadata: { ipAddress, userAgent, sessionId: session.id },
        },
      );

      this.logger.logSecurityEvent(
        SecurityEventType.AUTHENTICATION,
        SecuritySeverity.LOW,
        'Successful user login',
        {
          metadata: { userId: user.id, email, ipAddress, userAgent },
        },
      );

      // Return user data without password
      const { password: _, ...userWithoutPassword } = user;
      
      return {
        user: userWithoutPassword,
        accessToken,
        refreshToken,
        expiresIn: '24h',
      };
    });
  }

  async logout(userId: string, token?: string) {
    return this.prisma.executeWithMetrics('userLogout', async () => {
      // Deactivate session if token provided
      if (token) {
        await this.prisma.userSession.updateMany({
          where: {
            userId,
            token,
            isActive: true,
          },
          data: {
            isActive: false,
          },
        });
      } else {
        // Deactivate all sessions for user
        await this.prisma.userSession.updateMany({
          where: {
            userId,
            isActive: true,
          },
          data: {
            isActive: false,
          },
        });
      }

      this.logger.logBusinessEvent(
        'auth',
        'logout',
        BusinessOutcome.SUCCESS,
        {
          business: {
            entity: 'user',
            entityId: userId,
          },
        },
      );

      return { message: 'Logged out successfully' };
    });
  }

  async refreshToken(refreshToken: string) {
    return this.prisma.executeWithMetrics('refreshToken', async () => {
      try {
        // Verify refresh token
        const payload = this.jwtService.verify(refreshToken);
        
        // Check if session exists and is active
        const session = await this.prisma.userSession.findFirst({
          where: {
            token: refreshToken,
            isActive: true,
            expiresAt: { gt: new Date() },
          },
          include: {
            user: {
              include: {
                preferences: true,
              },
            },
          },
        });

        if (!session || !session.user.isActive) {
          throw new UnauthorizedException('Invalid refresh token');
        }

        // Generate new access token
        const newPayload = {
          sub: session.user.id,
          email: session.user.email,
          username: session.user.username,
          permissions: this.getUserPermissions(session.user),
        };

        const accessToken = this.jwtService.sign(newPayload);

        this.logger.logBusinessEvent(
          'auth',
          'refresh-token',
          BusinessOutcome.SUCCESS,
          {
            business: {
              entity: 'user',
              entityId: session.user.id,
            },
          },
        );

        return {
          accessToken,
          expiresIn: '24h',
        };
      } catch (error) {
        this.logger.logSecurityEvent(
          SecurityEventType.AUTHENTICATION,
          SecuritySeverity.MEDIUM,
          'Invalid refresh token attempt',
          {
            metadata: { error: error.message },
          },
        );
        
        throw new UnauthorizedException('Invalid refresh token');
      }
    });
  }

  async validateUser(userId: string) {
    return this.prisma.executeWithMetrics('validateUser', async () => {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          isActive: true,
          isVerified: true,
          preferences: true,
        },
      });

      if (!user || !user.isActive) {
        return null;
      }

      return {
        ...user,
        permissions: this.getUserPermissions(user),
      };
    });
  }

  private getUserPermissions(user: any): string[] {
    // Basic permissions for all users
    const permissions = ['USER_READ', 'USER_UPDATE'];

    // Add admin permissions if user is admin
    if (user.email === '<EMAIL>') {
      permissions.push('ADMIN', 'USER_CREATE', 'USER_DELETE');
    }

    // Add verified user permissions
    if (user.isVerified) {
      permissions.push('PROFILE_ANALYZE', 'NFT_CREATE');
    }

    return permissions;
  }

  async getUserSessions(userId: string) {
    return this.prisma.executeWithMetrics('getUserSessions', async () => {
      return this.prisma.userSession.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
        select: {
          id: true,
          ipAddress: true,
          userAgent: true,
          device: true,
          location: true,
          createdAt: true,
          expiresAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });
  }

  async revokeSession(userId: string, sessionId: string) {
    return this.prisma.executeWithMetrics('revokeSession', async () => {
      const session = await this.prisma.userSession.updateMany({
        where: {
          id: sessionId,
          userId,
          isActive: true,
        },
        data: {
          isActive: false,
        },
      });

      if (session.count === 0) {
        throw new UnauthorizedException('Session not found or already revoked');
      }

      this.logger.logBusinessEvent(
        'auth',
        'revoke-session',
        BusinessOutcome.SUCCESS,
        {
          business: {
            entity: 'user-session',
            entityId: sessionId,
          },
          metadata: { userId },
        },
      );

      return { message: 'Session revoked successfully' };
    });
  }
}
