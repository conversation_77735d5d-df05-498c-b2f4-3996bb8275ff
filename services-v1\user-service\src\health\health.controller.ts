import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthCheckService, HealthCheck, HealthCheckResult } from '@nestjs/terminus';
import { Public } from '../auth/public.decorator';
import { DatabaseHealthService } from './database-health.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private databaseHealth: DatabaseHealthService,
  ) {}

  @Public()
  @Get()
  @ApiOperation({ summary: 'Get comprehensive health status' })
  @ApiResponse({ status: 200, description: 'Health status retrieved successfully' })
  @HealthCheck()
  check(): Promise<HealthCheckResult> {
    return this.health.check([
      () => this.databaseHealth.isHealthy('database'),
    ]);
  }

  @Public()
  @Get('simple')
  @ApiOperation({ summary: 'Get simple health status' })
  @ApiResponse({ status: 200, description: 'Simple health status retrieved successfully' })
  getSimpleHealth() {
    return {
      status: 'healthy',
      service: 'user-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
      architecture: 'Industry Standard Microservice',
      dependencies: 'Service-Local Only',
    };
  }

  @Public()
  @Get('database')
  @ApiOperation({ summary: 'Get database health status' })
  @ApiResponse({ status: 200, description: 'Database health status retrieved successfully' })
  async getDatabaseHealth() {
    try {
      const result = await this.databaseHealth.isHealthy('database');
      return {
        status: 'healthy',
        database: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        database: { status: 'down', message: error.message },
        timestamp: new Date().toISOString(),
      };
    }
  }
}
