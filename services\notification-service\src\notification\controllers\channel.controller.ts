import { <PERSON>, Get, Post, Body, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { EmailService } from '../services/email.service';
import { SMSService } from '../services/sms.service';
import { PushNotificationService } from '../services/push-notification.service';

@ApiTags('channels')
@Controller('channels')
export class ChannelController {
  private readonly logger = new Logger(ChannelController.name);

  constructor(
    private readonly emailService: EmailService,
    private readonly smsService: SMSService,
    private readonly pushService: PushNotificationService,
  ) {}

  @Get('status')
  @ApiOperation({ summary: 'Get channel status' })
  @ApiResponse({ status: 200, description: 'Channel status retrieved successfully' })
  async getChannelStatus() {
    try {
      this.logger.log('Getting channel status');

      const emailConnected = await this.emailService.verifyConnection();

      return {
        success: true,
        data: {
          email: {
            configured: true,
            connected: emailConnected,
          },
          sms: {
            configured: this.smsService.isConfigured(),
            connected: this.smsService.isConfigured(),
          },
          push: {
            configured: this.pushService.isConfigured(),
            connected: this.pushService.isConfigured(),
          },
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get channel status: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve channel status',
        details: error.message,
      };
    }
  }

  @Post('email/test')
  @ApiOperation({ summary: 'Test email channel' })
  @ApiResponse({ status: 200, description: 'Email test completed' })
  async testEmail(@Body() testData: { to: string; subject?: string; message?: string }) {
    try {
      this.logger.log(`Testing email channel to: ${testData.to}`);

      const result = await this.emailService.sendEmail({
        to: testData.to,
        subject: testData.subject || 'Test Email from Social NFT Platform',
        text: testData.message || 'This is a test email to verify the email channel is working correctly.',
        html: `<p>${testData.message || 'This is a test email to verify the email channel is working correctly.'}</p>`,
      });

      return {
        success: result.success,
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to test email channel: ${error.message}`);
      return {
        success: false,
        error: 'Failed to test email channel',
        details: error.message,
      };
    }
  }

  @Post('sms/test')
  @ApiOperation({ summary: 'Test SMS channel' })
  @ApiResponse({ status: 200, description: 'SMS test completed' })
  async testSMS(@Body() testData: { to: string; message?: string }) {
    try {
      this.logger.log(`Testing SMS channel to: ${testData.to}`);

      const result = await this.smsService.sendSMS({
        to: testData.to,
        message: testData.message || 'Test SMS from Social NFT Platform. SMS channel is working correctly.',
      });

      return {
        success: result.success,
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to test SMS channel: ${error.message}`);
      return {
        success: false,
        error: 'Failed to test SMS channel',
        details: error.message,
      };
    }
  }

  @Post('push/test')
  @ApiOperation({ summary: 'Test push notification channel' })
  @ApiResponse({ status: 200, description: 'Push notification test completed' })
  async testPush(@Body() testData: { token: string; title?: string; body?: string }) {
    try {
      this.logger.log('Testing push notification channel');

      const result = await this.pushService.sendPushNotification({
        token: testData.token,
        title: testData.title || 'Test Push Notification',
        body: testData.body || 'This is a test push notification from Social NFT Platform.',
        data: {
          type: 'test',
          timestamp: new Date().toISOString(),
        },
      });

      return {
        success: result.success,
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to test push notification channel: ${error.message}`);
      return {
        success: false,
        error: 'Failed to test push notification channel',
        details: error.message,
      };
    }
  }
}
