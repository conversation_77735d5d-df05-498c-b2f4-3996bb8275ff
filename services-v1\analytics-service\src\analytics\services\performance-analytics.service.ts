import { Injectable, Logger } from '@nestjs/common';

export interface PerformanceMetric {
  id: string;
  metricType: string;
  value: number;
  unit: string;
  timestamp: Date;
  source: string;
  metadata: Record<string, any>;
}

export interface SystemPerformance {
  cpu: {
    usage: number; // percentage
    cores: number;
    loadAverage: number[];
  };
  memory: {
    used: number; // bytes
    total: number; // bytes
    percentage: number;
    heap: {
      used: number;
      total: number;
    };
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    requestsPerSecond: number;
    activeConnections: number;
  };
  database: {
    connections: number;
    queryTime: number; // ms
    slowQueries: number;
  };
}

export interface ServiceMetrics {
  serviceName: string;
  responseTime: {
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  };
  throughput: {
    requestsPerSecond: number;
    requestsPerMinute: number;
  };
  errorRate: {
    percentage: number;
    count: number;
  };
  availability: {
    uptime: number; // percentage
    lastDowntime: Date | null;
  };
}

export interface PerformanceReport {
  timestamp: Date;
  system: SystemPerformance;
  services: ServiceMetrics[];
  alerts: Array<{
    level: 'info' | 'warning' | 'error' | 'critical';
    message: string;
    metric: string;
    value: number;
    threshold: number;
  }>;
  recommendations: string[];
}

@Injectable()
export class PerformanceAnalyticsService {
  private readonly logger = new Logger(PerformanceAnalyticsService.name);
  private metrics: PerformanceMetric[] = [];
  private performanceHistory: PerformanceReport[] = [];
  private alertThresholds = {
    cpu: { warning: 70, critical: 90 },
    memory: { warning: 80, critical: 95 },
    responseTime: { warning: 1000, critical: 3000 }, // ms
    errorRate: { warning: 5, critical: 10 }, // percentage
  };

  constructor() {
    this.startPerformanceMonitoring();
  }

  /**
   * Record a performance metric
   */
  async recordMetric(
    metricType: string,
    value: number,
    unit: string,
    source: string,
    metadata: Record<string, any> = {}
  ): Promise<string> {
    try {
      const metric: PerformanceMetric = {
        id: this.generateMetricId(),
        metricType,
        value,
        unit,
        timestamp: new Date(),
        source,
        metadata,
      };

      this.metrics.push(metric);

      // Keep only recent metrics (last 24 hours)
      const cutoff = Date.now() - 24 * 60 * 60 * 1000;
      this.metrics = this.metrics.filter(m => m.timestamp.getTime() > cutoff);

      this.logger.debug(`Metric recorded: ${metricType} = ${value} ${unit}`, {
        source,
        metricId: metric.id,
      });

      return metric.id;
    } catch (error) {
      this.logger.error(`Failed to record metric: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Record response time metric
   */
  async recordResponseTime(
    service: string,
    endpoint: string,
    responseTime: number,
    statusCode: number
  ): Promise<void> {
    await this.recordMetric(
      'response_time',
      responseTime,
      'ms',
      service,
      { endpoint, statusCode }
    );
  }

  /**
   * Record throughput metric
   */
  async recordThroughput(service: string, requestCount: number, timeWindow: number): Promise<void> {
    const requestsPerSecond = requestCount / (timeWindow / 1000);
    await this.recordMetric(
      'throughput',
      requestsPerSecond,
      'rps',
      service,
      { requestCount, timeWindow }
    );
  }

  /**
   * Record error metric
   */
  async recordError(
    service: string,
    errorType: string,
    endpoint?: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    await this.recordMetric(
      'error',
      1,
      'count',
      service,
      { errorType, endpoint, ...metadata }
    );
  }

  /**
   * Get current system performance
   */
  async getSystemPerformance(): Promise<SystemPerformance> {
    try {
      // In a real implementation, this would collect actual system metrics
      // For now, we'll generate realistic mock data
      const performance: SystemPerformance = {
        cpu: {
          usage: 45 + Math.random() * 30, // 45-75%
          cores: 4,
          loadAverage: [1.2, 1.5, 1.8],
        },
        memory: {
          used: 2.1 * 1024 * 1024 * 1024, // 2.1 GB
          total: 8 * 1024 * 1024 * 1024, // 8 GB
          percentage: 26.25,
          heap: {
            used: 150 * 1024 * 1024, // 150 MB
            total: 512 * 1024 * 1024, // 512 MB
          },
        },
        network: {
          bytesIn: 1024 * 1024 * 50, // 50 MB
          bytesOut: 1024 * 1024 * 75, // 75 MB
          requestsPerSecond: 25 + Math.random() * 20,
          activeConnections: 150,
        },
        database: {
          connections: 12,
          queryTime: 15 + Math.random() * 10, // 15-25ms
          slowQueries: Math.floor(Math.random() * 3),
        },
      };

      this.logger.debug('System performance collected', {
        cpuUsage: performance.cpu.usage,
        memoryPercentage: performance.memory.percentage,
        requestsPerSecond: performance.network.requestsPerSecond,
      });

      return performance;
    } catch (error) {
      this.logger.error(`Failed to get system performance: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get service metrics
   */
  async getServiceMetrics(serviceName?: string): Promise<ServiceMetrics[]> {
    try {
      const services = serviceName 
        ? [serviceName] 
        : ['api-gateway', 'profile-analysis-service', 'blockchain-service', 'nft-generation-service', 'analytics-service'];

      const serviceMetrics: ServiceMetrics[] = [];

      for (const service of services) {
        const metrics = await this.calculateServiceMetrics(service);
        serviceMetrics.push(metrics);
      }

      this.logger.debug(`Service metrics calculated for ${serviceMetrics.length} services`);
      return serviceMetrics;
    } catch (error) {
      this.logger.error(`Failed to get service metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Generate performance report
   */
  async generatePerformanceReport(): Promise<PerformanceReport> {
    try {
      this.logger.log('Generating performance report');

      const system = await this.getSystemPerformance();
      const services = await this.getServiceMetrics();
      const alerts = this.generateAlerts(system, services);
      const recommendations = this.generateRecommendations(system, services, alerts);

      const report: PerformanceReport = {
        timestamp: new Date(),
        system,
        services,
        alerts,
        recommendations,
      };

      // Store report in history
      this.performanceHistory.push(report);

      // Keep only last 100 reports
      if (this.performanceHistory.length > 100) {
        this.performanceHistory = this.performanceHistory.slice(-100);
      }

      this.logger.log(`Performance report generated with ${alerts.length} alerts and ${recommendations.length} recommendations`);
      return report;
    } catch (error) {
      this.logger.error(`Failed to generate performance report: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get performance trends
   */
  getPerformanceTrends(hours: number = 24): Array<{
    timestamp: Date;
    cpuUsage: number;
    memoryUsage: number;
    responseTime: number;
    throughput: number;
    errorRate: number;
  }> {
    const trends = [];
    const now = Date.now();
    const interval = (hours * 60 * 60 * 1000) / 24; // 24 data points

    for (let i = 0; i < 24; i++) {
      const timestamp = new Date(now - (23 - i) * interval);
      
      // Calculate metrics for this time period
      const periodMetrics = this.metrics.filter(m => 
        Math.abs(m.timestamp.getTime() - timestamp.getTime()) < interval / 2
      );

      const cpuMetrics = periodMetrics.filter(m => m.metricType === 'cpu_usage');
      const memoryMetrics = periodMetrics.filter(m => m.metricType === 'memory_usage');
      const responseTimeMetrics = periodMetrics.filter(m => m.metricType === 'response_time');
      const throughputMetrics = periodMetrics.filter(m => m.metricType === 'throughput');
      const errorMetrics = periodMetrics.filter(m => m.metricType === 'error');

      trends.push({
        timestamp,
        cpuUsage: this.calculateAverage(cpuMetrics.map(m => m.value)) || 50 + Math.random() * 20,
        memoryUsage: this.calculateAverage(memoryMetrics.map(m => m.value)) || 60 + Math.random() * 15,
        responseTime: this.calculateAverage(responseTimeMetrics.map(m => m.value)) || 200 + Math.random() * 100,
        throughput: this.calculateAverage(throughputMetrics.map(m => m.value)) || 30 + Math.random() * 10,
        errorRate: errorMetrics.length || Math.random() * 2,
      });
    }

    return trends;
  }

  /**
   * Get metrics by type
   */
  getMetricsByType(metricType: string, limit: number = 100): PerformanceMetric[] {
    return this.metrics
      .filter(m => m.metricType === metricType)
      .slice(-limit);
  }

  /**
   * Get performance history
   */
  getPerformanceHistory(limit: number = 50): PerformanceReport[] {
    return this.performanceHistory.slice(-limit);
  }

  /**
   * Calculate service metrics
   */
  private async calculateServiceMetrics(serviceName: string): Promise<ServiceMetrics> {
    const serviceMetrics = this.metrics.filter(m => m.source === serviceName);
    const responseTimeMetrics = serviceMetrics.filter(m => m.metricType === 'response_time');
    const throughputMetrics = serviceMetrics.filter(m => m.metricType === 'throughput');
    const errorMetrics = serviceMetrics.filter(m => m.metricType === 'error');

    // Calculate response time percentiles
    const responseTimes = responseTimeMetrics.map(m => m.value).sort((a, b) => a - b);
    const responseTime = {
      avg: this.calculateAverage(responseTimes) || 250,
      p50: this.calculatePercentile(responseTimes, 50) || 200,
      p95: this.calculatePercentile(responseTimes, 95) || 500,
      p99: this.calculatePercentile(responseTimes, 99) || 800,
    };

    // Calculate throughput
    const avgThroughput = this.calculateAverage(throughputMetrics.map(m => m.value)) || 25;
    const throughput = {
      requestsPerSecond: avgThroughput,
      requestsPerMinute: avgThroughput * 60,
    };

    // Calculate error rate
    const totalRequests = Math.max(throughputMetrics.length * 60, 1000); // Estimate
    const errorCount = errorMetrics.length;
    const errorRate = {
      percentage: (errorCount / totalRequests) * 100,
      count: errorCount,
    };

    // Calculate availability (mock)
    const availability = {
      uptime: 99.5 + Math.random() * 0.5, // 99.5-100%
      lastDowntime: Math.random() > 0.9 ? new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) : null,
    };

    return {
      serviceName,
      responseTime,
      throughput,
      errorRate,
      availability,
    };
  }

  /**
   * Generate alerts based on thresholds
   */
  private generateAlerts(system: SystemPerformance, services: ServiceMetrics[]) {
    const alerts = [];

    // CPU alerts
    if (system.cpu.usage > this.alertThresholds.cpu.critical) {
      alerts.push({
        level: 'critical' as const,
        message: 'Critical CPU usage detected',
        metric: 'cpu_usage',
        value: system.cpu.usage,
        threshold: this.alertThresholds.cpu.critical,
      });
    } else if (system.cpu.usage > this.alertThresholds.cpu.warning) {
      alerts.push({
        level: 'warning' as const,
        message: 'High CPU usage detected',
        metric: 'cpu_usage',
        value: system.cpu.usage,
        threshold: this.alertThresholds.cpu.warning,
      });
    }

    // Memory alerts
    if (system.memory.percentage > this.alertThresholds.memory.critical) {
      alerts.push({
        level: 'critical' as const,
        message: 'Critical memory usage detected',
        metric: 'memory_usage',
        value: system.memory.percentage,
        threshold: this.alertThresholds.memory.critical,
      });
    } else if (system.memory.percentage > this.alertThresholds.memory.warning) {
      alerts.push({
        level: 'warning' as const,
        message: 'High memory usage detected',
        metric: 'memory_usage',
        value: system.memory.percentage,
        threshold: this.alertThresholds.memory.warning,
      });
    }

    // Service alerts
    services.forEach(service => {
      if (service.responseTime.avg > this.alertThresholds.responseTime.critical) {
        alerts.push({
          level: 'critical' as const,
          message: `Critical response time for ${service.serviceName}`,
          metric: 'response_time',
          value: service.responseTime.avg,
          threshold: this.alertThresholds.responseTime.critical,
        });
      }

      if (service.errorRate.percentage > this.alertThresholds.errorRate.warning) {
        alerts.push({
          level: 'warning' as const,
          message: `High error rate for ${service.serviceName}`,
          metric: 'error_rate',
          value: service.errorRate.percentage,
          threshold: this.alertThresholds.errorRate.warning,
        });
      }
    });

    return alerts;
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(system: SystemPerformance, services: ServiceMetrics[], alerts: any[]): string[] {
    const recommendations = [];

    if (system.cpu.usage > 70) {
      recommendations.push('Consider scaling horizontally or optimizing CPU-intensive operations');
    }

    if (system.memory.percentage > 80) {
      recommendations.push('Monitor memory leaks and consider increasing available memory');
    }

    if (system.database.queryTime > 50) {
      recommendations.push('Optimize database queries and consider adding indexes');
    }

    const slowServices = services.filter(s => s.responseTime.avg > 1000);
    if (slowServices.length > 0) {
      recommendations.push(`Optimize response times for: ${slowServices.map(s => s.serviceName).join(', ')}`);
    }

    const errorProneServices = services.filter(s => s.errorRate.percentage > 2);
    if (errorProneServices.length > 0) {
      recommendations.push(`Investigate error sources in: ${errorProneServices.map(s => s.serviceName).join(', ')}`);
    }

    if (alerts.filter(a => a.level === 'critical').length > 0) {
      recommendations.push('Address critical alerts immediately to prevent service degradation');
    }

    return recommendations;
  }

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    // Generate periodic system metrics
    setInterval(async () => {
      try {
        const system = await this.getSystemPerformance();
        
        await this.recordMetric('cpu_usage', system.cpu.usage, '%', 'system');
        await this.recordMetric('memory_usage', system.memory.percentage, '%', 'system');
        await this.recordMetric('network_rps', system.network.requestsPerSecond, 'rps', 'system');
        await this.recordMetric('db_query_time', system.database.queryTime, 'ms', 'system');
      } catch (error) {
        this.logger.error(`Performance monitoring error: ${error.message}`);
      }
    }, 30000); // Every 30 seconds

    this.logger.log('Performance monitoring started');
  }

  /**
   * Calculate average of array
   */
  private calculateAverage(values: number[]): number {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }

  /**
   * Calculate percentile
   */
  private calculatePercentile(values: number[], percentile: number): number {
    if (values.length === 0) return 0;
    const index = Math.ceil((percentile / 100) * values.length) - 1;
    return values[Math.max(0, index)];
  }

  /**
   * Generate metric ID
   */
  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup on service destruction
   */
  onModuleDestroy(): void {
    this.logger.log('Performance analytics service shutting down');
  }
}
