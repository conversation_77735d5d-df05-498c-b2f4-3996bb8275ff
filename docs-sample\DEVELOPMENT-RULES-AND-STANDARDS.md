# 🏗️ **SOCIAL COMMERCE V2 DEVELOPMENT RULES & STANDARDS**

## **📋 MANDATORY DEVELOPMENT GUIDELINES FOR ALL DEVELOPERS & AI AGENTS**

### **🔧 INTELLIGENT MIGRATION & IMPROVEMENT APPROACH**
**This project follows the "Intelligent Migration & Improvement" methodology for V1 to V2 transformation**

### **🧹 MANDATORY CLEANUP STANDARDS**
**ALL developers and AI agents MUST follow [CLEANUP-RULES-AND-STANDARDS.md](./CLEANUP-RULES-AND-STANDARDS.md) after ANY change**

---

## 🎯 **CORE PRINCIPLES**

### **1. INTELLIGENT MIGRATION ANALYSIS**
```
🔍 MANDATORY MIGRATION ANALYSIS STEPS:
1. ANALYZE V1 IMPLEMENTATION - Understand existing business logic
2. IDENTIFY WHAT TO KEEP - Proven functionality and working patterns
3. IDENTIFY WHAT TO FIX - Design issues and implementation problems
4. IDENTIFY WHAT TO IMPROVE - Performance, security, maintainability
5. IDENTIFY WHAT TO ENHANCE - Missing enterprise features
6. PLAN INTELLIGENT INTEGRATION - How to improve while preserving value
```

### **2. MIGRATION METHODOLOGY**
```
🔧 INTELLIGENT MIGRATION PRINCIPLES:
✅ KEEP: Proven business logic and functional behavior
🚨 FIX: Architectural problems and design mistakes
🔄 IMPROVE: Implementation quality and performance
🚀 ENHANCE: Add missing enterprise features and best practices

❌ NEVER: Copy-paste V1 code without analysis
❌ NEVER: Start from scratch without preserving business value
❌ NEVER: Ignore V1 design issues
❌ NEVER: Lose proven functionality
```

### **3. SYSTEMATIC IMPROVEMENT APPROACH**
```
🎯 INTELLIGENT IMPLEMENTATION PROCESS:
1. Analyze V1 implementation for business logic extraction
2. Identify design issues and improvement opportunities
3. Keep working business logic while fixing implementation
4. Improve code quality, performance, and maintainability
5. Add enterprise features without breaking existing functionality
6. Test to ensure no regression in business behavior
7. Document improvements and architectural decisions
8. Commit regularly to Git after each major progress milestone
```

### **4. VERSION CONTROL & COLLABORATION**
```
📝 MANDATORY GIT WORKFLOW:
✅ ALWAYS: Commit after each major implementation milestone
✅ ALWAYS: Use descriptive commit messages with context
✅ ALWAYS: Create feature branches for major work
✅ ALWAYS: Keep commits focused and atomic
✅ ALWAYS: Include implementation details in commit messages

❌ NEVER: Large commits without incremental progress
❌ NEVER: Generic commit messages like "fix" or "update"
❌ NEVER: Commit without testing the changes
❌ NEVER: Skip commits during long implementation sessions
```

---

## 📁 **ARCHITECTURE RULES**

### **Rule 1: Shared Infrastructure Usage**
```typescript
// ✅ CORRECT: Use shared infrastructure
import { ResponseService } from '@shared/responses/services/response.service';
import { ServiceLoggerService } from '@shared/logging/services/service-logger.service';
import { StandardizedConfigService } from '@shared/config/services/config.service';

// ❌ WRONG: Creating custom response handling
class CustomResponseHandler { ... }
```

### **Rule 2: Module Structure Compliance**
```
📁 MANDATORY SERVICE STRUCTURE:
services/[service-name]/
├── src/
│   ├── features/[entity]/
│   │   ├── controllers/
│   │   ├── services/
│   │   ├── repositories/
│   │   ├── dto/
│   │   └── entities/
│   ├── health/
│   ├── config/
│   └── main.ts
├── package.json
├── Dockerfile
└── .env.example
```

### **Rule 3: Import Path Standards**
```typescript
// ✅ CORRECT: Use path aliases
import { SharedModule } from '@shared/shared.module';
import { UserEntity } from '@services/user-service/entities/user.entity';

// ❌ WRONG: Relative imports across services
import { SharedModule } from '../../../shared/shared.module';
```

---

## 🔐 **AUTHENTICATION & AUTHORIZATION RULES**

### **Rule 4: Authentication Implementation**
```typescript
// ✅ CORRECT: Use standardized decorators
@Controller('users')
@UseGuards(StandardizedJwtAuthGuard, PermissionsGuard)
export class UserController {
  @Get()
  @RequireUserRead()
  async findAll(@CurrentUser() user: AuthenticatedUser) {
    // Implementation
  }
}

// ❌ WRONG: Custom authentication logic
@UseGuards(CustomAuthGuard)
```

### **Rule 5: Permission Checking**
```typescript
// ✅ CORRECT: Use permission decorators
@RequireStoreOwnership()
@RequirePermissions(Permission.STORE_WRITE)
async updateStore(@Param('id') id: string, @CurrentUser() user: AuthenticatedUser) {
  // Implementation
}

// ❌ WRONG: Manual permission checking
if (user.roles.includes('admin')) { ... }
```

---

## 📊 **RESPONSE STANDARDIZATION RULES**

### **Rule 6: Response Format**
```typescript
// ✅ CORRECT: Use ResponseService
constructor(private readonly responseService: ResponseService) {}

async findAll() {
  const data = await this.service.findAll();
  return this.responseService.success(data, 'Users retrieved successfully');
}

// ❌ WRONG: Direct response objects
return { success: true, data: users };
```

### **Rule 7: Error Handling**
```typescript
// ✅ CORRECT: Use standardized error responses
try {
  // Implementation
} catch (error) {
  if (error instanceof ValidationError) {
    return this.responseService.badRequest('Validation failed', error.details);
  }
  return this.responseService.internalServerError('Operation failed');
}

// ❌ WRONG: Custom error responses
throw new HttpException('Custom error', 500);
```

---

## 📝 **LOGGING RULES**

### **Rule 8: Structured Logging**
```typescript
// ✅ CORRECT: Use ServiceLoggerService
constructor(private readonly logger: ServiceLoggerService) {}

async createUser(userData: CreateUserDto, currentUser: AuthenticatedUser) {
  const operation = this.logger.startOperation('user_creation', {
    userId: currentUser.id,
    correlationId: this.generateCorrelationId()
  });

  try {
    const user = await this.userRepository.create(userData);
    
    this.logger.logBusinessEvent({
      message: 'User created successfully',
      eventType: EventType.USER_REGISTRATION,
      domain: 'user',
      action: 'create',
      outcome: BusinessOutcome.SUCCESS,
      resourceId: user.id,
      context: { userId: currentUser.id }
    });

    operation.end();
    return user;
  } catch (error) {
    this.logger.error('User creation failed', error);
    throw error;
  }
}

// ❌ WRONG: Console.log or custom logging
console.log('User created');
```

### **Rule 9: Correlation ID Tracking**
```typescript
// ✅ CORRECT: Always include correlation ID
async processRequest(@CorrelationId() correlationId: string) {
  this.logger.info('Processing request', { correlationId });
  // Implementation
}

// ❌ WRONG: No correlation tracking
async processRequest() {
  this.logger.info('Processing request');
}
```

---

## 🗄️ **DATA ACCESS RULES**

### **Rule 10: Repository Pattern**
```typescript
// ✅ CORRECT: Extend BaseRepository
@Injectable()
export class UserRepository extends BaseRepository<User, CreateUserDto, UpdateUserDto> {
  constructor(
    private readonly prisma: PrismaService,
    logger: ServiceLoggerService
  ) {
    super(logger, 'User');
  }

  protected getModel() {
    return this.prisma.user;
  }

  protected toEntity(data: any): User {
    return new User(data);
  }
}

// ❌ WRONG: Direct Prisma usage in services
@Injectable()
export class UserService {
  constructor(private readonly prisma: PrismaService) {}
  
  async findAll() {
    return this.prisma.user.findMany(); // Wrong!
  }
}
```

### **Rule 11: Database Transactions**
```typescript
// ✅ CORRECT: Use repository transaction methods
async createUserWithProfile(userData: CreateUserDto, profileData: CreateProfileDto) {
  return this.prisma.$transaction(async (tx) => {
    const user = await this.userRepository.create(userData);
    const profile = await this.profileRepository.create({
      ...profileData,
      userId: user.id
    });
    return { user, profile };
  });
}

// ❌ WRONG: Manual transaction handling
```

---

## ⚙️ **CONFIGURATION RULES**

### **Rule 12: Configuration Access**
```typescript
// ✅ CORRECT: Use StandardizedConfigService
constructor(private readonly configService: StandardizedConfigService) {}

async setupService() {
  const serviceConfig = this.configService.getServiceConfig();
  const dbConfig = this.configService.getDatabaseConfig();
  // Use configurations
}

// ❌ WRONG: Direct environment variable access
const port = process.env.PORT; // Wrong!
```

### **Rule 13: Environment Variables**
```typescript
// ✅ CORRECT: Follow naming conventions
SERVICE_NAME=user-service-v2
SERVICE_PORT=3001
DB_HOST=localhost
JWT_SECRET=your-secret

// ❌ WRONG: Inconsistent naming
userServicePort=3001
database_host=localhost
jwtSecret=secret
```

---

## 🧪 **TESTING RULES**

### **Rule 14: Test Structure**
```typescript
// ✅ CORRECT: Comprehensive test structure
describe('UserController', () => {
  let controller: UserController;
  let service: UserService;
  let responseService: ResponseService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        UserService,
        ResponseService,
        ServiceLoggerService,
        // Mock providers
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
  });

  describe('findAll', () => {
    it('should return standardized response', async () => {
      // Test implementation
    });
  });
});

// ❌ WRONG: Minimal or no tests
```

---

## 🚀 **DEPLOYMENT RULES**

### **Rule 15: Docker Configuration**
```dockerfile
# ✅ CORRECT: Standardized Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001
CMD ["npm", "run", "start:prod"]

# ❌ WRONG: Custom or inconsistent Docker setup
```

### **Rule 16: Health Check Implementation**
```typescript
// ✅ CORRECT: Standardized health check
@Controller('health')
export class HealthController {
  constructor(private readonly responseService: ResponseService) {}

  @Get()
  @Public()
  async checkHealth() {
    return this.responseService.healthCheck('healthy', {
      database: { status: 'healthy', responseTime: 50 },
      redis: { status: 'healthy', responseTime: 10 }
    });
  }
}

// ❌ WRONG: Custom health check format
```

---

## 📋 **AI AGENT SPECIFIC RULES**

### **Rule 17: Intelligent Migration Analysis (MANDATORY)**
```
🔍 MANDATORY MIGRATION ANALYSIS FOR AI AGENTS:

1. READ MIGRATION DOCUMENTATION:
   - Check docs/COMPREHENSIVE-V1-TO-V2-MIGRATION-PLAN.md
   - Understand Intelligent Migration & Improvement approach
   - Review current phase and migration status

2. ANALYZE V1 IMPLEMENTATION:
   - Study V1 business logic in ../social-commerce-refined/
   - Identify what works well (KEEP)
   - Identify design issues (FIX)
   - Identify improvement opportunities (IMPROVE)
   - Identify missing enterprise features (ENHANCE)

3. PLAN INTELLIGENT MIGRATION:
   - How to preserve proven business logic?
   - What design issues need fixing?
   - How to improve implementation quality?
   - What enterprise features to add?
   - How to integrate with V2 shared infrastructure?

4. IMPLEMENT WITH INTELLIGENCE:
   - KEEP: Preserve working business logic and functional behavior
   - FIX: Resolve architectural problems and design mistakes
   - IMPROVE: Enhance code quality, performance, maintainability
   - ENHANCE: Add enterprise features using V2 shared infrastructure

5. VALIDATE MIGRATION:
   - Ensure no business logic regression
   - Verify improved implementation quality
   - Confirm enterprise features work correctly
   - Update migration documentation
   - Commit changes with descriptive messages
```

### **Rule 19: Git Workflow for AI Agents (MANDATORY)**
```
📝 MANDATORY GIT WORKFLOW FOR AI AGENTS:

COMMIT FREQUENCY:
✅ After each TRUE Template-First file operation (every 30 lines)
✅ After completing each service analysis section
✅ After implementing each migration component
✅ After fixing each design issue
✅ After adding each enterprise enhancement

COMMIT MESSAGE FORMAT:
feat: [scope] - [brief description]

[Detailed description]
- What was implemented/analyzed
- What business logic was preserved (KEEP)
- What design issues were fixed (FIX)
- What improvements were made (IMPROVE)
- What enterprise features were added (ENHANCE)

EXAMPLES:
feat: api-gateway-analysis - complete business logic analysis

- Analyzed V1 API Gateway routing and forwarding logic
- KEEP: Service discovery concept and request forwarding patterns
- FIX: Identified hardcoded URLs and basic circuit breaker issues
- IMPROVE: Documented performance and error handling improvements
- ENHANCE: Planned advanced rate limiting and load balancing features
```

### **Rule 18: Migration Implementation Standards**
```
🔧 INTELLIGENT MIGRATION IMPLEMENTATION:

EXAMPLE - User Service Migration:
✅ KEEP: V1 registration workflow (email → verification → activation)
🚨 FIX: Hardcoded JWT secrets → Use V2 configuration service
🔄 IMPROVE: Basic password validation → Strong password policies
🚀 ENHANCE: Add MFA support using V2 authentication infrastructure

EXAMPLE - API Gateway Migration:
✅ KEEP: V1 service discovery and routing concepts
🚨 FIX: Hardcoded service URLs → Dynamic service registry
🔄 IMPROVE: Basic error handling → Comprehensive error categorization
🚀 ENHANCE: Add rate limiting, circuit breaker, request caching

FORBIDDEN APPROACHES:
❌ Copy-paste V1 code without analysis
❌ Ignore V1 design issues
❌ Start from scratch without preserving business logic
❌ Create custom implementations instead of using V2 shared infrastructure
```

### **Rule 19: Migration Code Review Checklist**
```
✅ BEFORE SUBMITTING MIGRATION CODE:

MIGRATION ANALYSIS:
□ Analyzed V1 implementation thoroughly
□ Identified business logic to preserve (KEEP)
□ Identified design issues to fix (FIX)
□ Identified improvements to make (IMPROVE)
□ Identified enterprise features to add (ENHANCE)
□ Documented migration decisions and rationale

IMPLEMENTATION QUALITY:
□ Uses V2 shared infrastructure components
□ Follows established V2 patterns and conventions
□ Preserves V1 business logic and functional behavior
□ Fixes identified V1 design issues
□ Improves code quality and performance
□ Adds appropriate enterprise features
□ Includes proper error handling with ResponseService
□ Has correlation ID tracking in all operations
□ Uses standardized response format
□ Includes comprehensive logging with ServiceLoggerService
□ Has proper authentication/authorization decorators
□ Follows repository pattern for data access

VALIDATION:
□ No business logic regression from V1
□ Improved implementation quality over V1
□ Enterprise features work correctly
□ Includes unit and integration tests
□ Updates migration documentation
□ No duplicate functionality
□ No temporary fixes or workarounds
□ Regular Git commits with descriptive messages
□ Incremental progress tracked in version control
□ Feature branches used for major implementations
```

---

## 🚨 **ENFORCEMENT RULES**

### **Rule 20: Migration Quality Gates**
```
🚫 MIGRATION CODE WILL BE REJECTED IF:

MIGRATION ANALYSIS FAILURES:
- No analysis of V1 implementation
- Copy-paste V1 code without improvement
- Ignores V1 design issues
- Starts from scratch without preserving business logic
- No documentation of migration decisions

IMPLEMENTATION QUALITY FAILURES:
- Uses custom authentication instead of V2 shared guards
- Creates duplicate response handling instead of using ResponseService
- Bypasses V2 shared infrastructure
- Has inconsistent naming conventions
- Lacks proper error handling
- Missing correlation ID tracking
- No logging implementation with ServiceLoggerService
- Direct database access without repository pattern
- Missing comprehensive tests
- Temporary fixes or TODO comments

MIGRATION VALIDATION FAILURES:
- Business logic regression from V1
- V1 design issues not fixed
- No improvement in code quality
- Enterprise features not properly integrated
- No migration documentation updates

VERSION CONTROL FAILURES:
- No Git commits during implementation
- Poor commit messages without context
- Large commits without incremental progress
- No feature branches for major work
- Missing implementation details in commit history
```

### **Rule 20: Continuous Improvement**
```
📈 CONTINUOUS IMPROVEMENT PROCESS:

1. Regular architecture reviews
2. Pattern consistency audits
3. Performance monitoring
4. Security assessments
5. Documentation updates
6. Developer feedback integration
```

---

## 📚 **REFERENCE DOCUMENTATION**

### **Required Reading for All Developers/AI Agents:**
1. `docs/COMPREHENSIVE-V1-TO-V2-MIGRATION-PLAN.md` - **CRITICAL: Migration strategy and approach**
2. `docs/IMPLEMENTATION-STATUS.md` - Current implementation status
3. `docs/AI-AGENT-GUIDELINES.md` - Mandatory AI agent guidelines
4. `../social-commerce-refined/` - V1 codebase for business logic analysis
5. `shared/` directory - V2 shared infrastructure
6. `services/user-service-v2/` - Reference V2 service implementation (when available)
7. `.env.example` - Environment variable standards
8. `package.json` - Workspace and dependency standards

### **Migration-Specific Documentation:**
1. **V1 Analysis** - Study V1 implementation in `../social-commerce-refined/`
2. **Business Logic Mapping** - Document what to KEEP, FIX, IMPROVE, ENHANCE
3. **Migration Decisions** - Document rationale for each migration choice
4. **Implementation Improvements** - Track quality improvements over V1
5. **Enterprise Enhancements** - Document new features added in V2

### **Quick Reference Commands:**
```bash
# Check current implementation status
cat docs/IMPLEMENTATION-STATUS.md

# Review shared infrastructure
ls -la shared/

# Check service structure
ls -la services/

# Validate environment configuration
npm run validate:config

# Run tests
npm test

# Check code quality
npm run lint
```

---

## 🎯 **SUCCESS METRICS**

### **Platform Consistency Indicators:**
- ✅ All services use shared infrastructure
- ✅ Consistent response formats across all APIs
- ✅ Unified authentication and authorization
- ✅ Comprehensive logging with correlation tracking
- ✅ No duplicate code or functionality
- ✅ All services follow the same structure
- ✅ Complete test coverage
- ✅ Proper error handling everywhere

---

## **🧹 CLEANUP RULES (MANDATORY)**

### **Rule 20: Mandatory Cleanup After Any Change**
```bash
🚨 ZERO TOLERANCE POLICY:
❌ No duplicate code or logic
❌ No dead code or unused imports
❌ No temporary or backup files
❌ No files in wrong directories
❌ No commented-out code
❌ No debug statements in production code

✅ MANDATORY CLEANUP CHECKLIST:
□ Remove unused imports and dependencies
□ Delete temporary and backup files
□ Eliminate duplicate code patterns
□ Clean up file structure and organization
□ Remove debug console.log statements
□ Update documentation for changes
□ Run automated cleanup scripts
□ Verify no empty directories exist
```

### **Rule 21: Automated Cleanup Enforcement**
```bash
# MANDATORY: Run before every commit
./scripts/cleanup.sh

# MANDATORY: Check dependencies
npx depcheck

# MANDATORY: Format and lint
npm run lint:fix
npm run format

# MANDATORY: Remove temporary files
find . -name "*.tmp" -delete
find . -name "*.backup" -delete
find . -type d -empty -delete
```

---

**🚀 REMEMBER: Consistency is key to maintainable, scalable, and professional software architecture!**

**🧹 CLEANUP: A clean codebase is a maintainable codebase. Every cleanup action contributes to long-term project success!**

**Every line of code should follow these rules to ensure our Social Commerce V2 platform remains clean, consistent, and enterprise-ready.**
