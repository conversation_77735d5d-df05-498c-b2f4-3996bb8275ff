import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';

// Business Logic Modules
import { AnalyticsModule } from './analytics/analytics.module';

// Infrastructure Modules
import { PrismaModule } from './prisma/prisma.module';
import { HealthModule } from './health/health.module';

// Configuration
import { AppConfig } from './config/app.config';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // Infrastructure Modules
    PrismaModule,
    HealthModule,

    // Business Logic Modules
    AnalyticsModule,
  ],
  controllers: [AppController],
  providers: [AppConfig, AppService],
})
export class AppModule {}
