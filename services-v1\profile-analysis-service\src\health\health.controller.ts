import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthCheck, HealthCheckService, PrismaHealthIndicator } from '@nestjs/terminus';
import { Public } from '../auth/public.decorator';
import { PrismaService } from '../prisma/prisma.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    private readonly health: HealthCheckService,
    private readonly prismaHealth: PrismaHealthIndicator,
    private readonly prisma: PrismaService,
  ) {}

  @Get()
  @Public()
  @ApiOperation({ summary: 'Comprehensive health check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  @ApiResponse({ status: 503, description: 'Service is unhealthy' })
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.prismaHealth.pingCheck('database', this.prisma),
    ]);
  }

  @Get('simple')
  @Public()
  @ApiOperation({ summary: 'Simple health check' })
  @ApiResponse({ status: 200, description: 'Service is running' })
  getSimpleHealth() {
    const healthInfo = {
      status: 'ok',
      service: 'profile-analysis-service',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      port: process.env.SERVICE_PORT || 3002,
      environment: process.env.NODE_ENV || 'development',
      features: [
        'Twitter Profile Analysis',
        'Social Media Scoring',
        'Configurable Parameters',
        'Real-time Analysis',
        'Historical Tracking',
      ],
    };

    this.logger.debug('Simple health check performed');
    return healthInfo;
  }

  @Get('detailed')
  @Public()
  @ApiOperation({ summary: 'Detailed health check with metrics' })
  @ApiResponse({ status: 200, description: 'Detailed service health information' })
  getDetailedHealth() {
    const memoryUsage = process.memoryUsage();
    
    const detailedHealth = {
      status: 'healthy',
      service: 'profile-analysis-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      port: process.env.SERVICE_PORT || 3002,
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
        external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB',
        rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
      },
      cpu: {
        usage: process.cpuUsage(),
      },
      features: {
        twitterAnalysis: true,
        socialScoring: true,
        configurableParameters: true,
        realTimeAnalysis: true,
        historicalTracking: true,
        mockTwitterSupport: process.env.USE_MOCK_TWITTER === 'true',
      },
      integrations: {
        database: 'PostgreSQL',
        twitterApi: process.env.USE_MOCK_TWITTER === 'true' ? 'Mock Service' : 'Twitter API v2',
        userService: process.env.USER_SERVICE_URL,
        apiGateway: process.env.API_GATEWAY_URL,
      },
    };

    this.logger.debug('Detailed health check performed');
    return detailedHealth;
  }
}
