'use client'

import React, { useState } from 'react'
import {
  DocumentTextIcon,
  ClockIcon,
  ArrowPathIcon,
  EyeIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import {
  useEvolutionHistory,
  useMetadataHistory,
  useRevertEvolution,
  useRollbackMetadata,
  useExportEvolutionData
} from '@/hooks/useNFTEvolution'
import { NFTEvolution, EvolutionStatus, EvolutionHistoryEntry } from '@/types/nft-evolution.types'

interface EvolutionHistoryProps {
  nftId: string
  evolutions?: NFTEvolution[]
  isLoading: boolean
  className?: string
}

export default function EvolutionHistory({
  nftId,
  evolutions = [],
  isLoading,
  className = ''
}: EvolutionHistoryProps) {
  const [selectedEvolution, setSelectedEvolution] = useState<NFTEvolution | null>(null)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [filterStatus, setFilterStatus] = useState<EvolutionStatus | 'all'>('all')
  const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest')

  const { data: historyEntries, isLoading: historyLoading } = useEvolutionHistory(nftId)
  const { data: metadataHistory, isLoading: metadataLoading } = useMetadataHistory(nftId)
  const revertEvolutionMutation = useRevertEvolution()
  const rollbackMetadataMutation = useRollbackMetadata()
  const exportDataMutation = useExportEvolutionData()

  const getStatusIcon = (status: EvolutionStatus) => {
    switch (status) {
      case EvolutionStatus.COMPLETED:
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />
      case EvolutionStatus.FAILED:
        return <XCircleIcon className="h-5 w-5 text-red-600" />
      case EvolutionStatus.CANCELLED:
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-600" />
      default:
        return <ClockIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusColor = (status: EvolutionStatus) => {
    switch (status) {
      case EvolutionStatus.COMPLETED:
        return 'text-green-600 bg-green-100'
      case EvolutionStatus.FAILED:
        return 'text-red-600 bg-red-100'
      case EvolutionStatus.CANCELLED:
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedItems(newExpanded)
  }

  const handleRevertEvolution = async (evolutionId: string) => {
    if (confirm('Are you sure you want to revert this evolution? This action cannot be undone.')) {
      try {
        await revertEvolutionMutation.mutateAsync(evolutionId)
      } catch (error) {
        console.error('Failed to revert evolution:', error)
      }
    }
  }

  const handleRollbackMetadata = async (version: string) => {
    if (confirm('Are you sure you want to rollback to this metadata version?')) {
      try {
        await rollbackMetadataMutation.mutateAsync({ nftId, version })
      } catch (error) {
        console.error('Failed to rollback metadata:', error)
      }
    }
  }

  const handleExportData = async (format: 'json' | 'csv') => {
    try {
      const blob = await exportDataMutation.mutateAsync({ nftId, format })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `nft-evolution-history-${nftId}.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to export data:', error)
    }
  }

  const filteredEvolutions = evolutions
    .filter(evolution => filterStatus === 'all' || evolution.status === filterStatus)
    .sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime()
      const dateB = new Date(b.createdAt).getTime()
      return sortOrder === 'newest' ? dateB - dateA : dateA - dateB
    })

  if (isLoading || historyLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header and Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Evolution History</h2>
          <p className="text-sm text-gray-600">Complete history of all evolution events</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Filter by Status */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="all">All Status</option>
            <option value={EvolutionStatus.COMPLETED}>Completed</option>
            <option value={EvolutionStatus.FAILED}>Failed</option>
            <option value={EvolutionStatus.CANCELLED}>Cancelled</option>
          </select>

          {/* Sort Order */}
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
          </select>

          {/* Export Button */}
          <div className="relative">
            <button
              onClick={() => handleExportData('json')}
              disabled={exportDataMutation.isPending}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              <DocumentTextIcon className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Evolution History List */}
      {filteredEvolutions.length > 0 ? (
        <div className="space-y-4">
          {filteredEvolutions.map((evolution) => (
            <div
              key={evolution.id}
              className="bg-white border border-gray-200 rounded-lg overflow-hidden"
            >
              {/* Evolution Header */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => toggleExpanded(evolution.id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {expandedItems.has(evolution.id) ? (
                        <ChevronDownIcon className="h-5 w-5" />
                      ) : (
                        <ChevronRightIcon className="h-5 w-5" />
                      )}
                    </button>
                    
                    {getStatusIcon(evolution.status)}
                    
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        Evolution Stage {evolution.evolutionStage}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {evolution.triggerType.replace('_', ' ')} • {new Date(evolution.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(evolution.status)}`}>
                      {evolution.status.replace('_', ' ')}
                    </span>
                    
                    {evolution.status === EvolutionStatus.COMPLETED && evolution.isReversible && (
                      <button
                        onClick={() => handleRevertEvolution(evolution.id)}
                        disabled={revertEvolutionMutation.isPending}
                        className="text-sm text-red-600 hover:text-red-700 disabled:opacity-50"
                      >
                        <ArrowPathIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Expanded Content */}
              {expandedItems.has(evolution.id) && (
                <div className="p-4 space-y-4">
                  {/* Evolution Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="text-sm font-medium text-gray-900">Impact Summary</div>
                      <div className="mt-1 space-y-1 text-sm text-gray-600">
                        <div>Rarity: {evolution.rarityImpact > 0 ? '+' : ''}{evolution.rarityImpact.toFixed(1)}</div>
                        <div>Value: {evolution.valueImpact > 0 ? '+' : ''}{evolution.valueImpact.toFixed(1)}%</div>
                        <div>Score: {evolution.evolutionScore.toFixed(1)}/10</div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="text-sm font-medium text-gray-900">Changes Made</div>
                      <div className="mt-1 space-y-1 text-sm text-gray-600">
                        <div>Traits: {evolution.traitChanges.length}</div>
                        <div>Visuals: {evolution.visualChanges.length}</div>
                        <div>Metadata: {evolution.metadataChanges.length}</div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="text-sm font-medium text-gray-900">Timeline</div>
                      <div className="mt-1 space-y-1 text-sm text-gray-600">
                        <div>Started: {new Date(evolution.createdAt).toLocaleDateString()}</div>
                        {evolution.completedAt && (
                          <div>Completed: {new Date(evolution.completedAt).toLocaleDateString()}</div>
                        )}
                        <div>Version: {evolution.currentVersion}</div>
                      </div>
                    </div>
                  </div>

                  {/* Trait Changes */}
                  {evolution.traitChanges.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Trait Changes</h4>
                      <div className="space-y-2">
                        {evolution.traitChanges.slice(0, 5).map((change, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-blue-50 rounded">
                            <div>
                              <span className="text-sm font-medium text-blue-900">{change.traitName}</span>
                              <span className="ml-2 text-xs text-blue-700">({change.changeType})</span>
                            </div>
                            <div className="text-sm text-blue-800">
                              {change.oldValue} → {change.newValue}
                            </div>
                          </div>
                        ))}
                        {evolution.traitChanges.length > 5 && (
                          <div className="text-xs text-gray-500 text-center">
                            +{evolution.traitChanges.length - 5} more trait changes
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Visual Changes */}
                  {evolution.visualChanges.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Visual Changes</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {evolution.visualChanges.slice(0, 4).map((change, index) => (
                          <div key={index} className="bg-purple-50 rounded p-2">
                            <div className="text-xs font-medium text-purple-900">{change.elementType}</div>
                            <div className="text-xs text-purple-700">{change.transformationType}</div>
                            {change.newImageUrl && (
                              <img
                                src={change.newImageUrl}
                                alt={change.elementType}
                                className="w-full h-16 object-cover rounded mt-1"
                              />
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Metadata Changes */}
                  {evolution.metadataChanges.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Metadata Changes</h4>
                      <div className="space-y-1">
                        {evolution.metadataChanges.slice(0, 3).map((change, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-green-50 rounded text-sm">
                            <span className="font-medium text-green-900">{change.field}</span>
                            <span className="text-green-700">{change.changeType}</span>
                          </div>
                        ))}
                        {evolution.metadataChanges.length > 3 && (
                          <div className="text-xs text-gray-500 text-center">
                            +{evolution.metadataChanges.length - 3} more metadata changes
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Community Reaction */}
                  {evolution.communityReaction && (
                    <div className="bg-gray-50 rounded-lg p-3">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Community Reaction</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <span>👍</span>
                          <span>{evolution.communityReaction.likes}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span>👎</span>
                          <span>{evolution.communityReaction.dislikes}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span>💬</span>
                          <span>{evolution.communityReaction.comments}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span>🔄</span>
                          <span>{evolution.communityReaction.shares}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span>Sentiment:</span>
                          <span className={`font-medium ${
                            evolution.communityReaction.sentimentScore > 0.5 ? 'text-green-600' :
                            evolution.communityReaction.sentimentScore < -0.5 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {evolution.communityReaction.sentimentScore > 0.5 ? 'Positive' :
                             evolution.communityReaction.sentimentScore < -0.5 ? 'Negative' : 'Neutral'}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No evolution history</h3>
          <p className="mt-1 text-sm text-gray-500">
            Evolution history will appear here once evolutions are completed.
          </p>
        </div>
      )}

      {/* Metadata History */}
      {metadataHistory && metadataHistory.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Metadata Version History</h3>
          
          <div className="space-y-3">
            {metadataHistory.slice(0, 5).map((change, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    Version {change.version}
                  </div>
                  <div className="text-xs text-gray-600">
                    {change.field} • {new Date(change.changeTimestamp).toLocaleString()}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">{change.changeType}</span>
                  <button
                    onClick={() => handleRollbackMetadata(change.version)}
                    disabled={rollbackMetadataMutation.isPending}
                    className="text-xs text-blue-600 hover:text-blue-700 disabled:opacity-50"
                  >
                    Rollback
                  </button>
                </div>
              </div>
            ))}
            
            {metadataHistory.length > 5 && (
              <div className="text-center">
                <button className="text-sm text-blue-600 hover:text-blue-700">
                  View All {metadataHistory.length} Versions
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* History Statistics */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">History Statistics</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{evolutions.length}</div>
            <div className="text-sm text-gray-600">Total Evolutions</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {evolutions.filter(e => e.status === EvolutionStatus.COMPLETED).length}
            </div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {evolutions.filter(e => e.status === EvolutionStatus.FAILED).length}
            </div>
            <div className="text-sm text-gray-600">Failed</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {evolutions.filter(e => e.isReversible).length}
            </div>
            <div className="text-sm text-gray-600">Reversible</div>
          </div>
        </div>
      </div>
    </div>
  )
}
