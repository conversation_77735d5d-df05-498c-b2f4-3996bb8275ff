import { Controller, Get, Post, Delete, Body, Param } from '@nestjs/common';
import { 
  ResponseService, 
  RequirePermissions, 
  Permission, 
  Public 
} from '../../../../../shared';
import { ServiceDiscoveryService, ServiceRegistration } from './service-discovery.service';

/**
 * Service Discovery Controller
 * 
 * Provides REST API for service discovery management.
 * Allows registration, unregistration, and querying of services.
 */
@Controller('discovery')
export class ServiceDiscoveryController {
  constructor(
    private readonly serviceDiscovery: ServiceDiscoveryService,
    private readonly responseService: ResponseService
  ) {}

  /**
   * Get all registered services
   */
  @Get('services')
  @RequirePermissions(Permission.ADMIN)
  async getAllServices() {
    const services = this.serviceDiscovery.getAllServices();
    const servicesArray = Array.from(services.entries()).map(([name, instances]) => ({
      name,
      instances: instances.length,
      healthyInstances: instances.filter(i => i.health === 'healthy').length,
      instances_detail: instances,
    }));

    return this.responseService.success(servicesArray, 'Services retrieved successfully');
  }

  /**
   * Get healthy instances for a specific service
   */
  @Get('services/:serviceName/healthy')
  @Public() // Allow internal service calls
  async getHealthyInstances(@Param('serviceName') serviceName: string) {
    const instances = await this.serviceDiscovery.getHealthyInstances(serviceName);
    return this.responseService.success(instances, `Healthy instances for ${serviceName} retrieved`);
  }

  /**
   * Select an instance for a service
   */
  @Get('services/:serviceName/select')
  @Public() // Allow internal service calls
  async selectInstance(@Param('serviceName') serviceName: string) {
    const instance = await this.serviceDiscovery.selectInstance(serviceName);
    
    if (!instance) {
      return this.responseService.notFound('Healthy instance', serviceName);
    }

    return this.responseService.success(instance, `Instance selected for ${serviceName}`);
  }

  /**
   * Register a new service instance
   */
  @Post('services/register')
  @RequirePermissions(Permission.ADMIN)
  async registerService(@Body() registration: ServiceRegistration) {
    await this.serviceDiscovery.registerService(registration);
    return this.responseService.created(
      { serviceName: registration.name, url: registration.url },
      'Service registered successfully'
    );
  }

  /**
   * Unregister a service instance
   */
  @Delete('services/:serviceName/instances/:instanceId')
  @RequirePermissions(Permission.ADMIN)
  async unregisterService(
    @Param('serviceName') serviceName: string,
    @Param('instanceId') instanceId: string
  ) {
    await this.serviceDiscovery.unregisterService(serviceName, instanceId);
    return this.responseService.deleted('Service instance unregistered successfully');
  }

  /**
   * Health check endpoint for the service discovery itself
   */
  @Get('health')
  @Public()
  async health() {
    const services = this.serviceDiscovery.getAllServices();
    const totalInstances = Array.from(services.values()).reduce((sum, instances) => sum + instances.length, 0);
    const healthyInstances = Array.from(services.values())
      .reduce((sum, instances) => sum + instances.filter(i => i.health === 'healthy').length, 0);

    return this.responseService.success({
      status: 'healthy',
      totalServices: services.size,
      totalInstances,
      healthyInstances,
      timestamp: new Date().toISOString(),
    }, 'Service discovery is healthy');
  }
}
