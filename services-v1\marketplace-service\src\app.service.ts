import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);
  private readonly startTime = Date.now();

  constructor(private readonly configService: ConfigService) {}

  getServiceInfo() {
    const info = {
      service: 'Marketplace Service',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      port: this.configService.get<number>('SERVICE_PORT', 3006),
      features: {
        nftTrading: true,
        auctions: true,
        offers: true,
        payments: true,
        listings: true,
        transactions: true,
        healthChecks: true,
        swagger: this.configService.get<string>('NODE_ENV') === 'development',
        cors: true,
        validation: true,
        authentication: true,
      },
      marketplace: {
        feePercentage: this.configService.get<number>('MARKETPLACE_FEE_PERCENTAGE', 2.5),
        minListingPrice: this.configService.get<number>('MIN_LISTING_PRICE', 0.001),
        maxListingPrice: this.configService.get<number>('MAX_LISTING_PRICE', 1000000),
        auctionMinDuration: this.configService.get<number>('AUCTION_MIN_DURATION', 3600),
        auctionMaxDuration: this.configService.get<number>('AUCTION_MAX_DURATION', 604800),
        bidIncrementPercentage: this.configService.get<number>('BID_INCREMENT_PERCENTAGE', 5),
      },
      payments: {
        processor: this.configService.get<string>('PAYMENT_PROCESSOR', 'stripe'),
        cryptoEnabled: this.configService.get<boolean>('CRYPTO_PAYMENT_ENABLED', true),
      },
      externalServices: {
        userService: this.configService.get<string>('USER_SERVICE_URL'),
        projectService: this.configService.get<string>('PROJECT_SERVICE_URL'),
        nftService: this.configService.get<string>('NFT_GENERATION_SERVICE_URL'),
        blockchainService: this.configService.get<string>('BLOCKCHAIN_SERVICE_URL'),
        analyticsService: this.configService.get<string>('ANALYTICS_SERVICE_URL'),
        profileService: this.configService.get<string>('PROFILE_ANALYSIS_SERVICE_URL'),
        notificationService: this.configService.get<string>('NOTIFICATION_SERVICE_URL'),
      },
    };

    this.logger.log('Service info retrieved', { 
      uptime: info.uptime,
      environment: info.environment 
    });

    return info;
  }

  getStatus() {
    const memoryUsage = process.memoryUsage();
    
    const status = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
      },
      cpu: {
        usage: process.cpuUsage(),
      },
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      nodeVersion: process.version,
      platform: process.platform,
      marketplace: {
        activeListings: 150, // Mock - replace with actual database query
        activeAuctions: 25, // Mock - replace with actual database query
        totalTransactions: 1250, // Mock - replace with actual database query
        totalVolume: '125.75 ETH', // Mock - replace with actual calculation
        averagePrice: '0.85 ETH', // Mock - replace with actual calculation
      },
    };

    this.logger.debug('Service status checked', {
      uptime: status.uptime,
      memoryUsed: Math.round(status.memory.used / 1024 / 1024) + 'MB',
    });

    return status;
  }
}
