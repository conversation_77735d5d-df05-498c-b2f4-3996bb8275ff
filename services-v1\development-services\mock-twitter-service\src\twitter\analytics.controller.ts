import { Controller, Get, Param } from '@nestjs/common';
import { TwitterService } from './twitter.service';

@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly twitterService: TwitterService) {}

  @Get(':userId')
  async getUserAnalytics(@Param('userId') userId: string) {
    return {
      status: 'success',
      data: {
        userId,
        engagementRate: 4.2,
        averageLikes: 25,
        averageRetweets: 8,
        averageReplies: 3,
        influenceScore: 67
      }
    };
  }
}
