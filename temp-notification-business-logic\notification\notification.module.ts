import { Module } from '@nestjs/common';

// Notification services
import { NotificationManagementService } from './services/notification-management.service';
import { EventNotificationService } from './services/event-notification.service';
import { NotificationAnalyticsService } from './services/notification-analytics.service';

@Module({
  providers: [
    NotificationManagementService,
    EventNotificationService,
    NotificationAnalyticsService,
  ],
  exports: [
    NotificationManagementService,
    EventNotificationService,
    NotificationAnalyticsService,
  ],
})
export class NotificationModule {}
