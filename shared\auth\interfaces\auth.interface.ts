/**
 * Core Authentication Interfaces
 * Defines the standard authentication structure for all services
 */

/**
 * Authentication result interface
 */
export interface AuthResult {
  success: boolean;
  data?: any;
  error?: string;
  statusCode?: number;
}

/**
 * Token validation result
 */
export interface TokenValidationResult {
  success: boolean;
  data?: {
    user: AuthenticatedUser;
    session?: UserSession;
    tokenInfo?: TokenInfo;
  };
  error?: string;
}

/**
 * Token information
 */
export interface TokenInfo {
  tokenId: string;
  type: TokenType;
  issuedAt: Date;
  expiresAt: Date;
  issuer: string;
  audience: string[];
  scopes: string[];
}

/**
 * Token types
 */
export enum TokenType {
  ACCESS_TOKEN = 'access_token',
  REFRESH_TOKEN = 'refresh_token',
  API_KEY = 'api_key',
  SESSION_TOKEN = 'session_token',
}

/**
 * Authentication context
 */
export interface AuthContext {
  user: AuthenticatedUser;
  session?: UserSession;
  permissions: string[];
  roles: string[];
  deviceInfo?: DeviceInfo;
  requestInfo: RequestInfo;
}

/**
 * Request information
 */
export interface RequestInfo {
  ipAddress: string;
  userAgent: string;
  requestId: string;
  timestamp: Date;
  endpoint: string;
  method: string;
}

/**
 * Device information
 */
export interface DeviceInfo {
  deviceId?: string;
  deviceType: DeviceType;
  platform?: string;
  browser?: string;
  version?: string;
  isTrusted: boolean;
}

/**
 * Device types
 */
export enum DeviceType {
  WEB = 'web',
  MOBILE = 'mobile',
  DESKTOP = 'desktop',
  API = 'api',
  UNKNOWN = 'unknown',
}

/**
 * Authenticated user interface
 */
export interface AuthenticatedUser {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  isVerified: boolean;
  lastLoginAt?: Date;
  sessionId?: string;
  profilePicture?: string;
  preferences?: UserPreferences;
  metadata?: Record<string, any>;
}

/**
 * User preferences
 */
export interface UserPreferences {
  language: string;
  timezone: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationPreferences;
}

/**
 * Notification preferences
 */
export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  marketing: boolean;
}

/**
 * User session interface
 */
export interface UserSession {
  sessionId: string;
  userId: string;
  deviceInfo?: DeviceInfo;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  expiresAt: Date;
  lastActivityAt: Date;
  isActive: boolean;
  metadata?: Record<string, any>;
}

/**
 * Authentication configuration
 */
export interface AuthConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  jwtRefreshExpiresIn: string;
  sessionTimeout: number;
  maxConcurrentSessions: number;
  enableMFA: boolean;
  enableSessionTracking: boolean;
  enableAuditLogging: boolean;
  trustedIPs: string[];
  allowedOrigins: string[];
}

/**
 * Authentication service interface
 */
export interface IAuthService {
  /**
   * Validate authentication token
   */
  validateToken(token: string, context?: any): Promise<TokenValidationResult>;

  /**
   * Generate authentication token
   */
  generateToken(user: AuthenticatedUser, type: TokenType): Promise<string>;

  /**
   * Refresh authentication token
   */
  refreshToken(refreshToken: string): Promise<AuthResult>;

  /**
   * Revoke authentication token
   */
  revokeToken(token: string): Promise<boolean>;

  /**
   * Get user by ID
   */
  getUserById(userId: string): Promise<AuthenticatedUser | null>;

  /**
   * Check if user has permission
   */
  hasPermission(user: AuthenticatedUser, permission: string): Promise<boolean>;

  /**
   * Check if user has any of the permissions
   */
  hasAnyPermission(user: AuthenticatedUser, permissions: string[]): Promise<boolean>;

  /**
   * Check if user has role
   */
  hasRole(user: AuthenticatedUser, role: string): Promise<boolean>;
}

/**
 * Permission service interface
 */
export interface IPermissionService {
  /**
   * Get user permissions
   */
  getUserPermissions(userId: string): Promise<string[]>;

  /**
   * Get role permissions
   */
  getRolePermissions(role: string): Promise<string[]>;

  /**
   * Check if user has permission
   */
  hasPermission(user: AuthenticatedUser, permission: string): Promise<boolean>;

  /**
   * Check if user has any permission
   */
  hasAnyPermission(user: AuthenticatedUser, permissions: string[]): Promise<boolean>;

  /**
   * Check if user has all permissions
   */
  hasAllPermissions(user: AuthenticatedUser, permissions: string[]): Promise<boolean>;

  /**
   * Grant permission to user
   */
  grantPermission(userId: string, permission: string): Promise<boolean>;

  /**
   * Revoke permission from user
   */
  revokePermission(userId: string, permission: string): Promise<boolean>;
}

/**
 * Session service interface
 */
export interface ISessionService {
  /**
   * Create user session
   */
  createSession(user: AuthenticatedUser, deviceInfo: DeviceInfo, requestInfo: RequestInfo): Promise<UserSession>;

  /**
   * Get user session
   */
  getSession(sessionId: string): Promise<UserSession | null>;

  /**
   * Update session activity
   */
  updateSessionActivity(sessionId: string): Promise<boolean>;

  /**
   * Terminate session
   */
  terminateSession(sessionId: string): Promise<boolean>;

  /**
   * Terminate all user sessions
   */
  terminateAllUserSessions(userId: string): Promise<boolean>;

  /**
   * Get active user sessions
   */
  getActiveSessions(userId: string): Promise<UserSession[]>;

  /**
   * Clean expired sessions
   */
  cleanExpiredSessions(): Promise<number>;
}

/**
 * Audit service interface
 */
export interface IAuditService {
  /**
   * Log authentication event
   */
  logAuthEvent(event: AuthEvent): Promise<void>;

  /**
   * Log permission check
   */
  logPermissionCheck(userId: string, permission: string, granted: boolean, context?: any): Promise<void>;

  /**
   * Log security event
   */
  logSecurityEvent(event: SecurityEvent): Promise<void>;
}

/**
 * Authentication event
 */
export interface AuthEvent {
  type: AuthEventType;
  userId?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Authentication event types
 */
export enum AuthEventType {
  LOGIN = 'login',
  LOGOUT = 'logout',
  TOKEN_REFRESH = 'token_refresh',
  TOKEN_REVOKE = 'token_revoke',
  PASSWORD_CHANGE = 'password_change',
  PERMISSION_GRANT = 'permission_grant',
  PERMISSION_REVOKE = 'permission_revoke',
  ROLE_ASSIGN = 'role_assign',
  ROLE_REVOKE = 'role_revoke',
}

/**
 * Security event
 */
export interface SecurityEvent {
  type: SecurityEventType;
  severity: SecuritySeverity;
  userId?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  description: string;
  metadata?: Record<string, any>;
}

/**
 * Security event types
 */
export enum SecurityEventType {
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  INVALID_TOKEN = 'invalid_token',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  PERMISSION_DENIED = 'permission_denied',
  ACCOUNT_LOCKED = 'account_locked',
  SECURITY_VIOLATION = 'security_violation',
}

/**
 * Security severity levels
 */
export enum SecuritySeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Standard permissions
 */
export enum Permission {
  // User permissions
  USER_READ = 'user:read',
  USER_CREATE = 'user:create',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',

  // Profile permissions
  PROFILE_ANALYZE = 'profile:analyze',
  PROFILE_READ = 'profile:read',

  // NFT permissions
  NFT_CREATE = 'nft:create',
  NFT_READ = 'nft:read',
  NFT_UPDATE = 'nft:update',
  NFT_DELETE = 'nft:delete',

  // Admin permissions
  ADMIN = 'admin',
  SYSTEM_CONFIG = 'system:config',
}

/**
 * Standard roles
 */
export enum Role {
  USER = 'user',
  PREMIUM_USER = 'premium_user',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

/**
 * JWT Payload interface
 */
export interface JWTPayload {
  sub: string; // User ID
  email: string;
  username?: string;
  roles: string[];
  permissions: string[];
  sessionId?: string;
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

/**
 * Auth Guard Options
 */
export interface AuthGuardOptions {
  optional?: boolean;
  roles?: string[];
  permissions?: string[];
  requireAll?: boolean;
}
