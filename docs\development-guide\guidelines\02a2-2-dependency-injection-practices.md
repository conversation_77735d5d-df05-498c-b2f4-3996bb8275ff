# 💉 Dependency Injection Best Practices

## Overview
Standardized dependency injection patterns for maintainable and testable code.

## Constructor Injection Patterns

### Standard Service Constructor
```typescript
@Injectable()
export class UserService {
  constructor(
    // 1. Primary dependencies (repositories)
    private readonly userRepository: UserRepository,
    
    // 2. Shared services (logging, config, etc.)
    private readonly logger: LoggerService,
    private readonly configService: ConfigService,
    
    // 3. External services (messaging, cache, etc.)
    private readonly messagingService: MessagingService,
    private readonly cacheService: CacheService,
    
    // 4. Other feature services (minimal)
    private readonly notificationService: NotificationService,
    
    // 5. Custom injected tokens
    @Inject('USER_CONFIG')
    private readonly userConfig: UserConfig,
  ) {
    // Initialize logger context
    this.logger.setContext(UserService.name);
  }
}
```

### Repository Constructor Pattern
```typescript
@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
    
    private readonly logger: LoggerService,
    
    // Optional: Query builder for complex queries
    private readonly dataSource: DataSource,
  ) {
    this.logger.setContext(UserRepository.name);
  }
}
```

### Controller Constructor Pattern
```typescript
@Controller('users')
export class UserController {
  constructor(
    // Primary service only
    private readonly userService: UserService,
    
    // Shared services if needed
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(UserController.name);
  }
}
```

## Interface-Based Injection

### Define Interfaces
```typescript
// interfaces/user-repository.interface.ts
export interface IUserRepository {
  findById(id: string): Promise<User | null>;
  create(userData: CreateUserDto): Promise<User>;
  update(id: string, userData: UpdateUserDto): Promise<User>;
  delete(id: string): Promise<void>;
}

// interfaces/user-service.interface.ts
export interface IUserService {
  createUser(userData: CreateUserDto): Promise<User>;
  getUserById(id: string): Promise<User>;
  updateUser(id: string, userData: UpdateUserDto): Promise<User>;
  deleteUser(id: string): Promise<void>;
}
```

### Implement Interfaces
```typescript
@Injectable()
export class UserRepository implements IUserRepository {
  // Implementation
}

@Injectable()
export class UserService implements IUserService {
  constructor(
    @Inject('USER_REPOSITORY')
    private readonly userRepository: IUserRepository,
  ) {}
}
```

### Module Provider Configuration
```typescript
@Module({
  providers: [
    // Interface-based providers
    {
      provide: 'USER_REPOSITORY',
      useClass: UserRepository,
    },
    {
      provide: 'USER_SERVICE',
      useClass: UserService,
    },
    
    // Direct class providers
    UserController,
  ],
  exports: ['USER_SERVICE', 'USER_REPOSITORY'],
})
export class UserModule {}
```

## Custom Provider Patterns

### Configuration Providers
```typescript
// config/user.config.ts
export const userConfig = {
  maxLoginAttempts: 5,
  passwordResetExpiry: 3600, // 1 hour
  sessionTimeout: 86400, // 24 hours
};

// In module
@Module({
  providers: [
    {
      provide: 'USER_CONFIG',
      useValue: userConfig,
    },
  ],
})
```

### Factory Providers
```typescript
@Module({
  providers: [
    {
      provide: 'DATABASE_CONNECTION',
      useFactory: (configService: ConfigService) => {
        return createDatabaseConnection(configService.getDatabaseConfig());
      },
      inject: [ConfigService],
    },
  ],
})
```

### Async Providers
```typescript
@Module({
  providers: [
    {
      provide: 'ASYNC_SERVICE',
      useFactory: async (configService: ConfigService) => {
        const config = await configService.getAsyncConfig();
        return new AsyncService(config);
      },
      inject: [ConfigService],
    },
  ],
})
```

## Testing with Dependency Injection

### Mock Providers for Testing
```typescript
describe('UserService', () => {
  let service: UserService;
  let repository: jest.Mocked<IUserRepository>;

  beforeEach(async () => {
    const mockRepository = {
      findById: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    const module = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: 'USER_REPOSITORY',
          useValue: mockRepository,
        },
        {
          provide: LoggerService,
          useValue: {
            setContext: jest.fn(),
            log: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get('USER_REPOSITORY');
  });
});
```

## Common Anti-Patterns to Avoid

### ❌ Circular Dependencies
```typescript
// BAD: Service A depends on Service B, Service B depends on Service A
@Injectable()
export class ServiceA {
  constructor(private serviceB: ServiceB) {}
}

@Injectable()
export class ServiceB {
  constructor(private serviceA: ServiceA) {}
}
```

### ❌ Too Many Dependencies
```typescript
// BAD: Service with too many dependencies
@Injectable()
export class OverloadedService {
  constructor(
    private dep1: Dep1,
    private dep2: Dep2,
    private dep3: Dep3,
    private dep4: Dep4,
    private dep5: Dep5,
    private dep6: Dep6,
    private dep7: Dep7,
    // ... more dependencies
  ) {}
}
```

### ❌ Direct Entity Injection
```typescript
// BAD: Injecting entities directly
@Injectable()
export class BadService {
  constructor(
    @InjectRepository(User)
    private userEntity: Repository<User>, // Should use repository pattern
  ) {}
}
```

## AI Agent Implementation Rules

### Dependency Order
1. Primary business dependencies (repositories, core services)
2. Shared infrastructure (logging, config, messaging)
3. External services (third-party integrations)
4. Other feature services (minimal, avoid circular deps)
5. Custom injected tokens

### Validation Checklist
- [ ] Constructor parameters follow standard order
- [ ] All dependencies are properly typed
- [ ] Interface-based injection is used where appropriate
- [ ] No circular dependencies exist
- [ ] Logger context is set in constructor
- [ ] Custom providers use descriptive tokens
- [ ] Testing mocks are properly structured

### Best Practices
- Use readonly for all injected dependencies
- Set logger context in constructor
- Minimize cross-feature dependencies
- Use interfaces for better testability
- Keep constructors focused on dependency injection only
