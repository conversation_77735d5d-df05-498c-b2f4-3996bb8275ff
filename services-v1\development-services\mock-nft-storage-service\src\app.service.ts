import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo() {
    return {
      name: 'Mock NFT Storage Service',
      version: '1.0.0',
      description: 'Mock NFT Storage API for Social NFT Platform Development',
      port: 3022,
      environment: process.env.NODE_ENV || 'development',
      type: 'mock',
      endpoints: {
        health: '/health',
        docs: '/api/docs',
        metadata: '/metadata',
        assets: '/assets',
        ipfs: '/ipfs'
      },
      timestamp: new Date().toISOString()
    };
  }

  healthCheck() {
    return {
      status: 'healthy',
      service: 'mock-nft-storage-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0'
    };
  }
}
