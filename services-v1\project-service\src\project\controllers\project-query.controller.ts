import {
  Controller,
  Get,
  Param,
  Query,
  <PERSON>ers,
  Res,
  HttpStatus,
  ParseBoolPipe,
  ParseIntPipe,
  DefaultValuePipe,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiHeader } from '@nestjs/swagger';
import { Response } from 'express';
import { ProjectQueryService, ProjectListQuery, ProjectStatsQuery } from '../services/project-query.service';

@ApiTags('Projects')
@Controller('projects')
export class ProjectQueryController {
  private readonly logger = new Logger(ProjectQueryController.name);

  constructor(private readonly projectQueryService: ProjectQueryService) {}

  @Get('search')
  @ApiOperation({
    summary: 'Search projects with advanced filtering',
    description: 'Search projects by name, description, category with filters'
  })
  @ApiQuery({ name: 'q', description: 'Search term' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by category' })
  @ApiQuery({ name: 'blockchain', required: false, description: 'Filter by blockchain network' })
  @ApiQuery({ name: 'minValue', required: false, description: 'Minimum market value' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  async searchProjects(
    @Query('q') searchTerm: string,
    @Query('category') category: string,
    @Query('blockchain') blockchainNetwork: string,
    @Query('minValue') minMarketValue: number,
    @Query('minParticipants') minParticipants: number,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Searching projects with term: ${searchTerm}`);
      
      const filters = {
        category,
        blockchainNetwork,
        minMarketValue,
        minParticipants,
      };

      const result = await this.projectQueryService.searchProjects(searchTerm, filters);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Project search failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('stats')
  @ApiOperation({
    summary: 'Get project statistics and analytics',
    description: 'Retrieve aggregated statistics for dashboard and monitoring'
  })
  @ApiQuery({ name: 'ownerId', required: false, description: 'Filter by owner ID' })
  @ApiQuery({ name: 'timeRange', required: false, enum: ['7d', '30d', '90d', 'all'], description: 'Time range for statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getProjectStats(
    @Query('ownerId') ownerId: string,
    @Query('timeRange') timeRange: '7d' | '30d' | '90d' | 'all',
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting project stats for owner: ${ownerId}, timeRange: ${timeRange}`);
      
      const query: ProjectStatsQuery = { ownerId, timeRange };
      const result = await this.projectQueryService.getProjectStats(query);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Project stats retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('owner/:ownerId')
  @ApiOperation({
    summary: 'Get projects by owner',
    description: 'Retrieve all projects owned by a specific user'
  })
  @ApiParam({ name: 'ownerId', description: 'Owner user ID' })
  @ApiQuery({ name: 'includeConfig', required: false, type: Boolean, description: 'Include full configuration' })
  @ApiResponse({ status: 200, description: 'Owner projects retrieved successfully' })
  async getProjectsByOwner(
    @Param('ownerId') ownerId: string,
    @Query('includeConfig', new DefaultValuePipe(false), ParseBoolPipe) includeConfig: boolean,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting projects for owner: ${ownerId}, includeConfig: ${includeConfig}`);
      
      const result = await this.projectQueryService.getProjectsByOwner(ownerId, includeConfig);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Owner projects retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get projects list with filtering and pagination',
    description: 'Retrieve projects with support for filtering, search, and pagination'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by category' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status (default: active)' })
  @ApiQuery({ name: 'ownerId', required: false, description: 'Filter by owner ID' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'blockchain', required: false, description: 'Filter by blockchain network' })
  @ApiQuery({ name: 'isPublic', required: false, type: Boolean, description: 'Filter by public status (default: true)' })
  @ApiResponse({ status: 200, description: 'Projects list retrieved successfully' })
  async getProjects(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('category') category: string,
    @Query('status', new DefaultValuePipe('active')) status: string,
    @Query('ownerId') ownerId: string,
    @Query('search') search: string,
    @Query('blockchain') blockchainNetwork: string,
    @Query('isPublic', new DefaultValuePipe(true), ParseBoolPipe) isPublic: boolean,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting projects list - page: ${page}, limit: ${limit}`);
      
      const query: ProjectListQuery = {
        page,
        limit,
        category,
        status,
        ownerId,
        search,
        blockchainNetwork,
        isPublic,
      };

      const result = await this.projectQueryService.getProjects(query);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Projects list retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get project by ID with complete details',
    description: 'Retrieve project details with optional configuration for owners'
  })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiQuery({ name: 'includeConfig', required: false, type: Boolean, description: 'Include full configuration (for owners)' })
  @ApiResponse({ status: 200, description: 'Project retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Request correlation ID' })
  async getProjectById(
    @Param('id') id: string,
    @Query('includeConfig', new DefaultValuePipe(false), ParseBoolPipe) includeConfig: boolean,
    @Headers('x-correlation-id') correlationId: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting project by ID: ${id}, includeConfig: ${includeConfig}`);
      
      const result = await this.projectQueryService.getProjectById(id, includeConfig);
      return res.status(HttpStatus.OK).json({
        ...result,
        correlationId,
      });
    } catch (error) {
      this.logger.error(`Project retrieval failed for ID ${id}: ${error.message}`, error.stack);
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
        correlationId,
      });
    }
  }
}
