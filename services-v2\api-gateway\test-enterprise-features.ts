/**
 * Enterprise API Gateway V2 - Comprehensive Feature Test
 * 
 * This test validates all enterprise features implemented in Phase 2:
 * - Service Discovery
 * - Circuit Breaker
 * - Load Balancing
 * - Caching
 * - Rate Limiting
 * - Monitoring Dashboard
 */

import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from './src/app.module';
import { ServiceDiscoveryService } from './src/service-discovery/service-discovery.service';
import { CircuitBreakerService } from './src/circuit-breaker/circuit-breaker.service';
import { LoadBalancerService } from './src/load-balancer/load-balancer.service';
import { CacheService } from './src/cache/cache.service';
import { RateLimitService } from './src/rate-limit/rate-limit.service';
import { ProxyV2Service } from './src/proxy-v2/proxy-v2.service';

describe('Enterprise API Gateway V2 - Phase 2 Features', () => {
  let app: TestingModule;
  let serviceDiscovery: ServiceDiscoveryService;
  let circuitBreaker: CircuitBreakerService;
  let loadBalancer: LoadBalancerService;
  let cache: CacheService;
  let rateLimit: RateLimitService;
  let proxy: ProxyV2Service;

  beforeAll(async () => {
    app = await Test.createTestingModule({
      imports: [AppV2Module],
    }).compile();

    serviceDiscovery = app.get<ServiceDiscoveryService>(ServiceDiscoveryService);
    circuitBreaker = app.get<CircuitBreakerService>(CircuitBreakerService);
    loadBalancer = app.get<LoadBalancerService>(LoadBalancerService);
    cache = app.get<CacheService>(CacheService);
    rateLimit = app.get<RateLimitService>(RateLimitService);
    proxy = app.get<ProxyV2Service>(ProxyV2Service);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Service Discovery', () => {
    it('should register and discover services', async () => {
      // Register a test service
      await serviceDiscovery.registerService({
        name: 'test-service',
        url: 'http://localhost:3001',
        metadata: {
          version: '1.0.0',
          capabilities: ['test'],
          weight: 1,
        },
      });

      // Get all services
      const services = serviceDiscovery.getAllServices();
      expect(services.has('test-service')).toBe(true);

      // Get healthy instances
      const healthyInstances = await serviceDiscovery.getHealthyInstances('test-service');
      expect(Array.isArray(healthyInstances)).toBe(true);

      console.log('✅ Service Discovery: Registration and discovery working');
    });

    it('should select instances with load balancing', async () => {
      const instance = await serviceDiscovery.selectInstance('test-service', 'round-robin');
      expect(instance).toBeDefined();
      expect(instance?.name).toBe('test-service');

      console.log('✅ Service Discovery: Instance selection working');
    });
  });

  describe('Circuit Breaker', () => {
    it('should execute operations with circuit breaker protection', async () => {
      const testOperation = async () => {
        return { success: true, data: 'test-data' };
      };

      const result = await circuitBreaker.execute(
        'test-service',
        testOperation,
        {
          failureThreshold: 5,
          resetTimeout: 60000,
          bulkheadEnabled: true,
          maxConcurrentRequests: 10,
        }
      );

      expect(result.success).toBe(true);
      expect(result.data).toBe('test-data');

      console.log('✅ Circuit Breaker: Operation execution working');
    });

    it('should track circuit breaker statistics', async () => {
      const stats = circuitBreaker.getCircuitStats('test-service');
      expect(stats).toBeDefined();
      expect(stats?.state).toBe('closed');
      expect(stats?.totalRequests).toBeGreaterThan(0);

      console.log('✅ Circuit Breaker: Statistics tracking working');
    });

    it('should provide circuit breaker health status', async () => {
      const isHealthy = circuitBreaker.isCircuitHealthy('test-service');
      expect(isHealthy).toBe(true);

      console.log('✅ Circuit Breaker: Health status working');
    });
  });

  describe('Load Balancer', () => {
    it('should select instances using different strategies', async () => {
      const instances = [
        {
          id: 'instance-1',
          name: 'test-service',
          url: 'http://localhost:3001',
          health: 'healthy' as const,
          lastHealthCheck: new Date(),
          metadata: { version: '1.0.0', region: 'us-east-1', capabilities: ['test'], weight: 1 },
        },
        {
          id: 'instance-2',
          name: 'test-service',
          url: 'http://localhost:3002',
          health: 'healthy' as const,
          lastHealthCheck: new Date(),
          metadata: { version: '1.0.0', region: 'us-east-1', capabilities: ['test'], weight: 2 },
        },
      ];

      // Test round-robin
      const roundRobinInstance = await loadBalancer.selectInstance(instances, 'round-robin');
      expect(roundRobinInstance).toBeDefined();

      // Test weighted
      const weightedInstance = await loadBalancer.selectInstance(instances, 'weighted');
      expect(weightedInstance).toBeDefined();

      // Test random
      const randomInstance = await loadBalancer.selectInstance(instances, 'random');
      expect(randomInstance).toBeDefined();

      console.log('✅ Load Balancer: Multiple strategies working');
    });

    it('should track connection statistics', async () => {
      loadBalancer.recordConnectionStart('instance-1');
      loadBalancer.recordConnectionEnd('instance-1');

      const stats = loadBalancer.getInstanceConnectionStats('instance-1');
      expect(stats).toBeDefined();
      expect(stats?.totalConnections).toBeGreaterThan(0);

      console.log('✅ Load Balancer: Connection tracking working');
    });
  });

  describe('Cache Service', () => {
    it('should store and retrieve cached data', async () => {
      const testData = { id: 1, name: 'Test Data', timestamp: new Date() };
      
      // Set cache entry
      await cache.set('test-key', testData, 300); // 5 minutes TTL

      // Get cache entry
      const cachedData = await cache.get('test-key');
      expect(cachedData).toEqual(testData);

      console.log('✅ Cache: Store and retrieve working');
    });

    it('should handle cache expiration', async () => {
      // Set cache entry with short TTL
      await cache.set('short-ttl-key', 'test-value', 1); // 1 second TTL

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 1100));

      // Try to get expired entry
      const expiredData = await cache.get('short-ttl-key');
      expect(expiredData).toBeNull();

      console.log('✅ Cache: TTL expiration working');
    });

    it('should provide cache statistics', async () => {
      const stats = cache.getStats();
      expect(stats).toBeDefined();
      expect(typeof stats.hitRate).toBe('number');
      expect(typeof stats.totalEntries).toBe('number');

      console.log('✅ Cache: Statistics tracking working');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const config = {
        windowMs: 60000, // 1 minute
        maxRequests: 5,
      };

      // Make requests within limit
      for (let i = 0; i < 5; i++) {
        const result = await rateLimit.checkLimit('test-user', config);
        expect(result.allowed).toBe(true);
        expect(result.remainingRequests).toBe(5 - i - 1);
      }

      // Exceed limit
      const exceededResult = await rateLimit.checkLimit('test-user', config);
      expect(exceededResult.allowed).toBe(false);
      expect(exceededResult.remainingRequests).toBe(0);

      console.log('✅ Rate Limiting: Limit enforcement working');
    });

    it('should provide rate limit statistics', async () => {
      const stats = rateLimit.getStats();
      expect(stats).toBeDefined();
      expect(typeof stats.totalEntries).toBe('number');
      expect(typeof stats.blockedEntries).toBe('number');

      console.log('✅ Rate Limiting: Statistics tracking working');
    });
  });

  describe('Proxy Service V2', () => {
    it('should provide proxy statistics', async () => {
      const stats = await proxy.getProxyStats();
      expect(stats).toBeDefined();
      expect(typeof stats.totalServices).toBe('number');
      expect(typeof stats.healthyServices).toBe('number');

      console.log('✅ Proxy V2: Statistics working');
    });
  });

  describe('Integration Test', () => {
    it('should demonstrate end-to-end enterprise features', async () => {
      console.log('\n🚀 Running End-to-End Enterprise Features Test...\n');

      // 1. Service Discovery
      console.log('1. Testing Service Discovery...');
      const services = serviceDiscovery.getAllServices();
      console.log(`   - Registered services: ${services.size}`);

      // 2. Circuit Breaker
      console.log('2. Testing Circuit Breaker...');
      const circuits = circuitBreaker.getAllCircuitStats();
      console.log(`   - Active circuits: ${circuits.size}`);

      // 3. Load Balancer
      console.log('3. Testing Load Balancer...');
      const lbHealth = loadBalancer.getHealthStatus();
      console.log(`   - Total instances: ${lbHealth.totalInstances}`);

      // 4. Cache
      console.log('4. Testing Cache...');
      const cacheStats = cache.getStats();
      console.log(`   - Cache entries: ${cacheStats.totalEntries}`);
      console.log(`   - Hit rate: ${cacheStats.hitRate.toFixed(2)}%`);

      // 5. Rate Limiting
      console.log('5. Testing Rate Limiting...');
      const rateLimitStats = rateLimit.getStats();
      console.log(`   - Rate limit entries: ${rateLimitStats.totalEntries}`);

      // 6. Proxy
      console.log('6. Testing Proxy V2...');
      const proxyStats = await proxy.getProxyStats();
      console.log(`   - Healthy services: ${proxyStats.healthyServices}/${proxyStats.totalServices}`);

      console.log('\n✅ All Enterprise Features Working Successfully!\n');

      // Verify all components are operational
      expect(services.size).toBeGreaterThanOrEqual(0);
      expect(circuits.size).toBeGreaterThanOrEqual(0);
      expect(lbHealth.status).toBe('healthy');
      expect(cacheStats).toBeDefined();
      expect(rateLimitStats).toBeDefined();
      expect(proxyStats).toBeDefined();
    });
  });
});

// Run the test if this file is executed directly
if (require.main === module) {
  console.log('🧪 Starting Enterprise API Gateway V2 Feature Tests...\n');
  
  // This would be run with Jest in a real environment
  console.log('To run these tests, use: npm test test-enterprise-features.ts');
  console.log('\nTest Coverage:');
  console.log('✅ Service Discovery & Health Management');
  console.log('✅ Circuit Breaker Pattern');
  console.log('✅ Load Balancing & Connection Management');
  console.log('✅ Multi-Level Caching');
  console.log('✅ Advanced Rate Limiting');
  console.log('✅ Enhanced Proxy Service V2');
  console.log('✅ End-to-End Integration');
}
