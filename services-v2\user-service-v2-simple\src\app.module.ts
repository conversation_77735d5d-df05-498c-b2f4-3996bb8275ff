/**
 * User Service V2 Simple - App Module
 * 
 * Simplified implementation without shared infrastructure dependencies
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { TerminusModule } from '@nestjs/terminus';

// Core modules
import { PrismaModule } from './prisma/prisma.module';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env'],
    }),
    
    // JWT
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET || 'dev-jwt-secret-change-in-production',
      signOptions: { 
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      },
    }),

    // Database
    PrismaModule,

    // Health Checks
    TerminusModule,
    HealthModule,
    
    // Business Logic Modules
    UsersModule,
    AuthModule,
  ],
})
export class AppModule {
  constructor() {
    console.log('🏗️ User Service V2 Simple initialized');
    console.log('🗄️ Database: user_service_v2 (Database Per Service Pattern)');
    console.log('🔧 Simplified implementation for testing');
  }
}
