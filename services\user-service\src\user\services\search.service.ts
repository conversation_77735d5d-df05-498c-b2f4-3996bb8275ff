import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../common/services/cache.service';
import { AuditService } from '../../common/services/audit.service';
import { RBACService } from './rbac.service';
import { 
  SearchQueryDto,
  AutocompleteQueryDto,
  SearchResultsDto,
  SearchResultItemDto,
  SearchFacetDto,
  SearchSuggestionDto,
  SearchAnalyticsDto,
  SearchType,
  SearchFilterDto,
  FilterOperator,
  SortOrder 
} from '../dto/search.dto';
import { Permission } from '../dto/rbac.dto';
import { RequestContext } from '../interfaces/request-context.interface';

export interface SearchResult {
  success: boolean;
  data?: SearchResultsDto;
  error?: string;
  message?: string;
}

export interface AutocompleteResult {
  success: boolean;
  data?: SearchSuggestionDto[];
  error?: string;
  message?: string;
}

@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
    private readonly auditService: AuditService,
    private readonly rbacService: RBACService,
  ) {}

  /**
   * Perform advanced search across platform entities
   */
  async search(searchQuery: SearchQueryDto, context: RequestContext): Promise<SearchResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Performing search: "${searchQuery.query}" type: ${searchQuery.type}`, { correlationId });

      // Check permissions
      const canSearch = await this.rbacService.hasPermission(context.userId, Permission.USER_READ);
      if (!canSearch) {
        return { success: false, error: 'Insufficient permissions to perform search' };
      }

      // Try cache first for common searches
      const cacheKey = this.generateSearchCacheKey(searchQuery);
      let cachedResults = await this.cacheService.get<SearchResultsDto>(cacheKey);

      if (cachedResults) {
        this.logger.log(`Search cache hit for query: "${searchQuery.query}"`, { correlationId });
        return { success: true, data: cachedResults };
      }

      // Perform search based on type
      let searchResults: SearchResultsDto;
      switch (searchQuery.type) {
        case SearchType.USERS:
          searchResults = await this.searchUsers(searchQuery, context);
          break;
        case SearchType.CAMPAIGNS:
          searchResults = await this.searchCampaigns(searchQuery, context);
          break;
        case SearchType.NFTS:
          searchResults = await this.searchNFTs(searchQuery, context);
          break;
        case SearchType.MARKETPLACE:
          searchResults = await this.searchMarketplace(searchQuery, context);
          break;
        case SearchType.GLOBAL:
          searchResults = await this.searchGlobal(searchQuery, context);
          break;
        default:
          throw new BadRequestException(`Unsupported search type: ${searchQuery.type}`);
      }

      // Add execution time
      searchResults.executionTime = Date.now() - startTime;
      searchResults.timestamp = new Date().toISOString();

      // Cache results for 5 minutes
      await this.cacheService.set(cacheKey, searchResults, 300);

      // Log search analytics
      await this.logSearchAnalytics(searchQuery, searchResults, context);

      this.logger.log(`Search completed: ${searchResults.totalCount} results in ${searchResults.executionTime}ms`, {
        correlationId,
        query: searchQuery.query,
        type: searchQuery.type,
        resultCount: searchResults.totalCount,
        duration: searchResults.executionTime,
      });

      return { success: true, data: searchResults };

    } catch (error) {
      this.logger.error(`Search failed: ${error.message}`, {
        correlationId,
        searchQuery,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      return { success: false, error: 'Search operation failed' };
    }
  }

  /**
   * Get autocomplete suggestions
   */
  async autocomplete(autocompleteQuery: AutocompleteQueryDto, context: RequestContext): Promise<AutocompleteResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Getting autocomplete for: "${autocompleteQuery.query}"`, { correlationId });

      // Try cache first
      const cacheKey = `autocomplete:${autocompleteQuery.type}:${autocompleteQuery.query.toLowerCase()}:${autocompleteQuery.limit}`;
      let suggestions = await this.cacheService.get<SearchSuggestionDto[]>(cacheKey);

      if (!suggestions) {
        suggestions = await this.generateAutocompleteSuggestions(autocompleteQuery, context);
        
        // Cache for 10 minutes
        await this.cacheService.set(cacheKey, suggestions, 600);
      }

      this.logger.log(`Autocomplete completed: ${suggestions.length} suggestions`, {
        correlationId,
        query: autocompleteQuery.query,
        suggestionCount: suggestions.length,
        duration: Date.now() - startTime,
      });

      return { success: true, data: suggestions };

    } catch (error) {
      this.logger.error(`Autocomplete failed: ${error.message}`, {
        correlationId,
        autocompleteQuery,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      return { success: false, error: 'Autocomplete operation failed' };
    }
  }

  /**
   * Search users
   */
  private async searchUsers(searchQuery: SearchQueryDto, context: RequestContext): Promise<SearchResultsDto> {
    const { query, filters, sortBy, sortOrder, page, limit } = searchQuery;
    
    // Build where clause
    const whereClause: any = {};
    
    if (query) {
      whereClause.OR = [
        { username: { contains: query, mode: 'insensitive' } },
        { displayName: { contains: query, mode: 'insensitive' } },
        { email: { contains: query, mode: 'insensitive' } },
        { bio: { contains: query, mode: 'insensitive' } },
      ];
    }

    // Apply filters
    this.applyFilters(whereClause, filters);

    // Build order clause
    const orderBy = this.buildOrderBy(sortBy || 'createdAt', sortOrder);

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute search
    const [users, totalCount] = await Promise.all([
      this.prisma.userQuery.findMany({
        where: whereClause,
        orderBy,
        skip,
        take: limit,
        select: {
          id: true,
          username: true,
          displayName: true,
          email: true,
          bio: true,
          profileImage: true,
          createdAt: true,
          lastUpdated: true,
          isEmailVerified: true,
          totalNfts: true,
        },
      }),
      this.prisma.userQuery.count({ where: whereClause }),
    ]);

    // Transform to search results
    const results: SearchResultItemDto[] = users.map(user => ({
      id: user.id,
      type: 'user',
      title: user.displayName || user.username,
      description: user.bio || `User since ${user.createdAt.getFullYear()}`,
      imageUrl: user.profileImage,
      url: `/users/${user.id}`,
      score: this.calculateRelevanceScore(query, user.username + ' ' + (user.displayName || '') + ' ' + (user.bio || '')),
      createdAt: user.createdAt.toISOString(),
      metadata: {
        username: user.username,
        isVerified: user.isEmailVerified,
        totalNfts: user.totalNfts,
        lastUpdated: user.lastUpdated?.toISOString(),
      },
    }));

    // Generate facets if requested
    const facets = searchQuery.includeFacets ? await this.generateUserFacets(whereClause) : undefined;

    return {
      query: query || '',
      type: SearchType.USERS,
      results,
      totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
      hasNext: page * limit < totalCount,
      hasPrev: page > 1,
      executionTime: 0, // Will be set by caller
      facets,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Search campaigns
   */
  private async searchCampaigns(searchQuery: SearchQueryDto, context: RequestContext): Promise<SearchResultsDto> {
    const { query, filters, sortBy, sortOrder, page, limit } = searchQuery;
    
    const whereClause: any = {};
    
    if (query) {
      whereClause.OR = [
        { name: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { tags: { hasSome: [query] } },
      ];
    }

    this.applyFilters(whereClause, filters);
    const orderBy = this.buildOrderBy(sortBy || 'createdAt', sortOrder);
    const skip = (page - 1) * limit;

    const [campaigns, totalCount] = await Promise.all([
      this.prisma.campaign.findMany({
        where: whereClause,
        orderBy,
        skip,
        take: limit,
        include: {
          _count: {
            select: { participations: true },
          },
        },
      }),
      this.prisma.campaign.count({ where: whereClause }),
    ]);

    const results: SearchResultItemDto[] = campaigns.map(campaign => ({
      id: campaign.id,
      type: 'campaign',
      title: campaign.name,
      description: campaign.description,
      imageUrl: campaign.bannerImage,
      url: `/campaigns/${campaign.id}`,
      score: this.calculateRelevanceScore(query, campaign.name + ' ' + campaign.description),
      createdAt: campaign.createdAt.toISOString(),
      metadata: {
        status: campaign.status,
        type: campaign.type,
        participants: campaign._count.participations,
        startDate: campaign.startDate?.toISOString(),
        endDate: campaign.endDate?.toISOString(),
        tags: campaign.tags,
      },
    }));

    const facets = searchQuery.includeFacets ? await this.generateCampaignFacets(whereClause) : undefined;

    return {
      query: query || '',
      type: SearchType.CAMPAIGNS,
      results,
      totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
      hasNext: page * limit < totalCount,
      hasPrev: page > 1,
      executionTime: 0,
      facets,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Search NFTs
   */
  private async searchNFTs(searchQuery: SearchQueryDto, context: RequestContext): Promise<SearchResultsDto> {
    const { query, filters, sortBy, sortOrder, page, limit } = searchQuery;
    
    const whereClause: any = {};
    
    if (query) {
      whereClause.OR = [
        { rarity: { contains: query, mode: 'insensitive' } },
        { status: { contains: query, mode: 'insensitive' } },
        { blockchain: { contains: query, mode: 'insensitive' } },
        { metadata: { path: '$', string_contains: query } },
      ];
    }

    this.applyFilters(whereClause, filters);
    const orderBy = this.buildOrderBy(sortBy || 'createdAt', sortOrder);
    const skip = (page - 1) * limit;

    const [nfts, totalCount] = await Promise.all([
      this.prisma.nFT.findMany({
        where: whereClause,
        orderBy,
        skip,
        take: limit,
        include: {
          user: {
            select: { id: true, username: true, displayName: true },
          },
          campaign: {
            select: { id: true, name: true },
          },
        },
      }),
      this.prisma.nFT.count({ where: whereClause }),
    ]);

    const results: SearchResultItemDto[] = nfts.map(nft => ({
      id: nft.id,
      type: 'nft',
      title: `${nft.rarity} NFT`,
      description: `${nft.rarity} NFT from ${nft.campaign.name}`,
      imageUrl: nft.imageUrl,
      url: `/nfts/${nft.id}`,
      score: this.calculateRelevanceScore(query, nft.rarity + ' ' + nft.status + ' ' + nft.blockchain),
      createdAt: nft.createdAt.toISOString(),
      metadata: {
        rarity: nft.rarity,
        status: nft.status,
        tokenId: nft.tokenId,
        contractAddress: nft.contractAddress,
        engagementScore: nft.engagementScore,
        owner: nft.user,
        campaign: nft.campaign,
        blockchain: nft.blockchain,
      },
    }));

    const facets = searchQuery.includeFacets ? await this.generateNFTFacets(whereClause) : undefined;

    return {
      query: query || '',
      type: SearchType.NFTS,
      results,
      totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
      hasNext: page * limit < totalCount,
      hasPrev: page > 1,
      executionTime: 0,
      facets,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Search marketplace listings
   */
  private async searchMarketplace(searchQuery: SearchQueryDto, context: RequestContext): Promise<SearchResultsDto> {
    const { query, filters, sortBy, sortOrder, page, limit } = searchQuery;
    
    const whereClause: any = { status: 'active' }; // Only show active listings
    
    if (query) {
      whereClause.OR = [
        { title: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { nft: { rarity: { contains: query, mode: 'insensitive' } } },
        { tags: { hasSome: [query] } },
      ];
    }

    this.applyFilters(whereClause, filters);
    const orderBy = this.buildOrderBy(sortBy || 'createdAt', sortOrder);
    const skip = (page - 1) * limit;

    const [listings, totalCount] = await Promise.all([
      this.prisma.marketplaceListing.findMany({
        where: whereClause,
        orderBy,
        skip,
        take: limit,
        include: {
          nft: {
            include: {
              user: {
                select: { id: true, username: true, displayName: true },
              },
              campaign: {
                select: { id: true, name: true },
              },
            },
          },
          seller: {
            select: { id: true, username: true, displayName: true },
          },
        },
      }),
      this.prisma.marketplaceListing.count({ where: whereClause }),
    ]);

    const results: SearchResultItemDto[] = listings.map(listing => ({
      id: listing.id,
      type: 'marketplace_listing',
      title: listing.title,
      description: listing.description,
      imageUrl: listing.nft.imageUrl,
      url: `/marketplace/listings/${listing.id}`,
      score: this.calculateRelevanceScore(query, listing.title + ' ' + listing.description),
      createdAt: listing.createdAt.toISOString(),
      metadata: {
        price: listing.price,
        currency: listing.currency,
        nftRarity: listing.nft.rarity,
        seller: listing.seller,
        nftOwner: listing.nft.user,
        viewCount: listing.viewCount,
        offerCount: 0, // TODO: Implement offer counting
        listingType: listing.listingType,
        tags: listing.tags,
      },
    }));

    const facets = searchQuery.includeFacets ? await this.generateMarketplaceFacets(whereClause) : undefined;

    return {
      query: query || '',
      type: SearchType.MARKETPLACE,
      results,
      totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
      hasNext: page * limit < totalCount,
      hasPrev: page > 1,
      executionTime: 0,
      facets,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Global search across all entity types
   */
  private async searchGlobal(searchQuery: SearchQueryDto, context: RequestContext): Promise<SearchResultsDto> {
    const { query, page, limit } = searchQuery;
    
    // Perform parallel searches across all types
    const [userResults, campaignResults, nftResults, marketplaceResults] = await Promise.all([
      this.searchUsers({ ...searchQuery, type: SearchType.USERS, limit: Math.ceil(limit / 4) }, context),
      this.searchCampaigns({ ...searchQuery, type: SearchType.CAMPAIGNS, limit: Math.ceil(limit / 4) }, context),
      this.searchNFTs({ ...searchQuery, type: SearchType.NFTS, limit: Math.ceil(limit / 4) }, context),
      this.searchMarketplace({ ...searchQuery, type: SearchType.MARKETPLACE, limit: Math.ceil(limit / 4) }, context),
    ]);

    // Combine and sort results by relevance
    const allResults = [
      ...userResults.results,
      ...campaignResults.results,
      ...nftResults.results,
      ...marketplaceResults.results,
    ].sort((a, b) => b.score - a.score);

    // Apply pagination to combined results
    const skip = (page - 1) * limit;
    const paginatedResults = allResults.slice(skip, skip + limit);
    const totalCount = allResults.length;

    return {
      query: query || '',
      type: SearchType.GLOBAL,
      results: paginatedResults,
      totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
      hasNext: page * limit < totalCount,
      hasPrev: page > 1,
      executionTime: 0,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Generate autocomplete suggestions
   */
  private async generateAutocompleteSuggestions(
    autocompleteQuery: AutocompleteQueryDto,
    context: RequestContext
  ): Promise<SearchSuggestionDto[]> {
    const { query, type, limit } = autocompleteQuery;
    const suggestions: SearchSuggestionDto[] = [];

    // Generate completion suggestions based on existing data
    switch (type) {
      case SearchType.USERS:
        const users = await this.prisma.userQuery.findMany({
          where: {
            OR: [
              { username: { startsWith: query, mode: 'insensitive' } },
              { displayName: { startsWith: query, mode: 'insensitive' } },
            ],
          },
          select: { username: true, displayName: true },
          take: limit,
        });
        
        users.forEach(user => {
          if (user.username.toLowerCase().startsWith(query.toLowerCase())) {
            suggestions.push({
              text: user.username,
              type: 'completion',
              score: 0.9,
              category: 'username',
            });
          }
          if (user.displayName?.toLowerCase().startsWith(query.toLowerCase())) {
            suggestions.push({
              text: user.displayName,
              type: 'completion',
              score: 0.8,
              category: 'display_name',
            });
          }
        });
        break;

      case SearchType.CAMPAIGNS:
        const campaigns = await this.prisma.campaign.findMany({
          where: {
            name: { startsWith: query, mode: 'insensitive' },
          },
          select: { name: true },
          take: limit,
        });
        
        campaigns.forEach(campaign => {
          suggestions.push({
            text: campaign.name,
            type: 'completion',
            score: 0.9,
            category: 'campaign',
          });
        });
        break;

      case SearchType.NFTS:
        const nfts = await this.prisma.nFT.findMany({
          where: {
            rarity: { startsWith: query, mode: 'insensitive' },
          },
          select: { rarity: true, status: true, blockchain: true },
          take: limit,
        });

        nfts.forEach(nft => {
          suggestions.push({
            text: nft.rarity,
            type: 'completion',
            score: 0.9,
            category: 'nft_rarity',
          });
        });
        break;

      default:
        // Global suggestions - mix of popular terms
        const popularTerms = ['NFT', 'campaign', 'legendary', 'rare', 'epic', 'marketplace'];
        popularTerms
          .filter(term => term.toLowerCase().startsWith(query.toLowerCase()))
          .forEach(term => {
            suggestions.push({
              text: term,
              type: 'popular',
              score: 0.7,
              category: 'popular',
            });
          });
        break;
    }

    // Sort by score and limit results
    return suggestions
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Apply search filters to where clause
   */
  private applyFilters(whereClause: any, filters?: SearchFilterDto[]): void {
    if (!filters || filters.length === 0) return;

    filters.forEach(filter => {
      const { field, operator, value, value2 } = filter;

      switch (operator) {
        case FilterOperator.EQUALS:
          whereClause[field] = value;
          break;
        case FilterOperator.NOT_EQUALS:
          whereClause[field] = { not: value };
          break;
        case FilterOperator.GREATER_THAN:
          whereClause[field] = { gt: value };
          break;
        case FilterOperator.GREATER_THAN_OR_EQUAL:
          whereClause[field] = { gte: value };
          break;
        case FilterOperator.LESS_THAN:
          whereClause[field] = { lt: value };
          break;
        case FilterOperator.LESS_THAN_OR_EQUAL:
          whereClause[field] = { lte: value };
          break;
        case FilterOperator.CONTAINS:
          whereClause[field] = { contains: value, mode: 'insensitive' };
          break;
        case FilterOperator.STARTS_WITH:
          whereClause[field] = { startsWith: value, mode: 'insensitive' };
          break;
        case FilterOperator.ENDS_WITH:
          whereClause[field] = { endsWith: value, mode: 'insensitive' };
          break;
        case FilterOperator.IN:
          whereClause[field] = { in: Array.isArray(value) ? value : [value] };
          break;
        case FilterOperator.NOT_IN:
          whereClause[field] = { notIn: Array.isArray(value) ? value : [value] };
          break;
        case FilterOperator.BETWEEN:
          whereClause[field] = { gte: value, lte: value2 };
          break;
        case FilterOperator.IS_NULL:
          whereClause[field] = null;
          break;
        case FilterOperator.IS_NOT_NULL:
          whereClause[field] = { not: null };
          break;
      }
    });
  }

  /**
   * Build order by clause
   */
  private buildOrderBy(sortBy: string, sortOrder?: SortOrder): any {
    return { [sortBy]: sortOrder || SortOrder.DESC };
  }

  /**
   * Calculate relevance score for search results
   */
  private calculateRelevanceScore(query: string, text: string): number {
    if (!query || !text) return 0;

    const queryLower = query.toLowerCase();
    const textLower = text.toLowerCase();

    // Exact match gets highest score
    if (textLower === queryLower) return 1.0;

    // Starts with query gets high score
    if (textLower.startsWith(queryLower)) return 0.9;

    // Contains query gets medium score
    if (textLower.includes(queryLower)) return 0.7;

    // Word boundary matches get lower score
    const words = queryLower.split(' ');
    const textWords = textLower.split(' ');
    const matchingWords = words.filter(word => 
      textWords.some(textWord => textWord.includes(word))
    );

    return matchingWords.length / words.length * 0.5;
  }

  /**
   * Generate search cache key
   */
  private generateSearchCacheKey(searchQuery: SearchQueryDto): string {
    const key = `search:${searchQuery.type}:${searchQuery.query || 'empty'}:${searchQuery.page}:${searchQuery.limit}:${searchQuery.sortBy || 'default'}:${searchQuery.sortOrder || 'desc'}`;
    
    if (searchQuery.filters && searchQuery.filters.length > 0) {
      const filterKey = searchQuery.filters
        .map(f => `${f.field}:${f.operator}:${f.value}`)
        .join('|');
      return `${key}:filters:${filterKey}`;
    }

    return key;
  }

  /**
   * Log search analytics
   */
  private async logSearchAnalytics(
    searchQuery: SearchQueryDto,
    searchResults: SearchResultsDto,
    context: RequestContext
  ): Promise<void> {
    try {
      const analytics: SearchAnalyticsDto = {
        query: searchQuery.query || '',
        type: searchQuery.type,
        resultCount: searchResults.totalCount,
        executionTime: searchResults.executionTime,
        userId: context.userId,
        timestamp: new Date().toISOString(),
        filters: searchQuery.filters,
        sortCriteria: {
          field: searchQuery.sortBy || 'createdAt',
          order: searchQuery.sortOrder || SortOrder.DESC,
        },
      };

      // Store in database for analytics
      await this.prisma.searchAnalytics.create({
        data: {
          query: analytics.query,
          type: analytics.type,
          resultCount: analytics.resultCount,
          executionTime: analytics.executionTime,
          userId: analytics.userId,
          filters: JSON.stringify(analytics.filters || []),
          sortCriteria: JSON.stringify(analytics.sortCriteria),
          timestamp: new Date(analytics.timestamp),
        },
      });

    } catch (error) {
      this.logger.error(`Failed to log search analytics: ${error.message}`, {
        searchQuery,
        error: error.stack,
      });
    }
  }

  /**
   * Generate user search facets
   */
  private async generateUserFacets(whereClause: any): Promise<SearchFacetDto[]> {
    // TODO: Implement user facet generation
    return [];
  }

  /**
   * Generate campaign search facets
   */
  private async generateCampaignFacets(whereClause: any): Promise<SearchFacetDto[]> {
    // TODO: Implement campaign facet generation
    return [];
  }

  /**
   * Generate NFT search facets
   */
  private async generateNFTFacets(whereClause: any): Promise<SearchFacetDto[]> {
    // TODO: Implement NFT facet generation
    return [];
  }

  /**
   * Generate marketplace search facets
   */
  private async generateMarketplaceFacets(whereClause: any): Promise<SearchFacetDto[]> {
    // TODO: Implement marketplace facet generation
    return [];
  }
}
