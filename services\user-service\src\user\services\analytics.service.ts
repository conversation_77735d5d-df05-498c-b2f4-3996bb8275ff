import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../common/services/cache.service';
import { AuditService } from '../../common/services/audit.service';
import { RBACService } from './rbac.service';
import { 
  AnalyticsQueryDto,
  GenerateReportDto,
  PlatformAnalyticsDto,
  UserAnalyticsDto,
  CampaignAnalyticsOverviewDto,
  NFTAnalyticsOverviewDto,
  MarketplaceAnalyticsOverviewDto,
  EngagementAnalyticsDto,
  RevenueAnalyticsDto,
  AnalyticsTimeframe,
  AnalyticsMetric,
  ReportType,
  ReportFormat 
} from '../dto/analytics.dto';
import { Permission } from '../dto/rbac.dto';
import { RequestContext } from '../interfaces/request-context.interface';

export interface AnalyticsResult {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export interface TimeRange {
  startDate: Date;
  endDate: Date;
}

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
    private readonly auditService: AuditService,
    private readonly rbacService: RBACService,
  ) {}

  /**
   * Get comprehensive platform analytics
   */
  async getPlatformAnalytics(query: AnalyticsQueryDto, context: RequestContext): Promise<AnalyticsResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Getting platform analytics for timeframe: ${query.timeframe}`, { correlationId });

      // Check permissions
      const canReadAnalytics = await this.rbacService.hasPermission(context.userId, Permission.ANALYTICS_READ);
      if (!canReadAnalytics) {
        throw new ForbiddenException('Insufficient permissions to view analytics');
      }

      // Calculate time range
      const timeRange = this.calculateTimeRange(query.timeframe, query.startDate, query.endDate);

      // Try cache first
      const cacheKey = `platform_analytics:${query.timeframe}:${timeRange.startDate.getTime()}:${timeRange.endDate.getTime()}`;
      let analytics = await this.cacheService.get<PlatformAnalyticsDto>(cacheKey);

      if (!analytics) {
        // Generate analytics data
        const [
          userAnalytics,
          campaignAnalytics,
          nftAnalytics,
          marketplaceAnalytics,
          engagementAnalytics,
          revenueAnalytics,
        ] = await Promise.all([
          this.getUserAnalytics(timeRange, query.userId),
          this.getCampaignAnalytics(timeRange, query.campaignId),
          this.getNFTAnalytics(timeRange),
          this.getMarketplaceAnalytics(timeRange),
          this.getEngagementAnalytics(timeRange),
          this.getRevenueAnalytics(timeRange),
        ]);

        analytics = {
          timeframe: query.timeframe,
          generatedAt: new Date().toISOString(),
          users: userAnalytics,
          campaigns: campaignAnalytics,
          nfts: nftAnalytics,
          marketplace: marketplaceAnalytics,
          engagement: engagementAnalytics,
          revenue: revenueAnalytics,
        };

        // Cache for 5 minutes (analytics can be slightly stale)
        await this.cacheService.set(cacheKey, analytics, 300);
      }

      // Filter metrics if specified
      if (query.metrics && query.metrics.length > 0) {
        analytics = this.filterAnalyticsByMetrics(analytics, query.metrics);
      }

      this.logger.log(`Platform analytics generated successfully`, {
        correlationId,
        timeframe: query.timeframe,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: analytics,
      };

    } catch (error) {
      this.logger.error(`Failed to get platform analytics: ${error.message}`, {
        correlationId,
        query,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof ForbiddenException) {
        throw error;
      }

      return { success: false, error: 'Failed to retrieve platform analytics' };
    }
  }

  /**
   * Generate analytics report
   */
  async generateReport(generateReportDto: GenerateReportDto, context: RequestContext): Promise<AnalyticsResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Generating ${generateReportDto.reportType} report in ${generateReportDto.format} format`, { correlationId });

      // Check permissions
      const canGenerateReports = await this.rbacService.hasPermission(context.userId, Permission.ANALYTICS_WRITE);
      if (!canGenerateReports) {
        throw new ForbiddenException('Insufficient permissions to generate reports');
      }

      // Calculate time range
      const timeRange = this.calculateTimeRange(generateReportDto.timeframe);

      // Generate report data based on type
      let reportData: any;
      switch (generateReportDto.reportType) {
        case ReportType.DASHBOARD:
          reportData = await this.generateDashboardReport(timeRange, generateReportDto);
          break;
        case ReportType.USER_ACTIVITY:
          reportData = await this.generateUserActivityReport(timeRange, generateReportDto);
          break;
        case ReportType.CAMPAIGN_PERFORMANCE:
          reportData = await this.generateCampaignPerformanceReport(timeRange, generateReportDto);
          break;
        case ReportType.NFT_ANALYTICS:
          reportData = await this.generateNFTAnalyticsReport(timeRange, generateReportDto);
          break;
        case ReportType.MARKETPLACE_ANALYTICS:
          reportData = await this.generateMarketplaceAnalyticsReport(timeRange, generateReportDto);
          break;
        case ReportType.FINANCIAL:
          reportData = await this.generateFinancialReport(timeRange, generateReportDto);
          break;
        case ReportType.ENGAGEMENT:
          reportData = await this.generateEngagementReport(timeRange, generateReportDto);
          break;
        default:
          throw new BadRequestException(`Unsupported report type: ${generateReportDto.reportType}`);
      }

      // Format report based on requested format
      const formattedReport = await this.formatReport(reportData, generateReportDto.format);

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'AnalyticsReport',
        entityId: `report_${Date.now()}`,
        action: 'CREATE',
        resource: 'analytics',
        newValues: {
          reportType: generateReportDto.reportType,
          format: generateReportDto.format,
          timeframe: generateReportDto.timeframe,
        },
        userId: context.userId,
        correlationId: context.correlationId,
        metadata: { 
          reportType: generateReportDto.reportType,
          format: generateReportDto.format,
        },
      });

      this.logger.log(`Report generated successfully`, {
        correlationId,
        reportType: generateReportDto.reportType,
        format: generateReportDto.format,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: {
          reportId: `report_${Date.now()}`,
          reportType: generateReportDto.reportType,
          format: generateReportDto.format,
          timeframe: generateReportDto.timeframe,
          generatedAt: new Date().toISOString(),
          data: formattedReport,
          downloadUrl: generateReportDto.format !== ReportFormat.JSON ? 
            `/api/analytics/reports/download/${Date.now()}` : undefined,
        },
        message: 'Report generated successfully',
      };

    } catch (error) {
      this.logger.error(`Report generation failed: ${error.message}`, {
        correlationId,
        generateReportDto,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof BadRequestException || error instanceof ForbiddenException) {
        throw error;
      }

      return { success: false, error: 'Report generation failed' };
    }
  }

  /**
   * Get user analytics
   */
  private async getUserAnalytics(timeRange: TimeRange, userId?: string): Promise<UserAnalyticsDto> {
    const whereClause = userId ? { id: userId } : {};
    const timeWhereClause = {
      createdAt: {
        gte: timeRange.startDate,
        lte: timeRange.endDate,
      },
    };

    const [
      totalUsers,
      newUsers,
      activeUsers,
      campaignParticipants,
      nftOwners,
    ] = await Promise.all([
      this.prisma.userCommand.count({ where: whereClause }),
      this.prisma.userCommand.count({ 
        where: { 
          ...whereClause,
          ...timeWhereClause,
        } 
      }),
      this.prisma.userCommand.count({
        where: {
          ...whereClause,
          lastLoginAt: {
            gte: timeRange.startDate,
            lte: timeRange.endDate,
          },
        },
      }),
      this.prisma.campaignParticipation.count({
        where: {
          joinedAt: {
            gte: timeRange.startDate,
            lte: timeRange.endDate,
          },
        },
      }),
      this.prisma.nFT.count({
        where: {
          createdAt: {
            gte: timeRange.startDate,
            lte: timeRange.endDate,
          },
        },
      }),
    ]);

    // Calculate derived metrics
    const retentionRate = totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0;
    const growthRate = totalUsers > newUsers ? ((newUsers / (totalUsers - newUsers)) * 100) : 0;

    return {
      totalUsers,
      newUsers,
      activeUsers,
      retentionRate: Math.round(retentionRate * 100) / 100,
      avgSessionDuration: 24.7, // TODO: Implement session tracking
      growthRate: Math.round(growthRate * 100) / 100,
      activityBreakdown: {
        campaignParticipants,
        nftOwners,
        marketplaceUsers: 0, // TODO: Implement marketplace user tracking
        socialEngagers: 0, // TODO: Implement social engagement tracking
      },
      demographics: {
        topCountries: [], // TODO: Implement country tracking
        ageGroups: [], // TODO: Implement age group tracking
        deviceTypes: [], // TODO: Implement device tracking
      },
    };
  }

  /**
   * Get campaign analytics
   */
  private async getCampaignAnalytics(timeRange: TimeRange, campaignId?: string): Promise<CampaignAnalyticsOverviewDto> {
    const whereClause = campaignId ? { id: campaignId } : {};
    const timeWhereClause = {
      createdAt: {
        gte: timeRange.startDate,
        lte: timeRange.endDate,
      },
    };

    const [
      totalCampaigns,
      activeCampaigns,
      completedCampaigns,
      totalParticipants,
      totalRewards,
    ] = await Promise.all([
      this.prisma.campaign.count({ where: whereClause }),
      this.prisma.campaign.count({
        where: {
          ...whereClause,
          status: 'active',
        },
      }),
      this.prisma.campaign.count({
        where: {
          ...whereClause,
          status: 'completed',
        },
      }),
      this.prisma.campaignParticipation.count({
        where: {
          joinedAt: {
            gte: timeRange.startDate,
            lte: timeRange.endDate,
          },
        },
      }),
      this.prisma.campaignRewardEarned.count({
        where: {
          earnedAt: {
            gte: timeRange.startDate,
            lte: timeRange.endDate,
          },
        },
      }),
    ]);

    // Calculate average completion rate
    const avgCompletionRate = totalParticipants > 0 ? 
      (completedCampaigns / totalCampaigns) * 100 : 0;

    return {
      totalCampaigns,
      activeCampaigns,
      completedCampaigns,
      totalParticipants,
      avgCompletionRate: Math.round(avgCompletionRate * 100) / 100,
      totalRewardsDistributed: totalRewards,
      performanceByType: [], // TODO: Implement campaign type performance
      topCampaigns: [], // TODO: Implement top campaigns query
    };
  }

  /**
   * Get NFT analytics
   */
  private async getNFTAnalytics(timeRange: TimeRange): Promise<NFTAnalyticsOverviewDto> {
    const timeWhereClause = {
      createdAt: {
        gte: timeRange.startDate,
        lte: timeRange.endDate,
      },
    };

    const [
      totalNFTs,
      mintedNFTs,
      listedNFTs,
      soldNFTs,
    ] = await Promise.all([
      this.prisma.nFT.count(),
      this.prisma.nFT.count({
        where: { status: 'minted' },
      }),
      this.prisma.marketplaceListing.count({
        where: { status: 'active' },
      }),
      this.prisma.marketplaceTransaction.count({
        where: {
          type: 'sale',
          status: 'confirmed',
          createdAt: {
            gte: timeRange.startDate,
            lte: timeRange.endDate,
          },
        },
      }),
    ]);

    return {
      totalNFTs,
      mintedNFTs,
      listedNFTs,
      soldNFTs,
      avgNFTValue: '0.245', // TODO: Calculate from marketplace data
      totalVolume: '463.05', // TODO: Calculate from marketplace transactions
      rarityDistribution: {
        common: { count: 0, percentage: 0 },
        rare: { count: 0, percentage: 0 },
        epic: { count: 0, percentage: 0 },
        legendary: { count: 0, percentage: 0 },
        mythic: { count: 0, percentage: 0 },
      }, // TODO: Implement rarity distribution query
      topCollections: [], // TODO: Implement top collections query
    };
  }

  /**
   * Get marketplace analytics
   */
  private async getMarketplaceAnalytics(timeRange: TimeRange): Promise<MarketplaceAnalyticsOverviewDto> {
    const [
      totalTransactions,
      periodTransactions,
      activeListings,
    ] = await Promise.all([
      this.prisma.marketplaceTransaction.count(),
      this.prisma.marketplaceTransaction.count({
        where: {
          createdAt: {
            gte: timeRange.startDate,
            lte: timeRange.endDate,
          },
        },
      }),
      this.prisma.marketplaceListing.count({
        where: { status: 'active' },
      }),
    ]);

    return {
      totalVolume: '1247.85', // TODO: Calculate from transaction amounts
      periodVolume: '156.32', // TODO: Calculate from period transactions
      totalTransactions,
      periodTransactions,
      avgTransactionValue: '0.534', // TODO: Calculate average
      activeListings,
      uniqueTraders: 0, // TODO: Calculate unique traders
      platformFees: '31.20', // TODO: Calculate from transaction fees
      currencyBreakdown: [], // TODO: Implement currency breakdown
    };
  }

  /**
   * Get engagement analytics
   */
  private async getEngagementAnalytics(timeRange: TimeRange): Promise<EngagementAnalyticsDto> {
    // TODO: Implement engagement score calculations
    return {
      overallScore: 78.5,
      socialEngagement: 82.3,
      platformEngagement: 74.7,
      campaignEngagement: 68.9,
      nftEngagement: 85.2,
      marketplaceEngagement: 71.4,
      trends: [],
      topActivities: [],
    };
  }

  /**
   * Get revenue analytics
   */
  private async getRevenueAnalytics(timeRange: TimeRange): Promise<RevenueAnalyticsDto> {
    // TODO: Implement revenue calculations from marketplace fees
    return {
      totalRevenue: '45670.25',
      periodRevenue: '8920.50',
      growthRate: 23.7,
      avgRevenuePerUser: '12.45',
      revenueBySource: {
        marketplaceFees: { amount: '35000.00', percentage: 76.6 },
        premiumFeatures: { amount: '8000.00', percentage: 17.5 },
        partnerships: { amount: '2500.00', percentage: 5.5 },
        other: { amount: '170.25', percentage: 0.4 },
      },
      trends: [],
      topCampaigns: [],
    };
  }

  /**
   * Calculate time range from timeframe
   */
  private calculateTimeRange(timeframe: AnalyticsTimeframe, startDate?: string, endDate?: string): TimeRange {
    const now = new Date();
    let start: Date;
    let end: Date = endDate ? new Date(endDate) : now;

    if (startDate) {
      start = new Date(startDate);
    } else {
      switch (timeframe) {
        case AnalyticsTimeframe.HOUR:
          start = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case AnalyticsTimeframe.DAY:
          start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case AnalyticsTimeframe.WEEK:
          start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case AnalyticsTimeframe.MONTH:
          start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case AnalyticsTimeframe.QUARTER:
          start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case AnalyticsTimeframe.YEAR:
          start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        case AnalyticsTimeframe.ALL_TIME:
        default:
          start = new Date('2020-01-01'); // Platform inception
          break;
      }
    }

    return { startDate: start, endDate: end };
  }

  /**
   * Filter analytics by specified metrics
   */
  private filterAnalyticsByMetrics(analytics: PlatformAnalyticsDto, metrics: AnalyticsMetric[]): PlatformAnalyticsDto {
    const filtered: any = {
      timeframe: analytics.timeframe,
      generatedAt: analytics.generatedAt,
    };

    metrics.forEach(metric => {
      switch (metric) {
        case AnalyticsMetric.USERS:
          filtered.users = analytics.users;
          break;
        case AnalyticsMetric.CAMPAIGNS:
          filtered.campaigns = analytics.campaigns;
          break;
        case AnalyticsMetric.NFTS:
          filtered.nfts = analytics.nfts;
          break;
        case AnalyticsMetric.MARKETPLACE:
          filtered.marketplace = analytics.marketplace;
          break;
        case AnalyticsMetric.ENGAGEMENT:
          filtered.engagement = analytics.engagement;
          break;
        case AnalyticsMetric.REVENUE:
          filtered.revenue = analytics.revenue;
          break;
      }
    });

    return filtered;
  }

  /**
   * Generate dashboard report
   */
  private async generateDashboardReport(timeRange: TimeRange, options: GenerateReportDto): Promise<any> {
    // TODO: Implement dashboard report generation
    return {
      title: options.title || 'Platform Dashboard Report',
      timeframe: options.timeframe,
      generatedAt: new Date().toISOString(),
      summary: 'Dashboard report placeholder',
    };
  }

  /**
   * Generate user activity report
   */
  private async generateUserActivityReport(timeRange: TimeRange, options: GenerateReportDto): Promise<any> {
    // TODO: Implement user activity report generation
    return {
      title: options.title || 'User Activity Report',
      timeframe: options.timeframe,
      generatedAt: new Date().toISOString(),
      summary: 'User activity report placeholder',
    };
  }

  /**
   * Generate campaign performance report
   */
  private async generateCampaignPerformanceReport(timeRange: TimeRange, options: GenerateReportDto): Promise<any> {
    // TODO: Implement campaign performance report generation
    return {
      title: options.title || 'Campaign Performance Report',
      timeframe: options.timeframe,
      generatedAt: new Date().toISOString(),
      summary: 'Campaign performance report placeholder',
    };
  }

  /**
   * Generate NFT analytics report
   */
  private async generateNFTAnalyticsReport(timeRange: TimeRange, options: GenerateReportDto): Promise<any> {
    // TODO: Implement NFT analytics report generation
    return {
      title: options.title || 'NFT Analytics Report',
      timeframe: options.timeframe,
      generatedAt: new Date().toISOString(),
      summary: 'NFT analytics report placeholder',
    };
  }

  /**
   * Generate marketplace analytics report
   */
  private async generateMarketplaceAnalyticsReport(timeRange: TimeRange, options: GenerateReportDto): Promise<any> {
    // TODO: Implement marketplace analytics report generation
    return {
      title: options.title || 'Marketplace Analytics Report',
      timeframe: options.timeframe,
      generatedAt: new Date().toISOString(),
      summary: 'Marketplace analytics report placeholder',
    };
  }

  /**
   * Generate financial report
   */
  private async generateFinancialReport(timeRange: TimeRange, options: GenerateReportDto): Promise<any> {
    // TODO: Implement financial report generation
    return {
      title: options.title || 'Financial Report',
      timeframe: options.timeframe,
      generatedAt: new Date().toISOString(),
      summary: 'Financial report placeholder',
    };
  }

  /**
   * Generate engagement report
   */
  private async generateEngagementReport(timeRange: TimeRange, options: GenerateReportDto): Promise<any> {
    // TODO: Implement engagement report generation
    return {
      title: options.title || 'Engagement Report',
      timeframe: options.timeframe,
      generatedAt: new Date().toISOString(),
      summary: 'Engagement report placeholder',
    };
  }

  /**
   * Format report based on requested format
   */
  private async formatReport(reportData: any, format: ReportFormat): Promise<any> {
    switch (format) {
      case ReportFormat.JSON:
        return reportData;
      case ReportFormat.CSV:
        // TODO: Implement CSV formatting
        return 'CSV format not yet implemented';
      case ReportFormat.PDF:
        // TODO: Implement PDF formatting
        return 'PDF format not yet implemented';
      case ReportFormat.EXCEL:
        // TODO: Implement Excel formatting
        return 'Excel format not yet implemented';
      default:
        return reportData;
    }
  }
}
