import { api } from '@/lib/api'
import {
  NetworkConfig,
  WalletConnection,
  SmartContract,
  Transaction,
  GasEstimate,
  CrossChainBridge,
  BridgeTransaction,
  NFTContract,
  MultiChainNFT,
  BlockchainAnalytics,
  ConnectWalletRequest,
  SwitchNetworkRequest,
  SendTransactionRequest,
  DeployContractRequest,
  BridgeNFTRequest,
  EstimateGasRequest,
  BlockchainSearchRequest,
  BlockchainSearchResponse,
  BlockchainNetwork,
  WalletType,
  ContractType,
  GasStrategy
} from '@/types/blockchain-integration.types'

export class BlockchainIntegrationService {
  // ===== NETWORK MANAGEMENT =====
  
  async getNetworks(): Promise<NetworkConfig[]> {
    try {
      const response = await api.get('/blockchain/networks')
      return response.data
    } catch (error) {
      console.error('Failed to fetch networks:', error)
      throw new Error('Failed to load blockchain networks')
    }
  }

  async getNetwork(networkId: string): Promise<NetworkConfig> {
    try {
      const response = await api.get(`/blockchain/networks/${networkId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch network:', error)
      throw new Error('Failed to load network details')
    }
  }

  async getActiveNetworks(): Promise<NetworkConfig[]> {
    try {
      const response = await api.get('/blockchain/networks/active')
      return response.data
    } catch (error) {
      console.error('Failed to fetch active networks:', error)
      throw new Error('Failed to load active networks')
    }
  }

  async updateNetworkConfig(networkId: string, config: Partial<NetworkConfig>): Promise<NetworkConfig> {
    try {
      const response = await api.patch(`/blockchain/networks/${networkId}`, config)
      return response.data
    } catch (error) {
      console.error('Failed to update network config:', error)
      throw new Error('Failed to update network configuration')
    }
  }

  // ===== WALLET MANAGEMENT =====

  async connectWallet(request: ConnectWalletRequest): Promise<WalletConnection> {
    try {
      const response = await api.post('/blockchain/wallet/connect', request)
      return response.data
    } catch (error) {
      console.error('Failed to connect wallet:', error)
      throw new Error('Failed to connect wallet')
    }
  }

  async disconnectWallet(walletId: string): Promise<void> {
    try {
      await api.post(`/blockchain/wallet/${walletId}/disconnect`)
    } catch (error) {
      console.error('Failed to disconnect wallet:', error)
      throw new Error('Failed to disconnect wallet')
    }
  }

  async getConnectedWallets(): Promise<WalletConnection[]> {
    try {
      const response = await api.get('/blockchain/wallet/connected')
      return response.data
    } catch (error) {
      console.error('Failed to fetch connected wallets:', error)
      throw new Error('Failed to load connected wallets')
    }
  }

  async getWalletBalance(address: string, network: BlockchainNetwork): Promise<{
    nativeBalance: string
    tokenBalances: any[]
    totalUsdValue: number
  }> {
    try {
      const response = await api.get(`/blockchain/wallet/${address}/balance`, {
        params: { network }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch wallet balance:', error)
      throw new Error('Failed to load wallet balance')
    }
  }

  async switchNetwork(request: SwitchNetworkRequest): Promise<WalletConnection> {
    try {
      const response = await api.post('/blockchain/wallet/switch-network', request)
      return response.data
    } catch (error) {
      console.error('Failed to switch network:', error)
      throw new Error('Failed to switch network')
    }
  }

  async addNetwork(networkConfig: NetworkConfig): Promise<boolean> {
    try {
      const response = await api.post('/blockchain/wallet/add-network', networkConfig)
      return response.data.success
    } catch (error) {
      console.error('Failed to add network:', error)
      throw new Error('Failed to add network to wallet')
    }
  }

  // ===== SMART CONTRACT MANAGEMENT =====

  async getContracts(network?: BlockchainNetwork): Promise<SmartContract[]> {
    try {
      const response = await api.get('/blockchain/contracts', {
        params: { network }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch contracts:', error)
      throw new Error('Failed to load smart contracts')
    }
  }

  async getContract(contractId: string): Promise<SmartContract> {
    try {
      const response = await api.get(`/blockchain/contracts/${contractId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch contract:', error)
      throw new Error('Failed to load contract details')
    }
  }

  async deployContract(request: DeployContractRequest): Promise<{
    contractId: string
    transactionHash: string
    contractAddress: string
    estimatedGas: string
  }> {
    try {
      const response = await api.post('/blockchain/contracts/deploy', request)
      return response.data
    } catch (error) {
      console.error('Failed to deploy contract:', error)
      throw new Error('Failed to deploy smart contract')
    }
  }

  async upgradeContract(contractId: string, newImplementation: string): Promise<Transaction> {
    try {
      const response = await api.post(`/blockchain/contracts/${contractId}/upgrade`, {
        newImplementation
      })
      return response.data
    } catch (error) {
      console.error('Failed to upgrade contract:', error)
      throw new Error('Failed to upgrade smart contract')
    }
  }

  async verifyContract(contractId: string, sourceCode: string): Promise<{
    isVerified: boolean
    verificationUrl?: string
  }> {
    try {
      const response = await api.post(`/blockchain/contracts/${contractId}/verify`, {
        sourceCode
      })
      return response.data
    } catch (error) {
      console.error('Failed to verify contract:', error)
      throw new Error('Failed to verify smart contract')
    }
  }

  async pauseContract(contractId: string): Promise<Transaction> {
    try {
      const response = await api.post(`/blockchain/contracts/${contractId}/pause`)
      return response.data
    } catch (error) {
      console.error('Failed to pause contract:', error)
      throw new Error('Failed to pause smart contract')
    }
  }

  async unpauseContract(contractId: string): Promise<Transaction> {
    try {
      const response = await api.post(`/blockchain/contracts/${contractId}/unpause`)
      return response.data
    } catch (error) {
      console.error('Failed to unpause contract:', error)
      throw new Error('Failed to unpause smart contract')
    }
  }

  // ===== TRANSACTION MANAGEMENT =====

  async sendTransaction(request: SendTransactionRequest): Promise<Transaction> {
    try {
      const response = await api.post('/blockchain/transactions/send', request)
      return response.data
    } catch (error) {
      console.error('Failed to send transaction:', error)
      throw new Error('Failed to send transaction')
    }
  }

  async getTransaction(hash: string): Promise<Transaction> {
    try {
      const response = await api.get(`/blockchain/transactions/${hash}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch transaction:', error)
      throw new Error('Failed to load transaction details')
    }
  }

  async getTransactionHistory(address: string, network?: BlockchainNetwork): Promise<Transaction[]> {
    try {
      const response = await api.get(`/blockchain/transactions/history/${address}`, {
        params: { network }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch transaction history:', error)
      throw new Error('Failed to load transaction history')
    }
  }

  async cancelTransaction(hash: string, gasPrice: string): Promise<Transaction> {
    try {
      const response = await api.post(`/blockchain/transactions/${hash}/cancel`, {
        gasPrice
      })
      return response.data
    } catch (error) {
      console.error('Failed to cancel transaction:', error)
      throw new Error('Failed to cancel transaction')
    }
  }

  async speedUpTransaction(hash: string, gasPrice: string): Promise<Transaction> {
    try {
      const response = await api.post(`/blockchain/transactions/${hash}/speed-up`, {
        gasPrice
      })
      return response.data
    } catch (error) {
      console.error('Failed to speed up transaction:', error)
      throw new Error('Failed to speed up transaction')
    }
  }

  async searchTransactions(request: BlockchainSearchRequest): Promise<BlockchainSearchResponse> {
    try {
      const response = await api.post('/blockchain/transactions/search', request)
      return response.data
    } catch (error) {
      console.error('Failed to search transactions:', error)
      throw new Error('Failed to search transactions')
    }
  }

  // ===== GAS MANAGEMENT =====

  async estimateGas(request: EstimateGasRequest): Promise<GasEstimate> {
    try {
      const response = await api.post('/blockchain/gas/estimate', request)
      return response.data
    } catch (error) {
      console.error('Failed to estimate gas:', error)
      throw new Error('Failed to estimate gas costs')
    }
  }

  async getGasPrice(network: BlockchainNetwork): Promise<{
    slow: string
    standard: string
    fast: string
    instant: string
    baseFee?: string
    priorityFee?: string
  }> {
    try {
      const response = await api.get(`/blockchain/gas/price/${network}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch gas price:', error)
      throw new Error('Failed to load gas prices')
    }
  }

  async getGasHistory(network: BlockchainNetwork, timeframe: string = '24h'): Promise<{
    history: Array<{
      timestamp: string
      gasPrice: string
      utilization: number
    }>
    average: string
    peak: string
    low: string
  }> {
    try {
      const response = await api.get(`/blockchain/gas/history/${network}`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch gas history:', error)
      throw new Error('Failed to load gas history')
    }
  }

  async optimizeGas(transactionData: any): Promise<{
    originalGas: string
    optimizedGas: string
    savings: string
    optimizations: string[]
  }> {
    try {
      const response = await api.post('/blockchain/gas/optimize', transactionData)
      return response.data
    } catch (error) {
      console.error('Failed to optimize gas:', error)
      throw new Error('Failed to optimize gas usage')
    }
  }

  // ===== CROSS-CHAIN BRIDGE =====

  async getBridges(): Promise<CrossChainBridge[]> {
    try {
      const response = await api.get('/blockchain/bridges')
      return response.data
    } catch (error) {
      console.error('Failed to fetch bridges:', error)
      throw new Error('Failed to load cross-chain bridges')
    }
  }

  async getBridge(bridgeId: string): Promise<CrossChainBridge> {
    try {
      const response = await api.get(`/blockchain/bridges/${bridgeId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch bridge:', error)
      throw new Error('Failed to load bridge details')
    }
  }

  async bridgeNFT(request: BridgeNFTRequest): Promise<BridgeTransaction> {
    try {
      const response = await api.post('/blockchain/bridges/nft', request)
      return response.data
    } catch (error) {
      console.error('Failed to bridge NFT:', error)
      throw new Error('Failed to bridge NFT')
    }
  }

  async getBridgeTransaction(transactionId: string): Promise<BridgeTransaction> {
    try {
      const response = await api.get(`/blockchain/bridges/transactions/${transactionId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch bridge transaction:', error)
      throw new Error('Failed to load bridge transaction')
    }
  }

  async getBridgeHistory(address: string): Promise<BridgeTransaction[]> {
    try {
      const response = await api.get(`/blockchain/bridges/history/${address}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch bridge history:', error)
      throw new Error('Failed to load bridge history')
    }
  }

  async estimateBridgeFee(request: BridgeNFTRequest): Promise<{
    bridgeFee: string
    gasFees: {
      source: string
      target: string
    }
    totalFee: string
    estimatedTime: number
  }> {
    try {
      const response = await api.post('/blockchain/bridges/estimate-fee', request)
      return response.data
    } catch (error) {
      console.error('Failed to estimate bridge fee:', error)
      throw new Error('Failed to estimate bridge fees')
    }
  }

  // ===== NFT CONTRACT MANAGEMENT =====

  async getNFTContracts(network?: BlockchainNetwork): Promise<NFTContract[]> {
    try {
      const response = await api.get('/blockchain/nft-contracts', {
        params: { network }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch NFT contracts:', error)
      throw new Error('Failed to load NFT contracts')
    }
  }

  async getNFTContract(contractAddress: string, network: BlockchainNetwork): Promise<NFTContract> {
    try {
      const response = await api.get(`/blockchain/nft-contracts/${contractAddress}`, {
        params: { network }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch NFT contract:', error)
      throw new Error('Failed to load NFT contract details')
    }
  }

  async deployNFTContract(contractData: {
    name: string
    symbol: string
    baseTokenURI: string
    maxSupply?: number
    royaltyRecipient?: string
    royaltyPercentage?: number
    network: BlockchainNetwork
  }): Promise<{
    contractAddress: string
    transactionHash: string
    deploymentCost: string
  }> {
    try {
      const response = await api.post('/blockchain/nft-contracts/deploy', contractData)
      return response.data
    } catch (error) {
      console.error('Failed to deploy NFT contract:', error)
      throw new Error('Failed to deploy NFT contract')
    }
  }

  async mintNFT(contractAddress: string, network: BlockchainNetwork, mintData: {
    to: string
    tokenId?: string
    tokenURI: string
    amount?: number
  }): Promise<Transaction> {
    try {
      const response = await api.post(`/blockchain/nft-contracts/${contractAddress}/mint`, {
        ...mintData,
        network
      })
      return response.data
    } catch (error) {
      console.error('Failed to mint NFT:', error)
      throw new Error('Failed to mint NFT')
    }
  }

  async transferNFT(contractAddress: string, network: BlockchainNetwork, transferData: {
    from: string
    to: string
    tokenId: string
    amount?: number
  }): Promise<Transaction> {
    try {
      const response = await api.post(`/blockchain/nft-contracts/${contractAddress}/transfer`, {
        ...transferData,
        network
      })
      return response.data
    } catch (error) {
      console.error('Failed to transfer NFT:', error)
      throw new Error('Failed to transfer NFT')
    }
  }

  async burnNFT(contractAddress: string, network: BlockchainNetwork, burnData: {
    tokenId: string
    amount?: number
  }): Promise<Transaction> {
    try {
      const response = await api.post(`/blockchain/nft-contracts/${contractAddress}/burn`, {
        ...burnData,
        network
      })
      return response.data
    } catch (error) {
      console.error('Failed to burn NFT:', error)
      throw new Error('Failed to burn NFT')
    }
  }

  // ===== MULTI-CHAIN NFT MANAGEMENT =====

  async getMultiChainNFT(nftId: string): Promise<MultiChainNFT> {
    try {
      const response = await api.get(`/blockchain/multi-chain-nft/${nftId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch multi-chain NFT:', error)
      throw new Error('Failed to load multi-chain NFT')
    }
  }

  async syncMultiChainNFT(nftId: string): Promise<MultiChainNFT> {
    try {
      const response = await api.post(`/blockchain/multi-chain-nft/${nftId}/sync`)
      return response.data
    } catch (error) {
      console.error('Failed to sync multi-chain NFT:', error)
      throw new Error('Failed to sync multi-chain NFT')
    }
  }

  async getMultiChainNFTHistory(nftId: string): Promise<{
    bridgeHistory: BridgeTransaction[]
    ownershipHistory: any[]
    syncHistory: any[]
  }> {
    try {
      const response = await api.get(`/blockchain/multi-chain-nft/${nftId}/history`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch multi-chain NFT history:', error)
      throw new Error('Failed to load multi-chain NFT history')
    }
  }

  // ===== ANALYTICS =====

  async getBlockchainAnalytics(network: BlockchainNetwork, timeframe: string = '30d'): Promise<BlockchainAnalytics> {
    try {
      const response = await api.get(`/blockchain/analytics/${network}`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch blockchain analytics:', error)
      throw new Error('Failed to load blockchain analytics')
    }
  }

  async getNetworkStatus(): Promise<Record<BlockchainNetwork, {
    isOnline: boolean
    blockHeight: number
    avgBlockTime: number
    gasPrice: string
    congestion: 'low' | 'medium' | 'high'
  }>> {
    try {
      const response = await api.get('/blockchain/network-status')
      return response.data
    } catch (error) {
      console.error('Failed to fetch network status:', error)
      throw new Error('Failed to load network status')
    }
  }

  async getPortfolioAnalytics(address: string): Promise<{
    totalValue: string
    networks: Record<BlockchainNetwork, {
      nativeBalance: string
      tokenValue: string
      nftCount: number
    }>
    topTokens: any[]
    topNFTs: any[]
    transactionSummary: {
      total: number
      successful: number
      failed: number
      totalGasSpent: string
    }
  }> {
    try {
      const response = await api.get(`/blockchain/portfolio/${address}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch portfolio analytics:', error)
      throw new Error('Failed to load portfolio analytics')
    }
  }
}

export const blockchainIntegrationService = new BlockchainIntegrationService()
