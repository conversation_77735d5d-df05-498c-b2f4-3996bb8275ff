'use client'

import React, { useState } from 'react'
import {
  BellIcon,
  SparklesIcon,
  ClockIcon,
  TrendingUpIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckIcon,
  XMarkIcon,
  CogIcon,
  EyeIcon,
  BoltIcon,
  UserGroupIcon,
  ChartBarIcon,
  CubeIcon
} from '@heroicons/react/24/outline'
import { useOptimizeNotificationTiming } from '@/hooks/useAI'

interface SmartNotificationsProps {
  userId: string
  notifications?: any[]
  className?: string
}

export default function SmartNotifications({
  userId,
  notifications = [],
  className = ''
}: SmartNotificationsProps) {
  const [selectedType, setSelectedType] = useState<'all' | 'price' | 'social' | 'portfolio' | 'market'>('all')
  const [showSettings, setShowSettings] = useState(false)

  const { data: timingOptimization } = useOptimizeNotificationTiming(userId)

  const notificationTypes = [
    { value: 'all', label: 'All Notifications' },
    { value: 'price', label: 'Price Alerts' },
    { value: 'social', label: 'Social Updates' },
    { value: 'portfolio', label: 'Portfolio Changes' },
    { value: 'market', label: 'Market Insights' }
  ]

  // Mock smart notifications if none provided
  const mockNotifications = [
    {
      id: '1',
      type: 'price',
      priority: 'high',
      title: 'Price Alert: CryptoPunk #1234',
      message: 'Price dropped 15% to 45 ETH - potential buying opportunity based on your preferences',
      confidence: 92,
      aiGenerated: true,
      actionable: true,
      action: 'View NFT',
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      isRead: false
    },
    {
      id: '2',
      type: 'social',
      priority: 'medium',
      title: 'Engagement Opportunity',
      message: 'Your follower @artlover just posted about NFTs you collect. Engage now for 40% higher visibility.',
      confidence: 78,
      aiGenerated: true,
      actionable: true,
      action: 'View Post',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      isRead: false
    },
    {
      id: '3',
      type: 'portfolio',
      priority: 'medium',
      title: 'Portfolio Rebalancing Suggestion',
      message: 'Your art NFT allocation is 85%. Consider diversifying into gaming NFTs for better risk distribution.',
      confidence: 85,
      aiGenerated: true,
      actionable: true,
      action: 'View Recommendations',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      isRead: true
    },
    {
      id: '4',
      type: 'market',
      priority: 'high',
      title: 'Market Trend Alert',
      message: 'AI detected unusual trading volume in gaming NFTs. 73% probability of price increase in next 24h.',
      confidence: 89,
      aiGenerated: true,
      actionable: true,
      action: 'View Analysis',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      isRead: true
    }
  ]

  const displayNotifications = notifications.length > 0 ? notifications : mockNotifications
  const filteredNotifications = selectedType === 'all' 
    ? displayNotifications 
    : displayNotifications.filter((notif: any) => notif.type === selectedType)

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'price':
        return <TrendingUpIcon className="h-5 w-5 text-green-600" />
      case 'social':
        return <UserGroupIcon className="h-5 w-5 text-blue-600" />
      case 'portfolio':
        return <ChartBarIcon className="h-5 w-5 text-purple-600" />
      case 'market':
        return <CubeIcon className="h-5 w-5 text-orange-600" />
      default:
        return <BellIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-200 bg-red-50'
      case 'medium':
        return 'border-yellow-200 bg-yellow-50'
      case 'low':
        return 'border-gray-200 bg-gray-50'
      default:
        return 'border-gray-200 bg-white'
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <BellIcon className="h-6 w-6 mr-2 text-blue-600" />
            Smart Notifications
          </h2>
          <p className="text-sm text-gray-600">
            AI-powered alerts and intelligent timing optimization
          </p>
        </div>
        
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <CogIcon className="h-4 w-4 mr-2" />
          Settings
        </button>
      </div>

      {/* Smart Timing Insights */}
      {timingOptimization && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <SparklesIcon className="h-5 w-5 text-blue-600 mr-2" />
              <div>
                <div className="text-sm font-medium text-gray-900">Optimal Notification Timing</div>
                <div className="text-xs text-gray-600">
                  Best times: {timingOptimization.optimalTimes?.join(', ') || '9:00 AM, 2:00 PM, 7:00 PM'}
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                {timingOptimization.engagementIncrease || 35}% higher engagement
              </div>
              <div className="text-xs text-gray-600">when timed optimally</div>
            </div>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <BellIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">
                {displayNotifications.length}
              </div>
              <div className="text-sm text-gray-600">Total Notifications</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <SparklesIcon className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">
                {displayNotifications.filter((n: any) => n.aiGenerated).length}
              </div>
              <div className="text-sm text-gray-600">AI Generated</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">
                {displayNotifications.filter((n: any) => n.priority === 'high').length}
              </div>
              <div className="text-sm text-gray-600">High Priority</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <BoltIcon className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">
                {displayNotifications.filter((n: any) => n.actionable).length}
              </div>
              <div className="text-sm text-gray-600">Actionable</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {notificationTypes.map((type) => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>

        <div className="text-sm text-gray-500">
          {filteredNotifications.length} notifications
        </div>
      </div>

      {/* Notifications List */}
      {filteredNotifications.length > 0 ? (
        <div className="space-y-3">
          {filteredNotifications.map((notification: any) => (
            <NotificationCard
              key={notification.id}
              notification={notification}
              getIcon={getNotificationIcon}
              getPriorityColor={getPriorityColor}
              formatTimeAgo={formatTimeAgo}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
          <p className="mt-1 text-sm text-gray-500">
            {selectedType !== 'all'
              ? `No ${selectedType} notifications found.`
              : 'You\'re all caught up! New smart notifications will appear here.'}
          </p>
        </div>
      )}

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Settings</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">AI Optimization</h4>
              <div className="space-y-2">
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Enable smart timing</span>
                  <input
                    type="checkbox"
                    defaultChecked
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">AI-generated insights</span>
                  <input
                    type="checkbox"
                    defaultChecked
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Predictive alerts</span>
                  <input
                    type="checkbox"
                    defaultChecked
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Notification Types</h4>
              <div className="space-y-2">
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Price alerts</span>
                  <input
                    type="checkbox"
                    defaultChecked
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Social updates</span>
                  <input
                    type="checkbox"
                    defaultChecked
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Portfolio insights</span>
                  <input
                    type="checkbox"
                    defaultChecked
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Market analysis</span>
                  <input
                    type="checkbox"
                    defaultChecked
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Delivery Preferences</h4>
              <div className="space-y-2">
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Push notifications</span>
                  <input
                    type="checkbox"
                    defaultChecked
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Email notifications</span>
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">SMS alerts (high priority only)</span>
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

interface NotificationCardProps {
  notification: any
  getIcon: (type: string) => React.ReactNode
  getPriorityColor: (priority: string) => string
  formatTimeAgo: (timestamp: string) => string
}

function NotificationCard({
  notification,
  getIcon,
  getPriorityColor,
  formatTimeAgo
}: NotificationCardProps) {
  const [isRead, setIsRead] = useState(notification.isRead)

  const handleMarkAsRead = () => {
    setIsRead(true)
  }

  const handleDismiss = () => {
    // Handle dismiss logic
  }

  return (
    <div className={`border rounded-lg p-4 transition-colors ${
      isRead ? 'border-gray-200 bg-white' : getPriorityColor(notification.priority)
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex-shrink-0">
            {getIcon(notification.type)}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className={`text-sm font-medium ${isRead ? 'text-gray-700' : 'text-gray-900'}`}>
                {notification.title}
              </h3>
              
              {notification.aiGenerated && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  <SparklesIcon className="h-3 w-3 mr-1" />
                  AI
                </span>
              )}
              
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                notification.priority === 'high' ? 'bg-red-100 text-red-800' :
                notification.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {notification.priority}
              </span>
              
              {notification.confidence && (
                <span className="text-xs text-gray-500">
                  {notification.confidence}% confidence
                </span>
              )}
            </div>
            
            <p className={`text-sm mb-2 ${isRead ? 'text-gray-600' : 'text-gray-700'}`}>
              {notification.message}
            </p>
            
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <div className="flex items-center">
                <ClockIcon className="h-3 w-3 mr-1" />
                {formatTimeAgo(notification.timestamp)}
              </div>
              
              <span className="capitalize">{notification.type}</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 ml-4">
          {!isRead && (
            <button
              onClick={handleMarkAsRead}
              className="text-blue-600 hover:text-blue-700"
              title="Mark as read"
            >
              <EyeIcon className="h-4 w-4" />
            </button>
          )}
          
          {notification.actionable && notification.action && (
            <button className="inline-flex items-center px-3 py-1 border border-blue-600 text-xs font-medium rounded text-blue-600 bg-white hover:bg-blue-50">
              <CheckIcon className="h-3 w-3 mr-1" />
              {notification.action}
            </button>
          )}
          
          <button
            onClick={handleDismiss}
            className="text-gray-400 hover:text-red-600"
            title="Dismiss"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}
