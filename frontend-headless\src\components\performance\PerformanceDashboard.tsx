'use client'

import React, { useState } from 'react'
import {
  ChartBarIcon,
  CpuChipIcon,
  ServerIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  BoltIcon,
  CubeIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline'
import {
  usePerformanceDashboard,
  useSystemPerformance,
  usePerformanceAlerts,
  useRealtimeMetrics
} from '@/hooks/usePerformance'
import SystemMetrics from './SystemMetrics'
import PerformanceAlerts from './PerformanceAlerts'
import OptimizationRecommendations from './OptimizationRecommendations'
import LoadTestManager from './LoadTestManager'
import CacheManager from './CacheManager'
import DatabaseOptimization from './DatabaseOptimization'

interface PerformanceDashboardProps {
  className?: string
}

export default function PerformanceDashboard({
  className = ''
}: PerformanceDashboardProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'system' | 'alerts' | 'optimization' | 'testing' | 'cache' | 'database'>('overview')

  const { data: dashboardData, isLoading: dashboardLoading } = usePerformanceDashboard()
  const { data: systemData, isLoading: systemLoading } = useSystemPerformance()
  const { data: alerts } = usePerformanceAlerts()
  const { data: realtimeMetrics } = useRealtimeMetrics()

  const tabs = [
    {
      id: 'overview',
      name: 'Overview',
      icon: ChartBarIcon,
      description: 'Performance overview and key metrics'
    },
    {
      id: 'system',
      name: 'System Metrics',
      icon: ServerIcon,
      description: 'CPU, memory, disk, and network metrics'
    },
    {
      id: 'alerts',
      name: 'Alerts',
      icon: ExclamationTriangleIcon,
      count: alerts?.filter(a => a.status === 'active').length || 0,
      description: 'Performance alerts and notifications'
    },
    {
      id: 'optimization',
      name: 'Optimization',
      icon: BoltIcon,
      description: 'Performance optimization recommendations'
    },
    {
      id: 'testing',
      name: 'Load Testing',
      icon: CpuChipIcon,
      description: 'Load testing and performance validation'
    },
    {
      id: 'cache',
      name: 'Cache Management',
      icon: CubeIcon,
      description: 'Cache strategies and performance'
    },
    {
      id: 'database',
      name: 'Database',
      icon: ServerIcon,
      description: 'Database optimization and query analysis'
    }
  ]

  const quickStats = [
    {
      label: 'Response Time',
      value: dashboardData?.responseTime?.average || 0,
      unit: 'ms',
      change: dashboardData?.responseTime?.change || 0,
      trend: dashboardData?.responseTime?.trend || 'stable',
      icon: ClockIcon,
      color: 'text-blue-600',
      target: 200
    },
    {
      label: 'Throughput',
      value: dashboardData?.throughput?.requestsPerSecond || 0,
      unit: 'req/s',
      change: dashboardData?.throughput?.change || 0,
      trend: dashboardData?.throughput?.trend || 'stable',
      icon: ArrowTrendingUpIcon,
      color: 'text-green-600',
      target: 1000
    },
    {
      label: 'Error Rate',
      value: dashboardData?.errorRate?.percentage || 0,
      unit: '%',
      change: dashboardData?.errorRate?.change || 0,
      trend: dashboardData?.errorRate?.trend || 'stable',
      icon: ExclamationTriangleIcon,
      color: 'text-red-600',
      target: 1
    },
    {
      label: 'Availability',
      value: dashboardData?.availability?.percentage || 99.9,
      unit: '%',
      change: dashboardData?.availability?.change || 0,
      trend: dashboardData?.availability?.trend || 'stable',
      icon: CheckCircleIcon,
      color: 'text-purple-600',
      target: 99.9
    }
  ]

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <ArrowTrendingUpIcon className="h-4 w-4 text-green-600" />
      case 'degrading':
        return <ArrowTrendingDownIcon className="h-4 w-4 text-red-600" />
      default:
        return <div className="h-4 w-4 bg-gray-400 rounded-full" />
    }
  }

  const getStatusColor = (value: number, target: number, isErrorRate = false) => {
    if (isErrorRate) {
      if (value <= target) return 'text-green-600'
      if (value <= target * 2) return 'text-yellow-600'
      return 'text-red-600'
    } else {
      if (value >= target) return 'text-green-600'
      if (value >= target * 0.8) return 'text-yellow-600'
      return 'text-red-600'
    }
  }

  if (dashboardLoading || systemLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <ChartBarIcon className="h-8 w-8 mr-3 text-blue-600" />
            Performance Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Monitor, optimize, and enhance platform performance
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">System Status</div>
            <div className="text-xs text-green-600 flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              All systems operational
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat) => (
          <div key={stat.label} className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                <div className="flex items-baseline space-x-2 mt-1">
                  <p className={`text-2xl font-bold ${getStatusColor(stat.value, stat.target, stat.label === 'Error Rate')}`}>
                    {stat.value.toFixed(stat.label === 'Error Rate' ? 2 : 0)}
                  </p>
                  <span className="text-sm text-gray-500">{stat.unit}</span>
                </div>
                <div className="flex items-center mt-1">
                  {getTrendIcon(stat.trend)}
                  <span className={`text-sm ml-1 ${
                    stat.change > 0 && stat.label !== 'Error Rate' ? 'text-green-600' :
                    stat.change < 0 && stat.label === 'Error Rate' ? 'text-green-600' :
                    stat.change === 0 ? 'text-gray-600' :
                    'text-red-600'
                  }`}>
                    {stat.change > 0 ? '+' : ''}{stat.change.toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className={`p-3 rounded-lg bg-gray-50`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </div>
            
            {/* Progress bar */}
            <div className="mt-4">
              <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                <span>Target: {stat.target}{stat.unit}</span>
                <span>{((stat.value / stat.target) * 100).toFixed(0)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    getStatusColor(stat.value, stat.target, stat.label === 'Error Rate').includes('green') ? 'bg-green-500' :
                    getStatusColor(stat.value, stat.target, stat.label === 'Error Rate').includes('yellow') ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}
                  style={{
                    width: `${Math.min(100, (stat.value / stat.target) * 100)}%`
                  }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Real-time Metrics */}
      {realtimeMetrics && realtimeMetrics.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <BoltIcon className="h-5 w-5 mr-2 text-orange-600" />
            Real-time Metrics
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {realtimeMetrics.slice(0, 6).map((metric, index) => (
              <div key={index} className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {metric.value.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600">{metric.name}</div>
                <div className="text-xs text-gray-500">{metric.unit}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
              {tab.count !== undefined && tab.count > 0 && (
                <span className="ml-2 bg-red-100 text-red-900 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-96">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Performance Summary */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Summary</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Overall Health Score</span>
                    <span className="text-lg font-semibold text-green-600">
                      {dashboardData?.healthScore || 95}/100
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Active Optimizations</span>
                    <span className="text-lg font-semibold text-blue-600">
                      {dashboardData?.activeOptimizations || 3}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Performance Trend</span>
                    <div className="flex items-center">
                      {getTrendIcon(dashboardData?.overallTrend || 'improving')}
                      <span className="text-sm text-green-600 ml-1">Improving</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Alerts */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Alerts</h3>
                {alerts && alerts.length > 0 ? (
                  <div className="space-y-3">
                    {alerts.slice(0, 3).map((alert) => (
                      <div key={alert.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                        <ExclamationTriangleIcon className={`h-4 w-4 ${
                          alert.severity === 'critical' ? 'text-red-600' :
                          alert.severity === 'high' ? 'text-orange-600' :
                          'text-yellow-600'
                        }`} />
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900">{alert.title}</div>
                          <div className="text-xs text-gray-600">{alert.description}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    <CheckCircleIcon className="mx-auto h-8 w-8 mb-2" />
                    <p className="text-sm">No active alerts</p>
                  </div>
                )}
              </div>
            </div>

            {/* Performance Trends Chart Placeholder */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Trends</h3>
              <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <ChartBarIcon className="mx-auto h-12 w-12 mb-2" />
                  <p>Performance trends chart would be displayed here</p>
                  <p className="text-xs">Integration with charting library needed</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'system' && (
          <SystemMetrics systemData={systemData} />
        )}

        {activeTab === 'alerts' && (
          <PerformanceAlerts alerts={alerts} />
        )}

        {activeTab === 'optimization' && (
          <OptimizationRecommendations />
        )}

        {activeTab === 'testing' && (
          <LoadTestManager />
        )}

        {activeTab === 'cache' && (
          <CacheManager />
        )}

        {activeTab === 'database' && (
          <DatabaseOptimization />
        )}
      </div>

      {/* Performance Status Footer */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2" />
            <div>
              <div className="text-sm font-medium text-gray-900">Performance Status</div>
              <div className="text-xs text-gray-600">
                Last updated: {new Date().toLocaleTimeString()} • 
                Monitoring {dashboardData?.monitoredServices || 9} services • 
                Processing {dashboardData?.metricsPerSecond || 1247} metrics/sec
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 text-xs text-gray-600">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              System: Healthy
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Database: Optimal
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Cache: Active
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
