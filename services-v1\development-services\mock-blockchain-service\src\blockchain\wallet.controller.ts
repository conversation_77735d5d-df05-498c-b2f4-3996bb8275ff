import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BlockchainService } from './blockchain.service';

@ApiTags('wallet')
@Controller('wallet')
export class WalletController {
  constructor(private readonly blockchainService: BlockchainService) {}

  @Get(':address')
  @ApiOperation({ summary: 'Get wallet information' })
  @ApiResponse({ status: 200, description: 'Wallet information retrieved successfully' })
  async getWalletInfo(@Param('address') address: string) {
    const wallet = await this.blockchainService.getWalletInfo(address);
    return {
      status: wallet ? 'success' : 'error',
      data: wallet,
      message: wallet ? 'Wallet information retrieved successfully' : 'Wallet not found'
    };
  }
}
