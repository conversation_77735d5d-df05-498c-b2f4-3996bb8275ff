import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { AppConfig } from './config/app.config';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    const app = await NestFactory.create(AppModule);
    
    // Get configuration
    const config = app.get(AppConfig);
    
    // Enable CORS
    app.enableCors({
      origin: config.cors.origin,
      credentials: config.cors.credentials,
    });
    
    // Global validation pipe
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));
    
    // API prefix
    app.setGlobalPrefix('api');
    
    // Swagger documentation
    const swaggerConfig = new DocumentBuilder()
      .setTitle('Notification Service')
      .setDescription('Real-time Notifications & Communication API')
      .setVersion('1.0')
      .addTag('notifications', 'Notification management operations')
      .addTag('templates', 'Notification template operations')
      .addTag('preferences', 'User notification preferences')
      .addTag('channels', 'Communication channel operations')
      .addTag('events', 'Event-driven notifications')
      .addTag('health', 'Health check operations')
      .build();
    
    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('api/docs', app, document);
    
    // Start server
    await app.listen(config.port);
    
    logger.log(`🚀 Notification Service is running on: http://localhost:${config.port}`);
    logger.log(`📚 API Documentation: http://localhost:${config.port}/api/docs`);
    logger.log(`🏥 Health Check: http://localhost:${config.port}/api/health`);
    logger.log(`🌍 Environment: ${config.nodeEnv}`);
    logger.log(`📧 Notification Features: Email, SMS, Push, In-App notifications`);
    
  } catch (error) {
    logger.error('❌ Failed to start Notification Service', error);
    process.exit(1);
  }
}

bootstrap();
