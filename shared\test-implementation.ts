/**
 * Shared Infrastructure Test Implementation
 * 
 * Comprehensive test to verify all shared infrastructure components work correctly
 * This file demonstrates proper usage and validates the implementation
 */

import { Test, TestingModule } from '@nestjs/testing';
import { Controller, Get, Post, Body, Module } from '@nestjs/common';
import {
  SharedInfrastructureModule,
  ResponseService,
  ServiceLoggerService,
  StandardizedConfigService,
  StandardizedPrismaService,
  RequirePermissions,
  Permission,
  Public,
  BusinessOutcome,
  SecurityEventType,
  SecuritySeverity,
  setupBusinessService,
  setupTesting,
} from './index';

/**
 * Test DTO
 */
class CreateTestDto {
  name: string;
  email: string;
}

/**
 * Test Controller
 */
@Controller('test')
class TestController {
  constructor(
    private readonly responseService: ResponseService,
    private readonly logger: ServiceLoggerService
  ) {}

  @Get('public')
  @Public()
  async getPublicData() {
    this.logger.info('Public endpoint accessed');
    return this.responseService.success({ message: 'Public data' });
  }

  @Get('protected')
  @RequirePermissions(Permission.USER_READ)
  async getProtectedData() {
    this.logger.logBusinessEvent('test', 'read', BusinessOutcome.SUCCESS);
    return this.responseService.success({ message: 'Protected data' });
  }

  @Post('create')
  @RequirePermissions(Permission.USER_CREATE)
  async createData(@Body() dto: CreateTestDto) {
    this.logger.logBusinessEvent('test', 'create', BusinessOutcome.SUCCESS, {
      business: {
        entity: 'test',
        attributes: { name: dto.name, email: dto.email },
      },
    });
    
    return this.responseService.created({ id: '123', ...dto });
  }

  @Get('error')
  async triggerError() {
    this.logger.logSecurityEvent(
      SecurityEventType.SUSPICIOUS_ACTIVITY,
      SecuritySeverity.HIGH,
      'Test error triggered'
    );
    
    throw new Error('Test error for validation');
  }

  @Get('performance')
  async testPerformance() {
    const startTime = Date.now();
    
    // Simulate some work
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const duration = Date.now() - startTime;
    this.logger.logPerformanceMetric('test-operation', duration, true);
    
    return this.responseService.success({ duration });
  }
}

/**
 * Test Service
 */
class TestService {
  constructor(
    private readonly prisma: StandardizedPrismaService,
    private readonly logger: ServiceLoggerService
  ) {}

  async testDatabaseOperation() {
    return this.prisma.executeWithMetrics('testQuery', async () => {
      // Simulate database operation
      await new Promise(resolve => setTimeout(resolve, 50));
      return { result: 'success' };
    });
  }

  async testHealthCheck() {
    return this.prisma.healthCheck();
  }
}

/**
 * Test Module
 */
@Module({
  imports: [
    setupTesting('shared-infrastructure-test'),
  ],
  controllers: [TestController],
  providers: [TestService],
})
class TestModule {}

/**
 * Test Suite
 */
describe('Shared Infrastructure Integration Test', () => {
  let app: TestingModule;
  let responseService: ResponseService;
  let logger: ServiceLoggerService;
  let configService: StandardizedConfigService;
  let prismaService: StandardizedPrismaService;
  let testService: TestService;

  beforeAll(async () => {
    app = await Test.createTestingModule({
      imports: [TestModule],
    }).compile();

    responseService = app.get<ResponseService>(ResponseService);
    logger = app.get<ServiceLoggerService>(ServiceLoggerService);
    configService = app.get<StandardizedConfigService>(StandardizedConfigService);
    prismaService = app.get<StandardizedPrismaService>(StandardizedPrismaService);
    testService = app.get<TestService>(TestService);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Configuration Service', () => {
    it('should provide service configuration', () => {
      const serviceConfig = configService.getServiceConfig();
      
      expect(serviceConfig).toBeDefined();
      expect(serviceConfig.serviceName).toBe('shared-infrastructure-test');
      expect(serviceConfig.version).toBe('1.0.0-test');
      expect(serviceConfig.nodeEnv).toBeDefined();
    });

    it('should provide database configuration', () => {
      const dbConfig = configService.getDatabaseConfig();
      
      expect(dbConfig).toBeDefined();
      expect(dbConfig.host).toBeDefined();
      expect(dbConfig.port).toBeDefined();
      expect(dbConfig.database).toBeDefined();
    });

    it('should provide JWT configuration', () => {
      const jwtConfig = configService.getJWTConfig();
      
      expect(jwtConfig).toBeDefined();
      expect(jwtConfig.secret).toBeDefined();
      expect(jwtConfig.expiresIn).toBeDefined();
    });
  });

  describe('Response Service', () => {
    it('should create success response', () => {
      const data = { message: 'test' };
      const response = responseService.success(data, 'Test successful');
      
      expect(response.success).toBe(true);
      expect(response.data).toEqual(data);
      expect(response.message).toBe('Test successful');
      expect(response.correlationId).toBeDefined();
      expect(response.timestamp).toBeDefined();
      expect(response.service).toBe('shared-infrastructure-test');
    });

    it('should create error response', () => {
      const response = responseService.error('Test error');
      
      expect(response.success).toBe(false);
      expect(response.error).toBeDefined();
      expect(response.error.message).toBe('Test error');
      expect(response.correlationId).toBeDefined();
    });

    it('should create paginated response', () => {
      const data = [{ id: 1 }, { id: 2 }];
      const pagination = responseService.buildPaginationMeta(1, 10, 2);
      const response = responseService.paginated(data, pagination);
      
      expect(response.success).toBe(true);
      expect(response.data).toEqual(data);
      expect(response.pagination).toBeDefined();
      expect(response.pagination.totalCount).toBe(2);
    });

    it('should create not found response', () => {
      const response = responseService.notFound('User', '123');
      
      expect(response.success).toBe(false);
      expect(response.error.message).toContain('User');
      expect(response.error.message).toContain('123');
    });
  });

  describe('Logging Service', () => {
    it('should log business events', () => {
      // This test verifies the logger doesn't throw errors
      expect(() => {
        logger.logBusinessEvent('test', 'create', BusinessOutcome.SUCCESS);
      }).not.toThrow();
    });

    it('should log security events', () => {
      expect(() => {
        logger.logSecurityEvent(
          SecurityEventType.AUTHENTICATION,
          SecuritySeverity.MEDIUM,
          'Test security event'
        );
      }).not.toThrow();
    });

    it('should log performance metrics', () => {
      expect(() => {
        logger.logPerformanceMetric('test-operation', 100, true);
      }).not.toThrow();
    });

    it('should create child loggers', () => {
      const childLogger = logger.withCorrelationId('test-correlation-id');
      expect(childLogger).toBeDefined();
      expect(childLogger).toBeInstanceOf(ServiceLoggerService);
    });
  });

  describe('Database Service', () => {
    it('should provide connection status', () => {
      const status = prismaService.getConnectionStatus();
      
      expect(status).toBeDefined();
      expect(status.databaseUrl).toBeDefined();
      expect(status.maxConnections).toBeDefined();
    });

    it('should execute queries with metrics', async () => {
      const result = await testService.testDatabaseOperation();
      
      expect(result).toBeDefined();
      expect(result.result).toBe('success');
    });

    it('should provide health check', async () => {
      const health = await testService.testHealthCheck();
      
      expect(health).toBeDefined();
      expect(health.status).toBeDefined();
      expect(health.details).toBeDefined();
    });

    it('should track query metrics', () => {
      const metrics = prismaService.getQueryMetrics();
      expect(Array.isArray(metrics)).toBe(true);
    });

    it('should provide performance summary', () => {
      const summary = prismaService.getQueryPerformanceSummary();
      
      expect(summary).toBeDefined();
      expect(typeof summary.totalQueries).toBe('number');
      expect(typeof summary.averageDuration).toBe('number');
      expect(typeof summary.successRate).toBe('number');
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete request flow', async () => {
      // Test the complete flow: request -> logging -> response -> metrics
      const testData = { name: 'Test', email: '<EMAIL>' };
      
      // This would normally be tested with actual HTTP requests
      // For now, we test the service layer integration
      const response = responseService.created(testData, 'Test created');
      
      expect(response.success).toBe(true);
      expect(response.data).toEqual(testData);
      expect(response.message).toBe('Test created');
    });

    it('should handle error scenarios', () => {
      expect(() => {
        throw new Error('Test error');
      }).toThrow('Test error');
      
      // Verify error response creation
      const errorResponse = responseService.error('Test error');
      expect(errorResponse.success).toBe(false);
    });

    it('should maintain correlation IDs', () => {
      const correlationId = 'test-correlation-123';
      const response = responseService.success({ test: true }, 'Test', correlationId);
      
      expect(response.correlationId).toBe(correlationId);
    });
  });

  describe('Type Guards', () => {
    it('should identify standardized responses', () => {
      const response = responseService.success({ test: true });
      
      // Import type guards from index
      const { TypeGuards } = require('./index');
      
      expect(TypeGuards.isStandardizedResponse(response)).toBe(true);
      expect(TypeGuards.isSuccessResponse(response)).toBe(true);
      expect(TypeGuards.isErrorResponse(response)).toBe(false);
    });

    it('should identify error responses', () => {
      const response = responseService.error('Test error');
      
      const { TypeGuards } = require('./index');
      
      expect(TypeGuards.isStandardizedResponse(response)).toBe(true);
      expect(TypeGuards.isErrorResponse(response)).toBe(true);
      expect(TypeGuards.isSuccessResponse(response)).toBe(false);
    });
  });

  describe('Constants and Helpers', () => {
    it('should provide shared constants', () => {
      const { SharedConstants } = require('./index');
      
      expect(SharedConstants.PAGINATION.DEFAULT_PAGE).toBe(1);
      expect(SharedConstants.PAGINATION.DEFAULT_LIMIT).toBe(20);
      expect(SharedConstants.TIMEOUTS.DEFAULT_REQUEST).toBe(30000);
    });

    it('should provide version information', () => {
      const { SharedInfrastructureVersion } = require('./index');
      
      expect(SharedInfrastructureVersion.version).toBeDefined();
      expect(SharedInfrastructureVersion.features).toBeInstanceOf(Array);
      expect(SharedInfrastructureVersion.features.length).toBeGreaterThan(0);
    });
  });
});

/**
 * Manual Test Runner
 * Run this to manually test the shared infrastructure
 */
export async function runManualTests() {
  console.log('🧪 Running Shared Infrastructure Manual Tests...\n');

  try {
    // Test 1: Configuration
    console.log('1️⃣ Testing Configuration Service...');
    const app = await Test.createTestingModule({
      imports: [TestModule],
    }).compile();

    const configService = app.get<StandardizedConfigService>(StandardizedConfigService);
    const serviceConfig = configService.getServiceConfig();
    console.log('✅ Service Config:', serviceConfig);

    // Test 2: Response Service
    console.log('\n2️⃣ Testing Response Service...');
    const responseService = app.get<ResponseService>(ResponseService);
    const successResponse = responseService.success({ test: true }, 'Test successful');
    console.log('✅ Success Response:', JSON.stringify(successResponse, null, 2));

    // Test 3: Logging Service
    console.log('\n3️⃣ Testing Logging Service...');
    const logger = app.get<ServiceLoggerService>(ServiceLoggerService);
    logger.logBusinessEvent('test', 'manual-test', BusinessOutcome.SUCCESS);
    console.log('✅ Business event logged');

    // Test 4: Database Service
    console.log('\n4️⃣ Testing Database Service...');
    const prismaService = app.get<StandardizedPrismaService>(StandardizedPrismaService);
    const connectionStatus = prismaService.getConnectionStatus();
    console.log('✅ Connection Status:', connectionStatus);

    await app.close();
    console.log('\n🎉 All manual tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Manual test failed:', error);
    throw error;
  }
}

// Export for use in other test files
export { TestController, TestService, TestModule };
