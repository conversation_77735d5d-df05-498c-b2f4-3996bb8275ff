import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
const jwt = require('jsonwebtoken');

@Injectable()
export class JwtService {
  private readonly logger = new Logger(JwtService.name);
  private readonly jwtSecret: string;
  private readonly jwtExpiresIn: string;

  constructor(private readonly configService: ConfigService) {
    this.jwtSecret = this.configService.get('JWT_SECRET', 'social-nft-platform-secret-key');
    this.jwtExpiresIn = this.configService.get('JWT_EXPIRES_IN', '24h');
    
    this.logger.log('🔐 JWT Service initialized');
    this.logger.log(`⏰ Token expiration: ${this.jwtExpiresIn}`);
  }

  /**
   * Generate JWT token for user
   */
  async generateToken(user: any): Promise<string> {
    try {
      this.logger.log(`🔐 Generating JWT token for user: ${user.username}`);

      const payload = {
        sub: user.id, // Subject (user ID)
        username: user.username,
        email: user.email,
        displayName: user.displayName,
        provider: user.provider,
        providerId: user.providerId,
        isActive: user.isActive,
        role: user.role || 'user',
        iat: Math.floor(Date.now() / 1000) // Issued at
        // Note: exp will be set by jwt.sign() using expiresIn option
      };

      const token = jwt.sign(payload, this.jwtSecret, {
        algorithm: 'HS256',
        expiresIn: this.jwtExpiresIn || '24h'
      });

      this.logger.log(`✅ JWT token generated successfully for user: ${user.username}`);
      return token;

    } catch (error) {
      this.logger.error('❌ Error generating JWT token:', error);
      throw new Error(`Failed to generate JWT token: ${error.message}`);
    }
  }

  /**
   * Validate JWT token
   */
  async validateToken(token: string): Promise<any> {
    try {
      this.logger.log('🔍 Validating JWT token');

      const decoded = jwt.verify(token, this.jwtSecret) as any;
      
      this.logger.log(`✅ JWT token validated for user: ${decoded.username}`);
      return {
        success: true,
        data: {
          user: {
            id: decoded.sub,
            username: decoded.username,
            email: decoded.email,
            displayName: decoded.displayName,
            provider: decoded.provider,
            providerId: decoded.providerId,
            isActive: decoded.isActive,
            role: decoded.role
          },
          issuedAt: new Date(decoded.iat * 1000),
          expiresAt: new Date(decoded.exp * 1000)
        }
      };

    } catch (error) {
      this.logger.error('❌ JWT token validation failed:', error.message);
      
      if (error.name === 'TokenExpiredError') {
        return {
          success: false,
          error: 'Token expired',
          code: 'TOKEN_EXPIRED'
        };
      } else if (error.name === 'JsonWebTokenError') {
        return {
          success: false,
          error: 'Invalid token',
          code: 'INVALID_TOKEN'
        };
      } else {
        return {
          success: false,
          error: 'Token validation failed',
          code: 'VALIDATION_FAILED'
        };
      }
    }
  }

  /**
   * Decode JWT token without validation (for debugging)
   */
  decodeToken(token: string): any {
    try {
      const decoded = jwt.decode(token);
      this.logger.log('🔍 JWT token decoded (without validation)');
      return decoded;
    } catch (error) {
      this.logger.error('❌ Error decoding JWT token:', error);
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as any;
      if (!decoded || !decoded.exp) {
        return true;
      }
      
      const currentTime = Math.floor(Date.now() / 1000);
      return decoded.exp < currentTime;
    } catch (error) {
      this.logger.error('❌ Error checking token expiration:', error);
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  getTokenExpiration(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any;
      if (!decoded || !decoded.exp) {
        return null;
      }
      
      return new Date(decoded.exp * 1000);
    } catch (error) {
      this.logger.error('❌ Error getting token expiration:', error);
      return null;
    }
  }

  /**
   * Parse expiration time string to seconds
   */
  private parseExpirationTime(expiresIn: string): number {
    const timeValue = parseInt(expiresIn);
    const timeUnit = expiresIn.replace(timeValue.toString(), '');

    switch (timeUnit) {
      case 's': // seconds
        return timeValue;
      case 'm': // minutes
        return timeValue * 60;
      case 'h': // hours
        return timeValue * 60 * 60;
      case 'd': // days
        return timeValue * 24 * 60 * 60;
      default:
        this.logger.warn(`Unknown time unit: ${timeUnit}, defaulting to seconds`);
        return timeValue;
    }
  }

  /**
   * Generate refresh token (longer expiration)
   */
  async generateRefreshToken(user: any): Promise<string> {
    try {
      this.logger.log(`🔄 Generating refresh token for user: ${user.username}`);

      const payload = {
        sub: user.id,
        username: user.username,
        type: 'refresh',
        iat: Math.floor(Date.now() / 1000)
        // Note: exp will be set by jwt.sign() using expiresIn option
      };

      const refreshToken = jwt.sign(payload, this.jwtSecret, {
        algorithm: 'HS256',
        expiresIn: '30d'
      });

      this.logger.log(`✅ Refresh token generated for user: ${user.username}`);
      return refreshToken;

    } catch (error) {
      this.logger.error('❌ Error generating refresh token:', error);
      throw new Error(`Failed to generate refresh token: ${error.message}`);
    }
  }

  /**
   * Validate refresh token
   */
  async validateRefreshToken(refreshToken: string): Promise<any> {
    try {
      this.logger.log('🔍 Validating refresh token');

      const decoded = jwt.verify(refreshToken, this.jwtSecret) as any;
      
      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }
      
      this.logger.log(`✅ Refresh token validated for user: ${decoded.username}`);
      return {
        success: true,
        data: {
          userId: decoded.sub,
          username: decoded.username
        }
      };

    } catch (error) {
      this.logger.error('❌ Refresh token validation failed:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get service info
   */
  getServiceInfo(): any {
    return {
      serviceName: 'JwtService',
      version: '1.0.0',
      jwtExpiresIn: this.jwtExpiresIn,
      algorithm: 'HS256',
      features: [
        'Token generation',
        'Token validation', 
        'Refresh tokens',
        'Expiration checking'
      ]
    };
  }
}
