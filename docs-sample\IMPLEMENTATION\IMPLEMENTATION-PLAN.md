# 🚀 **COMPREHENSIVE IMPLEMENTATION PLAN: API GATEWAY & ENTERPRISE STANDARDIZATION**

Based on our analysis, here's a detailed step-by-step plan to redesign your platform from scratch while preserving your business logic.

## 🎯 **IMPLEMENTATION STRATEGY**

**Approach**: Keep existing business logic, rebuild infrastructure from scratch for clean architecture

**Timeline**: 3-4 weeks total
- **Week 1**: Foundation & Shared Infrastructure
- **Week 2**: API Gateway & Service Standardization  
- **Week 3**: Service Migration & Testing
- **Week 4**: Integration & Optimization

---

## 📋 **PHASE 1: FOUNDATION & SHARED INFRASTRUCTURE (Week 1)**

### **Step 1.1: Create Shared Infrastructure Foundation (Day 1-2)**

#### **1.1.1 Create Shared Module Structure**
```bash
# Create shared infrastructure
mkdir -p shared/{auth,responses,logging,data,config,utils}
mkdir -p shared/auth/{interfaces,guards,decorators,middleware,strategies,services}
mkdir -p shared/responses/{interfaces,dto,interceptors,decorators,utils}
mkdir -p shared/logging/{interfaces,services,interceptors}
mkdir -p shared/data/{repositories,services,interfaces}
mkdir -p shared/config/{interfaces,services,validation}
```

#### **1.1.2 Implement Core Interfaces**
Create foundational interfaces that all services will use:

**File: `shared/responses/interfaces/base-response.interface.ts`**
```typescript
export interface BaseResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  correlationId: string;
  timestamp: string;
  service: string;
  version: string;
}

export interface SuccessResponse<T = any> extends BaseResponse<T> {
  success: true;
  data: T;
}

export interface ErrorResponse extends BaseResponse<never> {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    stack?: string;
    validation?: ValidationError[];
  };
  statusCode: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  nextPage?: number;
  prevPage?: number;
}

export interface PaginatedResponse<T = any> extends BaseResponse<T[]> {
  success: true;
  data: T[];
  pagination: PaginationMeta;
  filters?: Record<string, any>;
  sorting?: SortingOptions;
}
```

**File: `shared/auth/interfaces/auth.interface.ts`**
```typescript
export interface AuthenticatedUser {
  id: string;
  email: string;
  username?: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  lastLoginAt?: Date;
  sessionId?: string;
}

export enum Permission {
  // User Management
  USER_READ = 'user:read',
  USER_WRITE = 'user:write',
  USER_DELETE = 'user:delete',
  USER_ADMIN = 'user:admin',
  
  // Store Management
  STORE_READ = 'store:read',
  STORE_WRITE = 'store:write',
  STORE_DELETE = 'store:delete',
  STORE_ADMIN = 'store:admin',
  
  // Product Management
  PRODUCT_READ = 'product:read',
  PRODUCT_WRITE = 'product:write',
  PRODUCT_DELETE = 'product:delete',
  PRODUCT_ADMIN = 'product:admin',
  
  // System Administration
  SYSTEM_READ = 'system:read',
  SYSTEM_ADMIN = 'system:admin',
}

export enum Role {
  GUEST = 'guest',
  USER = 'user',
  STORE_OWNER = 'store_owner',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}
```

#### **1.1.3 Create Configuration Infrastructure**

**File: `shared/config/interfaces/config.interface.ts`**
```typescript
export interface ServiceConfig {
  serviceName: string;
  port: number;
  nodeEnv: 'development' | 'production' | 'test';
  version: string;
}

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  url: string;
}

export interface JWTConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
}

export interface RabbitMQConfig {
  url: string;
  queue: string;
  exchange: string;
}
```

**File: `shared/config/services/config.service.ts`**
```typescript
import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { ServiceConfig, DatabaseConfig, JWTConfig } from '../interfaces/config.interface';

@Injectable()
export class StandardizedConfigService {
  constructor(private readonly configService: NestConfigService) {}

  getServiceConfig(): ServiceConfig {
    return {
      serviceName: this.configService.get<string>('SERVICE_NAME')!,
      port: this.configService.get<number>('SERVICE_PORT')!,
      nodeEnv: this.configService.get<'development' | 'production' | 'test'>('NODE_ENV')!,
      version: this.configService.get<string>('SERVICE_VERSION', '1.0.0'),
    };
  }

  getDatabaseConfig(): DatabaseConfig {
    return {
      host: this.configService.get<string>('DB_HOST')!,
      port: this.configService.get<number>('DB_PORT')!,
      database: this.configService.get<string>('DB_DATABASE')!,
      username: this.configService.get<string>('DB_USERNAME')!,
      password: this.configService.get<string>('DB_PASSWORD')!,
      url: this.configService.get<string>('DATABASE_URL')!,
    };
  }

  getJWTConfig(): JWTConfig {
    return {
      secret: this.configService.get<string>('JWT_SECRET')!,
      expiresIn: this.configService.get<string>('JWT_EXPIRES_IN', '24h'),
      refreshExpiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '7d'),
    };
  }
}
```

### **Step 1.2: Implement Response Standardization (Day 2-3)**

#### **1.2.1 Response Service Implementation**

**File: `shared/responses/services/response.service.ts`**
```typescript
import { Injectable } from '@nestjs/common';
import { StandardizedConfigService } from '../../config/services/config.service';
import { BaseResponse, SuccessResponse, ErrorResponse, PaginatedResponse, PaginationMeta } from '../interfaces/base-response.interface';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ResponseService {
  constructor(private readonly configService: StandardizedConfigService) {}

  private createBaseResponse(): Omit<BaseResponse, 'success' | 'data'> {
    const serviceConfig = this.configService.getServiceConfig();
    return {
      correlationId: `${serviceConfig.serviceName}_${Date.now()}_${uuidv4().substring(0, 8)}`,
      timestamp: new Date().toISOString(),
      service: serviceConfig.serviceName,
      version: serviceConfig.version,
    };
  }

  success<T>(data: T, message?: string): SuccessResponse<T> {
    return {
      ...this.createBaseResponse(),
      success: true,
      data,
      message,
    };
  }

  created<T>(data: T, message?: string): SuccessResponse<T> {
    return {
      ...this.createBaseResponse(),
      success: true,
      data,
      message: message || 'Resource created successfully',
    };
  }

  paginated<T>(
    data: T[],
    pagination: PaginationMeta,
    options?: { filters?: Record<string, any>; sorting?: any; message?: string }
  ): PaginatedResponse<T> {
    return {
      ...this.createBaseResponse(),
      success: true,
      data,
      pagination,
      filters: options?.filters,
      sorting: options?.sorting,
      message: options?.message || 'Data retrieved successfully',
    };
  }

  error(
    statusCode: number,
    code: string,
    message: string,
    details?: any,
    validation?: any[]
  ): ErrorResponse {
    return {
      ...this.createBaseResponse(),
      success: false,
      error: {
        code,
        message,
        details,
        validation,
        stack: this.configService.getServiceConfig().nodeEnv === 'development' ? new Error().stack : undefined,
      },
      statusCode,
    };
  }
}
```

#### **1.2.2 Response Interceptor**

**File: `shared/responses/interceptors/response-transform.interceptor.ts`**
```typescript
import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ResponseService } from '../services/response.service';

@Injectable()
export class ResponseTransformInterceptor implements NestInterceptor {
  constructor(private readonly responseService: ResponseService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        // If data is already a standardized response, return as-is
        if (data && typeof data === 'object' && 'success' in data && 'correlationId' in data) {
          return data;
        }

        // Transform raw data into standardized response
        return this.responseService.success(data);
      }),
    );
  }
}
```

### **Step 1.3: Implement Authentication Infrastructure (Day 3-4)**

#### **1.3.1 Authentication Guards**

**File: `shared/auth/guards/jwt-auth.guard.ts`**
```typescript
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { StandardizedConfigService } from '../../config/services/config.service';

@Injectable()
export class StandardizedJwtAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector,
    private readonly configService: StandardizedConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is public
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // Extract and validate JWT token
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('Authentication token required');
    }

    try {
      const jwtConfig = this.configService.getJWTConfig();
      const payload = await this.jwtService.verifyAsync(token, {
        secret: jwtConfig.secret,
      });

      // Add user to request context
      request.user = payload;
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

**File: `shared/auth/guards/permissions.guard.ts`**
```typescript
import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Permission } from '../interfaces/auth.interface';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>('permissions', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User context not found');
    }

    const hasPermission = requiredPermissions.some(permission =>
      user.permissions?.includes(permission)
    );

    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions');
    }

    return true;
  }
}
```

#### **1.3.2 Authentication Decorators**

**File: `shared/auth/decorators/auth.decorator.ts`**
```typescript
import { SetMetadata, createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Permission, Role, AuthenticatedUser } from '../interfaces/auth.interface';

export const Public = () => SetMetadata('isPublic', true);

export const RequirePermissions = (...permissions: Permission[]) =>
  SetMetadata('permissions', permissions);

export const RequireRoles = (...roles: Role[]) =>
  SetMetadata('roles', roles);

export const CurrentUser = createParamDecorator(
  (data: keyof AuthenticatedUser | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;
    return data ? user?.[data] : user;
  },
);

// Convenience decorators
export const RequireUserRead = () => RequirePermissions(Permission.USER_READ);
export const RequireUserWrite = () => RequirePermissions(Permission.USER_WRITE);
export const RequireStoreRead = () => RequirePermissions(Permission.STORE_READ);
export const RequireStoreWrite = () => RequirePermissions(Permission.STORE_WRITE);
export const RequireProductRead = () => RequirePermissions(Permission.PRODUCT_READ);
export const RequireProductWrite = () => RequirePermissions(Permission.PRODUCT_WRITE);
```

#### **1.3.3 Authentication Module**

**File: `shared/auth/auth.module.ts`**
```typescript
import { Global, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { StandardizedJwtAuthGuard } from './guards/jwt-auth.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import { StandardizedConfigService } from '../config/services/config.service';

@Global()
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: StandardizedConfigService) => {
        const jwtConfig = configService.getJWTConfig();
        return {
          secret: jwtConfig.secret,
          signOptions: {
            expiresIn: jwtConfig.expiresIn,
          },
        };
      },
      inject: [StandardizedConfigService],
    }),
  ],
  providers: [
    StandardizedJwtAuthGuard,
    PermissionsGuard,
    {
      provide: APP_GUARD,
      useClass: StandardizedJwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: PermissionsGuard,
    },
  ],
  exports: [
    StandardizedJwtAuthGuard,
    PermissionsGuard,
    JwtModule,
  ],
})
export class StandardizedAuthModule {}
```

### **Step 1.4: Implement Logging Infrastructure (Day 4-5)**

#### **1.4.1 Logging Interfaces**

**File: `shared/logging/interfaces/logger.interface.ts`**
```typescript
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

export enum BusinessOutcome {
  SUCCESS = 'success',
  FAILURE = 'failure',
  PARTIAL = 'partial',
}

export interface LogContext {
  correlationId?: string;
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

export interface BusinessEvent {
  domain: string;
  action: string;
  outcome: BusinessOutcome;
  context: LogContext;
  timestamp: string;
  service: string;
}

export interface PerformanceMetric {
  operation: string;
  duration: number;
  success: boolean;
  context: LogContext;
  timestamp: string;
  service: string;
}
```

#### **1.4.2 Service Logger**

**File: `shared/logging/services/service-logger.service.ts`**
```typescript
import { Injectable, Logger } from '@nestjs/common';
import { StandardizedConfigService } from '../../config/services/config.service';
import { LogLevel, BusinessOutcome, LogContext, BusinessEvent, PerformanceMetric } from '../interfaces/logger.interface';

@Injectable()
export class ServiceLoggerService extends Logger {
  private readonly serviceName: string;

  constructor(private readonly configService: StandardizedConfigService) {
    super();
    this.serviceName = this.configService.getServiceConfig().serviceName;
  }

  logBusinessEvent(
    domain: string,
    action: string,
    outcome: BusinessOutcome,
    context: LogContext = {}
  ): void {
    const event: BusinessEvent = {
      domain,
      action,
      outcome,
      context,
      timestamp: new Date().toISOString(),
      service: this.serviceName,
    };

    this.log(JSON.stringify(event), `BusinessEvent:${domain}:${action}`);
  }

  logPerformanceMetric(
    operation: string,
    duration: number,
    success: boolean,
    context: LogContext = {}
  ): void {
    const metric: PerformanceMetric = {
      operation,
      duration,
      success,
      context,
      timestamp: new Date().toISOString(),
      service: this.serviceName,
    };

    this.log(JSON.stringify(metric), `Performance:${operation}`);
  }

  logOperationStart(operation: string, context: LogContext = {}): void {
    this.log(
      JSON.stringify({
        operation,
        phase: 'start',
        context,
        timestamp: new Date().toISOString(),
        service: this.serviceName,
      }),
      `Operation:${operation}:Start`
    );
  }

  logOperationEnd(operation: string, success: boolean, context: LogContext = {}): void {
    this.log(
      JSON.stringify({
        operation,
        phase: 'end',
        success,
        context,
        timestamp: new Date().toISOString(),
        service: this.serviceName,
      }),
      `Operation:${operation}:End`
    );
  }
}
```

### **Step 1.5: Implement Data Layer Infrastructure (Day 5-6)**

#### **1.5.1 Base Repository**

**File: `shared/data/repositories/base.repository.ts`**
```typescript
import { Injectable } from '@nestjs/common';
import { StandardizedPrismaService } from '../services/prisma.service';
import { ServiceLoggerService } from '../../logging/services/service-logger.service';
import { BusinessOutcome } from '../../logging/interfaces/logger.interface';
import { PaginationMeta } from '../../responses/interfaces/base-response.interface';

export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface FilterOptions {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'contains';
  value: any;
}

export interface SortingOptions {
  field: string;
  direction: 'asc' | 'desc';
}

@Injectable()
export abstract class BaseRepository<TEntity, TCreateDto, TUpdateDto> {
  constructor(
    protected readonly prisma: StandardizedPrismaService,
    protected readonly logger: ServiceLoggerService,
    protected readonly entityName: string,
  ) {}

  protected abstract getModel(): any;
  protected abstract toDto(entity: any): TEntity;
  protected abstract toCreateData(dto: TCreateDto): any;
  protected abstract toUpdateData(dto: TUpdateDto): any;

  async create(createDto: TCreateDto): Promise<TEntity> {
    const startTime = Date.now();
    try {
      this.logger.logOperationStart(`${this.entityName.toLowerCase()}_creation`);

      const data = this.toCreateData(createDto);
      const entity = await this.getModel().create({ data });
      const result = this.toDto(entity);

      this.logger.logBusinessEvent(
        this.entityName.toLowerCase(),
        'creation',
        BusinessOutcome.SUCCESS,
        { [`${this.entityName.toLowerCase()}Id`]: entity.id }
      );

      this.logger.logPerformanceMetric(
        `${this.entityName.toLowerCase()}_creation`,
        Date.now() - startTime,
        true
      );

      return result;
    } catch (error) {
      this.logger.logBusinessEvent(
        this.entityName.toLowerCase(),
        'creation',
        BusinessOutcome.FAILURE,
        { error: error.message }
      );

      this.logger.logPerformanceMetric(
        `${this.entityName.toLowerCase()}_creation`,
        Date.now() - startTime,
        false
      );

      throw error;
    }
  }

  async findById(id: string): Promise<TEntity | null> {
    const startTime = Date.now();
    try {
      const entity = await this.getModel().findUnique({ where: { id } });

      this.logger.logPerformanceMetric(
        `${this.entityName.toLowerCase()}_find_by_id`,
        Date.now() - startTime,
        true
      );

      return entity ? this.toDto(entity) : null;
    } catch (error) {
      this.logger.logPerformanceMetric(
        `${this.entityName.toLowerCase()}_find_by_id`,
        Date.now() - startTime,
        false
      );
      throw error;
    }
  }
}
```

---

## 📋 **PHASE 2: API GATEWAY REDESIGN (Week 2)**

### **Step 2.1: Create New API Gateway Structure (Day 6-7)**

#### **2.1.1 API Gateway Architecture**

**File: `services/api-gateway-v2/src/main.ts`**
```typescript
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { StandardizedConfigService } from '../../../shared/config/services/config.service';
import { ResponseTransformInterceptor } from '../../../shared/responses/interceptors/response-transform.interceptor';
import { ServiceLoggerService } from '../../../shared/logging/services/service-logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Get configuration
  const configService = app.get(StandardizedConfigService);
  const serviceConfig = configService.getServiceConfig();
  const logger = app.get(ServiceLoggerService);

  // Global pipes
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Global interceptors
  app.useGlobalInterceptors(new ResponseTransformInterceptor(app.get('ResponseService')));

  // CORS configuration
  app.enableCors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3003'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID'],
    credentials: true,
  });

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Social Commerce API Gateway')
    .setDescription('Centralized API Gateway for Social Commerce Platform')
    .setVersion(serviceConfig.version)
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  await app.listen(serviceConfig.port);

  logger.logBusinessEvent(
    'api-gateway',
    'startup',
    'success',
    { port: serviceConfig.port, version: serviceConfig.version }
  );
}

bootstrap();
```

#### **2.1.2 Service Configuration**

**File: `services/api-gateway-v2/src/config/services.config.ts`**
```typescript
export interface ServiceEndpoint {
  name: string;
  url: string;
  prefix: string;
  timeout: number;
  retries: number;
  healthCheck: string;
  circuitBreaker: {
    failureThreshold: number;
    resetTimeout: number;
  };
  rateLimit: {
    requests: number;
    window: string;
  };
}

export const SERVICES_CONFIG: Record<string, ServiceEndpoint> = {
  'user-service': {
    name: 'user-service',
    url: 'http://user-service:3001',
    prefix: '/api/users',
    timeout: 5000,
    retries: 3,
    healthCheck: '/health',
    circuitBreaker: {
      failureThreshold: 5,
      resetTimeout: 60000,
    },
    rateLimit: {
      requests: 100,
      window: '1m',
    },
  },
  'store-service': {
    name: 'store-service',
    url: 'http://store-service:3002',
    prefix: '/api/stores',
    timeout: 5000,
    retries: 3,
    healthCheck: '/health',
    circuitBreaker: {
      failureThreshold: 5,
      resetTimeout: 60000,
    },
    rateLimit: {
      requests: 50,
      window: '1m',
    },
  },
  'product-service': {
    name: 'product-service',
    url: 'http://product-service:3004',
    prefix: '/api/products',
    timeout: 5000,
    retries: 3,
    healthCheck: '/health',
    circuitBreaker: {
      failureThreshold: 5,
      resetTimeout: 60000,
    },
    rateLimit: {
      requests: 200,
      window: '1m',
    },
  },
  'cart-service': {
    name: 'cart-service',
    url: 'http://cart-service:3005',
    prefix: '/api/cart',
    timeout: 3000,
    retries: 2,
    healthCheck: '/health',
    circuitBreaker: {
      failureThreshold: 3,
      resetTimeout: 30000,
    },
    rateLimit: {
      requests: 150,
      window: '1m',
    },
  },
  'order-service': {
    name: 'order-service',
    url: 'http://order-service:3006',
    prefix: '/api/orders',
    timeout: 10000,
    retries: 3,
    healthCheck: '/health',
    circuitBreaker: {
      failureThreshold: 5,
      resetTimeout: 60000,
    },
    rateLimit: {
      requests: 50,
      window: '1m',
    },
  },
  'payment-service': {
    name: 'payment-service',
    url: 'http://payment-service:3008',
    prefix: '/api/payments',
    timeout: 15000,
    retries: 3,
    healthCheck: '/health',
    circuitBreaker: {
      failureThreshold: 3,
      resetTimeout: 120000,
    },
    rateLimit: {
      requests: 30,
      window: '1m',
    },
  },
  'media-service': {
    name: 'media-service',
    url: 'http://media-service:3011',
    prefix: '/api/media',
    timeout: 30000,
    retries: 2,
    healthCheck: '/health',
    circuitBreaker: {
      failureThreshold: 5,
      resetTimeout: 60000,
    },
    rateLimit: {
      requests: 100,
      window: '1m',
    },
  },
};
```

#### **2.1.3 Proxy Service Implementation**

**File: `services/api-gateway-v2/src/proxy/proxy.service.ts`**
```typescript
import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom, timeout, retry, catchError } from 'rxjs';
import { ServiceLoggerService } from '../../../../shared/logging/services/service-logger.service';
import { BusinessOutcome } from '../../../../shared/logging/interfaces/logger.interface';
import { SERVICES_CONFIG, ServiceEndpoint } from '../config/services.config';

@Injectable()
export class ProxyService {
  constructor(
    private readonly httpService: HttpService,
    private readonly logger: ServiceLoggerService,
  ) {}

  async forwardRequest(
    serviceName: string,
    path: string,
    method: string,
    body?: any,
    headers?: Record<string, string>,
    query?: Record<string, any>
  ): Promise<any> {
    const serviceConfig = SERVICES_CONFIG[serviceName];
    if (!serviceConfig) {
      throw new HttpException(`Service ${serviceName} not found`, HttpStatus.NOT_FOUND);
    }

    const startTime = Date.now();
    const correlationId = headers?.['x-correlation-id'] || `gw_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      this.logger.logOperationStart(`proxy_${serviceName}`, {
        correlationId,
        method,
        path,
        service: serviceName,
      });

      // Build target URL
      const targetUrl = `${serviceConfig.url}${path}`;

      // Prepare headers
      const forwardHeaders = {
        ...headers,
        'x-correlation-id': correlationId,
        'x-forwarded-by': 'api-gateway',
        'x-service-name': serviceName,
      };

      // Make request with circuit breaker pattern
      const response = await firstValueFrom(
        this.httpService.request({
          method: method.toLowerCase() as any,
          url: targetUrl,
          data: body,
          headers: forwardHeaders,
          params: query,
          timeout: serviceConfig.timeout,
        }).pipe(
          timeout(serviceConfig.timeout),
          retry(serviceConfig.retries),
          catchError((error) => {
            this.logger.logBusinessEvent(
              'api-gateway',
              'proxy_request',
              BusinessOutcome.FAILURE,
              {
                correlationId,
                service: serviceName,
                error: error.message,
                statusCode: error.response?.status,
              }
            );
            throw error;
          })
        )
      );

      const duration = Date.now() - startTime;

      this.logger.logBusinessEvent(
        'api-gateway',
        'proxy_request',
        BusinessOutcome.SUCCESS,
        {
          correlationId,
          service: serviceName,
          statusCode: response.status,
          duration,
        }
      );

      this.logger.logPerformanceMetric(
        `proxy_${serviceName}`,
        duration,
        true,
        { correlationId, method, path }
      );

      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.logPerformanceMetric(
        `proxy_${serviceName}`,
        duration,
        false,
        { correlationId, method, path, error: error.message }
      );

      // Transform error to standardized format
      if (error.response) {
        throw new HttpException(
          error.response.data || 'Service error',
          error.response.status || HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      throw new HttpException(
        'Service unavailable',
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }

  async checkServiceHealth(serviceName: string): Promise<boolean> {
    const serviceConfig = SERVICES_CONFIG[serviceName];
    if (!serviceConfig) {
      return false;
    }

    try {
      const response = await firstValueFrom(
        this.httpService.get(`${serviceConfig.url}${serviceConfig.healthCheck}`, {
          timeout: 3000,
        }).pipe(timeout(3000))
      );

      return response.status === 200;
    } catch (error) {
      this.logger.logBusinessEvent(
        'api-gateway',
        'health_check',
        BusinessOutcome.FAILURE,
        { service: serviceName, error: error.message }
      );
      return false;
    }
  }
}
```

#### **2.1.4 Gateway Controller**

**File: `services/api-gateway-v2/src/gateway/gateway.controller.ts`**
```typescript
import { Controller, All, Req, Res, Next, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ProxyService } from '../proxy/proxy.service';
import { ServiceLoggerService } from '../../../../shared/logging/services/service-logger.service';
import { Public } from '../../../../shared/auth/decorators/auth.decorator';
import { SERVICES_CONFIG } from '../config/services.config';

@Controller()
@ApiTags('Gateway')
export class GatewayController {
  constructor(
    private readonly proxyService: ProxyService,
    private readonly logger: ServiceLoggerService,
  ) {}

  @All('api/*')
  @Public()
  @ApiOperation({ summary: 'Proxy requests to microservices' })
  async proxyRequest(
    @Req() req: Request,
    @Res() res: Response,
    @Next() next: NextFunction,
  ) {
    try {
      // Extract service name from path
      const pathSegments = req.path.split('/');
      const serviceName = this.extractServiceName(req.path);

      if (!serviceName) {
        throw new HttpException('Invalid API path', HttpStatus.BAD_REQUEST);
      }

      // Remove gateway prefix from path
      const servicePath = this.buildServicePath(req.path, serviceName);

      // Forward request
      const result = await this.proxyService.forwardRequest(
        serviceName,
        servicePath,
        req.method,
        req.body,
        req.headers as Record<string, string>,
        req.query as Record<string, any>
      );

      // Return response
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  private extractServiceName(path: string): string | null {
    // Map paths to services
    const pathMappings = {
      '/api/users': 'user-service',
      '/api/auth': 'user-service',
      '/api/stores': 'store-service',
      '/api/products': 'product-service',
      '/api/cart': 'cart-service',
      '/api/orders': 'order-service',
      '/api/payments': 'payment-service',
      '/api/media': 'media-service',
      '/api/notifications': 'notification-service',
      '/api/search': 'search-service',
      '/api/analytics': 'analytics-service',
      '/api/recommendations': 'recommendation-service',
      '/api/social': 'social-service',
    };

    for (const [prefix, service] of Object.entries(pathMappings)) {
      if (path.startsWith(prefix)) {
        return service;
      }
    }

    return null;
  }

  private buildServicePath(originalPath: string, serviceName: string): string {
    const serviceConfig = SERVICES_CONFIG[serviceName];
    if (!serviceConfig) {
      return originalPath;
    }

    // Remove the gateway prefix and add service-specific path
    return originalPath.replace('/api', '');
  }
}
```

### **Step 2.2: Health Check Aggregation (Day 7-8)**

**File: `services/api-gateway-v2/src/health/health.controller.ts`**
```typescript
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ProxyService } from '../proxy/proxy.service';
import { ResponseService } from '../../../../shared/responses/services/response.service';
import { Public } from '../../../../shared/auth/decorators/auth.decorator';
import { SERVICES_CONFIG } from '../config/services.config';

@Controller('health')
@ApiTags('Health')
export class HealthController {
  constructor(
    private readonly proxyService: ProxyService,
    private readonly responseService: ResponseService,
  ) {}

  @Get()
  @Public()
  @ApiOperation({ summary: 'Check API Gateway health' })
  async checkHealth() {
    return this.responseService.success({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'api-gateway',
    });
  }

  @Get('services')
  @Public()
  @ApiOperation({ summary: 'Check all services health' })
  async checkServicesHealth() {
    const healthChecks = await Promise.allSettled(
      Object.keys(SERVICES_CONFIG).map(async (serviceName) => {
        const isHealthy = await this.proxyService.checkServiceHealth(serviceName);
        return {
          service: serviceName,
          status: isHealthy ? 'healthy' : 'unhealthy',
          url: SERVICES_CONFIG[serviceName].url,
        };
      })
    );

    const results = healthChecks.map((result, index) => {
      const serviceName = Object.keys(SERVICES_CONFIG)[index];
      if (result.status === 'fulfilled') {
        return result.value;
      }
      return {
        service: serviceName,
        status: 'error',
        error: result.reason?.message || 'Unknown error',
      };
    });

    const overallStatus = results.every(r => r.status === 'healthy') ? 'healthy' : 'degraded';

    return this.responseService.success({
      overall: overallStatus,
      services: results,
      timestamp: new Date().toISOString(),
    });
  }
}
```

---

## 📋 **PHASE 3: SERVICE MIGRATION & STANDARDIZATION (Week 3)**

### **Step 3.1: Create Service Templates (Day 8-9)**

#### **3.1.1 Service Template Generator**

**File: `scripts/generate-service.js`**
```javascript
const fs = require('fs');
const path = require('path');

function generateService(serviceName, entityName) {
  const serviceDir = `services/${serviceName}`;
  const srcDir = `${serviceDir}/src`;

  // Create directory structure
  const dirs = [
    `${srcDir}/features/${entityName.toLowerCase()}`,
    `${srcDir}/features/${entityName.toLowerCase()}/controllers`,
    `${srcDir}/features/${entityName.toLowerCase()}/services`,
    `${srcDir}/features/${entityName.toLowerCase()}/repositories`,
    `${srcDir}/features/${entityName.toLowerCase()}/dto`,
    `${srcDir}/features/${entityName.toLowerCase()}/entities`,
    `${srcDir}/health`,
    `${srcDir}/config`,
  ];

  dirs.forEach(dir => {
    fs.mkdirSync(dir, { recursive: true });
  });

  // Generate main.ts
  const mainTs = `
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { StandardizedConfigService } from '../../../shared/config/services/config.service';
import { ResponseTransformInterceptor } from '../../../shared/responses/interceptors/response-transform.interceptor';
import { ServiceLoggerService } from '../../../shared/logging/services/service-logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const configService = app.get(StandardizedConfigService);
  const serviceConfig = configService.getServiceConfig();
  const logger = app.get(ServiceLoggerService);

  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  app.useGlobalInterceptors(new ResponseTransformInterceptor(app.get('ResponseService')));

  const config = new DocumentBuilder()
    .setTitle('${serviceName.charAt(0).toUpperCase() + serviceName.slice(1)} Service')
    .setDescription('${entityName} management service')
    .setVersion(serviceConfig.version)
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  await app.listen(serviceConfig.port);

  logger.logBusinessEvent(
    '${serviceName}',
    'startup',
    'success',
    { port: serviceConfig.port }
  );
}

bootstrap();
`;

  fs.writeFileSync(`${srcDir}/main.ts`, mainTs);

  // Generate controller template
  const controllerTemplate = `
import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { StandardizedJwtAuthGuard } from '../../../../shared/auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '../../../../shared/auth/guards/permissions.guard';
import { RequirePermissions, CurrentUser } from '../../../../shared/auth/decorators/auth.decorator';
import { Permission, AuthenticatedUser } from '../../../../shared/auth/interfaces/auth.interface';
import { ResponseService } from '../../../../shared/responses/services/response.service';
import { ${entityName}Service } from '../services/${entityName.toLowerCase()}.service';
import { Create${entityName}Dto } from '../dto/create-${entityName.toLowerCase()}.dto';
import { Update${entityName}Dto } from '../dto/update-${entityName.toLowerCase()}.dto';

@Controller('${entityName.toLowerCase()}s')
@UseGuards(StandardizedJwtAuthGuard, PermissionsGuard)
@ApiTags('${entityName}s')
@ApiBearerAuth()
export class ${entityName}Controller {
  constructor(
    private readonly ${entityName.toLowerCase()}Service: ${entityName}Service,
    private readonly responseService: ResponseService,
  ) {}

  @Get()
  @RequirePermissions(Permission.${entityName.toUpperCase()}_READ)
  @ApiOperation({ summary: 'Get ${entityName.toLowerCase()}s list' })
  async findAll(@Query() query: any) {
    const result = await this.${entityName.toLowerCase()}Service.findAll(query);
    return this.responseService.paginated(
      result.data,
      result.pagination,
      { message: '${entityName}s retrieved successfully' }
    );
  }

  @Get(':id')
  @RequirePermissions(Permission.${entityName.toUpperCase()}_READ)
  @ApiOperation({ summary: 'Get ${entityName.toLowerCase()} by ID' })
  async findOne(@Param('id') id: string) {
    const result = await this.${entityName.toLowerCase()}Service.findById(id);
    return this.responseService.success(result, '${entityName} retrieved successfully');
  }

  @Post()
  @RequirePermissions(Permission.${entityName.toUpperCase()}_WRITE)
  @ApiOperation({ summary: 'Create ${entityName.toLowerCase()}' })
  async create(
    @Body() createDto: Create${entityName}Dto,
    @CurrentUser() user: AuthenticatedUser
  ) {
    const result = await this.${entityName.toLowerCase()}Service.create(createDto, user);
    return this.responseService.created(result, '${entityName} created successfully');
  }

  @Put(':id')
  @RequirePermissions(Permission.${entityName.toUpperCase()}_WRITE)
  @ApiOperation({ summary: 'Update ${entityName.toLowerCase()}' })
  async update(
    @Param('id') id: string,
    @Body() updateDto: Update${entityName}Dto,
    @CurrentUser() user: AuthenticatedUser
  ) {
    const result = await this.${entityName.toLowerCase()}Service.update(id, updateDto, user);
    return this.responseService.success(result, '${entityName} updated successfully');
  }

  @Delete(':id')
  @RequirePermissions(Permission.${entityName.toUpperCase()}_DELETE)
  @ApiOperation({ summary: 'Delete ${entityName.toLowerCase()}' })
  async remove(@Param('id') id: string, @CurrentUser() user: AuthenticatedUser) {
    await this.${entityName.toLowerCase()}Service.remove(id, user);
    return this.responseService.success(null, '${entityName} deleted successfully');
  }
}
`;

  fs.writeFileSync(`${srcDir}/features/${entityName.toLowerCase()}/controllers/${entityName.toLowerCase()}.controller.ts`, controllerTemplate);

  console.log(`✅ Service ${serviceName} generated successfully!`);
  console.log(`📁 Location: ${serviceDir}`);
  console.log(`🔧 Next steps:`);
  console.log(`   1. Implement business logic in services`);
  console.log(`   2. Create DTOs and entities`);
  console.log(`   3. Implement repository`);
  console.log(`   4. Add to docker-compose.yml`);
}

// Usage: node scripts/generate-service.js user-service-v2 User
const [,, serviceName, entityName] = process.argv;
if (!serviceName || !entityName) {
  console.error('Usage: node generate-service.js <service-name> <EntityName>');
  process.exit(1);
}

generateService(serviceName, entityName);
```

### **Step 3.2: Migrate Existing Services (Day 9-12)**

#### **3.2.1 Migration Strategy**

**Migration Order (Preserve Business Logic):**
1. **User Service** → Extract business logic → Apply new structure
2. **Store Service** → Extract business logic → Apply new structure
3. **Product Service** → Extract business logic → Apply new structure
4. **Cart Service** → Extract business logic → Apply new structure
5. **Order Service** → Extract business logic → Apply new structure
6. **Payment Service** → Extract business logic → Apply new structure
7. **Media Service** → Extract business logic → Apply new structure

#### **3.2.2 Migration Script**

**File: `scripts/migrate-service.js`**
```javascript
const fs = require('fs');
const path = require('path');

async function migrateService(oldServiceName, newServiceName) {
  console.log(`🔄 Migrating ${oldServiceName} to ${newServiceName}...`);

  const oldDir = `services/${oldServiceName}`;
  const newDir = `services/${newServiceName}`;

  // 1. Generate new service structure
  console.log('📁 Creating new service structure...');
  // Use the generate-service script

  // 2. Extract business logic from old service
  console.log('🔍 Extracting business logic...');
  const businessLogic = extractBusinessLogic(oldDir);

  // 3. Apply business logic to new structure
  console.log('🔧 Applying business logic to new structure...');
  applyBusinessLogic(newDir, businessLogic);

  // 4. Update environment variables
  console.log('⚙️ Updating environment configuration...');
  updateEnvironmentConfig(newServiceName);

  // 5. Update docker-compose
  console.log('🐳 Updating Docker configuration...');
  updateDockerCompose(oldServiceName, newServiceName);

  console.log(`✅ Migration completed: ${oldServiceName} → ${newServiceName}`);
}

function extractBusinessLogic(serviceDir) {
  // Extract controllers, services, entities, DTOs
  const businessLogic = {
    controllers: [],
    services: [],
    entities: [],
    dtos: [],
    repositories: [],
  };

  // Read existing files and extract business logic
  // This is a simplified version - actual implementation would be more complex

  return businessLogic;
}

function applyBusinessLogic(newServiceDir, businessLogic) {
  // Apply extracted business logic to new standardized structure
  // Wrap with new infrastructure components
}

function updateEnvironmentConfig(serviceName) {
  const envTemplate = `
# Service Configuration
SERVICE_NAME=${serviceName}
SERVICE_PORT=3001
SERVICE_VERSION=2.0.0
NODE_ENV=development

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=${serviceName.replace('-', '_')}
DB_USERNAME=postgres
DB_PASSWORD=1111
DATABASE_URL=****************************************/${serviceName.replace('-', '_')}

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# RabbitMQ Configuration
RABBITMQ_URL=amqp://admin:admin@rabbitmq:5672
RABBITMQ_QUEUE=${serviceName.replace('-', '_')}_queue
RABBITMQ_EXCHANGE=${serviceName.replace('-', '_')}_exchange

# API Gateway
API_GATEWAY_URL=http://api-gateway-v2:3000

# Service URLs (for inter-service communication)
USER_SERVICE_URL=http://user-service-v2:3001
STORE_SERVICE_URL=http://store-service-v2:3002
PRODUCT_SERVICE_URL=http://product-service-v2:3004
CART_SERVICE_URL=http://cart-service-v2:3005
ORDER_SERVICE_URL=http://order-service-v2:3006
PAYMENT_SERVICE_URL=http://payment-service-v2:3008
MEDIA_SERVICE_URL=http://media-service-v2:3011
`;

  fs.writeFileSync(`services/${serviceName}/.env.example`, envTemplate);
}

// Usage: node scripts/migrate-service.js user-service user-service-v2
const [,, oldService, newService] = process.argv;
if (!oldService || !newService) {
  console.error('Usage: node migrate-service.js <old-service> <new-service>');
  process.exit(1);
}

migrateService(oldService, newService);
```

---

## 📋 **PHASE 4: INTEGRATION & TESTING (Week 4)**

### **Step 4.1: Integration Testing (Day 13-14)**

#### **4.1.1 End-to-End Test Suite**

**File: `tests/e2e/platform-integration.test.ts`**
```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../services/api-gateway-v2/src/app.module';

describe('Platform Integration Tests', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get auth token
    const loginResponse = await request(app.getHttpServer())
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      });

    authToken = loginResponse.body.data.accessToken;
  });

  describe('API Gateway Integration', () => {
    it('should route user requests correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('correlationId');
      expect(response.body).toHaveProperty('service');
    });

    it('should route store requests correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/stores')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('pagination');
    });

    it('should handle service errors gracefully', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/nonexistent/endpoint')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Cross-Service Communication', () => {
    it('should create order with product validation', async () => {
      // Create product first
      const productResponse = await request(app.getHttpServer())
        .post('/api/products')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Test Product',
          price: 99.99,
          storeId: 'test-store-id',
        });

      // Add to cart
      const cartResponse = await request(app.getHttpServer())
        .post('/api/cart/items')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: productResponse.body.data.id,
          quantity: 2,
        });

      // Create order
      const orderResponse = await request(app.getHttpServer())
        .post('/api/orders')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          cartId: cartResponse.body.data.id,
        });

      expect(orderResponse.status).toBe(201);
      expect(orderResponse.body.data).toHaveProperty('id');
    });
  });

  afterAll(async () => {
    await app.close();
  });
});
```

### **Step 4.2: Performance Testing (Day 14-15)**

#### **4.2.1 Load Testing Script**

**File: `tests/performance/load-test.js`**
```javascript
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

export let errorRate = new Rate('errors');

export let options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up
    { duration: '5m', target: 50 }, // Stay at 50 users
    { duration: '2m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 0 }, // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    errors: ['rate<0.1'], // Error rate under 10%
  },
};

const BASE_URL = 'http://localhost:3000';
let authToken;

export function setup() {
  // Login to get auth token
  const loginResponse = http.post(`${BASE_URL}/api/auth/login`, {
    email: '<EMAIL>',
    password: 'password123',
  });

  return { authToken: loginResponse.json().data.accessToken };
}

export default function(data) {
  const headers = {
    'Authorization': `Bearer ${data.authToken}`,
    'Content-Type': 'application/json',
  };

  // Test different endpoints
  const endpoints = [
    '/api/users/profile',
    '/api/stores',
    '/api/products',
    '/api/cart',
  ];

  const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
  const response = http.get(`${BASE_URL}${endpoint}`, { headers });

  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
    'has correlation ID': (r) => r.json().correlationId !== undefined,
  }) || errorRate.add(1);

  sleep(1);
}
```

### **Step 4.3: Deployment & Monitoring (Day 15-16)**

#### **4.3.1 Updated Docker Compose**

**File: `docker-compose.v2.yml`**
```yaml
version: '3.8'

services:
  # API Gateway V2
  api-gateway-v2:
    build: ./services/api-gateway-v2
    ports:
      - "3000:3000"
    environment:
      - SERVICE_NAME=api-gateway-v2
      - SERVICE_PORT=3000
      - NODE_ENV=development
      - JWT_SECRET=your_jwt_secret_key_here
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - social-commerce-network

  # User Service V2
  user-service-v2:
    build: ./services/user-service-v2
    ports:
      - "3001:3001"
    environment:
      - SERVICE_NAME=user-service-v2
      - SERVICE_PORT=3001
      - DATABASE_URL=****************************************/user_service_v2
      - JWT_SECRET=your_jwt_secret_key_here
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://admin:admin@rabbitmq:5672
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - social-commerce-network

  # Store Service V2
  store-service-v2:
    build: ./services/store-service-v2
    ports:
      - "3002:3002"
    environment:
      - SERVICE_NAME=store-service-v2
      - SERVICE_PORT=3002
      - DATABASE_URL=****************************************/store_service_v2
      - JWT_SECRET=your_jwt_secret_key_here
      - USER_SERVICE_URL=http://user-service-v2:3001
    depends_on:
      - postgres
      - user-service-v2
    networks:
      - social-commerce-network

  # Infrastructure Services
  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1111
      POSTGRES_DB: social_commerce
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    networks:
      - social-commerce-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - social-commerce-network

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - social-commerce-network

volumes:
  postgres_data:

networks:
  social-commerce-network:
    driver: bridge
```

## 🚀 **IMPLEMENTATION EXECUTION PLAN**

### **Week 1: Foundation**
- **Day 1-2**: Create shared infrastructure
- **Day 3-4**: Implement authentication & response standardization
- **Day 5-6**: Implement logging & data layer

### **Week 2: API Gateway**
- **Day 6-7**: Build new API Gateway with proxy service
- **Day 7-8**: Implement health checks & monitoring

### **Week 3: Service Migration**
- **Day 8-9**: Create service templates & migration scripts
- **Day 9-12**: Migrate all 13 services one by one

### **Week 4: Integration**
- **Day 13-14**: Integration testing & bug fixes
- **Day 14-15**: Performance testing & optimization
- **Day 15-16**: Deployment & monitoring setup

## 🎯 **SUCCESS METRICS**

### **Technical Metrics**
- ✅ All 13 services use standardized patterns
- ✅ API Gateway handles 100% of external requests
- ✅ Response time < 500ms for 95% of requests
- ✅ Error rate < 1%
- ✅ 100% test coverage for critical paths

### **Business Metrics**
- ✅ Zero downtime migration
- ✅ Preserved all existing business logic
- ✅ Frontend integration ready
- ✅ Scalable architecture for future growth

## 🔧 **NEXT STEPS AFTER IMPLEMENTATION**

1. **Frontend Integration** - Connect Next.js app to new API Gateway
2. **Performance Optimization** - Fine-tune based on load testing results
3. **Security Hardening** - Implement additional security measures
4. **Monitoring & Alerting** - Set up comprehensive monitoring
5. **Documentation** - Update all API documentation

**This plan provides a complete roadmap to transform your platform into an enterprise-grade, standardized microservices architecture while preserving all your existing business logic!**
