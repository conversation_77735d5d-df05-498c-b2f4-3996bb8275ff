// Enterprise Analytics Command Model (Write Side) - Template
import { IsString, IsOptional, IsNumber, IsEnum, IsJSON } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum EventType {
  USER_ACTION = 'user_action',
  NFT_INTERACTION = 'nft_interaction',
  MARKETPLACE_ACTIVITY = 'marketplace_activity'
}

export class CreateAnalyticsEventCommandDto {
  @ApiProperty({ description: 'Event type', enum: EventType })
  @IsEnum(EventType)
  eventType: EventType;

  @ApiProperty({ description: 'Event category' })
  @IsString()
  eventCategory: string;

  @ApiProperty({ description: 'Event name' })
  @IsString()
  eventName: string;
}
