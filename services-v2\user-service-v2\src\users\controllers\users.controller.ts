/**
 * Users Controller - User Service V2
 * 
 * Clean implementation using shared infrastructure for:
 * - Standardized responses
 * - Authentication & authorization
 * - Business event logging
 * - Error handling
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import {
  ResponseService,
  RequirePermissions,
  Permission,
  CurrentUser,
  AuthenticatedUser,
  Public,
  StandardizedJwtAuthGuard,
} from '../../../../../shared';
import { UsersService } from '../services/users.service';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { QueryUsersDto } from '../dto/query-users.dto';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly responseService: ResponseService,
  ) {}

  @Post()
  @Public() // Registration is public
  @ApiOperation({ summary: 'Create a new user (Registration)' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 409, description: 'User already exists' })
  async create(@Body() createUserDto: CreateUserDto) {
    const user = await this.usersService.create(createUserDto);
    return this.responseService.created(user, 'User created successfully');
  }

  @Get()
  @UseGuards(StandardizedJwtAuthGuard)
  @RequirePermissions(Permission.USER_READ)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all users with pagination and filtering' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  async findAll(
    @Query() queryDto: QueryUsersDto,
    @CurrentUser() user: AuthenticatedUser,
  ) {
    const result = await this.usersService.findAll(queryDto);
    
    if (queryDto.page && queryDto.limit) {
      return this.responseService.paginated(
        result.users,
        result.pagination,
        {
          message: 'Users retrieved successfully',
          filters: result.filters,
          sorting: result.sorting,
        },
      );
    }
    
    return this.responseService.success(result.users, 'Users retrieved successfully');
  }

  @Get('me')
  @UseGuards(StandardizedJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Current user profile' })
  async getCurrentUser(@CurrentUser() user: AuthenticatedUser) {
    const userProfile = await this.usersService.findOne(user.id);
    return this.responseService.success(userProfile, 'User profile retrieved');
  }

  @Get(':id')
  @UseGuards(StandardizedJwtAuthGuard)
  @RequirePermissions(Permission.USER_READ)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User found' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id') id: string) {
    const user = await this.usersService.findOne(id);
    
    if (!user) {
      return this.responseService.notFound('User', id);
    }
    
    return this.responseService.success(user, 'User retrieved successfully');
  }

  @Put(':id')
  @UseGuards(StandardizedJwtAuthGuard)
  @RequirePermissions(Permission.USER_UPDATE)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user by ID' })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() user: AuthenticatedUser,
  ) {
    // Users can only update their own profile unless they have admin permissions
    if (id !== user.id && !user.permissions?.includes(Permission.ADMIN)) {
      return this.responseService.forbidden('You can only update your own profile');
    }

    const updatedUser = await this.usersService.update(id, updateUserDto);

    if (!updatedUser) {
      return this.responseService.notFound('User', id);
    }

    return this.responseService.updated(updatedUser, 'User updated successfully');
  }

  @Put('me')
  @UseGuards(StandardizedJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update current user profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  async updateProfile(
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() user: AuthenticatedUser,
  ) {
    const updatedUser = await this.usersService.update(user.id, updateUserDto);
    return this.responseService.updated(updatedUser, 'Profile updated successfully');
  }

  @Delete(':id')
  @UseGuards(StandardizedJwtAuthGuard)
  @RequirePermissions(Permission.USER_DELETE)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete user by ID' })
  @ApiResponse({ status: 200, description: 'User deleted successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async remove(@Param('id') id: string) {
    const deleted = await this.usersService.remove(id);
    
    if (!deleted) {
      return this.responseService.notFound('User', id);
    }
    
    return this.responseService.deleted('User deleted successfully');
  }

  @Post(':id/activate')
  @UseGuards(StandardizedJwtAuthGuard)
  @RequirePermissions(Permission.USER_UPDATE)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Activate user account' })
  @ApiResponse({ status: 200, description: 'User activated successfully' })
  async activate(@Param('id') id: string) {
    const user = await this.usersService.activate(id);
    
    if (!user) {
      return this.responseService.notFound('User', id);
    }
    
    return this.responseService.success(user, 'User activated successfully');
  }

  @Post(':id/deactivate')
  @UseGuards(StandardizedJwtAuthGuard)
  @RequirePermissions(Permission.USER_UPDATE)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Deactivate user account' })
  @ApiResponse({ status: 200, description: 'User deactivated successfully' })
  async deactivate(@Param('id') id: string) {
    const user = await this.usersService.deactivate(id);
    
    if (!user) {
      return this.responseService.notFound('User', id);
    }
    
    return this.responseService.success(user, 'User deactivated successfully');
  }
}
