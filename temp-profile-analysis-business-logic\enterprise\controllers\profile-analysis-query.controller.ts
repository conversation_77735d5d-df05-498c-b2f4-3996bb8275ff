import { <PERSON>, <PERSON>, <PERSON><PERSON>, Query, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus, Lo<PERSON> } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { Response } from 'express';
import { TwitterAnalysisService } from '../services/twitter-analysis.service';

@ApiTags('Profile Analysis Queries (Read Operations)')
@Controller('analysis')
export class ProfileAnalysisQueryController {
  private readonly logger = new Logger(ProfileAnalysisQueryController.name);

  constructor(private readonly twitterAnalysisService: TwitterAnalysisService) {}

  @Get('results/:analysisId')
  @ApiOperation({ summary: 'Get analysis results by ID' })
  @ApiParam({ name: 'analysisId', description: 'Analysis ID' })
  @ApiResponse({
    status: 200,
    description: 'Analysis results retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            twitterHandle: { type: 'string' },
            status: { type: 'string' },
            score: { type: 'number' },
            analysisData: { type: 'object' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async getAnalysisResults(
    @Param('analysisId') analysisId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting analysis results for ID: ${analysisId}`, {
        correlationId: headers['x-correlation-id']
      });

      const analysis = await this.twitterAnalysisService.getAnalysisById(analysisId);

      if (!analysis) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          error: {
            message: `Analysis with ID ${analysisId} not found`,
            timestamp: new Date().toISOString(),
          },
          correlationId: headers['x-correlation-id']
        });
      }

      this.logger.log(`Analysis results retrieved successfully for ID: ${analysisId}`, {
        score: analysis.score,
        status: analysis.status,
        correlationId: headers['x-correlation-id']
      });

      return res.status(HttpStatus.OK).json({
        success: true,
        data: analysis,
        correlationId: headers['x-correlation-id']
      });

    } catch (error) {
      this.logger.error(`Failed to get analysis results for ID: ${analysisId}`, {
        error: error.message,
        stack: error.stack,
        correlationId: headers['x-correlation-id']
      });

      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to retrieve analysis results',
          timestamp: new Date().toISOString(),
        },
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('history')
  @ApiOperation({ summary: 'Get user analysis history' })
  @ApiQuery({ name: 'userId', required: true, type: String, description: 'User ID' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of results (default: 10)' })
  @ApiQuery({ name: 'offset', required: false, type: Number, description: 'Offset for pagination (default: 0)' })
  @ApiResponse({
    status: 200,
    description: 'Analysis history retrieved successfully'
  })
  @ApiResponse({ status: 400, description: 'Missing or invalid userId' })
  async getAnalysisHistory(
    @Query('userId') userId: string,
    @Query('limit') limit: number = 10,
    @Query('offset') offset: number = 0,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      if (!userId) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            message: 'userId is required',
            timestamp: new Date().toISOString(),
          },
          correlationId: headers['x-correlation-id']
        });
      }

      this.logger.log(`Getting analysis history for user: ${userId}`, {
        limit,
        offset,
        correlationId: headers['x-correlation-id']
      });

      const history = await this.twitterAnalysisService.getUserAnalysisHistory(userId, limit, offset);

      this.logger.log(`Analysis history retrieved successfully for user: ${userId}`, {
        count: history.analyses.length,
        total: history.total,
        correlationId: headers['x-correlation-id']
      });

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          ...history,
          limit,
          offset
        },
        correlationId: headers['x-correlation-id']
      });

    } catch (error) {
      this.logger.error(`Failed to get analysis history for user: ${userId}`, {
        error: error.message,
        stack: error.stack,
        correlationId: headers['x-correlation-id']
      });

      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to retrieve analysis history',
          timestamp: new Date().toISOString(),
        },
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for analysis service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    return res.status(HttpStatus.OK).json({
      success: true,
      data: {
        status: 'healthy',
        service: 'profile-analysis-service',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        uptime: process.uptime(),
      },
      correlationId: headers['x-correlation-id']
    });
  }
}
