import { Injectable, OnModuleInit } from '@nestjs/common';
import { ServiceLoggerService, MetricsService } from '../../shared';

export interface ServiceInstance {
  id: string;
  name: string;
  url: string;
  health: 'healthy' | 'unhealthy' | 'unknown';
  lastHealthCheck: Date;
  metadata: {
    version: string;
    region: string;
    capabilities: string[];
    weight: number;
  };
}

export interface ServiceRegistration {
  name: string;
  url: string;
  metadata?: Partial<ServiceInstance['metadata']>;
}

/**
 * Service Discovery Service
 * 
 * Manages dynamic service registration and health-based routing.
 * Provides automatic service discovery with health monitoring.
 * 
 * @example
 * ```typescript
 * const healthyInstances = await serviceDiscovery.getHealthyInstances('user-service');
 * const instance = await serviceDiscovery.selectInstance('user-service', 'round-robin');
 * ```
 */
@Injectable()
export class ServiceDiscoveryService implements OnModuleInit {
  private services = new Map<string, ServiceInstance[]>();
  private healthCheckInterval = 30000; // 30 seconds
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(
    private readonly logger: ServiceLoggerService,
    private readonly metrics: MetricsService
  ) {}

  async onModuleInit(): Promise<void> {
    await this.initializeServiceRegistry();
    this.startHealthChecking();
  }

  /**
   * Register a service instance
   */
  async registerService(registration: ServiceRegistration): Promise<void> {
    const instance: ServiceInstance = {
      id: `${registration.name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: registration.name,
      url: registration.url,
      health: 'unknown',
      lastHealthCheck: new Date(),
      metadata: {
        version: registration.metadata?.version || '1.0.0',
        region: registration.metadata?.region || process.env.REGION || 'local',
        capabilities: registration.metadata?.capabilities || ['http', 'rest'],
        weight: registration.metadata?.weight || 1,
      },
    };

    const serviceName = instance.name;
    const instances = this.services.get(serviceName) || [];
    
    // Remove existing instance with same URL
    const filteredInstances = instances.filter(i => i.url !== instance.url);
    filteredInstances.push(instance);
    
    this.services.set(serviceName, filteredInstances);
    
    this.logger.logBusinessEvent('service-discovery', 'register', 'SUCCESS', {
      business: {
        domain: 'service-discovery',
        entity: 'service-instance',
        action: 'register',
        outcome: 'SUCCESS',
        attributes: {
          serviceName,
          instanceId: instance.id,
          url: instance.url,
          version: instance.metadata.version,
        },
      },
    });

    this.metrics.increment('service_registrations_total', {
      service: serviceName,
      status: 'success',
    });
  }

  /**
   * Get all healthy instances for a service
   */
  async getHealthyInstances(serviceName: string): Promise<ServiceInstance[]> {
    const instances = this.services.get(serviceName) || [];
    const healthyInstances = instances.filter(i => i.health === 'healthy');
    
    this.metrics.gauge('healthy_service_instances', healthyInstances.length, {
      service: serviceName,
    });
    
    return healthyInstances;
  }

  /**
   * Select an instance using load balancing strategy
   */
  async selectInstance(
    serviceName: string, 
    strategy: 'round-robin' | 'weighted' | 'least-connections' = 'round-robin'
  ): Promise<ServiceInstance | null> {
    const healthyInstances = await this.getHealthyInstances(serviceName);
    
    if (healthyInstances.length === 0) {
      this.logger.warn(`No healthy instances available for service: ${serviceName}`);
      return null;
    }

    let selectedInstance: ServiceInstance;

    switch (strategy) {
      case 'weighted':
        selectedInstance = this.selectWeightedInstance(healthyInstances);
        break;
      case 'least-connections':
        // For now, fallback to round-robin (connections tracking would need implementation)
        selectedInstance = this.selectRoundRobinInstance(serviceName, healthyInstances);
        break;
      case 'round-robin':
      default:
        selectedInstance = this.selectRoundRobinInstance(serviceName, healthyInstances);
        break;
    }

    this.metrics.increment('instance_selections_total', {
      service: serviceName,
      strategy,
      instance_id: selectedInstance.id,
    });

    return selectedInstance;
  }

  /**
   * Get all registered services
   */
  getAllServices(): Map<string, ServiceInstance[]> {
    return new Map(this.services);
  }

  /**
   * Unregister a service instance
   */
  async unregisterService(serviceName: string, instanceId: string): Promise<void> {
    const instances = this.services.get(serviceName) || [];
    const filteredInstances = instances.filter(i => i.id !== instanceId);
    
    if (filteredInstances.length !== instances.length) {
      this.services.set(serviceName, filteredInstances);
      
      this.logger.logBusinessEvent('service-discovery', 'unregister', 'SUCCESS', {
        business: {
          domain: 'service-discovery',
          entity: 'service-instance',
          action: 'unregister',
          outcome: 'SUCCESS',
          attributes: {
            serviceName,
            instanceId,
          },
        },
      });

      this.metrics.increment('service_unregistrations_total', {
        service: serviceName,
        status: 'success',
      });
    }
  }

  private async initializeServiceRegistry(): Promise<void> {
    // Register known V2 services from environment
    const serviceConfigs: ServiceRegistration[] = [
      {
        name: 'user-service',
        url: process.env.USER_SERVICE_V2_URL || 'http://localhost:3001',
        metadata: { version: '2.0.0', capabilities: ['auth', 'users', 'shared-infrastructure'] }
      },
      {
        name: 'profile-analysis-service',
        url: process.env.PROFILE_SERVICE_V2_URL || 'http://localhost:3002',
        metadata: { version: '2.0.0', capabilities: ['ai', 'analysis', 'shared-infrastructure'] }
      },
      {
        name: 'nft-generation-service',
        url: process.env.NFT_SERVICE_V2_URL || 'http://localhost:3003',
        metadata: { version: '2.0.0', capabilities: ['nft', 'generation', 'shared-infrastructure'] }
      },
      {
        name: 'blockchain-service',
        url: process.env.BLOCKCHAIN_SERVICE_V2_URL || 'http://localhost:3004',
        metadata: { version: '2.0.0', capabilities: ['blockchain', 'smart-contracts', 'shared-infrastructure'] }
      },
      {
        name: 'marketplace-service',
        url: process.env.MARKETPLACE_SERVICE_V2_URL || 'http://localhost:3005',
        metadata: { version: '2.0.0', capabilities: ['marketplace', 'trading', 'shared-infrastructure'] }
      },
      {
        name: 'project-service',
        url: process.env.PROJECT_SERVICE_V2_URL || 'http://localhost:3006',
        metadata: { version: '2.0.0', capabilities: ['projects', 'management', 'shared-infrastructure'] }
      },
      {
        name: 'analytics-service',
        url: process.env.ANALYTICS_SERVICE_V2_URL || 'http://localhost:3007',
        metadata: { version: '2.0.0', capabilities: ['analytics', 'reporting', 'shared-infrastructure'] }
      },
      {
        name: 'notification-service',
        url: process.env.NOTIFICATION_SERVICE_V2_URL || 'http://localhost:3008',
        metadata: { version: '2.0.0', capabilities: ['notifications', 'messaging', 'shared-infrastructure'] }
      },
    ];

    for (const config of serviceConfigs) {
      await this.registerService(config);
    }

    this.logger.info(`Initialized service registry with ${serviceConfigs.length} services`);
  }

  private startHealthChecking(): void {
    this.healthCheckTimer = setInterval(async () => {
      await this.performHealthChecks();
    }, this.healthCheckInterval);
  }

  private async performHealthChecks(): Promise<void> {
    const startTime = Date.now();
    let totalChecks = 0;
    let healthyCount = 0;

    for (const [serviceName, instances] of this.services.entries()) {
      for (const instance of instances) {
        totalChecks++;
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);

          const response = await fetch(`${instance.url}/health/simple`, {
            method: 'GET',
            signal: controller.signal,
          });
          
          clearTimeout(timeoutId);
          
          const previousHealth = instance.health;
          instance.health = response.ok ? 'healthy' : 'unhealthy';
          instance.lastHealthCheck = new Date();
          
          if (instance.health === 'healthy') {
            healthyCount++;
          }

          // Log health state changes
          if (previousHealth !== instance.health) {
            this.logger.logBusinessEvent('service-discovery', 'health-change', 'SUCCESS', {
              business: {
                domain: 'service-discovery',
                entity: 'service-instance',
                action: 'health-change',
                outcome: 'SUCCESS',
                attributes: {
                  serviceName,
                  instanceId: instance.id,
                  previousHealth,
                  currentHealth: instance.health,
                },
              },
            });
          }
          
          this.metrics.increment('health_checks_total', {
            service: serviceName,
            status: instance.health,
          });
          
        } catch (error) {
          const previousHealth = instance.health;
          instance.health = 'unhealthy';
          instance.lastHealthCheck = new Date();
          
          if (previousHealth !== 'unhealthy') {
            this.logger.warn(`Health check failed for ${serviceName}:${instance.id}`, {
              error: error.message,
              instanceUrl: instance.url,
            });
          }

          this.metrics.increment('health_checks_total', {
            service: serviceName,
            status: 'unhealthy',
          });
        }
      }
    }

    const duration = Date.now() - startTime;
    this.metrics.observe('health_check_duration', duration);
    this.metrics.gauge('total_service_instances', totalChecks);
    this.metrics.gauge('healthy_service_instances_total', healthyCount);
  }

  private selectRoundRobinInstance(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    // Simple round-robin implementation
    // In production, you might want to store round-robin state
    const index = Math.floor(Math.random() * instances.length);
    return instances[index];
  }

  private selectWeightedInstance(instances: ServiceInstance[]): ServiceInstance {
    const totalWeight = instances.reduce((sum, instance) => sum + instance.metadata.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const instance of instances) {
      random -= instance.metadata.weight;
      if (random <= 0) {
        return instance;
      }
    }
    
    return instances[0]; // Fallback
  }
}
