/**
 * Analysis Module - Profile Analysis Service V2
 * 
 * Core profile analysis business logic
 */

import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AnalysisController } from './controllers/analysis.controller';
import { AnalysisService } from './services/analysis.service';
import { QueueService } from './services/queue.service';
import { AIService } from './services/ai.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [
    PrismaModule,
    HttpModule,
  ],
  controllers: [AnalysisController],
  providers: [
    AnalysisService,
    QueueService,
    AIService,
  ],
  exports: [
    AnalysisService,
    QueueService,
    AIService,
  ],
})
export class AnalysisModule {}
