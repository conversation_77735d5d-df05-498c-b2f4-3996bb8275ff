#!/bin/bash

# Implement Authentication Patterns Standardization
# This script standardizes authentication across all services

set -e

echo "🔐 Implementing Authentication Patterns Standardization"
echo "======================================================"

# Services to standardize
SERVICES=(
    "api-gateway"
    "user-service"
    "profile-analysis-service"
    "nft-generation-service"
    "blockchain-service"
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Function to create standardized auth module for service
create_service_auth_module() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    echo "🔧 Creating standardized auth module for $service_name..."
    
    # Check if service directory exists
    if [ ! -d "$service_dir" ]; then
        echo "❌ Service directory not found: $service_dir"
        return 1
    fi
    
    # Create auth directory structure
    mkdir -p "$service_dir/src/auth"
    
    # Create service-specific auth module
    cat > "$service_dir/src/auth/auth.module.ts" << 'EOF'
import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

// Shared authentication components
import { StandardizedJwtAuthGuard } from '../../../../shared/auth/guards/jwt-auth.guard';
import { StandardizedRBACGuard } from '../../../../shared/auth/guards/rbac.guard';

// Service-specific auth services
import { AuthService } from './services/auth.service';
import { PermissionService } from './services/permission.service';
import { SessionService } from './services/session.service';

@Global()
@Module({
  imports: [
    ConfigModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN') || '24h',
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    // Service implementations
    AuthService,
    PermissionService,
    SessionService,
    
    // Provide services for injection
    {
      provide: 'AUTH_SERVICE',
      useClass: AuthService,
    },
    {
      provide: 'PERMISSION_SERVICE',
      useClass: PermissionService,
    },
    {
      provide: 'SESSION_SERVICE',
      useClass: SessionService,
    },
    
    // Guards
    StandardizedJwtAuthGuard,
    StandardizedRBACGuard,
  ],
  exports: [
    AuthService,
    PermissionService,
    SessionService,
    StandardizedJwtAuthGuard,
    StandardizedRBACGuard,
    JwtModule,
    'AUTH_SERVICE',
    'PERMISSION_SERVICE',
    'SESSION_SERVICE',
  ],
})
export class ServiceAuthModule {}
EOF

    # Create auth service implementation
    mkdir -p "$service_dir/src/auth/services"
    
    cat > "$service_dir/src/auth/services/auth.service.ts" << 'EOF'
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { 
  IAuthService, 
  AuthenticatedUser, 
  TokenValidationResult, 
  TokenType,
  AuthResult 
} from '../../../../../shared/auth/interfaces/auth.interface';

@Injectable()
export class AuthService implements IAuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  async validateToken(token: string, context?: any): Promise<TokenValidationResult> {
    try {
      // Verify JWT token
      const decoded = this.jwtService.verify(token);
      
      if (!decoded || !decoded.sub) {
        return {
          success: false,
          error: 'Invalid token structure',
        };
      }

      // Get user information (this would typically query a database)
      const user = await this.getUserById(decoded.sub);
      if (!user) {
        return {
          success: false,
          error: 'User not found',
        };
      }

      // Check if user is active
      if (!user.isActive) {
        return {
          success: false,
          error: 'User account is inactive',
        };
      }

      return {
        success: true,
        data: {
          user,
          tokenInfo: {
            tokenId: decoded.jti || 'unknown',
            type: TokenType.ACCESS_TOKEN,
            issuedAt: new Date(decoded.iat * 1000),
            expiresAt: new Date(decoded.exp * 1000),
            issuer: decoded.iss || 'unknown',
            audience: Array.isArray(decoded.aud) ? decoded.aud : [decoded.aud || 'unknown'],
            scopes: decoded.scopes || [],
          },
        },
      };
    } catch (error) {
      this.logger.warn('Token validation failed', { error: error.message });
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async generateToken(user: AuthenticatedUser, type: TokenType): Promise<string> {
    const payload = {
      sub: user.id,
      email: user.email,
      roles: user.roles,
      permissions: user.permissions,
      type,
    };

    const options = {
      expiresIn: type === TokenType.REFRESH_TOKEN 
        ? this.configService.get<string>('JWT_REFRESH_EXPIRES_IN') || '7d'
        : this.configService.get<string>('JWT_EXPIRES_IN') || '24h',
    };

    return this.jwtService.sign(payload, options);
  }

  async refreshToken(refreshToken: string): Promise<AuthResult> {
    try {
      const decoded = this.jwtService.verify(refreshToken);
      const user = await this.getUserById(decoded.sub);
      
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      const newToken = await this.generateToken(user, TokenType.ACCESS_TOKEN);
      return { success: true, data: { token: newToken } };
    } catch (error) {
      return { success: false, error: 'Invalid refresh token' };
    }
  }

  async revokeToken(token: string): Promise<boolean> {
    // Implementation would typically involve adding token to blacklist
    // For now, we'll just log the revocation
    this.logger.log('Token revoked', { token: token.substring(0, 10) + '...' });
    return true;
  }

  async getUserById(userId: string): Promise<AuthenticatedUser | null> {
    // This is a placeholder implementation
    // In a real application, you would query your user database
    
    // For now, return a mock user for demonstration
    if (userId === 'mock-user-id') {
      return {
        id: userId,
        email: '<EMAIL>',
        username: 'mockuser',
        roles: ['user'],
        permissions: ['user:read', 'user:profile:read', 'user:profile:write'],
        isActive: true,
        isVerified: true,
        lastLoginAt: new Date(),
      };
    }

    return null;
  }

  async hasPermission(user: AuthenticatedUser, permission: string): Promise<boolean> {
    return user.permissions.includes(permission);
  }

  async hasAnyPermission(user: AuthenticatedUser, permissions: string[]): Promise<boolean> {
    return permissions.some(permission => user.permissions.includes(permission));
  }

  async hasRole(user: AuthenticatedUser, role: string): Promise<boolean> {
    return user.roles.includes(role);
  }
}
EOF

    # Create permission service implementation
    cat > "$service_dir/src/auth/services/permission.service.ts" << 'EOF'
import { Injectable, Logger } from '@nestjs/common';
import { 
  IPermissionService, 
  AuthenticatedUser 
} from '../../../../../shared/auth/interfaces/auth.interface';
import { Permission, Role, DEFAULT_ROLE_PERMISSIONS } from '../../../../../shared/auth/interfaces/permission.interface';

@Injectable()
export class PermissionService implements IPermissionService {
  private readonly logger = new Logger(PermissionService.name);

  async getUserPermissions(userId: string): Promise<string[]> {
    // This would typically query the database for user permissions
    // For now, return empty array
    return [];
  }

  async getRolePermissions(role: string): Promise<string[]> {
    const roleConfig = DEFAULT_ROLE_PERMISSIONS.find(rp => rp.role === role);
    return roleConfig ? roleConfig.permissions : [];
  }

  async hasPermission(user: AuthenticatedUser, permission: string): Promise<boolean> {
    // Check direct permissions
    if (user.permissions.includes(permission)) {
      return true;
    }

    // Check role-based permissions
    for (const role of user.roles) {
      const rolePermissions = await this.getRolePermissions(role);
      if (rolePermissions.includes(permission)) {
        return true;
      }
    }

    return false;
  }

  async hasAnyPermission(user: AuthenticatedUser, permissions: string[]): Promise<boolean> {
    for (const permission of permissions) {
      if (await this.hasPermission(user, permission)) {
        return true;
      }
    }
    return false;
  }

  async hasAllPermissions(user: AuthenticatedUser, permissions: string[]): Promise<boolean> {
    for (const permission of permissions) {
      if (!(await this.hasPermission(user, permission))) {
        return false;
      }
    }
    return true;
  }

  async grantPermission(userId: string, permission: string): Promise<boolean> {
    // Implementation would update database
    this.logger.log('Permission granted', { userId, permission });
    return true;
  }

  async revokePermission(userId: string, permission: string): Promise<boolean> {
    // Implementation would update database
    this.logger.log('Permission revoked', { userId, permission });
    return true;
  }
}
EOF

    # Create session service implementation
    cat > "$service_dir/src/auth/services/session.service.ts" << 'EOF'
import { Injectable, Logger } from '@nestjs/common';
import { 
  ISessionService, 
  AuthenticatedUser, 
  UserSession,
  DeviceInfo,
  RequestInfo 
} from '../../../../../shared/auth/interfaces/auth.interface';

@Injectable()
export class SessionService implements ISessionService {
  private readonly logger = new Logger(SessionService.name);
  private readonly sessions = new Map<string, UserSession>(); // In-memory store for demo

  async createSession(
    user: AuthenticatedUser, 
    deviceInfo: DeviceInfo, 
    requestInfo: RequestInfo
  ): Promise<UserSession> {
    const sessionId = this.generateSessionId();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours

    const session: UserSession = {
      sessionId,
      userId: user.id,
      deviceInfo,
      ipAddress: requestInfo.ipAddress,
      userAgent: requestInfo.userAgent,
      createdAt: now,
      expiresAt,
      lastActivityAt: now,
      isActive: true,
    };

    this.sessions.set(sessionId, session);
    this.logger.log('Session created', { sessionId, userId: user.id });

    return session;
  }

  async getSession(sessionId: string): Promise<UserSession | null> {
    return this.sessions.get(sessionId) || null;
  }

  async updateSessionActivity(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.lastActivityAt = new Date();
      return true;
    }
    return false;
  }

  async terminateSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.isActive = false;
      this.logger.log('Session terminated', { sessionId });
      return true;
    }
    return false;
  }

  async terminateAllUserSessions(userId: string): Promise<boolean> {
    let terminated = 0;
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.userId === userId && session.isActive) {
        session.isActive = false;
        terminated++;
      }
    }
    this.logger.log('All user sessions terminated', { userId, count: terminated });
    return true;
  }

  async getActiveSessions(userId: string): Promise<UserSession[]> {
    const userSessions: UserSession[] = [];
    for (const session of this.sessions.values()) {
      if (session.userId === userId && session.isActive) {
        userSessions.push(session);
      }
    }
    return userSessions;
  }

  async cleanExpiredSessions(): Promise<number> {
    const now = new Date();
    let cleaned = 0;
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.expiresAt < now) {
        this.sessions.delete(sessionId);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      this.logger.log('Expired sessions cleaned', { count: cleaned });
    }
    
    return cleaned;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
EOF

    # Create auth module index
    cat > "$service_dir/src/auth/index.ts" << 'EOF'
export * from './auth.module';
export * from './services/auth.service';
export * from './services/permission.service';
export * from './services/session.service';
EOF

    echo "✅ Auth module created for $service_name"
}

# Function to update app.module.ts to use standardized auth
update_app_module_auth() {
    local service_name="$1"
    local service_dir="services/$service_name"
    local app_module="$service_dir/src/app.module.ts"
    
    echo "🔧 Updating app.module.ts auth configuration for $service_name..."
    
    # Create backup
    cp "$app_module" "$app_module.auth_backup.$(date +%Y%m%d_%H%M%S)"
    
    # Update imports section to include auth module
    sed -i '/import.*EnterpriseModule/a import { ServiceAuthModule } from '\''./auth/auth.module'\'';' "$app_module"
    
    # Update imports array to include ServiceAuthModule
    sed -i '/EnterpriseModule,/a \    ServiceAuthModule,' "$app_module"
    
    echo "✅ app.module.ts updated for $service_name"
}

# Function to install required dependencies
install_auth_dependencies() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    echo "📦 Installing auth dependencies for $service_name..."
    
    cd "$service_dir"
    
    # Check if dependencies are already installed
    if ! npm list @nestjs/passport &>/dev/null; then
        npm install @nestjs/passport passport passport-jwt
    fi
    
    if ! npm list @nestjs/jwt &>/dev/null; then
        npm install @nestjs/jwt
    fi
    
    cd - > /dev/null
    
    echo "✅ Dependencies installed for $service_name"
}

# Main execution
echo "🚀 Starting authentication standardization..."

for service_name in "${SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service_name..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service_name" ]; then
        echo "❌ Service directory not found: services/$service_name"
        continue
    fi
    
    # Create auth module
    create_service_auth_module "$service_name"
    
    # Update app.module.ts
    update_app_module_auth "$service_name"
    
    # Install dependencies (commented out to avoid hanging)
    # install_auth_dependencies "$service_name"
    
    echo "✅ $service_name authentication standardization complete"
done

echo ""
echo "🎉 Authentication Patterns Standardization Completed!"
echo "===================================================="
echo ""
echo "📋 Summary:"
echo "- Created standardized auth modules for ${#SERVICES[@]} services"
echo "- Implemented JWT authentication with validation"
echo "- Added RBAC permission system"
echo "- Created session management services"
echo "- Updated app.module.ts files to use standardized auth"
echo ""
echo "📁 Files Created:"
echo "services/[service]/src/auth/auth.module.ts           # Service auth module"
echo "services/[service]/src/auth/services/auth.service.ts # Auth service implementation"
echo "services/[service]/src/auth/services/permission.service.ts # Permission service"
echo "services/[service]/src/auth/services/session.service.ts    # Session service"
echo "services/[service]/src/auth/index.ts                 # Auth exports"
echo ""
echo "🔍 Next Steps:"
echo "1. Install dependencies: npm install @nestjs/passport @nestjs/jwt passport passport-jwt"
echo "2. Test authentication endpoints"
echo "3. Implement database integration for user/permission storage"
echo "4. Test RBAC permission system"
echo "5. Configure production JWT secrets"
echo ""
echo "⚠️  Important Notes:"
echo "• Services now use standardized authentication patterns"
echo "• JWT tokens are validated consistently across all services"
echo "• RBAC system provides fine-grained permission control"
echo "• Session management is implemented for user tracking"
