import { Modu<PERSON> } from '@nestjs/common';
import { StorageController } from './storage.controller';
import { StorageService } from './storage.service';
import { MetadataController } from './metadata.controller';
import { AssetsController } from './assets.controller';
import { IPFSController } from './ipfs.controller';

@Module({
  controllers: [
    StorageController,
    MetadataController,
    AssetsController,
    IPFSController,
  ],
  providers: [StorageService],
  exports: [StorageService],
})
export class StorageModule {}
