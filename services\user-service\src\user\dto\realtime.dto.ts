import { IsString, Is<PERSON>ptional, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, Is<PERSON><PERSON>y, IsDateString, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum NotificationType {
  CAMPAIGN_UPDATE = 'campaign_update',
  NFT_GENERATED = 'nft_generated',
  NFT_MINTED = 'nft_minted',
  MARKETPLACE_SALE = 'marketplace_sale',
  MARKETPLACE_OFFER = 'marketplace_offer',
  REWARD_EARNED = 'reward_earned',
  SYSTEM_ALERT = 'system_alert',
  USER_MESSAGE = 'user_message',
  ACTIVITY_UPDATE = 'activity_update',
  ANALYTICS_UPDATE = 'analytics_update',
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum ActivityType {
  USER_JOINED = 'user_joined',
  CAMPAIGN_CREATED = 'campaign_created',
  CAMPAIGN_COMPLETED = 'campaign_completed',
  NFT_GENERATED = 'nft_generated',
  NFT_LISTED = 'nft_listed',
  NFT_SOLD = 'nft_sold',
  OFFER_MADE = 'offer_made',
  OFFER_ACCEPTED = 'offer_accepted',
  REWARD_CLAIMED = 'reward_claimed',
  MILESTONE_REACHED = 'milestone_reached',
}

export enum WebSocketEvent {
  // Connection events
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  
  // Notification events
  NOTIFICATION = 'notification',
  NOTIFICATION_READ = 'notification_read',
  NOTIFICATION_CLEAR = 'notification_clear',
  
  // Activity events
  ACTIVITY_UPDATE = 'activity_update',
  USER_ACTIVITY = 'user_activity',
  PLATFORM_ACTIVITY = 'platform_activity',
  
  // Analytics events
  ANALYTICS_UPDATE = 'analytics_update',
  REAL_TIME_METRICS = 'real_time_metrics',
  
  // Chat events
  MESSAGE_SEND = 'message_send',
  MESSAGE_RECEIVE = 'message_receive',
  TYPING_START = 'typing_start',
  TYPING_STOP = 'typing_stop',
  
  // Campaign events
  CAMPAIGN_UPDATE = 'campaign_update',
  CAMPAIGN_PROGRESS = 'campaign_progress',
  
  // Marketplace events
  MARKETPLACE_UPDATE = 'marketplace_update',
  PRICE_UPDATE = 'price_update',
  
  // System events
  SYSTEM_STATUS = 'system_status',
  MAINTENANCE_MODE = 'maintenance_mode',
}

export class NotificationDto {
  @ApiProperty({
    description: 'Notification ID',
    example: 'notif_123',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Notification type',
    enum: NotificationType,
    example: NotificationType.NFT_GENERATED,
  })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({
    description: 'Notification priority',
    enum: NotificationPriority,
    example: NotificationPriority.MEDIUM,
  })
  @IsEnum(NotificationPriority)
  priority: NotificationPriority;

  @ApiProperty({
    description: 'Notification title',
    example: 'NFT Generated Successfully!',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Notification message',
    example: 'Your legendary NFT has been generated and is ready for minting.',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Notification icon URL',
    example: 'https://platform.com/icons/nft-generated.png',
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiPropertyOptional({
    description: 'Action URL for notification',
    example: '/nfts/nft_123',
  })
  @IsOptional()
  @IsString()
  actionUrl?: string;

  @ApiPropertyOptional({
    description: 'Action button text',
    example: 'View NFT',
  })
  @IsOptional()
  @IsString()
  actionText?: string;

  @ApiProperty({
    description: 'Target user ID',
    example: 'user_123',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Whether notification is read',
    example: false,
  })
  @IsBoolean()
  isRead: boolean;

  @ApiProperty({
    description: 'Notification creation timestamp',
    example: '2025-06-04T10:30:00Z',
  })
  @IsDateString()
  createdAt: string;

  @ApiPropertyOptional({
    description: 'Notification expiration timestamp',
    example: '2025-06-11T10:30:00Z',
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({
    description: 'Additional notification data',
    example: {
      nftId: 'nft_123',
      campaignId: 'campaign_456',
      rarity: 'legendary',
    },
  })
  @IsOptional()
  data?: Record<string, any>;
}

export class CreateNotificationDto {
  @ApiProperty({
    description: 'Notification type',
    enum: NotificationType,
    example: NotificationType.NFT_GENERATED,
  })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({
    description: 'Notification priority',
    enum: NotificationPriority,
    example: NotificationPriority.MEDIUM,
  })
  @IsEnum(NotificationPriority)
  priority: NotificationPriority;

  @ApiProperty({
    description: 'Notification title',
    example: 'NFT Generated Successfully!',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Notification message',
    example: 'Your legendary NFT has been generated and is ready for minting.',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Target user ID',
    example: 'user_123',
  })
  @IsString()
  userId: string;

  @ApiPropertyOptional({
    description: 'Notification icon URL',
    example: 'https://platform.com/icons/nft-generated.png',
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiPropertyOptional({
    description: 'Action URL for notification',
    example: '/nfts/nft_123',
  })
  @IsOptional()
  @IsString()
  actionUrl?: string;

  @ApiPropertyOptional({
    description: 'Action button text',
    example: 'View NFT',
  })
  @IsOptional()
  @IsString()
  actionText?: string;

  @ApiPropertyOptional({
    description: 'Notification expiration in hours',
    example: 168,
    default: 168,
  })
  @IsOptional()
  @IsNumber()
  expiresInHours?: number = 168; // 7 days default

  @ApiPropertyOptional({
    description: 'Additional notification data',
    example: {
      nftId: 'nft_123',
      campaignId: 'campaign_456',
      rarity: 'legendary',
    },
  })
  @IsOptional()
  data?: Record<string, any>;
}

export class ActivityFeedItemDto {
  @ApiProperty({
    description: 'Activity ID',
    example: 'activity_123',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Activity type',
    enum: ActivityType,
    example: ActivityType.NFT_GENERATED,
  })
  @IsEnum(ActivityType)
  type: ActivityType;

  @ApiProperty({
    description: 'Activity title',
    example: 'New NFT Generated',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Activity description',
    example: 'CryptoEnthusiast generated a legendary NFT from the Summer Campaign',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'User who performed the activity',
  })
  user: {
    id: string;
    username: string;
    displayName?: string;
    avatar?: string;
  };

  @ApiPropertyOptional({
    description: 'Related entity details',
  })
  @IsOptional()
  entity?: {
    type: string;
    id: string;
    name: string;
    url?: string;
  };

  @ApiProperty({
    description: 'Activity timestamp',
    example: '2025-06-04T10:30:00Z',
  })
  @IsDateString()
  createdAt: string;

  @ApiPropertyOptional({
    description: 'Activity metadata',
    example: {
      nftRarity: 'legendary',
      campaignType: 'social_engagement',
      value: '1.5 ETH',
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class ChatMessageDto {
  @ApiProperty({
    description: 'Message ID',
    example: 'msg_123',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Chat room ID',
    example: 'room_456',
  })
  @IsString()
  roomId: string;

  @ApiProperty({
    description: 'Sender user ID',
    example: 'user_123',
  })
  @IsString()
  senderId: string;

  @ApiProperty({
    description: 'Sender details',
  })
  sender: {
    id: string;
    username: string;
    displayName?: string;
    avatar?: string;
  };

  @ApiProperty({
    description: 'Message content',
    example: 'Hey everyone! Just minted my first NFT!',
  })
  @IsString()
  content: string;

  @ApiPropertyOptional({
    description: 'Message type',
    example: 'text',
    enum: ['text', 'image', 'file', 'system'],
  })
  @IsOptional()
  @IsString()
  messageType?: string = 'text';

  @ApiPropertyOptional({
    description: 'Attached media URLs',
    example: ['https://platform.com/uploads/image1.jpg'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiProperty({
    description: 'Message timestamp',
    example: '2025-06-04T10:30:00Z',
  })
  @IsDateString()
  createdAt: string;

  @ApiPropertyOptional({
    description: 'Message edit timestamp',
    example: '2025-06-04T10:35:00Z',
  })
  @IsOptional()
  @IsDateString()
  editedAt?: string;

  @ApiProperty({
    description: 'Whether message is edited',
    example: false,
  })
  @IsBoolean()
  isEdited: boolean;

  @ApiPropertyOptional({
    description: 'Reply to message ID',
    example: 'msg_122',
  })
  @IsOptional()
  @IsString()
  replyToId?: string;

  @ApiPropertyOptional({
    description: 'Message metadata',
    example: {
      mentions: ['user_456'],
      hashtags: ['#NFT', '#crypto'],
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class SendMessageDto {
  @ApiProperty({
    description: 'Chat room ID',
    example: 'room_456',
  })
  @IsString()
  roomId: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hey everyone! Just minted my first NFT!',
  })
  @IsString()
  content: string;

  @ApiPropertyOptional({
    description: 'Message type',
    example: 'text',
    enum: ['text', 'image', 'file'],
  })
  @IsOptional()
  @IsString()
  messageType?: string = 'text';

  @ApiPropertyOptional({
    description: 'Attached media URLs',
    example: ['https://platform.com/uploads/image1.jpg'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiPropertyOptional({
    description: 'Reply to message ID',
    example: 'msg_122',
  })
  @IsOptional()
  @IsString()
  replyToId?: string;

  @ApiPropertyOptional({
    description: 'Message metadata',
    example: {
      mentions: ['user_456'],
      hashtags: ['#NFT', '#crypto'],
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class RealTimeMetricsDto {
  @ApiProperty({
    description: 'Active users count',
    example: 1247,
  })
  @IsNumber()
  activeUsers: number;

  @ApiProperty({
    description: 'Active campaigns count',
    example: 12,
  })
  @IsNumber()
  activeCampaigns: number;

  @ApiProperty({
    description: 'Recent transactions count (last hour)',
    example: 23,
  })
  @IsNumber()
  recentTransactions: number;

  @ApiProperty({
    description: 'Live engagement score',
    example: 78.5,
  })
  @IsNumber()
  liveEngagement: number;

  @ApiProperty({
    description: 'Current marketplace volume (24h)',
    example: '45.67',
  })
  @IsString()
  currentVolume: string;

  @ApiProperty({
    description: 'Active listings count',
    example: 342,
  })
  @IsNumber()
  activeListings: number;

  @ApiProperty({
    description: 'Metrics timestamp',
    example: '2025-06-04T10:30:00Z',
  })
  @IsDateString()
  timestamp: string;

  @ApiPropertyOptional({
    description: 'Additional real-time metrics',
    example: {
      newUsersToday: 45,
      nftsGeneratedToday: 123,
      averageResponseTime: 250,
    },
  })
  @IsOptional()
  metrics?: Record<string, any>;
}

export class WebSocketResponseDto {
  @ApiProperty({
    description: 'Event type',
    enum: WebSocketEvent,
    example: WebSocketEvent.NOTIFICATION,
  })
  @IsEnum(WebSocketEvent)
  event: WebSocketEvent;

  @ApiProperty({
    description: 'Response data',
  })
  data: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: '2025-06-04T10:30:00Z',
  })
  @IsDateString()
  timestamp: string;

  @ApiPropertyOptional({
    description: 'Target room or user',
    example: 'user_123',
  })
  @IsOptional()
  @IsString()
  target?: string;

  @ApiPropertyOptional({
    description: 'Response metadata',
    example: {
      source: 'campaign_service',
      priority: 'medium',
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}
