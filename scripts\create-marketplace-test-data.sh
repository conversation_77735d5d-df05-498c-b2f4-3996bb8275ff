#!/bin/bash

# =============================================================================
# MARKETPLACE TEST DATA CREATION SCRIPT
# =============================================================================
# Creates comprehensive test data for marketplace business logic testing
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# API endpoints
MARKETPLACE_API="http://localhost:3006/api/marketplace"
API_GATEWAY="http://localhost:3010/api/marketplace"

echo ""
log_info "🧪 MARKETPLACE TEST DATA CREATION"
echo "=================================="

# Test if services are running
log_info "Checking service availability..."

if ! curl -s "$MARKETPLACE_API/../../health" > /dev/null; then
    log_error "Marketplace service not available at localhost:3006"
    exit 1
fi

if ! curl -s "http://localhost:3010/api/health" > /dev/null; then
    log_error "API Gateway not available at localhost:3010"
    exit 1
fi

log_success "Services are running"

# Test Data Variables
TEST_USER_ID="test_user_$(date +%s)"
TEST_NFT_ID="nft_$(date +%s)"
TEST_TOKEN_ID="token_$(date +%s)"

echo ""
log_info "📋 Creating test data with:"
echo "  User ID: $TEST_USER_ID"
echo "  NFT ID: $TEST_NFT_ID"
echo "  Token ID: $TEST_TOKEN_ID"

# Function to create test listing (using correct DTO structure)
create_test_listing() {
    # Generate a valid UUID for nftId
    local uuid=$(uuidgen 2>/dev/null || echo "550e8400-e29b-41d4-a716-************")

    local listing_data='{
        "nftId": "'$uuid'",
        "tokenId": "'$TEST_TOKEN_ID'",
        "contractAddress": "******************************************",
        "price": "1.5",
        "currency": "ETH",
        "listingType": "fixed_price",
        "description": "A test NFT for marketplace validation"
    }'

    echo "$listing_data"
}

# Function to test API endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    log_info "Testing: $description"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -H "x-user-id: $TEST_USER_ID" \
            -d "$data" \
            "$endpoint")
    else
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X "$method" \
            "$endpoint")
    fi
    
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    echo "  Response: $response_body"
    echo "  Status: $http_status"
    
    if [[ "$http_status" =~ ^2[0-9][0-9]$ ]]; then
        log_success "$description - SUCCESS"
        return 0
    else
        log_error "$description - FAILED (Status: $http_status)"
        return 1
    fi
}

echo ""
log_info "🧪 PHASE 2: BUSINESS LOGIC TESTING"
echo "=================================="

# Test 1: Create NFT Listing
echo ""
log_info "Test 1: Create NFT Listing"
echo "-------------------------"

listing_data=$(create_test_listing)
test_endpoint "POST" "$MARKETPLACE_API/listings" "$listing_data" "Create NFT Listing (Direct)"

# Test 2: Get All Listings
echo ""
log_info "Test 2: Get All Listings"
echo "----------------------"

test_endpoint "GET" "$MARKETPLACE_API/listings" "" "Get All Listings (Direct)"

# Test 3: API Gateway Listing Creation
echo ""
log_info "Test 3: API Gateway Listing Creation"
echo "----------------------------------"

test_endpoint "POST" "$API_GATEWAY/listings" "$listing_data" "Create NFT Listing (via API Gateway)"

# Test 4: API Gateway Get Listings
echo ""
log_info "Test 4: API Gateway Get Listings"
echo "------------------------------"

test_endpoint "GET" "$API_GATEWAY/listings" "" "Get All Listings (via API Gateway)"

echo ""
log_success "🎉 Marketplace Business Logic Testing Complete!"
echo ""
log_info "📊 Summary:"
log_info "- Test data created with User ID: $TEST_USER_ID"
log_info "- NFT listing tests executed"
log_info "- Both direct service and API Gateway routes tested"
echo ""
