import { Controller, Get, Post, Query, Body, Req, UseGuards, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { SearchService } from '../services/search.service';
import { 
  SearchQueryDto,
  AutocompleteQueryDto,
  SearchResultsDto,
  SearchSuggestionDto,
  SearchType,
  SortOrder,
  FilterOperator 
} from '../dto/search.dto';
import { ErrorResponseDto } from '../../common/dto/error-response.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RBACGuard } from '../guards/rbac.guard';
import { Authenticated } from '../decorators/permissions.decorator';
import { Public } from '../decorators/public.decorator';

@ApiTags('Advanced Search & Filtering')
@Controller('search')
@UseGuards(JwtAuthGuard, RBACGuard)
@ApiBearerAuth()
export class SearchController {
  constructor(private readonly searchService: SearchService) {}

  @Get()
  @Authenticated()
  @ApiOperation({ summary: 'Perform advanced search across platform entities' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'Search query string' })
  @ApiQuery({ name: 'type', enum: SearchType, required: true, description: 'Search type' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiQuery({ name: 'sortBy', type: String, required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', enum: SortOrder, required: false, description: 'Sort order' })
  @ApiQuery({ name: 'includeFacets', type: Boolean, required: false, description: 'Include faceted search results' })
  @ApiQuery({ name: 'includeSuggestions', type: Boolean, required: false, description: 'Include search suggestions' })
  @ApiQuery({ name: 'includeAnalytics', type: Boolean, required: false, description: 'Include search analytics' })
  @ApiQuery({ name: 'dateFrom', type: String, required: false, description: 'Start date filter (ISO string)' })
  @ApiQuery({ name: 'dateTo', type: String, required: false, description: 'End date filter (ISO string)' })
  @ApiQuery({ name: 'location', type: String, required: false, description: 'Geographic location filter' })
  @ApiQuery({ name: 'language', type: String, required: false, description: 'Language filter' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully', type: SearchResultsDto })
  @ApiResponse({ status: 400, description: 'Invalid search parameters', type: ErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async search(
    @Query('query') query: string | undefined,
    @Query('type') type: SearchType,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Query('sortBy') sortBy: string | undefined,
    @Query('sortOrder') sortOrder: SortOrder | undefined,
    @Query('includeFacets') includeFacets: boolean | undefined,
    @Query('includeSuggestions') includeSuggestions: boolean | undefined,
    @Query('includeAnalytics') includeAnalytics: boolean | undefined,
    @Query('dateFrom') dateFrom: string | undefined,
    @Query('dateTo') dateTo: string | undefined,
    @Query('location') location: string | undefined,
    @Query('language') language: string | undefined,
    @Req() request: any
  ) {
    const searchQuery: SearchQueryDto = {
      query,
      type,
      page: page || 1,
      limit: Math.min(limit || 20, 100),
      sortBy,
      sortOrder,
      includeFacets: includeFacets || false,
      includeSuggestions: includeSuggestions || false,
      includeAnalytics: includeAnalytics || false,
      dateFrom,
      dateTo,
      location,
      language,
    };

    const result = await this.searchService.search(searchQuery, request.context);
    return result;
  }

  @Get('users')
  @Authenticated()
  @ApiOperation({ summary: 'Search users with advanced filtering' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'User search query' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'sortBy', type: String, required: false, description: 'Sort field (username, displayName, createdAt, reputation)' })
  @ApiQuery({ name: 'sortOrder', enum: SortOrder, required: false, description: 'Sort order' })
  @ApiQuery({ name: 'verified', type: Boolean, required: false, description: 'Filter by verified status' })
  @ApiQuery({ name: 'includeFacets', type: Boolean, required: false, description: 'Include faceted results' })
  @ApiResponse({ status: 200, description: 'User search results retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async searchUsers(
    @Query('query') query: string | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Query('sortBy') sortBy: string | undefined,
    @Query('sortOrder') sortOrder: SortOrder | undefined,
    @Query('verified') verified: boolean | undefined,
    @Query('includeFacets') includeFacets: boolean | undefined,
    @Req() request: any
  ) {
    const searchQuery: SearchQueryDto = {
      query,
      type: SearchType.USERS,
      page: page || 1,
      limit: Math.min(limit || 20, 100),
      sortBy,
      sortOrder,
      includeFacets: includeFacets || false,
      filters: verified !== undefined ? [{
        field: 'isVerified',
        operator: FilterOperator.EQUALS,
        value: verified,
      }] : undefined,
    };

    const result = await this.searchService.search(searchQuery, request.context);
    return result;
  }

  @Get('campaigns')
  @Authenticated()
  @ApiOperation({ summary: 'Search campaigns with advanced filtering' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'Campaign search query' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'sortBy', type: String, required: false, description: 'Sort field (name, createdAt, startDate, participants)' })
  @ApiQuery({ name: 'sortOrder', enum: SortOrder, required: false, description: 'Sort order' })
  @ApiQuery({ name: 'status', type: String, required: false, description: 'Filter by campaign status' })
  @ApiQuery({ name: 'type', type: String, required: false, description: 'Filter by campaign type' })
  @ApiQuery({ name: 'includeFacets', type: Boolean, required: false, description: 'Include faceted results' })
  @ApiResponse({ status: 200, description: 'Campaign search results retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async searchCampaigns(
    @Query('query') query: string | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Query('sortBy') sortBy: string | undefined,
    @Query('sortOrder') sortOrder: SortOrder | undefined,
    @Query('status') status: string | undefined,
    @Query('type') campaignType: string | undefined,
    @Query('includeFacets') includeFacets: boolean | undefined,
    @Req() request: any
  ) {
    const filters = [];
    if (status) {
      filters.push({
        field: 'status',
        operator: FilterOperator.EQUALS,
        value: status,
      });
    }
    if (campaignType) {
      filters.push({
        field: 'type',
        operator: FilterOperator.EQUALS,
        value: campaignType,
      });
    }

    const searchQuery: SearchQueryDto = {
      query,
      type: SearchType.CAMPAIGNS,
      page: page || 1,
      limit: Math.min(limit || 20, 100),
      sortBy,
      sortOrder,
      includeFacets: includeFacets || false,
      filters: filters.length > 0 ? filters : undefined,
    };

    const result = await this.searchService.search(searchQuery, request.context);
    return result;
  }

  @Get('nfts')
  @Authenticated()
  @ApiOperation({ summary: 'Search NFTs with advanced filtering' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'NFT search query' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'sortBy', type: String, required: false, description: 'Sort field (name, createdAt, rarity, estimatedValue)' })
  @ApiQuery({ name: 'sortOrder', enum: SortOrder, required: false, description: 'Sort order' })
  @ApiQuery({ name: 'rarity', type: String, required: false, description: 'Filter by NFT rarity' })
  @ApiQuery({ name: 'status', type: String, required: false, description: 'Filter by NFT status' })
  @ApiQuery({ name: 'minValue', type: Number, required: false, description: 'Minimum estimated value filter' })
  @ApiQuery({ name: 'maxValue', type: Number, required: false, description: 'Maximum estimated value filter' })
  @ApiQuery({ name: 'includeFacets', type: Boolean, required: false, description: 'Include faceted results' })
  @ApiResponse({ status: 200, description: 'NFT search results retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async searchNFTs(
    @Query('query') query: string | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Query('sortBy') sortBy: string | undefined,
    @Query('sortOrder') sortOrder: SortOrder | undefined,
    @Query('rarity') rarity: string | undefined,
    @Query('status') status: string | undefined,
    @Query('minValue') minValue: number | undefined,
    @Query('maxValue') maxValue: number | undefined,
    @Query('includeFacets') includeFacets: boolean | undefined,
    @Req() request: any
  ) {
    const filters = [];
    if (rarity) {
      filters.push({
        field: 'rarity',
        operator: FilterOperator.EQUALS,
        value: rarity,
      });
    }
    if (status) {
      filters.push({
        field: 'status',
        operator: FilterOperator.EQUALS,
        value: status,
      });
    }
    if (minValue !== undefined && maxValue !== undefined) {
      filters.push({
        field: 'estimatedValue',
        operator: FilterOperator.BETWEEN,
        value: minValue,
        value2: maxValue,
      });
    } else if (minValue !== undefined) {
      filters.push({
        field: 'estimatedValue',
        operator: FilterOperator.GREATER_THAN_OR_EQUAL,
        value: minValue,
      });
    } else if (maxValue !== undefined) {
      filters.push({
        field: 'estimatedValue',
        operator: FilterOperator.LESS_THAN_OR_EQUAL,
        value: maxValue,
      });
    }

    const searchQuery: SearchQueryDto = {
      query,
      type: SearchType.NFTS,
      page: page || 1,
      limit: Math.min(limit || 20, 100),
      sortBy,
      sortOrder,
      includeFacets: includeFacets || false,
      filters: filters.length > 0 ? filters : undefined,
    };

    const result = await this.searchService.search(searchQuery, request.context);
    return result;
  }

  @Get('marketplace')
  @Authenticated()
  @ApiOperation({ summary: 'Search marketplace listings with advanced filtering' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'Marketplace search query' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'sortBy', type: String, required: false, description: 'Sort field (price, createdAt, nftName, nftRarity)' })
  @ApiQuery({ name: 'sortOrder', enum: SortOrder, required: false, description: 'Sort order' })
  @ApiQuery({ name: 'minPrice', type: Number, required: false, description: 'Minimum price filter' })
  @ApiQuery({ name: 'maxPrice', type: Number, required: false, description: 'Maximum price filter' })
  @ApiQuery({ name: 'currency', type: String, required: false, description: 'Filter by currency' })
  @ApiQuery({ name: 'rarity', type: String, required: false, description: 'Filter by NFT rarity' })
  @ApiQuery({ name: 'includeFacets', type: Boolean, required: false, description: 'Include faceted results' })
  @ApiResponse({ status: 200, description: 'Marketplace search results retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async searchMarketplace(
    @Query('query') query: string | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Query('sortBy') sortBy: string | undefined,
    @Query('sortOrder') sortOrder: SortOrder | undefined,
    @Query('minPrice') minPrice: number | undefined,
    @Query('maxPrice') maxPrice: number | undefined,
    @Query('currency') currency: string | undefined,
    @Query('rarity') rarity: string | undefined,
    @Query('includeFacets') includeFacets: boolean | undefined,
    @Req() request: any
  ) {
    const filters = [];
    if (minPrice !== undefined && maxPrice !== undefined) {
      filters.push({
        field: 'price',
        operator: FilterOperator.BETWEEN,
        value: minPrice,
        value2: maxPrice,
      });
    } else if (minPrice !== undefined) {
      filters.push({
        field: 'price',
        operator: FilterOperator.GREATER_THAN_OR_EQUAL,
        value: minPrice,
      });
    } else if (maxPrice !== undefined) {
      filters.push({
        field: 'price',
        operator: FilterOperator.LESS_THAN_OR_EQUAL,
        value: maxPrice,
      });
    }
    if (currency) {
      filters.push({
        field: 'currency',
        operator: FilterOperator.EQUALS,
        value: currency,
      });
    }
    if (rarity) {
      filters.push({
        field: 'nft.rarity',
        operator: FilterOperator.EQUALS,
        value: rarity,
      });
    }

    const searchQuery: SearchQueryDto = {
      query,
      type: SearchType.MARKETPLACE,
      page: page || 1,
      limit: Math.min(limit || 20, 100),
      sortBy,
      sortOrder,
      includeFacets: includeFacets || false,
      filters: filters.length > 0 ? filters : undefined,
    };

    const result = await this.searchService.search(searchQuery, request.context);
    return result;
  }

  @Get('autocomplete')
  @Authenticated()
  @ApiOperation({ summary: 'Get search autocomplete suggestions' })
  @ApiQuery({ name: 'query', type: String, required: true, description: 'Partial search query' })
  @ApiQuery({ name: 'type', enum: SearchType, required: true, description: 'Search type for autocomplete' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Maximum suggestions (default: 10, max: 20)' })
  @ApiQuery({ name: 'includePopular', type: Boolean, required: false, description: 'Include popular searches' })
  @ApiQuery({ name: 'includeRecent', type: Boolean, required: false, description: 'Include recent searches' })
  @ApiResponse({ status: 200, description: 'Autocomplete suggestions retrieved successfully', type: [SearchSuggestionDto] })
  @ApiResponse({ status: 400, description: 'Invalid autocomplete parameters', type: ErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async autocomplete(
    @Query('query') query: string,
    @Query('type') type: SearchType,
    @Query('limit') limit: number | undefined,
    @Query('includePopular') includePopular: boolean | undefined,
    @Query('includeRecent') includeRecent: boolean | undefined,
    @Req() request: any
  ) {
    const autocompleteQuery: AutocompleteQueryDto = {
      query,
      type,
      limit: Math.min(limit || 10, 20),
      includePopular: includePopular !== false,
      includeRecent: includeRecent !== false,
    };

    const result = await this.searchService.autocomplete(autocompleteQuery, request.context);
    return result;
  }

  @Get('suggestions/:query')
  @Public()
  @ApiOperation({ summary: 'Get search suggestions for a query (public endpoint)' })
  @ApiParam({ name: 'query', description: 'Search query to get suggestions for' })
  @ApiQuery({ name: 'type', enum: SearchType, required: false, description: 'Search type (default: global)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Maximum suggestions (default: 5)' })
  @ApiResponse({ status: 200, description: 'Search suggestions retrieved successfully' })
  async getSearchSuggestions(
    @Param('query') query: string,
    @Query('type') type: SearchType | undefined,
    @Query('limit') limit: number | undefined,
  ) {
    const autocompleteQuery: AutocompleteQueryDto = {
      query,
      type: type || SearchType.GLOBAL,
      limit: Math.min(limit || 5, 10),
      includePopular: true,
      includeRecent: false,
    };

    // Create a minimal context for public endpoint
    const context = {
      userId: 'anonymous',
      correlationId: `search_${Date.now()}`,
    };

    const result = await this.searchService.autocomplete(autocompleteQuery, context);
    return result;
  }

  @Get('popular')
  @Public()
  @ApiOperation({ summary: 'Get popular search queries (public endpoint)' })
  @ApiQuery({ name: 'type', enum: SearchType, required: false, description: 'Search type (default: global)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Maximum results (default: 10)' })
  @ApiResponse({ status: 200, description: 'Popular searches retrieved successfully' })
  async getPopularSearches(
    @Query('type') type: SearchType | undefined,
    @Query('limit') limit: number | undefined,
  ) {
    // TODO: Implement popular searches from analytics
    return {
      success: true,
      data: {
        type: type || SearchType.GLOBAL,
        popularSearches: [
          { query: 'NFT', count: 1250, trend: 'up' },
          { query: 'legendary', count: 890, trend: 'up' },
          { query: 'campaign', count: 675, trend: 'stable' },
          { query: 'rare', count: 543, trend: 'down' },
          { query: 'marketplace', count: 432, trend: 'up' },
        ].slice(0, limit || 10),
        generatedAt: new Date().toISOString(),
      },
    };
  }

  @Get('analytics')
  @Authenticated()
  @ApiOperation({ summary: 'Get search analytics and insights' })
  @ApiQuery({ name: 'timeframe', type: String, required: false, description: 'Analytics timeframe (7d, 30d, 90d)' })
  @ApiQuery({ name: 'type', enum: SearchType, required: false, description: 'Filter by search type' })
  @ApiResponse({ status: 200, description: 'Search analytics retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async getSearchAnalytics(
    @Query('timeframe') timeframe: string | undefined,
    @Query('type') type: SearchType | undefined,
    @Req() request: any
  ) {
    // TODO: Implement search analytics
    return {
      success: true,
      data: {
        timeframe: timeframe || '7d',
        type: type || 'all',
        totalSearches: 12450,
        uniqueQueries: 3420,
        avgResultsPerSearch: 23.7,
        avgExecutionTime: 67,
        topQueries: [
          { query: 'NFT', count: 1250, avgResults: 45 },
          { query: 'legendary', count: 890, avgResults: 12 },
          { query: 'campaign', count: 675, avgResults: 34 },
        ],
        searchTrends: {
          thisWeek: 2340,
          lastWeek: 2180,
          growth: 7.3,
        },
        generatedAt: new Date().toISOString(),
      },
    };
  }
}
