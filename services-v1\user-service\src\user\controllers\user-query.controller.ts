import { <PERSON>, Get, Param, Query, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { UserQueryService, UserQueryOptions } from '../services/user-query.service';
import { UserAnalyticsService } from '../services/user-analytics.service';
import { UserAnalyticsDto } from '../dto/user-analytics.dto';
import { ErrorResponseDto } from '../../common/dto/error-response.dto';

@ApiTags('Users - Query')
@Controller('users')
export class UserQueryController {
  constructor(
    private readonly userQueryService: UserQueryService,
    private readonly userAnalyticsService: UserAnalyticsService,
  ) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User found successfully' })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  async getUserById(@Param('id') id: string, @Req() request: any) {
    const result = await this.userQueryService.getUserById(id, request.context);
    return result;
  }

  @Get()
  @ApiOperation({ summary: 'Get users with pagination' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of users to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of users to skip' })
  @ApiQuery({ name: 'includeInactive', required: false, description: 'Include inactive users' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  async getUsers(@Query() query: any, @Req() request: any) {
    const options: UserQueryOptions = {
      limit: parseInt(query.limit) || 50,
      offset: parseInt(query.offset) || 0,
      includeInactive: query.includeInactive === 'true',
      sortBy: query.sortBy || 'createdAt',
      sortOrder: query.sortOrder || 'desc',
    };
    
    const result = await this.userQueryService.getUsers(options, request.context);
    return result;
  }

  @Get(':id/analytics')
  @ApiOperation({ summary: 'Get comprehensive user analytics' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User analytics retrieved successfully', type: UserAnalyticsDto })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  async getUserAnalytics(@Param('id') id: string, @Req() request: any) {
    const result = await this.userAnalyticsService.getUserAnalytics(id, request.context);
    return result;
  }
}
