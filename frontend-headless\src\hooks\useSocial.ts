import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query'
import { socialService } from '@/services/socialService'
import {
  UserProfile,
  SocialPost,
  Comment,
  Community,
  DirectMessage,
  Conversation,
  SocialFeed,
  SocialAnalytics,
  SocialNotification,
  SocialLeaderboard,
  CreatePostRequest,
  UpdatePostRequest,
  CreateCommentRequest,
  UpdateProfileRequest,
  CreateCommunityRequest,
  SendMessageRequest,
  SocialSearchRequest,
  FeedFilters,
  CommunityCategory,
  LeaderboardType
} from '@/types/social.types'

// ===== USER PROFILE HOOKS =====

export function useCurrentUserProfile() {
  return useQuery({
    queryKey: ['social-profile', 'me'],
    queryFn: () => socialService.getCurrentUserProfile(),
    staleTime: 300000, // 5 minutes
  })
}

export function useUserProfile(userId: string) {
  return useQuery({
    queryKey: ['social-profile', userId],
    queryFn: () => socialService.getUserProfile(userId),
    enabled: !!userId,
    staleTime: 300000,
  })
}

export function useUpdateProfile() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (updates: UpdateProfileRequest) => socialService.updateProfile(updates),
    onSuccess: (updatedProfile) => {
      queryClient.setQueryData(['social-profile', 'me'], updatedProfile)
      queryClient.setQueryData(['social-profile', updatedProfile.id], updatedProfile)
      queryClient.invalidateQueries({ queryKey: ['social-profile'] })
    },
    onError: (error: any) => {
      console.error('Failed to update profile:', error)
    },
  })
}

export function useUploadAvatar() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (file: File) => socialService.uploadAvatar(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['social-profile', 'me'] })
    },
    onError: (error: any) => {
      console.error('Failed to upload avatar:', error)
    },
  })
}

export function useUploadCoverImage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (file: File) => socialService.uploadCoverImage(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['social-profile', 'me'] })
    },
    onError: (error: any) => {
      console.error('Failed to upload cover image:', error)
    },
  })
}

// ===== SOCIAL POSTS HOOKS =====

export function useCreatePost() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (postData: CreatePostRequest) => socialService.createPost(postData),
    onSuccess: (newPost) => {
      queryClient.invalidateQueries({ queryKey: ['social-feed'] })
      queryClient.invalidateQueries({ queryKey: ['user-posts'] })
      queryClient.setQueryData(['social-post', newPost.id], newPost)
    },
    onError: (error: any) => {
      console.error('Failed to create post:', error)
    },
  })
}

export function useSocialPost(postId: string) {
  return useQuery({
    queryKey: ['social-post', postId],
    queryFn: () => socialService.getPost(postId),
    enabled: !!postId,
    staleTime: 60000, // 1 minute
  })
}

export function useUpdatePost() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ postId, updates }: { postId: string; updates: UpdatePostRequest }) =>
      socialService.updatePost(postId, updates),
    onSuccess: (updatedPost, { postId }) => {
      queryClient.setQueryData(['social-post', postId], updatedPost)
      queryClient.invalidateQueries({ queryKey: ['social-feed'] })
      queryClient.invalidateQueries({ queryKey: ['user-posts'] })
    },
    onError: (error: any) => {
      console.error('Failed to update post:', error)
    },
  })
}

export function useDeletePost() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (postId: string) => socialService.deletePost(postId),
    onSuccess: (_, postId) => {
      queryClient.removeQueries({ queryKey: ['social-post', postId] })
      queryClient.invalidateQueries({ queryKey: ['social-feed'] })
      queryClient.invalidateQueries({ queryKey: ['user-posts'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete post:', error)
    },
  })
}

export function useLikePost() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (postId: string) => socialService.likePost(postId),
    onMutate: async (postId) => {
      await queryClient.cancelQueries({ queryKey: ['social-post', postId] })
      
      const previousPost = queryClient.getQueryData(['social-post', postId]) as SocialPost
      
      if (previousPost) {
        queryClient.setQueryData(['social-post', postId], {
          ...previousPost,
          engagement: {
            ...previousPost.engagement,
            likesCount: previousPost.engagement.likesCount + 1,
            isLikedByUser: true
          }
        })
      }
      
      return { previousPost }
    },
    onError: (error, postId, context) => {
      if (context?.previousPost) {
        queryClient.setQueryData(['social-post', postId], context.previousPost)
      }
      console.error('Failed to like post:', error)
    },
    onSettled: (_, __, postId) => {
      queryClient.invalidateQueries({ queryKey: ['social-post', postId] })
    },
  })
}

export function useUnlikePost() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (postId: string) => socialService.unlikePost(postId),
    onMutate: async (postId) => {
      await queryClient.cancelQueries({ queryKey: ['social-post', postId] })
      
      const previousPost = queryClient.getQueryData(['social-post', postId]) as SocialPost
      
      if (previousPost) {
        queryClient.setQueryData(['social-post', postId], {
          ...previousPost,
          engagement: {
            ...previousPost.engagement,
            likesCount: Math.max(0, previousPost.engagement.likesCount - 1),
            isLikedByUser: false
          }
        })
      }
      
      return { previousPost }
    },
    onError: (error, postId, context) => {
      if (context?.previousPost) {
        queryClient.setQueryData(['social-post', postId], context.previousPost)
      }
      console.error('Failed to unlike post:', error)
    },
    onSettled: (_, __, postId) => {
      queryClient.invalidateQueries({ queryKey: ['social-post', postId] })
    },
  })
}

export function useSharePost() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ postId, comment }: { postId: string; comment?: string }) =>
      socialService.sharePost(postId, comment),
    onSuccess: (_, { postId }) => {
      queryClient.invalidateQueries({ queryKey: ['social-post', postId] })
      queryClient.invalidateQueries({ queryKey: ['social-feed'] })
    },
    onError: (error: any) => {
      console.error('Failed to share post:', error)
    },
  })
}

export function useBookmarkPost() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (postId: string) => socialService.bookmarkPost(postId),
    onSuccess: (_, postId) => {
      queryClient.invalidateQueries({ queryKey: ['social-post', postId] })
      queryClient.invalidateQueries({ queryKey: ['bookmarked-posts'] })
    },
    onError: (error: any) => {
      console.error('Failed to bookmark post:', error)
    },
  })
}

export function useUnbookmarkPost() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (postId: string) => socialService.unbookmarkPost(postId),
    onSuccess: (_, postId) => {
      queryClient.invalidateQueries({ queryKey: ['social-post', postId] })
      queryClient.invalidateQueries({ queryKey: ['bookmarked-posts'] })
    },
    onError: (error: any) => {
      console.error('Failed to unbookmark post:', error)
    },
  })
}

// ===== COMMENTS HOOKS =====

export function useCreateComment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ postId, commentData }: { postId: string; commentData: CreateCommentRequest }) =>
      socialService.createComment(postId, commentData),
    onSuccess: (newComment, { postId }) => {
      queryClient.invalidateQueries({ queryKey: ['post-comments', postId] })
      queryClient.invalidateQueries({ queryKey: ['social-post', postId] })
    },
    onError: (error: any) => {
      console.error('Failed to create comment:', error)
    },
  })
}

export function usePostComments(postId: string, limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['post-comments', postId, limit, offset],
    queryFn: () => socialService.getComments(postId, limit, offset),
    enabled: !!postId,
    staleTime: 60000,
  })
}

export function useUpdateComment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ commentId, content }: { commentId: string; content: string }) =>
      socialService.updateComment(commentId, content),
    onSuccess: (updatedComment) => {
      queryClient.invalidateQueries({ queryKey: ['post-comments'] })
    },
    onError: (error: any) => {
      console.error('Failed to update comment:', error)
    },
  })
}

export function useDeleteComment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (commentId: string) => socialService.deleteComment(commentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['post-comments'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete comment:', error)
    },
  })
}

export function useLikeComment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (commentId: string) => socialService.likeComment(commentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['post-comments'] })
    },
    onError: (error: any) => {
      console.error('Failed to like comment:', error)
    },
  })
}

export function useUnlikeComment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (commentId: string) => socialService.unlikeComment(commentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['post-comments'] })
    },
    onError: (error: any) => {
      console.error('Failed to unlike comment:', error)
    },
  })
}

// ===== SOCIAL FEED HOOKS =====

export function useSocialFeed(filters?: FeedFilters) {
  return useInfiniteQuery({
    queryKey: ['social-feed', filters],
    queryFn: ({ pageParam }) => socialService.getFeed(filters, 20, pageParam),
    initialPageParam: undefined,
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    staleTime: 60000,
  })
}

export function useUserPosts(userId: string, limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['user-posts', userId, limit, offset],
    queryFn: () => socialService.getUserPosts(userId, limit, offset),
    enabled: !!userId,
    staleTime: 300000,
  })
}

export function useTrendingPosts(timeframe = 'day', limit = 20) {
  return useQuery({
    queryKey: ['trending-posts', timeframe, limit],
    queryFn: () => socialService.getTrendingPosts(timeframe, limit),
    staleTime: 300000,
  })
}

export function useBookmarkedPosts(limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['bookmarked-posts', limit, offset],
    queryFn: () => socialService.getBookmarkedPosts(limit, offset),
    staleTime: 300000,
  })
}

// ===== FOLLOW SYSTEM HOOKS =====

export function useFollowUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (userId: string) => socialService.followUser(userId),
    onSuccess: (_, userId) => {
      queryClient.invalidateQueries({ queryKey: ['social-profile', userId] })
      queryClient.invalidateQueries({ queryKey: ['followers'] })
      queryClient.invalidateQueries({ queryKey: ['following'] })
    },
    onError: (error: any) => {
      console.error('Failed to follow user:', error)
    },
  })
}

export function useUnfollowUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (userId: string) => socialService.unfollowUser(userId),
    onSuccess: (_, userId) => {
      queryClient.invalidateQueries({ queryKey: ['social-profile', userId] })
      queryClient.invalidateQueries({ queryKey: ['followers'] })
      queryClient.invalidateQueries({ queryKey: ['following'] })
    },
    onError: (error: any) => {
      console.error('Failed to unfollow user:', error)
    },
  })
}

export function useFollowers(userId: string, limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['followers', userId, limit, offset],
    queryFn: () => socialService.getFollowers(userId, limit, offset),
    enabled: !!userId,
    staleTime: 300000,
  })
}

export function useFollowing(userId: string, limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['following', userId, limit, offset],
    queryFn: () => socialService.getFollowing(userId, limit, offset),
    enabled: !!userId,
    staleTime: 300000,
  })
}

export function useFollowRequests(limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['follow-requests', limit, offset],
    queryFn: () => socialService.getFollowRequests(limit, offset),
    staleTime: 60000,
  })
}

export function useAcceptFollowRequest() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (requestId: string) => socialService.acceptFollowRequest(requestId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['follow-requests'] })
      queryClient.invalidateQueries({ queryKey: ['followers'] })
    },
    onError: (error: any) => {
      console.error('Failed to accept follow request:', error)
    },
  })
}

export function useRejectFollowRequest() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (requestId: string) => socialService.rejectFollowRequest(requestId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['follow-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to reject follow request:', error)
    },
  })
}

// ===== DIRECT MESSAGES HOOKS =====

export function useConversations(limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['conversations', limit, offset],
    queryFn: () => socialService.getConversations(limit, offset),
    staleTime: 60000,
    refetchInterval: 30000, // Refetch every 30 seconds for new messages
  })
}

export function useConversation(conversationId: string) {
  return useQuery({
    queryKey: ['conversation', conversationId],
    queryFn: () => socialService.getConversation(conversationId),
    enabled: !!conversationId,
    staleTime: 60000,
  })
}

export function useMessages(conversationId: string, limit = 50, offset = 0) {
  return useQuery({
    queryKey: ['messages', conversationId, limit, offset],
    queryFn: () => socialService.getMessages(conversationId, limit, offset),
    enabled: !!conversationId,
    staleTime: 30000,
    refetchInterval: 10000, // Refetch every 10 seconds for new messages
  })
}

export function useSendMessage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (messageData: SendMessageRequest) => socialService.sendMessage(messageData),
    onSuccess: (newMessage) => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] })
      queryClient.invalidateQueries({ queryKey: ['messages', newMessage.conversationId] })
    },
    onError: (error: any) => {
      console.error('Failed to send message:', error)
    },
  })
}

export function useMarkMessageAsRead() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (messageId: string) => socialService.markMessageAsRead(messageId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] })
      queryClient.invalidateQueries({ queryKey: ['messages'] })
    },
    onError: (error: any) => {
      console.error('Failed to mark message as read:', error)
    },
  })
}

export function useMarkConversationAsRead() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (conversationId: string) => socialService.markConversationAsRead(conversationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] })
    },
    onError: (error: any) => {
      console.error('Failed to mark conversation as read:', error)
    },
  })
}

// ===== COMMUNITIES HOOKS =====

export function useCommunities(category?: CommunityCategory, limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['communities', category, limit, offset],
    queryFn: () => socialService.getCommunities(category, limit, offset),
    staleTime: 300000,
  })
}

export function useCommunity(communityId: string) {
  return useQuery({
    queryKey: ['community', communityId],
    queryFn: () => socialService.getCommunity(communityId),
    enabled: !!communityId,
    staleTime: 300000,
  })
}

export function useCreateCommunity() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (communityData: CreateCommunityRequest) => socialService.createCommunity(communityData),
    onSuccess: (newCommunity) => {
      queryClient.invalidateQueries({ queryKey: ['communities'] })
      queryClient.setQueryData(['community', newCommunity.id], newCommunity)
    },
    onError: (error: any) => {
      console.error('Failed to create community:', error)
    },
  })
}

export function useJoinCommunity() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (communityId: string) => socialService.joinCommunity(communityId),
    onSuccess: (_, communityId) => {
      queryClient.invalidateQueries({ queryKey: ['community', communityId] })
      queryClient.invalidateQueries({ queryKey: ['user-communities'] })
    },
    onError: (error: any) => {
      console.error('Failed to join community:', error)
    },
  })
}

export function useLeaveCommunity() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (communityId: string) => socialService.leaveCommunity(communityId),
    onSuccess: (_, communityId) => {
      queryClient.invalidateQueries({ queryKey: ['community', communityId] })
      queryClient.invalidateQueries({ queryKey: ['user-communities'] })
    },
    onError: (error: any) => {
      console.error('Failed to leave community:', error)
    },
  })
}

export function useCommunityPosts(communityId: string, limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['community-posts', communityId, limit, offset],
    queryFn: () => socialService.getCommunityPosts(communityId, limit, offset),
    enabled: !!communityId,
    staleTime: 300000,
  })
}

export function useUserCommunities(userId?: string, limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['user-communities', userId, limit, offset],
    queryFn: () => socialService.getUserCommunities(userId, limit, offset),
    staleTime: 300000,
  })
}

// ===== SEARCH HOOKS =====

export function useSocialSearch(searchRequest: SocialSearchRequest) {
  return useQuery({
    queryKey: ['social-search', searchRequest],
    queryFn: () => socialService.search(searchRequest),
    enabled: !!searchRequest.query && searchRequest.query.length > 2,
    staleTime: 300000,
  })
}

export function useSuggestedUsers(limit = 10) {
  return useQuery({
    queryKey: ['suggested-users', limit],
    queryFn: () => socialService.getSuggestedUsers(limit),
    staleTime: 600000, // 10 minutes
  })
}

export function useSuggestedCommunities(limit = 10) {
  return useQuery({
    queryKey: ['suggested-communities', limit],
    queryFn: () => socialService.getSuggestedCommunities(limit),
    staleTime: 600000,
  })
}

// ===== ANALYTICS HOOKS =====

export function useSocialAnalytics(timeframe = '30d') {
  return useQuery({
    queryKey: ['social-analytics', timeframe],
    queryFn: () => socialService.getSocialAnalytics(timeframe),
    staleTime: 300000,
  })
}

export function useUserAnalytics(userId: string, timeframe = '30d') {
  return useQuery({
    queryKey: ['user-analytics', userId, timeframe],
    queryFn: () => socialService.getUserAnalytics(userId, timeframe),
    enabled: !!userId,
    staleTime: 300000,
  })
}

// ===== LEADERBOARD HOOKS =====

export function useLeaderboard(type: LeaderboardType, timeframe = 'weekly') {
  return useQuery({
    queryKey: ['leaderboard', type, timeframe],
    queryFn: () => socialService.getLeaderboard(type, timeframe),
    staleTime: 300000,
  })
}

// ===== NOTIFICATIONS HOOKS =====

export function useSocialNotifications(limit = 20, offset = 0) {
  return useQuery({
    queryKey: ['social-notifications', limit, offset],
    queryFn: () => socialService.getNotifications(limit, offset),
    staleTime: 60000,
    refetchInterval: 30000, // Refetch every 30 seconds for new notifications
  })
}

export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (notificationId: string) => socialService.markNotificationAsRead(notificationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['social-notifications'] })
    },
    onError: (error: any) => {
      console.error('Failed to mark notification as read:', error)
    },
  })
}

export function useMarkAllNotificationsAsRead() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => socialService.markAllNotificationsAsRead(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['social-notifications'] })
    },
    onError: (error: any) => {
      console.error('Failed to mark all notifications as read:', error)
    },
  })
}

export function useDeleteNotification() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (notificationId: string) => socialService.deleteNotification(notificationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['social-notifications'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete notification:', error)
    },
  })
}
