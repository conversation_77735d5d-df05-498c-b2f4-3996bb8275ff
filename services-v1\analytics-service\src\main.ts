import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { AppConfig } from './config/app.config';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    const app = await NestFactory.create(AppModule);
    
    // Get configuration
    const config = app.get(AppConfig);
    
    // Enable CORS
    app.enableCors({
      origin: config.cors.origin,
      credentials: config.cors.credentials,
    });
    
    // Global validation pipe
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));
    
    // API prefix
    app.setGlobalPrefix('api');
    
    // Swagger documentation
    const swaggerConfig = new DocumentBuilder()
      .setTitle('Analytics Service')
      .setDescription('Platform Analytics & Insights API')
      .setVersion('1.0')
      .addTag('analytics', 'Analytics operations')
      .addTag('events', 'Event tracking operations')
      .addTag('performance', 'Performance monitoring operations')
      .addTag('behavior', 'User behavior analysis operations')
      .addTag('health', 'Health check operations')
      .build();
    
    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('api/docs', app, document);
    
    // Start server
    await app.listen(config.port);
    
    logger.log(`🚀 Analytics Service is running on: http://localhost:${config.port}`);
    logger.log(`📚 API Documentation: http://localhost:${config.port}/api/docs`);
    logger.log(`🏥 Health Check: http://localhost:${config.port}/api/health`);
    logger.log(`🌍 Environment: ${config.nodeEnv}`);
    logger.log(`📊 Analytics Features: Event tracking, Performance monitoring, User behavior analysis`);
    
  } catch (error) {
    logger.error('❌ Failed to start Analytics Service', error);
    process.exit(1);
  }
}

bootstrap();
