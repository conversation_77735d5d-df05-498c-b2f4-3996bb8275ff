import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CampaignMetricsDto {
  @ApiProperty({
    description: 'Campaign ID',
    example: 'campaign_123',
  })
  campaignId: string;

  @ApiProperty({
    description: 'Campaign name',
    example: 'Summer NFT Collection Launch',
  })
  campaignName: string;

  @ApiProperty({
    description: 'Total participants',
    example: 1250,
  })
  totalParticipants: number;

  @ApiProperty({
    description: 'Active participants',
    example: 980,
  })
  activeParticipants: number;

  @ApiProperty({
    description: 'Completed participants',
    example: 450,
  })
  completedParticipants: number;

  @ApiProperty({
    description: 'Completion rate percentage',
    example: 36.0,
  })
  completionRate: number;

  @ApiProperty({
    description: 'Average engagement score',
    example: 78.5,
  })
  averageEngagementScore: number;

  @ApiProperty({
    description: 'Total points distributed',
    example: 125000,
  })
  totalPointsDistributed: number;

  @ApiProperty({
    description: 'Total rewards claimed',
    example: 320,
  })
  totalRewardsClaimed: number;

  @ApiProperty({
    description: 'Total rewards available',
    example: 1000,
  })
  totalRewardsAvailable: number;

  @ApiProperty({
    description: 'Reward claim rate percentage',
    example: 32.0,
  })
  rewardClaimRate: number;

  @ApiProperty({
    description: 'Campaign start date',
    example: '2025-06-10T00:00:00Z',
  })
  startDate: string;

  @ApiProperty({
    description: 'Campaign end date',
    example: '2025-06-30T23:59:59Z',
  })
  endDate: string;

  @ApiProperty({
    description: 'Days remaining in campaign',
    example: 15,
  })
  daysRemaining: number;

  @ApiProperty({
    description: 'Campaign status',
    example: 'active',
  })
  status: string;
}

export class ParticipationTrendDto {
  @ApiProperty({
    description: 'Date',
    example: '2025-06-15',
  })
  date: string;

  @ApiProperty({
    description: 'New participants on this date',
    example: 45,
  })
  newParticipants: number;

  @ApiProperty({
    description: 'Cumulative participants',
    example: 1250,
  })
  cumulativeParticipants: number;

  @ApiProperty({
    description: 'Completions on this date',
    example: 12,
  })
  completions: number;

  @ApiProperty({
    description: 'Cumulative completions',
    example: 450,
  })
  cumulativeCompletions: number;
}

export class RequirementAnalyticsDto {
  @ApiProperty({
    description: 'Requirement type',
    example: 'twitter_retweet',
  })
  requirementType: string;

  @ApiProperty({
    description: 'Total submissions',
    example: 890,
  })
  totalSubmissions: number;

  @ApiProperty({
    description: 'Approved submissions',
    example: 750,
  })
  approvedSubmissions: number;

  @ApiProperty({
    description: 'Rejected submissions',
    example: 85,
  })
  rejectedSubmissions: number;

  @ApiProperty({
    description: 'Pending submissions',
    example: 55,
  })
  pendingSubmissions: number;

  @ApiProperty({
    description: 'Approval rate percentage',
    example: 84.3,
  })
  approvalRate: number;

  @ApiProperty({
    description: 'Average points awarded',
    example: 8.5,
  })
  averagePointsAwarded: number;

  @ApiProperty({
    description: 'Average completion time in hours',
    example: 24.5,
  })
  averageCompletionTime: number;
}

export class RewardAnalyticsDto {
  @ApiProperty({
    description: 'Reward type',
    example: 'nft_generation',
  })
  rewardType: string;

  @ApiProperty({
    description: 'Reward name',
    example: 'Exclusive Campaign NFT',
  })
  rewardName: string;

  @ApiProperty({
    description: 'Total available',
    example: 1000,
  })
  totalAvailable: number;

  @ApiProperty({
    description: 'Total earned',
    example: 450,
  })
  totalEarned: number;

  @ApiProperty({
    description: 'Total claimed',
    example: 320,
  })
  totalClaimed: number;

  @ApiProperty({
    description: 'Claim rate percentage',
    example: 71.1,
  })
  claimRate: number;

  @ApiProperty({
    description: 'Average time to claim in hours',
    example: 48.2,
  })
  averageTimeToClaim: number;

  @ApiPropertyOptional({
    description: 'Reward rarity',
    example: 'rare',
  })
  rarity?: string;

  @ApiPropertyOptional({
    description: 'Reward value',
    example: 100,
  })
  value?: number;
}

export class TopParticipantDto {
  @ApiProperty({
    description: 'User ID',
    example: 'user_123',
  })
  userId: string;

  @ApiProperty({
    description: 'Username',
    example: 'cryptoenthusiast',
  })
  username: string;

  @ApiPropertyOptional({
    description: 'Display name',
    example: 'Crypto Enthusiast',
  })
  displayName?: string;

  @ApiProperty({
    description: 'Total points earned',
    example: 250,
  })
  totalPoints: number;

  @ApiProperty({
    description: 'Requirements completed',
    example: 8,
  })
  requirementsCompleted: number;

  @ApiProperty({
    description: 'Rewards earned',
    example: 3,
  })
  rewardsEarned: number;

  @ApiProperty({
    description: 'Participation rank',
    example: 1,
  })
  rank: number;

  @ApiProperty({
    description: 'Completion percentage',
    example: 100.0,
  })
  completionPercentage: number;

  @ApiProperty({
    description: 'Join date',
    example: '2025-06-10T08:30:00Z',
  })
  joinedAt: string;
}

export class CampaignAnalyticsDto {
  @ApiProperty({
    description: 'Campaign metrics overview',
    type: CampaignMetricsDto,
  })
  metrics: CampaignMetricsDto;

  @ApiProperty({
    description: 'Participation trends over time',
    type: [ParticipationTrendDto],
  })
  participationTrends: ParticipationTrendDto[];

  @ApiProperty({
    description: 'Requirement completion analytics',
    type: [RequirementAnalyticsDto],
  })
  requirementAnalytics: RequirementAnalyticsDto[];

  @ApiProperty({
    description: 'Reward distribution analytics',
    type: [RewardAnalyticsDto],
  })
  rewardAnalytics: RewardAnalyticsDto[];

  @ApiProperty({
    description: 'Top performing participants',
    type: [TopParticipantDto],
  })
  topParticipants: TopParticipantDto[];

  @ApiProperty({
    description: 'Analytics generation timestamp',
    example: '2025-06-03T20:30:00Z',
  })
  generatedAt: string;
}

export class CampaignPerformanceDto {
  @ApiProperty({
    description: 'Campaign ID',
    example: 'campaign_123',
  })
  campaignId: string;

  @ApiProperty({
    description: 'Performance score (0-100)',
    example: 85.5,
  })
  performanceScore: number;

  @ApiProperty({
    description: 'Engagement rate',
    example: 78.2,
  })
  engagementRate: number;

  @ApiProperty({
    description: 'Conversion rate (participants to completions)',
    example: 36.0,
  })
  conversionRate: number;

  @ApiProperty({
    description: 'Retention rate',
    example: 82.5,
  })
  retentionRate: number;

  @ApiProperty({
    description: 'Social reach estimate',
    example: 125000,
  })
  socialReach: number;

  @ApiProperty({
    description: 'Social impressions estimate',
    example: 450000,
  })
  socialImpressions: number;

  @ApiProperty({
    description: 'Cost per participant',
    example: 2.50,
  })
  costPerParticipant: number;

  @ApiProperty({
    description: 'Return on investment percentage',
    example: 340.0,
  })
  roi: number;

  @ApiProperty({
    description: 'Performance benchmarks comparison',
    example: {
      industryAverage: 65.0,
      platformAverage: 72.5,
      previousCampaigns: 78.0,
    },
  })
  benchmarks: Record<string, number>;

  @ApiProperty({
    description: 'Key performance insights',
    example: [
      'High engagement on Twitter requirements',
      'Lower completion rate for content creation tasks',
      'Strong reward claim rate indicates good incentive alignment',
    ],
  })
  insights: string[];

  @ApiProperty({
    description: 'Recommended optimizations',
    example: [
      'Simplify content creation requirements',
      'Increase reward visibility',
      'Add mid-campaign engagement boosters',
    ],
  })
  recommendations: string[];
}
