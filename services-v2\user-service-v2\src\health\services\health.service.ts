/**
 * Health Service - User Service V2
 * 
 * Health check business logic using shared infrastructure
 */

import { Injectable } from '@nestjs/common';
import {
  StandardizedPrismaService,
  ServiceLoggerService,
  StandardizedConfigService,
} from '../../../../../shared';

@Injectable()
export class HealthService {
  constructor(
    private readonly prisma: StandardizedPrismaService,
    private readonly logger: ServiceLoggerService,
    private readonly config: StandardizedConfigService,
  ) {}

  async getSimpleHealth() {
    const serviceConfig = this.config.getServiceConfig();
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      service: serviceConfig.serviceName,
      version: serviceConfig.version,
      environment: serviceConfig.nodeEnv,
    };
  }

  async getDetailedHealth() {
    const serviceConfig = this.config.getServiceConfig();
    const startTime = Date.now();

    try {
      // Test database connection
      const dbHealth = await this.prisma.healthCheck();
      
      // Get query performance metrics
      const queryMetrics = this.prisma.getQueryPerformanceSummary();
      
      // Get memory usage
      const memoryUsage = process.memoryUsage();
      
      const responseTime = Date.now() - startTime;

      const health = {
        status: dbHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        service: serviceConfig.serviceName,
        version: serviceConfig.version,
        environment: serviceConfig.nodeEnv,
        responseTime,
        dependencies: [
          {
            name: 'database',
            status: dbHealth.status,
            responseTime: dbHealth.details.responseTime,
            details: {
              connected: dbHealth.details.connected,
              queryMetrics: dbHealth.details.queryMetrics,
            },
          },
        ],
        metrics: {
          memoryUsage: {
            used: memoryUsage.heapUsed,
            total: memoryUsage.heapTotal,
            percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
          },
          database: queryMetrics,
        },
      };

      // Log health check
      this.logger.logPerformanceMetric(
        'health-check',
        responseTime,
        health.status === 'healthy',
        {
          metadata: {
            dependencies: health.dependencies.length,
            memoryUsagePercent: health.metrics.memoryUsage.percentage,
          },
        },
      );

      return health;
    } catch (error) {
      this.logger.error('Health check failed', error);
      
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        service: serviceConfig.serviceName,
        version: serviceConfig.version,
        environment: serviceConfig.nodeEnv,
        error: error.message,
      };
    }
  }

  async getDatabaseHealth() {
    try {
      const dbHealth = await this.prisma.healthCheck();
      const connectionStatus = this.prisma.getConnectionStatus();
      
      return {
        status: dbHealth.status,
        details: {
          ...dbHealth.details,
          connectionStatus,
        },
      };
    } catch (error) {
      this.logger.error('Database health check failed', error);
      
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  async getReadiness() {
    try {
      // Check if service is ready to accept requests
      const dbHealth = await this.prisma.healthCheck();
      
      const isReady = dbHealth.status === 'healthy';
      
      return {
        status: isReady ? 'ready' : 'not-ready',
        timestamp: new Date().toISOString(),
        checks: {
          database: dbHealth.status === 'healthy',
        },
      };
    } catch (error) {
      return {
        status: 'not-ready',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  async getLiveness() {
    // Simple liveness check - service is alive if it can respond
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }
}
