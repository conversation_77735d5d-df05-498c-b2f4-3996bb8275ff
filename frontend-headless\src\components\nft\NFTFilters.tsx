'use client'

import React, { useState } from 'react'
import { NFTFilters, NFTRarity, NFTStatus, BlockchainNetwork } from '@/types/nft.types'
import {
  FunnelIcon,
  XMarkIcon,
  ChevronDownIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

interface NFTFiltersProps {
  filters: NFTFilters
  onFiltersChange: (filters: NFTFilters) => void
  availableRarities: NFTRarity[]
  availableStatuses: NFTStatus[]
  availableBlockchains: BlockchainNetwork[]
  showAdvanced?: boolean
  className?: string
}

export default function NFTFiltersComponent({
  filters,
  onFiltersChange,
  availableRarities,
  availableStatuses,
  availableBlockchains,
  showAdvanced = false,
  className = ''
}: NFTFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(showAdvanced)
  const [searchQuery, setSearchQuery] = useState(filters.searchQuery || '')

  const updateFilter = (key: keyof NFTFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const toggleArrayFilter = (key: keyof NFTFilters, value: string) => {
    const currentArray = (filters[key] as string[]) || []
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    
    updateFilter(key, newArray.length > 0 ? newArray : undefined)
  }

  const clearFilters = () => {
    onFiltersChange({})
    setSearchQuery('')
  }

  const hasActiveFilters = () => {
    return Object.keys(filters).some(key => {
      const value = filters[key as keyof NFTFilters]
      return Array.isArray(value) ? value.length > 0 : value !== undefined && value !== ''
    })
  }

  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    updateFilter('searchQuery', value || undefined)
  }

  return (
    <div className={`bg-gray-50 border-b border-gray-200 ${className}`}>
      <div className="px-6 py-4">
        {/* Search Bar */}
        <div className="flex items-center space-x-4 mb-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search NFTs by name, handle, or description..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
            <ChevronDownIcon className={`h-4 w-4 ml-2 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
          </button>

          {hasActiveFilters() && (
            <button
              onClick={clearFilters}
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-800"
            >
              <XMarkIcon className="h-4 w-4 mr-1" />
              Clear
            </button>
          )}
        </div>

        {/* Advanced Filters */}
        {isExpanded && (
          <div className="space-y-4">
            {/* Rarity Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Rarity</label>
              <div className="flex flex-wrap gap-2">
                {availableRarities.map((rarity) => (
                  <button
                    key={rarity}
                    onClick={() => toggleArrayFilter('rarity', rarity)}
                    className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                      filters.rarity?.includes(rarity)
                        ? 'bg-blue-100 text-blue-800 border-blue-200'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {rarity.charAt(0).toUpperCase() + rarity.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <div className="flex flex-wrap gap-2">
                {availableStatuses.map((status) => (
                  <button
                    key={status}
                    onClick={() => toggleArrayFilter('status', status)}
                    className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                      filters.status?.includes(status)
                        ? 'bg-green-100 text-green-800 border-green-200'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Blockchain Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Blockchain</label>
              <div className="flex flex-wrap gap-2">
                {availableBlockchains.map((blockchain) => (
                  <button
                    key={blockchain}
                    onClick={() => toggleArrayFilter('blockchain', blockchain)}
                    className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                      filters.blockchain?.includes(blockchain)
                        ? 'bg-purple-100 text-purple-800 border-purple-200'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {blockchain.charAt(0).toUpperCase() + blockchain.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Score Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Score Range</label>
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <input
                    type="number"
                    placeholder="Min"
                    min="0"
                    max="100"
                    value={filters.scoreRange?.min || ''}
                    onChange={(e) => updateFilter('scoreRange', {
                      ...filters.scoreRange,
                      min: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <span className="text-gray-500">to</span>
                <div className="flex-1">
                  <input
                    type="number"
                    placeholder="Max"
                    min="0"
                    max="100"
                    value={filters.scoreRange?.max || ''}
                    onChange={(e) => updateFilter('scoreRange', {
                      ...filters.scoreRange,
                      max: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Boolean Filters */}
            <div className="flex flex-wrap gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.isMinted === true}
                  onChange={(e) => updateFilter('isMinted', e.target.checked ? true : undefined)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Minted Only</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.isListed === true}
                  onChange={(e) => updateFilter('isListed', e.target.checked ? true : undefined)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Listed for Sale</span>
              </label>
            </div>

            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Creation Date</label>
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <input
                    type="date"
                    value={filters.dateRange?.start || ''}
                    onChange={(e) => updateFilter('dateRange', {
                      ...filters.dateRange,
                      start: e.target.value || undefined
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <span className="text-gray-500">to</span>
                <div className="flex-1">
                  <input
                    type="date"
                    value={filters.dateRange?.end || ''}
                    onChange={(e) => updateFilter('dateRange', {
                      ...filters.dateRange,
                      end: e.target.value || undefined
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters Summary */}
        {hasActiveFilters() && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-2">
                {filters.rarity?.map((rarity) => (
                  <span
                    key={rarity}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {rarity}
                    <button
                      onClick={() => toggleArrayFilter('rarity', rarity)}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </span>
                ))}
                {filters.status?.map((status) => (
                  <span
                    key={status}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    {status}
                    <button
                      onClick={() => toggleArrayFilter('status', status)}
                      className="ml-1 text-green-600 hover:text-green-800"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </span>
                ))}
                {filters.blockchain?.map((blockchain) => (
                  <span
                    key={blockchain}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                  >
                    {blockchain}
                    <button
                      onClick={() => toggleArrayFilter('blockchain', blockchain)}
                      className="ml-1 text-purple-600 hover:text-purple-800"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
              <span className="text-xs text-gray-500">
                {Object.keys(filters).filter(key => {
                  const value = filters[key as keyof NFTFilters]
                  return Array.isArray(value) ? value.length > 0 : value !== undefined && value !== ''
                }).length} active filters
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
