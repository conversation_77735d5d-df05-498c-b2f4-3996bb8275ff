import { Injectable, Logger } from '@nestjs/common';

export interface InfluenceMetrics {
  overallInfluenceScore: number;
  reachScore: number;
  authorityScore: number;
  networkEffectScore: number;
  thoughtLeadershipScore: number;
  communityInfluenceScore: number;
  contentAmplificationScore: number;
  crossPlatformInfluence: number;
  influenceGrowthTrend: 'rapid' | 'steady' | 'slow' | 'declining';
  influenceCategory: 'micro' | 'macro' | 'mega' | 'celebrity';
  influenceNiche: string[];
  kloutScore: number; // Klout-style influence score
}

export interface TwitterProfileData {
  id: string;
  username: string;
  displayName: string;
  description?: string;
  followersCount: number;
  followingCount: number;
  tweetsCount: number;
  listedCount: number;
  verified: boolean;
  profileImageUrl?: string;
  location?: string;
  website?: string;
  createdAt: string;
  // Enhanced data for influence analysis
  recentTweets?: any[];
  mentions?: any[];
  retweets?: any[];
  followerQuality?: any;
  networkConnections?: any[];
}

@Injectable()
export class InfluenceMetricsService {
  private readonly logger = new Logger(InfluenceMetricsService.name);

  constructor() {}

  /**
   * Calculate comprehensive influence metrics
   */
  async calculateInfluenceMetrics(profileData: TwitterProfileData): Promise<InfluenceMetrics> {
    try {
      // Calculate core influence components
      const reachScore = this.calculateReachScore(profileData);
      const authorityScore = this.calculateAuthorityScore(profileData);
      const networkEffectScore = this.calculateNetworkEffectScore(profileData);
      const thoughtLeadershipScore = this.calculateThoughtLeadershipScore(profileData);
      const communityInfluenceScore = this.calculateCommunityInfluenceScore(profileData);
      const contentAmplificationScore = this.calculateContentAmplificationScore(profileData);
      const crossPlatformInfluence = this.estimateCrossPlatformInfluence(profileData);

      // Calculate overall influence score (weighted average)
      const overallInfluenceScore = this.calculateOverallInfluenceScore({
        reachScore,
        authorityScore,
        networkEffectScore,
        thoughtLeadershipScore,
        communityInfluenceScore,
        contentAmplificationScore,
        crossPlatformInfluence,
      });

      // Determine influence characteristics
      const influenceGrowthTrend = this.determineInfluenceGrowthTrend(profileData);
      const influenceCategory = this.categorizeInfluence(profileData, overallInfluenceScore);
      const influenceNiche = this.identifyInfluenceNiche(profileData);
      const kloutScore = this.calculateKloutStyleScore(profileData, overallInfluenceScore);

      const metrics: InfluenceMetrics = {
        overallInfluenceScore,
        reachScore,
        authorityScore,
        networkEffectScore,
        thoughtLeadershipScore,
        communityInfluenceScore,
        contentAmplificationScore,
        crossPlatformInfluence,
        influenceGrowthTrend,
        influenceCategory,
        influenceNiche,
        kloutScore,
      };

      return metrics;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Calculate reach score based on follower count and potential audience
   */
  private calculateReachScore(profileData: TwitterProfileData): number {
    let reachScore = 0;

    // Base score from follower count (logarithmic scale)
    if (profileData.followersCount > 0) {
      reachScore = Math.min(80, Math.log10(profileData.followersCount) * 20);
    }

    // Bonus for being listed (indicates reach beyond direct followers)
    if (profileData.listedCount > 0) {
      const listBonus = Math.min(15, Math.log10(profileData.listedCount + 1) * 5);
      reachScore += listBonus;
    }

    // Verified accounts get reach bonus
    if (profileData.verified) {
      reachScore += 5;
    }

    return Math.min(100, Math.max(0, reachScore));
  }

  /**
   * Calculate authority score based on verification, follower quality, and mentions
   */
  private calculateAuthorityScore(profileData: TwitterProfileData): number {
    let authorityScore = 30; // Base authority

    // Verification is a strong authority signal
    if (profileData.verified) {
      authorityScore += 40;
    }

    // Follower-to-following ratio indicates authority
    const followerRatio = profileData.followingCount > 0 
      ? profileData.followersCount / profileData.followingCount 
      : profileData.followersCount;

    if (followerRatio > 10) authorityScore += 20;
    else if (followerRatio > 5) authorityScore += 15;
    else if (followerRatio > 2) authorityScore += 10;
    else if (followerRatio > 1) authorityScore += 5;

    // Being listed indicates authority
    if (profileData.listedCount > 100) authorityScore += 15;
    else if (profileData.listedCount > 50) authorityScore += 10;
    else if (profileData.listedCount > 10) authorityScore += 5;

    // Account age contributes to authority
    const accountAge = this.calculateAccountAge(profileData.createdAt);
    if (accountAge > 1825) authorityScore += 10; // 5+ years
    else if (accountAge > 1095) authorityScore += 7; // 3+ years
    else if (accountAge > 365) authorityScore += 5; // 1+ year

    // Complete profile indicates authority
    if (profileData.description && profileData.website && profileData.location) {
      authorityScore += 5;
    }

    return Math.min(100, Math.max(0, authorityScore));
  }

  /**
   * Calculate network effect score based on retweets and mentions
   */
  private calculateNetworkEffectScore(profileData: TwitterProfileData): number {
    let networkScore = 20; // Base network effect

    // Estimate network effect from retweets and mentions
    if (profileData.recentTweets && profileData.recentTweets.length > 0) {
      const avgRetweets = profileData.recentTweets.reduce((sum, tweet) => 
        sum + (tweet.retweets || 0), 0) / profileData.recentTweets.length;
      
      const retweetScore = Math.min(30, Math.log10(avgRetweets + 1) * 10);
      networkScore += retweetScore;

      // Mentions indicate network connections
      const avgMentions = profileData.recentTweets.reduce((sum, tweet) => 
        sum + (tweet.mentions ? tweet.mentions.length : 0), 0) / profileData.recentTweets.length;
      
      const mentionScore = Math.min(20, avgMentions * 5);
      networkScore += mentionScore;
    } else {
      // Estimate based on follower count and activity
      const estimatedNetworkEffect = this.estimateNetworkEffect(profileData);
      networkScore += estimatedNetworkEffect;
    }

    // Listed count indicates network reach
    if (profileData.listedCount > 0) {
      const listNetworkScore = Math.min(15, Math.log10(profileData.listedCount + 1) * 5);
      networkScore += listNetworkScore;
    }

    // Verified accounts typically have stronger network effects
    if (profileData.verified) {
      networkScore += 15;
    }

    return Math.min(100, Math.max(0, networkScore));
  }

  /**
   * Calculate thought leadership score based on content quality and engagement
   */
  private calculateThoughtLeadershipScore(profileData: TwitterProfileData): number {
    let thoughtLeadershipScore = 25; // Base score

    // Tweet frequency indicates thought leadership activity
    const accountAge = this.calculateAccountAge(profileData.createdAt);
    const tweetsPerDay = profileData.tweetsCount / Math.max(1, accountAge);

    if (tweetsPerDay >= 1 && tweetsPerDay <= 10) {
      thoughtLeadershipScore += 20; // Optimal posting frequency
    } else if (tweetsPerDay >= 0.5 && tweetsPerDay < 1) {
      thoughtLeadershipScore += 15;
    } else if (tweetsPerDay > 10) {
      thoughtLeadershipScore += 5; // Too frequent might indicate less thoughtful content
    }

    // Bio indicates thought leadership positioning
    if (profileData.description) {
      const bioKeywords = this.extractThoughtLeadershipKeywords(profileData.description);
      thoughtLeadershipScore += bioKeywords.length * 3;
    }

    // Website indicates professional presence
    if (profileData.website) {
      thoughtLeadershipScore += 10;
    }

    // Verified accounts often are thought leaders
    if (profileData.verified) {
      thoughtLeadershipScore += 15;
    }

    // High follower count with reasonable following indicates thought leadership
    if (profileData.followersCount > 5000 && profileData.followingCount < profileData.followersCount * 0.5) {
      thoughtLeadershipScore += 15;
    }

    return Math.min(100, Math.max(0, thoughtLeadershipScore));
  }

  /**
   * Calculate community influence score
   */
  private calculateCommunityInfluenceScore(profileData: TwitterProfileData): number {
    let communityScore = 30; // Base community influence

    // Replies and mentions indicate community engagement
    if (profileData.recentTweets && profileData.recentTweets.length > 0) {
      const avgReplies = profileData.recentTweets.reduce((sum, tweet) => 
        sum + (tweet.replies || 0), 0) / profileData.recentTweets.length;
      
      const replyScore = Math.min(25, avgReplies * 2);
      communityScore += replyScore;
    }

    // Listed count indicates community recognition
    if (profileData.listedCount > 0) {
      const communityListScore = Math.min(20, Math.log10(profileData.listedCount + 1) * 7);
      communityScore += communityListScore;
    }

    // Follower engagement ratio
    const engagementRatio = profileData.followersCount > 0 
      ? Math.min(1, profileData.tweetsCount / profileData.followersCount)
      : 0;
    
    communityScore += engagementRatio * 15;

    // Account completeness indicates community commitment
    let completenessScore = 0;
    if (profileData.description) completenessScore += 3;
    if (profileData.profileImageUrl) completenessScore += 3;
    if (profileData.location) completenessScore += 2;
    if (profileData.website) completenessScore += 2;
    
    communityScore += completenessScore;

    return Math.min(100, Math.max(0, communityScore));
  }

  /**
   * Calculate content amplification score
   */
  private calculateContentAmplificationScore(profileData: TwitterProfileData): number {
    let amplificationScore = 20; // Base amplification

    // Retweet ratio indicates content amplification
    if (profileData.recentTweets && profileData.recentTweets.length > 0) {
      const totalRetweets = profileData.recentTweets.reduce((sum, tweet) => 
        sum + (tweet.retweets || 0), 0);
      const avgRetweets = totalRetweets / profileData.recentTweets.length;
      
      amplificationScore += Math.min(40, Math.log10(avgRetweets + 1) * 15);
    } else {
      // Estimate based on follower count
      const estimatedAmplification = Math.min(30, Math.log10(profileData.followersCount + 1) * 5);
      amplificationScore += estimatedAmplification;
    }

    // Verified accounts typically have higher amplification
    if (profileData.verified) {
      amplificationScore += 20;
    }

    // Large follower base increases amplification potential
    if (profileData.followersCount > 50000) {
      amplificationScore += 15;
    } else if (profileData.followersCount > 10000) {
      amplificationScore += 10;
    }

    // Listed count indicates amplification reach
    if (profileData.listedCount > 0) {
      amplificationScore += Math.min(10, Math.log10(profileData.listedCount + 1) * 3);
    }

    return Math.min(100, Math.max(0, amplificationScore));
  }

  /**
   * Estimate cross-platform influence
   */
  private estimateCrossPlatformInfluence(profileData: TwitterProfileData): number {
    let crossPlatformScore = 30; // Base assumption

    // Website indicates cross-platform presence
    if (profileData.website) {
      crossPlatformScore += 25;
      
      // Check for common platform indicators in website
      const website = profileData.website.toLowerCase();
      if (website.includes('linkedin') || website.includes('instagram') || 
          website.includes('youtube') || website.includes('tiktok')) {
        crossPlatformScore += 15;
      }
    }

    // Bio mentions of other platforms
    if (profileData.description) {
      const bio = profileData.description.toLowerCase();
      let platformMentions = 0;
      
      if (bio.includes('youtube') || bio.includes('yt')) platformMentions++;
      if (bio.includes('instagram') || bio.includes('ig')) platformMentions++;
      if (bio.includes('linkedin')) platformMentions++;
      if (bio.includes('tiktok')) platformMentions++;
      if (bio.includes('podcast')) platformMentions++;
      if (bio.includes('blog')) platformMentions++;
      
      crossPlatformScore += platformMentions * 8;
    }

    // Verified accounts often have cross-platform presence
    if (profileData.verified) {
      crossPlatformScore += 20;
    }

    // Large following suggests cross-platform influence
    if (profileData.followersCount > 100000) {
      crossPlatformScore += 15;
    }

    return Math.min(100, Math.max(0, crossPlatformScore));
  }

  /**
   * Calculate overall influence score with weighted components
   */
  private calculateOverallInfluenceScore(components: {
    reachScore: number;
    authorityScore: number;
    networkEffectScore: number;
    thoughtLeadershipScore: number;
    communityInfluenceScore: number;
    contentAmplificationScore: number;
    crossPlatformInfluence: number;
  }): number {
    const weights = {
      reach: 0.20,
      authority: 0.25,
      networkEffect: 0.15,
      thoughtLeadership: 0.15,
      communityInfluence: 0.10,
      contentAmplification: 0.10,
      crossPlatform: 0.05,
    };

    const weightedScore = 
      (components.reachScore * weights.reach) +
      (components.authorityScore * weights.authority) +
      (components.networkEffectScore * weights.networkEffect) +
      (components.thoughtLeadershipScore * weights.thoughtLeadership) +
      (components.communityInfluenceScore * weights.communityInfluence) +
      (components.contentAmplificationScore * weights.contentAmplification) +
      (components.crossPlatformInfluence * weights.crossPlatform);

    return Math.round(Math.min(100, Math.max(0, weightedScore)));
  }

  /**
   * Determine influence growth trend
   */
  private determineInfluenceGrowthTrend(profileData: TwitterProfileData): 'rapid' | 'steady' | 'slow' | 'declining' {
    const accountAge = this.calculateAccountAge(profileData.createdAt);
    const followersPerDay = profileData.followersCount / Math.max(1, accountAge);

    if (followersPerDay > 50) return 'rapid';
    if (followersPerDay > 10) return 'steady';
    if (followersPerDay > 1) return 'slow';
    return 'declining';
  }

  /**
   * Categorize influence level
   */
  private categorizeInfluence(profileData: TwitterProfileData, influenceScore: number): 'micro' | 'macro' | 'mega' | 'celebrity' {
    if (profileData.followersCount > 1000000 || influenceScore > 90) return 'celebrity';
    if (profileData.followersCount > 100000 || influenceScore > 80) return 'mega';
    if (profileData.followersCount > 10000 || influenceScore > 60) return 'macro';
    return 'micro';
  }

  /**
   * Identify influence niche based on bio and content
   */
  private identifyInfluenceNiche(profileData: TwitterProfileData): string[] {
    const niches: string[] = [];
    
    if (profileData.description) {
      const bio = profileData.description.toLowerCase();
      
      // Technology
      if (bio.includes('tech') || bio.includes('developer') || bio.includes('engineer') || 
          bio.includes('startup') || bio.includes('ai') || bio.includes('blockchain')) {
        niches.push('Technology');
      }
      
      // Business
      if (bio.includes('ceo') || bio.includes('founder') || bio.includes('entrepreneur') || 
          bio.includes('business') || bio.includes('marketing')) {
        niches.push('Business');
      }
      
      // Media/Entertainment
      if (bio.includes('journalist') || bio.includes('writer') || bio.includes('author') || 
          bio.includes('media') || bio.includes('entertainment')) {
        niches.push('Media');
      }
      
      // Finance
      if (bio.includes('finance') || bio.includes('investment') || bio.includes('crypto') || 
          bio.includes('trading') || bio.includes('economist')) {
        niches.push('Finance');
      }
      
      // Politics
      if (bio.includes('politic') || bio.includes('government') || bio.includes('policy') || 
          bio.includes('senator') || bio.includes('congressman')) {
        niches.push('Politics');
      }
      
      // Sports
      if (bio.includes('sport') || bio.includes('athlete') || bio.includes('coach') || 
          bio.includes('fitness') || bio.includes('nfl') || bio.includes('nba')) {
        niches.push('Sports');
      }
    }
    
    return niches.length > 0 ? niches : ['General'];
  }

  /**
   * Calculate Klout-style influence score
   */
  private calculateKloutStyleScore(profileData: TwitterProfileData, overallInfluence: number): number {
    // Klout score typically ranges from 1-100 with different weighting
    let kloutScore = overallInfluence * 0.7; // Base from overall influence
    
    // Add specific Klout-style factors
    const reachFactor = Math.min(20, Math.log10(profileData.followersCount + 1) * 3);
    const amplificationFactor = profileData.listedCount > 0 ? Math.min(15, Math.log10(profileData.listedCount + 1) * 5) : 0;
    const networkFactor = profileData.verified ? 15 : 5;
    
    kloutScore += reachFactor + amplificationFactor + networkFactor;
    
    return Math.round(Math.min(100, Math.max(1, kloutScore)));
  }

  /**
   * Extract thought leadership keywords from bio
   */
  private extractThoughtLeadershipKeywords(bio: string): string[] {
    const keywords = [
      'expert', 'specialist', 'consultant', 'advisor', 'thought leader',
      'speaker', 'author', 'researcher', 'analyst', 'strategist',
      'founder', 'ceo', 'director', 'head of', 'lead', 'principal'
    ];
    
    const bioLower = bio.toLowerCase();
    return keywords.filter(keyword => bioLower.includes(keyword));
  }

  /**
   * Estimate network effect from profile characteristics
   */
  private estimateNetworkEffect(profileData: TwitterProfileData): number {
    let networkEffect = 0;
    
    // Estimate based on follower count and activity
    const followerFactor = Math.min(25, Math.log10(profileData.followersCount + 1) * 5);
    const activityFactor = Math.min(15, profileData.tweetsCount / 1000);
    
    networkEffect = followerFactor + activityFactor;
    
    return Math.min(30, networkEffect);
  }

  /**
   * Calculate account age in days
   */
  private calculateAccountAge(createdAt: string): number {
    return Math.floor((Date.now() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24));
  }
}
