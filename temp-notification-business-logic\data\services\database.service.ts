import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StandardizedPrismaService } from '../../../../../shared/data/services/prisma.service';
import { ServiceLoggerService } from '../../../logging/services/service-logger.service';
import { MetricsService } from '../../../../../shared/logging/services/metrics.service';
import {
  BusinessOutcome,
  SecurityEventType,
  SecuritySeverity,
  SecurityOutcome,
} from '../../../../../shared/logging/interfaces/logger.interface';

@Injectable()
export class DatabaseService implements OnModuleInit {
  private readonly logger = new Logger(DatabaseService.name);
  private readonly serviceName: string;

  constructor(
    private readonly prisma: StandardizedPrismaService,
    private readonly serviceLogger: ServiceLoggerService,
    private readonly metricsService: MetricsService,
    private readonly configService: ConfigService,
  ) {
    this.serviceName = this.configService.get<string>('SERVICE_NAME') || 'unknown-service';
  }

  async onModuleInit() {
    await this.initializeDatabase();
  }

  /**
   * Initialize database connection and perform health checks
   */
  private async initializeDatabase(): Promise<void> {
    try {
      this.serviceLogger.logOperationStart('database_initialization');

      // Perform initial health check
      const healthResult = await this.prisma.healthCheck();
      
      if (healthResult.status === 'healthy') {
        this.serviceLogger.logBusinessEvent(
          'database',
          'initialization',
          BusinessOutcome.SUCCESS,
          {
            responseTime: healthResult.responseTime,
            details: healthResult.details,
          }
        );

        this.logger.log(`✅ Database initialized successfully for ${this.serviceName}`);
      } else {
        throw new Error(`Database health check failed: ${JSON.stringify(healthResult.details)}`);
      }

      this.serviceLogger.logOperationComplete('database_initialization', 0, true);
    } catch (error) {
      this.serviceLogger.logOperationComplete('database_initialization', 0, false, {
        error: error.message,
      });

      this.serviceLogger.logSecurityEvent(
        SecurityEventType.CONFIGURATION_CHANGE,
        SecuritySeverity.HIGH,
        SecurityOutcome.FAILURE,
        {
          details: {
            component: 'database',
            error: error.message,
          },
        }
      );

      this.logger.error(`❌ Database initialization failed for ${this.serviceName}:`, error);
      throw error;
    }
  }

  /**
   * Execute raw SQL query with monitoring
   */
  async executeQuery<T>(query: string, params?: any[]): Promise<T[]> {
    const startTime = Date.now();
    
    try {
      this.serviceLogger.logOperationStart('database_raw_query', {
        metadata: {
          query: query.substring(0, 100), // Truncate for logging
          paramCount: params?.length || 0,
        },
      });

      const result = await this.prisma.queryWithMetrics<T>(query, params);
      const duration = Date.now() - startTime;

      this.serviceLogger.logDatabaseOperation('raw_query', 'custom', duration, true, {
        query: query.substring(0, 100),
        resultCount: result.length,
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.serviceLogger.logDatabaseOperation('raw_query', 'custom', duration, false, {
        query: query.substring(0, 100),
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Execute operation within transaction
   */
  async executeInTransaction<T>(
    operation: (prisma: any) => Promise<T>,
    options?: {
      maxWait?: number;
      timeout?: number;
      isolationLevel?: any;
    }
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      this.serviceLogger.logOperationStart('database_transaction', {
        metadata: { options },
      });

      const result = await this.prisma.transactionWithMetrics(operation, options);
      const duration = Date.now() - startTime;

      this.serviceLogger.logDatabaseOperation('transaction', 'multiple', duration, true, {
        options,
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.serviceLogger.logDatabaseOperation('transaction', 'multiple', duration, false, {
        error: error.message,
        options,
      });
      throw error;
    }
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'unhealthy' | 'degraded';
    responseTime: number;
    details: any;
  }> {
    return this.prisma.healthCheck();
  }

  /**
   * Get database connection status
   */
  isConnected(): boolean {
    return this.prisma.isConnected();
  }

  /**
   * Get service name
   */
  getServiceName(): string {
    return this.serviceName;
  }

  /**
   * Get Prisma client for direct access (use sparingly)
   */
  getPrismaClient(): StandardizedPrismaService {
    return this.prisma;
  }
}
