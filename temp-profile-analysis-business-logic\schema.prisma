// Enterprise Profile Analysis Service - Prisma Schema Template
// CQRS Pattern with Command/Query Models

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENTERPRISE COMMAND MODELS (Write Side)
// ========================================

model ProfileAnalysisCommand {
  id                  String   @id @default(cuid())

  // Core Analysis Data
  userId              String   // User ID being analyzed
  twitterHandle       String   // Twitter username (without @)
  platform            String   @default("twitter") // "twitter", "instagram", "linkedin", "github"
  analysisType        String   @default("comprehensive") // "sentiment", "engagement", "influence", "authenticity", "comprehensive"

  // Analysis Parameters
  timeframe           String?  // "7d", "30d", "90d", "1y"
  includeMetrics      Json?    // Specific metrics to analyze
  analysisDepth       String   @default("standard") // basic, standard, deep

  // Processing Status
  status              String   @default("pending") // pending, processing, completed, failed
  progress            Float    @default(0.0) // 0.0 to 1.0

  // Results
  score               Float?   // Overall analysis score (0-100)
  analysisData        Json?    // Complete analysis results
  analysisResults     Json?    // Legacy field for compatibility
  confidence          Float?   // Confidence score 0.0 to 1.0
  completedAt         DateTime? // When analysis was completed

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  updatedBy           String?
  version             Int      @default(1)
  correlationId       String?  // For tracking requests

  // Enterprise Compliance
  dataClassification  String   @default("internal")
  retentionPolicy     String   @default("7years")

  @@map("profile_analysis_commands")
}

model SocialMetricsCommand {
  id                  String   @id @default(cuid())

  // Core Metrics Data
  profileId           String   // Reference to profile being tracked
  platform            String   // "twitter", "instagram", "linkedin"
  metricType          String   // "followers", "engagement", "reach", "sentiment"

  // Metric Values
  currentValue        Float    // Current metric value
  previousValue       Float?   // Previous period value
  changePercent       Float?   // Percentage change

  // Time Context
  measurementDate     DateTime
  periodType          String   // "daily", "weekly", "monthly"

  // Status
  status              String   @default("active") // active, archived

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  version             Int      @default(1)

  @@map("social_metrics_commands")
}

// ========================================
// ENTERPRISE QUERY MODELS (Read Side)
// ========================================

model ProfileAnalysisQuery {
  id                  String   @id

  // Core Analysis Data
  userId              String   // User ID being analyzed
  twitterHandle       String   // Twitter username (without @)
  platform            String   @default("twitter")
  analysisType        String   @default("comprehensive")

  // Status & Results
  status              String
  progress            Float
  score               Float?   // Overall analysis score (0-100)
  analysisData        Json?    // Complete analysis results
  confidence          Float?

  // Key Metrics Summary (for quick access)
  influenceScore      Float?
  engagementRate      Float?
  sentimentScore      Float?
  authenticityScore   Float?

  // Performance Metrics
  processingTime      Float?   // Time to complete analysis
  dataQuality         Float?   // Quality of source data

  // Timestamps
  createdAt           DateTime
  updatedAt           DateTime @updatedAt
  lastUpdated         DateTime @updatedAt

  @@map("profile_analysis_queries")
}

// ========================================
// ENTERPRISE AUDIT & EVENT SOURCING
// ========================================

model AuditLog {
  id              String   @id @default(cuid())
  entityType      String   // "profile_analysis", "social_metrics", "twitter_auth"
  entityId        String
  action          String   // "create", "analyze", "update", "complete", "authenticate"
  oldValues       Json?
  newValues       Json?
  userId          String?
  sessionId       String?
  ipAddress       String?
  userAgent       String?
  correlationId   String?
  createdAt       DateTime @default(now())

  @@map("audit_logs")
}

model ProfileAnalysisEvent {
  id              String   @id @default(cuid())
  eventType       String   // "analysis_started", "metrics_calculated", "profile_authenticated"
  eventVersion    String   @default("1.0")
  aggregateId     String   // Analysis/Profile ID
  eventData       Json
  correlationId   String?
  causationId     String?
  userId          String?
  createdAt       DateTime @default(now())

  @@map("profile_analysis_events")
}
