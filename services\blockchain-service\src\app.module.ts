import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { HttpModule } from '@nestjs/axios';
import { TerminusModule } from '@nestjs/terminus';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthModule } from './health/health.module';
import { PrismaModule } from './prisma/prisma.module';
import { BlockchainModule } from './blockchain/blockchain.module';
import { AppConfig } from './config/app.config';

@Module({
  imports: [
    // Configuration - Simple and service-local (Industry Standard)
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env'],
      validate: (config) => AppConfig.validate(config),
    }),
    
    // JWT - Service-local configuration (Industry Standard)
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET || 'dev-jwt-secret-change-in-production',
      signOptions: { 
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      },
    }),

    // HTTP module for external service communication
    HttpModule.register({
      timeout: 10000,
      maxRedirects: 3,
    }),

    // Database - Service-local (Industry Standard)
    PrismaModule,

    // Health Checks - Service-local (Industry Standard)
    TerminusModule,
    HealthModule,
    
    // Business Logic - Blockchain and Smart Contract Operations
    BlockchainModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
