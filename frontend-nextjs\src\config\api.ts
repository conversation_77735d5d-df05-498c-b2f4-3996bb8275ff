// Service-specific base URLs
export const SERVICES = {
  USER_SERVICE: process.env.NEXT_PUBLIC_USER_SERVICE_URL || 'http://localhost:3011',
  PROJECT_SERVICE: process.env.NEXT_PUBLIC_PROJECT_SERVICE_URL || 'http://localhost:3005',
  NFT_SERVICE: process.env.NEXT_PUBLIC_NFT_SERVICE_URL || 'http://localhost:3003',
  PROFILE_SERVICE: process.env.NEXT_PUBLIC_PROFILE_SERVICE_URL || 'http://localhost:3002',
  API_GATEWAY: process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://localhost:3010'
};

export const API_CONFIG = {
  BASE_URL: SERVICES.API_GATEWAY, // Use API Gateway for all requests (production-like)
  SERVICES,
  ENDPOINTS: {
    AUTH: {
      REGISTER: '/api/users/auth/register',  // Route through API Gateway to User Service
      LOGIN: '/api/users/auth/login',
      PROFILE: '/api/users/auth/profile',
      LOGOUT: '/api/users/auth/logout'
    },
    CAMPAIGNS: {
      LIST: '/api/projects/campaigns',  // Route through API Gateway to Project Service
      DETAILS: '/api/projects/campaigns',
      JOIN: '/api/projects/campaigns'
    },
    NFTS: {
      USER_NFTS: '/api/nfts/generation/user',  // Route through API Gateway to NFT Service
      GENERATE: '/api/nfts/generation/generate',
      UPDATE: '/api/nfts/generation/update'
    },
    ANALYSIS: {
      HISTORY: '/api/profile/analysis/history',  // Route through API Gateway to Profile Service
      ANALYZE: '/api/profile/analysis/twitter-profile'
    },
    MARKETPLACE: {
      LISTINGS: '/api/marketplace/listings',  // Route through API Gateway to Marketplace Service
      AUCTIONS: '/api/marketplace/auctions',
      OFFERS: '/api/marketplace/offers'
    },
    BLOCKCHAIN: {
      MINT: '/api/blockchain/mint',  // Route through API Gateway to Blockchain Service
      TRANSACTIONS: '/api/blockchain/transactions'
    },
    NOTIFICATIONS: {
      USER: '/api/notifications/user',  // Route through API Gateway to Notification Service
      PREFERENCES: '/api/notifications/preferences'
    },
    ANALYTICS: {
      OVERVIEW: '/api/analytics/overview',  // Route through API Gateway to Analytics Service
      EVENTS: '/api/analytics/events'
    },
    HEALTH: '/api/health'
  }
};

export const API_TIMEOUT = 10000; // 10 seconds
