# Social NFT Platform - Core Requirements Summary

## Platform Goal
Help blockchain, crypto, and Web3 projects attract and retain users through evolving NFTs linked to social media activity.

## Core Workflow
**Users do NOT create projects. Users only participate in campaigns created by project owners.**

### Correct User Journey:
1. **User connects Twitter** to platform (OAuth authentication)
2. **Browse available campaigns** created by project owners
3. **Choose campaign to participate** in
4. **Profile analysis** of user's Twitter account
5. **Initial NFT generation** based on analysis score
6. **NFT minting** on user's chosen blockchain network
7. **Ongoing activity tracking** during campaign period
8. **NFT evolution** based on continued social media engagement
9. **Marketplace trading** of evolved NFTs

## Three Stakeholders

### 1. Platform Administrators (Social NFT Team)
- **Access:** Admin dashboard with role-based permissions
- **Capabilities:**
  - User management system
  - Project management system  
  - Reporting and analytics system
  - Support management system

### 2. Project Owners (Blockchain/Web3 Projects)
- **Access:** Project owner dashboard (username/password login)
- **Capabilities:**
  - Create and configure campaigns
  - Set analysis parameters and weights
  - Define NFT generation themes and styles
  - Configure blockchain networks and contracts
  - Set NFT rarity thresholds (Common, Rare, Legendary)
  - Monitor campaign analytics

### 3. Users (Social Media Participants)
- **Access:** Twitter OAuth login only (no username/password)
- **Capabilities:**
  - Connect Twitter account
  - Browse and join campaigns
  - View their NFT collection and status
  - Update/re-mint NFTs on blockchain
  - List NFTs for sale on marketplace
  - **CANNOT:** Create projects or campaigns

## Service Requirements

### Project Service
**Purpose:** Campaign management (NOT user project creation)
- Create campaigns (project owners only)
- Configure analysis parameters
- Set NFT generation settings
- Define blockchain networks
- Monitor campaign performance

### Profile Analysis Service  
**Purpose:** Analyze Twitter profiles with configurable parameters
- **Fixed Parameters:** Bio, avatar, banner, follower count, post count, etc.
- **Variable Parameters:** Campaign activity, engagement, content quality, referrals
- **Configurable Weights:** Project owners set parameter importance
- **Score Calculation:** Real-time updates during campaigns
- **Multi-Network Support:** Twitter (initial), Farcaster, Lens (future)

### NFT Generation Service
**Purpose:** Create evolving NFTs based on analysis scores
- **Template-Based:** Project owners choose themes/styles
- **Score-Based Rarity:** Common, Rare, Legendary thresholds
- **Evolution Logic:** NFTs change appearance based on score changes
- **Metadata Generation:** Dynamic metadata reflecting current state

### Blockchain Service
**Purpose:** Multi-network NFT minting and management
- **Multi-Chain Support:** Ethereum, Polygon, BSC, Base, etc.
- **User Choice:** Users select which network to mint on
- **Contract Management:** Deploy and manage NFT contracts
- **Transaction Tracking:** Monitor minting and trading

### Marketplace Service
**Purpose:** NFT trading between users
- **Listing Management:** Users list NFTs for sale
- **Price Discovery:** Market-driven pricing
- **Transaction Facilitation:** Secure trading
- **Collection Analytics:** Track collection values

### Analytics Service
**Purpose:** Platform-wide analytics and reporting
- **User Analytics:** Profile scores, engagement tracking
- **Project Analytics:** Campaign performance, ROI metrics
- **Market Analytics:** Trading volumes, price trends
- **Platform Metrics:** User acquisition, retention

## Technical Requirements

### Modularity
- Each service must be independent and modular
- Easy to add/remove features without affecting other services
- Support for adding new social networks (Farcaster, Lens)
- Support for adding new blockchain networks

### Authentication
- **Users:** Twitter OAuth only (no username/password)
- **Project Owners:** Username/password with project-specific access
- **Admins:** Role-based access control with multiple permission levels

### Frontend Requirements
- **Home Page:** Hero section, popular projects, trending campaigns
- **Projects Section:** Search and filter campaigns
- **Monitoring Section:** Top gainers/losers, market values, recent transactions
- **User Dashboard:** NFT collection, status updates, marketplace access

### Performance Requirements
- **Real-time Updates:** Score changes reflected immediately
- **Scalable Analysis:** Handle multiple concurrent profile analyses
- **Fast NFT Generation:** Quick image generation and metadata creation
- **Reliable Minting:** Robust blockchain interactions

## Compliance Rules

### For All AI Agents
1. **Follow Template-Based Approach:** Use proven service templates
2. **Respect User Roles:** Users cannot create projects/campaigns
3. **Maintain Modularity:** Each service independent and extensible
4. **Document Everything:** All implementations must be documented
5. **Test Integration:** Verify end-to-end workflows

### Implementation Priority
1. **Phase 1:** Core user participation workflow
2. **Phase 2:** Project owner campaign management
3. **Phase 3:** Advanced analytics and marketplace
4. **Phase 4:** Multi-network and multi-social expansion

## Success Metrics
- Users can connect Twitter and participate in campaigns
- Project owners can create and manage campaigns
- NFTs generate and evolve based on social activity
- Marketplace enables NFT trading
- Platform scales to support multiple projects and users
