'use client'

import React, { useState } from 'react'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'
import { NFTGenerationStatus, GenerationProvider, NFTQuality, NFTRarity, GenerationFilters } from '@/types/nft-generation.types'

interface GenerationFiltersProps {
  filters: GenerationFilters
  onFiltersChange: (filters: GenerationFilters) => void
  className?: string
}

export default function GenerationFiltersComponent({
  filters,
  onFiltersChange,
  className = ''
}: GenerationFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const statusOptions = [
    { value: NFTGenerationStatus.PENDING, label: 'Pending', color: 'text-gray-600 bg-gray-100' },
    { value: NFTGenerationStatus.QUEUED, label: 'Queued', color: 'text-blue-600 bg-blue-100' },
    { value: NFTGenerationStatus.GENERATING, label: 'Generating', color: 'text-yellow-600 bg-yellow-100' },
    { value: NFTGenerationStatus.GENERATED, label: 'Generated', color: 'text-green-600 bg-green-100' },
    { value: NFTGenerationStatus.REVIEWING, label: 'Reviewing', color: 'text-purple-600 bg-purple-100' },
    { value: NFTGenerationStatus.APPROVED, label: 'Approved', color: 'text-green-600 bg-green-100' },
    { value: NFTGenerationStatus.REJECTED, label: 'Rejected', color: 'text-red-600 bg-red-100' },
    { value: NFTGenerationStatus.MINTING, label: 'Minting', color: 'text-indigo-600 bg-indigo-100' },
    { value: NFTGenerationStatus.MINTED, label: 'Minted', color: 'text-green-600 bg-green-100' },
    { value: NFTGenerationStatus.DISTRIBUTED, label: 'Distributed', color: 'text-blue-600 bg-blue-100' },
    { value: NFTGenerationStatus.FAILED, label: 'Failed', color: 'text-red-600 bg-red-100' }
  ]

  const providerOptions = [
    { value: GenerationProvider.OPENAI_DALLE, label: 'OpenAI DALL-E', icon: '🤖' },
    { value: GenerationProvider.MIDJOURNEY, label: 'Midjourney', icon: '🎨' },
    { value: GenerationProvider.STABLE_DIFFUSION, label: 'Stable Diffusion', icon: '🔬' },
    { value: GenerationProvider.CUSTOM_AI, label: 'Custom AI', icon: '⚙️' },
    { value: GenerationProvider.MANUAL, label: 'Manual', icon: '✋' }
  ]

  const qualityOptions = [
    { value: NFTQuality.LOW, label: 'Low', color: 'text-gray-600 bg-gray-100' },
    { value: NFTQuality.MEDIUM, label: 'Medium', color: 'text-blue-600 bg-blue-100' },
    { value: NFTQuality.HIGH, label: 'High', color: 'text-green-600 bg-green-100' },
    { value: NFTQuality.PREMIUM, label: 'Premium', color: 'text-purple-600 bg-purple-100' }
  ]

  const rarityOptions = [
    { value: NFTRarity.COMMON, label: 'Common', color: 'text-gray-600 bg-gray-100' },
    { value: NFTRarity.RARE, label: 'Rare', color: 'text-blue-600 bg-blue-100' },
    { value: NFTRarity.EPIC, label: 'Epic', color: 'text-purple-600 bg-purple-100' },
    { value: NFTRarity.LEGENDARY, label: 'Legendary', color: 'text-yellow-600 bg-yellow-100' },
    { value: NFTRarity.MYTHIC, label: 'Mythic', color: 'text-red-600 bg-red-100' }
  ]

  const updateFilters = (updates: Partial<GenerationFilters>) => {
    onFiltersChange({ ...filters, ...updates, page: 1 }) // Reset to page 1 when filters change
  }

  const toggleArrayFilter = <T,>(key: keyof GenerationFilters, value: T) => {
    const currentArray = (filters[key] as T[]) || []
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    updateFilters({ [key]: newArray })
  }

  const clearAllFilters = () => {
    onFiltersChange({
      campaignId: filters.campaignId, // Keep campaign filter if set
      status: [],
      provider: [],
      quality: [],
      rarity: [],
      dateRange: undefined,
      sortBy: 'created_at',
      sortOrder: 'desc',
      page: 1,
      limit: filters.limit
    })
  }

  const hasActiveFilters = 
    (filters.status && filters.status.length > 0) ||
    (filters.provider && filters.provider.length > 0) ||
    (filters.quality && filters.quality.length > 0) ||
    (filters.rarity && filters.rarity.length > 0) ||
    filters.dateRange

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.status && filters.status.length > 0) count += filters.status.length
    if (filters.provider && filters.provider.length > 0) count += filters.provider.length
    if (filters.quality && filters.quality.length > 0) count += filters.quality.length
    if (filters.rarity && filters.rarity.length > 0) count += filters.rarity.length
    if (filters.dateRange) count += 1
    return count
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Quick Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search */}
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            value={filters.search || ''}
            onChange={(e) => updateFilters({ search: e.target.value })}
            placeholder="Search by prompt, name, or ID..."
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
          />
        </div>

        {/* Quick Status Filters */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => updateFilters({ status: [NFTGenerationStatus.PENDING, NFTGenerationStatus.QUEUED] })}
            className={`px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
              filters.status?.includes(NFTGenerationStatus.PENDING) || filters.status?.includes(NFTGenerationStatus.QUEUED)
                ? 'border-blue-300 bg-blue-100 text-blue-700'
                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            Pending
          </button>
          
          <button
            onClick={() => updateFilters({ status: [NFTGenerationStatus.GENERATING] })}
            className={`px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
              filters.status?.includes(NFTGenerationStatus.GENERATING)
                ? 'border-yellow-300 bg-yellow-100 text-yellow-700'
                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            Active
          </button>
          
          <button
            onClick={() => updateFilters({ status: [NFTGenerationStatus.REVIEWING] })}
            className={`px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
              filters.status?.includes(NFTGenerationStatus.REVIEWING)
                ? 'border-purple-300 bg-purple-100 text-purple-700'
                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            Review
          </button>
        </div>

        {/* Advanced Filters Toggle */}
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={`inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
            showAdvanced
              ? 'border-purple-300 bg-purple-100 text-purple-700'
              : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
          }`}
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          Filters
          {hasActiveFilters && (
            <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
              {getActiveFilterCount()}
            </span>
          )}
        </button>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <XMarkIcon className="h-4 w-4 mr-2" />
            Clear
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <div className="space-y-4">
            {/* Status Filters */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Generation Status
              </label>
              <div className="flex flex-wrap gap-2">
                {statusOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => toggleArrayFilter('status', option.value)}
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border transition-colors ${
                      filters.status?.includes(option.value)
                        ? `${option.color} border-current`
                        : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {option.label}
                    {filters.status?.includes(option.value) && (
                      <XMarkIcon className="h-3 w-3 ml-1" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Provider Filters */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Generation Provider
              </label>
              <div className="flex flex-wrap gap-2">
                {providerOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => toggleArrayFilter('provider', option.value)}
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border transition-colors ${
                      filters.provider?.includes(option.value)
                        ? 'border-blue-300 bg-blue-100 text-blue-700'
                        : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <span className="mr-1">{option.icon}</span>
                    {option.label}
                    {filters.provider?.includes(option.value) && (
                      <XMarkIcon className="h-3 w-3 ml-1" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Quality and Rarity Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quality Level
                </label>
                <div className="flex flex-wrap gap-2">
                  {qualityOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => toggleArrayFilter('quality', option.value)}
                      className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border transition-colors ${
                        filters.quality?.includes(option.value)
                          ? `${option.color} border-current`
                          : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {option.label}
                      {filters.quality?.includes(option.value) && (
                        <XMarkIcon className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rarity Level
                </label>
                <div className="flex flex-wrap gap-2">
                  {rarityOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => toggleArrayFilter('rarity', option.value)}
                      className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border transition-colors ${
                        filters.rarity?.includes(option.value)
                          ? `${option.color} border-current`
                          : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {option.label}
                      {filters.rarity?.includes(option.value) && (
                        <XMarkIcon className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date Range
              </label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <input
                    type="date"
                    value={filters.dateRange?.start || ''}
                    onChange={(e) => updateFilters({
                      dateRange: {
                        start: e.target.value,
                        end: filters.dateRange?.end || ''
                      }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
                <div>
                  <input
                    type="date"
                    value={filters.dateRange?.end || ''}
                    onChange={(e) => updateFilters({
                      dateRange: {
                        start: filters.dateRange?.start || '',
                        end: e.target.value
                      }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="flex flex-wrap items-center gap-2 text-sm">
          <span className="text-gray-600">Active filters:</span>
          
          {filters.status?.map((status) => {
            const option = statusOptions.find(opt => opt.value === status)
            return (
              <span
                key={status}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${option?.color}`}
              >
                {option?.label}
                <button
                  onClick={() => toggleArrayFilter('status', status)}
                  className="ml-1 hover:text-current"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )
          })}

          {filters.provider?.map((provider) => {
            const option = providerOptions.find(opt => opt.value === provider)
            return (
              <span
                key={provider}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700"
              >
                <span className="mr-1">{option?.icon}</span>
                {option?.label}
                <button
                  onClick={() => toggleArrayFilter('provider', provider)}
                  className="ml-1 text-blue-500 hover:text-blue-700"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )
          })}

          {filters.quality?.map((quality) => {
            const option = qualityOptions.find(opt => opt.value === quality)
            return (
              <span
                key={quality}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${option?.color}`}
              >
                {option?.label}
                <button
                  onClick={() => toggleArrayFilter('quality', quality)}
                  className="ml-1 hover:text-current"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )
          })}

          {filters.rarity?.map((rarity) => {
            const option = rarityOptions.find(opt => opt.value === rarity)
            return (
              <span
                key={rarity}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${option?.color}`}
              >
                {option?.label}
                <button
                  onClick={() => toggleArrayFilter('rarity', rarity)}
                  className="ml-1 hover:text-current"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )
          })}

          {filters.dateRange && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
              <CalendarIcon className="h-3 w-3 mr-1" />
              {filters.dateRange.start} - {filters.dateRange.end}
              <button
                onClick={() => updateFilters({ dateRange: undefined })}
                className="ml-1 text-gray-500 hover:text-gray-700"
              >
                <XMarkIcon className="h-3 w-3" />
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  )
}
