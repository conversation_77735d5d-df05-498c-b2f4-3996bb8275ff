import { api } from '@/lib/api'
import {
  PerformanceMetric,
  SystemPerformance,
  PerformanceAlert,
  OptimizationRecommendation,
  LoadTestResult,
  CacheStrategy,
  GetPerformanceMetricsRequest,
  GetPerformanceMetricsResponse,
  CreateOptimizationRequest,
  UpdateOptimizationRequest,
  RunLoadTestRequest,
  GetSystemPerformanceResponse,
  PerformanceCategory,
  OptimizationType,
  CacheType,
  AlertSeverity
} from '@/types/performance.types'

class PerformanceService {
  // Performance Metrics
  async getPerformanceMetrics(request: GetPerformanceMetricsRequest): Promise<GetPerformanceMetricsResponse> {
    try {
      const params = new URLSearchParams()
      if (request.category) params.append('category', request.category)
      if (request.timeRange) {
        params.append('start', request.timeRange.start)
        params.append('end', request.timeRange.end)
      }
      if (request.aggregation) params.append('aggregation', request.aggregation)
      if (request.limit) params.append('limit', request.limit.toString())
      if (request.offset) params.append('offset', request.offset.toString())

      const response = await api.get(`/performance/metrics?${params}`)
      return response.data
    } catch (error) {
      console.error('Failed to get performance metrics:', error)
      throw error
    }
  }

  async getSystemPerformance(): Promise<GetSystemPerformanceResponse> {
    try {
      const response = await api.get('/performance/system')
      return response.data
    } catch (error) {
      console.error('Failed to get system performance:', error)
      throw error
    }
  }

  async getRealtimeMetrics(): Promise<PerformanceMetric[]> {
    try {
      const response = await api.get('/performance/metrics/realtime')
      return response.data.metrics
    } catch (error) {
      console.error('Failed to get realtime metrics:', error)
      return []
    }
  }

  async recordCustomMetric(metric: Omit<PerformanceMetric, 'id' | 'timestamp'>): Promise<void> {
    try {
      await api.post('/performance/metrics/custom', metric)
    } catch (error) {
      console.error('Failed to record custom metric:', error)
      throw error
    }
  }

  // Performance Alerts
  async getPerformanceAlerts(severity?: AlertSeverity): Promise<PerformanceAlert[]> {
    try {
      const params = new URLSearchParams()
      if (severity) params.append('severity', severity)

      const response = await api.get(`/performance/alerts?${params}`)
      return response.data.alerts
    } catch (error) {
      console.error('Failed to get performance alerts:', error)
      return []
    }
  }

  async acknowledgeAlert(alertId: string): Promise<void> {
    try {
      await api.patch(`/performance/alerts/${alertId}/acknowledge`)
    } catch (error) {
      console.error('Failed to acknowledge alert:', error)
      throw error
    }
  }

  async resolveAlert(alertId: string, resolution?: string): Promise<void> {
    try {
      await api.patch(`/performance/alerts/${alertId}/resolve`, { resolution })
    } catch (error) {
      console.error('Failed to resolve alert:', error)
      throw error
    }
  }

  async suppressAlert(alertId: string, duration: number): Promise<void> {
    try {
      await api.patch(`/performance/alerts/${alertId}/suppress`, { duration })
    } catch (error) {
      console.error('Failed to suppress alert:', error)
      throw error
    }
  }

  // Optimization Recommendations
  async getOptimizationRecommendations(type?: OptimizationType): Promise<OptimizationRecommendation[]> {
    try {
      const params = new URLSearchParams()
      if (type) params.append('type', type)

      const response = await api.get(`/performance/optimizations?${params}`)
      return response.data.recommendations
    } catch (error) {
      console.error('Failed to get optimization recommendations:', error)
      return []
    }
  }

  async createOptimizationRecommendation(request: CreateOptimizationRequest): Promise<OptimizationRecommendation> {
    try {
      const response = await api.post('/performance/optimizations', request)
      return response.data
    } catch (error) {
      console.error('Failed to create optimization recommendation:', error)
      throw error
    }
  }

  async updateOptimizationRecommendation(request: UpdateOptimizationRequest): Promise<OptimizationRecommendation> {
    try {
      const response = await api.patch(`/performance/optimizations/${request.id}`, request)
      return response.data
    } catch (error) {
      console.error('Failed to update optimization recommendation:', error)
      throw error
    }
  }

  async implementOptimization(optimizationId: string): Promise<void> {
    try {
      await api.post(`/performance/optimizations/${optimizationId}/implement`)
    } catch (error) {
      console.error('Failed to implement optimization:', error)
      throw error
    }
  }

  async generateOptimizationReport(): Promise<any> {
    try {
      const response = await api.get('/performance/optimizations/report')
      return response.data
    } catch (error) {
      console.error('Failed to generate optimization report:', error)
      throw error
    }
  }

  // Caching
  async getCacheStrategies(): Promise<CacheStrategy[]> {
    try {
      const response = await api.get('/performance/cache/strategies')
      return response.data.strategies
    } catch (error) {
      console.error('Failed to get cache strategies:', error)
      return []
    }
  }

  async createCacheStrategy(strategy: Omit<CacheStrategy, 'id'>): Promise<CacheStrategy> {
    try {
      const response = await api.post('/performance/cache/strategies', strategy)
      return response.data
    } catch (error) {
      console.error('Failed to create cache strategy:', error)
      throw error
    }
  }

  async updateCacheStrategy(strategyId: string, updates: Partial<CacheStrategy>): Promise<CacheStrategy> {
    try {
      const response = await api.patch(`/performance/cache/strategies/${strategyId}`, updates)
      return response.data
    } catch (error) {
      console.error('Failed to update cache strategy:', error)
      throw error
    }
  }

  async getCacheMetrics(type?: CacheType): Promise<any> {
    try {
      const params = new URLSearchParams()
      if (type) params.append('type', type)

      const response = await api.get(`/performance/cache/metrics?${params}`)
      return response.data
    } catch (error) {
      console.error('Failed to get cache metrics:', error)
      throw error
    }
  }

  async clearCache(type?: CacheType, pattern?: string): Promise<void> {
    try {
      const params = new URLSearchParams()
      if (type) params.append('type', type)
      if (pattern) params.append('pattern', pattern)

      await api.delete(`/performance/cache/clear?${params}`)
    } catch (error) {
      console.error('Failed to clear cache:', error)
      throw error
    }
  }

  async warmupCache(keys: string[]): Promise<void> {
    try {
      await api.post('/performance/cache/warmup', { keys })
    } catch (error) {
      console.error('Failed to warmup cache:', error)
      throw error
    }
  }

  // Load Testing
  async getLoadTests(): Promise<LoadTestResult[]> {
    try {
      const response = await api.get('/performance/load-tests')
      return response.data.tests
    } catch (error) {
      console.error('Failed to get load tests:', error)
      return []
    }
  }

  async runLoadTest(request: RunLoadTestRequest): Promise<LoadTestResult> {
    try {
      const response = await api.post('/performance/load-tests', request)
      return response.data
    } catch (error) {
      console.error('Failed to run load test:', error)
      throw error
    }
  }

  async getLoadTestResult(testId: string): Promise<LoadTestResult> {
    try {
      const response = await api.get(`/performance/load-tests/${testId}`)
      return response.data
    } catch (error) {
      console.error('Failed to get load test result:', error)
      throw error
    }
  }

  async stopLoadTest(testId: string): Promise<void> {
    try {
      await api.post(`/performance/load-tests/${testId}/stop`)
    } catch (error) {
      console.error('Failed to stop load test:', error)
      throw error
    }
  }

  async scheduleLoadTest(testId: string, scheduleTime: string): Promise<void> {
    try {
      await api.post(`/performance/load-tests/${testId}/schedule`, { scheduleTime })
    } catch (error) {
      console.error('Failed to schedule load test:', error)
      throw error
    }
  }

  // Database Optimization
  async getDatabaseMetrics(): Promise<any> {
    try {
      const response = await api.get('/performance/database/metrics')
      return response.data
    } catch (error) {
      console.error('Failed to get database metrics:', error)
      throw error
    }
  }

  async getSlowQueries(limit = 10): Promise<any[]> {
    try {
      const response = await api.get(`/performance/database/slow-queries?limit=${limit}`)
      return response.data.queries
    } catch (error) {
      console.error('Failed to get slow queries:', error)
      return []
    }
  }

  async optimizeQuery(queryId: string): Promise<any> {
    try {
      const response = await api.post(`/performance/database/optimize-query/${queryId}`)
      return response.data
    } catch (error) {
      console.error('Failed to optimize query:', error)
      throw error
    }
  }

  async analyzeIndexUsage(): Promise<any> {
    try {
      const response = await api.get('/performance/database/index-analysis')
      return response.data
    } catch (error) {
      console.error('Failed to analyze index usage:', error)
      throw error
    }
  }

  async createIndex(table: string, columns: string[], options?: any): Promise<void> {
    try {
      await api.post('/performance/database/create-index', { table, columns, options })
    } catch (error) {
      console.error('Failed to create index:', error)
      throw error
    }
  }

  // Frontend Performance
  async getFrontendMetrics(): Promise<any> {
    try {
      const response = await api.get('/performance/frontend/metrics')
      return response.data
    } catch (error) {
      console.error('Failed to get frontend metrics:', error)
      throw error
    }
  }

  async getWebVitals(): Promise<any> {
    try {
      const response = await api.get('/performance/frontend/web-vitals')
      return response.data
    } catch (error) {
      console.error('Failed to get web vitals:', error)
      throw error
    }
  }

  async recordWebVital(metric: string, value: number, metadata?: any): Promise<void> {
    try {
      await api.post('/performance/frontend/web-vitals', { metric, value, metadata })
    } catch (error) {
      console.error('Failed to record web vital:', error)
      throw error
    }
  }

  async getBundleAnalysis(): Promise<any> {
    try {
      const response = await api.get('/performance/frontend/bundle-analysis')
      return response.data
    } catch (error) {
      console.error('Failed to get bundle analysis:', error)
      throw error
    }
  }

  async optimizeAssets(): Promise<any> {
    try {
      const response = await api.post('/performance/frontend/optimize-assets')
      return response.data
    } catch (error) {
      console.error('Failed to optimize assets:', error)
      throw error
    }
  }

  // Network Optimization
  async getNetworkMetrics(): Promise<any> {
    try {
      const response = await api.get('/performance/network/metrics')
      return response.data
    } catch (error) {
      console.error('Failed to get network metrics:', error)
      throw error
    }
  }

  async optimizeNetworkRequests(): Promise<any> {
    try {
      const response = await api.post('/performance/network/optimize')
      return response.data
    } catch (error) {
      console.error('Failed to optimize network requests:', error)
      throw error
    }
  }

  async enableCompression(types: string[]): Promise<void> {
    try {
      await api.post('/performance/network/compression', { types })
    } catch (error) {
      console.error('Failed to enable compression:', error)
      throw error
    }
  }

  async configureCDN(settings: any): Promise<void> {
    try {
      await api.post('/performance/network/cdn', settings)
    } catch (error) {
      console.error('Failed to configure CDN:', error)
      throw error
    }
  }

  // Real-time Monitoring
  async startRealtimeMonitoring(): Promise<EventSource> {
    try {
      const eventSource = new EventSource('/api/performance/realtime')
      return eventSource
    } catch (error) {
      console.error('Failed to start realtime monitoring:', error)
      throw error
    }
  }

  async getPerformanceDashboard(): Promise<any> {
    try {
      const response = await api.get('/performance/dashboard')
      return response.data
    } catch (error) {
      console.error('Failed to get performance dashboard:', error)
      throw error
    }
  }
}

export const performanceService = new PerformanceService()
