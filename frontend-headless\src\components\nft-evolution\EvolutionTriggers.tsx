'use client'

import React, { useState } from 'react'
import {
  SparklesIcon,
  ClockIcon,
  UserGroupIcon,
  TrophyIcon,
  ChartBarIcon,
  CalendarIcon,
  CogIcon,
  PlayIcon,
  EyeIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import {
  useCreateEvolution,
  useSimulateEvolution,
  useEstimateEvolutionCosts
} from '@/hooks/useNFTEvolution'
import { EvolutionTrigger, EvolutionTriggerType } from '@/types/nft-evolution.types'

interface EvolutionTriggersProps {
  nftId: string
  availableTriggers?: EvolutionTrigger[]
  isLoading: boolean
  onTriggerActivated?: (evolutionId: string) => void
  className?: string
}

export default function EvolutionTriggers({
  nftId,
  availableTriggers = [],
  isLoading,
  onTriggerActivated,
  className = ''
}: EvolutionTriggersProps) {
  const [selectedTrigger, setSelectedTrigger] = useState<EvolutionTrigger | null>(null)
  const [showSimulation, setShowSimulation] = useState(false)
  const [customizations, setCustomizations] = useState<Record<string, any>>({})

  const createEvolutionMutation = useCreateEvolution()
  const simulateEvolutionMutation = useSimulateEvolution()
  const estimateCostsMutation = useEstimateEvolutionCosts()

  const getTriggerIcon = (type: EvolutionTriggerType) => {
    switch (type) {
      case EvolutionTriggerType.TIME_BASED:
        return <ClockIcon className="h-6 w-6" />
      case EvolutionTriggerType.INTERACTION_BASED:
        return <UserGroupIcon className="h-6 w-6" />
      case EvolutionTriggerType.ACHIEVEMENT_BASED:
        return <TrophyIcon className="h-6 w-6" />
      case EvolutionTriggerType.COMMUNITY_BASED:
        return <UserGroupIcon className="h-6 w-6" />
      case EvolutionTriggerType.MARKET_BASED:
        return <ChartBarIcon className="h-6 w-6" />
      case EvolutionTriggerType.SEASONAL:
        return <CalendarIcon className="h-6 w-6" />
      case EvolutionTriggerType.MANUAL:
        return <CogIcon className="h-6 w-6" />
      default:
        return <SparklesIcon className="h-6 w-6" />
    }
  }

  const getTriggerColor = (type: EvolutionTriggerType) => {
    switch (type) {
      case EvolutionTriggerType.TIME_BASED:
        return 'text-blue-600 bg-blue-100'
      case EvolutionTriggerType.INTERACTION_BASED:
        return 'text-green-600 bg-green-100'
      case EvolutionTriggerType.ACHIEVEMENT_BASED:
        return 'text-yellow-600 bg-yellow-100'
      case EvolutionTriggerType.COMMUNITY_BASED:
        return 'text-purple-600 bg-purple-100'
      case EvolutionTriggerType.MARKET_BASED:
        return 'text-red-600 bg-red-100'
      case EvolutionTriggerType.SEASONAL:
        return 'text-orange-600 bg-orange-100'
      case EvolutionTriggerType.MANUAL:
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-indigo-600 bg-indigo-100'
    }
  }

  const handleActivateTrigger = async (trigger: EvolutionTrigger) => {
    try {
      const evolution = await createEvolutionMutation.mutateAsync({
        nftId,
        triggerId: trigger.id,
        customizations,
        approvalRequired: trigger.requiredAchievements && trigger.requiredAchievements.length > 0
      })
      
      onTriggerActivated?.(evolution.id)
      setSelectedTrigger(null)
      setCustomizations({})
    } catch (error) {
      console.error('Failed to activate trigger:', error)
    }
  }

  const handleSimulateTrigger = async (trigger: EvolutionTrigger) => {
    try {
      const simulation = await simulateEvolutionMutation.mutateAsync({
        nftId,
        triggerId: trigger.id,
        customizations
      })
      
      setShowSimulation(true)
      // Handle simulation results
    } catch (error) {
      console.error('Failed to simulate trigger:', error)
    }
  }

  const handleEstimateCosts = async (trigger: EvolutionTrigger) => {
    try {
      const costs = await estimateCostsMutation.mutateAsync({
        nftId,
        triggerId: trigger.id
      })
      
      // Handle cost estimation results
    } catch (error) {
      console.error('Failed to estimate costs:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div>
        <h2 className="text-lg font-medium text-gray-900">Available Evolution Triggers</h2>
        <p className="text-sm text-gray-600">
          Activate triggers to evolve your NFT with new traits and capabilities
        </p>
      </div>

      {/* Triggers Grid */}
      {availableTriggers.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {availableTriggers.map((trigger) => (
            <div
              key={trigger.id}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all"
            >
              {/* Trigger Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${getTriggerColor(trigger.type)}`}>
                    {getTriggerIcon(trigger.type)}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{trigger.name}</h3>
                    <p className="text-sm text-gray-600">
                      {trigger.type.replace('_', ' ')} trigger
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">
                    Priority: {trigger.priority}
                  </span>
                  {!trigger.isActive && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                      Inactive
                    </span>
                  )}
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-gray-700 mb-4">{trigger.description}</p>

              {/* Trigger Conditions */}
              {trigger.conditions && trigger.conditions.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Conditions</h4>
                  <div className="space-y-1">
                    {trigger.conditions.slice(0, 3).map((condition, index) => (
                      <div key={index} className="text-xs text-gray-600 flex items-center">
                        <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                        {condition.type}: {condition.operator} {condition.value}
                        {condition.isRequired && (
                          <span className="ml-1 text-red-500">*</span>
                        )}
                      </div>
                    ))}
                    {trigger.conditions.length > 3 && (
                      <div className="text-xs text-gray-500">
                        +{trigger.conditions.length - 3} more conditions
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Requirements */}
              <div className="mb-4 space-y-2">
                {trigger.requiredInteractions && (
                  <div className="text-xs text-gray-600">
                    <span className="font-medium">Required Interactions:</span> {trigger.requiredInteractions}
                  </div>
                )}
                
                {trigger.requiredAchievements && trigger.requiredAchievements.length > 0 && (
                  <div className="text-xs text-gray-600">
                    <span className="font-medium">Required Achievements:</span> {trigger.requiredAchievements.length}
                  </div>
                )}
                
                {trigger.minMarketValue && (
                  <div className="text-xs text-gray-600">
                    <span className="font-medium">Min Market Value:</span> ${trigger.minMarketValue}
                  </div>
                )}
              </div>

              {/* Timing Information */}
              <div className="mb-4 text-xs text-gray-600 space-y-1">
                {trigger.activationTime && (
                  <div>
                    <span className="font-medium">Activation:</span> {new Date(trigger.activationTime).toLocaleString()}
                  </div>
                )}
                
                {trigger.expirationTime && (
                  <div>
                    <span className="font-medium">Expires:</span> {new Date(trigger.expirationTime).toLocaleString()}
                  </div>
                )}
                
                {trigger.cooldownPeriod && (
                  <div>
                    <span className="font-medium">Cooldown:</span> {trigger.cooldownPeriod} seconds
                  </div>
                )}
              </div>

              {/* Usage Stats */}
              <div className="mb-4 flex items-center justify-between text-xs text-gray-600">
                <div>
                  <span className="font-medium">Used:</span> {trigger.currentExecutions}
                  {trigger.maxExecutions && ` / ${trigger.maxExecutions}`}
                </div>
                
                {trigger.maxExecutions && (
                  <div className="w-16 bg-gray-200 rounded-full h-1">
                    <div
                      className="bg-blue-600 h-1 rounded-full"
                      style={{ 
                        width: `${(trigger.currentExecutions / trigger.maxExecutions) * 100}%` 
                      }}
                    ></div>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleSimulateTrigger(trigger)}
                  disabled={!trigger.isActive || simulateEvolutionMutation.isPending}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Preview
                </button>
                
                <button
                  onClick={() => handleEstimateCosts(trigger)}
                  disabled={!trigger.isActive || estimateCostsMutation.isPending}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <InformationCircleIcon className="h-4 w-4 mr-2" />
                  Costs
                </button>
                
                <button
                  onClick={() => handleActivateTrigger(trigger)}
                  disabled={!trigger.isActive || createEvolutionMutation.isPending}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlayIcon className="h-4 w-4 mr-2" />
                  Activate
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No triggers available</h3>
          <p className="mt-1 text-sm text-gray-500">
            No evolution triggers are currently available for this NFT.
          </p>
        </div>
      )}

      {/* Trigger Categories */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Trigger Types</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {Object.values(EvolutionTriggerType).map((type) => {
            const count = availableTriggers.filter(t => t.type === type).length
            return (
              <div key={type} className="text-center">
                <div className={`inline-flex p-2 rounded-lg ${getTriggerColor(type)} mb-1`}>
                  {getTriggerIcon(type)}
                </div>
                <div className="text-xs font-medium text-gray-900">
                  {type.replace('_', ' ')}
                </div>
                <div className="text-xs text-gray-600">{count} available</div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Simulation Results Modal */}
      {showSimulation && simulateEvolutionMutation.data && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Evolution Simulation</h3>
              <button
                onClick={() => setShowSimulation(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <img
                  src={simulateEvolutionMutation.data.estimatedOutcome.visualPreview}
                  alt="Evolution Preview"
                  className="w-full h-48 object-cover rounded-lg"
                />
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Rarity Change:</span>
                  <span className="ml-1 font-medium text-purple-600">
                    +{simulateEvolutionMutation.data.estimatedOutcome.rarityChange}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Value Change:</span>
                  <span className="ml-1 font-medium text-green-600">
                    +{simulateEvolutionMutation.data.estimatedOutcome.valueChange}%
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Success Rate:</span>
                  <span className="ml-1 font-medium text-blue-600">
                    {(simulateEvolutionMutation.data.estimatedOutcome.successProbability * 100).toFixed(1)}%
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Total Cost:</span>
                  <span className="ml-1 font-medium text-gray-900">
                    ${simulateEvolutionMutation.data.costs.totalCost}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  onClick={() => setShowSimulation(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // Proceed with actual evolution
                    setShowSimulation(false)
                  }}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
                >
                  Proceed with Evolution
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
