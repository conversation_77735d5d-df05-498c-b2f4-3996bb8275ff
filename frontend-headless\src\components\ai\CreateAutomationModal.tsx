'use client'

import React, { useState } from 'react'
import {
  XMarkIcon,
  CogIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import {
  CreateAutomationRequest,
  AutomationType,
  TriggerType,
  ActionType,
  ConditionOperator,
  LogicalOperator,
  AutomationTrigger,
  AutomationCondition,
  AutomationAction
} from '@/types/ai.types'

interface CreateAutomationModalProps {
  onClose: () => void
  onCreateRule: (request: CreateAutomationRequest) => void
  className?: string
}

export default function CreateAutomationModal({
  onClose,
  onCreateRule,
  className = ''
}: CreateAutomationModalProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [ruleData, setRuleData] = useState<CreateAutomationRequest>({
    name: '',
    description: '',
    type: AutomationType.TRADING,
    trigger: {
      type: TriggerType.PRICE_CHANGE,
      parameters: {}
    },
    conditions: [],
    actions: [],
    priority: 1
  })

  const automationTypes = [
    { value: AutomationType.TRADING, label: 'Trading', description: 'Automated buy/sell orders' },
    { value: AutomationType.SOCIAL, label: 'Social', description: 'Social media interactions' },
    { value: AutomationType.CONTENT, label: 'Content', description: 'Content creation and posting' },
    { value: AutomationType.NOTIFICATION, label: 'Notifications', description: 'Smart alerts and reminders' },
    { value: AutomationType.PORTFOLIO, label: 'Portfolio', description: 'Portfolio management actions' }
  ]

  const triggerTypes = [
    { value: TriggerType.PRICE_CHANGE, label: 'Price Change', description: 'When price changes by amount/percentage' },
    { value: TriggerType.TIME_BASED, label: 'Time Based', description: 'At specific times or intervals' },
    { value: TriggerType.EVENT_BASED, label: 'Event Based', description: 'When specific events occur' },
    { value: TriggerType.THRESHOLD_REACHED, label: 'Threshold', description: 'When metrics reach thresholds' },
    { value: TriggerType.USER_ACTION, label: 'User Action', description: 'When user performs actions' },
    { value: TriggerType.MARKET_CONDITION, label: 'Market Condition', description: 'Based on market conditions' }
  ]

  const actionTypes = [
    { value: ActionType.SEND_NOTIFICATION, label: 'Send Notification', description: 'Send alert or message' },
    { value: ActionType.CREATE_ORDER, label: 'Create Order', description: 'Place buy/sell order' },
    { value: ActionType.CANCEL_ORDER, label: 'Cancel Order', description: 'Cancel existing order' },
    { value: ActionType.UPDATE_PORTFOLIO, label: 'Update Portfolio', description: 'Modify portfolio allocation' },
    { value: ActionType.POST_CONTENT, label: 'Post Content', description: 'Create social media post' },
    { value: ActionType.JOIN_COMMUNITY, label: 'Join Community', description: 'Join NFT community' },
    { value: ActionType.FOLLOW_USER, label: 'Follow User', description: 'Follow another user' }
  ]

  const conditionOperators = [
    { value: ConditionOperator.EQUALS, label: 'Equals' },
    { value: ConditionOperator.NOT_EQUALS, label: 'Not Equals' },
    { value: ConditionOperator.GREATER_THAN, label: 'Greater Than' },
    { value: ConditionOperator.LESS_THAN, label: 'Less Than' },
    { value: ConditionOperator.CONTAINS, label: 'Contains' },
    { value: ConditionOperator.IN, label: 'In' },
    { value: ConditionOperator.NOT_IN, label: 'Not In' }
  ]

  const addCondition = () => {
    const newCondition: AutomationCondition = {
      field: '',
      operator: ConditionOperator.EQUALS,
      value: '',
      logicalOperator: ruleData.conditions.length > 0 ? LogicalOperator.AND : undefined
    }
    setRuleData(prev => ({
      ...prev,
      conditions: [...prev.conditions, newCondition]
    }))
  }

  const updateCondition = (index: number, field: keyof AutomationCondition, value: any) => {
    setRuleData(prev => ({
      ...prev,
      conditions: prev.conditions.map((condition, i) =>
        i === index ? { ...condition, [field]: value } : condition
      )
    }))
  }

  const removeCondition = (index: number) => {
    setRuleData(prev => ({
      ...prev,
      conditions: prev.conditions.filter((_, i) => i !== index)
    }))
  }

  const addAction = () => {
    const newAction: AutomationAction = {
      type: ActionType.SEND_NOTIFICATION,
      parameters: {}
    }
    setRuleData(prev => ({
      ...prev,
      actions: [...prev.actions, newAction]
    }))
  }

  const updateAction = (index: number, field: keyof AutomationAction, value: any) => {
    setRuleData(prev => ({
      ...prev,
      actions: prev.actions.map((action, i) =>
        i === index ? { ...action, [field]: value } : action
      )
    }))
  }

  const removeAction = (index: number) => {
    setRuleData(prev => ({
      ...prev,
      actions: prev.actions.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!ruleData.name.trim() || !ruleData.description.trim() || ruleData.actions.length === 0) {
      alert('Please fill in all required fields and add at least one action.')
      return
    }

    onCreateRule(ruleData)
  }

  const canProceed = (step: number) => {
    switch (step) {
      case 1:
        return ruleData.name.trim().length > 0 && ruleData.description.trim().length > 0
      case 2:
        return true // Trigger is always required but has defaults
      case 3:
        return true // Conditions are optional
      case 4:
        return ruleData.actions.length > 0
      default:
        return false
    }
  }

  const steps = [
    { number: 1, title: 'Basic Info', description: 'Name, description, and type' },
    { number: 2, title: 'Trigger', description: 'When to execute the rule' },
    { number: 3, title: 'Conditions', description: 'Optional conditions to check' },
    { number: 4, title: 'Actions', description: 'What actions to perform' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <CogIcon className="h-6 w-6 mr-2 text-blue-600" />
              Create Automation Rule
            </h2>
            <p className="text-sm text-gray-600">Set up automated actions and workflows</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= step.number
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step.number}
                </div>
                <div className="ml-3">
                  <div className="text-sm font-medium text-gray-900">{step.title}</div>
                  <div className="text-xs text-gray-600">{step.description}</div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    currentStep > step.number ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {/* Step 1: Basic Info */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rule Name *
                </label>
                <input
                  type="text"
                  value={ruleData.name}
                  onChange={(e) => setRuleData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter rule name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  value={ruleData.description}
                  onChange={(e) => setRuleData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what this rule does"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Automation Type
                </label>
                <div className="space-y-2">
                  {automationTypes.map((type) => (
                    <label key={type.value} className="flex items-center">
                      <input
                        type="radio"
                        name="type"
                        value={type.value}
                        checked={ruleData.type === type.value}
                        onChange={(e) => setRuleData(prev => ({ ...prev, type: e.target.value as AutomationType }))}
                        className="mr-3 text-blue-600 focus:ring-blue-500"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{type.label}</div>
                        <div className="text-xs text-gray-600">{type.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority (1-10)
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={ruleData.priority}
                  onChange={(e) => setRuleData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                />
                <div className="text-xs text-gray-500 mt-1">Higher numbers = higher priority</div>
              </div>
            </div>
          )}

          {/* Step 2: Trigger */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Trigger Type
                </label>
                <div className="space-y-2">
                  {triggerTypes.map((trigger) => (
                    <label key={trigger.value} className="flex items-center">
                      <input
                        type="radio"
                        name="triggerType"
                        value={trigger.value}
                        checked={ruleData.trigger.type === trigger.value}
                        onChange={(e) => setRuleData(prev => ({
                          ...prev,
                          trigger: { ...prev.trigger, type: e.target.value as TriggerType }
                        }))}
                        className="mr-3 text-blue-600 focus:ring-blue-500"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{trigger.label}</div>
                        <div className="text-xs text-gray-600">{trigger.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Trigger Parameters */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Trigger Parameters
                </label>
                <div className="space-y-2">
                  {ruleData.trigger.type === TriggerType.PRICE_CHANGE && (
                    <>
                      <input
                        type="text"
                        placeholder="Asset ID or symbol"
                        onChange={(e) => setRuleData(prev => ({
                          ...prev,
                          trigger: {
                            ...prev.trigger,
                            parameters: { ...prev.trigger.parameters, asset: e.target.value }
                          }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                      <input
                        type="number"
                        placeholder="Percentage change (e.g., 5 for 5%)"
                        onChange={(e) => setRuleData(prev => ({
                          ...prev,
                          trigger: {
                            ...prev.trigger,
                            parameters: { ...prev.trigger.parameters, percentage: parseFloat(e.target.value) }
                          }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </>
                  )}

                  {ruleData.trigger.type === TriggerType.TIME_BASED && (
                    <input
                      type="text"
                      placeholder="Cron expression (e.g., 0 9 * * 1-5)"
                      onChange={(e) => setRuleData(prev => ({
                        ...prev,
                        trigger: {
                          ...prev.trigger,
                          schedule: e.target.value,
                          parameters: { ...prev.trigger.parameters, schedule: e.target.value }
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  )}

                  {ruleData.trigger.type === TriggerType.THRESHOLD_REACHED && (
                    <>
                      <input
                        type="text"
                        placeholder="Metric name"
                        onChange={(e) => setRuleData(prev => ({
                          ...prev,
                          trigger: {
                            ...prev.trigger,
                            parameters: { ...prev.trigger.parameters, metric: e.target.value }
                          }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                      <input
                        type="number"
                        placeholder="Threshold value"
                        onChange={(e) => setRuleData(prev => ({
                          ...prev,
                          trigger: {
                            ...prev.trigger,
                            parameters: { ...prev.trigger.parameters, threshold: parseFloat(e.target.value) }
                          }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Conditions */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-900">Conditions (Optional)</h3>
                <button
                  type="button"
                  onClick={addCondition}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PlusIcon className="h-3 w-3 mr-1" />
                  Add Condition
                </button>
              </div>

              {ruleData.conditions.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p className="text-sm">No conditions added. The rule will execute whenever the trigger fires.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {ruleData.conditions.map((condition, index) => (
                    <div key={index} className="flex items-center space-x-2 p-3 border border-gray-200 rounded">
                      {index > 0 && (
                        <select
                          value={condition.logicalOperator || LogicalOperator.AND}
                          onChange={(e) => updateCondition(index, 'logicalOperator', e.target.value as LogicalOperator)}
                          className="px-2 py-1 border border-gray-300 rounded text-xs focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value={LogicalOperator.AND}>AND</option>
                          <option value={LogicalOperator.OR}>OR</option>
                        </select>
                      )}

                      <input
                        type="text"
                        placeholder="Field"
                        value={condition.field}
                        onChange={(e) => updateCondition(index, 'field', e.target.value)}
                        className="flex-1 px-2 py-1 border border-gray-300 rounded text-xs focus:ring-blue-500 focus:border-blue-500"
                      />

                      <select
                        value={condition.operator}
                        onChange={(e) => updateCondition(index, 'operator', e.target.value as ConditionOperator)}
                        className="px-2 py-1 border border-gray-300 rounded text-xs focus:ring-blue-500 focus:border-blue-500"
                      >
                        {conditionOperators.map((op) => (
                          <option key={op.value} value={op.value}>{op.label}</option>
                        ))}
                      </select>

                      <input
                        type="text"
                        placeholder="Value"
                        value={String(condition.value)}
                        onChange={(e) => updateCondition(index, 'value', e.target.value)}
                        className="flex-1 px-2 py-1 border border-gray-300 rounded text-xs focus:ring-blue-500 focus:border-blue-500"
                      />

                      <button
                        type="button"
                        onClick={() => removeCondition(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Step 4: Actions */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-900">Actions *</h3>
                <button
                  type="button"
                  onClick={addAction}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PlusIcon className="h-3 w-3 mr-1" />
                  Add Action
                </button>
              </div>

              {ruleData.actions.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p className="text-sm">No actions added. Please add at least one action.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {ruleData.actions.map((action, index) => (
                    <div key={index} className="p-3 border border-gray-200 rounded space-y-2">
                      <div className="flex items-center justify-between">
                        <select
                          value={action.type}
                          onChange={(e) => updateAction(index, 'type', e.target.value as ActionType)}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm focus:ring-blue-500 focus:border-blue-500"
                        >
                          {actionTypes.map((actionType) => (
                            <option key={actionType.value} value={actionType.value}>
                              {actionType.label}
                            </option>
                          ))}
                        </select>

                        <button
                          type="button"
                          onClick={() => removeAction(index)}
                          className="ml-2 text-red-600 hover:text-red-800"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>

                      {/* Action Parameters */}
                      <div className="space-y-2">
                        {action.type === ActionType.SEND_NOTIFICATION && (
                          <input
                            type="text"
                            placeholder="Notification message"
                            onChange={(e) => updateAction(index, 'parameters', { message: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-blue-500 focus:border-blue-500"
                          />
                        )}

                        {(action.type === ActionType.CREATE_ORDER || action.type === ActionType.CANCEL_ORDER) && (
                          <>
                            <input
                              type="text"
                              placeholder="Asset ID"
                              onChange={(e) => updateAction(index, 'parameters', { 
                                ...action.parameters, 
                                assetId: e.target.value 
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                            <input
                              type="number"
                              placeholder="Amount"
                              onChange={(e) => updateAction(index, 'parameters', { 
                                ...action.parameters, 
                                amount: parseFloat(e.target.value) 
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                          </>
                        )}

                        <input
                          type="number"
                          placeholder="Delay (milliseconds)"
                          value={action.delay || ''}
                          onChange={(e) => updateAction(index, 'delay', parseInt(e.target.value) || undefined)}
                          className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Navigation */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div>
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={() => setCurrentStep(currentStep - 1)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Previous
                </button>
              )}
            </div>

            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>

              {currentStep < 4 ? (
                <button
                  type="button"
                  onClick={() => setCurrentStep(currentStep + 1)}
                  disabled={!canProceed(currentStep)}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={!canProceed(currentStep)}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Create Rule
                </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
