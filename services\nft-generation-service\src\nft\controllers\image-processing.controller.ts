import { <PERSON>, Post, Body, Res, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { ImageProcessingService } from '../services/image-processing.service';

@ApiTags('Images')
@Controller('images')
export class ImageProcessingController {
  private readonly logger = new Logger(ImageProcessingController.name);

  constructor(private readonly imageProcessingService: ImageProcessingService) {}

  @Post('process')
  @ApiOperation({
    summary: 'Process image with AI enhancements',
    description: 'Apply AI-powered image processing and enhancements'
  })
  @ApiBody({
    description: 'Image processing parameters',
    schema: {
      type: 'object',
      properties: {
        imageUrl: { type: 'string', example: 'https://example.com/image.png' },
        operations: {
          type: 'array',
          items: { type: 'string' },
          example: ['resize', 'enhance', 'filter']
        },
        parameters: {
          type: 'object',
          properties: {
            width: { type: 'number' },
            height: { type: 'number' },
            quality: { type: 'number' }
          }
        }
      },
      required: ['imageUrl', 'operations']
    }
  })
  @ApiResponse({ status: 200, description: 'Image processed successfully' })
  async processImage(@Body() processingData: any, @Res() res: Response) {
    try {
      this.logger.log('Processing image with AI enhancements');
      
      const result = await this.imageProcessingService.processImage(processingData);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Image processing failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }
}
