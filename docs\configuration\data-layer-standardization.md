# 🗄️ Data Layer Standardization

**Standardizing data access patterns, repository implementations, and database management across all services while keeping Prisma**

## 📋 Current Data Layer Issues

### **Issues Identified:**
1. **Inconsistent Prisma Service Implementations** across services
2. **Mixed Database Configuration** patterns
3. **No Standardized Repository Pattern** implementation
4. **Inconsistent Transaction Handling** approaches
5. **Limited Query Optimization** and monitoring
6. **No Unified Data Access Layer** interfaces
7. **Inconsistent Error Handling** in data operations
8. **No Standardized Migration Management**
9. **Limited Database Health Monitoring**
10. **No Centralized Database Utilities**
11. **Mixed CQRS Implementation** patterns
12. **No Standardized Caching Strategy**

### **Impact:**
- Inconsistent data access patterns across services
- Difficult debugging of database-related issues
- Poor query performance monitoring
- Inconsistent error handling and transaction management
- Hard to maintain and scale data operations
- No unified approach to database health monitoring

## 🎯 Standardization Objectives

1. **Unified Prisma Service**: Standardized Prisma client configuration and lifecycle
2. **Repository Pattern**: Consistent repository interfaces and implementations
3. **Transaction Management**: Standardized transaction handling across services
4. **Query Optimization**: Performance monitoring and query optimization
5. **Error Handling**: Unified database error handling and recovery
6. **Health Monitoring**: Comprehensive database health checks
7. **Migration Management**: Standardized database migration workflows
8. **Caching Strategy**: Unified caching patterns for data access

## 🏗️ Standardized Data Layer Architecture

### **Data Layer Structure**
```
shared/data/                          # Shared data infrastructure
├── interfaces/                       # Data access interfaces
│   ├── repository.interface.ts      # Base repository interface
│   ├── transaction.interface.ts     # Transaction management interface
│   └── query.interface.ts           # Query builder interface
├── services/                         # Data services
│   ├── prisma.service.ts            # Standardized Prisma service
│   ├── transaction.service.ts       # Transaction management service
│   └── query-optimizer.service.ts   # Query optimization service
├── repositories/                     # Base repository implementations
│   ├── base.repository.ts           # Abstract base repository
│   └── cqrs.repository.ts           # CQRS repository implementation
├── decorators/                       # Data decorators
│   ├── transactional.decorator.ts   # Transaction decorator
│   └── cached.decorator.ts          # Caching decorator
├── interceptors/                     # Data interceptors
│   ├── query-logging.interceptor.ts # Query performance logging
│   └── cache.interceptor.ts         # Caching interceptor
├── utils/                           # Data utilities
│   ├── query-builder.util.ts       # Query building utilities
│   ├── migration.util.ts           # Migration utilities
│   └── health-check.util.ts        # Database health utilities
└── types/                           # Data types
    ├── pagination.types.ts          # Pagination types
    └── query.types.ts               # Query types
```

## 📝 Core Data Interfaces

### **Base Repository Interface**
```typescript
// shared/data/interfaces/repository.interface.ts
export interface IBaseRepository<T, CreateDto, UpdateDto> {
  // Basic CRUD operations
  findById(id: string, options?: FindOptions): Promise<T | null>;
  findMany(criteria?: FindCriteria<T>, options?: FindOptions): Promise<T[]>;
  findOne(criteria: FindCriteria<T>, options?: FindOptions): Promise<T | null>;
  create(data: CreateDto, options?: CreateOptions): Promise<T>;
  update(id: string, data: UpdateDto, options?: UpdateOptions): Promise<T>;
  delete(id: string, options?: DeleteOptions): Promise<boolean>;
  
  // Bulk operations
  createMany(data: CreateDto[], options?: CreateManyOptions): Promise<T[]>;
  updateMany(criteria: FindCriteria<T>, data: Partial<UpdateDto>, options?: UpdateManyOptions): Promise<number>;
  deleteMany(criteria: FindCriteria<T>, options?: DeleteManyOptions): Promise<number>;
  
  // Advanced queries
  count(criteria?: FindCriteria<T>): Promise<number>;
  exists(criteria: FindCriteria<T>): Promise<boolean>;
  paginate(criteria?: FindCriteria<T>, pagination?: PaginationOptions): Promise<PaginatedResult<T>>;
  
  // Transaction support
  withTransaction<R>(fn: (repository: this) => Promise<R>): Promise<R>;
  
  // Health and monitoring
  healthCheck(): Promise<HealthCheckResult>;
}

export interface FindOptions {
  include?: Record<string, boolean | FindOptions>;
  select?: Record<string, boolean>;
  orderBy?: Record<string, 'asc' | 'desc'>;
  cache?: CacheOptions;
  timeout?: number;
}

export interface FindCriteria<T> {
  where?: Partial<T> & {
    AND?: FindCriteria<T>[];
    OR?: FindCriteria<T>[];
    NOT?: FindCriteria<T>;
  };
}

export interface PaginationOptions {
  page: number;
  limit: number;
  cursor?: string;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    nextCursor?: string;
    prevCursor?: string;
  };
}
```

### **Transaction Management Interface**
```typescript
// shared/data/interfaces/transaction.interface.ts
export interface ITransactionManager {
  // Transaction lifecycle
  begin(options?: TransactionOptions): Promise<ITransaction>;
  commit(transaction: ITransaction): Promise<void>;
  rollback(transaction: ITransaction): Promise<void>;
  
  // Transaction execution
  execute<T>(fn: (tx: ITransaction) => Promise<T>, options?: TransactionOptions): Promise<T>;
  
  // Nested transactions
  savepoint(transaction: ITransaction, name: string): Promise<void>;
  rollbackToSavepoint(transaction: ITransaction, name: string): Promise<void>;
  releaseSavepoint(transaction: ITransaction, name: string): Promise<void>;
}

export interface ITransaction {
  id: string;
  status: TransactionStatus;
  startTime: Date;
  isolationLevel: IsolationLevel;
  readonly: boolean;
  
  // Transaction operations
  query<T>(sql: string, params?: any[]): Promise<T[]>;
  execute(sql: string, params?: any[]): Promise<number>;
  
  // Repository access within transaction
  getRepository<T>(repositoryClass: new (...args: any[]) => T): T;
}

export enum TransactionStatus {
  ACTIVE = 'active',
  COMMITTED = 'committed',
  ROLLED_BACK = 'rolled_back',
  FAILED = 'failed',
}

export enum IsolationLevel {
  READ_UNCOMMITTED = 'READ_UNCOMMITTED',
  READ_COMMITTED = 'READ_COMMITTED',
  REPEATABLE_READ = 'REPEATABLE_READ',
  SERIALIZABLE = 'SERIALIZABLE',
}

export interface TransactionOptions {
  isolationLevel?: IsolationLevel;
  timeout?: number;
  readonly?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
}
```

### **Query Builder Interface**
```typescript
// shared/data/interfaces/query.interface.ts
export interface IQueryBuilder<T> {
  // Selection
  select(fields: (keyof T)[]): this;
  include(relations: Record<string, boolean | IQueryBuilder<any>>): this;
  
  // Filtering
  where(criteria: FindCriteria<T>): this;
  and(criteria: FindCriteria<T>): this;
  or(criteria: FindCriteria<T>): this;
  not(criteria: FindCriteria<T>): this;
  
  // Sorting
  orderBy(field: keyof T, direction: 'asc' | 'desc'): this;
  orderByRaw(sql: string): this;
  
  // Pagination
  skip(count: number): this;
  take(count: number): this;
  cursor(cursor: any): this;
  
  // Grouping
  groupBy(fields: (keyof T)[]): this;
  having(criteria: FindCriteria<T>): this;
  
  // Aggregation
  count(field?: keyof T): Promise<number>;
  sum(field: keyof T): Promise<number>;
  avg(field: keyof T): Promise<number>;
  min(field: keyof T): Promise<any>;
  max(field: keyof T): Promise<any>;
  
  // Execution
  findMany(): Promise<T[]>;
  findFirst(): Promise<T | null>;
  findUnique(): Promise<T | null>;
  
  // Raw queries
  raw(sql: string, params?: any[]): Promise<any[]>;
  
  // Performance
  explain(): Promise<QueryExplanation>;
  timeout(ms: number): this;
  cache(options: CacheOptions): this;
}

export interface QueryExplanation {
  plan: string;
  executionTime: number;
  cost: number;
  rows: number;
  indexes: string[];
}

export interface CacheOptions {
  key?: string;
  ttl?: number;
  tags?: string[];
  invalidateOn?: string[];
}
```

## 🔧 Standardized Prisma Service

### **Enterprise Prisma Service**
```typescript
// shared/data/services/prisma.service.ts
import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaClient, Prisma } from '@prisma/client';
import { MetricsService } from '../../logging/services/metrics.service';
import { ServiceLoggerService } from '../../logging/services/service-logger.service';

@Injectable()
export class StandardizedPrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(StandardizedPrismaService.name);
  private readonly serviceName: string;
  private connectionRetries = 0;
  private readonly maxRetries = 5;

  constructor(
    private readonly configService: ConfigService,
    private readonly metricsService: MetricsService,
    private readonly serviceLogger: ServiceLoggerService,
  ) {
    super({
      datasources: {
        db: {
          url: configService.get<string>('DATABASE_URL'),
        },
      },
      log: [
        { level: 'query', emit: 'event' },
        { level: 'error', emit: 'event' },
        { level: 'info', emit: 'event' },
        { level: 'warn', emit: 'event' },
      ],
      errorFormat: 'pretty',
    });

    this.serviceName = this.configService.get<string>('SERVICE_NAME') || 'unknown-service';
    this.setupEventListeners();
  }

  async onModuleInit() {
    await this.connectWithRetry();
    this.setupHealthMonitoring();
  }

  async onModuleDestroy() {
    await this.gracefulDisconnect();
  }

  private async connectWithRetry(): Promise<void> {
    try {
      await this.$connect();
      this.logger.log(`✅ Connected to PostgreSQL database (${this.serviceName})`);
      this.connectionRetries = 0;
      
      // Record successful connection
      this.metricsService.gauge('database_connection_status', 1, {
        service: this.serviceName,
        database: 'postgresql',
      });
      
      this.serviceLogger.logOperationComplete('database_connection', 0, true);
    } catch (error) {
      this.connectionRetries++;
      this.logger.error(`❌ Failed to connect to database (attempt ${this.connectionRetries}/${this.maxRetries}):`, error);
      
      // Record failed connection
      this.metricsService.gauge('database_connection_status', 0, {
        service: this.serviceName,
        database: 'postgresql',
      });
      
      this.serviceLogger.logOperationComplete('database_connection', 0, false, {
        error: error.message,
        attempt: this.connectionRetries,
      });

      if (this.connectionRetries < this.maxRetries) {
        const delay = Math.pow(2, this.connectionRetries) * 1000; // Exponential backoff
        this.logger.log(`Retrying connection in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.connectWithRetry();
      } else {
        throw error;
      }
    }
  }

  private async gracefulDisconnect(): Promise<void> {
    try {
      await this.$disconnect();
      this.logger.log('🔌 Disconnected from PostgreSQL database');
      
      this.metricsService.gauge('database_connection_status', 0, {
        service: this.serviceName,
        database: 'postgresql',
      });
    } catch (error) {
      this.logger.error('Error during database disconnection:', error);
    }
  }

  private setupEventListeners(): void {
    // Query logging and metrics
    this.$on('query', (e) => {
      const duration = e.duration;
      
      // Log slow queries
      if (duration > 1000) {
        this.serviceLogger.getStructuredLogger().warn(
          `Slow query detected: ${e.query} (${duration}ms)`,
          {
            operation: 'database_query',
            performance: {
              startTime: Date.now() - duration,
              endTime: Date.now(),
              duration,
              dbQueryCount: 1,
              dbQueryTime: duration,
            },
            metadata: {
              query: e.query,
              params: e.params,
              target: e.target,
              slow: true,
            },
          }
        );
      }

      // Record query metrics
      this.metricsService.histogram('database_query_duration_seconds', duration / 1000, {
        service: this.serviceName,
        operation: this.extractOperationType(e.query),
        target: e.target,
      });

      this.metricsService.increment('database_queries_total', {
        service: this.serviceName,
        operation: this.extractOperationType(e.query),
        target: e.target,
      });
    });

    // Error logging
    this.$on('error', (e) => {
      this.serviceLogger.getStructuredLogger().error(
        `Database error: ${e.message}`,
        e,
        {
          operation: 'database_operation',
          error: {
            name: 'DatabaseError',
            message: e.message,
            code: 'DATABASE_ERROR',
          },
          metadata: {
            target: e.target,
            timestamp: new Date().toISOString(),
          },
        }
      );

      this.metricsService.increment('database_errors_total', {
        service: this.serviceName,
        error_type: 'database_error',
      });
    });

    // Info and warn logging
    this.$on('info', (e) => {
      this.serviceLogger.getStructuredLogger().info(`Database info: ${e.message}`, {
        metadata: { target: e.target },
      });
    });

    this.$on('warn', (e) => {
      this.serviceLogger.getStructuredLogger().warn(`Database warning: ${e.message}`, {
        metadata: { target: e.target },
      });
    });
  }

  private extractOperationType(query: string): string {
    const normalizedQuery = query.trim().toLowerCase();
    if (normalizedQuery.startsWith('select')) return 'select';
    if (normalizedQuery.startsWith('insert')) return 'insert';
    if (normalizedQuery.startsWith('update')) return 'update';
    if (normalizedQuery.startsWith('delete')) return 'delete';
    if (normalizedQuery.startsWith('create')) return 'create';
    if (normalizedQuery.startsWith('drop')) return 'drop';
    if (normalizedQuery.startsWith('alter')) return 'alter';
    return 'other';
  }

  private setupHealthMonitoring(): void {
    // Periodic health checks
    setInterval(async () => {
      try {
        const startTime = Date.now();
        await this.$queryRaw`SELECT 1 as health_check`;
        const responseTime = Date.now() - startTime;

        this.metricsService.gauge('database_health_response_time', responseTime, {
          service: this.serviceName,
          database: 'postgresql',
        });

        this.metricsService.gauge('database_health_status', 1, {
          service: this.serviceName,
          database: 'postgresql',
        });
      } catch (error) {
        this.metricsService.gauge('database_health_status', 0, {
          service: this.serviceName,
          database: 'postgresql',
        });

        this.logger.error('Database health check failed:', error);
      }
    }, 30000); // Every 30 seconds
  }

  // Enhanced query methods with monitoring
  async queryWithMetrics<T>(query: string, params?: any[]): Promise<T[]> {
    const startTime = Date.now();
    try {
      const result = await this.$queryRawUnsafe<T[]>(query, ...params || []);
      const duration = Date.now() - startTime;

      this.serviceLogger.logDatabaseOperation('raw_query', 'custom', duration, true);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.serviceLogger.logDatabaseOperation('raw_query', 'custom', duration, false);
      throw error;
    }
  }

  // Transaction with monitoring
  async transactionWithMetrics<T>(fn: (prisma: Prisma.TransactionClient) => Promise<T>): Promise<T> {
    const startTime = Date.now();
    try {
      const result = await this.$transaction(fn);
      const duration = Date.now() - startTime;

      this.serviceLogger.logDatabaseOperation('transaction', 'multiple', duration, true);
      this.metricsService.increment('database_transactions_total', {
        service: this.serviceName,
        status: 'success',
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.serviceLogger.logDatabaseOperation('transaction', 'multiple', duration, false);
      this.metricsService.increment('database_transactions_total', {
        service: this.serviceName,
        status: 'error',
      });
      throw error;
    }
  }

  // Health check method
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    responseTime: number;
    details: any;
  }> {
    try {
      const startTime = Date.now();
      await this.$queryRaw`SELECT 1 as health_check`;
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime,
        details: {
          database: 'postgresql',
          service: this.serviceName,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: -1,
        details: {
          database: 'postgresql',
          service: this.serviceName,
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }
}
```

## 🚀 Implementation Plan

### **Phase 1: Create Shared Data Infrastructure**
1. Create shared data interfaces and base classes
2. Implement standardized Prisma service
3. Create base repository implementations
4. Implement transaction management utilities

### **Phase 2: Standardize Service Data Layers**
1. Update all services to use standardized Prisma service
2. Implement repository pattern across services
3. Add transaction management and error handling
4. Update database health monitoring

### **Phase 3: Advanced Data Features**
1. Implement query optimization and monitoring
2. Add caching strategies and decorators
3. Create migration management utilities
4. Add performance monitoring and alerting

### **Phase 4: Data Analytics and Insights**
1. Implement query performance analytics
2. Add database usage insights
3. Create data access pattern analysis
4. Add capacity planning metrics

## 📊 Benefits of Standardization

1. **Consistent Data Access**: Unified patterns across all services
2. **Performance Monitoring**: Comprehensive query and transaction monitoring
3. **Error Handling**: Standardized database error handling and recovery
4. **Health Monitoring**: Proactive database health monitoring
5. **Query Optimization**: Automatic slow query detection and optimization
6. **Transaction Safety**: Reliable transaction management with retry logic
7. **Caching Strategy**: Unified caching patterns for improved performance

## 🔍 Next Steps

1. **Create Shared Data Infrastructure**
2. **Implement Standardized Prisma Services**
3. **Update All Service Data Layers**
4. **Add Performance Monitoring**
5. **Test Data Layer Standardization**
