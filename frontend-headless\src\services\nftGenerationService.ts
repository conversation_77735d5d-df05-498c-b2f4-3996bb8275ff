import { api } from '@/lib/api'
import {
  NFTGenerationRequest,
  GenerationQueue,
  GenerationBatch,
  GenerationStats,
  CreateGenerationRequestData,
  UpdateGenerationRequestData,
  GenerationFilters,
  QueueConfiguration,
  NFTGenerationStatus,
  GenerationProvider,
  ReviewData
} from '@/types/nft-generation.types'

export class NFTGenerationService {
  // ===== GENERATION REQUEST MANAGEMENT =====
  
  async getGenerationRequests(filters?: GenerationFilters): Promise<{
    requests: NFTGenerationRequest[]
    total: number
    page: number
    limit: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get('/nft-generation/requests', { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch generation requests:', error)
      throw new Error('Failed to load generation requests')
    }
  }

  async getGenerationRequest(id: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.get(`/nft-generation/requests/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch generation request:', error)
      throw new Error('Failed to load generation request details')
    }
  }

  async createGenerationRequest(data: CreateGenerationRequestData): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post('/nft-generation/requests', data)
      return response.data
    } catch (error) {
      console.error('Failed to create generation request:', error)
      throw new Error('Failed to create generation request')
    }
  }

  async updateGenerationRequest(id: string, data: UpdateGenerationRequestData): Promise<NFTGenerationRequest> {
    try {
      const response = await api.patch(`/nft-generation/requests/${id}`, data)
      return response.data
    } catch (error) {
      console.error('Failed to update generation request:', error)
      throw new Error('Failed to update generation request')
    }
  }

  async deleteGenerationRequest(id: string): Promise<void> {
    try {
      await api.delete(`/nft-generation/requests/${id}`)
    } catch (error) {
      console.error('Failed to delete generation request:', error)
      throw new Error('Failed to delete generation request')
    }
  }

  // ===== GENERATION CONTROL =====

  async startGeneration(id: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${id}/start`)
      return response.data
    } catch (error) {
      console.error('Failed to start generation:', error)
      throw new Error('Failed to start generation')
    }
  }

  async cancelGeneration(id: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${id}/cancel`)
      return response.data
    } catch (error) {
      console.error('Failed to cancel generation:', error)
      throw new Error('Failed to cancel generation')
    }
  }

  async retryGeneration(id: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${id}/retry`)
      return response.data
    } catch (error) {
      console.error('Failed to retry generation:', error)
      throw new Error('Failed to retry generation')
    }
  }

  async selectImage(requestId: string, imageId: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${requestId}/select-image`, { imageId })
      return response.data
    } catch (error) {
      console.error('Failed to select image:', error)
      throw new Error('Failed to select image')
    }
  }

  // ===== REVIEW WORKFLOW =====

  async submitForReview(id: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${id}/submit-review`)
      return response.data
    } catch (error) {
      console.error('Failed to submit for review:', error)
      throw new Error('Failed to submit for review')
    }
  }

  async reviewGeneration(id: string, reviewData: ReviewData): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${id}/review`, reviewData)
      return response.data
    } catch (error) {
      console.error('Failed to review generation:', error)
      throw new Error('Failed to review generation')
    }
  }

  async approveGeneration(id: string, feedback?: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${id}/approve`, { feedback })
      return response.data
    } catch (error) {
      console.error('Failed to approve generation:', error)
      throw new Error('Failed to approve generation')
    }
  }

  async rejectGeneration(id: string, reason: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${id}/reject`, { reason })
      return response.data
    } catch (error) {
      console.error('Failed to reject generation:', error)
      throw new Error('Failed to reject generation')
    }
  }

  // ===== MINTING OPERATIONS =====

  async startMinting(id: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${id}/mint`)
      return response.data
    } catch (error) {
      console.error('Failed to start minting:', error)
      throw new Error('Failed to start minting')
    }
  }

  async getMintingStatus(id: string): Promise<{
    status: string
    transactionHash?: string
    tokenId?: string
    progress: number
  }> {
    try {
      const response = await api.get(`/nft-generation/requests/${id}/minting-status`)
      return response.data
    } catch (error) {
      console.error('Failed to get minting status:', error)
      throw new Error('Failed to get minting status')
    }
  }

  async distributeNFT(id: string): Promise<NFTGenerationRequest> {
    try {
      const response = await api.post(`/nft-generation/requests/${id}/distribute`)
      return response.data
    } catch (error) {
      console.error('Failed to distribute NFT:', error)
      throw new Error('Failed to distribute NFT')
    }
  }

  // ===== QUEUE MANAGEMENT =====

  async getQueues(): Promise<GenerationQueue[]> {
    try {
      const response = await api.get('/nft-generation/queues')
      return response.data
    } catch (error) {
      console.error('Failed to fetch queues:', error)
      throw new Error('Failed to load queues')
    }
  }

  async getQueue(id: string): Promise<GenerationQueue> {
    try {
      const response = await api.get(`/nft-generation/queues/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch queue:', error)
      throw new Error('Failed to load queue details')
    }
  }

  async createQueue(data: {
    name: string
    campaignId: string
    provider: GenerationProvider
    maxConcurrent: number
    priority: number
  }): Promise<GenerationQueue> {
    try {
      const response = await api.post('/nft-generation/queues', data)
      return response.data
    } catch (error) {
      console.error('Failed to create queue:', error)
      throw new Error('Failed to create queue')
    }
  }

  async updateQueue(id: string, data: Partial<GenerationQueue>): Promise<GenerationQueue> {
    try {
      const response = await api.patch(`/nft-generation/queues/${id}`, data)
      return response.data
    } catch (error) {
      console.error('Failed to update queue:', error)
      throw new Error('Failed to update queue')
    }
  }

  async pauseQueue(id: string): Promise<GenerationQueue> {
    try {
      const response = await api.post(`/nft-generation/queues/${id}/pause`)
      return response.data
    } catch (error) {
      console.error('Failed to pause queue:', error)
      throw new Error('Failed to pause queue')
    }
  }

  async resumeQueue(id: string): Promise<GenerationQueue> {
    try {
      const response = await api.post(`/nft-generation/queues/${id}/resume`)
      return response.data
    } catch (error) {
      console.error('Failed to resume queue:', error)
      throw new Error('Failed to resume queue')
    }
  }

  async deleteQueue(id: string): Promise<void> {
    try {
      await api.delete(`/nft-generation/queues/${id}`)
    } catch (error) {
      console.error('Failed to delete queue:', error)
      throw new Error('Failed to delete queue')
    }
  }

  // ===== BATCH OPERATIONS =====

  async getBatches(queueId?: string): Promise<GenerationBatch[]> {
    try {
      const response = await api.get('/nft-generation/batches', { 
        params: queueId ? { queueId } : undefined 
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch batches:', error)
      throw new Error('Failed to load batches')
    }
  }

  async getBatch(id: string): Promise<GenerationBatch> {
    try {
      const response = await api.get(`/nft-generation/batches/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch batch:', error)
      throw new Error('Failed to load batch details')
    }
  }

  async createBatch(data: {
    queueId: string
    campaignId: string
    requestIds: string[]
    priority?: number
  }): Promise<GenerationBatch> {
    try {
      const response = await api.post('/nft-generation/batches', data)
      return response.data
    } catch (error) {
      console.error('Failed to create batch:', error)
      throw new Error('Failed to create batch')
    }
  }

  async processBatch(id: string): Promise<GenerationBatch> {
    try {
      const response = await api.post(`/nft-generation/batches/${id}/process`)
      return response.data
    } catch (error) {
      console.error('Failed to process batch:', error)
      throw new Error('Failed to process batch')
    }
  }

  // ===== ANALYTICS AND STATS =====

  async getGenerationStats(filters?: {
    campaignId?: string
    timeframe?: '24h' | '7d' | '30d' | '90d'
    provider?: GenerationProvider
  }): Promise<GenerationStats> {
    try {
      const response = await api.get('/nft-generation/stats', { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch generation stats:', error)
      throw new Error('Failed to load generation statistics')
    }
  }

  async getProviderStats(): Promise<Record<GenerationProvider, {
    totalRequests: number
    successRate: number
    averageTime: number
    averageCost: number
    qualityScore: number
  }>> {
    try {
      const response = await api.get('/nft-generation/provider-stats')
      return response.data
    } catch (error) {
      console.error('Failed to fetch provider stats:', error)
      throw new Error('Failed to load provider statistics')
    }
  }

  async getCampaignGenerationStats(campaignId: string): Promise<{
    totalRequests: number
    completedRequests: number
    pendingRequests: number
    failedRequests: number
    averageQuality: number
    totalCost: number
    estimatedCompletion: string
  }> {
    try {
      const response = await api.get(`/nft-generation/campaigns/${campaignId}/stats`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign generation stats:', error)
      throw new Error('Failed to load campaign generation statistics')
    }
  }

  // ===== CONFIGURATION =====

  async getQueueConfiguration(): Promise<QueueConfiguration> {
    try {
      const response = await api.get('/nft-generation/config')
      return response.data
    } catch (error) {
      console.error('Failed to fetch queue configuration:', error)
      throw new Error('Failed to load queue configuration')
    }
  }

  async updateQueueConfiguration(config: Partial<QueueConfiguration>): Promise<QueueConfiguration> {
    try {
      const response = await api.patch('/nft-generation/config', config)
      return response.data
    } catch (error) {
      console.error('Failed to update queue configuration:', error)
      throw new Error('Failed to update queue configuration')
    }
  }

  // ===== UTILITY METHODS =====

  async validatePrompt(prompt: string): Promise<{
    valid: boolean
    suggestions: string[]
    issues: string[]
    enhancedPrompt: string
  }> {
    try {
      const response = await api.post('/nft-generation/validate-prompt', { prompt })
      return response.data
    } catch (error) {
      console.error('Failed to validate prompt:', error)
      throw new Error('Failed to validate prompt')
    }
  }

  async estimateCost(data: CreateGenerationRequestData): Promise<{
    estimatedCost: number
    breakdown: Record<string, number>
    currency: string
  }> {
    try {
      const response = await api.post('/nft-generation/estimate-cost', data)
      return response.data
    } catch (error) {
      console.error('Failed to estimate cost:', error)
      throw new Error('Failed to estimate generation cost')
    }
  }

  async getProviderCapabilities(provider: GenerationProvider): Promise<{
    maxResolution: { width: number; height: number }
    supportedStyles: string[]
    supportedFormats: string[]
    features: string[]
    pricing: Record<string, number>
  }> {
    try {
      const response = await api.get(`/nft-generation/providers/${provider}/capabilities`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch provider capabilities:', error)
      throw new Error('Failed to load provider capabilities')
    }
  }

  async exportGenerationData(filters?: GenerationFilters, format: 'json' | 'csv' | 'pdf' = 'csv'): Promise<Blob> {
    try {
      const response = await api.get('/nft-generation/export', {
        params: { ...filters, format },
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to export generation data:', error)
      throw new Error('Failed to export generation data')
    }
  }
}

export const nftGenerationService = new NFTGenerationService()
