import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import axios from 'axios';

export interface ProfileAnalysisData {
  id: string;
  userId: string;
  twitterHandle: string;
  score: number;
  status: string;
  analysisData: {
    profile: {
      followerCount: number;
      followingCount: number;
      tweetCount: number;
      engagementRate: number;
      accountAge: number;
      isVerified: boolean;
      hasProfileImage: boolean;
      hasBio: boolean;
    };
    metrics: {
      contentQuality: number;
      activityLevel: number;
      influenceScore: number;
      authenticity: number;
      engagement: number;
    };
    breakdown: {
      followerScore: number;
      engagementScore: number;
      contentScore: number;
      activityScore: number;
      profileScore: number;
    };
    nftRecommendation: {
      score: number;
      rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
      reasoning: string[];
    };
  };
}

export interface GenerateNftFromAnalysisDto {
  userId: string;
  analysisId: string;
  campaignId?: string;
  customization?: {
    backgroundColor?: string;
    style?: 'modern' | 'classic' | 'artistic' | 'minimal';
    theme?: 'tech' | 'nature' | 'abstract' | 'social';
  };
}

export interface NFTGenerationResult {
  id: string;
  userId: string;
  analysisId: string;
  name: string;
  description: string;
  rarity: string;
  score: number;
  attributes: NFTAttribute[];
  metadata: NFTMetadata;
  imageUrl: string;
  status: 'pending' | 'generating' | 'completed' | 'failed';
  createdAt: Date;
  updatedAt: Date;
}

export interface NFTAttribute {
  trait_type: string;
  value: string | number;
  display_type?: 'number' | 'boost_percentage' | 'boost_number' | 'date';
  max_value?: number;
}

export interface NFTMetadata {
  name: string;
  description: string;
  image: string;
  external_url: string;
  attributes: NFTAttribute[];
  background_color: string;
  animation_url?: string;
  youtube_url?: string;
}

@Injectable()
export class ProfileNftGenerationService {
  private readonly logger = new Logger(ProfileNftGenerationService.name);
  private readonly profileAnalysisServiceUrl: string;

  constructor(private readonly prisma: PrismaService) {
    this.profileAnalysisServiceUrl = process.env.PROFILE_ANALYSIS_SERVICE_URL || 'http://localhost:3002';
  }

  /**
   * Generate NFT based on Profile Analysis results
   */
  async generateNftFromAnalysis(generateDto: GenerateNftFromAnalysisDto): Promise<NFTGenerationResult> {
    const startTime = Date.now();
    const correlationId = `nft-gen-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      this.logger.log(`Starting NFT generation from analysis for user: ${generateDto.userId}`, { correlationId });

      // Step 1: Fetch Profile Analysis Data
      const analysisData = await this.fetchProfileAnalysis(generateDto.analysisId, correlationId);
      
      if (!analysisData) {
        throw new NotFoundException(`Profile analysis with ID ${generateDto.analysisId} not found`);
      }

      if (analysisData.userId !== generateDto.userId) {
        throw new BadRequestException(`Analysis ${generateDto.analysisId} does not belong to user ${generateDto.userId}`);
      }

      // Step 2: Generate NFT Attributes based on Analysis
      const attributes = this.generateNftAttributes(analysisData);

      // Step 3: Create NFT Metadata
      const metadata = this.createNftMetadata(analysisData, attributes, generateDto.customization);

      // Step 4: Generate NFT Name and Description
      const { name, description } = this.generateNftNameAndDescription(analysisData);

      // Step 5: Create NFT Record in Database
      const nft = await this.prisma.nftCommand.create({
        data: {
          userId: generateDto.userId,
          campaignId: generateDto.campaignId || 'profile-analysis-campaign',
          templateId: 'profile-analysis-template',
          name,
          description,
          rarity: analysisData.analysisData.nftRecommendation.rarity,
          score: analysisData.score,
          generationStatus: 'GENERATING',
          generationParams: {
            analysisId: generateDto.analysisId,
            customization: generateDto.customization || {},
            generatedAt: new Date().toISOString()
          },
          metadata: metadata as any,
          createdBy: generateDto.userId,
          updatedBy: generateDto.userId,
        },
      });

      // Step 6: Generate Image URL (placeholder for now)
      const imageUrl = await this.generateImageUrl(analysisData, generateDto.customization);

      // Step 7: Update NFT with Image URL and Complete Generation
      const completedNft = await this.prisma.nftCommand.update({
        where: { id: nft.id },
        data: {
          generationStatus: 'COMPLETED',
          metadata: {
            ...metadata,
            image: imageUrl,
          } as any,
          updatedBy: generateDto.userId,
        },
      });

      // Step 8: Create Query Model for Fast Reads
      await this.createQueryModel(completedNft, analysisData);

      const responseTime = Date.now() - startTime;
      this.logger.log(`NFT generation completed successfully in ${responseTime}ms`, { 
        correlationId, 
        nftId: completedNft.id,
        rarity: analysisData.analysisData.nftRecommendation.rarity,
        score: analysisData.score
      });

      return {
        id: completedNft.id,
        userId: completedNft.userId,
        analysisId: generateDto.analysisId,
        name: completedNft.name,
        description: completedNft.description,
        rarity: completedNft.rarity,
        score: analysisData.score,
        attributes,
        metadata: (completedNft.metadata as any) || metadata,
        imageUrl,
        status: 'completed',
        createdAt: completedNft.createdAt,
        updatedAt: completedNft.updatedAt,
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`NFT generation failed after ${responseTime}ms: ${error.message}`, {
        correlationId,
        error: error.stack,
        userId: generateDto.userId,
        analysisId: generateDto.analysisId
      });

      throw error;
    }
  }

  /**
   * Fetch Profile Analysis data from Profile Analysis Service
   */
  private async fetchProfileAnalysis(analysisId: string, correlationId: string): Promise<ProfileAnalysisData | null> {
    try {
      this.logger.debug(`Fetching profile analysis: ${analysisId}`, { correlationId });

      const response = await axios.get(
        `${this.profileAnalysisServiceUrl}/api/analysis/results/${analysisId}`,
        {
          headers: {
            'x-correlation-id': correlationId,
          },
          timeout: 10000,
        }
      );

      if (!response.data || !response.data.success) {
        return null;
      }

      return response.data.data;

    } catch (error) {
      this.logger.error(`Failed to fetch profile analysis: ${error.message}`, { analysisId, correlationId });
      
      if (error.response?.status === 404) {
        return null;
      }
      
      throw new BadRequestException(`Failed to fetch profile analysis: ${error.message}`);
    }
  }

  /**
   * Generate NFT attributes based on analysis data
   */
  private generateNftAttributes(analysisData: ProfileAnalysisData): NFTAttribute[] {
    const attributes: NFTAttribute[] = [];

    // Core Analysis Attributes
    attributes.push(
      { trait_type: 'Overall Score', value: analysisData.score, display_type: 'number', max_value: 100 },
      { trait_type: 'Rarity', value: this.capitalizeRarity(analysisData.analysisData.nftRecommendation.rarity) },
      { trait_type: 'Twitter Handle', value: `@${analysisData.twitterHandle}` }
    );

    // Profile Metrics
    const profile = analysisData.analysisData.profile;
    attributes.push(
      { trait_type: 'Followers', value: profile.followerCount, display_type: 'number' },
      { trait_type: 'Following', value: profile.followingCount, display_type: 'number' },
      { trait_type: 'Tweets', value: profile.tweetCount, display_type: 'number' },
      { trait_type: 'Engagement Rate', value: Math.round(profile.engagementRate * 100) / 100, display_type: 'number' },
      { trait_type: 'Account Age (Days)', value: profile.accountAge, display_type: 'number' },
      { trait_type: 'Verified', value: profile.isVerified ? 'Yes' : 'No' }
    );

    // Analysis Metrics
    const metrics = analysisData.analysisData.metrics;
    attributes.push(
      { trait_type: 'Content Quality', value: metrics.contentQuality, display_type: 'number', max_value: 100 },
      { trait_type: 'Activity Level', value: metrics.activityLevel, display_type: 'number', max_value: 100 },
      { trait_type: 'Influence Score', value: metrics.influenceScore, display_type: 'number', max_value: 100 },
      { trait_type: 'Authenticity', value: metrics.authenticity, display_type: 'number', max_value: 100 },
      { trait_type: 'Engagement', value: metrics.engagement, display_type: 'number', max_value: 100 }
    );

    // Special Attributes based on Performance
    if (profile.isVerified) {
      attributes.push({ trait_type: 'Special', value: 'Verified Account' });
    }

    if (analysisData.score >= 90) {
      attributes.push({ trait_type: 'Achievement', value: 'Elite Performer' });
    } else if (analysisData.score >= 80) {
      attributes.push({ trait_type: 'Achievement', value: 'High Performer' });
    } else if (analysisData.score >= 70) {
      attributes.push({ trait_type: 'Achievement', value: 'Good Performer' });
    }

    // Generation Metadata
    attributes.push(
      { trait_type: 'Generation Date', value: new Date().toISOString().split('T')[0] },
      { trait_type: 'Analysis ID', value: analysisData.id }
    );

    return attributes;
  }

  /**
   * Create NFT metadata
   */
  private createNftMetadata(
    analysisData: ProfileAnalysisData, 
    attributes: NFTAttribute[], 
    customization?: GenerateNftFromAnalysisDto['customization']
  ): NFTMetadata {
    const rarity = analysisData.analysisData.nftRecommendation.rarity;
    const score = analysisData.score;
    
    return {
      name: `${this.capitalizeRarity(rarity)} Social NFT - @${analysisData.twitterHandle}`,
      description: `A ${rarity} NFT representing the social media analysis of @${analysisData.twitterHandle}. This NFT captures their digital presence with a score of ${score}/100, showcasing ${analysisData.analysisData.nftRecommendation.reasoning.join(', ').toLowerCase()}.`,
      image: '', // Will be filled later
      external_url: `${process.env.FRONTEND_URL || 'https://platform.example.com'}/nft/${analysisData.id}`,
      attributes,
      background_color: this.getRarityColor(rarity),
      animation_url: customization?.style === 'artistic' ? `${process.env.FRONTEND_URL}/animations/${analysisData.id}` : undefined,
    };
  }

  /**
   * Generate NFT name and description
   */
  private generateNftNameAndDescription(analysisData: ProfileAnalysisData): { name: string; description: string } {
    const rarity = this.capitalizeRarity(analysisData.analysisData.nftRecommendation.rarity);
    const handle = analysisData.twitterHandle;
    const score = analysisData.score;
    
    const name = `${rarity} Social NFT - @${handle}`;
    
    const description = `This ${rarity.toLowerCase()} NFT represents the comprehensive social media analysis of @${handle}. ` +
      `With an overall score of ${score}/100, this NFT captures their digital influence and engagement patterns. ` +
      `Key highlights: ${analysisData.analysisData.nftRecommendation.reasoning.join(', ').toLowerCase()}. ` +
      `Generated from real-time social media analytics including ${analysisData.analysisData.profile.followerCount.toLocaleString()} followers ` +
      `and ${analysisData.analysisData.profile.tweetCount.toLocaleString()} tweets.`;

    return { name, description };
  }

  /**
   * Generate image URL using real image generation
   */
  private async generateImageUrl(
    analysisData: ProfileAnalysisData,
    customization?: GenerateNftFromAnalysisDto['customization']
  ): Promise<string> {
    try {
      this.logger.log(`Generating real NFT image for analysis ${analysisData.id}`);

      // For now, return a placeholder URL that indicates real image generation
      // In a full implementation, this would integrate with the NFTImageGeneratorService
      // and upload the generated image to external storage

      const rarity = analysisData.analysisData.nftRecommendation.rarity;
      const style = customization?.style || 'modern';
      const theme = customization?.theme || 'social';
      const twitterHandle = analysisData.twitterHandle;

      // Generate a data URL for the SVG image (immediate display)
      const timestamp = Date.now();

      // Get score from analysis data
      const score = analysisData.score || 75;

      // For now, create a simple SVG data URL that can be displayed immediately
      const svgContent = this.generateSimpleSVG(rarity, score, twitterHandle);
      const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svgContent).toString('base64')}`;

      return dataUrl;

    } catch (error) {
      this.logger.error(`Failed to generate image URL: ${error.message}`);

      // Fallback to simple placeholder SVG
      const fallbackSvg = this.generateSimpleSVG('common', 50, analysisData.twitterHandle);
      return `data:image/svg+xml;base64,${Buffer.from(fallbackSvg).toString('base64')}`;
    }
  }

  /**
   * Generate a simple SVG for immediate display
   */
  private generateSimpleSVG(rarity: string, score: number, twitterHandle: string): string {
    const colors = this.getRarityColors(rarity);

    return `
      <svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
        <!-- Background Gradient -->
        <defs>
          <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:${colors.primary};stop-opacity:0.8" />
            <stop offset="70%" style="stop-color:${colors.secondary};stop-opacity:0.4" />
            <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
          </radialGradient>

          <linearGradient id="scoreGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:${colors.primary}" />
            <stop offset="100%" style="stop-color:${colors.accent}" />
          </linearGradient>
        </defs>

        <!-- Background -->
        <rect width="100%" height="100%" fill="url(#bgGradient)" />

        <!-- Decorative Circles -->
        <circle cx="200" cy="200" r="150" fill="none" stroke="${colors.accent}" stroke-width="2" stroke-opacity="0.3" />
        <circle cx="200" cy="200" r="120" fill="none" stroke="${colors.primary}" stroke-width="2" stroke-opacity="0.5" />
        <circle cx="200" cy="200" r="90" fill="none" stroke="${colors.accent}" stroke-width="2" stroke-opacity="0.3" />

        <!-- Score Circle -->
        <circle cx="200" cy="200" r="60" fill="none" stroke="${colors.accent}" stroke-width="6" stroke-opacity="0.3" />
        <circle cx="200" cy="200" r="60" fill="none" stroke="${colors.primary}" stroke-width="6"
                stroke-linecap="round" stroke-dasharray="377"
                stroke-dashoffset="${377 - (score / 100) * 377}"
                transform="rotate(-90 200 200)" />

        <!-- Score Text -->
        <text x="200" y="200" text-anchor="middle" dominant-baseline="middle"
              fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
          ${score}
        </text>

        <!-- Score Label -->
        <text x="200" y="225" text-anchor="middle" dominant-baseline="middle"
              fill="white" font-family="Arial, sans-serif" font-size="12">
          SCORE
        </text>

        <!-- Rarity Badge -->
        <rect x="150" y="50" width="100" height="30" fill="${colors.primary}"
              stroke="${colors.accent}" stroke-width="2" rx="5" />
        <text x="200" y="70" text-anchor="middle" dominant-baseline="middle"
              fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
          ${rarity.toUpperCase()}
        </text>

        <!-- Twitter Handle -->
        <rect x="0" y="350" width="400" height="50" fill="rgba(0, 0, 0, 0.7)" />
        <text x="200" y="380" text-anchor="middle" dominant-baseline="middle"
              fill="${colors.accent}" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
          @${twitterHandle}
        </text>

        <!-- Decorative Elements -->
        <g stroke="${colors.primary}" stroke-width="2" stroke-opacity="0.6" fill="none">
          <line x1="50" y1="50" x2="70" y2="50" />
          <line x1="60" y1="40" x2="60" y2="60" />
          <line x1="330" y1="50" x2="350" y2="50" />
          <line x1="340" y1="40" x2="340" y2="60" />
          <line x1="50" y1="350" x2="70" y2="350" />
          <line x1="60" y1="340" x2="60" y2="360" />
          <line x1="330" y1="350" x2="350" y2="350" />
          <line x1="340" y1="340" x2="340" y2="360" />
        </g>
      </svg>
    `.trim();
  }

  /**
   * Generate image URL for existing NFT from query model
   */
  private async generateImageUrlForExistingNft(nft: any): Promise<string> {
    try {
      // Extract Twitter handle from display name or description
      const twitterHandle = this.extractTwitterHandle(nft.displayName, nft.displayDescription);

      // Generate SVG for existing NFT
      const svgContent = this.generateSimpleSVG(
        nft.rarity || 'common',
        nft.score || 50,
        twitterHandle
      );

      const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svgContent).toString('base64')}`;
      return dataUrl;

    } catch (error) {
      this.logger.error(`Failed to generate image for existing NFT ${nft.id}: ${error.message}`);

      // Fallback to simple placeholder
      const fallbackSvg = this.generateSimpleSVG('common', 50, 'unknown');
      return `data:image/svg+xml;base64,${Buffer.from(fallbackSvg).toString('base64')}`;
    }
  }

  /**
   * Extract Twitter handle from NFT name or description
   */
  private extractTwitterHandle(name: string, description: string): string {
    // Try to extract from name first (format: "Legendary Social NFT - @username")
    const nameMatch = name?.match(/@(\w+)/);
    if (nameMatch) {
      return nameMatch[1];
    }

    // Try to extract from description
    const descMatch = description?.match(/@(\w+)/);
    if (descMatch) {
      return descMatch[1];
    }

    // Fallback
    return 'user';
  }

  /**
   * Get rarity-specific colors
   */
  private getRarityColors(rarity: string) {
    const colorMap = {
      common: { primary: '#6B7280', secondary: '#9CA3AF', accent: '#D1D5DB' },
      rare: { primary: '#3B82F6', secondary: '#60A5FA', accent: '#93C5FD' },
      epic: { primary: '#8B5CF6', secondary: '#A78BFA', accent: '#C4B5FD' },
      legendary: { primary: '#F59E0B', secondary: '#FBBF24', accent: '#FCD34D' },
      mythic: { primary: '#EF4444', secondary: '#F87171', accent: '#FCA5A5' },
    };

    return colorMap[rarity.toLowerCase()] || colorMap.common;
  }

  /**
   * Get rarity background color
   */
  private getRarityColor(rarity: string): string {
    const colors = {
      common: '#808080',
      rare: '#0070f3',
      epic: '#7c3aed',
      legendary: '#f59e0b',
      mythic: '#ef4444',
    };
    return colors[rarity] || colors.common;
  }

  /**
   * Capitalize rarity string
   */
  private capitalizeRarity(rarity: string): string {
    return rarity.charAt(0).toUpperCase() + rarity.slice(1);
  }

  /**
   * Create query model for fast reads
   */
  private async createQueryModel(nft: any, analysisData: ProfileAnalysisData): Promise<void> {
    try {
      await this.prisma.nftQuery.upsert({
        where: { id: nft.id },
        update: {
          displayName: nft.name,
          displayDescription: nft.description,
          userId: nft.userId,
          status: 'completed',
          rarity: nft.rarity,
          score: analysisData.score,
          lastUpdated: new Date(),
        },
        create: {
          id: nft.id,
          displayName: nft.name,
          displayDescription: nft.description,
          userId: nft.userId,
          projectId: nft.projectId,
          campaignId: nft.campaignId,
          status: 'completed',
          rarity: nft.rarity,
          score: analysisData.score,
          viewCount: 0,
          likeCount: 0,
          shareCount: 0,
          isListed: false,
          totalSales: 0,
          popularityScore: analysisData.score / 100,
          createdAt: nft.createdAt,
          lastUpdated: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Failed to create query model: ${error.message}`, { nftId: nft.id });
      // Don't throw - this is not critical for the main flow
    }
  }

  /**
   * Get user's NFT generation history
   */
  async getUserNftHistory(userId: string, limit: number = 10, offset: number = 0): Promise<{
    nfts: NFTGenerationResult[];
    total: number;
  }> {
    try {
      const validLimit = Math.max(1, Math.min(100, Number(limit) || 10));
      const validOffset = Math.max(0, Number(offset) || 0);

      const [nfts, total] = await Promise.all([
        this.prisma.nftQuery.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          take: validLimit,
          skip: validOffset,
        }),
        this.prisma.nftQuery.count({
          where: { userId },
        }),
      ]);

      return {
        nfts: await Promise.all(nfts.map(async (nft) => {
          // Generate image URL for each NFT based on its data
          const imageUrl = await this.generateImageUrlForExistingNft(nft);

          // Create proper metadata structure
          const metadata: NFTMetadata = {
            name: nft.displayName,
            description: nft.displayDescription,
            image: imageUrl,
            external_url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/nft/${nft.id}`,
            background_color: this.getRarityColor(nft.rarity || 'common'),
            attributes: [
              { trait_type: 'Rarity', value: nft.rarity || 'common' },
              { trait_type: 'Score', value: nft.score || 0, display_type: 'number' },
              { trait_type: 'Created', value: nft.createdAt.toISOString().split('T')[0] }
            ]
          };

          return {
            id: nft.id,
            userId: nft.userId,
            analysisId: '', // Would need to be stored in the model
            name: nft.displayName,
            description: nft.displayDescription,
            rarity: nft.rarity || 'common',
            score: nft.score || 0,
            attributes: metadata.attributes,
            metadata,
            imageUrl,
            status: nft.status as any,
            createdAt: nft.createdAt,
            updatedAt: nft.lastUpdated,
          };
        })),
        total,
      };

    } catch (error) {
      this.logger.error(`Failed to get user NFT history: ${error.message}`, { userId });
      throw error;
    }
  }
}
