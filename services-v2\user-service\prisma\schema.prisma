// User Service V2 - Prisma Schema
// Database Per Service Pattern - user_service_v2 database only

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management Tables (User Service Domain Only)
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String

  // Profile Information
  firstName String? @map("first_name")
  lastName  String? @map("last_name")
  avatar    String?
  bio       String?

  // Account Status
  isActive     Boolean @default(true) @map("is_active")
  isVerified   Boolean @default(false) @map("is_verified")
  emailVerified <PERSON>olean @default(false) @map("email_verified")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  lastLogin DateTime? @map("last_login")

  // Relations to other services (via IDs only - microservice pattern)
  // profileAnalysisIds String[] // References to Profile Analysis Service
  // nftIds            String[] // References to NFT Generation Service
  // projectIds        String[] // References to Project Service

  // Relations
  sessions    UserSession[]
  permissions UserPermission[]
  roles       UserRole[]

  @@map("users")
}

// User Sessions (for authentication tracking)
model UserSession {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  sessionId String   @unique @map("session_id")
  
  // Session Details
  ipAddress String   @map("ip_address")
  userAgent String   @map("user_agent")
  deviceInfo Json?   @map("device_info")
  
  // Session Status
  isActive  Boolean  @default(true) @map("is_active")
  expiresAt DateTime @map("expires_at")
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  lastActivityAt DateTime @default(now()) @map("last_activity_at")
  
  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_sessions")
}

// User Permissions (RBAC)
model UserPermission {
  id         String @id @default(cuid())
  userId     String @map("user_id")
  permission String
  
  // Metadata
  grantedBy String?   @map("granted_by")
  grantedAt DateTime  @default(now()) @map("granted_at")
  expiresAt DateTime? @map("expires_at")
  
  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([userId, permission])
  @@map("user_permissions")
}

// User Roles
model UserRole {
  id       String @id @default(cuid())
  userId   String @map("user_id")
  role     String
  
  // Metadata
  assignedBy String?   @map("assigned_by")
  assignedAt DateTime  @default(now()) @map("assigned_at")
  expiresAt  DateTime? @map("expires_at")
  
  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([userId, role])
  @@map("user_roles")
}


