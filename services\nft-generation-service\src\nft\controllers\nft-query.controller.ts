import {
  <PERSON>,
  Get,
  Param,
  Query,
  <PERSON>s,
  HttpStatus,
  ParseIntPipe,
  DefaultValuePipe,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { NftQueryService } from '../services/nft-query.service';

@ApiTags('NFTs')
@Controller('nfts')
export class NftQueryController {
  private readonly logger = new Logger(NftQueryController.name);

  constructor(private readonly nftQueryService: NftQueryService) {}

  @Get()
  @ApiOperation({
    summary: 'Get NFTs list with filtering and pagination',
    description: 'Retrieve NFTs with support for filtering, search, and pagination'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'projectId', required: false, description: 'Filter by project ID' })
  @ApiQuery({ name: 'collectionId', required: false, description: 'Filter by collection ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiResponse({ status: 200, description: 'NFTs list retrieved successfully' })
  async getNfts(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('projectId') projectId: string,
    @Query('collectionId') collectionId: string,
    @Query('status') status: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting NFTs list - page: ${page}, limit: ${limit}`);
      
      const query = { page, limit, projectId, collectionId, status };
      const result = await this.nftQueryService.getNfts(query);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`NFTs list retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get NFT by ID',
    description: 'Retrieve NFT details by ID'
  })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiResponse({ status: 200, description: 'NFT retrieved successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  async getNftById(@Param('id') id: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting NFT by ID: ${id}`);
      
      const result = await this.nftQueryService.getNftById(id);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`NFT retrieval failed for ID ${id}: ${error.message}`, error.stack);
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
