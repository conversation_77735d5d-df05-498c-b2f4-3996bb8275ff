// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model TwitterProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  username    String   @unique
  displayName String?
  bio         String?
  location    String?
  website     String?
  avatarUrl   String?
  bannerUrl   String?
  verified    <PERSON><PERSON><PERSON>  @default(false)
  protected   <PERSON><PERSON><PERSON>  @default(false)
  
  // Metrics
  followersCount   Int @default(0)
  followingCount   Int @default(0)
  tweetsCount      Int @default(0)
  listedCount      Int @default(0)
  
  // Timestamps
  accountCreatedAt DateTime?
  lastTweetAt      DateTime?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  
  // Relations
  analyses ProfileAnalysis[]
  
  @@map("twitter_profiles")
}

model ProfileAnalysis {
  id        String   @id @default(cuid())
  profileId String
  userId    String
  
  // Analysis Configuration
  campaignId     String?
  analysisType   String   @default("standard")
  parameters     Json     // Configurable analysis parameters
  weights        Json     // Parameter weights
  
  // Analysis Results
  overallScore   Float
  categoryScores Json     // Breakdown by category
  metrics        Json     // Detailed metrics
  insights       Json?    // AI-generated insights
  
  // Status
  status         String   @default("completed") // pending, processing, completed, failed
  processingTime Int?     // Processing time in milliseconds
  
  // Timestamps
  analyzedAt DateTime @default(now())
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  // Relations
  profile TwitterProfile @relation(fields: [profileId], references: [id], onDelete: Cascade)
  
  @@map("profile_analyses")
}

model AnalysisTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  
  // Template Configuration
  parameters  Json     // Default parameters
  weights     Json     // Default weights
  thresholds  Json     // Score thresholds
  
  // Metadata
  isActive    Boolean  @default(true)
  isDefault   Boolean  @default(false)
  createdBy   String?
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("analysis_templates")
}

model AnalysisHistory {
  id         String   @id @default(cuid())
  userId     String
  profileId  String
  analysisId String
  
  // Historical Data
  snapshot   Json     // Profile snapshot at time of analysis
  results    Json     // Analysis results
  
  // Metadata
  version    String   @default("1.0")
  
  // Timestamps
  createdAt  DateTime @default(now())
  
  @@map("analysis_history")
}
