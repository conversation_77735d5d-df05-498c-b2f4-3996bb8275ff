import { api } from '@/lib/api'
import {
  NFTEvolution,
  EvolutionTrigger,
  EvolutionTemplate,
  EvolutionAnalytics,
  CreateEvolutionRequest,
  UpdateEvolutionRequest,
  EvolutionSearchRequest,
  EvolutionSearchResponse,
  EvolutionStatus,
  EvolutionTriggerType,
  TraitChange,
  MetadataChange,
  VisualChange,
  EvolutionHistoryEntry
} from '@/types/nft-evolution.types'

export class NFTEvolutionService {
  // ===== EVOLUTION MANAGEMENT =====
  
  async getEvolution(id: string): Promise<NFTEvolution> {
    try {
      const response = await api.get(`/nft-evolution/evolutions/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution:', error)
      throw new Error('Failed to load evolution details')
    }
  }

  async getNFTEvolutions(nftId: string): Promise<NFTEvolution[]> {
    try {
      const response = await api.get(`/nft-evolution/nfts/${nftId}/evolutions`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch NFT evolutions:', error)
      throw new Error('Failed to load NFT evolution history')
    }
  }

  async searchEvolutions(request: EvolutionSearchRequest): Promise<EvolutionSearchResponse> {
    try {
      const response = await api.post('/nft-evolution/evolutions/search', request)
      return response.data
    } catch (error) {
      console.error('Failed to search evolutions:', error)
      throw new Error('Failed to search evolutions')
    }
  }

  async createEvolution(request: CreateEvolutionRequest): Promise<NFTEvolution> {
    try {
      const response = await api.post('/nft-evolution/evolutions', request)
      return response.data
    } catch (error) {
      console.error('Failed to create evolution:', error)
      throw new Error('Failed to create evolution')
    }
  }

  async updateEvolution(id: string, request: UpdateEvolutionRequest): Promise<NFTEvolution> {
    try {
      const response = await api.patch(`/nft-evolution/evolutions/${id}`, request)
      return response.data
    } catch (error) {
      console.error('Failed to update evolution:', error)
      throw new Error('Failed to update evolution')
    }
  }

  async cancelEvolution(id: string): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/evolutions/${id}/cancel`)
      return response.data
    } catch (error) {
      console.error('Failed to cancel evolution:', error)
      throw new Error('Failed to cancel evolution')
    }
  }

  async approveEvolution(id: string): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/evolutions/${id}/approve`)
      return response.data
    } catch (error) {
      console.error('Failed to approve evolution:', error)
      throw new Error('Failed to approve evolution')
    }
  }

  async rejectEvolution(id: string, reason: string): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/evolutions/${id}/reject`, { reason })
      return response.data
    } catch (error) {
      console.error('Failed to reject evolution:', error)
      throw new Error('Failed to reject evolution')
    }
  }

  // ===== EVOLUTION TRIGGERS =====

  async getTriggers(): Promise<EvolutionTrigger[]> {
    try {
      const response = await api.get('/nft-evolution/triggers')
      return response.data
    } catch (error) {
      console.error('Failed to fetch triggers:', error)
      throw new Error('Failed to load evolution triggers')
    }
  }

  async getTrigger(id: string): Promise<EvolutionTrigger> {
    try {
      const response = await api.get(`/nft-evolution/triggers/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch trigger:', error)
      throw new Error('Failed to load trigger details')
    }
  }

  async getAvailableTriggersForNFT(nftId: string): Promise<EvolutionTrigger[]> {
    try {
      const response = await api.get(`/nft-evolution/nfts/${nftId}/available-triggers`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch available triggers:', error)
      throw new Error('Failed to load available triggers')
    }
  }

  async createTrigger(trigger: Omit<EvolutionTrigger, 'id' | 'createdAt' | 'updatedAt' | 'currentExecutions'>): Promise<EvolutionTrigger> {
    try {
      const response = await api.post('/nft-evolution/triggers', trigger)
      return response.data
    } catch (error) {
      console.error('Failed to create trigger:', error)
      throw new Error('Failed to create evolution trigger')
    }
  }

  async updateTrigger(id: string, updates: Partial<EvolutionTrigger>): Promise<EvolutionTrigger> {
    try {
      const response = await api.patch(`/nft-evolution/triggers/${id}`, updates)
      return response.data
    } catch (error) {
      console.error('Failed to update trigger:', error)
      throw new Error('Failed to update evolution trigger')
    }
  }

  async deleteTrigger(id: string): Promise<void> {
    try {
      await api.delete(`/nft-evolution/triggers/${id}`)
    } catch (error) {
      console.error('Failed to delete trigger:', error)
      throw new Error('Failed to delete evolution trigger')
    }
  }

  async activateTrigger(id: string): Promise<EvolutionTrigger> {
    try {
      const response = await api.post(`/nft-evolution/triggers/${id}/activate`)
      return response.data
    } catch (error) {
      console.error('Failed to activate trigger:', error)
      throw new Error('Failed to activate evolution trigger')
    }
  }

  async deactivateTrigger(id: string): Promise<EvolutionTrigger> {
    try {
      const response = await api.post(`/nft-evolution/triggers/${id}/deactivate`)
      return response.data
    } catch (error) {
      console.error('Failed to deactivate trigger:', error)
      throw new Error('Failed to deactivate evolution trigger')
    }
  }

  // ===== EVOLUTION TEMPLATES =====

  async getTemplates(): Promise<EvolutionTemplate[]> {
    try {
      const response = await api.get('/nft-evolution/templates')
      return response.data
    } catch (error) {
      console.error('Failed to fetch templates:', error)
      throw new Error('Failed to load evolution templates')
    }
  }

  async getTemplate(id: string): Promise<EvolutionTemplate> {
    try {
      const response = await api.get(`/nft-evolution/templates/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch template:', error)
      throw new Error('Failed to load template details')
    }
  }

  async createTemplate(template: Omit<EvolutionTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<EvolutionTemplate> {
    try {
      const response = await api.post('/nft-evolution/templates', template)
      return response.data
    } catch (error) {
      console.error('Failed to create template:', error)
      throw new Error('Failed to create evolution template')
    }
  }

  async updateTemplate(id: string, updates: Partial<EvolutionTemplate>): Promise<EvolutionTemplate> {
    try {
      const response = await api.patch(`/nft-evolution/templates/${id}`, updates)
      return response.data
    } catch (error) {
      console.error('Failed to update template:', error)
      throw new Error('Failed to update evolution template')
    }
  }

  async deleteTemplate(id: string): Promise<void> {
    try {
      await api.delete(`/nft-evolution/templates/${id}`)
    } catch (error) {
      console.error('Failed to delete template:', error)
      throw new Error('Failed to delete evolution template')
    }
  }

  async applyTemplate(templateId: string, nftId: string, customizations?: Record<string, any>): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/templates/${templateId}/apply`, {
        nftId,
        customizations
      })
      return response.data
    } catch (error) {
      console.error('Failed to apply template:', error)
      throw new Error('Failed to apply evolution template')
    }
  }

  // ===== TRAIT MANAGEMENT =====

  async updateTraits(evolutionId: string, traitChanges: TraitChange[]): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/evolutions/${evolutionId}/traits`, {
        traitChanges
      })
      return response.data
    } catch (error) {
      console.error('Failed to update traits:', error)
      throw new Error('Failed to update evolution traits')
    }
  }

  async validateTraitChanges(nftId: string, traitChanges: TraitChange[]): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    rarityImpact: number
    valueImpact: number
  }> {
    try {
      const response = await api.post(`/nft-evolution/nfts/${nftId}/validate-traits`, {
        traitChanges
      })
      return response.data
    } catch (error) {
      console.error('Failed to validate trait changes:', error)
      throw new Error('Failed to validate trait changes')
    }
  }

  async previewTraitChanges(nftId: string, traitChanges: TraitChange[]): Promise<{
    previewImageUrl: string
    rarityScore: number
    estimatedValue: number
    traitDistribution: Record<string, number>
  }> {
    try {
      const response = await api.post(`/nft-evolution/nfts/${nftId}/preview-traits`, {
        traitChanges
      })
      return response.data
    } catch (error) {
      console.error('Failed to preview trait changes:', error)
      throw new Error('Failed to preview trait changes')
    }
  }

  // ===== METADATA MANAGEMENT =====

  async updateMetadata(evolutionId: string, metadataChanges: MetadataChange[]): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/evolutions/${evolutionId}/metadata`, {
        metadataChanges
      })
      return response.data
    } catch (error) {
      console.error('Failed to update metadata:', error)
      throw new Error('Failed to update evolution metadata')
    }
  }

  async getMetadataHistory(nftId: string): Promise<MetadataChange[]> {
    try {
      const response = await api.get(`/nft-evolution/nfts/${nftId}/metadata-history`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch metadata history:', error)
      throw new Error('Failed to load metadata history')
    }
  }

  async rollbackMetadata(nftId: string, version: string): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/nfts/${nftId}/rollback-metadata`, {
        version
      })
      return response.data
    } catch (error) {
      console.error('Failed to rollback metadata:', error)
      throw new Error('Failed to rollback metadata')
    }
  }

  async validateMetadata(nftId: string, metadata: Record<string, any>): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    schema: string
  }> {
    try {
      const response = await api.post(`/nft-evolution/nfts/${nftId}/validate-metadata`, {
        metadata
      })
      return response.data
    } catch (error) {
      console.error('Failed to validate metadata:', error)
      throw new Error('Failed to validate metadata')
    }
  }

  // ===== VISUAL CHANGES =====

  async updateVisuals(evolutionId: string, visualChanges: VisualChange[]): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/evolutions/${evolutionId}/visuals`, {
        visualChanges
      })
      return response.data
    } catch (error) {
      console.error('Failed to update visuals:', error)
      throw new Error('Failed to update evolution visuals')
    }
  }

  async generateVisualPreview(nftId: string, visualChanges: VisualChange[]): Promise<{
    previewUrl: string
    animationUrl?: string
    generationTime: number
    quality: number
  }> {
    try {
      const response = await api.post(`/nft-evolution/nfts/${nftId}/preview-visuals`, {
        visualChanges
      })
      return response.data
    } catch (error) {
      console.error('Failed to generate visual preview:', error)
      throw new Error('Failed to generate visual preview')
    }
  }

  async regenerateVisuals(evolutionId: string, elementIds: string[]): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/evolutions/${evolutionId}/regenerate-visuals`, {
        elementIds
      })
      return response.data
    } catch (error) {
      console.error('Failed to regenerate visuals:', error)
      throw new Error('Failed to regenerate visuals')
    }
  }

  // ===== EVOLUTION HISTORY =====

  async getEvolutionHistory(nftId: string): Promise<EvolutionHistoryEntry[]> {
    try {
      const response = await api.get(`/nft-evolution/nfts/${nftId}/history`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution history:', error)
      throw new Error('Failed to load evolution history')
    }
  }

  async getEvolutionTimeline(nftId: string): Promise<{
    timeline: EvolutionHistoryEntry[]
    stages: number[]
    currentStage: number
    nextPossibleEvolutions: EvolutionTrigger[]
  }> {
    try {
      const response = await api.get(`/nft-evolution/nfts/${nftId}/timeline`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution timeline:', error)
      throw new Error('Failed to load evolution timeline')
    }
  }

  async revertEvolution(evolutionId: string): Promise<NFTEvolution> {
    try {
      const response = await api.post(`/nft-evolution/evolutions/${evolutionId}/revert`)
      return response.data
    } catch (error) {
      console.error('Failed to revert evolution:', error)
      throw new Error('Failed to revert evolution')
    }
  }

  // ===== ANALYTICS =====

  async getEvolutionAnalytics(nftId: string, timeframe: string = '30d'): Promise<EvolutionAnalytics> {
    try {
      const response = await api.get(`/nft-evolution/nfts/${nftId}/analytics`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution analytics:', error)
      throw new Error('Failed to load evolution analytics')
    }
  }

  async getGlobalEvolutionStats(): Promise<{
    totalEvolutions: number
    activeEvolutions: number
    averageEvolutionTime: number
    popularTriggers: Array<{ triggerId: string; name: string; count: number }>
    valueImpactDistribution: Record<string, number>
    successRateByType: Record<EvolutionTriggerType, number>
  }> {
    try {
      const response = await api.get('/nft-evolution/analytics/global')
      return response.data
    } catch (error) {
      console.error('Failed to fetch global evolution stats:', error)
      throw new Error('Failed to load global evolution statistics')
    }
  }

  async getEvolutionPredictions(nftId: string): Promise<{
    nextEvolution: {
      triggerId: string
      probability: number
      estimatedTime: string
      expectedValueIncrease: number
    }
    recommendations: Array<{
      action: string
      reason: string
      impact: number
      confidence: number
    }>
  }> {
    try {
      const response = await api.get(`/nft-evolution/nfts/${nftId}/predictions`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution predictions:', error)
      throw new Error('Failed to load evolution predictions')
    }
  }

  // ===== UTILITY METHODS =====

  async simulateEvolution(nftId: string, triggerId: string, customizations?: Record<string, any>): Promise<{
    simulationId: string
    previewEvolution: NFTEvolution
    estimatedOutcome: {
      rarityChange: number
      valueChange: number
      visualPreview: string
      successProbability: number
    }
    costs: {
      gasFee: number
      serviceFee: number
      totalCost: number
    }
  }> {
    try {
      const response = await api.post(`/nft-evolution/nfts/${nftId}/simulate`, {
        triggerId,
        customizations
      })
      return response.data
    } catch (error) {
      console.error('Failed to simulate evolution:', error)
      throw new Error('Failed to simulate evolution')
    }
  }

  async estimateEvolutionCosts(nftId: string, triggerId: string): Promise<{
    gasFee: number
    serviceFee: number
    totalCost: number
    currency: string
    breakdown: Record<string, number>
  }> {
    try {
      const response = await api.post(`/nft-evolution/nfts/${nftId}/estimate-costs`, {
        triggerId
      })
      return response.data
    } catch (error) {
      console.error('Failed to estimate evolution costs:', error)
      throw new Error('Failed to estimate evolution costs')
    }
  }

  async exportEvolutionData(nftId: string, format: 'json' | 'csv' = 'json'): Promise<Blob> {
    try {
      const response = await api.get(`/nft-evolution/nfts/${nftId}/export`, {
        params: { format },
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to export evolution data:', error)
      throw new Error('Failed to export evolution data')
    }
  }
}

export const nftEvolutionService = new NFTEvolutionService()
