{"name": "profile-analysis-service", "version": "1.0.0", "description": "Profile Analysis Service - Industry Standard Microservices Implementation", "author": "Social NFT Platform Team", "private": true, "license": "UNLICENSED", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.4.19", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.19", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-express": "^10.4.19", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^11.0.0", "@prisma/client": "^6.8.2", "axios": "^1.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "prisma": "^6.8.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "twitter-api-v2": "^1.23.2", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^10.4.19", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.19", "@types/express": "^4.17.21", "@types/jest": "^29.5.13", "@types/node": "^22.10.1", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.3.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}