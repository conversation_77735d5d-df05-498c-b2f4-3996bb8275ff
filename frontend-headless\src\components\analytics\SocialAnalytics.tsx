'use client'

import React from 'react'
import {
  GlobeAltIcon,
  ShareIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'
import { useSocialAnalytics } from '@/hooks/useAnalytics'
import { AnalyticsTimeframe } from '@/types/analytics.types'

interface SocialAnalyticsProps {
  campaignId: string
  timeframe: AnalyticsTimeframe
  className?: string
}

export default function SocialAnalytics({
  campaignId,
  timeframe,
  className = ''
}: SocialAnalyticsProps) {
  const { data: social, isLoading } = useSocialAnalytics(campaignId, timeframe)

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h2 className="text-lg font-medium text-gray-900">Social Media Analytics</h2>
        <p className="text-sm text-gray-600">Social engagement and viral metrics</p>
      </div>

      {/* Social Metrics */}
      {social && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-blue-600 bg-blue-100 mb-4">
              <ShareIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                {social.totalSocialShares.toLocaleString()}
              </h3>
              <p className="text-sm font-medium text-gray-700">Total Shares</p>
            </div>
            <p className="text-sm text-gray-500">Across all platforms</p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-green-600 bg-green-100 mb-4">
              <GlobeAltIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                {social.socialReach.toLocaleString()}
              </h3>
              <p className="text-sm font-medium text-gray-700">Social Reach</p>
            </div>
            <p className="text-sm text-gray-500">Total audience reached</p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-purple-600 bg-purple-100 mb-4">
              <HeartIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                {(social.socialEngagementRate * 100).toFixed(1)}%
              </h3>
              <p className="text-sm font-medium text-gray-700">Engagement Rate</p>
            </div>
            <p className="text-sm text-gray-500">Social engagement</p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-orange-600 bg-orange-100 mb-4">
              <UserGroupIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                {social.viralityCoefficient.toFixed(2)}
              </h3>
              <p className="text-sm font-medium text-gray-700">Virality Score</p>
            </div>
            <p className="text-sm text-gray-500">Viral coefficient</p>
          </div>
        </div>
      )}

      {/* Platform Breakdown */}
      {social && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Performance</h3>
          <div className="space-y-4">
            {social.platformMetrics.map((platform, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900">{platform.platform}</h4>
                  <span className="text-sm text-gray-600">
                    {(platform.conversionRate * 100).toFixed(1)}% conversion
                  </span>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Shares:</span>
                    <span className="font-medium ml-1">{platform.shares.toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Reach:</span>
                    <span className="font-medium ml-1">{platform.reach.toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Engagement:</span>
                    <span className="font-medium ml-1">{platform.engagement.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Sentiment Analysis */}
      {social && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Sentiment Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{social.positiveReactions}</div>
              <div className="text-sm text-gray-600">Positive Reactions</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-600">{social.neutralReactions}</div>
              <div className="text-sm text-gray-600">Neutral Reactions</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">{social.negativeReactions}</div>
              <div className="text-sm text-gray-600">Negative Reactions</div>
            </div>
          </div>
          <div className="mt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {social.sentimentScore.toFixed(1)}/10
              </div>
              <div className="text-sm text-gray-600">Overall Sentiment Score</div>
            </div>
          </div>
        </div>
      )}

      {/* Top Influencers */}
      {social && social.topInfluencers && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Influencers</h3>
          <div className="space-y-3">
            {social.topInfluencers.slice(0, 5).map((influencer, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-gray-900">{influencer.influencer}</div>
                  <div className="text-xs text-gray-600">
                    {influencer.reach.toLocaleString()} reach • {influencer.engagement.toLocaleString()} engagement
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-green-600">
                    {(influencer.roi * 100).toFixed(1)}% ROI
                  </div>
                  <div className="text-xs text-gray-500">
                    {influencer.conversions} conversions
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
