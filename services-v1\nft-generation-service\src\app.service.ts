import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);
  private readonly startTime = Date.now();

  constructor(private readonly configService: ConfigService) {}

  getServiceInfo() {
    const info = {
      service: 'NFT Generation Service',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      port: this.configService.get<number>('SERVICE_PORT', 3003),
      features: {
        nftGeneration: true,
        imageProcessing: true,
        templateManagement: true,
        aiPowered: true,
        healthChecks: true,
        swagger: this.configService.get<string>('NODE_ENV') === 'development',
        cors: true,
        validation: true,
        authentication: true,
      },
      configuration: {
        outputDir: this.configService.get<string>('NFT_OUTPUT_DIR', './output'),
        templatesDir: this.configService.get<string>('NFT_TEMPLATES_DIR', './public/nft-templates'),
        imagesDir: this.configService.get<string>('NFT_IMAGES_DIR', './public/nft-images'),
        maxImageSize: this.configService.get<number>('MAX_IMAGE_SIZE', 2048),
        supportedFormats: this.configService.get<string>('SUPPORTED_FORMATS', 'png,jpg,jpeg,gif').split(','),
      },
      externalServices: {
        userService: this.configService.get<string>('USER_SERVICE_URL'),
        projectService: this.configService.get<string>('PROJECT_SERVICE_URL'),
        blockchainService: this.configService.get<string>('BLOCKCHAIN_SERVICE_URL'),
        marketplaceService: this.configService.get<string>('MARKETPLACE_SERVICE_URL'),
        analyticsService: this.configService.get<string>('ANALYTICS_SERVICE_URL'),
        profileService: this.configService.get<string>('PROFILE_ANALYSIS_SERVICE_URL'),
      },
    };

    this.logger.log('Service info retrieved', { 
      uptime: info.uptime,
      environment: info.environment 
    });

    return info;
  }

  getStatus() {
    const memoryUsage = process.memoryUsage();
    
    const status = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
      },
      cpu: {
        usage: process.cpuUsage(),
      },
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      nodeVersion: process.version,
      platform: process.platform,
      imageProcessing: {
        available: true,
        supportedFormats: this.configService.get<string>('SUPPORTED_FORMATS', 'png,jpg,jpeg,gif').split(','),
        maxImageSize: this.configService.get<number>('MAX_IMAGE_SIZE', 2048),
      },
    };

    this.logger.debug('Service status checked', {
      uptime: status.uptime,
      memoryUsed: Math.round(status.memory.used / 1024 / 1024) + 'MB',
    });

    return status;
  }
}
