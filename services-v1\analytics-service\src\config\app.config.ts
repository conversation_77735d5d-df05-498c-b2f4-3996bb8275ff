import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IsNumber, IsString, IsArray, IsBoolean, validateSync } from 'class-validator';
import { plainToClass, Transform } from 'class-transformer';

export class AppConfigValidation {
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  PORT: number = 3009;

  @IsString()
  NODE_ENV: string = 'development';

  @IsString()
  DATABASE_URL: string = '';

  @IsArray()
  @Transform(({ value }) => value.split(',').map((origin: string) => origin.trim()))
  CORS_ORIGIN: string[] = ['http://localhost:3000', 'http://localhost:3010'];

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  CORS_CREDENTIALS: boolean = true;

  @IsString()
  REDIS_URL: string = 'redis://localhost:6379';

  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  ANALYTICS_BATCH_SIZE: number = 100;

  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  ANALYTICS_FLUSH_INTERVAL: number = 5000;

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  ANALYTICS_REAL_TIME: boolean = true;
}

@Injectable()
export class AppConfig {
  private readonly config: AppConfigValidation;

  constructor(private configService: ConfigService) {
    const config = plainToClass(AppConfigValidation, process.env, {
      enableImplicitConversion: true,
    });

    const errors = validateSync(config);
    if (errors.length > 0) {
      throw new Error(`Configuration validation failed: ${errors.toString()}`);
    }

    this.config = config;
  }

  get port(): number {
    return this.config.PORT;
  }

  get nodeEnv(): string {
    return this.config.NODE_ENV;
  }

  get isDevelopment(): boolean {
    return this.nodeEnv === 'development';
  }

  get isProduction(): boolean {
    return this.nodeEnv === 'production';
  }

  get databaseUrl(): string {
    return this.config.DATABASE_URL;
  }

  get cors() {
    return {
      origin: this.config.CORS_ORIGIN,
      credentials: this.config.CORS_CREDENTIALS,
    };
  }

  get redis() {
    return {
      url: this.config.REDIS_URL,
    };
  }

  get analytics() {
    return {
      batchSize: this.config.ANALYTICS_BATCH_SIZE,
      flushInterval: this.config.ANALYTICS_FLUSH_INTERVAL,
      enableRealTime: this.config.ANALYTICS_REAL_TIME,
    };
  }
}
