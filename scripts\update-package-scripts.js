#!/usr/bin/env node

/**
 * Update Package.json Scripts for Enterprise Standardization
 * Adds all standardization-related scripts to package.json
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Updating package.json with enterprise standardization scripts');
console.log('================================================================');

// Read current package.json
const packageJsonPath = path.join(process.cwd(), 'package.json');

if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ package.json not found in current directory');
    process.exit(1);
}

let packageJson;
try {
    packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
} catch (error) {
    console.error('❌ Error reading package.json:', error.message);
    process.exit(1);
}

// Backup original package.json
const backupPath = `package.json.backup.${Date.now()}`;
fs.writeFileSync(backupPath, JSON.stringify(packageJson, null, 2));
console.log(`📋 Backup created: ${backupPath}`);

// Initialize scripts object if it doesn't exist
if (!packageJson.scripts) {
    packageJson.scripts = {};
}

// Enterprise standardization scripts
const standardizationScripts = {
    // Implementation Scripts
    "implement:env": "./scripts/implement-environment-standardization.sh",
    "implement:config": "./scripts/implement-configuration-standardization.sh",
    "implement:auth": "./scripts/implement-authentication-standardization.sh",
    "implement:responses": "./scripts/implement-response-standardization.sh",
    "implement:logging": "./scripts/implement-logging-standardization.sh",
    "implement:data": "./scripts/implement-data-layer-standardization.sh",
    "implement:all": "./scripts/implement-all-standardization.sh",
    
    // Validation Scripts
    "validate:env": "node scripts/validate-environment.js",
    "validate:config": "node scripts/validate-configuration.js",
    "validate:auth": "node scripts/validate-authentication.js",
    "validate:responses": "node scripts/validate-responses.js",
    "validate:logging": "node scripts/validate-logging.js",
    "validate:data": "node scripts/validate-data-layer.js",
    "validate:patterns": "./scripts/validate-all-patterns.sh",
    "validate:all": "npm run validate:env && npm run validate:config && npm run validate:auth && npm run validate:responses && npm run validate:logging && npm run validate:data",
    
    // Testing Scripts
    "test:patterns": "jest --testPathPattern=patterns",
    "test:compliance": "jest --testPathPattern=compliance",
    "test:standardization": "npm run test:patterns && npm run test:compliance",
    "test:all-patterns": "npm run test:patterns && npm run test:compliance",
    "test:services": "jest --testPathPattern=services",
    "test:integration": "jest --testPathPattern=integration",
    "test:e2e": "jest --testPathPattern=e2e",
    "test:all": "npm run test:patterns && npm run test:services && npm run test:integration",
    
    // Code Generation Scripts
    "generate:service": "node scripts/generate-service.js",
    "generate:controller": "node scripts/generate-controller.js",
    "generate:repository": "node scripts/generate-repository.js",
    "generate:module": "node scripts/generate-module.js",
    "generate:config": "node scripts/generate-config-class.js",
    "generate:docs": "node scripts/generate-documentation.js",
    "generate:api-docs": "node scripts/generate-api-documentation.js",
    "generate:openapi": "node scripts/generate-openapi-spec.js",
    
    // Health and Monitoring Scripts
    "health:all": "node scripts/check-all-health.js",
    "health:services": "node scripts/check-services-health.js",
    "health:databases": "node scripts/check-databases-health.js",
    "health:apis": "node scripts/check-apis-health.js",
    "monitor:services": "node scripts/monitor-services.js",
    "monitor:performance": "node scripts/monitor-performance.js",
    "monitor:logs": "node scripts/monitor-logs.js",
    
    // Diagnostic Scripts
    "diagnose:all": "node scripts/diagnose-all.js",
    "diagnose:env": "node scripts/diagnose-environment.js",
    "diagnose:config": "node scripts/diagnose-configuration.js",
    "diagnose:auth": "node scripts/diagnose-authentication.js",
    "diagnose:responses": "node scripts/diagnose-responses.js",
    "diagnose:logging": "node scripts/diagnose-logging.js",
    "diagnose:data": "node scripts/diagnose-data-layer.js",
    "diagnose:connectivity": "node scripts/diagnose-connectivity.js",
    "diagnose:performance": "node scripts/diagnose-performance.js",
    
    // Maintenance Scripts
    "fix:patterns": "node scripts/fix-pattern-violations.js",
    "fix:imports": "node scripts/fix-imports.js",
    "fix:formatting": "prettier --write \"**/*.{ts,js,json,md}\"",
    "fix:lint": "eslint --fix \"**/*.{ts,js}\"",
    "fix:all": "npm run fix:patterns && npm run fix:imports && npm run fix:formatting && npm run fix:lint",
    
    // Update and Migration Scripts
    "update:patterns": "node scripts/update-patterns.js",
    "update:dependencies": "npm update && npm audit fix",
    "migrate:patterns": "node scripts/migrate-patterns.js",
    "migrate:config": "node scripts/migrate-configuration.js",
    "rollback:patterns": "node scripts/rollback-patterns.js",
    "backup:patterns": "node scripts/backup-patterns.js",
    
    // Compliance and Reporting Scripts
    "check:compliance": "node scripts/check-compliance.js",
    "report:compliance": "./scripts/generate-compliance-report.sh",
    "report:performance": "node scripts/generate-performance-report.js",
    "report:security": "node scripts/generate-security-report.js",
    "audit:security": "npm audit && node scripts/security-audit.js",
    "audit:patterns": "node scripts/audit-patterns.js",
    
    // Environment Management Scripts
    "env:dev": "node scripts/switch-environment.js development",
    "env:prod": "node scripts/switch-environment.js production",
    "env:test": "node scripts/switch-environment.js test",
    "env:validate": "node scripts/validate-environment.js",
    "env:check": "node scripts/check-environment.js",
    "env:docs": "node scripts/generate-env-docs.js",
    
    // Database Scripts
    "db:health": "node scripts/check-database-health.js",
    "db:test": "node scripts/test-database-connection.js",
    "db:migrate": "prisma migrate deploy",
    "db:reset": "prisma migrate reset",
    "db:seed": "prisma db seed",
    "db:studio": "prisma studio",
    "db:generate": "prisma generate",
    "db:validate": "prisma validate",
    
    // Development Workflow Scripts
    "dev:setup": "npm run implement:all && npm run validate:all",
    "dev:check": "npm run validate:all && npm run test:patterns",
    "dev:fix": "npm run fix:all && npm run validate:all",
    "dev:clean": "node scripts/clean-development.js",
    "dev:reset": "node scripts/reset-development.js",
    
    // CI/CD Scripts
    "ci:validate": "npm run validate:all",
    "ci:test": "npm run test:all",
    "ci:build": "npm run build",
    "ci:deploy": "node scripts/deploy.js",
    "ci:rollback": "node scripts/rollback-deployment.js",
    
    // Documentation Scripts
    "docs:build": "npm run generate:docs && npm run generate:api-docs",
    "docs:serve": "node scripts/serve-docs.js",
    "docs:validate": "node scripts/validate-documentation.js",
    "docs:update": "node scripts/update-documentation.js",
    
    // Utility Scripts
    "clean:logs": "node scripts/clean-logs.js",
    "clean:cache": "node scripts/clean-cache.js",
    "clean:deps": "rm -rf node_modules && npm install",
    "clean:all": "npm run clean:logs && npm run clean:cache && npm run clean:deps",
    
    // Pre-commit and Git Hooks
    "pre-commit": "npm run validate:all && npm run test:patterns",
    "pre-push": "npm run validate:all && npm run test:all",
    "install:hooks": "node scripts/install-git-hooks.js",
    
    // Service-specific Scripts
    "start:all": "node scripts/start-all-services.js",
    "stop:all": "node scripts/stop-all-services.js",
    "restart:all": "npm run stop:all && npm run start:all",
    "logs:all": "node scripts/view-all-logs.js",
    "logs:service": "node scripts/view-service-logs.js",
    
    // Performance and Optimization
    "optimize:images": "node scripts/optimize-images.js",
    "optimize:bundle": "node scripts/optimize-bundle.js",
    "analyze:bundle": "node scripts/analyze-bundle.js",
    "analyze:performance": "node scripts/analyze-performance.js",
    "analyze:logs": "node scripts/analyze-logs.js",
    
    // Security Scripts
    "security:scan": "node scripts/security-scan.js",
    "security:audit": "npm audit && node scripts/security-audit.js",
    "security:update": "node scripts/update-security.js",
    "security:report": "node scripts/generate-security-report.js"
};

// Add scripts to package.json, preserving existing scripts
console.log('📝 Adding standardization scripts...');

let addedCount = 0;
let updatedCount = 0;
let skippedCount = 0;

for (const [scriptName, scriptCommand] of Object.entries(standardizationScripts)) {
    if (packageJson.scripts[scriptName]) {
        if (packageJson.scripts[scriptName] !== scriptCommand) {
            console.log(`🔄 Updating script: ${scriptName}`);
            packageJson.scripts[scriptName] = scriptCommand;
            updatedCount++;
        } else {
            skippedCount++;
        }
    } else {
        console.log(`➕ Adding script: ${scriptName}`);
        packageJson.scripts[scriptName] = scriptCommand;
        addedCount++;
    }
}

// Sort scripts alphabetically for better organization
const sortedScripts = {};
Object.keys(packageJson.scripts).sort().forEach(key => {
    sortedScripts[key] = packageJson.scripts[key];
});
packageJson.scripts = sortedScripts;

// Add development dependencies for standardization tools
console.log('📦 Adding development dependencies...');

if (!packageJson.devDependencies) {
    packageJson.devDependencies = {};
}

const devDependencies = {
    "@types/jest": "^29.5.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint": "^8.0.0",
    "eslint-config-prettier": "^9.0.0",
    "eslint-plugin-import": "^2.28.0",
    "jest": "^29.5.0",
    "prettier": "^3.0.0",
    "ts-jest": "^29.1.0",
    "typescript": "^5.0.0",
    "husky": "^8.0.0",
    "lint-staged": "^14.0.0"
};

let depsAdded = 0;
for (const [dep, version] of Object.entries(devDependencies)) {
    if (!packageJson.devDependencies[dep]) {
        packageJson.devDependencies[dep] = version;
        depsAdded++;
    }
}

// Add lint-staged configuration
if (!packageJson['lint-staged']) {
    packageJson['lint-staged'] = {
        "*.{ts,js}": [
            "eslint --fix",
            "prettier --write"
        ],
        "*.{json,md}": [
            "prettier --write"
        ]
    };
    console.log('➕ Added lint-staged configuration');
}

// Add husky configuration
if (!packageJson.husky) {
    packageJson.husky = {
        "hooks": {
            "pre-commit": "lint-staged && npm run pre-commit",
            "pre-push": "npm run pre-push"
        }
    };
    console.log('➕ Added husky configuration');
}

// Write updated package.json
try {
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    console.log('✅ package.json updated successfully');
} catch (error) {
    console.error('❌ Error writing package.json:', error.message);
    process.exit(1);
}

// Display summary
console.log('');
console.log('📊 Update Summary:');
console.log('==================');
console.log(`➕ Scripts added: ${addedCount}`);
console.log(`🔄 Scripts updated: ${updatedCount}`);
console.log(`⏭️  Scripts skipped: ${skippedCount}`);
console.log(`📦 Dev dependencies added: ${depsAdded}`);
console.log('');

console.log('🎯 Key Scripts Available:');
console.log('=========================');
console.log('• npm run implement:all     - Implement all standardization phases');
console.log('• npm run validate:all      - Validate all patterns');
console.log('• npm run test:all-patterns - Test all standardization patterns');
console.log('• npm run health:all        - Check health of all services');
console.log('• npm run report:compliance - Generate compliance report');
console.log('• npm run fix:all           - Fix common pattern violations');
console.log('• npm run dev:setup         - Complete development setup');
console.log('');

console.log('📚 Next Steps:');
console.log('==============');
console.log('1. Run: npm install (to install new dev dependencies)');
console.log('2. Run: npm run install:hooks (to set up git hooks)');
console.log('3. Run: npm run validate:all (to check current compliance)');
console.log('4. Run: npm run implement:all (if standardization not complete)');
console.log('');

console.log('✅ Package.json update complete!');
console.log(`📋 Backup available at: ${backupPath}`);
