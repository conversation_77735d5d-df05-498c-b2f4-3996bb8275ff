/**
 * Base Repository Implementation
 * Provides standardized repository pattern with monitoring and caching
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StandardizedPrismaService } from '../services/prisma.service';
import { ServiceLoggerService } from '../../logging/services/service-logger.service';
import { MetricsService } from '../../logging/services/metrics.service';
import {
  IBaseRepository,
  FindOptions,
  FindCriteria,
  CreateOptions,
  UpdateOptions,
  DeleteOptions,
  CreateManyOptions,
  UpdateManyOptions,
  DeleteManyOptions,
  PaginationOptions,
  PaginatedResult,
  HealthCheckResult,
  WhereClause,
  SortDirection,
} from '../interfaces/repository.interface';
import {
  PerformanceMetricCategory,
  BusinessMetricCategory,
} from '../../logging/interfaces/metrics.interface';

/**
 * Abstract base repository with enterprise features
 */
@Injectable()
export abstract class BaseRepository<T, CreateDto, UpdateDto> implements IBaseRepository<T, CreateDto, UpdateDto> {
  protected readonly logger = new Logger(this.constructor.name);
  protected readonly modelName: string;

  constructor(
    protected readonly prisma: StandardizedPrismaService,
    protected readonly serviceLogger: ServiceLoggerService,
    protected readonly metricsService: MetricsService,
    protected readonly configService: ConfigService,
    modelName: string,
  ) {
    this.modelName = modelName;
  }

  /**
   * Get the Prisma model delegate
   */
  protected abstract getModel(): any;

  /**
   * Transform entity to DTO
   */
  protected abstract toDto(entity: any): T;

  /**
   * Transform create DTO to entity data
   */
  protected abstract toCreateData(dto: CreateDto): any;

  /**
   * Transform update DTO to entity data
   */
  protected abstract toUpdateData(dto: UpdateDto): any;

  /**
   * Find entity by ID
   */
  async findById(id: string, options?: FindOptions): Promise<T | null> {
    const startTime = Date.now();
    const operation = `${this.modelName}.findById`;

    try {
      this.serviceLogger.logOperationStart(operation, {
        metadata: { id, options },
      });

      const result = await this.getModel().findUnique({
        where: { id },
        ...this.buildFindOptions(options),
      });

      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, true);
      this.serviceLogger.logOperationComplete(operation, duration, true);

      return result ? this.toDto(result) : null;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      this.serviceLogger.logOperationComplete(operation, duration, false, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Find multiple entities
   */
  async findMany(criteria?: FindCriteria<T>, options?: FindOptions): Promise<T[]> {
    const startTime = Date.now();
    const operation = `${this.modelName}.findMany`;

    try {
      this.serviceLogger.logOperationStart(operation, {
        metadata: { criteria, options },
      });

      const query = {
        ...this.buildFindCriteria(criteria),
        ...this.buildFindOptions(options),
      };

      const results = await this.getModel().findMany(query);
      const duration = Date.now() - startTime;

      this.recordOperationMetrics(operation, duration, true, { count: results.length });
      this.serviceLogger.logOperationComplete(operation, duration, true, {
        count: results.length,
      });

      return results.map(result => this.toDto(result));
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      this.serviceLogger.logOperationComplete(operation, duration, false, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Find single entity
   */
  async findOne(criteria: FindCriteria<T>, options?: FindOptions): Promise<T | null> {
    const startTime = Date.now();
    const operation = `${this.modelName}.findOne`;

    try {
      this.serviceLogger.logOperationStart(operation, {
        metadata: { criteria, options },
      });

      const query = {
        ...this.buildFindCriteria(criteria),
        ...this.buildFindOptions(options),
      };

      const result = await this.getModel().findFirst(query);
      const duration = Date.now() - startTime;

      this.recordOperationMetrics(operation, duration, true);
      this.serviceLogger.logOperationComplete(operation, duration, true);

      return result ? this.toDto(result) : null;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      this.serviceLogger.logOperationComplete(operation, duration, false, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Create entity
   */
  async create(data: CreateDto, options?: CreateOptions): Promise<T> {
    const startTime = Date.now();
    const operation = `${this.modelName}.create`;

    try {
      this.serviceLogger.logOperationStart(operation, {
        metadata: { data, options },
      });

      const createData = this.toCreateData(data);
      const query = {
        data: createData,
        ...this.buildCreateOptions(options),
      };

      const result = await this.getModel().create(query);
      const duration = Date.now() - startTime;

      this.recordOperationMetrics(operation, duration, true);
      this.recordBusinessMetric('entity_created', 1);
      this.serviceLogger.logOperationComplete(operation, duration, true);

      return this.toDto(result);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      this.serviceLogger.logOperationComplete(operation, duration, false, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Update entity
   */
  async update(id: string, data: UpdateDto, options?: UpdateOptions): Promise<T> {
    const startTime = Date.now();
    const operation = `${this.modelName}.update`;

    try {
      this.serviceLogger.logOperationStart(operation, {
        metadata: { id, data, options },
      });

      const updateData = this.toUpdateData(data);
      const query = {
        where: { id },
        data: updateData,
        ...this.buildUpdateOptions(options),
      };

      const result = await this.getModel().update(query);
      const duration = Date.now() - startTime;

      this.recordOperationMetrics(operation, duration, true);
      this.recordBusinessMetric('entity_updated', 1);
      this.serviceLogger.logOperationComplete(operation, duration, true);

      return this.toDto(result);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      this.serviceLogger.logOperationComplete(operation, duration, false, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Delete entity
   */
  async delete(id: string, options?: DeleteOptions): Promise<boolean> {
    const startTime = Date.now();
    const operation = `${this.modelName}.delete`;

    try {
      this.serviceLogger.logOperationStart(operation, {
        metadata: { id, options },
      });

      await this.getModel().delete({
        where: { id },
        ...this.buildDeleteOptions(options),
      });

      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, true);
      this.recordBusinessMetric('entity_deleted', 1);
      this.serviceLogger.logOperationComplete(operation, duration, true);

      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      this.serviceLogger.logOperationComplete(operation, duration, false, {
        error: error.message,
      });
      
      // Check if error is due to record not found
      if (error.code === 'P2025') {
        return false;
      }
      throw error;
    }
  }

  /**
   * Create multiple entities
   */
  async createMany(data: CreateDto[], options?: CreateManyOptions): Promise<T[]> {
    const startTime = Date.now();
    const operation = `${this.modelName}.createMany`;

    try {
      this.serviceLogger.logOperationStart(operation, {
        metadata: { count: data.length, options },
      });

      const createData = data.map(item => this.toCreateData(item));
      
      // Use transaction for bulk create
      const results = await this.prisma.transactionWithMetrics(async (tx) => {
        const created = [];
        for (const item of createData) {
          const result = await tx[this.modelName].create({
            data: item,
            ...this.buildCreateOptions(options),
          });
          created.push(result);
        }
        return created;
      });

      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, true, { count: results.length });
      this.recordBusinessMetric('entities_created', results.length);
      this.serviceLogger.logOperationComplete(operation, duration, true, {
        count: results.length,
      });

      return results.map(result => this.toDto(result));
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      this.serviceLogger.logOperationComplete(operation, duration, false, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Update multiple entities
   */
  async updateMany(criteria: FindCriteria<T>, data: Partial<UpdateDto>, options?: UpdateManyOptions): Promise<number> {
    const startTime = Date.now();
    const operation = `${this.modelName}.updateMany`;

    try {
      this.serviceLogger.logOperationStart(operation, {
        metadata: { criteria, data, options },
      });

      const updateData = this.toUpdateData(data as UpdateDto);
      const result = await this.getModel().updateMany({
        where: this.buildWhereClause(criteria?.where),
        data: updateData,
        ...this.buildUpdateManyOptions(options),
      });

      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, true, { count: result.count });
      this.recordBusinessMetric('entities_updated', result.count);
      this.serviceLogger.logOperationComplete(operation, duration, true, {
        count: result.count,
      });

      return result.count;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      this.serviceLogger.logOperationComplete(operation, duration, false, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Delete multiple entities
   */
  async deleteMany(criteria: FindCriteria<T>, options?: DeleteManyOptions): Promise<number> {
    const startTime = Date.now();
    const operation = `${this.modelName}.deleteMany`;

    try {
      this.serviceLogger.logOperationStart(operation, {
        metadata: { criteria, options },
      });

      const result = await this.getModel().deleteMany({
        where: this.buildWhereClause(criteria?.where),
        ...this.buildDeleteManyOptions(options),
      });

      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, true, { count: result.count });
      this.recordBusinessMetric('entities_deleted', result.count);
      this.serviceLogger.logOperationComplete(operation, duration, true, {
        count: result.count,
      });

      return result.count;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      this.serviceLogger.logOperationComplete(operation, duration, false, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Count entities
   */
  async count(criteria?: FindCriteria<T>): Promise<number> {
    const startTime = Date.now();
    const operation = `${this.modelName}.count`;

    try {
      const result = await this.getModel().count({
        where: this.buildWhereClause(criteria?.where),
      });

      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, true);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      throw error;
    }
  }

  /**
   * Check if entity exists
   */
  async exists(criteria: FindCriteria<T>): Promise<boolean> {
    const count = await this.count(criteria);
    return count > 0;
  }

  /**
   * Paginate entities
   */
  async paginate(criteria?: FindCriteria<T>, pagination?: PaginationOptions): Promise<PaginatedResult<T>> {
    const startTime = Date.now();
    const operation = `${this.modelName}.paginate`;

    try {
      const page = pagination?.page || 1;
      const limit = Math.min(pagination?.limit || 20, 100); // Max 100 items per page
      const skip = (page - 1) * limit;

      const [data, totalCount] = await Promise.all([
        this.findMany(criteria, { ...criteria, skip, take: limit }),
        this.count(criteria),
      ]);

      const totalPages = Math.ceil(totalCount / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, true, {
        page,
        limit,
        totalCount,
        totalPages,
      });

      return {
        data,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext,
          hasPrev,
          nextPage: hasNext ? page + 1 : undefined,
          prevPage: hasPrev ? page - 1 : undefined,
          offset: skip,
        },
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordOperationMetrics(operation, duration, false);
      throw error;
    }
  }

  /**
   * Execute operation within transaction
   */
  async withTransaction<R>(fn: (repository: this) => Promise<R>): Promise<R> {
    return this.prisma.transactionWithMetrics(async (tx) => {
      // Create a new repository instance with the transaction client
      const transactionalRepo = Object.create(this);
      transactionalRepo.prisma = tx;
      return fn(transactionalRepo);
    });
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<HealthCheckResult> {
    try {
      const startTime = Date.now();
      await this.count();
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime,
        details: {
          model: this.modelName,
          operation: 'count',
          timestamp: new Date(),
        },
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: -1,
        details: {
          model: this.modelName,
          error: error.message,
          timestamp: new Date(),
        },
        timestamp: new Date(),
      };
    }
  }

  /**
   * Build find options for Prisma
   */
  protected buildFindOptions(options?: FindOptions): any {
    if (!options) return {};

    return {
      include: options.include,
      select: options.select,
      orderBy: options.orderBy,
      take: options.timeout ? undefined : undefined, // Prisma doesn't support timeout directly
    };
  }

  /**
   * Build find criteria for Prisma
   */
  protected buildFindCriteria(criteria?: FindCriteria<T>): any {
    if (!criteria) return {};

    return {
      where: this.buildWhereClause(criteria.where),
      include: criteria.include,
      select: criteria.select,
      orderBy: criteria.orderBy,
      skip: criteria.skip,
      take: criteria.take,
      cursor: criteria.cursor,
    };
  }

  /**
   * Build where clause for Prisma
   */
  protected buildWhereClause(where?: WhereClause<T>): any {
    if (!where) return {};

    // This is a simplified implementation
    // In a real implementation, you would handle all the where clause operators
    return where;
  }

  /**
   * Build create options
   */
  protected buildCreateOptions(options?: CreateOptions): any {
    if (!options) return {};

    return {
      include: options.include,
      select: options.select,
    };
  }

  /**
   * Build update options
   */
  protected buildUpdateOptions(options?: UpdateOptions): any {
    if (!options) return {};

    return {
      include: options.include,
      select: options.select,
    };
  }

  /**
   * Build delete options
   */
  protected buildDeleteOptions(options?: DeleteOptions): any {
    return {}; // Prisma delete doesn't have many options
  }

  /**
   * Build update many options
   */
  protected buildUpdateManyOptions(options?: UpdateManyOptions): any {
    return {}; // Prisma updateMany doesn't have many options
  }

  /**
   * Build delete many options
   */
  protected buildDeleteManyOptions(options?: DeleteManyOptions): any {
    return {}; // Prisma deleteMany doesn't have many options
  }

  /**
   * Record operation metrics
   */
  protected recordOperationMetrics(operation: string, duration: number, success: boolean, metadata?: any): void {
    this.metricsService.recordPerformanceMetric({
      name: 'repository_operation_duration',
      value: duration,
      type: 'histogram' as any,
      category: PerformanceMetricCategory.DATABASE,
      unit: 'ms',
      timestamp: new Date(),
      tags: {
        service: this.prisma.getServiceName(),
        model: this.modelName,
        operation,
        status: success ? 'success' : 'error',
      },
      threshold: {
        warning: 1000,
        critical: 5000,
      },
    });

    this.metricsService.increment('repository_operations_total', {
      service: this.prisma.getServiceName(),
      model: this.modelName,
      operation,
      status: success ? 'success' : 'error',
    });
  }

  /**
   * Record business metrics
   */
  protected recordBusinessMetric(metric: string, value: number): void {
    this.metricsService.recordBusinessMetric({
      name: metric,
      value,
      type: 'counter' as any,
      category: BusinessMetricCategory.CONTENT,
      timestamp: new Date(),
      tags: {
        service: this.prisma.getServiceName(),
        model: this.modelName,
      },
    });
  }
}
