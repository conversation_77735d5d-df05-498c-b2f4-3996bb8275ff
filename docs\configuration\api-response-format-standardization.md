# 🔄 API Response Format Standardization

**Standardizing API response formats, error handling, and data structures across all services**

## 📋 Current API Response Issues

### **Issues Identified:**
1. **Inconsistent Response Formats** across services
2. **Mixed Error Handling** patterns (some use standard format, others don't)
3. **No Standardized Pagination** structure
4. **Inconsistent Status Codes** and error responses
5. **Missing Response Metadata** (correlation IDs, timestamps)
6. **No Unified DTO Structure** for responses
7. **Inconsistent API Documentation** patterns
8. **Mixed Success/Error Response Structures**

### **Impact:**
- Frontend integration complexity due to inconsistent response formats
- Difficult error handling and debugging across services
- Poor developer experience with unpredictable API responses
- Inconsistent logging and monitoring capabilities
- Hard to implement client-side caching and state management

## 🎯 Standardization Objectives

1. **Unified Response Structure**: Consistent response format across all services
2. **Standardized Error Handling**: Uniform error response patterns
3. **Comprehensive Pagination**: Consistent pagination across all list endpoints
4. **Status Code Standards**: Proper HTTP status code usage
5. **Response Metadata**: Correlation IDs, timestamps, and service information
6. **Type-Safe DTOs**: Strongly typed response interfaces
7. **API Documentation**: Consistent OpenAPI/Swagger documentation

## 🏗️ Standardized Response Architecture

### **Response Structure Hierarchy**
```
shared/responses/                      # Shared response infrastructure
├── interfaces/                       # Response interfaces
│   ├── base-response.interface.ts    # Core response interfaces
│   ├── pagination.interface.ts      # Pagination interfaces
│   └── error-response.interface.ts   # Error response interfaces
├── dto/                              # Response DTOs
│   ├── base-response.dto.ts         # Base response DTO
│   ├── paginated-response.dto.ts    # Paginated response DTO
│   └── error-response.dto.ts        # Error response DTO
├── interceptors/                     # Response interceptors
│   ├── response-transform.interceptor.ts # Response transformation
│   └── error-handling.interceptor.ts     # Error handling
├── decorators/                       # Response decorators
│   ├── api-response.decorator.ts    # API response decorators
│   └── paginated.decorator.ts       # Pagination decorators
└── utils/                           # Response utilities
    ├── response-builder.util.ts     # Response building utilities
    └── status-code.util.ts          # Status code utilities
```

## 📝 Core Response Interfaces

### **Base Response Interface**
```typescript
// shared/responses/interfaces/base-response.interface.ts
export interface BaseResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  correlationId: string;
  timestamp: string;
  service: string;
  version: string;
}

export interface SuccessResponse<T = any> extends BaseResponse<T> {
  success: true;
  data: T;
}

export interface ErrorResponse extends BaseResponse<never> {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    stack?: string; // Only in development
    validation?: ValidationError[];
  };
  statusCode: number;
}
```

### **Pagination Interface**
```typescript
// shared/responses/interfaces/pagination.interface.ts
export interface PaginationMeta {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  nextPage?: number;
  prevPage?: number;
}

export interface PaginatedResponse<T = any> extends BaseResponse<T[]> {
  success: true;
  data: T[];
  pagination: PaginationMeta;
  filters?: Record<string, any>;
  sorting?: SortingOptions;
}

export interface SortingOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface FilterOptions {
  field: string;
  operator: FilterOperator;
  value: any;
}

export enum FilterOperator {
  EQUALS = 'eq',
  NOT_EQUALS = 'ne',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  IN = 'in',
  NOT_IN = 'nin',
  CONTAINS = 'contains',
  STARTS_WITH = 'starts_with',
  ENDS_WITH = 'ends_with',
}
```

### **Error Response Interface**
```typescript
// shared/responses/interfaces/error-response.interface.ts
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
  constraints?: Record<string, string>;
}

export interface ErrorDetails {
  code: string;
  message: string;
  details?: any;
  stack?: string;
  validation?: ValidationError[];
  context?: Record<string, any>;
}

export enum ErrorCode {
  // Client Errors (4xx)
  BAD_REQUEST = 'BAD_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Server Errors (5xx)
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  GATEWAY_TIMEOUT = 'GATEWAY_TIMEOUT',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  
  // Business Logic Errors
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  RESOURCE_LOCKED = 'RESOURCE_LOCKED',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
}
```

## 🔧 Response DTOs

### **Base Response DTO**
```typescript
// shared/responses/dto/base-response.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsString, IsOptional, IsDateString } from 'class-validator';

export class BaseResponseDto<T = any> {
  @ApiProperty({
    description: 'Indicates if the request was successful',
    example: true,
  })
  @IsBoolean()
  success: boolean;

  @ApiPropertyOptional({
    description: 'Response data',
  })
  @IsOptional()
  data?: T;

  @ApiPropertyOptional({
    description: 'Optional message',
    example: 'Operation completed successfully',
  })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiProperty({
    description: 'Unique correlation ID for request tracking',
    example: 'req_1234567890_abcdef',
  })
  @IsString()
  correlationId: string;

  @ApiProperty({
    description: 'Response timestamp in ISO format',
    example: '2025-06-08T12:00:00.000Z',
  })
  @IsDateString()
  timestamp: string;

  @ApiProperty({
    description: 'Service that generated the response',
    example: 'user-service',
  })
  @IsString()
  service: string;

  @ApiProperty({
    description: 'Service version',
    example: '1.0.0',
  })
  @IsString()
  version: string;
}
```

### **Paginated Response DTO**
```typescript
// shared/responses/dto/paginated-response.dto.ts
export class PaginationMetaDto {
  @ApiProperty({ description: 'Current page number', example: 1 })
  @IsNumber()
  page: number;

  @ApiProperty({ description: 'Items per page', example: 20 })
  @IsNumber()
  limit: number;

  @ApiProperty({ description: 'Total number of items', example: 150 })
  @IsNumber()
  totalCount: number;

  @ApiProperty({ description: 'Total number of pages', example: 8 })
  @IsNumber()
  totalPages: number;

  @ApiProperty({ description: 'Has next page', example: true })
  @IsBoolean()
  hasNext: boolean;

  @ApiProperty({ description: 'Has previous page', example: false })
  @IsBoolean()
  hasPrev: boolean;

  @ApiPropertyOptional({ description: 'Next page number', example: 2 })
  @IsOptional()
  @IsNumber()
  nextPage?: number;

  @ApiPropertyOptional({ description: 'Previous page number', example: null })
  @IsOptional()
  @IsNumber()
  prevPage?: number;
}

export class PaginatedResponseDto<T> extends BaseResponseDto<T[]> {
  @ApiProperty({
    description: 'Array of items for current page',
    type: 'array',
  })
  data: T[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: PaginationMetaDto,
  })
  pagination: PaginationMetaDto;

  @ApiPropertyOptional({
    description: 'Applied filters',
    example: { status: 'active', category: 'nft' },
  })
  @IsOptional()
  filters?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Applied sorting',
    example: { field: 'createdAt', direction: 'desc' },
  })
  @IsOptional()
  sorting?: SortingOptions;
}
```

### **Error Response DTO**
```typescript
// shared/responses/dto/error-response.dto.ts
export class ValidationErrorDto {
  @ApiProperty({ description: 'Field name with validation error' })
  @IsString()
  field: string;

  @ApiProperty({ description: 'Validation error message' })
  @IsString()
  message: string;

  @ApiPropertyOptional({ description: 'Invalid value' })
  @IsOptional()
  value?: any;

  @ApiPropertyOptional({ description: 'Validation constraints' })
  @IsOptional()
  constraints?: Record<string, string>;
}

export class ErrorDetailsDto {
  @ApiProperty({
    description: 'Error code',
    enum: ErrorCode,
    example: ErrorCode.VALIDATION_ERROR,
  })
  @IsEnum(ErrorCode)
  code: ErrorCode;

  @ApiProperty({
    description: 'Human-readable error message',
    example: 'Validation failed for the provided data',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Additional error details',
  })
  @IsOptional()
  details?: any;

  @ApiPropertyOptional({
    description: 'Stack trace (development only)',
  })
  @IsOptional()
  @IsString()
  stack?: string;

  @ApiPropertyOptional({
    description: 'Validation errors',
    type: [ValidationErrorDto],
  })
  @IsOptional()
  @IsArray()
  validation?: ValidationErrorDto[];

  @ApiPropertyOptional({
    description: 'Additional error context',
  })
  @IsOptional()
  context?: Record<string, any>;
}

export class ErrorResponseDto extends BaseResponseDto<never> {
  @ApiProperty({
    description: 'Always false for error responses',
    example: false,
  })
  success: false;

  @ApiProperty({
    description: 'Error details',
    type: ErrorDetailsDto,
  })
  error: ErrorDetailsDto;

  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
  })
  @IsNumber()
  statusCode: number;
}
```

## 🚀 Implementation Plan

### **Phase 1: Create Shared Response Infrastructure**
1. Create shared response interfaces and DTOs
2. Implement response transformation interceptors
3. Create response building utilities
4. Implement error handling interceptors

### **Phase 2: Standardize Service Responses**
1. Update all controllers to use standardized response DTOs
2. Implement response transformation across services
3. Standardize error handling patterns
4. Update API documentation

### **Phase 3: Implement Pagination Standards**
1. Create pagination utilities and decorators
2. Standardize list endpoint responses
3. Implement filtering and sorting standards
4. Update frontend integration

### **Phase 4: Advanced Response Features**
1. Implement response caching headers
2. Add response compression
3. Implement API versioning in responses
4. Add response performance metrics

## 📊 Benefits of Standardization

1. **Consistent Developer Experience**: Predictable API responses across all services
2. **Simplified Frontend Integration**: Unified response handling in client applications
3. **Better Error Handling**: Comprehensive error information with proper status codes
4. **Enhanced Debugging**: Correlation IDs and detailed error context
5. **Improved Performance**: Standardized caching and compression
6. **Better Documentation**: Consistent OpenAPI/Swagger documentation
7. **Type Safety**: Strongly typed responses with validation

## 🔍 Next Steps

1. **Create Shared Response Infrastructure**
2. **Implement Response Transformation Interceptors**
3. **Update All Service Controllers**
4. **Standardize Error Handling**
5. **Test Response Standardization**
