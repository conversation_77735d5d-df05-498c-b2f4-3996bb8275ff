'use client'

import React from 'react'
import {
  SparklesIcon,
  ClockIcon,
  ChartBarIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  CalendarIcon,
  TrophyIcon
} from '@heroicons/react/24/outline'
import { usePredictedEvolution, useUpdateEvolutionPrediction } from '@/hooks/useEvolution'
import { NFTRarity } from '@/types/nft.types'

interface EvolutionPredictionProps {
  nftId: string
  className?: string
}

export default function EvolutionPrediction({
  nftId,
  className = ''
}: EvolutionPredictionProps) {
  const { data: prediction, isLoading, refetch } = usePredictedEvolution(nftId)
  const updatePredictionMutation = useUpdateEvolutionPrediction()

  const handleUpdatePrediction = async () => {
    try {
      await updatePredictionMutation.mutateAsync(nftId)
      refetch()
    } catch (error) {
      console.error('Failed to update prediction:', error)
    }
  }

  const getRarityColor = (rarity: NFTRarity) => {
    switch (rarity) {
      case NFTRarity.MYTHIC: return 'text-purple-600 bg-purple-100 border-purple-200'
      case NFTRarity.LEGENDARY: return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      case NFTRarity.EPIC: return 'text-purple-500 bg-purple-100 border-purple-200'
      case NFTRarity.RARE: return 'text-blue-500 bg-blue-100 border-blue-200'
      case NFTRarity.COMMON: return 'text-gray-500 bg-gray-100 border-gray-200'
      default: return 'text-gray-500 bg-gray-100 border-gray-200'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600 bg-green-100'
    if (confidence >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 80) return 'High Confidence'
    if (confidence >= 60) return 'Medium Confidence'
    return 'Low Confidence'
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = date.getTime() - now.getTime()
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24))

    if (diffDays < 0) return 'Overdue'
    if (diffDays === 0) return 'Today'
    if (diffDays === 1) return 'Tomorrow'
    if (diffDays < 7) return `In ${diffDays} days`
    if (diffDays < 30) return `In ${Math.ceil(diffDays / 7)} weeks`
    return `In ${Math.ceil(diffDays / 30)} months`
  }

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!prediction) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center py-8">
          <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No prediction available</h3>
          <p className="mt-1 text-sm text-gray-500">
            Evolution predictions will be generated as your NFT accumulates more data.
          </p>
          <button
            onClick={handleUpdatePrediction}
            disabled={updatePredictionMutation.isPending}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {updatePredictionMutation.isPending ? 'Generating...' : 'Generate Prediction'}
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <SparklesIcon className="h-5 w-5 mr-2 text-gray-500" />
              Evolution Prediction
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              AI-powered prediction for your NFT's next evolution
            </p>
          </div>
          
          <button
            onClick={handleUpdatePrediction}
            disabled={updatePredictionMutation.isPending}
            className="text-sm text-blue-600 hover:text-blue-500 font-medium disabled:opacity-50"
          >
            {updatePredictionMutation.isPending ? 'Updating...' : 'Update Prediction'}
          </button>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Main Prediction */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <TrophyIcon className="h-8 w-8 text-purple-600" />
              <div>
                <h4 className="text-lg font-semibold text-gray-900">Next Evolution</h4>
                <p className="text-sm text-gray-600">Predicted target rarity</p>
              </div>
            </div>
            <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getRarityColor(prediction.targetRarity)}`}>
              {prediction.targetRarity.charAt(0).toUpperCase() + prediction.targetRarity.slice(1)}
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <CalendarIcon className="h-6 w-6 mx-auto text-gray-500 mb-2" />
              <p className="text-sm text-gray-600">Estimated Date</p>
              <p className="text-lg font-semibold text-gray-900">
                {formatDate(prediction.estimatedDate)}
              </p>
              <p className="text-xs text-gray-500">
                {new Date(prediction.estimatedDate).toLocaleDateString()}
              </p>
            </div>

            <div className="text-center">
              <ChartBarIcon className="h-6 w-6 mx-auto text-gray-500 mb-2" />
              <p className="text-sm text-gray-600">Score Needed</p>
              <p className="text-lg font-semibold text-gray-900">
                +{prediction.requiredScoreIncrease}
              </p>
              <p className="text-xs text-gray-500">points to evolve</p>
            </div>

            <div className="text-center">
              <ArrowTrendingUpIcon className="h-6 w-6 mx-auto text-gray-500 mb-2" />
              <p className="text-sm text-gray-600">Confidence</p>
              <p className="text-lg font-semibold text-gray-900">{prediction.confidence}%</p>
              <span className={`inline-block mt-1 px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(prediction.confidence)}`}>
                {getConfidenceLabel(prediction.confidence)}
              </span>
            </div>
          </div>
        </div>

        {/* Prediction Factors */}
        {prediction.factors && prediction.factors.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <ChartBarIcon className="h-5 w-5 mr-2 text-gray-500" />
              Key Factors
            </h4>
            <div className="space-y-3">
              {prediction.factors.map((factor, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-gray-900">{factor.factor}</h5>
                    <span className="text-sm text-gray-500">
                      {(factor.importance * 100).toFixed(0)}% importance
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">{factor.description}</p>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <span className="text-gray-600">
                        Current: <span className="font-medium">{factor.currentValue}</span>
                      </span>
                      <span className="text-gray-600">
                        Target: <span className="font-medium">{factor.targetValue}</span>
                      </span>
                    </div>
                    
                    {/* Progress Bar */}
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${Math.min(100, (factor.currentValue / factor.targetValue) * 100)}%`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recommendations */}
        {prediction.recommendations && prediction.recommendations.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <LightBulbIcon className="h-5 w-5 mr-2 text-gray-500" />
              Recommendations
            </h4>
            <div className="space-y-3">
              {prediction.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start space-x-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <LightBulbIcon className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-yellow-900 mb-1">
                      Recommendation #{index + 1}
                    </p>
                    <p className="text-sm text-yellow-800">{recommendation}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Confidence Warning */}
        {prediction.confidence < 60 && (
          <div className="flex items-start space-x-3 p-4 bg-orange-50 rounded-lg border border-orange-200">
            <ExclamationTriangleIcon className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-orange-900 mb-1">Low Confidence Prediction</p>
              <p className="text-sm text-orange-800">
                This prediction has low confidence due to limited data. Continue engaging with your NFT 
                to improve prediction accuracy. More evolution events will help our AI make better predictions.
              </p>
            </div>
          </div>
        )}

        {/* Prediction Disclaimer */}
        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <p className="text-xs text-gray-600">
            <strong>Disclaimer:</strong> Evolution predictions are estimates based on current data and historical patterns. 
            Actual evolution timing may vary based on social activity, engagement, and other dynamic factors. 
            Predictions are updated regularly as new data becomes available.
          </p>
        </div>
      </div>
    </div>
  )
}
