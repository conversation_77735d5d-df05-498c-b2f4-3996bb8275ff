/**
 * Core Logging Interfaces
 * Defines the standard logging structure for all services
 */

/**
 * Log levels enumeration
 */
export enum LogLevel {
  TRACE = 'trace',
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

/**
 * Log format enumeration
 */
export enum LogFormat {
  JSON = 'json',
  TEXT = 'text',
  COMBINED = 'combined',
}

/**
 * Structured logger interface
 */
export interface IStructuredLogger {
  log(level: LogLevel, message: string, context?: LogContext): void;
  info(message: string, context?: LogContext): void;
  warn(message: string, context?: LogContext): void;
  error(message: string, error?: Error, context?: LogContext): void;
  debug(message: string, context?: LogContext): void;
  trace(message: string, context?: LogContext): void;
  fatal(message: string, error?: Error, context?: LogContext): void;
  
  // Context management
  setContext(context: Partial<LogContext>): void;
  getContext(): LogContext;
  clearContext(): void;
  
  // Child logger creation
  child(context: Partial<LogContext>): IStructuredLogger;
}

/**
 * Log context interface
 */
export interface LogContext {
  correlationId?: string;
  traceId?: string;
  spanId?: string;
  userId?: string;
  sessionId?: string;
  service?: string;
  operation?: string;
  metadata?: Record<string, any>;
  performance?: PerformanceMetrics;
  request?: RequestContext;
  response?: ResponseContext;
  error?: ErrorContext;
  security?: SecurityContext;
  business?: BusinessContext;
}

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryUsage?: NodeJS.MemoryUsage;
  cpuUsage?: NodeJS.CpuUsage;
  dbQueryCount?: number;
  dbQueryTime?: number;
  cacheHits?: number;
  cacheMisses?: number;
  externalApiCalls?: number;
  externalApiTime?: number;
}

/**
 * Request context interface
 */
export interface RequestContext {
  method: string;
  path: string;
  url?: string;
  userAgent?: string;
  ipAddress?: string;
  headers?: Record<string, string>;
  query?: Record<string, any>;
  params?: Record<string, any>;
  body?: any;
  contentType?: string;
  contentLength?: number;
  referer?: string;
}

/**
 * Response context interface
 */
export interface ResponseContext {
  statusCode: number;
  contentType?: string;
  contentLength?: number;
  headers?: Record<string, string>;
  body?: any;
  cached?: boolean;
  compressed?: boolean;
}

/**
 * Error context interface
 */
export interface ErrorContext {
  name: string;
  message: string;
  stack?: string;
  code?: string;
  statusCode?: number;
  cause?: any;
  details?: Record<string, any>;
  fingerprint?: string;
  tags?: string[];
}

/**
 * Security context interface
 */
export interface SecurityContext {
  eventType: SecurityEventType;
  severity: SecuritySeverity;
  actor?: {
    userId?: string;
    sessionId?: string;
    ipAddress?: string;
    userAgent?: string;
  };
  resource?: {
    type: string;
    id?: string;
    attributes?: Record<string, any>;
  };
  action?: string;
  outcome: SecurityOutcome;
  riskScore?: number;
  details?: Record<string, any>;
}

/**
 * Business context interface
 */
export interface BusinessContext {
  domain: string;
  entity?: string;
  entityId?: string;
  action: string;
  outcome: BusinessOutcome;
  metrics?: Record<string, number>;
  attributes?: Record<string, any>;
  tags?: string[];
}

/**
 * Security event types
 */
export enum SecurityEventType {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  CONFIGURATION_CHANGE = 'configuration_change',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  POLICY_VIOLATION = 'policy_violation',
  SECURITY_SCAN = 'security_scan',
  VULNERABILITY = 'vulnerability',
  INCIDENT = 'incident',
}

/**
 * Security severity levels
 */
export enum SecuritySeverity {
  INFO = 'info',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Security outcomes
 */
export enum SecurityOutcome {
  SUCCESS = 'success',
  FAILURE = 'failure',
  BLOCKED = 'blocked',
  ALLOWED = 'allowed',
  DETECTED = 'detected',
  PREVENTED = 'prevented',
}

/**
 * Business outcomes
 */
export enum BusinessOutcome {
  SUCCESS = 'success',
  FAILURE = 'failure',
  PARTIAL = 'partial',
  CANCELLED = 'cancelled',
  PENDING = 'pending',
  TIMEOUT = 'timeout',
}

/**
 * Log entry interface
 */
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context: LogContext;
  service: string;
  environment: string;
  version: string;
  hostname?: string;
  pid?: number;
  thread?: string;
}

/**
 * Log transport interface
 */
export interface ILogTransport {
  name: string;
  level: LogLevel;
  write(entry: LogEntry): Promise<void>;
  flush?(): Promise<void>;
  close?(): Promise<void>;
}

/**
 * Log formatter interface
 */
export interface ILogFormatter {
  format(entry: LogEntry): string;
}

/**
 * Log filter interface
 */
export interface ILogFilter {
  shouldLog(entry: LogEntry): boolean;
}

/**
 * Logger configuration interface
 */
export interface LoggerConfig {
  level: LogLevel;
  format: LogFormat;
  transports: LogTransportConfig[];
  filters?: ILogFilter[];
  defaultContext?: Partial<LogContext>;
  enableCorrelationTracking?: boolean;
  enablePerformanceTracking?: boolean;
  enableSecurityLogging?: boolean;
  enableBusinessLogging?: boolean;
  sanitizeFields?: string[];
  maxMessageLength?: number;
  maxContextSize?: number;
}

/**
 * Log transport configuration
 */
export interface LogTransportConfig {
  type: LogTransportType;
  level?: LogLevel;
  options?: Record<string, any>;
}

/**
 * Log transport types
 */
export enum LogTransportType {
  CONSOLE = 'console',
  FILE = 'file',
  HTTP = 'http',
  ELASTICSEARCH = 'elasticsearch',
  SYSLOG = 'syslog',
  STREAM = 'stream',
}

/**
 * Log aggregation interface
 */
export interface ILogAggregator {
  aggregate(entries: LogEntry[]): LogAggregation;
}

/**
 * Log aggregation result
 */
export interface LogAggregation {
  totalEntries: number;
  entriesByLevel: Record<LogLevel, number>;
  entriesByService: Record<string, number>;
  timeRange: {
    start: string;
    end: string;
  };
  topErrors: ErrorSummary[];
  performanceMetrics: PerformanceSummary;
  securityEvents: SecuritySummary[];
}

/**
 * Error summary interface
 */
export interface ErrorSummary {
  message: string;
  count: number;
  services: string[];
  firstOccurrence: string;
  lastOccurrence: string;
  fingerprint: string;
}

/**
 * Performance summary interface
 */
export interface PerformanceSummary {
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  totalRequests: number;
  errorRate: number;
  slowestOperations: OperationSummary[];
}

/**
 * Operation summary interface
 */
export interface OperationSummary {
  operation: string;
  service: string;
  averageTime: number;
  maxTime: number;
  count: number;
}

/**
 * Security summary interface
 */
export interface SecuritySummary {
  eventType: SecurityEventType;
  severity: SecuritySeverity;
  count: number;
  services: string[];
  firstOccurrence: string;
  lastOccurrence: string;
}
