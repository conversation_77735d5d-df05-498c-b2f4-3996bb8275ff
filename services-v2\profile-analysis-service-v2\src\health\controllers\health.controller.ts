/**
 * Health Controller - Profile Analysis Service V2
 * 
 * Health check endpoints
 */

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from '../services/health.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async getSimpleHealth() {
    const health = await this.healthService.getSimpleHealth();
    
    return {
      success: true,
      data: health,
      message: 'Profile Analysis Service V2 is healthy',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }

  @Get('detailed')
  @ApiOperation({ summary: 'Detailed health check with dependencies' })
  @ApiResponse({ status: 200, description: 'Detailed health information' })
  async getDetailedHealth() {
    const health = await this.healthService.getDetailedHealth();
    
    if (health.status === 'healthy') {
      return {
        success: true,
        data: health,
        message: 'Profile Analysis Service V2 is healthy',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    } else {
      return {
        success: false,
        data: health,
        message: 'Profile Analysis Service V2 is unhealthy',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
  }

  @Get('database')
  @ApiOperation({ summary: 'Database health check' })
  @ApiResponse({ status: 200, description: 'Database health information' })
  async getDatabaseHealth() {
    const health = await this.healthService.getDatabaseHealth();
    
    if (health.status === 'healthy') {
      return {
        success: true,
        data: health,
        message: 'Profile Analysis database is healthy',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    } else {
      return {
        success: false,
        data: health,
        message: 'Profile Analysis database is unhealthy',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
  }

  @Get('ready')
  @ApiOperation({ summary: 'Readiness probe for Kubernetes' })
  @ApiResponse({ status: 200, description: 'Service is ready' })
  @ApiResponse({ status: 503, description: 'Service is not ready' })
  async getReadiness() {
    const readiness = await this.healthService.getReadiness();
    
    if (readiness.status === 'ready') {
      return {
        success: true,
        data: readiness,
        message: 'Profile Analysis Service V2 is ready',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    } else {
      return {
        success: false,
        data: readiness,
        message: 'Profile Analysis Service V2 is not ready',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
  }

  @Get('live')
  @ApiOperation({ summary: 'Liveness probe for Kubernetes' })
  @ApiResponse({ status: 200, description: 'Service is alive' })
  async getLiveness() {
    const liveness = await this.healthService.getLiveness();
    
    return {
      success: true,
      data: liveness,
      message: 'Profile Analysis Service V2 is alive',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }
}
