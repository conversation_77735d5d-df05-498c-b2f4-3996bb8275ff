import { Injectable, Logger, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as crypto from 'crypto';
import { UserManagementService, User, UserSession } from './user-management.service';

export interface LoginRequest {
  email: string;
  password: string;
  deviceName?: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  session: UserSession;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface TokenPayload {
  sub: string; // user ID
  email: string;
  username: string;
  sessionId: string;
  iat: number;
  exp: number;
  type: 'access' | 'refresh';
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirmRequest {
  token: string;
  newPassword: string;
}

export interface EmailVerificationRequest {
  token: string;
}

@Injectable()
export class AuthenticationService {
  private readonly logger = new Logger(AuthenticationService.name);
  private refreshTokens: Map<string, { userId: string; sessionId: string; expiresAt: Date }> = new Map();
  private passwordResetTokens: Map<string, { userId: string; expiresAt: Date }> = new Map();
  private emailVerificationTokens: Map<string, { userId: string; expiresAt: Date }> = new Map();

  constructor(
    private readonly userManagement: UserManagementService,
    private readonly jwtService: JwtService
  ) {}

  /**
   * Authenticate user and create session
   */
  async login(loginRequest: LoginRequest, ipAddress: string = 'unknown', userAgent: string = 'unknown'): Promise<LoginResponse> {
    try {
      this.logger.log(`Login attempt for: ${loginRequest.email}`);

      // Get user by email
      const user = await this.userManagement.getUserByEmail(loginRequest.email);
      if (!user) {
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check if account is locked
      if (user.security.lockedUntil && user.security.lockedUntil > new Date()) {
        throw new UnauthorizedException('Account is temporarily locked');
      }

      // Verify password
      const isPasswordValid = await this.userManagement.verifyPassword(user.id, loginRequest.password);
      if (!isPasswordValid) {
        await this.handleFailedLogin(user.id, ipAddress, userAgent);
        throw new UnauthorizedException('Invalid credentials');
      }

      // Reset failed login attempts
      await this.resetFailedLoginAttempts(user.id);

      // Create session
      const session = await this.createUserSession(user.id, loginRequest.deviceName || 'Unknown Device', ipAddress, userAgent, loginRequest.rememberMe);

      // Generate tokens
      const accessToken = await this.generateAccessToken(user, session.id);
      const refreshToken = await this.generateRefreshToken(user.id, session.id, loginRequest.rememberMe);

      // Update user login metadata
      await this.updateLoginMetadata(user.id, ipAddress, userAgent);

      this.logger.log(`Login successful for user: ${user.id}`);

      return {
        user,
        accessToken,
        refreshToken,
        expiresIn: 3600, // 1 hour
        session,
      };
    } catch (error) {
      this.logger.error(`Login failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshRequest: RefreshTokenRequest): Promise<{ accessToken: string; expiresIn: number }> {
    try {
      this.logger.log('Token refresh attempt');

      // Validate refresh token
      const tokenData = this.refreshTokens.get(refreshRequest.refreshToken);
      if (!tokenData || tokenData.expiresAt < new Date()) {
        throw new UnauthorizedException('Invalid or expired refresh token');
      }

      // Get user
      const user = await this.userManagement.getUserById(tokenData.userId);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Generate new access token
      const accessToken = await this.generateAccessToken(user, tokenData.sessionId);

      this.logger.log(`Token refreshed for user: ${user.id}`);

      return {
        accessToken,
        expiresIn: 3600, // 1 hour
      };
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Logout user and invalidate session
   */
  async logout(userId: string, sessionId: string): Promise<void> {
    try {
      this.logger.log(`Logout for user: ${userId}, session: ${sessionId}`);

      // Invalidate session
      await this.invalidateUserSession(userId, sessionId);

      // Remove refresh tokens for this session
      for (const [token, data] of this.refreshTokens.entries()) {
        if (data.userId === userId && data.sessionId === sessionId) {
          this.refreshTokens.delete(token);
        }
      }

      this.logger.log(`Logout successful for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Logout failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Logout from all devices
   */
  async logoutAll(userId: string): Promise<void> {
    try {
      this.logger.log(`Logout all devices for user: ${userId}`);

      // Invalidate all sessions
      await this.invalidateAllUserSessions(userId);

      // Remove all refresh tokens for this user
      for (const [token, data] of this.refreshTokens.entries()) {
        if (data.userId === userId) {
          this.refreshTokens.delete(token);
        }
      }

      this.logger.log(`Logout all successful for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Logout all failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Verify JWT token
   */
  async verifyToken(token: string): Promise<TokenPayload> {
    try {
      const payload = this.jwtService.verify(token) as TokenPayload;
      
      // Verify user still exists
      const user = await this.userManagement.getUserById(payload.sub);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Verify session is still active
      const isSessionActive = await this.isSessionActive(payload.sub, payload.sessionId);
      if (!isSessionActive) {
        throw new UnauthorizedException('Session expired');
      }

      return payload;
    } catch (error) {
      this.logger.error(`Token verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(request: PasswordResetRequest): Promise<void> {
    try {
      this.logger.log(`Password reset requested for: ${request.email}`);

      const user = await this.userManagement.getUserByEmail(request.email);
      if (!user) {
        // Don't reveal if email exists
        this.logger.log(`Password reset requested for non-existent email: ${request.email}`);
        return;
      }

      // Generate reset token
      const resetToken = this.generateSecureToken();
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      this.passwordResetTokens.set(resetToken, {
        userId: user.id,
        expiresAt,
      });

      // In a real implementation, send email here
      this.logger.log(`Password reset token generated for user: ${user.id}`);
      this.logger.debug(`Reset token (for testing): ${resetToken}`);
    } catch (error) {
      this.logger.error(`Password reset request failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Confirm password reset
   */
  async confirmPasswordReset(request: PasswordResetConfirmRequest): Promise<void> {
    try {
      this.logger.log('Password reset confirmation attempt');

      const tokenData = this.passwordResetTokens.get(request.token);
      if (!tokenData || tokenData.expiresAt < new Date()) {
        throw new BadRequestException('Invalid or expired reset token');
      }

      // Get user
      const user = await this.userManagement.getUserById(tokenData.userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      // Change password (bypass current password check)
      await this.forcePasswordChange(user.id, request.newPassword);

      // Remove reset token
      this.passwordResetTokens.delete(request.token);

      // Invalidate all sessions
      await this.logoutAll(user.id);

      this.logger.log(`Password reset successful for user: ${user.id}`);
    } catch (error) {
      this.logger.error(`Password reset confirmation failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(userId: string): Promise<void> {
    try {
      this.logger.log(`Email verification requested for user: ${userId}`);

      const user = await this.userManagement.getUserById(userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      if (user.emailVerified) {
        throw new BadRequestException('Email already verified');
      }

      // Generate verification token
      const verificationToken = this.generateSecureToken();
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      this.emailVerificationTokens.set(verificationToken, {
        userId: user.id,
        expiresAt,
      });

      // In a real implementation, send email here
      this.logger.log(`Email verification token generated for user: ${user.id}`);
      this.logger.debug(`Verification token (for testing): ${verificationToken}`);
    } catch (error) {
      this.logger.error(`Email verification request failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Verify email
   */
  async verifyEmail(request: EmailVerificationRequest): Promise<void> {
    try {
      this.logger.log('Email verification attempt');

      const tokenData = this.emailVerificationTokens.get(request.token);
      if (!tokenData || tokenData.expiresAt < new Date()) {
        throw new BadRequestException('Invalid or expired verification token');
      }

      // Update user email verification status
      const user = await this.userManagement.getUserById(tokenData.userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      // Mark email as verified and activate account
      await this.markEmailAsVerified(user.id);

      // Remove verification token
      this.emailVerificationTokens.delete(request.token);

      this.logger.log(`Email verified for user: ${user.id}`);
    } catch (error) {
      this.logger.error(`Email verification failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get user sessions
   */
  async getUserSessions(userId: string): Promise<UserSession[]> {
    try {
      const user = await this.userManagement.getUserById(userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      return user.security.sessions.filter(session => session.isActive);
    } catch (error) {
      this.logger.error(`Failed to get user sessions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create user session
   */
  private async createUserSession(
    userId: string,
    deviceName: string,
    ipAddress: string,
    userAgent: string,
    rememberMe: boolean = false
  ): Promise<UserSession> {
    const sessionId = this.generateSessionId();
    const deviceId = this.generateDeviceId(userAgent, ipAddress);
    const expiresAt = new Date(Date.now() + (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000)); // 30 days or 1 day

    const session: UserSession = {
      id: sessionId,
      deviceId,
      deviceName,
      ipAddress,
      userAgent,
      createdAt: new Date(),
      lastActiveAt: new Date(),
      expiresAt,
      isActive: true,
    };

    // Add session to user
    const user = await this.userManagement.getUserById(userId);
    if (user) {
      user.security.sessions.push(session);
      
      // Keep only last 10 sessions
      if (user.security.sessions.length > 10) {
        user.security.sessions = user.security.sessions.slice(-10);
      }
    }

    return session;
  }

  /**
   * Generate access token
   */
  private async generateAccessToken(user: User, sessionId: string): Promise<string> {
    const payload: Omit<TokenPayload, 'iat' | 'exp'> = {
      sub: user.id,
      email: user.email,
      username: user.username,
      sessionId,
      type: 'access',
    };

    return this.jwtService.sign(payload, { expiresIn: '1h' });
  }

  /**
   * Generate refresh token
   */
  private async generateRefreshToken(userId: string, sessionId: string, rememberMe: boolean = false): Promise<string> {
    const refreshToken = this.generateSecureToken();
    const expiresAt = new Date(Date.now() + (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000)); // 30 days or 7 days

    this.refreshTokens.set(refreshToken, {
      userId,
      sessionId,
      expiresAt,
    });

    return refreshToken;
  }

  /**
   * Handle failed login attempt
   */
  private async handleFailedLogin(userId: string, ipAddress: string, userAgent: string): Promise<void> {
    const user = await this.userManagement.getUserById(userId);
    if (!user) return;

    user.security.failedLoginAttempts += 1;

    // Lock account after 5 failed attempts
    if (user.security.failedLoginAttempts >= 5) {
      user.security.lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      this.logger.warn(`Account locked due to failed login attempts: ${userId}`);
    }

    // Log security event
    // await this.logSecurityEvent(userId, 'failed_login', { ipAddress, userAgent });
  }

  /**
   * Reset failed login attempts
   */
  private async resetFailedLoginAttempts(userId: string): Promise<void> {
    const user = await this.userManagement.getUserById(userId);
    if (!user) return;

    user.security.failedLoginAttempts = 0;
    user.security.lockedUntil = undefined;
  }

  /**
   * Update login metadata
   */
  private async updateLoginMetadata(userId: string, ipAddress: string, userAgent: string): Promise<void> {
    const user = await this.userManagement.getUserById(userId);
    if (!user) return;

    user.metadata.lastLoginAt = new Date();
    user.metadata.loginCount += 1;
    user.metadata.ipAddress = ipAddress;
    user.metadata.userAgent = userAgent;
    user.metadata.updatedAt = new Date();
  }

  /**
   * Force password change (for reset)
   */
  private async forcePasswordChange(userId: string, newPassword: string): Promise<void> {
    // This bypasses the current password check
    const bcrypt = require('bcrypt');
    const user = await this.userManagement.getUserById(userId);
    if (!user) return;

    const passwordHash = await bcrypt.hash(newPassword, 12);
    user.passwordHash = passwordHash;
    user.security.passwordChangedAt = new Date();
    user.metadata.updatedAt = new Date();
  }

  /**
   * Mark email as verified
   */
  private async markEmailAsVerified(userId: string): Promise<void> {
    const user = await this.userManagement.getUserById(userId);
    if (!user) return;

    user.emailVerified = true;
    user.status = 'active';
    user.metadata.updatedAt = new Date();
  }

  /**
   * Check if session is active
   */
  private async isSessionActive(userId: string, sessionId: string): Promise<boolean> {
    const user = await this.userManagement.getUserById(userId);
    if (!user) return false;

    const session = user.security.sessions.find(s => s.id === sessionId);
    return session ? session.isActive && session.expiresAt > new Date() : false;
  }

  /**
   * Invalidate user session
   */
  private async invalidateUserSession(userId: string, sessionId: string): Promise<void> {
    const user = await this.userManagement.getUserById(userId);
    if (!user) return;

    const session = user.security.sessions.find(s => s.id === sessionId);
    if (session) {
      session.isActive = false;
    }
  }

  /**
   * Invalidate all user sessions
   */
  private async invalidateAllUserSessions(userId: string): Promise<void> {
    const user = await this.userManagement.getUserById(userId);
    if (!user) return;

    user.security.sessions.forEach(session => {
      session.isActive = false;
    });
  }

  /**
   * Generate secure token
   */
  private generateSecureToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate device ID
   */
  private generateDeviceId(userAgent: string, ipAddress: string): string {
    const hash = crypto.createHash('sha256');
    hash.update(userAgent + ipAddress);
    return hash.digest('hex').substr(0, 16);
  }
}
