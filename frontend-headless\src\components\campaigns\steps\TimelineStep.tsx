'use client'

import React from 'react'
import {
  CalendarIcon,
  ClockIcon,
  UsersIcon,
  GlobeAltIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { CreateCampaignRequest } from '@/types/campaign.types'

interface TimelineStepProps {
  data: Partial<CreateCampaignRequest>
  updateData: (updates: Partial<CreateCampaignRequest>) => void
  errors: string[]
}

export default function TimelineStep({ data, updateData, errors }: TimelineStepProps) {
  const today = new Date().toISOString().split('T')[0]
  const oneWeekFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  const oneMonthFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

  const durationPresets = [
    { label: '1 Week', days: 7 },
    { label: '2 Weeks', days: 14 },
    { label: '1 Month', days: 30 },
    { label: '2 Months', days: 60 },
    { label: '3 Months', days: 90 },
    { label: 'Custom', days: null }
  ]

  const regions = [
    'Global',
    'North America',
    'Europe',
    'Asia Pacific',
    'Latin America',
    'Middle East & Africa',
    'United States',
    'Canada',
    'United Kingdom',
    'Germany',
    'France',
    'Japan',
    'Australia',
    'Brazil',
    'India'
  ]

  const handleDurationPreset = (days: number | null) => {
    if (days && data.startDate) {
      const startDate = new Date(data.startDate)
      const endDate = new Date(startDate.getTime() + days * 24 * 60 * 60 * 1000)
      updateData({ endDate: endDate.toISOString().split('T')[0] })
    }
  }

  const calculateDuration = () => {
    if (data.startDate && data.endDate) {
      const start = new Date(data.startDate)
      const end = new Date(data.endDate)
      const diffTime = Math.abs(end.getTime() - start.getTime())
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays
    }
    return 0
  }

  const handleGeoRestrictionToggle = (region: string) => {
    const currentRestrictions = data.geoRestrictions || []
    if (region === 'Global') {
      updateData({ geoRestrictions: [] })
    } else {
      const newRestrictions = currentRestrictions.includes(region)
        ? currentRestrictions.filter(r => r !== region)
        : [...currentRestrictions.filter(r => r !== 'Global'), region]
      updateData({ geoRestrictions: newRestrictions })
    }
  }

  return (
    <div className="space-y-6">
      {/* Campaign Timeline */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Start Date */}
        <div>
          <label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-2">
            <CalendarIcon className="inline h-4 w-4 mr-1" />
            Start Date *
          </label>
          <input
            type="date"
            id="start-date"
            value={data.startDate || ''}
            min={today}
            onChange={(e) => updateData({ startDate: e.target.value })}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${
              errors.some(e => e.includes('Start date')) ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          <p className="mt-1 text-sm text-gray-500">
            When should your campaign begin?
          </p>
        </div>

        {/* End Date */}
        <div>
          <label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-2">
            <CalendarIcon className="inline h-4 w-4 mr-1" />
            End Date *
          </label>
          <input
            type="date"
            id="end-date"
            value={data.endDate || ''}
            min={data.startDate || today}
            onChange={(e) => updateData({ endDate: e.target.value })}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${
              errors.some(e => e.includes('End date')) ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          <p className="mt-1 text-sm text-gray-500">
            When should your campaign end?
          </p>
        </div>
      </div>

      {/* Duration Presets */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <ClockIcon className="inline h-4 w-4 mr-1" />
          Quick Duration Selection
        </label>
        <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
          {durationPresets.map((preset) => (
            <button
              key={preset.label}
              onClick={() => handleDurationPreset(preset.days)}
              disabled={!data.startDate}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {preset.label}
            </button>
          ))}
        </div>
        {calculateDuration() > 0 && (
          <p className="mt-2 text-sm text-blue-600">
            Campaign duration: {calculateDuration()} days
          </p>
        )}
      </div>

      {/* Participant Limits */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Max Participants */}
        <div>
          <label htmlFor="max-participants" className="block text-sm font-medium text-gray-700 mb-2">
            <UsersIcon className="inline h-4 w-4 mr-1" />
            Maximum Participants
          </label>
          <input
            type="number"
            id="max-participants"
            value={data.maxParticipants || ''}
            onChange={(e) => updateData({ maxParticipants: e.target.value ? parseInt(e.target.value) : undefined })}
            placeholder="Leave empty for unlimited"
            min="1"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
          <p className="mt-1 text-sm text-gray-500">
            Limit the number of participants (optional)
          </p>
        </div>

        {/* Minimum Age */}
        <div>
          <label htmlFor="min-age" className="block text-sm font-medium text-gray-700 mb-2">
            Minimum Age
          </label>
          <select
            id="min-age"
            value={data.minAge || ''}
            onChange={(e) => updateData({ minAge: e.target.value ? parseInt(e.target.value) : undefined })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">No age restriction</option>
            <option value="13">13+ years</option>
            <option value="16">16+ years</option>
            <option value="18">18+ years</option>
            <option value="21">21+ years</option>
          </select>
          <p className="mt-1 text-sm text-gray-500">
            Set age requirements for participation
          </p>
        </div>
      </div>

      {/* Geographic Restrictions */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <GlobeAltIcon className="inline h-4 w-4 mr-1" />
          Geographic Availability
        </label>
        
        <div className="space-y-3">
          {/* Global Option */}
          <label className="flex items-center space-x-3">
            <input
              type="radio"
              name="geo-restriction"
              checked={!data.geoRestrictions || data.geoRestrictions.length === 0}
              onChange={() => handleGeoRestrictionToggle('Global')}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span className="text-sm font-medium text-gray-700">Global (All regions)</span>
          </label>

          {/* Specific Regions */}
          <div>
            <label className="flex items-center space-x-3 mb-2">
              <input
                type="radio"
                name="geo-restriction"
                checked={data.geoRestrictions && data.geoRestrictions.length > 0}
                onChange={() => {
                  if (!data.geoRestrictions || data.geoRestrictions.length === 0) {
                    updateData({ geoRestrictions: ['United States'] })
                  }
                }}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <span className="text-sm font-medium text-gray-700">Specific regions only</span>
            </label>

            {data.geoRestrictions && data.geoRestrictions.length > 0 && (
              <div className="ml-7 grid grid-cols-2 md:grid-cols-3 gap-2">
                {regions.slice(1).map((region) => (
                  <label key={region} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={data.geoRestrictions?.includes(region) || false}
                      onChange={() => handleGeoRestrictionToggle(region)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">{region}</span>
                  </label>
                ))}
              </div>
            )}
          </div>
        </div>

        <p className="mt-2 text-sm text-gray-500">
          Choose where your campaign will be available. Geographic restrictions help comply with local regulations.
        </p>
      </div>

      {/* Campaign Schedule Preview */}
      {data.startDate && data.endDate && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">Campaign Schedule Preview</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-700">
            <div>
              <span className="font-medium">Start:</span> {new Date(data.startDate).toLocaleDateString()}
            </div>
            <div>
              <span className="font-medium">End:</span> {new Date(data.endDate).toLocaleDateString()}
            </div>
            <div>
              <span className="font-medium">Duration:</span> {calculateDuration()} days
            </div>
          </div>
          
          {data.maxParticipants && (
            <div className="mt-2 text-sm text-blue-700">
              <span className="font-medium">Max Participants:</span> {data.maxParticipants.toLocaleString()}
            </div>
          )}
          
          {data.geoRestrictions && data.geoRestrictions.length > 0 && (
            <div className="mt-2 text-sm text-blue-700">
              <span className="font-medium">Available in:</span> {data.geoRestrictions.join(', ')}
            </div>
          )}
        </div>
      )}

      {/* Help Text */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-yellow-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Timeline Planning Tips</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Longer campaigns (2-4 weeks) typically have higher completion rates</li>
                <li>Consider time zones when setting start/end dates for global campaigns</li>
                <li>Leave buffer time for campaign promotion before the start date</li>
                <li>Participant limits create urgency but may reduce total engagement</li>
                <li>Geographic restrictions help ensure legal compliance</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
