'use client'

import React, { useState } from 'react'
import {
  BellIcon,
  CheckI<PERSON>,
  TrashIcon,
  FunnelIcon,
  EyeIcon,
  EyeSlashIcon,
  UserPlusIcon,
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  ShareIcon,
  AtSymbolIcon,
  UserGroupIcon,
  TrophyIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline'
import {
  useSocialNotifications,
  useMarkNotificationAsRead,
  useMarkAllNotificationsAsRead,
  useDeleteNotification
} from '@/hooks/useSocial'
import { SocialNotification, SocialNotificationType } from '@/types/social.types'

interface SocialNotificationsProps {
  className?: string
}

export default function SocialNotifications({
  className = ''
}: SocialNotificationsProps) {
  const [filterType, setFilterType] = useState<SocialNotificationType | 'all'>('all')
  const [showUnreadOnly, setShowUnreadOnly] = useState(false)

  const { data: notificationsData, isLoading } = useSocialNotifications(50, 0)
  const markAsReadMutation = useMarkNotificationAsRead()
  const markAllAsReadMutation = useMarkAllNotificationsAsRead()
  const deleteNotificationMutation = useDeleteNotification()

  const notifications = notificationsData?.notifications || []
  const unreadCount = notificationsData?.unreadCount || 0

  const filteredNotifications = notifications.filter(notification => {
    const typeMatch = filterType === 'all' || notification.type === filterType
    const readMatch = !showUnreadOnly || !notification.isRead
    return typeMatch && readMatch
  })

  const notificationTypes = [
    { value: 'all', label: 'All Notifications' },
    { value: SocialNotificationType.NEW_FOLLOWER, label: 'New Followers' },
    { value: SocialNotificationType.POST_LIKE, label: 'Post Likes' },
    { value: SocialNotificationType.POST_COMMENT, label: 'Comments' },
    { value: SocialNotificationType.POST_SHARE, label: 'Shares' },
    { value: SocialNotificationType.MENTION, label: 'Mentions' },
    { value: SocialNotificationType.DIRECT_MESSAGE, label: 'Messages' },
    { value: SocialNotificationType.COMMUNITY_INVITE, label: 'Community Invites' },
    { value: SocialNotificationType.ACHIEVEMENT_EARNED, label: 'Achievements' }
  ]

  const handleMarkAsRead = (notificationId: string) => {
    markAsReadMutation.mutate(notificationId)
  }

  const handleMarkAllAsRead = () => {
    if (unreadCount > 0) {
      markAllAsReadMutation.mutate()
    }
  }

  const handleDelete = (notificationId: string) => {
    if (confirm('Are you sure you want to delete this notification?')) {
      deleteNotificationMutation.mutate(notificationId)
    }
  }

  const getNotificationIcon = (type: SocialNotificationType) => {
    switch (type) {
      case SocialNotificationType.NEW_FOLLOWER:
        return <UserPlusIcon className="h-5 w-5 text-blue-600" />
      case SocialNotificationType.POST_LIKE:
        return <HeartIcon className="h-5 w-5 text-red-600" />
      case SocialNotificationType.POST_COMMENT:
        return <ChatBubbleOvalLeftIcon className="h-5 w-5 text-green-600" />
      case SocialNotificationType.POST_SHARE:
        return <ShareIcon className="h-5 w-5 text-purple-600" />
      case SocialNotificationType.MENTION:
        return <AtSymbolIcon className="h-5 w-5 text-orange-600" />
      case SocialNotificationType.DIRECT_MESSAGE:
        return <ChatBubbleOvalLeftIcon className="h-5 w-5 text-blue-600" />
      case SocialNotificationType.COMMUNITY_INVITE:
        return <UserGroupIcon className="h-5 w-5 text-indigo-600" />
      case SocialNotificationType.ACHIEVEMENT_EARNED:
        return <TrophyIcon className="h-5 w-5 text-yellow-600" />
      case SocialNotificationType.BADGE_EARNED:
        return <ShieldCheckIcon className="h-5 w-5 text-green-600" />
      default:
        return <BellIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const formatNotificationTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg">
              <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <BellIcon className="h-6 w-6 mr-2" />
            Notifications
            {unreadCount > 0 && (
              <span className="ml-2 bg-red-100 text-red-800 py-1 px-2 rounded-full text-xs">
                {unreadCount} unread
              </span>
            )}
          </h2>
          <p className="text-sm text-gray-600">Stay updated with your social activity</p>
        </div>
        
        {unreadCount > 0 && (
          <button
            onClick={handleMarkAllAsRead}
            disabled={markAllAsReadMutation.isPending}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <CheckIcon className="h-4 w-4 mr-2" />
            Mark All Read
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {notificationTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>

          <label className="flex items-center text-sm text-gray-600">
            <input
              type="checkbox"
              checked={showUnreadOnly}
              onChange={(e) => setShowUnreadOnly(e.target.checked)}
              className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            Unread only
          </label>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            {filteredNotifications.length} notifications
          </span>
          <button className="text-gray-400 hover:text-gray-600">
            <FunnelIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Notifications List */}
      {filteredNotifications.length > 0 ? (
        <div className="space-y-2">
          {filteredNotifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onMarkAsRead={() => handleMarkAsRead(notification.id)}
              onDelete={() => handleDelete(notification.id)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
          <p className="mt-1 text-sm text-gray-500">
            {showUnreadOnly ? 'No unread notifications.' : 'You\'re all caught up!'}
          </p>
        </div>
      )}

      {/* Notification Settings */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Notification Settings</h3>
        
        <div className="space-y-3">
          <label className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Email notifications</span>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </label>
          
          <label className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Push notifications</span>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </label>
          
          <label className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Marketing notifications</span>
            <input
              type="checkbox"
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </label>
        </div>
        
        <button className="mt-4 text-sm text-blue-600 hover:text-blue-700">
          Manage all notification preferences
        </button>
      </div>
    </div>
  )
}

interface NotificationItemProps {
  notification: SocialNotification
  onMarkAsRead: () => void
  onDelete: () => void
}

function NotificationItem({ notification, onMarkAsRead, onDelete }: NotificationItemProps) {
  const getNotificationIcon = (type: SocialNotificationType) => {
    switch (type) {
      case SocialNotificationType.NEW_FOLLOWER:
        return <UserPlusIcon className="h-5 w-5 text-blue-600" />
      case SocialNotificationType.POST_LIKE:
        return <HeartIcon className="h-5 w-5 text-red-600" />
      case SocialNotificationType.POST_COMMENT:
        return <ChatBubbleOvalLeftIcon className="h-5 w-5 text-green-600" />
      case SocialNotificationType.POST_SHARE:
        return <ShareIcon className="h-5 w-5 text-purple-600" />
      case SocialNotificationType.MENTION:
        return <AtSymbolIcon className="h-5 w-5 text-orange-600" />
      case SocialNotificationType.DIRECT_MESSAGE:
        return <ChatBubbleOvalLeftIcon className="h-5 w-5 text-blue-600" />
      case SocialNotificationType.COMMUNITY_INVITE:
        return <UserGroupIcon className="h-5 w-5 text-indigo-600" />
      case SocialNotificationType.ACHIEVEMENT_EARNED:
        return <TrophyIcon className="h-5 w-5 text-yellow-600" />
      case SocialNotificationType.BADGE_EARNED:
        return <ShieldCheckIcon className="h-5 w-5 text-green-600" />
      default:
        return <BellIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const formatNotificationTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  return (
    <div className={`flex items-start space-x-3 p-4 border rounded-lg transition-colors ${
      notification.isRead
        ? 'border-gray-200 bg-white'
        : 'border-blue-200 bg-blue-50'
    }`}>
      {/* Icon */}
      <div className="flex-shrink-0">
        {getNotificationIcon(notification.type)}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className={`text-sm font-medium ${
              notification.isRead ? 'text-gray-900' : 'text-gray-900'
            }`}>
              {notification.title}
            </h4>
            <p className={`text-sm mt-1 ${
              notification.isRead ? 'text-gray-600' : 'text-gray-700'
            }`}>
              {notification.message}
            </p>
            <div className="flex items-center mt-2 space-x-4">
              <span className="text-xs text-gray-500">
                {formatNotificationTime(notification.createdAt)}
              </span>
              {notification.actionUrl && (
                <a
                  href={notification.actionUrl}
                  className="text-xs text-blue-600 hover:text-blue-700"
                >
                  {notification.actionText || 'View'}
                </a>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2 ml-4">
            {!notification.isRead && (
              <button
                onClick={onMarkAsRead}
                className="text-blue-600 hover:text-blue-700"
                title="Mark as read"
              >
                <EyeIcon className="h-4 w-4" />
              </button>
            )}
            
            <button
              onClick={onDelete}
              className="text-red-600 hover:text-red-700"
              title="Delete notification"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Unread indicator */}
      {!notification.isRead && (
        <div className="flex-shrink-0">
          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
        </div>
      )}
    </div>
  )
}
