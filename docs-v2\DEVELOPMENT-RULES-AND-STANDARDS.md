# 🛠️ **DEVELOPMENT RULES AND STANDARDS**

## **📋 COMPREHENSIVE DEVELOPMENT STANDARDS FOR SOCIAL NFT PLATFORM**

**Purpose**: Establish mandatory development rules and standards  
**Scope**: All developers, AI agents, and platform development  
**Authority**: Single source of truth for development practices

---

## 🏗️ **ARCHITECTURAL STANDARDS**

### **Microservices Architecture Principles**
- **Service Independence**: Each service is completely independent
- **Database Per Service**: Each service has its own PostgreSQL database
- **API Gateway Pattern**: All external communication through API Gateway (port 3010)
- **Shared Infrastructure**: Common patterns for cross-cutting concerns
- **Event-Driven Communication**: Asynchronous communication where appropriate

### **Service Communication Rules**
```typescript
// ✅ CORRECT: Communication through API Gateway
const response = await fetch('http://localhost:3010/api/users/profile');

// ❌ INCORRECT: Direct service-to-service communication
const response = await fetch('http://localhost:3001/api/profile');
```

### **Database Architecture Standards**
```sql
-- Standard database naming convention
user_service              -- User management
profile_analysis_service  -- Profile analysis and AI
nft_generation_service   -- NFT generation and processing
blockchain_service       -- Blockchain interactions
marketplace_service      -- NFT marketplace
project_service         -- Project management
analytics_service       -- Analytics and reporting
notification_service    -- Notifications
```

---

## 🔧 **TECHNOLOGY STACK STANDARDS**

### **Backend Technology Requirements**
- **Framework**: NestJS with TypeScript (strict mode)
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT-based authentication
- **Validation**: class-validator and class-transformer
- **Documentation**: Swagger/OpenAPI integration
- **Testing**: Jest for unit and integration tests

### **Frontend Technology Requirements**
- **Framework**: Next.js with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **State Management**: React Query for server state
- **Authentication**: JWT with refresh token rotation
- **API Communication**: Axios with interceptors

### **Development Tools Requirements**
- **Code Quality**: ESLint + Prettier configuration
- **Git Hooks**: Husky for pre-commit validation
- **Package Management**: npm (consistent across all services)
- **Environment Management**: dotenv with validation

---

## 📁 **PROJECT STRUCTURE STANDARDS**

### **Service Directory Structure**
```
services/{service-name}/
├── src/
│   ├── auth/              # Authentication guards and decorators
│   ├── config/            # Configuration management
│   ├── health/            # Health check endpoints
│   ├── prisma/            # Database service and schema
│   ├── shared/            # Shared utilities and interfaces
│   ├── {domain}/          # Business logic modules
│   ├── app.module.ts      # Main application module
│   └── main.ts            # Application bootstrap
├── prisma/
│   ├── schema.prisma      # Database schema
│   └── migrations/        # Database migrations
├── test/                  # Test files
├── .env                   # Environment variables
├── .env.example           # Environment template
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
└── README.md              # Service documentation
```

### **Frontend Directory Structure**
```
frontend-headless/
├── src/
│   ├── app/               # Next.js app directory
│   ├── components/        # Reusable UI components
│   ├── lib/               # Utility functions and API
│   ├── hooks/             # Custom React hooks
│   ├── types/             # TypeScript type definitions
│   └── styles/            # Global styles and themes
├── public/                # Static assets
├── .env.local             # Local environment variables
├── next.config.js         # Next.js configuration
├── tailwind.config.js     # Tailwind CSS configuration
└── package.json           # Dependencies and scripts
```

---

## 🔐 **SECURITY STANDARDS**

### **Authentication and Authorization**
```typescript
// Standard JWT guard implementation
@UseGuards(JwtAuthGuard)
@Controller('users')
export class UsersController {
  @Get('profile')
  @ApiOperation({ summary: 'Get user profile' })
  async getProfile(@Request() req) {
    return this.usersService.findById(req.user.id);
  }
}
```

### **Input Validation Standards**
```typescript
// Mandatory input validation
export class CreateUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @MinLength(8)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
  password: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;
}
```

### **Environment Variable Security**
```bash
# Security configuration standards
JWT_SECRET=your-super-secret-jwt-key-change-in-production
DATABASE_URL=postgresql://postgres:1111@localhost:5432/service_name?schema=public
GATEWAY_SECRET=your-super-secret-gateway-key-change-in-production

# Never commit actual secrets to version control
# Use .env.example for templates
```

---

## 🏥 **HEALTH CHECK STANDARDS**

### **Mandatory Health Endpoints**
```typescript
// Standard health check implementation
@Controller('health')
export class HealthController {
  @Get('simple')
  simpleCheck() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      service: 'service-name',
      version: '1.0.0',
    };
  }

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.prismaHealth.isHealthy('database'),
    ]);
  }
}
```

### **Database Health Monitoring**
```typescript
// Standard database health check
@Injectable()
export class PrismaHealthIndicator extends HealthIndicator {
  constructor(private readonly prisma: PrismaService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return this.getStatus(key, true);
    } catch (error) {
      return this.getStatus(key, false, { error: error.message });
    }
  }
}
```

---

## 📊 **ERROR HANDLING STANDARDS**

### **Standardized Error Responses**
```typescript
// Standard error response format
export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    path: string;
  };
}

// Standard error handling
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    const status = exception.getStatus();

    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        code: exception.constructor.name,
        message: exception.message,
        timestamp: new Date().toISOString(),
        path: request.url,
      },
    };

    response.status(status).json(errorResponse);
  }
}
```

---

## 📝 **LOGGING STANDARDS**

### **Structured Logging Implementation**
```typescript
// Standard logging service
@Injectable()
export class LoggerService {
  private readonly logger = new Logger(LoggerService.name);

  log(message: string, context?: any) {
    this.logger.log(JSON.stringify({
      message,
      context,
      timestamp: new Date().toISOString(),
      service: process.env.SERVICE_NAME,
    }));
  }

  error(message: string, error?: any, context?: any) {
    this.logger.error(JSON.stringify({
      message,
      error: error?.message || error,
      stack: error?.stack,
      context,
      timestamp: new Date().toISOString(),
      service: process.env.SERVICE_NAME,
    }));
  }
}
```

---

## 🧪 **TESTING STANDARDS**

### **Unit Testing Requirements**
```typescript
// Standard test structure
describe('UsersService', () => {
  let service: UsersService;
  let prisma: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  it('should create a user', async () => {
    const userData = { email: '<EMAIL>', name: 'Test User' };
    const expectedUser = { id: 1, ...userData };

    jest.spyOn(prisma.user, 'create').mockResolvedValue(expectedUser);

    const result = await service.create(userData);

    expect(result).toEqual(expectedUser);
    expect(prisma.user.create).toHaveBeenCalledWith({ data: userData });
  });
});
```

### **Integration Testing Standards**
- **Database Testing**: Use test database with proper cleanup
- **API Testing**: Test all endpoints with various scenarios
- **Error Testing**: Test error conditions and edge cases
- **Performance Testing**: Ensure response times meet standards

---

## 📦 **PACKAGE MANAGEMENT STANDARDS**

### **Dependency Management Rules**
```json
// Standard package.json structure
{
  "name": "@social-nft-platform/service-name",
  "version": "1.0.0",
  "scripts": {
    "build": "nest build",
    "start": "node dist/main",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:e2e": "jest --config ./test/jest-e2e.json"
  }
}
```

### **Version Management**
- **Semantic Versioning**: Follow semver for all packages
- **Lock Files**: Always commit package-lock.json
- **Security Updates**: Regular dependency security audits
- **Compatibility**: Ensure compatibility across services

---

---

## 🔄 **API GATEWAY STANDARDS**

### **Current API Gateway Issues (Based on Analysis)**
Our current API Gateway has several critical issues that need addressing:

#### **❌ Critical Issues Found**
1. **Hardcoded Service Discovery**: Static service mapping without dynamic discovery
2. **No Circuit Breaker**: Missing enterprise-grade circuit breaker with bulkhead pattern
3. **Basic Rate Limiting**: Global rate limiting only, no per-service/per-user limits
4. **Simple Error Handling**: Basic error forwarding without categorization or retry logic
5. **No Load Balancing**: Single service instance assumption
6. **No Request Caching**: All requests hit services directly
7. **Basic Security**: Missing API key management and advanced security features

#### **✅ Required API Gateway Features**
```typescript
// Enterprise API Gateway Requirements
interface APIGatewayFeatures {
  serviceDiscovery: {
    dynamic: true;
    healthBased: true;
    loadBalancing: ['round-robin', 'least-connections', 'weighted'];
  };
  circuitBreaker: {
    bulkheadPattern: true;
    adaptiveThresholds: true;
    cascadingFailurePrevention: true;
  };
  rateLimiting: {
    perService: true;
    perUser: true;
    algorithms: ['token-bucket', 'sliding-window'];
  };
  caching: {
    intelligent: true;
    multiLevel: true;
    invalidation: 'event-driven';
  };
  security: {
    apiKeyManagement: true;
    requestSigning: true;
    payloadEncryption: true;
  };
}
```

---

## 🚀 **PERFORMANCE STANDARDS**

### **Response Time Requirements**
- **API Gateway**: < 50ms overhead
- **Service Endpoints**: < 500ms for 95% of requests
- **Database Queries**: < 100ms for simple queries
- **Complex Operations**: < 2s for complex business logic

### **Scalability Requirements**
- **Horizontal Scaling**: All services must support horizontal scaling
- **Database Connections**: Proper connection pooling (max 10 connections per service)
- **Memory Usage**: < 512MB per service instance
- **CPU Usage**: < 70% under normal load

### **Reliability Requirements**
- **Uptime**: 99.9% availability target
- **Error Rate**: < 1% error rate
- **Recovery Time**: < 30s for service recovery
- **Data Consistency**: ACID compliance for critical operations

---

## 📋 **CODE REVIEW STANDARDS**

### **Mandatory Review Checklist**
- ✅ **Architecture Compliance**: Follows microservices patterns
- ✅ **Security Review**: No security vulnerabilities
- ✅ **Performance Review**: Meets performance standards
- ✅ **Test Coverage**: Adequate test coverage (>80%)
- ✅ **Documentation**: Proper documentation and comments
- ✅ **Error Handling**: Comprehensive error handling
- ✅ **Code Quality**: Clean, readable, maintainable code

### **Review Process**
1. **Automated Checks**: ESLint, Prettier, TypeScript compilation
2. **Security Scan**: Dependency vulnerability scan
3. **Test Execution**: All tests must pass
4. **Manual Review**: Code review by senior developer
5. **Integration Testing**: End-to-end testing in staging

---

## 🔧 **DEPLOYMENT STANDARDS**

### **Environment Configuration**
```bash
# Development Environment
NODE_ENV=development
LOG_LEVEL=debug
DATABASE_URL=postgresql://postgres:1111@localhost:5432/service_dev

# Staging Environment
NODE_ENV=staging
LOG_LEVEL=info
DATABASE_URL=**********************************************/service_staging

# Production Environment
NODE_ENV=production
LOG_LEVEL=error
DATABASE_URL=**************************************************/service_prod
```

### **Deployment Process**
1. **Build Validation**: Successful build and test execution
2. **Security Scan**: No critical vulnerabilities
3. **Staging Deployment**: Deploy to staging environment
4. **Integration Testing**: Full end-to-end testing
5. **Production Deployment**: Blue-green deployment strategy
6. **Health Monitoring**: Post-deployment health verification

---

**🎯 Following these comprehensive development rules ensures consistent, maintainable, and scalable platform development!**
