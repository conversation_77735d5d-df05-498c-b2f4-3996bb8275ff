import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { marketplaceIntegrationService } from '@/services/marketplaceIntegrationService'
import {
  CampaignMarketplaceListing,
  NFTCollectionListing,
  MarketplaceFilters,
  BulkOperation,
  BulkOperationRequest,
  MarketplaceDiscovery,
  CampaignIntegrationSettings,
  MarketplaceTransaction,
  CampaignMarketplaceAnalytics,
  CreateMarketplaceListingRequest,
  UpdateMarketplaceListingRequest,
  MarketplaceSearchRequest,
  MarketplaceStatus
} from '@/types/marketplace-integration.types'

// ===== CAMPAIGN MARKETPLACE LISTINGS HOOKS =====

export function useCampaignListings(filters?: MarketplaceFilters) {
  return useQuery({
    queryKey: ['campaign-listings', filters],
    queryFn: () => marketplaceIntegrationService.getCampaignListings(filters),
    staleTime: 300000, // 5 minutes
    refetchInterval: 300000, // 5 minutes for marketplace updates
  })
}

export function useCampaignListing(id: string) {
  return useQuery({
    queryKey: ['campaign-listing', id],
    queryFn: () => marketplaceIntegrationService.getCampaignListing(id),
    enabled: !!id,
    staleTime: 300000,
    refetchInterval: 300000,
  })
}

export function useCreateCampaignListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateMarketplaceListingRequest) => 
      marketplaceIntegrationService.createCampaignListing(data),
    onSuccess: (newListing) => {
      queryClient.invalidateQueries({ queryKey: ['campaign-listings'] })
      queryClient.invalidateQueries({ queryKey: ['marketplace-discovery'] })
      queryClient.setQueryData(['campaign-listing', newListing.id], newListing)
    },
    onError: (error: any) => {
      console.error('Failed to create campaign listing:', error)
    },
  })
}

export function useUpdateCampaignListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateMarketplaceListingRequest }) =>
      marketplaceIntegrationService.updateCampaignListing(id, data),
    onSuccess: (updatedListing, { id }) => {
      queryClient.setQueryData(['campaign-listing', id], updatedListing)
      queryClient.invalidateQueries({ queryKey: ['campaign-listings'] })
    },
    onError: (error: any) => {
      console.error('Failed to update campaign listing:', error)
    },
  })
}

export function useDeleteCampaignListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => marketplaceIntegrationService.deleteCampaignListing(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: ['campaign-listing', id] })
      queryClient.invalidateQueries({ queryKey: ['campaign-listings'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete campaign listing:', error)
    },
  })
}

// ===== LISTING MANAGEMENT HOOKS =====

export function usePublishListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => marketplaceIntegrationService.publishListing(id),
    onSuccess: (updatedListing, id) => {
      queryClient.setQueryData(['campaign-listing', id], updatedListing)
      queryClient.invalidateQueries({ queryKey: ['campaign-listings'] })
      queryClient.invalidateQueries({ queryKey: ['marketplace-discovery'] })
    },
    onError: (error: any) => {
      console.error('Failed to publish listing:', error)
    },
  })
}

export function useUnpublishListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => marketplaceIntegrationService.unpublishListing(id),
    onSuccess: (updatedListing, id) => {
      queryClient.setQueryData(['campaign-listing', id], updatedListing)
      queryClient.invalidateQueries({ queryKey: ['campaign-listings'] })
    },
    onError: (error: any) => {
      console.error('Failed to unpublish listing:', error)
    },
  })
}

export function useFeatureListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, duration }: { id: string; duration: number }) =>
      marketplaceIntegrationService.featureListing(id, duration),
    onSuccess: (updatedListing, { id }) => {
      queryClient.setQueryData(['campaign-listing', id], updatedListing)
      queryClient.invalidateQueries({ queryKey: ['campaign-listings'] })
      queryClient.invalidateQueries({ queryKey: ['featured-listings'] })
    },
    onError: (error: any) => {
      console.error('Failed to feature listing:', error)
    },
  })
}

export function useUpdateListingStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: MarketplaceStatus }) =>
      marketplaceIntegrationService.updateListingStatus(id, status),
    onSuccess: (updatedListing, { id }) => {
      queryClient.setQueryData(['campaign-listing', id], updatedListing)
      queryClient.invalidateQueries({ queryKey: ['campaign-listings'] })
    },
    onError: (error: any) => {
      console.error('Failed to update listing status:', error)
    },
  })
}

// ===== NFT COLLECTIONS HOOKS =====

export function useCollectionListings(campaignId?: string) {
  return useQuery({
    queryKey: ['collection-listings', campaignId],
    queryFn: () => marketplaceIntegrationService.getCollectionListings(campaignId),
    staleTime: 300000,
  })
}

export function useCollectionListing(id: string) {
  return useQuery({
    queryKey: ['collection-listing', id],
    queryFn: () => marketplaceIntegrationService.getCollectionListing(id),
    enabled: !!id,
    staleTime: 300000,
  })
}

export function useCreateCollectionListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: {
      campaignId: string
      collectionName: string
      description: string
      basePrice: number
      royaltyPercentage: number
    }) => marketplaceIntegrationService.createCollectionListing(data),
    onSuccess: (newCollection) => {
      queryClient.invalidateQueries({ queryKey: ['collection-listings'] })
      queryClient.setQueryData(['collection-listing', newCollection.id], newCollection)
    },
    onError: (error: any) => {
      console.error('Failed to create collection listing:', error)
    },
  })
}

// ===== MARKETPLACE SEARCH & DISCOVERY HOOKS =====

export function useMarketplaceSearch(request: MarketplaceSearchRequest) {
  return useQuery({
    queryKey: ['marketplace-search', request],
    queryFn: () => marketplaceIntegrationService.searchMarketplace(request),
    enabled: !!(request.query || request.filters),
    staleTime: 300000,
  })
}

export function useMarketplaceDiscovery() {
  return useQuery({
    queryKey: ['marketplace-discovery'],
    queryFn: () => marketplaceIntegrationService.getMarketplaceDiscovery(),
    staleTime: 600000, // 10 minutes
    refetchInterval: 600000,
  })
}

export function useFeaturedListings() {
  return useQuery({
    queryKey: ['featured-listings'],
    queryFn: () => marketplaceIntegrationService.getFeaturedListings(),
    staleTime: 300000,
    refetchInterval: 300000,
  })
}

export function useTrendingCollections() {
  return useQuery({
    queryKey: ['trending-collections'],
    queryFn: () => marketplaceIntegrationService.getTrendingCollections(),
    staleTime: 300000,
    refetchInterval: 300000,
  })
}

export function useRecommendations(userId?: string) {
  return useQuery({
    queryKey: ['marketplace-recommendations', userId],
    queryFn: () => marketplaceIntegrationService.getRecommendations(userId),
    staleTime: 600000,
  })
}

// ===== BULK OPERATIONS HOOKS =====

export function useBulkOperations() {
  return useQuery({
    queryKey: ['bulk-operations'],
    queryFn: () => marketplaceIntegrationService.getBulkOperations(),
    staleTime: 60000, // 1 minute
    refetchInterval: 60000,
  })
}

export function useBulkOperation(id: string) {
  return useQuery({
    queryKey: ['bulk-operation', id],
    queryFn: () => marketplaceIntegrationService.getBulkOperation(id),
    enabled: !!id,
    staleTime: 30000,
    refetchInterval: 30000, // Frequent updates for operation status
  })
}

export function useCreateBulkOperation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: BulkOperationRequest) =>
      marketplaceIntegrationService.createBulkOperation(request),
    onSuccess: (newOperation) => {
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
      queryClient.setQueryData(['bulk-operation', newOperation.id], newOperation)
    },
    onError: (error: any) => {
      console.error('Failed to create bulk operation:', error)
    },
  })
}

export function useExecuteBulkOperation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => marketplaceIntegrationService.executeBulkOperation(id),
    onSuccess: (updatedOperation, id) => {
      queryClient.setQueryData(['bulk-operation', id], updatedOperation)
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
    },
    onError: (error: any) => {
      console.error('Failed to execute bulk operation:', error)
    },
  })
}

export function useCancelBulkOperation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => marketplaceIntegrationService.cancelBulkOperation(id),
    onSuccess: (updatedOperation, id) => {
      queryClient.setQueryData(['bulk-operation', id], updatedOperation)
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
    },
    onError: (error: any) => {
      console.error('Failed to cancel bulk operation:', error)
    },
  })
}

export function useDeleteBulkOperation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => marketplaceIntegrationService.deleteBulkOperation(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: ['bulk-operation', id] })
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete bulk operation:', error)
    },
  })
}

// ===== INTEGRATION SETTINGS HOOKS =====

export function useCampaignIntegrationSettings(campaignId: string) {
  return useQuery({
    queryKey: ['campaign-integration-settings', campaignId],
    queryFn: () => marketplaceIntegrationService.getCampaignIntegrationSettings(campaignId),
    enabled: !!campaignId,
    staleTime: 600000,
  })
}

export function useUpdateCampaignIntegrationSettings() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ campaignId, settings }: { 
      campaignId: string; 
      settings: Partial<CampaignIntegrationSettings> 
    }) => marketplaceIntegrationService.updateCampaignIntegrationSettings(campaignId, settings),
    onSuccess: (updatedSettings, { campaignId }) => {
      queryClient.setQueryData(['campaign-integration-settings', campaignId], updatedSettings)
    },
    onError: (error: any) => {
      console.error('Failed to update integration settings:', error)
    },
  })
}

export function useToggleAutoListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ campaignId, enable }: { campaignId: string; enable: boolean }) =>
      enable 
        ? marketplaceIntegrationService.enableAutoListing(campaignId)
        : marketplaceIntegrationService.disableAutoListing(campaignId),
    onSuccess: (updatedSettings, { campaignId }) => {
      queryClient.setQueryData(['campaign-integration-settings', campaignId], updatedSettings)
    },
    onError: (error: any) => {
      console.error('Failed to toggle auto listing:', error)
    },
  })
}

// ===== TRANSACTIONS HOOKS =====

export function useMarketplaceTransactions(filters?: {
  campaignId?: string
  itemType?: string
  status?: string
  startDate?: string
  endDate?: string
  page?: number
  limit?: number
}) {
  return useQuery({
    queryKey: ['marketplace-transactions', filters],
    queryFn: () => marketplaceIntegrationService.getMarketplaceTransactions(filters),
    staleTime: 300000,
  })
}

export function useTransaction(id: string) {
  return useQuery({
    queryKey: ['marketplace-transaction', id],
    queryFn: () => marketplaceIntegrationService.getTransaction(id),
    enabled: !!id,
    staleTime: 300000,
  })
}

// ===== ANALYTICS HOOKS =====

export function useCampaignMarketplaceAnalytics(campaignId: string, timeframe: string = '30d') {
  return useQuery({
    queryKey: ['campaign-marketplace-analytics', campaignId, timeframe],
    queryFn: () => marketplaceIntegrationService.getCampaignMarketplaceAnalytics(campaignId, timeframe),
    enabled: !!campaignId,
    staleTime: 300000,
  })
}

export function useMarketplaceOverview() {
  return useQuery({
    queryKey: ['marketplace-overview'],
    queryFn: () => marketplaceIntegrationService.getMarketplaceOverview(),
    staleTime: 300000,
    refetchInterval: 300000,
  })
}

// ===== FAVORITES HOOKS =====

export function useAddToFavorites() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (listingId: string) => marketplaceIntegrationService.addToFavorites(listingId),
    onSuccess: (_, listingId) => {
      queryClient.invalidateQueries({ queryKey: ['favorites'] })
      // Update the listing to reflect favorite status
      queryClient.invalidateQueries({ queryKey: ['campaign-listing', listingId] })
    },
    onError: (error: any) => {
      console.error('Failed to add to favorites:', error)
    },
  })
}

export function useRemoveFromFavorites() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (listingId: string) => marketplaceIntegrationService.removeFromFavorites(listingId),
    onSuccess: (_, listingId) => {
      queryClient.invalidateQueries({ queryKey: ['favorites'] })
      queryClient.invalidateQueries({ queryKey: ['campaign-listing', listingId] })
    },
    onError: (error: any) => {
      console.error('Failed to remove from favorites:', error)
    },
  })
}

export function useFavorites() {
  return useQuery({
    queryKey: ['favorites'],
    queryFn: () => marketplaceIntegrationService.getFavorites(),
    staleTime: 300000,
  })
}

// ===== SOCIAL FEATURES HOOKS =====

export function useShareListing() {
  return useMutation({
    mutationFn: ({ listingId, platform }: { listingId: string; platform: string }) =>
      marketplaceIntegrationService.shareListing(listingId, platform),
    onError: (error: any) => {
      console.error('Failed to share listing:', error)
    },
  })
}

export function useReportListing() {
  return useMutation({
    mutationFn: ({ listingId, reason, details }: { 
      listingId: string; 
      reason: string; 
      details?: string 
    }) => marketplaceIntegrationService.reportListing(listingId, reason, details),
    onError: (error: any) => {
      console.error('Failed to report listing:', error)
    },
  })
}

// ===== UTILITY HOOKS =====

export function useValidateListingData() {
  return useMutation({
    mutationFn: (data: CreateMarketplaceListingRequest) =>
      marketplaceIntegrationService.validateListingData(data),
    onError: (error: any) => {
      console.error('Failed to validate listing data:', error)
    },
  })
}

export function useEstimateListingFees() {
  return useMutation({
    mutationFn: (data: {
      basePrice: number
      royaltyPercentage: number
      listingType: string
      duration?: number
    }) => marketplaceIntegrationService.estimateListingFees(data),
    onError: (error: any) => {
      console.error('Failed to estimate listing fees:', error)
    },
  })
}

export function useMarketplaceTrends() {
  return useQuery({
    queryKey: ['marketplace-trends'],
    queryFn: () => marketplaceIntegrationService.getMarketplaceTrends(),
    staleTime: 3600000, // 1 hour
  })
}

export function useExportMarketplaceData() {
  return useMutation({
    mutationFn: ({ filters, format }: { 
      filters?: MarketplaceFilters; 
      format?: 'csv' | 'json' 
    }) => marketplaceIntegrationService.exportMarketplaceData(filters, format),
    onError: (error: any) => {
      console.error('Failed to export marketplace data:', error)
    },
  })
}
