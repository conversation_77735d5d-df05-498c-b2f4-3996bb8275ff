// Enterprise Analytics Query Controller (Read Side) - Enhanced
import { <PERSON>, <PERSON>, <PERSON>m, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus, Lo<PERSON>, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';

// Enhanced analytics services
import { EventTrackingService } from '../../analytics/services/event-tracking.service';
import { UserBehaviorService } from '../../analytics/services/user-behavior.service';
import { PerformanceAnalyticsService } from '../../analytics/services/performance-analytics.service';

@ApiTags('Analytics Queries (Read Operations)')
@Controller('enterprise/analytics')
export class AnalyticsQueryController {
  private readonly logger = new Logger(AnalyticsQueryController.name);

  constructor(
    private readonly eventTracking: EventTrackingService,
    private readonly userBehavior: UserBehaviorService,
    private readonly performanceAnalytics: PerformanceAnalyticsService
  ) {}

  @Get('events/metrics')
  @ApiOperation({ summary: 'Get event metrics overview (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Event metrics retrieved successfully' })
  async getEventMetrics(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Event metrics query received`, { correlationId });

      const metrics = this.eventTracking.getEventMetrics();

      return res.status(HttpStatus.OK).json({
        success: true,
        data: metrics,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get event metrics: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('events/type/:eventType')
  @ApiOperation({ summary: 'Get events by type (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Events retrieved successfully' })
  async getEventsByType(
    @Param('eventType') eventType: string,
    @Query('limit') limit: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Events by type query received`, { correlationId, eventType });

      const events = this.eventTracking.getEventsByType(eventType, parseInt(limit) || 100);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          eventType,
          events,
          count: events.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get events by type: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('users/:userId/behavior')
  @ApiOperation({ summary: 'Get user behavior analysis (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'User behavior retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserBehavior(
    @Param('userId') userId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 User behavior query received`, { correlationId, userId });

      const behaviorProfile = await this.userBehavior.analyzeUserBehavior(userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: behaviorProfile,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get user behavior: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('users/:userId/events')
  @ApiOperation({ summary: 'Get events by user (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'User events retrieved successfully' })
  async getUserEvents(
    @Param('userId') userId: string,
    @Query('limit') limit: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 User events query received`, { correlationId, userId });

      const events = this.eventTracking.getEventsByUser(userId, parseInt(limit) || 100);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          userId,
          events,
          count: events.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get user events: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('users/:userId/trends')
  @ApiOperation({ summary: 'Get user behavior trends (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'User trends retrieved successfully' })
  async getUserTrends(
    @Param('userId') userId: string,
    @Query('days') days: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📈 User trends query received`, { correlationId, userId });

      const trends = this.userBehavior.getUserBehaviorTrends(userId, parseInt(days) || 30);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          userId,
          trends,
          period: `${parseInt(days) || 30} days`
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get user trends: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('behavior/insights')
  @ApiOperation({ summary: 'Get behavior insights overview (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Behavior insights retrieved successfully' })
  async getBehaviorInsights(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🧠 Behavior insights query received`, { correlationId });

      const insights = await this.userBehavior.getBehaviorInsights();

      return res.status(HttpStatus.OK).json({
        success: true,
        data: insights,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get behavior insights: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('performance/system')
  @ApiOperation({ summary: 'Get system performance metrics (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'System performance retrieved successfully' })
  async getSystemPerformance(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`⚡ System performance query received`, { correlationId });

      const performance = await this.performanceAnalytics.getSystemPerformance();

      return res.status(HttpStatus.OK).json({
        success: true,
        data: performance,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get system performance: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('performance/services')
  @ApiOperation({ summary: 'Get service performance metrics (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Service performance retrieved successfully' })
  async getServicePerformance(
    @Query('service') serviceName: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`⚡ Service performance query received`, { correlationId, serviceName });

      const services = await this.performanceAnalytics.getServiceMetrics(serviceName);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          services,
          count: services.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get service performance: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('performance/report')
  @ApiOperation({ summary: 'Get comprehensive performance report (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Performance report retrieved successfully' })
  async getPerformanceReport(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Performance report query received`, { correlationId });

      const report = await this.performanceAnalytics.generatePerformanceReport();

      return res.status(HttpStatus.OK).json({
        success: true,
        data: report,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get performance report: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('performance/trends')
  @ApiOperation({ summary: 'Get performance trends (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Performance trends retrieved successfully' })
  async getPerformanceTrends(
    @Query('hours') hours: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📈 Performance trends query received`, { correlationId });

      const trends = this.performanceAnalytics.getPerformanceTrends(parseInt(hours) || 24);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          trends,
          period: `${parseInt(hours) || 24} hours`
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get performance trends: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('dashboard')
  @ApiOperation({ summary: 'Get analytics dashboard data (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved successfully' })
  async getAnalyticsDashboard(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Analytics dashboard query received`, { correlationId });

      // Get comprehensive dashboard data
      const [eventMetrics, behaviorInsights, performanceReport] = await Promise.all([
        this.eventTracking.getEventMetrics(),
        this.userBehavior.getBehaviorInsights(),
        this.performanceAnalytics.generatePerformanceReport()
      ]);

      const dashboard = {
        overview: {
          totalEvents: eventMetrics.totalEvents,
          eventsPerSecond: eventMetrics.eventsPerSecond,
          uniqueUsers: eventMetrics.uniqueUsers,
          activeUsers: behaviorInsights.activeUsers,
          systemHealth: performanceReport.alerts.filter(a => a.level === 'critical').length === 0 ? 'healthy' : 'warning'
        },
        events: {
          topEvents: eventMetrics.topEvents,
          recentEvents: eventMetrics.recentEvents.slice(-10)
        },
        users: {
          totalUsers: behaviorInsights.totalUsers,
          newUsers: behaviorInsights.newUsers,
          userSegments: behaviorInsights.userSegments,
          topActions: behaviorInsights.topUserActions.slice(0, 5)
        },
        performance: {
          systemPerformance: performanceReport.system,
          alerts: performanceReport.alerts,
          recommendations: performanceReport.recommendations
        },
        timestamp: new Date()
      };

      return res.status(HttpStatus.OK).json({
        success: true,
        data: dashboard,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get analytics dashboard: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }
}
