'use client'

import React, { useState } from 'react'
import {
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  CubeIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  MinusIcon
} from '@heroicons/react/24/outline'
import {
  usePredictions,
  usePricePrediction,
  useMarketTrendPrediction,
  useUserBehaviorPrediction
} from '@/hooks/useAI'
import {
  PredictiveAnalytics as PredictiveAnalyticsType,
  PredictionType,
  FactorImpact
} from '@/types/ai.types'

interface PredictiveAnalyticsProps {
  userId: string
  className?: string
}

export default function PredictiveAnalytics({
  userId,
  className = ''
}: PredictiveAnalyticsProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState('7d')
  const [selectedType, setSelectedType] = useState<PredictionType | 'all'>('all')

  const { data: predictions, isLoading } = usePredictions({
    types: selectedType === 'all' ? undefined : [selectedType],
    timeframe: selectedTimeframe,
    limit: 20
  })

  const { data: marketTrend } = useMarketTrendPrediction(selectedTimeframe)
  const { data: userBehavior } = useUserBehaviorPrediction(userId)

  const timeframes = [
    { value: '1d', label: '24 Hours' },
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' }
  ]

  const predictionTypes = [
    { value: 'all', label: 'All Predictions' },
    { value: PredictionType.PRICE_MOVEMENT, label: 'Price Movement' },
    { value: PredictionType.MARKET_TREND, label: 'Market Trends' },
    { value: PredictionType.USER_BEHAVIOR, label: 'User Behavior' },
    { value: PredictionType.ENGAGEMENT_RATE, label: 'Engagement' },
    { value: PredictionType.PORTFOLIO_PERFORMANCE, label: 'Portfolio' },
    { value: PredictionType.COMMUNITY_GROWTH, label: 'Community Growth' },
    { value: PredictionType.NFT_POPULARITY, label: 'NFT Popularity' }
  ]

  const getPredictionIcon = (type: PredictionType) => {
    switch (type) {
      case PredictionType.PRICE_MOVEMENT:
        return <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
      case PredictionType.MARKET_TREND:
        return <TrendingUpIcon className="h-5 w-5 text-blue-600" />
      case PredictionType.USER_BEHAVIOR:
        return <UserGroupIcon className="h-5 w-5 text-purple-600" />
      case PredictionType.ENGAGEMENT_RATE:
        return <ChartBarIcon className="h-5 w-5 text-orange-600" />
      case PredictionType.PORTFOLIO_PERFORMANCE:
        return <TrendingUpIcon className="h-5 w-5 text-indigo-600" />
      case PredictionType.COMMUNITY_GROWTH:
        return <UserGroupIcon className="h-5 w-5 text-teal-600" />
      case PredictionType.NFT_POPULARITY:
        return <CubeIcon className="h-5 w-5 text-pink-600" />
      default:
        return <ChartBarIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600 bg-green-100'
    if (confidence >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    return `${Math.floor(diffInHours / 24)}d ago`
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <ChartBarIcon className="h-6 w-6 mr-2 text-blue-600" />
            Predictive Analytics
          </h2>
          <p className="text-sm text-gray-600">
            AI-powered predictions and market insights
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {timeframes.map((timeframe) => (
              <option key={timeframe.value} value={timeframe.value}>
                {timeframe.label}
              </option>
            ))}
          </select>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {predictionTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Key Predictions Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Market Trend */}
        {marketTrend && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900">Market Trend</h3>
              <TrendingUpIcon className="h-5 w-5 text-blue-600" />
            </div>
            
            <div className="space-y-2">
              <div className="text-2xl font-bold text-gray-900">
                {typeof marketTrend.prediction.value === 'string' 
                  ? marketTrend.prediction.value 
                  : `${marketTrend.prediction.value}%`}
              </div>
              
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                getConfidenceColor(marketTrend.confidence)
              }`}>
                {marketTrend.confidence}% confidence
              </div>
              
              <p className="text-xs text-gray-600 mt-2">
                Valid until {new Date(marketTrend.validUntil).toLocaleDateString()}
              </p>
            </div>
          </div>
        )}

        {/* User Behavior */}
        {userBehavior && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900">Your Behavior</h3>
              <UserGroupIcon className="h-5 w-5 text-purple-600" />
            </div>
            
            <div className="space-y-2">
              <div className="text-2xl font-bold text-gray-900">
                {typeof userBehavior.prediction.value === 'string' 
                  ? userBehavior.prediction.value 
                  : `${userBehavior.prediction.value}%`}
              </div>
              
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                getConfidenceColor(userBehavior.confidence)
              }`}>
                {userBehavior.confidence}% confidence
              </div>
              
              <p className="text-xs text-gray-600 mt-2">
                Prediction accuracy: {userBehavior.historicalAccuracy}%
              </p>
            </div>
          </div>
        )}

        {/* Overall Accuracy */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">AI Accuracy</h3>
            <ChartBarIcon className="h-5 w-5 text-green-600" />
          </div>
          
          <div className="space-y-2">
            <div className="text-2xl font-bold text-gray-900">
              {predictions?.predictions.length 
                ? Math.round(predictions.predictions.reduce((acc, p) => acc + p.historicalAccuracy, 0) / predictions.predictions.length)
                : 85}%
            </div>
            
            <div className="text-xs text-gray-600">
              Based on {predictions?.predictions.length || 0} predictions
            </div>
            
            <div className="flex items-center text-xs text-green-600">
              <ArrowUpIcon className="h-3 w-3 mr-1" />
              +2.3% this week
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Predictions */}
      {predictions && predictions.predictions.length > 0 ? (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Detailed Predictions</h3>
          
          {predictions.predictions.map((prediction) => (
            <PredictionCard
              key={prediction.id}
              prediction={prediction}
              getIcon={getPredictionIcon}
              getConfidenceColor={getConfidenceColor}
              formatTimeAgo={formatTimeAgo}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No predictions available</h3>
          <p className="mt-1 text-sm text-gray-500">
            Predictions will appear here as our AI analyzes market data and trends.
          </p>
        </div>
      )}
    </div>
  )
}

interface PredictionCardProps {
  prediction: PredictiveAnalyticsType
  getIcon: (type: PredictionType) => React.ReactNode
  getConfidenceColor: (confidence: number) => string
  formatTimeAgo: (date: string) => string
}

function PredictionCard({
  prediction,
  getIcon,
  getConfidenceColor,
  formatTimeAgo
}: PredictionCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const getFactorImpactIcon = (impact: FactorImpact) => {
    switch (impact) {
      case FactorImpact.POSITIVE:
        return <ArrowUpIcon className="h-3 w-3 text-green-600" />
      case FactorImpact.NEGATIVE:
        return <ArrowDownIcon className="h-3 w-3 text-red-600" />
      default:
        return <MinusIcon className="h-3 w-3 text-gray-600" />
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex-shrink-0">
            {getIcon(prediction.type)}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-sm font-medium text-gray-900 capitalize">
                {prediction.type.replace(/_/g, ' ')}
              </h3>
              
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                getConfidenceColor(prediction.confidence)
              }`}>
                {prediction.confidence}% confidence
              </span>
            </div>
            
            <div className="space-y-2">
              <div className="text-lg font-semibold text-gray-900">
                {typeof prediction.prediction.value === 'string' 
                  ? prediction.prediction.value 
                  : `${prediction.prediction.value}${prediction.prediction.range ? ` (${prediction.prediction.range.min}-${prediction.prediction.range.max})` : ''}`}
              </div>
              
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <div className="flex items-center">
                  <ClockIcon className="h-3 w-3 mr-1" />
                  {formatTimeAgo(prediction.createdAt)}
                </div>
                
                <div>Target: {prediction.target}</div>
                <div>Timeframe: {prediction.timeframe}</div>
                <div>Accuracy: {prediction.historicalAccuracy}%</div>
              </div>
            </div>
          </div>
        </div>

        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-gray-400 hover:text-gray-600"
        >
          <InformationCircleIcon className="h-5 w-5" />
        </button>
      </div>

      {/* Scenarios */}
      {prediction.prediction.scenarios.length > 0 && (
        <div className="mt-4">
          <h4 className="text-xs font-medium text-gray-900 mb-2">Scenarios</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            {prediction.prediction.scenarios.map((scenario, index) => (
              <div key={index} className="bg-gray-50 rounded p-2">
                <div className="text-xs font-medium text-gray-900">{scenario.name}</div>
                <div className="text-xs text-gray-600">{scenario.probability}% probability</div>
                <div className="text-xs text-gray-700 mt-1">{scenario.outcome}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Expanded Details */}
      {isExpanded && (
        <div className="mt-4 pt-4 border-t border-gray-200 space-y-4">
          {/* Factors */}
          {prediction.factors.length > 0 && (
            <div>
              <h4 className="text-xs font-medium text-gray-900 mb-2">Key Factors</h4>
              <div className="space-y-2">
                {prediction.factors.map((factor, index) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-2">
                      {getFactorImpactIcon(factor.impact)}
                      <span className="text-gray-900">{factor.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-600">Weight: {factor.weight}%</span>
                      <span className="text-gray-600">Value: {factor.value}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Validity */}
          <div className="flex items-center text-xs text-gray-500">
            <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
            Valid until {new Date(prediction.validUntil).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  )
}
