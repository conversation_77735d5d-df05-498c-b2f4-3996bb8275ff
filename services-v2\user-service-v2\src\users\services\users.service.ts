/**
 * Users Service - User Service V2
 * 
 * Business logic for user management using shared infrastructure
 * Database Per Service Pattern - Uses user_service_v2 database only
 */

import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import {
  StandardizedPrismaService,
  ServiceLoggerService,
  BusinessOutcome,
  SecurityEventType,
  SecuritySeverity,
} from '../../../../../shared';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { QueryUsersDto } from '../dto/query-users.dto';

@Injectable()
export class UsersService {
  constructor(
    private readonly prisma: StandardizedPrismaService,
    private readonly logger: ServiceLoggerService,
  ) {}

  async create(createUserDto: CreateUserDto) {
    return this.prisma.executeWithMetrics('createUser', async () => {
      // Check if user already exists
      const existingUser = await this.prisma.user.findFirst({
        where: {
          OR: [
            { email: createUserDto.email },
            { username: createUserDto.username },
          ],
        },
      });

      if (existingUser) {
        this.logger.logSecurityEvent(
          SecurityEventType.SUSPICIOUS_ACTIVITY,
          SecuritySeverity.LOW,
          'Attempted to create user with existing email or username',
          {
            metadata: {
              email: createUserDto.email,
              username: createUserDto.username,
            },
          },
        );
        
        throw new ConflictException('User with this email or username already exists');
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(createUserDto.password, saltRounds);

      // Create user
      const user = await this.prisma.user.create({
        data: {
          ...createUserDto,
          password: hashedPassword,
        },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
          bio: true,
          isActive: true,
          isVerified: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Create default preferences
      await this.prisma.userPreference.create({
        data: {
          userId: user.id,
        },
      });

      this.logger.logBusinessEvent(
        'user',
        'create',
        BusinessOutcome.SUCCESS,
        {
          business: {
            entity: 'user',
            entityId: user.id,
            attributes: {
              email: user.email,
              username: user.username,
            },
          },
        },
      );

      return user;
    });
  }

  async findAll(queryDto: QueryUsersDto) {
    return this.prisma.executeWithMetrics('findAllUsers', async () => {
      const {
        page = 1,
        limit = 20,
        search,
        isActive,
        isVerified,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = queryDto;

      const skip = (page - 1) * limit;
      const where: any = {};

      // Apply filters
      if (search) {
        where.OR = [
          { email: { contains: search, mode: 'insensitive' } },
          { username: { contains: search, mode: 'insensitive' } },
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      if (isVerified !== undefined) {
        where.isVerified = isVerified;
      }

      // Get users and total count
      const [users, totalCount] = await Promise.all([
        this.prisma.user.findMany({
          where,
          select: {
            id: true,
            email: true,
            username: true,
            firstName: true,
            lastName: true,
            avatar: true,
            bio: true,
            isActive: true,
            isVerified: true,
            emailVerified: true,
            createdAt: true,
            updatedAt: true,
            lastLogin: true,
          },
          skip,
          take: limit,
          orderBy: {
            [sortBy]: sortOrder,
          },
        }),
        this.prisma.user.count({ where }),
      ]);

      this.logger.logBusinessEvent(
        'user',
        'list',
        BusinessOutcome.SUCCESS,
        {
          metadata: {
            count: users.length,
            totalCount,
            page,
            limit,
            filters: { search, isActive, isVerified },
          },
        },
      );

      // Build pagination metadata
      const totalPages = Math.ceil(totalCount / limit);
      const pagination = {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        nextPage: page < totalPages ? page + 1 : undefined,
        prevPage: page > 1 ? page - 1 : undefined,
      };

      return {
        users,
        pagination,
        filters: Object.entries({ search, isActive, isVerified })
          .filter(([_, value]) => value !== undefined)
          .map(([field, value]) => ({ field, operator: 'eq' as const, value })),
        sorting: [{ field: sortBy, direction: sortOrder as 'asc' | 'desc' }],
      };
    });
  }

  async findOne(id: string) {
    return this.prisma.executeWithMetrics('findUserById', async () => {
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
          bio: true,
          isActive: true,
          isVerified: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
          preferences: true,
        },
      });

      if (user) {
        this.logger.logBusinessEvent(
          'user',
          'read',
          BusinessOutcome.SUCCESS,
          {
            business: {
              entity: 'user',
              entityId: user.id,
            },
          },
        );
      }

      return user;
    });
  }

  async findByEmail(email: string) {
    return this.prisma.executeWithMetrics('findUserByEmail', async () => {
      return this.prisma.user.findUnique({
        where: { email },
        include: {
          preferences: true,
        },
      });
    });
  }

  async findByUsername(username: string) {
    return this.prisma.executeWithMetrics('findUserByUsername', async () => {
      return this.prisma.user.findUnique({
        where: { username },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
          bio: true,
          isActive: true,
          isVerified: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    return this.prisma.executeWithMetrics('updateUser', async () => {
      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!existingUser) {
        throw new NotFoundException('User not found');
      }

      // If updating email or username, check for conflicts
      if (updateUserDto.email || updateUserDto.username) {
        const conflictUser = await this.prisma.user.findFirst({
          where: {
            AND: [
              { id: { not: id } },
              {
                OR: [
                  updateUserDto.email ? { email: updateUserDto.email } : {},
                  updateUserDto.username ? { username: updateUserDto.username } : {},
                ].filter(condition => Object.keys(condition).length > 0),
              },
            ],
          },
        });

        if (conflictUser) {
          throw new ConflictException('Email or username already exists');
        }
      }

      // Hash password if provided
      const updateData = { ...updateUserDto };
      if (updateUserDto.password) {
        const saltRounds = 12;
        updateData.password = await bcrypt.hash(updateUserDto.password, saltRounds);
      }

      const user = await this.prisma.user.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
          bio: true,
          isActive: true,
          isVerified: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      this.logger.logBusinessEvent(
        'user',
        'update',
        BusinessOutcome.SUCCESS,
        {
          business: {
            entity: 'user',
            entityId: user.id,
            attributes: updateUserDto,
          },
        },
      );

      return user;
    });
  }

  async remove(id: string) {
    return this.prisma.executeWithMetrics('deleteUser', async () => {
      try {
        await this.prisma.user.delete({
          where: { id },
        });

        this.logger.logBusinessEvent(
          'user',
          'delete',
          BusinessOutcome.SUCCESS,
          {
            business: {
              entity: 'user',
              entityId: id,
            },
          },
        );

        return true;
      } catch (error) {
        if (error.code === 'P2025') {
          // User not found
          return false;
        }
        throw error;
      }
    });
  }

  async activate(id: string) {
    return this.prisma.executeWithMetrics('activateUser', async () => {
      try {
        const user = await this.prisma.user.update({
          where: { id },
          data: { isActive: true },
          select: {
            id: true,
            email: true,
            username: true,
            isActive: true,
          },
        });

        this.logger.logBusinessEvent(
          'user',
          'activate',
          BusinessOutcome.SUCCESS,
          {
            business: {
              entity: 'user',
              entityId: user.id,
            },
          },
        );

        return user;
      } catch (error) {
        if (error.code === 'P2025') {
          return null;
        }
        throw error;
      }
    });
  }

  async deactivate(id: string) {
    return this.prisma.executeWithMetrics('deactivateUser', async () => {
      try {
        const user = await this.prisma.user.update({
          where: { id },
          data: { isActive: false },
          select: {
            id: true,
            email: true,
            username: true,
            isActive: true,
          },
        });

        this.logger.logBusinessEvent(
          'user',
          'deactivate',
          BusinessOutcome.SUCCESS,
          {
            business: {
              entity: 'user',
              entityId: user.id,
            },
          },
        );

        return user;
      } catch (error) {
        if (error.code === 'P2025') {
          return null;
        }
        throw error;
      }
    });
  }

  async updateLastLogin(id: string) {
    return this.prisma.executeWithMetrics('updateLastLogin', async () => {
      return this.prisma.user.update({
        where: { id },
        data: { lastLogin: new Date() },
        select: { id: true, lastLogin: true },
      });
    });
  }
}
