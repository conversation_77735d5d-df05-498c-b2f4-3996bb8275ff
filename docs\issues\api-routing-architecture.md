# API Routing Architecture Guidelines

## Overview

This document establishes the standardized API routing architecture for the Social NFT Platform to prevent double API prefix issues and ensure consistent endpoint patterns across all microservices.

## Problem Statement

### The Double API Prefix Issue

**Issue:** Services were implementing both:
- Global API prefix: `app.setGlobalPrefix('api')` in `main.ts`
- Controller-level prefix: `@Controller('api/resource')` in controllers
- **Result:** Double prefixes like `/api/api/marketplace/listings` ❌

### Impact
- Broken API endpoints returning 404 errors
- Inconsistent health check paths
- API Gateway routing failures
- Developer confusion and debugging overhead

## Solution: Standardized API Routing Architecture

### ✅ RECOMMENDED APPROACH: Global Prefix Only

**Implementation:**
```typescript
// main.ts - Global prefix
app.setGlobalPrefix('api');

// Controllers - Resource paths only
@Controller('marketplace/listings')
@Controller('projects')
@Controller('campaigns')
```

**Result:** Clean, consistent paths like `/api/marketplace/listings` ✅

## Implementation Standards

### 1. Main.ts Configuration
```typescript
// ✅ CORRECT: Global API prefix
app.setGlobalPrefix(process.env.API_PREFIX || 'api');

// ✅ CORRECT: Swagger setup
SwaggerModule.setup('api/docs', app, document);
```

### 2. Controller Patterns
```typescript
// ✅ CORRECT: Resource-only paths
@Controller('marketplace/listings')
@Controller('projects')
@Controller('campaigns')
@Controller('auth/twitter')

// ❌ INCORRECT: Including 'api/' prefix
@Controller('api/marketplace/listings')
@Controller('api/projects')
```

### 3. Health Endpoint Standard
```typescript
// ✅ CORRECT: App controller for health
@Controller()
export class AppController {
  @Get('health')
  getHealth() { /* ... */ }
}

// Result: /api/health (with global prefix)
```

## Service-Specific Guidelines

### Core Business Services
- **Pattern:** `@Controller('resource')`
- **Examples:** `projects`, `campaigns`, `marketplace/listings`

### Authentication Services
- **Pattern:** `@Controller('auth/provider')`
- **Examples:** `auth/twitter`, `auth/oauth`

### Utility Services
- **Pattern:** `@Controller('utility')`
- **Examples:** `migration`, `health`, `environment`

## API Gateway Integration

### Proxy Service Configuration
```typescript
// ✅ CORRECT: Service routing
const response = await this.proxyService.forwardRequest(
  'marketplace-service',
  '/api/marketplace/listings',  // Full path with global prefix
  'GET'
);
```

### Health Check Paths
```typescript
// ✅ STANDARDIZED: All services use /api/health
const services = [
  { name: 'marketplace-service', health: '/api/health' },
  { name: 'project-service', health: '/api/health' },
  { name: 'user-service', health: '/api/health' }
];
```

## Migration Guide

### For Existing Services

1. **Verify Global Prefix**
   ```bash
   grep -n "setGlobalPrefix" services/*/src/main.ts
   ```

2. **Fix Controller Paths**
   ```bash
   # Find controllers with double prefix
   find services -name "*.controller.ts" -exec grep -l "@Controller('api/" {} \;

   # Fix by removing 'api/' prefix
   sed -i "s/@Controller('api\//@Controller('/g" controller-file.ts
   ```

3. **Test Endpoints**
   ```bash
   curl http://localhost:PORT/api/resource
   ```

## Validation Checklist

### ✅ Service Compliance Check
- [ ] Global prefix set in `main.ts`
- [ ] No `api/` prefix in controller decorators
- [ ] Health endpoint accessible at `/api/health`
- [ ] Swagger docs at `/api/docs`
- [ ] All endpoints return 200/proper responses

### ✅ API Gateway Integration
- [ ] Proxy routes use full paths with `/api/`
- [ ] Health checks use standardized paths
- [ ] No double prefix in forwarded requests

## Benefits

### 1. Consistency
- Uniform endpoint patterns across all services
- Predictable API structure for frontend integration
- Simplified API documentation

### 2. Maintainability
- Centralized prefix control via environment variables
- Easy API versioning (change global prefix)
- Reduced debugging overhead

### 3. Scalability
- Clear service boundaries
- Consistent microservices architecture
- Simplified service discovery

## Enforcement

### Development Rules
1. **All new services** MUST follow this architecture
2. **Code reviews** MUST verify controller patterns
3. **CI/CD pipelines** SHOULD validate endpoint patterns

### Automated Checks
```bash
# Add to CI/CD pipeline
./tools/scripts/validate-api-architecture.sh
```

## Implementation History

### Phase 1: Issue Discovery (2025-05-31)
- **Problem Identified:** Double API prefix causing 404 errors across all services
- **Root Cause:** Inconsistent use of global prefix + controller-level API paths
- **Impact:** Marketplace service, Project service, API Gateway routing failures

### Phase 2: Systematic Fix (2025-05-31)
- **Services Fixed:**
  - ✅ marketplace-service: `/api/marketplace/listings` working
  - ✅ project-service: `/api/projects` and `/api/campaigns` fixed
  - ✅ profile-analysis-service: `/api/auth/twitter` fixed
  - ✅ api-gateway: `/api/environment` fixed
- **Validation:** All controllers verified clean of double prefixes

### Phase 3: Architecture Documentation (2025-05-31)
- **Guidelines Created:** Comprehensive API routing standards
- **Validation Script:** Automated compliance checking
- **Enforcement:** CI/CD integration ready

## Tools and Scripts

### Validation Script
```bash
# Run architecture validation
./tools/scripts/validate-api-architecture.sh

# Make executable if needed
chmod +x tools/scripts/validate-api-architecture.sh
```

### Quick Fix Script
```bash
# Fix double API prefixes (if found)
./tools/scripts/fix-double-api-prefix.sh
```

### Manual Verification
```bash
# Check for remaining issues
find services -name "*.controller.ts" -exec grep -l "@Controller('api/" {} \;

# Should return no results if all fixed
```

## Related Documentation
- [Service Implementation Guide](../development/service-implementation-guide.md)
- [API Gateway Configuration](../development/api-gateway-setup.md)
- [Microservices Architecture](../architecture/microservices-overview.md)
- [Double API Prefix Fix Implementation](../development/projectid-enhancement-implementation.md)

---

**Status:** ✅ Implemented & Validated
**Last Updated:** 2025-05-31
**Version:** 1.0
**Applies To:** All microservices in Social NFT Platform
**Validation:** Automated via `validate-api-architecture.sh`
