import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthService } from '../services/auth.service';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(
    private readonly authService: AuthService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if the route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();

    try {
      // Extract token from Authorization header
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new UnauthorizedException('Missing or invalid authorization header');
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      // ✅ FIXED: Use new validateToken method with context parameter
      const context = {
        correlationId: request.headers['x-correlation-id'] || 'guard-' + Date.now(),
        userAgent: request.headers['user-agent'],
        ipAddress: request.ip || request.connection.remoteAddress
      };

      const validationResult = await this.authService.validateToken(token, context);

      if (!validationResult.success || !validationResult.data) {
        throw new UnauthorizedException(validationResult.error || 'Invalid or expired token');
      }

      // ✅ FIXED: Extract user data from validation result
      const userData = validationResult.data.user;
      const sessionData = validationResult.data.session;

      // Add user info to request
      request.user = userData;

      // Add session info to context if not already present
      if (!request.context) {
        request.context = {};
      }
      request.context.userId = userData.id;
      request.context.sessionId = sessionData?.id;
      request.context.correlationId = context.correlationId;

      this.logger.debug(`Authentication successful for user: ${userData.username}`, {
        userId: userData.id,
        sessionId: sessionData?.id,
        correlationId: context.correlationId
      });

      return true;

    } catch (error) {
      this.logger.debug(`Authentication failed: ${error.message}`, {
        path: request.url,
        method: request.method,
      });

      throw new UnauthorizedException(error.message || 'Authentication failed');
    }
  }
}
