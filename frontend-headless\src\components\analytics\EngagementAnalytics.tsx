'use client'

import React, { useState } from 'react'
import {
  UserGroupIcon,
  ClockIcon,
  CursorArrowRaysIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  ShareIcon,
  ChartBarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import {
  useEngagementAnalytics,
  useParticipantJourney,
  useFunnelAnalysis
} from '@/hooks/useAnalytics'
import { AnalyticsTimeframe } from '@/types/analytics.types'

interface EngagementAnalyticsProps {
  campaignId: string
  timeframe: AnalyticsTimeframe
  className?: string
}

export default function EngagementAnalytics({
  campaignId,
  timeframe,
  className = ''
}: EngagementAnalyticsProps) {
  const [selectedView, setSelectedView] = useState<'overview' | 'journey' | 'funnel'>('overview')

  const { data: engagement, isLoading: engagementLoading } = useEngagementAnalytics(campaignId, timeframe)
  const { data: journey, isLoading: journeyLoading } = useParticipantJ<PERSON>ney(campaignId)
  const { data: funnel, isLoading: funnelLoading } = useFunnelAnalysis(campaignId)

  if (engagementLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!engagement) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No engagement data available</h3>
        <p className="mt-1 text-sm text-gray-500">Engagement analytics will appear here once data is collected.</p>
      </div>
    )
  }

  const engagementMetrics = [
    {
      title: 'Total Sessions',
      value: engagement.totalSessions.toLocaleString(),
      icon: UserGroupIcon,
      color: 'text-blue-600 bg-blue-100',
      description: `${Math.round(engagement.averageSessionDuration / 60)}m avg duration`
    },
    {
      title: 'Active Users',
      value: engagement.dailyActiveUsers.toLocaleString(),
      icon: UserGroupIcon,
      color: 'text-green-600 bg-green-100',
      description: `${engagement.weeklyActiveUsers.toLocaleString()} weekly`
    },
    {
      title: 'Bounce Rate',
      value: `${(engagement.bounceRate * 100).toFixed(1)}%`,
      icon: ExclamationTriangleIcon,
      color: engagement.bounceRate > 0.7 ? 'text-red-600 bg-red-100' : engagement.bounceRate > 0.5 ? 'text-yellow-600 bg-yellow-100' : 'text-green-600 bg-green-100',
      description: `${(engagement.returnVisitorRate * 100).toFixed(1)}% return rate`
    },
    {
      title: 'Total Interactions',
      value: (engagement.totalClicks + engagement.totalShares + engagement.totalComments + engagement.totalLikes).toLocaleString(),
      icon: CursorArrowRaysIcon,
      color: 'text-purple-600 bg-purple-100',
      description: 'Clicks, shares, comments, likes'
    }
  ]

  const interactionBreakdown = [
    { type: 'Clicks', count: engagement.totalClicks, icon: CursorArrowRaysIcon, color: 'text-blue-600' },
    { type: 'Shares', count: engagement.totalShares, icon: ShareIcon, color: 'text-green-600' },
    { type: 'Comments', count: engagement.totalComments, icon: ChatBubbleLeftIcon, color: 'text-purple-600' },
    { type: 'Likes', count: engagement.totalLikes, icon: HeartIcon, color: 'text-red-600' }
  ]

  const viewOptions = [
    { id: 'overview', name: 'Overview' },
    { id: 'journey', name: 'User Journey' },
    { id: 'funnel', name: 'Funnel Analysis' }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Engagement Analytics</h2>
          <p className="text-sm text-gray-600">User interaction and engagement patterns</p>
        </div>

        {/* View Selector */}
        <div className="flex items-center border border-gray-300 rounded-md">
          {viewOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => setSelectedView(option.id as any)}
              className={`px-3 py-2 text-sm font-medium ${
                selectedView === option.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-700 hover:bg-gray-50'
              } ${option.id === 'overview' ? 'rounded-l-md' : option.id === 'funnel' ? 'rounded-r-md' : ''}`}
            >
              {option.name}
            </button>
          ))}
        </div>
      </div>

      {selectedView === 'overview' && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {engagementMetrics.map((metric, index) => (
              <div
                key={index}
                className="bg-white border border-gray-200 rounded-lg p-6"
              >
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${metric.color} mb-4`}>
                  <metric.icon className="h-6 w-6" />
                </div>
                <div className="mb-2">
                  <h3 className="text-2xl font-bold text-gray-900">{metric.value}</h3>
                  <p className="text-sm font-medium text-gray-700">{metric.title}</p>
                </div>
                <p className="text-sm text-gray-500">{metric.description}</p>
              </div>
            ))}
          </div>

          {/* Interaction Breakdown */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Interaction Breakdown</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {interactionBreakdown.map((interaction, index) => (
                <div key={index} className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <interaction.icon className={`h-8 w-8 ${interaction.color}`} />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">{interaction.count.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">{interaction.type}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Engagement Patterns */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Peak Hours */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Peak Engagement Hours</h3>
              <div className="space-y-3">
                {engagement.peakEngagementHours.slice(0, 5).map((hour, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{hour}:00</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${((24 - index) / 24) * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900">High</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Peak Days */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Peak Engagement Days</h3>
              <div className="space-y-3">
                {engagement.peakEngagementDays.slice(0, 5).map((day, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{day}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${((7 - index) / 7) * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900">High</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Content Engagement */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Content Engagement</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Most Engaged Content */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Most Engaged Content</h4>
                <div className="space-y-3">
                  {engagement.mostEngagedContent.slice(0, 5).map((content, index) => (
                    <div key={content.contentId} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{content.title}</div>
                        <div className="text-xs text-gray-600">{content.contentType}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-green-600">{content.engagementScore.toFixed(1)}</div>
                        <div className="text-xs text-gray-500">{content.views} views</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Least Engaged Content */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Needs Improvement</h4>
                <div className="space-y-3">
                  {engagement.leastEngagedContent.slice(0, 5).map((content, index) => (
                    <div key={content.contentId} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{content.title}</div>
                        <div className="text-xs text-gray-600">{content.contentType}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-red-600">{content.engagementScore.toFixed(1)}</div>
                        <div className="text-xs text-gray-500">{content.views} views</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Dropoff Analysis */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Common Dropoff Points</h3>
            <div className="space-y-4">
              {engagement.commonDropoffPoints.map((dropoff, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900">{dropoff.step}</h4>
                    <span className="text-sm font-medium text-red-600">
                      {(dropoff.dropoffRate * 100).toFixed(1)}% dropoff
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 mb-2">
                    {dropoff.participantsLost.toLocaleString()} participants lost
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {dropoff.commonReasons.map((reason, reasonIndex) => (
                      <span
                        key={reasonIndex}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700"
                      >
                        {reason}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      {selectedView === 'journey' && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">User Journey Analysis</h3>
          {journeyLoading ? (
            <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
          ) : journey ? (
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-600">User journey visualization</p>
                <p className="text-xs text-gray-500">Sankey diagram or flow chart would be implemented here</p>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No journey data available
            </div>
          )}
        </div>
      )}

      {selectedView === 'funnel' && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Conversion Funnel</h3>
          {funnelLoading ? (
            <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
          ) : funnel ? (
            <div className="space-y-4">
              {funnel.funnelSteps.map((step, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="flex-shrink-0 w-24 text-right">
                    <div className="text-sm font-medium text-gray-900">{step.step}</div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm text-gray-600">{step.participants.toLocaleString()} participants</span>
                      <span className="text-sm font-medium text-gray-900">
                        {(step.conversionRate * 100).toFixed(1)}% conversion
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-blue-600 h-3 rounded-full"
                        style={{ width: `${step.conversionRate * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="flex-shrink-0 w-16 text-right">
                    <span className="text-xs text-red-600">
                      -{(step.dropoffRate * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No funnel data available
            </div>
          )}
        </div>
      )}
    </div>
  )
}
