import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { UserManagementService, User, UserProfile, UserPreferences } from './user-management.service';

export interface ProfileUpdateRequest {
  displayName?: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
  location?: string;
  website?: string;
  avatar?: string;
  coverImage?: string;
  socialLinks?: {
    twitter?: string;
    discord?: string;
    telegram?: string;
    instagram?: string;
  };
}

export interface ProfileStatsUpdate {
  nftsGenerated?: number;
  nftsMinted?: number;
  analysesCompleted?: number;
  scoreToAdd?: number;
}

export interface UserActivity {
  id: string;
  userId: string;
  type: 'nft_generated' | 'nft_minted' | 'analysis_completed' | 'profile_updated' | 'achievement_unlocked';
  title: string;
  description: string;
  metadata: Record<string, any>;
  timestamp: Date;
  isPublic: boolean;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'nft' | 'analysis' | 'social' | 'milestone';
  requirement: {
    type: string;
    value: number;
  };
  reward?: {
    type: 'badge' | 'title' | 'feature';
    value: string;
  };
}

export interface UserAchievement {
  achievementId: string;
  unlockedAt: Date;
  progress: number;
  isCompleted: boolean;
}

export interface PublicProfile {
  id: string;
  username: string;
  displayName: string;
  bio?: string;
  avatar?: string;
  location?: string;
  website?: string;
  socialLinks: UserProfile['socialLinks'];
  stats: UserProfile['stats'];
  achievements: UserAchievement[];
  recentActivity: UserActivity[];
  joinedAt: Date;
  isVerified: boolean;
}

@Injectable()
export class UserProfileService {
  private readonly logger = new Logger(UserProfileService.name);
  private userActivities: Map<string, UserActivity[]> = new Map();
  private userAchievements: Map<string, UserAchievement[]> = new Map();
  private achievements: Achievement[] = [];

  constructor(private readonly userManagement: UserManagementService) {
    this.initializeAchievements();
  }

  /**
   * Get user's full profile
   */
  async getUserProfile(userId: string): Promise<User> {
    try {
      this.logger.log(`Getting profile for user: ${userId}`);

      const user = await this.userManagement.getUserById(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      return user;
    } catch (error) {
      this.logger.error(`Failed to get user profile: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get public profile (limited information)
   */
  async getPublicProfile(username: string): Promise<PublicProfile> {
    try {
      this.logger.log(`Getting public profile for username: ${username}`);

      const user = await this.userManagement.getUserByUsername(username);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if profile is public
      if (!user.preferences.privacy.profilePublic) {
        throw new NotFoundException('Profile is private');
      }

      const achievements = this.userAchievements.get(user.id) || [];
      const recentActivity = this.getRecentActivity(user.id, 10);

      const publicProfile: PublicProfile = {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        bio: user.profile.bio,
        avatar: user.profile.avatar,
        location: user.profile.location,
        website: user.profile.website,
        socialLinks: user.profile.socialLinks,
        stats: user.preferences.privacy.showStats ? user.profile.stats : {
          nftsGenerated: 0,
          nftsMinted: 0,
          analysesCompleted: 0,
          totalScore: 0,
          averageScore: 0,
        },
        achievements: achievements.filter(a => a.isCompleted),
        recentActivity: recentActivity.filter(a => a.isPublic),
        joinedAt: user.metadata.createdAt,
        isVerified: user.emailVerified,
      };

      return publicProfile;
    } catch (error) {
      this.logger.error(`Failed to get public profile: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: string, updateRequest: ProfileUpdateRequest): Promise<User> {
    try {
      this.logger.log(`Updating profile for user: ${userId}`);

      // Validate update request
      this.validateProfileUpdate(updateRequest);

      // Update user profile
      const updatedUser = await this.userManagement.updateUser(userId, updateRequest);

      // Log activity
      await this.logActivity(userId, 'profile_updated', 'Profile Updated', 'User updated their profile information', {
        updatedFields: Object.keys(updateRequest),
      });

      this.logger.log(`Profile updated successfully for user: ${userId}`);
      return updatedUser;
    } catch (error) {
      this.logger.error(`Failed to update profile: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(userId: string, preferences: Partial<UserPreferences>): Promise<User> {
    try {
      this.logger.log(`Updating preferences for user: ${userId}`);

      const updatedUser = await this.userManagement.updateUserPreferences(userId, preferences);

      this.logger.log(`Preferences updated successfully for user: ${userId}`);
      return updatedUser;
    } catch (error) {
      this.logger.error(`Failed to update preferences: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update user stats (called by other services)
   */
  async updateUserStats(userId: string, statsUpdate: ProfileStatsUpdate): Promise<void> {
    try {
      this.logger.log(`Updating stats for user: ${userId}`, statsUpdate);

      const user = await this.userManagement.getUserById(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const currentStats = user.profile.stats;
      const newStats = { ...currentStats };

      // Update stats
      if (statsUpdate.nftsGenerated !== undefined) {
        newStats.nftsGenerated += statsUpdate.nftsGenerated;
        
        // Log activity for NFT generation
        if (statsUpdate.nftsGenerated > 0) {
          await this.logActivity(userId, 'nft_generated', 'NFT Generated', `Generated ${statsUpdate.nftsGenerated} NFT(s)`, {
            count: statsUpdate.nftsGenerated,
          });
        }
      }

      if (statsUpdate.nftsMinted !== undefined) {
        newStats.nftsMinted += statsUpdate.nftsMinted;
        
        // Log activity for NFT minting
        if (statsUpdate.nftsMinted > 0) {
          await this.logActivity(userId, 'nft_minted', 'NFT Minted', `Minted ${statsUpdate.nftsMinted} NFT(s) to blockchain`, {
            count: statsUpdate.nftsMinted,
          });
        }
      }

      if (statsUpdate.analysesCompleted !== undefined) {
        newStats.analysesCompleted += statsUpdate.analysesCompleted;
        
        // Log activity for analysis completion
        if (statsUpdate.analysesCompleted > 0) {
          await this.logActivity(userId, 'analysis_completed', 'Analysis Completed', `Completed ${statsUpdate.analysesCompleted} profile analysis`, {
            count: statsUpdate.analysesCompleted,
          });
        }
      }

      if (statsUpdate.scoreToAdd !== undefined) {
        newStats.totalScore += statsUpdate.scoreToAdd;
        
        // Recalculate average score
        if (newStats.analysesCompleted > 0) {
          newStats.averageScore = newStats.totalScore / newStats.analysesCompleted;
        }
      }

      // Update user stats
      await this.userManagement.updateUserStats(userId, newStats);

      // Check for achievements
      await this.checkAchievements(userId, newStats);

      this.logger.log(`Stats updated successfully for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to update user stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get user activity feed
   */
  async getUserActivity(userId: string, limit: number = 50, offset: number = 0): Promise<UserActivity[]> {
    try {
      const activities = this.userActivities.get(userId) || [];
      return activities
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(offset, offset + limit);
    } catch (error) {
      this.logger.error(`Failed to get user activity: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get user achievements
   */
  async getUserAchievements(userId: string): Promise<{ achievements: UserAchievement[]; available: Achievement[] }> {
    try {
      const userAchievements = this.userAchievements.get(userId) || [];
      const availableAchievements = this.achievements;

      return {
        achievements: userAchievements,
        available: availableAchievements,
      };
    } catch (error) {
      this.logger.error(`Failed to get user achievements: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Search users by various criteria
   */
  async searchUsers(query: string, filters?: {
    location?: string;
    minScore?: number;
    hasNFTs?: boolean;
  }, limit: number = 20): Promise<PublicProfile[]> {
    try {
      this.logger.log(`Searching users with query: ${query}`);

      // Get users from user management service
      const users = await this.userManagement.searchUsers(query, limit * 2); // Get more to filter

      // Filter and convert to public profiles
      const publicProfiles: PublicProfile[] = [];

      for (const user of users) {
        // Skip private profiles
        if (!user.preferences.privacy.profilePublic) {
          continue;
        }

        // Apply filters
        if (filters) {
          if (filters.location && user.profile.location !== filters.location) {
            continue;
          }

          if (filters.minScore && user.profile.stats.averageScore < filters.minScore) {
            continue;
          }

          if (filters.hasNFTs && user.profile.stats.nftsGenerated === 0) {
            continue;
          }
        }

        const achievements = this.userAchievements.get(user.id) || [];
        const recentActivity = this.getRecentActivity(user.id, 5);

        publicProfiles.push({
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          bio: user.profile.bio,
          avatar: user.profile.avatar,
          location: user.profile.location,
          website: user.profile.website,
          socialLinks: user.profile.socialLinks,
          stats: user.preferences.privacy.showStats ? user.profile.stats : {
            nftsGenerated: 0,
            nftsMinted: 0,
            analysesCompleted: 0,
            totalScore: 0,
            averageScore: 0,
          },
          achievements: achievements.filter(a => a.isCompleted),
          recentActivity: recentActivity.filter(a => a.isPublic),
          joinedAt: user.metadata.createdAt,
          isVerified: user.emailVerified,
        });

        if (publicProfiles.length >= limit) {
          break;
        }
      }

      return publicProfiles;
    } catch (error) {
      this.logger.error(`Failed to search users: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get leaderboard
   */
  async getLeaderboard(type: 'score' | 'nfts' | 'analyses', limit: number = 50): Promise<PublicProfile[]> {
    try {
      this.logger.log(`Getting leaderboard for: ${type}`);

      // Get all users (in a real implementation, this would be optimized)
      const { users } = await this.userManagement.getAllUsers(1000);

      // Filter public profiles and sort
      const publicUsers = users.filter(user => user.preferences.privacy.profilePublic && user.preferences.privacy.showStats);

      let sortedUsers: User[];
      switch (type) {
        case 'score':
          sortedUsers = publicUsers.sort((a, b) => b.profile.stats.averageScore - a.profile.stats.averageScore);
          break;
        case 'nfts':
          sortedUsers = publicUsers.sort((a, b) => b.profile.stats.nftsGenerated - a.profile.stats.nftsGenerated);
          break;
        case 'analyses':
          sortedUsers = publicUsers.sort((a, b) => b.profile.stats.analysesCompleted - a.profile.stats.analysesCompleted);
          break;
        default:
          sortedUsers = publicUsers;
      }

      // Convert to public profiles
      const leaderboard: PublicProfile[] = sortedUsers.slice(0, limit).map(user => {
        const achievements = this.userAchievements.get(user.id) || [];
        const recentActivity = this.getRecentActivity(user.id, 3);

        return {
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          bio: user.profile.bio,
          avatar: user.profile.avatar,
          location: user.profile.location,
          website: user.profile.website,
          socialLinks: user.profile.socialLinks,
          stats: user.profile.stats,
          achievements: achievements.filter(a => a.isCompleted),
          recentActivity: recentActivity.filter(a => a.isPublic),
          joinedAt: user.metadata.createdAt,
          isVerified: user.emailVerified,
        };
      });

      return leaderboard;
    } catch (error) {
      this.logger.error(`Failed to get leaderboard: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Log user activity
   */
  private async logActivity(
    userId: string,
    type: UserActivity['type'],
    title: string,
    description: string,
    metadata: Record<string, any> = {},
    isPublic: boolean = true
  ): Promise<void> {
    try {
      const activity: UserActivity = {
        id: this.generateActivityId(),
        userId,
        type,
        title,
        description,
        metadata,
        timestamp: new Date(),
        isPublic,
      };

      if (!this.userActivities.has(userId)) {
        this.userActivities.set(userId, []);
      }

      const activities = this.userActivities.get(userId)!;
      activities.push(activity);

      // Keep only last 100 activities
      if (activities.length > 100) {
        this.userActivities.set(userId, activities.slice(-100));
      }

      this.logger.debug(`Activity logged for user ${userId}: ${type}`);
    } catch (error) {
      this.logger.error(`Failed to log activity: ${error.message}`, error.stack);
    }
  }

  /**
   * Get recent activity
   */
  private getRecentActivity(userId: string, limit: number): UserActivity[] {
    const activities = this.userActivities.get(userId) || [];
    return activities
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Check and unlock achievements
   */
  private async checkAchievements(userId: string, stats: UserProfile['stats']): Promise<void> {
    try {
      if (!this.userAchievements.has(userId)) {
        this.userAchievements.set(userId, []);
      }

      const userAchievements = this.userAchievements.get(userId)!;

      for (const achievement of this.achievements) {
        // Check if already unlocked
        const existing = userAchievements.find(ua => ua.achievementId === achievement.id);
        if (existing && existing.isCompleted) {
          continue;
        }

        // Check achievement requirements
        let progress = 0;
        let isCompleted = false;

        switch (achievement.requirement.type) {
          case 'nfts_generated':
            progress = stats.nftsGenerated;
            isCompleted = progress >= achievement.requirement.value;
            break;
          case 'nfts_minted':
            progress = stats.nftsMinted;
            isCompleted = progress >= achievement.requirement.value;
            break;
          case 'analyses_completed':
            progress = stats.analysesCompleted;
            isCompleted = progress >= achievement.requirement.value;
            break;
          case 'average_score':
            progress = stats.averageScore;
            isCompleted = progress >= achievement.requirement.value;
            break;
        }

        if (existing) {
          // Update existing achievement
          existing.progress = progress;
          if (!existing.isCompleted && isCompleted) {
            existing.isCompleted = true;
            existing.unlockedAt = new Date();
            
            // Log achievement unlock
            await this.logActivity(userId, 'achievement_unlocked', 'Achievement Unlocked!', `Unlocked "${achievement.name}"`, {
              achievementId: achievement.id,
              achievementName: achievement.name,
            });
          }
        } else if (progress > 0) {
          // Create new achievement progress
          const userAchievement: UserAchievement = {
            achievementId: achievement.id,
            unlockedAt: isCompleted ? new Date() : new Date(0),
            progress,
            isCompleted,
          };

          userAchievements.push(userAchievement);

          if (isCompleted) {
            // Log achievement unlock
            await this.logActivity(userId, 'achievement_unlocked', 'Achievement Unlocked!', `Unlocked "${achievement.name}"`, {
              achievementId: achievement.id,
              achievementName: achievement.name,
            });
          }
        }
      }
    } catch (error) {
      this.logger.error(`Failed to check achievements: ${error.message}`, error.stack);
    }
  }

  /**
   * Validate profile update request
   */
  private validateProfileUpdate(updateRequest: ProfileUpdateRequest): void {
    const errors: string[] = [];

    if (updateRequest.website && !this.isValidUrl(updateRequest.website)) {
      errors.push('Invalid website URL');
    }

    if (updateRequest.bio && updateRequest.bio.length > 500) {
      errors.push('Bio must be 500 characters or less');
    }

    if (updateRequest.displayName && (updateRequest.displayName.length < 1 || updateRequest.displayName.length > 50)) {
      errors.push('Display name must be between 1 and 50 characters');
    }

    if (errors.length > 0) {
      throw new BadRequestException(`Validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Initialize achievements
   */
  private initializeAchievements(): void {
    this.achievements = [
      {
        id: 'first_nft',
        name: 'First NFT',
        description: 'Generate your first NFT',
        icon: '🎨',
        category: 'nft',
        requirement: { type: 'nfts_generated', value: 1 },
        reward: { type: 'badge', value: 'Creator' },
      },
      {
        id: 'nft_collector',
        name: 'NFT Collector',
        description: 'Generate 10 NFTs',
        icon: '🖼️',
        category: 'nft',
        requirement: { type: 'nfts_generated', value: 10 },
        reward: { type: 'badge', value: 'Collector' },
      },
      {
        id: 'nft_master',
        name: 'NFT Master',
        description: 'Generate 50 NFTs',
        icon: '👑',
        category: 'nft',
        requirement: { type: 'nfts_generated', value: 50 },
        reward: { type: 'title', value: 'NFT Master' },
      },
      {
        id: 'first_mint',
        name: 'First Mint',
        description: 'Mint your first NFT to blockchain',
        icon: '⛓️',
        category: 'nft',
        requirement: { type: 'nfts_minted', value: 1 },
        reward: { type: 'badge', value: 'Minter' },
      },
      {
        id: 'analyst',
        name: 'Analyst',
        description: 'Complete 5 profile analyses',
        icon: '📊',
        category: 'analysis',
        requirement: { type: 'analyses_completed', value: 5 },
        reward: { type: 'badge', value: 'Analyst' },
      },
      {
        id: 'high_scorer',
        name: 'High Scorer',
        description: 'Achieve an average score of 80+',
        icon: '🏆',
        category: 'milestone',
        requirement: { type: 'average_score', value: 80 },
        reward: { type: 'badge', value: 'High Scorer' },
      },
    ];
  }

  /**
   * Validate URL
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Generate activity ID
   */
  private generateActivityId(): string {
    return `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
