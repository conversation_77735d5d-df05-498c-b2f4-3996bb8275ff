import { api } from '@/lib/api'
import {
  CampaignAnalytics,
  AnalyticsQuery,
  AnalyticsResponse,
  AnalyticsTimeframe,
  MetricType,
  ReportTemplate,
  GeneratedReport,
  ReportFormat,
  ChartConfig,
  ReportFilter,
  ReportSchedule
} from '@/types/analytics.types'

export class AnalyticsService {
  // ===== CAMPAIGN ANALYTICS =====
  
  async getCampaignAnalytics(
    campaignId: string, 
    timeframe: AnalyticsTimeframe = AnalyticsTimeframe.MONTH
  ): Promise<CampaignAnalytics> {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign analytics:', error)
      throw new Error('Failed to load campaign analytics')
    }
  }

  async getMultipleCampaignAnalytics(query: AnalyticsQuery): Promise<AnalyticsResponse> {
    try {
      const response = await api.post('/analytics/campaigns/query', query)
      return response.data
    } catch (error) {
      console.error('Failed to fetch multiple campaign analytics:', error)
      throw new Error('Failed to load campaign analytics')
    }
  }

  async getCampaignComparison(
    campaignIds: string[], 
    timeframe: AnalyticsTimeframe = AnalyticsTimeframe.MONTH
  ): Promise<CampaignAnalytics[]> {
    try {
      const response = await api.post('/analytics/campaigns/compare', {
        campaignIds,
        timeframe
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign comparison:', error)
      throw new Error('Failed to load campaign comparison')
    }
  }

  // ===== REAL-TIME ANALYTICS =====

  async getRealTimeMetrics(campaignId: string): Promise<{
    activeParticipants: number
    currentEngagements: number
    recentConversions: number
    liveActivity: Array<{
      timestamp: string
      event: string
      count: number
    }>
  }> {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/realtime`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch real-time metrics:', error)
      throw new Error('Failed to load real-time metrics')
    }
  }

  async getActivityFeed(campaignId: string, limit: number = 50): Promise<Array<{
    id: string
    timestamp: string
    event: string
    participant: string
    details: any
  }>> {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/activity`, {
        params: { limit }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch activity feed:', error)
      throw new Error('Failed to load activity feed')
    }
  }

  // ===== ENGAGEMENT ANALYTICS =====

  async getEngagementAnalytics(campaignId: string, timeframe: AnalyticsTimeframe) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/engagement`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch engagement analytics:', error)
      throw new Error('Failed to load engagement analytics')
    }
  }

  async getParticipantJourney(campaignId: string, participantId?: string) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/journey`, {
        params: participantId ? { participantId } : undefined
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch participant journey:', error)
      throw new Error('Failed to load participant journey')
    }
  }

  async getFunnelAnalysis(campaignId: string) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/funnel`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch funnel analysis:', error)
      throw new Error('Failed to load funnel analysis')
    }
  }

  // ===== CONVERSION ANALYTICS =====

  async getConversionAnalytics(campaignId: string, timeframe: AnalyticsTimeframe) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/conversion`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch conversion analytics:', error)
      throw new Error('Failed to load conversion analytics')
    }
  }

  async getCohortAnalysis(campaignId: string) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/cohort`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch cohort analysis:', error)
      throw new Error('Failed to load cohort analysis')
    }
  }

  async getRetentionAnalysis(campaignId: string) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/retention`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch retention analysis:', error)
      throw new Error('Failed to load retention analysis')
    }
  }

  // ===== REVENUE & ROI ANALYTICS =====

  async getRevenueAnalytics(campaignId: string, timeframe: AnalyticsTimeframe) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/revenue`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch revenue analytics:', error)
      throw new Error('Failed to load revenue analytics')
    }
  }

  async getROIAnalysis(campaignId: string) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/roi`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch ROI analysis:', error)
      throw new Error('Failed to load ROI analysis')
    }
  }

  async getCostAnalysis(campaignId: string, timeframe: AnalyticsTimeframe) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/costs`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch cost analysis:', error)
      throw new Error('Failed to load cost analysis')
    }
  }

  // ===== SOCIAL & GEOGRAPHIC ANALYTICS =====

  async getSocialAnalytics(campaignId: string, timeframe: AnalyticsTimeframe) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/social`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch social analytics:', error)
      throw new Error('Failed to load social analytics')
    }
  }

  async getGeographicAnalytics(campaignId: string) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/geographic`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch geographic analytics:', error)
      throw new Error('Failed to load geographic analytics')
    }
  }

  async getMarketAnalysis(campaignId: string) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/market`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch market analysis:', error)
      throw new Error('Failed to load market analysis')
    }
  }

  // ===== BENCHMARKING =====

  async getBenchmarkData(campaignId: string) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/benchmarks`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch benchmark data:', error)
      throw new Error('Failed to load benchmark data')
    }
  }

  async getIndustryBenchmarks(industry: string) {
    try {
      const response = await api.get('/analytics/benchmarks/industry', {
        params: { industry }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch industry benchmarks:', error)
      throw new Error('Failed to load industry benchmarks')
    }
  }

  async getCompetitorAnalysis(campaignId: string) {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/competitors`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch competitor analysis:', error)
      throw new Error('Failed to load competitor analysis')
    }
  }

  // ===== REPORT TEMPLATES =====

  async getReportTemplates(): Promise<ReportTemplate[]> {
    try {
      const response = await api.get('/analytics/reports/templates')
      return response.data
    } catch (error) {
      console.error('Failed to fetch report templates:', error)
      throw new Error('Failed to load report templates')
    }
  }

  async getReportTemplate(id: string): Promise<ReportTemplate> {
    try {
      const response = await api.get(`/analytics/reports/templates/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch report template:', error)
      throw new Error('Failed to load report template')
    }
  }

  async createReportTemplate(template: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ReportTemplate> {
    try {
      const response = await api.post('/analytics/reports/templates', template)
      return response.data
    } catch (error) {
      console.error('Failed to create report template:', error)
      throw new Error('Failed to create report template')
    }
  }

  async updateReportTemplate(id: string, template: Partial<ReportTemplate>): Promise<ReportTemplate> {
    try {
      const response = await api.patch(`/analytics/reports/templates/${id}`, template)
      return response.data
    } catch (error) {
      console.error('Failed to update report template:', error)
      throw new Error('Failed to update report template')
    }
  }

  async deleteReportTemplate(id: string): Promise<void> {
    try {
      await api.delete(`/analytics/reports/templates/${id}`)
    } catch (error) {
      console.error('Failed to delete report template:', error)
      throw new Error('Failed to delete report template')
    }
  }

  // ===== REPORT GENERATION =====

  async generateReport(
    templateId: string,
    campaignId: string,
    format: ReportFormat = ReportFormat.PDF,
    filters?: ReportFilter[]
  ): Promise<GeneratedReport> {
    try {
      const response = await api.post('/analytics/reports/generate', {
        templateId,
        campaignId,
        format,
        filters
      })
      return response.data
    } catch (error) {
      console.error('Failed to generate report:', error)
      throw new Error('Failed to generate report')
    }
  }

  async getGeneratedReports(campaignId?: string): Promise<GeneratedReport[]> {
    try {
      const response = await api.get('/analytics/reports/generated', {
        params: campaignId ? { campaignId } : undefined
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch generated reports:', error)
      throw new Error('Failed to load generated reports')
    }
  }

  async downloadReport(reportId: string): Promise<Blob> {
    try {
      const response = await api.get(`/analytics/reports/generated/${reportId}/download`, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to download report:', error)
      throw new Error('Failed to download report')
    }
  }

  async scheduleReport(
    templateId: string,
    campaignId: string,
    schedule: ReportSchedule
  ): Promise<{ id: string; message: string }> {
    try {
      const response = await api.post('/analytics/reports/schedule', {
        templateId,
        campaignId,
        schedule
      })
      return response.data
    } catch (error) {
      console.error('Failed to schedule report:', error)
      throw new Error('Failed to schedule report')
    }
  }

  // ===== CUSTOM QUERIES =====

  async executeCustomQuery(query: AnalyticsQuery): Promise<AnalyticsResponse> {
    try {
      const response = await api.post('/analytics/query', query)
      return response.data
    } catch (error) {
      console.error('Failed to execute custom query:', error)
      throw new Error('Failed to execute custom query')
    }
  }

  async getQuerySuggestions(campaignId: string): Promise<{
    metrics: string[]
    dimensions: string[]
    filters: string[]
  }> {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/query-suggestions`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch query suggestions:', error)
      throw new Error('Failed to load query suggestions')
    }
  }

  // ===== DATA EXPORT =====

  async exportAnalyticsData(
    campaignId: string,
    format: ReportFormat = ReportFormat.CSV,
    query?: Partial<AnalyticsQuery>
  ): Promise<Blob> {
    try {
      const response = await api.post(`/analytics/campaigns/${campaignId}/export`, {
        format,
        query
      }, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to export analytics data:', error)
      throw new Error('Failed to export analytics data')
    }
  }

  async exportChartData(
    campaignId: string,
    chartConfig: ChartConfig,
    format: ReportFormat = ReportFormat.PNG
  ): Promise<Blob> {
    try {
      const response = await api.post(`/analytics/campaigns/${campaignId}/export-chart`, {
        chartConfig,
        format
      }, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to export chart data:', error)
      throw new Error('Failed to export chart data')
    }
  }

  // ===== ALERTS & NOTIFICATIONS =====

  async createAlert(campaignId: string, alert: {
    name: string
    metric: string
    condition: 'greater_than' | 'less_than' | 'equals' | 'change_by'
    threshold: number
    frequency: 'immediate' | 'hourly' | 'daily'
    recipients: string[]
  }): Promise<{ id: string; message: string }> {
    try {
      const response = await api.post(`/analytics/campaigns/${campaignId}/alerts`, alert)
      return response.data
    } catch (error) {
      console.error('Failed to create alert:', error)
      throw new Error('Failed to create alert')
    }
  }

  async getAlerts(campaignId: string): Promise<Array<{
    id: string
    name: string
    metric: string
    condition: string
    threshold: number
    status: 'active' | 'triggered' | 'disabled'
    lastTriggered?: string
  }>> {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/alerts`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch alerts:', error)
      throw new Error('Failed to load alerts')
    }
  }

  async updateAlert(campaignId: string, alertId: string, updates: any): Promise<void> {
    try {
      await api.patch(`/analytics/campaigns/${campaignId}/alerts/${alertId}`, updates)
    } catch (error) {
      console.error('Failed to update alert:', error)
      throw new Error('Failed to update alert')
    }
  }

  async deleteAlert(campaignId: string, alertId: string): Promise<void> {
    try {
      await api.delete(`/analytics/campaigns/${campaignId}/alerts/${alertId}`)
    } catch (error) {
      console.error('Failed to delete alert:', error)
      throw new Error('Failed to delete alert')
    }
  }

  // ===== DASHBOARD CONFIGURATION =====

  async getDashboardConfig(campaignId: string): Promise<{
    widgets: Array<{
      id: string
      type: string
      title: string
      metric: string
      position: { x: number; y: number; width: number; height: number }
      config: any
    }>
    layout: string
    refreshInterval: number
  }> {
    try {
      const response = await api.get(`/analytics/campaigns/${campaignId}/dashboard`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch dashboard config:', error)
      throw new Error('Failed to load dashboard configuration')
    }
  }

  async updateDashboardConfig(campaignId: string, config: any): Promise<void> {
    try {
      await api.patch(`/analytics/campaigns/${campaignId}/dashboard`, config)
    } catch (error) {
      console.error('Failed to update dashboard config:', error)
      throw new Error('Failed to update dashboard configuration')
    }
  }

  // ===== PERFORMANCE MONITORING =====

  async getAnalyticsHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'down'
    latency: number
    uptime: number
    dataFreshness: string
    issues: string[]
  }> {
    try {
      const response = await api.get('/analytics/health')
      return response.data
    } catch (error) {
      console.error('Failed to fetch analytics health:', error)
      throw new Error('Failed to load analytics health status')
    }
  }

  async refreshAnalyticsCache(campaignId: string): Promise<{ message: string }> {
    try {
      const response = await api.post(`/analytics/campaigns/${campaignId}/refresh-cache`)
      return response.data
    } catch (error) {
      console.error('Failed to refresh analytics cache:', error)
      throw new Error('Failed to refresh analytics cache')
    }
  }
}

export const analyticsService = new AnalyticsService()
