I think we need a fundamental and radical redesign of our program and platform (which we are renaming from Twitter NFT Generator to Social NFT). To that end, it's best if I first explain what the platform's goal is, what problem it is meant to solve, and how it works.

Project Goal:
The goal of this platform is to help blockchain, crypto, and Web3 related projects by assisting them in attracting and retaining new and existing users. Therefore, it's based on two well-known and popular concepts and tools, which are:
1- Social networks including Twitter, Farcaster, and Lens (with initial focus on Twitter since it has a large pool of potential users to attract to Web3 projects).
2- NFTs

What problem are we trying to solve?

Many projects struggle with user retention after the initial acquisition of new users. In other words, users stop being actively engaged after onboarding and initial entry. As the hype around the project fades, they experience audience churn. We aim to maintain and sustain user activity by linking upgradable NFTs (NFTs that evolve over time) and creating a marketplace for these NFTs. This also increases brand awareness and user engagement rates with these projects, along with other benefits.

How we work:

We act as an intermediary between blockchain, crypto, and Web3 project owners seeking user acquisition on the one hand, and active and potential social media users, particularly on Twitter, on the other. We engage with these project owners to launch user acquisition campaigns for them on our platform. Simultaneously, we encourage social media users, especially on Twitter, to participate and interact with these projects and their campaigns.

Who are the stakeholders of our platform?

Our platform has three main stakeholders, as follows:

1- The creators and administrators of the very platform we are implementing, which we call Social NFT.
2- Owners of blockchain, crypto, and Web3-related projects, who are looking to attract audiences and expand their market. We refer to them collectively as the Project/Projects.
3- Users of cyberspace and social networks, especially Twitter, who are interested in activity in the field of emerging crypto and Web3 projects, or who engage in airdrop hunting, or even regular users interested in NFTs, and ultimately, collectors.

Now we need to see how these three stakeholders interact with our platform (Social NFT).

1- Blockchain, Crypto, and Web3 Project Owners (Projects): They enter the platform through their respective pages by entering their username and password. Then, they proceed to register their information and launch a campaign.

What information do they enter? The information they enter includes the following:

1-1- Name/description and images related to the project and the campaign they want to run/link to their website and social media/campaign duration and participation conditions. This information is used in the project display and listing section.

1-2- Various parameters related to the analysis of users' Twitter profiles, and the weight or coefficient they want each parameter to have. These parameters include parameters with fixed weight (e.g., having or not having a bio in the profile/having or not having a specific text or logo in the bio and username/having or not having an avatar/having or not having a banner/categorizing the number of followers, e.g., 100 to 500, 500 to 1000, 1000 to 5000, etc./total number of posts, likes, retweets, profile comments/profile engagement rate and views/and other factors) are evaluated during the initial analysis and NFT minting, as well as parameters with variable weight (e.g., time of joining the campaign (i.e., being early or not)/number of active days during the campaign/number of interactions with project posts such as likes, comments, retweets, mentions/number of posts or content produced related to the project/quality of post content/number of referrals/and other factors). The implementation of receiving these parameters from project owners should be such that it is possible to change them and add or remove parameters.

1-3- Classification of the final score and determining the minimum final score for allocating a specific type of NFT (e.g., minimum final score for a Common NFT/minimum final score for a Rare NFT/minimum final score for a Legendary NFT).

These parameters are received through the projects' dedicated page and provided as input to the platform's profile analysis system.

1-4- Theme, genre, and overall style of the NFT (e.g., specific character, animal, regular and irregular shapes, abstract art) / Fixed and immutable aspects of the NFT (e.g., type of design/main color/project logo and branding/etc.) / The ability to select the NFT theme and style from pre-designed options. This information is obtained through the dedicated project pages and provided as input to the NFT generation system (NFT generator).

1-5- Determining the blockchain network on which the project intends to create the contract and mint the NFTs. Therefore, the platform should have the capability and capacity to support various networks (e.g., Ethereum, Binance Smart Chain, Polygon, Base, etc.) and be designed and implemented in such a way that adding and removing networks is easily possible with minimal need for changes to other parts of the platform.

2- The second beneficiary of the platform is the platform users. Users interested in participating in campaigns register and log into the site with their Twitter accounts. Their Twitter profiles are then analyzed using our platform's profile analysis system, applying fixed parameters determined by the projects (obtained through their dedicated page, as mentioned earlier). This analysis results in an overall score, and based on this score and the threshold set by the projects, users are eligible to receive one of the NFTs: Common, Rare, or Legendary. However, this is not the end, and there is no guarantee that the type of NFT minted will remain the same, except through regular activity, competition with others, and maintaining the relevant score threshold. Therefore, after minting the initial NFT, users begin to engage in activity, content creation, and interaction with the Twitter posts of the project whose NFT they minted. Through this activity, and by applying variable parameters defined in the analysis system, their overall score increases, decreases, and adjusts throughout the campaign period, consequently changing the type of NFT they hold. (For example, a profile whose initial NFT type was Common can increase its overall score during the campaign period by interacting extensively with the project's posts and creating content related to that project, thus upgrading from Common to Rare or Legendary. Conversely, a profile with a Legendary NFT can lose its overall score and be downgraded to a Common NFT if it does not engage in activity during the campaign period.) It is worth noting that as the overall score changes, so does the appearance of the NFT, which will be visible.

Through their dedicated page, accessible only by connecting their Twitter account to the platform, users can view a list of NFTs they have minted or purchased and check their status, such as appearance/type/and last update time. They will also have the option to update (re-mint on the blockchain) and list the NFT for sale on the marketplace.

Because this system is based on social networks, users can only access the platform and their dedicated page by logging in with their Twitter account or wallet, and they do not need to log in using a username and password.

The user login and authentication system, using their social network, initially and in the first phase relies on Twitter and the Twitter API. However, it must be designed and implemented in a way (e.g., modular) that allows for the addition of login and authentication through Farcaster and Lens in the future, or for them to replace Twitter.

Users do not need to create a project or specify or change an NFT theme, and will not have such an option available.

Project creation is only possible through the dedicated page of project owners or the platform admin page, which also requires initial contact with the platform and obtaining login information.

The third beneficiary is the platform managers and support staff who can access the platform through a dedicated admin page that should be role-based and have different access levels. The admin page or system should have the following capabilities and be designed and implemented in a way (e.g., modular) that allows for the development and addition of extra systems and features in the future, or the deactivation of an active system or module.

1- User Management System
2- Project Management System
3- Reporting System

The platform should have a separate support management system.

Important parts of the platform's front-end:

1- Navigation System
2- Home Page: Should include the following:
- Main banner or the hero section
- Popular Projects Section: which should be able to use various criteria such as trends/new/most number of users or NFTs minted/ total market value of minted NFTs of the project/ highest NFT trading volume compared to other projects/ etc. to filter and display projects.
3- Footer: Most links, as well as social media addresses, should be located at the bottom.
4- Projects Section: Projects should be searchable through a search field and should also have various filtering options such as: new/ active or inactive/ category/ etc.

5- Monitoring Section for Users' Final Score Status and Project Collection Market Value: This section should be designed similar to the Yaps Kaito page and include the following:

Section 1: Top of the page. Top Gainers and Top Losers System
In the left part of this section, dedicated to listing top gainers and losers, the users' Twitter profile is displayed first, followed by the final score obtained from the analyzer system after analyzing each profile. After that, the change in the final score over the past 7 days, then the past 30 days, and finally the NFT type are displayed.
In the right part, the same information for top gainers and losers is displayed as a bubble or rectangular map, along with a change chart.

Section 2: Middle of the page. System for displaying the market value of collections and their growth and decline
In this section, instead of displaying a graph of the relationship between users' Twitter profiles, we design a bubble or rectangular map with a chart (similar to the map we designed for top gainers and losers) for project collections. We display the overall value of that collection (the sum of the current prices of the NFTs minted for that project) and the amount of increase or decrease in the overall collection value as a map. Users can click on it to be directed to the dedicated page for that collection, where all the minted NFTs are displayed.

Section 3: Bottom of the page: Recent Transactions
In this section, the latest NFT purchases and sales are listed, including information on the buyer's and seller's profiles, the NFT bought or sold, the price, and the name of the collection.

Important points:
1- The entire system and its various components must be designed and implemented in a modular way so that changes or upgrades in one section do not affect the entire system and also facilitate platform implementation.
2- The profile analysis system must be accurate, advanced, and, of course, independent and modular so that it receives inputs, processes them, and provides the required output to other modules and systems.
3- The NFT generation system should also be accurate, advanced, and modular, just like the analyzer system.
4- The NFT minting system should be such that the user has the option to choose on which network to mint the NFT.
