import { <PERSON>, Get, Post, Put, Delete, Param, Body, Res, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { ListingManagementService } from '../services/listing-management.service';

@ApiTags('Listings')
@Controller('listings')
export class ListingController {
  private readonly logger = new Logger(ListingController.name);

  constructor(private readonly listingManagementService: ListingManagementService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get listing details', description: 'Get details of a specific NFT listing' })
  @ApiParam({ name: 'id', description: 'Listing ID' })
  @ApiResponse({ status: 200, description: 'Listing details retrieved successfully' })
  async getListing(@Param('id') id: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting listing details for: ${id}`);
      const result = await this.listingManagementService.getListing(id);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Listing retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update listing', description: 'Update an existing NFT listing' })
  @ApiParam({ name: 'id', description: 'Listing ID' })
  @ApiBody({
    description: 'Listing update data',
    schema: {
      type: 'object',
      properties: {
        price: { type: 'string', example: '2.0' },
        duration: { type: 'number', example: 2592000 }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Listing updated successfully' })
  async updateListing(@Param('id') id: string, @Body() updateData: any, @Res() res: Response) {
    try {
      this.logger.log(`Updating listing: ${id}`);
      const result = await this.listingManagementService.updateListing(id, updateData);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Listing update failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Cancel listing', description: 'Cancel an active NFT listing' })
  @ApiParam({ name: 'id', description: 'Listing ID' })
  @ApiResponse({ status: 200, description: 'Listing cancelled successfully' })
  async cancelListing(@Param('id') id: string, @Res() res: Response) {
    try {
      this.logger.log(`Cancelling listing: ${id}`);
      const result = await this.listingManagementService.cancelListing(id);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Listing cancellation failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }
}
