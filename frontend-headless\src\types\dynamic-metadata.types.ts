export enum MetadataFieldType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  OBJECT = 'object',
  IMAGE_URL = 'image_url',
  ANIMATION_URL = 'animation_url',
  EXTERNAL_URL = 'external_url',
  TRAIT = 'trait',
  ATTRIBUTE = 'attribute'
}

export enum MetadataStandard {
  ERC721 = 'erc721',
  ERC1155 = 'erc1155',
  OPENSEA = 'opensea',
  CUSTOM = 'custom'
}

export enum ValidationLevel {
  NONE = 'none',
  BASIC = 'basic',
  STRICT = 'strict',
  CUSTOM = 'custom'
}

export enum StorageProvider {
  IPFS = 'ipfs',
  ARWEAVE = 'arweave',
  CENTRALIZED = 'centralized',
  HYBRID = 'hybrid'
}

export interface MetadataSchema {
  id: string
  name: string
  description: string
  version: string
  
  // Schema Definition
  standard: MetadataStandard
  fields: MetadataField[]
  requiredFields: string[]
  
  // Validation
  validationLevel: ValidationLevel
  validationRules: ValidationRule[]
  
  // Versioning
  isActive: boolean
  previousVersion?: string
  migrationRules?: MigrationRule[]
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface MetadataField {
  id: string
  name: string
  displayName: string
  description: string
  
  // Type Information
  type: MetadataFieldType
  format?: string
  pattern?: string
  
  // Constraints
  required: boolean
  minLength?: number
  maxLength?: number
  minValue?: number
  maxValue?: number
  allowedValues?: any[]
  
  // Default Values
  defaultValue?: any
  computedValue?: string // Formula for computed fields
  
  // UI Configuration
  isEditable: boolean
  isVisible: boolean
  displayOrder: number
  groupName?: string
  
  // Validation
  validationRules: FieldValidationRule[]
  
  // Evolution
  isEvolvable: boolean
  evolutionRules?: EvolutionRule[]
}

export interface ValidationRule {
  id: string
  name: string
  description: string
  
  // Rule Definition
  ruleType: 'field' | 'cross_field' | 'external' | 'custom'
  condition: string
  errorMessage: string
  warningMessage?: string
  
  // Execution
  isBlocking: boolean
  priority: number
  
  // Context
  applicableStandards: MetadataStandard[]
  applicableFields?: string[]
}

export interface FieldValidationRule {
  ruleId: string
  parameters: Record<string, any>
  isActive: boolean
}

export interface MigrationRule {
  fromVersion: string
  toVersion: string
  
  // Migration Actions
  fieldMappings: FieldMapping[]
  transformations: FieldTransformation[]
  
  // Validation
  preValidation: ValidationRule[]
  postValidation: ValidationRule[]
  
  // Rollback
  isReversible: boolean
  rollbackRules?: MigrationRule[]
}

export interface FieldMapping {
  sourceField: string
  targetField: string
  mappingType: 'direct' | 'transform' | 'compute' | 'default'
  transformation?: string
}

export interface FieldTransformation {
  field: string
  transformationType: 'rename' | 'type_change' | 'format_change' | 'value_transform'
  transformationFunction: string
  parameters: Record<string, any>
}

export interface EvolutionRule {
  triggerId: string
  changeType: 'value' | 'type' | 'constraint' | 'visibility'
  changeFunction: string
  parameters: Record<string, any>
  conditions: EvolutionCondition[]
}

export interface EvolutionCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'exists'
  value: any
  weight: number
}

export interface DynamicMetadata {
  id: string
  nftId: string
  tokenId: string
  
  // Schema Information
  schemaId: string
  schemaVersion: string
  
  // Metadata Content
  data: Record<string, any>
  computedFields: Record<string, any>
  
  // Storage Information
  storageProvider: StorageProvider
  ipfsHash?: string
  arweaveId?: string
  centralizedUrl?: string
  
  // Versioning
  version: string
  previousVersion?: string
  changeLog: MetadataChangeEntry[]
  
  // Validation
  isValid: boolean
  validationErrors: ValidationError[]
  validationWarnings: ValidationWarning[]
  lastValidated: string
  
  // Evolution
  evolutionHistory: MetadataEvolutionEntry[]
  
  // Metadata
  createdAt: string
  updatedAt: string
  lastModifiedBy: string
}

export interface MetadataChangeEntry {
  id: string
  changeType: 'create' | 'update' | 'delete' | 'migrate' | 'evolve'
  
  // Change Details
  field?: string
  oldValue?: any
  newValue?: any
  changeReason: string
  
  // Context
  triggeredBy: string
  triggerType: 'manual' | 'evolution' | 'migration' | 'automation'
  
  // Validation
  wasValidated: boolean
  validationResult?: ValidationResult
  
  // Metadata
  timestamp: string
  userId: string
  transactionHash?: string
}

export interface MetadataEvolutionEntry {
  id: string
  evolutionId: string
  
  // Evolution Details
  triggerId: string
  evolutionType: string
  
  // Changes Made
  fieldsChanged: string[]
  changesSummary: Record<string, any>
  
  // Impact
  validationImpact: ValidationImpact
  storageImpact: StorageImpact
  
  // Metadata
  timestamp: string
  completedAt?: string
}

export interface ValidationError {
  field: string
  ruleId: string
  message: string
  severity: 'error' | 'warning' | 'info'
  details?: any
}

export interface ValidationWarning {
  field: string
  ruleId: string
  message: string
  suggestion?: string
  details?: any
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  score: number
  validatedAt: string
  validationDuration: number
}

export interface ValidationImpact {
  errorsIntroduced: number
  errorsResolved: number
  warningsIntroduced: number
  warningsResolved: number
  validationScoreChange: number
}

export interface StorageImpact {
  sizeChange: number
  storageProvider: StorageProvider
  newHash?: string
  gasEstimate?: number
  costEstimate?: number
}

export interface MetadataTemplate {
  id: string
  name: string
  description: string
  category: string
  
  // Template Configuration
  schemaId: string
  defaultValues: Record<string, any>
  requiredCustomizations: string[]
  
  // Usage
  usageCount: number
  rating: number
  
  // Customization
  isCustomizable: boolean
  customizationOptions: TemplateCustomization[]
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
  isPublic: boolean
  tags: string[]
}

export interface TemplateCustomization {
  field: string
  customizationType: 'value' | 'constraint' | 'visibility' | 'validation'
  options: any[]
  defaultOption: any
  isRequired: boolean
}

export interface MetadataSync {
  id: string
  nftId: string
  
  // Sync Configuration
  syncTargets: SyncTarget[]
  syncFrequency: 'manual' | 'real_time' | 'scheduled'
  syncSchedule?: string
  
  // Status
  lastSyncAt?: string
  nextSyncAt?: string
  syncStatus: 'pending' | 'syncing' | 'completed' | 'failed'
  
  // Results
  syncResults: SyncResult[]
  
  // Configuration
  conflictResolution: 'source_wins' | 'target_wins' | 'manual' | 'merge'
  
  // Metadata
  createdAt: string
  updatedAt: string
}

export interface SyncTarget {
  targetId: string
  targetType: 'blockchain' | 'marketplace' | 'storage' | 'api'
  targetUrl?: string
  
  // Configuration
  fieldMappings: Record<string, string>
  transformations: SyncTransformation[]
  
  // Authentication
  authType: 'none' | 'api_key' | 'oauth' | 'signature'
  authConfig: Record<string, any>
  
  // Status
  isActive: boolean
  lastSyncAt?: string
  syncStatus: 'pending' | 'syncing' | 'completed' | 'failed'
}

export interface SyncTransformation {
  field: string
  transformationType: 'format' | 'value' | 'type' | 'custom'
  transformationFunction: string
  parameters: Record<string, any>
}

export interface SyncResult {
  targetId: string
  syncedAt: string
  status: 'success' | 'partial' | 'failed'
  
  // Details
  fieldsSynced: string[]
  fieldsSkipped: string[]
  errors: SyncError[]
  
  // Performance
  duration: number
  dataSize: number
}

export interface SyncError {
  field: string
  error: string
  details?: any
  isRetryable: boolean
}

export interface MetadataAnalytics {
  nftId: string
  timeframe: string
  
  // Usage Metrics
  viewCount: number
  updateCount: number
  validationCount: number
  syncCount: number
  
  // Quality Metrics
  validationScore: number
  completenessScore: number
  consistencyScore: number
  
  // Evolution Metrics
  evolutionCount: number
  fieldChangeCount: number
  schemaUpgradeCount: number
  
  // Performance Metrics
  averageValidationTime: number
  averageSyncTime: number
  storageEfficiency: number
  
  // Trends
  qualityTrend: MetricTrend[]
  usageTrend: MetricTrend[]
  
  // Comparisons
  peerComparison: PeerComparison
}

export interface MetricTrend {
  date: string
  value: number
  change: number
}

export interface PeerComparison {
  averageQuality: number
  averageCompleteness: number
  averageEvolutions: number
  percentileRank: number
}

// API Request/Response Types
export interface CreateMetadataRequest {
  nftId: string
  schemaId: string
  data: Record<string, any>
  storageProvider?: StorageProvider
  validateOnCreate?: boolean
}

export interface UpdateMetadataRequest {
  data?: Record<string, any>
  schemaVersion?: string
  changeReason?: string
  validateOnUpdate?: boolean
  syncTargets?: string[]
}

export interface ValidateMetadataRequest {
  data: Record<string, any>
  schemaId: string
  validationLevel?: ValidationLevel
  customRules?: ValidationRule[]
}

export interface MigrateMetadataRequest {
  targetSchemaId: string
  targetSchemaVersion: string
  migrationRules?: MigrationRule[]
  validateAfterMigration?: boolean
}

export interface MetadataSearchRequest {
  query?: string
  filters?: {
    schemaId?: string
    schemaVersion?: string
    storageProvider?: StorageProvider
    isValid?: boolean
    hasEvolutions?: boolean
    createdAfter?: string
    createdBefore?: string
  }
  sortBy?: 'created_at' | 'updated_at' | 'validation_score' | 'evolution_count'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface MetadataSearchResponse {
  metadata: DynamicMetadata[]
  total: number
  page: number
  limit: number
  hasMore: boolean
  aggregations: {
    bySchema: Record<string, number>
    byProvider: Record<StorageProvider, number>
    byValidation: Record<string, number>
    averageQuality: number
  }
}
