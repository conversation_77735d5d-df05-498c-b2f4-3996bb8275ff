import { Injectable } from '@nestjs/common';
import { ServiceLoggerService, MetricsService } from '../../shared';

export interface CacheConfig {
  defaultTtl: number;
  maxMemorySize: number;
  enableCompression: boolean;
}

export interface CacheEntry {
  key: string;
  value: any;
  ttl: number;
  createdAt: Date;
  expiresAt: Date;
  accessCount: number;
  lastAccessed: Date;
  size: number;
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  evictionCount: number;
}

/**
 * Cache Service
 * 
 * Provides multi-level caching for API Gateway with:
 * - In-memory caching with TTL
 * - LRU eviction policy
 * - Compression support
 * - Comprehensive metrics
 * 
 * @example
 * ```typescript
 * await cache.set('user:123', userData, 300); // 5 minutes TTL
 * const user = await cache.get('user:123');
 * ```
 */
@Injectable()
export class CacheService {
  private cache = new Map<string, CacheEntry>();
  private stats: CacheStats = {
    totalEntries: 0,
    totalSize: 0,
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    evictionCount: 0,
  };
  
  private readonly config: CacheConfig = {
    defaultTtl: 300, // 5 minutes
    maxMemorySize: 100 * 1024 * 1024, // 100MB
    enableCompression: true,
  };

  private cleanupInterval?: NodeJS.Timeout;

  constructor(
    private readonly logger: ServiceLoggerService,
    private readonly metrics: MetricsService
  ) {
    this.startCleanupTimer();
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.recordMiss(key);
      return null;
    }

    // Check if expired
    if (entry.expiresAt < new Date()) {
      this.cache.delete(key);
      this.updateStats();
      this.recordMiss(key);
      return null;
    }

    // Update access stats
    entry.accessCount++;
    entry.lastAccessed = new Date();
    
    this.recordHit(key);
    
    return entry.value as T;
  }

  /**
   * Set value in cache
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const actualTtl = ttl || this.config.defaultTtl;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + actualTtl * 1000);
    
    // Calculate size (rough estimation)
    const size = this.calculateSize(value);
    
    // Check memory limit
    if (this.stats.totalSize + size > this.config.maxMemorySize) {
      await this.evictLRU(size);
    }

    const entry: CacheEntry = {
      key,
      value,
      ttl: actualTtl,
      createdAt: now,
      expiresAt,
      accessCount: 0,
      lastAccessed: now,
      size,
    };

    // Remove existing entry if present
    if (this.cache.has(key)) {
      const oldEntry = this.cache.get(key)!;
      this.stats.totalSize -= oldEntry.size;
    }

    this.cache.set(key, entry);
    this.updateStats();

    this.logger.debug(`Cache entry set`, {
      key,
      ttl: actualTtl,
      size,
      expiresAt: expiresAt.toISOString(),
    });

    this.metrics.increment('cache_operations_total', {
      operation: 'set',
      status: 'success',
    });
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<boolean> {
    const deleted = this.cache.delete(key);
    
    if (deleted) {
      this.updateStats();
      
      this.logger.debug(`Cache entry deleted`, { key });
      
      this.metrics.increment('cache_operations_total', {
        operation: 'delete',
        status: 'success',
      });
    }

    return deleted;
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    const entriesCount = this.cache.size;
    this.cache.clear();
    this.updateStats();

    this.logger.logBusinessEvent('cache', 'clear', 'SUCCESS', {
      business: {
        domain: 'cache',
        entity: 'cache-entries',
        action: 'clear',
        outcome: 'SUCCESS',
        attributes: {
          entriesCleared: entriesCount,
        },
      },
    });

    this.metrics.increment('cache_operations_total', {
      operation: 'clear',
      status: 'success',
    });
  }

  /**
   * Check if key exists in cache
   */
  async has(key: string): Promise<boolean> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    // Check if expired
    if (entry.expiresAt < new Date()) {
      this.cache.delete(key);
      this.updateStats();
      return false;
    }

    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get cache entries (for debugging)
   */
  getEntries(): CacheEntry[] {
    return Array.from(this.cache.values());
  }

  /**
   * Get cache health status
   */
  getHealthStatus(): any {
    const memoryUsagePercent = (this.stats.totalSize / this.config.maxMemorySize) * 100;
    
    return {
      status: 'healthy',
      stats: this.stats,
      config: this.config,
      memoryUsage: {
        used: this.stats.totalSize,
        max: this.config.maxMemorySize,
        usagePercent: memoryUsagePercent.toFixed(2) + '%',
      },
      timestamp: new Date().toISOString(),
    };
  }

  private recordHit(key: string): void {
    this.stats.hitCount++;
    this.updateHitRate();
    
    this.metrics.increment('cache_requests_total', {
      result: 'hit',
    });

    this.logger.debug(`Cache hit`, { key });
  }

  private recordMiss(key: string): void {
    this.stats.missCount++;
    this.updateHitRate();
    
    this.metrics.increment('cache_requests_total', {
      result: 'miss',
    });

    this.logger.debug(`Cache miss`, { key });
  }

  private updateHitRate(): void {
    const totalRequests = this.stats.hitCount + this.stats.missCount;
    this.stats.hitRate = totalRequests > 0 ? (this.stats.hitCount / totalRequests) * 100 : 0;
    
    this.metrics.gauge('cache_hit_rate', this.stats.hitRate);
  }

  private updateStats(): void {
    this.stats.totalEntries = this.cache.size;
    this.stats.totalSize = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.size, 0);
    
    this.metrics.gauge('cache_entries_total', this.stats.totalEntries);
    this.metrics.gauge('cache_memory_usage_bytes', this.stats.totalSize);
  }

  private async evictLRU(requiredSize: number): Promise<void> {
    const entries = Array.from(this.cache.values())
      .sort((a, b) => a.lastAccessed.getTime() - b.lastAccessed.getTime());

    let freedSize = 0;
    let evictedCount = 0;

    for (const entry of entries) {
      if (freedSize >= requiredSize) {
        break;
      }

      this.cache.delete(entry.key);
      freedSize += entry.size;
      evictedCount++;
      this.stats.evictionCount++;

      this.logger.debug(`Cache entry evicted (LRU)`, {
        key: entry.key,
        size: entry.size,
        lastAccessed: entry.lastAccessed.toISOString(),
      });
    }

    this.updateStats();

    this.metrics.increment('cache_evictions_total', {
      reason: 'lru',
      count: evictedCount.toString(),
    });

    this.logger.debug(`Cache LRU eviction completed`, {
      evictedCount,
      freedSize,
      requiredSize,
    });
  }

  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpired();
    }, 60000); // Run every minute
  }

  private cleanupExpired(): void {
    const now = new Date();
    let expiredCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt < now) {
        this.cache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      this.updateStats();
      
      this.logger.debug(`Cache cleanup completed`, {
        expiredCount,
        remainingEntries: this.cache.size,
      });

      this.metrics.increment('cache_evictions_total', {
        reason: 'expired',
        count: expiredCount.toString(),
      });
    }
  }

  private calculateSize(value: any): number {
    // Rough estimation of object size in bytes
    const jsonString = JSON.stringify(value);
    return new Blob([jsonString]).size;
  }
}
