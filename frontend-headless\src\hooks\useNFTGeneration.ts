import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { nftGenerationService } from '@/services/nftGenerationService'
import {
  NFTGenerationRequest,
  GenerationQueue,
  GenerationBatch,
  GenerationStats,
  CreateGenerationRequestData,
  UpdateGenerationRequestData,
  GenerationFilters,
  QueueConfiguration,
  GenerationProvider,
  ReviewData
} from '@/types/nft-generation.types'

// ===== GENERATION REQUEST HOOKS =====

export function useGenerationRequests(filters?: GenerationFilters) {
  return useQuery({
    queryKey: ['generation-requests', filters],
    queryFn: () => nftGenerationService.getGenerationRequests(filters),
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // 1 minute for real-time updates
  })
}

export function useGenerationRequest(id: string) {
  return useQuery({
    queryKey: ['generation-request', id],
    queryFn: () => nftGenerationService.getGenerationRequest(id),
    enabled: !!id,
    staleTime: 30000,
    refetchInterval: 30000, // Frequent updates for status tracking
  })
}

export function useCreateGenerationRequest() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateGenerationRequestData) => nftGenerationService.createGenerationRequest(data),
    onSuccess: (newRequest) => {
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
      queryClient.invalidateQueries({ queryKey: ['generation-stats'] })
      queryClient.setQueryData(['generation-request', newRequest.id], newRequest)
    },
    onError: (error: any) => {
      console.error('Failed to create generation request:', error)
    },
  })
}

export function useUpdateGenerationRequest() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateGenerationRequestData }) => 
      nftGenerationService.updateGenerationRequest(id, data),
    onSuccess: (updatedRequest, { id }) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to update generation request:', error)
    },
  })
}

export function useDeleteGenerationRequest() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.deleteGenerationRequest(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: ['generation-request', id] })
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete generation request:', error)
    },
  })
}

// ===== GENERATION CONTROL HOOKS =====

export function useStartGeneration() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.startGeneration(id),
    onSuccess: (updatedRequest, id) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
      queryClient.invalidateQueries({ queryKey: ['generation-stats'] })
    },
    onError: (error: any) => {
      console.error('Failed to start generation:', error)
    },
  })
}

export function useCancelGeneration() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.cancelGeneration(id),
    onSuccess: (updatedRequest, id) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to cancel generation:', error)
    },
  })
}

export function useRetryGeneration() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.retryGeneration(id),
    onSuccess: (updatedRequest, id) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to retry generation:', error)
    },
  })
}

export function useSelectImage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ requestId, imageId }: { requestId: string; imageId: string }) => 
      nftGenerationService.selectImage(requestId, imageId),
    onSuccess: (updatedRequest, { requestId }) => {
      queryClient.setQueryData(['generation-request', requestId], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to select image:', error)
    },
  })
}

// ===== REVIEW WORKFLOW HOOKS =====

export function useSubmitForReview() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.submitForReview(id),
    onSuccess: (updatedRequest, id) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to submit for review:', error)
    },
  })
}

export function useReviewGeneration() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, reviewData }: { id: string; reviewData: ReviewData }) => 
      nftGenerationService.reviewGeneration(id, reviewData),
    onSuccess: (updatedRequest, { id }) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to review generation:', error)
    },
  })
}

export function useApproveGeneration() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, feedback }: { id: string; feedback?: string }) => 
      nftGenerationService.approveGeneration(id, feedback),
    onSuccess: (updatedRequest, { id }) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to approve generation:', error)
    },
  })
}

export function useRejectGeneration() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) => 
      nftGenerationService.rejectGeneration(id, reason),
    onSuccess: (updatedRequest, { id }) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to reject generation:', error)
    },
  })
}

// ===== MINTING HOOKS =====

export function useStartMinting() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.startMinting(id),
    onSuccess: (updatedRequest, id) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to start minting:', error)
    },
  })
}

export function useMintingStatus(id: string) {
  return useQuery({
    queryKey: ['minting-status', id],
    queryFn: () => nftGenerationService.getMintingStatus(id),
    enabled: !!id,
    refetchInterval: 10000, // 10 seconds for minting updates
    staleTime: 5000,
  })
}

export function useDistributeNFT() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.distributeNFT(id),
    onSuccess: (updatedRequest, id) => {
      queryClient.setQueryData(['generation-request', id], updatedRequest)
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to distribute NFT:', error)
    },
  })
}

// ===== QUEUE MANAGEMENT HOOKS =====

export function useGenerationQueues() {
  return useQuery({
    queryKey: ['generation-queues'],
    queryFn: () => nftGenerationService.getQueues(),
    staleTime: 60000, // 1 minute
    refetchInterval: 30000, // 30 seconds
  })
}

export function useGenerationQueue(id: string) {
  return useQuery({
    queryKey: ['generation-queue', id],
    queryFn: () => nftGenerationService.getQueue(id),
    enabled: !!id,
    staleTime: 30000,
    refetchInterval: 30000,
  })
}

export function useCreateQueue() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: {
      name: string
      campaignId: string
      provider: GenerationProvider
      maxConcurrent: number
      priority: number
    }) => nftGenerationService.createQueue(data),
    onSuccess: (newQueue) => {
      queryClient.invalidateQueries({ queryKey: ['generation-queues'] })
      queryClient.setQueryData(['generation-queue', newQueue.id], newQueue)
    },
    onError: (error: any) => {
      console.error('Failed to create queue:', error)
    },
  })
}

export function usePauseQueue() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.pauseQueue(id),
    onSuccess: (updatedQueue, id) => {
      queryClient.setQueryData(['generation-queue', id], updatedQueue)
      queryClient.invalidateQueries({ queryKey: ['generation-queues'] })
    },
    onError: (error: any) => {
      console.error('Failed to pause queue:', error)
    },
  })
}

export function useResumeQueue() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.resumeQueue(id),
    onSuccess: (updatedQueue, id) => {
      queryClient.setQueryData(['generation-queue', id], updatedQueue)
      queryClient.invalidateQueries({ queryKey: ['generation-queues'] })
    },
    onError: (error: any) => {
      console.error('Failed to resume queue:', error)
    },
  })
}

// ===== BATCH OPERATIONS HOOKS =====

export function useGenerationBatches(queueId?: string) {
  return useQuery({
    queryKey: ['generation-batches', queueId],
    queryFn: () => nftGenerationService.getBatches(queueId),
    staleTime: 30000,
    refetchInterval: 30000,
  })
}

export function useCreateBatch() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: {
      queueId: string
      campaignId: string
      requestIds: string[]
      priority?: number
    }) => nftGenerationService.createBatch(data),
    onSuccess: (newBatch) => {
      queryClient.invalidateQueries({ queryKey: ['generation-batches'] })
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to create batch:', error)
    },
  })
}

export function useProcessBatch() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => nftGenerationService.processBatch(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['generation-batches'] })
      queryClient.invalidateQueries({ queryKey: ['generation-requests'] })
    },
    onError: (error: any) => {
      console.error('Failed to process batch:', error)
    },
  })
}

// ===== ANALYTICS HOOKS =====

export function useGenerationStats(filters?: {
  campaignId?: string
  timeframe?: '24h' | '7d' | '30d' | '90d'
  provider?: GenerationProvider
}) {
  return useQuery({
    queryKey: ['generation-stats', filters],
    queryFn: () => nftGenerationService.getGenerationStats(filters),
    staleTime: 300000, // 5 minutes
  })
}

export function useProviderStats() {
  return useQuery({
    queryKey: ['provider-stats'],
    queryFn: () => nftGenerationService.getProviderStats(),
    staleTime: 300000,
  })
}

export function useCampaignGenerationStats(campaignId: string) {
  return useQuery({
    queryKey: ['campaign-generation-stats', campaignId],
    queryFn: () => nftGenerationService.getCampaignGenerationStats(campaignId),
    enabled: !!campaignId,
    staleTime: 60000,
    refetchInterval: 60000,
  })
}

// ===== UTILITY HOOKS =====

export function useValidatePrompt() {
  return useMutation({
    mutationFn: (prompt: string) => nftGenerationService.validatePrompt(prompt),
    onError: (error: any) => {
      console.error('Failed to validate prompt:', error)
    },
  })
}

export function useEstimateCost() {
  return useMutation({
    mutationFn: (data: CreateGenerationRequestData) => nftGenerationService.estimateCost(data),
    onError: (error: any) => {
      console.error('Failed to estimate cost:', error)
    },
  })
}

export function useProviderCapabilities(provider: GenerationProvider) {
  return useQuery({
    queryKey: ['provider-capabilities', provider],
    queryFn: () => nftGenerationService.getProviderCapabilities(provider),
    enabled: !!provider,
    staleTime: 3600000, // 1 hour
  })
}

export function useExportGenerationData() {
  return useMutation({
    mutationFn: ({ filters, format }: { filters?: GenerationFilters; format?: 'json' | 'csv' | 'pdf' }) => 
      nftGenerationService.exportGenerationData(filters, format),
    onError: (error: any) => {
      console.error('Failed to export generation data:', error)
    },
  })
}
