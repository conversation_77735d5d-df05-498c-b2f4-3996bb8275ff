import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo() {
    return {
      name: 'Profile Analysis Service',
      version: '1.0.0',
      description: 'Industry Standard Microservices Implementation',
      architecture: 'Independent Microservice',
      dependencies: 'Service-Local Only',
      features: [
        'Twitter Profile Analysis',
        'Social Media Scoring',
        'Configurable Analysis Parameters',
        'Real-time Analysis Processing',
        'Historical Data Tracking',
        'Mock Twitter Service Support',
        'Multi-Campaign Support',
        'API Documentation',
        'Health Monitoring',
      ],
      integrations: [
        'Twitter API v2',
        'Mock Twitter Service',
        'User Service',
        'API Gateway',
      ],
      timestamp: new Date().toISOString(),
    };
  }
}
