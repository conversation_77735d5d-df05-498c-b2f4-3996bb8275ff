# 🚀 **SHARED INFRASTRUCTURE IMPLEMENTATION GUIDE**

## **📋 OVERVIEW**

This guide provides step-by-step instructions for implementing the Shared Infrastructure in your microservices. The shared infrastructure implements the **Microservice Chassis Pattern** - an industry-standard approach used by Netflix, Uber, Google, and Amazon.

---

## 🎯 **QUICK START**

### **Step 1: Install Shared Infrastructure**

In your service directory:
```bash
# Install the shared infrastructure
npm install ../shared

# Install peer dependencies
npm install @nestjs/common @nestjs/core @nestjs/config @nestjs/jwt @prisma/client winston
```

### **Step 2: Update Your App Module**

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { setupBusinessService } from '../../../shared';
import { UsersModule } from './users/users.module';

@Module({
  imports: [
    // Setup shared infrastructure for business service
    setupBusinessService('user-service', '1.0.0'),
    
    // Your business modules
    UsersModule,
  ],
})
export class AppModule {}
```

### **Step 3: Update Your Controllers**

```typescript
// src/users/users.controller.ts
import { Controller, Get, Post, Body, Param } from '@nestjs/common';
import { 
  ResponseService, 
  RequirePermissions, 
  Permission, 
  CurrentUser, 
  AuthenticatedUser 
} from '../../../../shared';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';

@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly responseService: ResponseService
  ) {}

  @Get()
  @RequirePermissions(Permission.USER_READ)
  async findAll(@CurrentUser() user: AuthenticatedUser) {
    const users = await this.usersService.findAll();
    return this.responseService.success(users, 'Users retrieved successfully');
  }

  @Post()
  @RequirePermissions(Permission.USER_CREATE)
  async create(@Body() createUserDto: CreateUserDto) {
    const user = await this.usersService.create(createUserDto);
    return this.responseService.created(user, 'User created successfully');
  }

  @Get(':id')
  @RequirePermissions(Permission.USER_READ)
  async findOne(@Param('id') id: string) {
    const user = await this.usersService.findOne(id);
    if (!user) {
      return this.responseService.notFound('User', id);
    }
    return this.responseService.success(user);
  }
}
```

### **Step 4: Update Your Services**

```typescript
// src/users/users.service.ts
import { Injectable } from '@nestjs/common';
import { 
  StandardizedPrismaService, 
  ServiceLoggerService, 
  BusinessOutcome 
} from '../../../../shared';
import { CreateUserDto } from './dto/create-user.dto';

@Injectable()
export class UsersService {
  constructor(
    private readonly prisma: StandardizedPrismaService,
    private readonly logger: ServiceLoggerService
  ) {}

  async findAll() {
    return this.prisma.executeWithMetrics('findAllUsers', async () => {
      const users = await this.prisma.user.findMany();
      
      this.logger.logBusinessEvent(
        'user',
        'list',
        BusinessOutcome.SUCCESS,
        { metadata: { count: users.length } }
      );
      
      return users;
    });
  }

  async create(createUserDto: CreateUserDto) {
    return this.prisma.executeWithMetrics('createUser', async () => {
      const user = await this.prisma.user.create({
        data: createUserDto,
      });
      
      this.logger.logBusinessEvent(
        'user',
        'create',
        BusinessOutcome.SUCCESS,
        { 
          business: {
            entity: 'user',
            entityId: user.id,
            attributes: { email: user.email }
          }
        }
      );
      
      return user;
    });
  }

  async findOne(id: string) {
    return this.prisma.executeWithMetrics('findUserById', async () => {
      return this.prisma.user.findUnique({
        where: { id },
      });
    });
  }
}
```

---

## 🔧 **CONFIGURATION**

### **Environment Variables**

Create a `.env` file in your service root:

```bash
# Service Configuration
SERVICE_NAME=user-service
SERVICE_PORT=3001
NODE_ENV=development
LOG_LEVEL=info

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/user_service

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3010
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100

# Monitoring Configuration
ENABLE_METRICS=true
ENABLE_TRACING=true
ENABLE_HEALTH_CHECKS=true
```

### **Custom Configuration**

```typescript
// src/config/app.config.ts
import { StandardizedConfigService } from '../../../shared';

export const createCustomConfig = (configService: StandardizedConfigService) => ({
  // Service-specific configuration
  features: {
    enableEmailVerification: configService.get<boolean>('ENABLE_EMAIL_VERIFICATION', true),
    enableSocialLogin: configService.get<boolean>('ENABLE_SOCIAL_LOGIN', false),
  },
  
  // External services
  emailService: {
    apiKey: configService.get<string>('EMAIL_API_KEY'),
    fromAddress: configService.get<string>('EMAIL_FROM_ADDRESS'),
  },
});
```

---

## 📊 **DIFFERENT SERVICE TYPES**

### **API Gateway Service**

```typescript
// api-gateway/src/app.module.ts
import { Module } from '@nestjs/common';
import { setupAPIGateway } from '../../../shared';

@Module({
  imports: [
    setupAPIGateway('api-gateway', '1.0.0'),
    // Gateway-specific modules
  ],
})
export class AppModule {}
```

### **Business Service (with Database)**

```typescript
// user-service/src/app.module.ts
import { Module } from '@nestjs/common';
import { setupBusinessService } from '../../../shared';
import { AuthService } from './auth/auth.service';

@Module({
  imports: [
    setupBusinessService('user-service', '1.0.0', AuthService),
    // Business modules
  ],
})
export class AppModule {}
```

### **Utility Service (Minimal)**

```typescript
// notification-service/src/app.module.ts
import { Module } from '@nestjs/common';
import { setupUtilityService } from '../../../shared';

@Module({
  imports: [
    setupUtilityService('notification-service', '1.0.0'),
    // Utility modules
  ],
})
export class AppModule {}
```

---

## 🧪 **TESTING SETUP**

### **Test Module Setup**

```typescript
// src/users/users.controller.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { setupTesting } from '../../../../shared';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';

describe('UsersController', () => {
  let controller: UsersController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [setupTesting('user-service-test')],
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: {
            findAll: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
```

---

## 🔍 **MONITORING AND LOGGING**

### **Health Check Endpoint**

The shared infrastructure automatically provides health check endpoints:

```bash
# Simple health check
GET /health/simple

# Detailed health check
GET /health/detailed

# Database health check
GET /health/database
```

### **Structured Logging**

```typescript
// Automatic request/response logging
// Business event logging
this.logger.logBusinessEvent('user', 'create', BusinessOutcome.SUCCESS);

// Security event logging
this.logger.logSecurityEvent(
  SecurityEventType.AUTHENTICATION,
  SecuritySeverity.MEDIUM,
  'Failed login attempt'
);

// Performance logging
this.logger.logPerformanceMetric('user-creation', 150, true);
```

### **Correlation ID Tracking**

All requests automatically get correlation IDs for distributed tracing:

```typescript
// Access correlation ID in controllers
@Get()
async findAll(@Headers('x-correlation-id') correlationId: string) {
  // Use correlationId for tracking across services
}
```

---

## 🔒 **AUTHENTICATION & AUTHORIZATION**

### **Protected Endpoints**

```typescript
@Controller('admin')
export class AdminController {
  @Get('users')
  @RequirePermissions(Permission.ADMIN)
  async getUsers() {
    // Only admins can access
  }

  @Post('settings')
  @Roles(Role.SUPER_ADMIN)
  async updateSettings() {
    // Only super admins can access
  }
}
```

### **Public Endpoints**

```typescript
@Controller('public')
export class PublicController {
  @Get('status')
  @Public()
  async getStatus() {
    // No authentication required
  }
}
```

---

## 📈 **PERFORMANCE OPTIMIZATION**

### **Database Query Optimization**

```typescript
// Automatic query performance tracking
async findUsersWithPosts() {
  return this.prisma.executeWithMetrics('findUsersWithPosts', async () => {
    return this.prisma.user.findMany({
      include: { posts: true },
    });
  });
}
```

### **Response Caching**

```typescript
// Cache responses for better performance
@Get('popular-posts')
@CacheKey('popular-posts')
@CacheTTL(300) // 5 minutes
async getPopularPosts() {
  // Cached automatically
}
```

---

## 🚨 **ERROR HANDLING**

### **Automatic Error Handling**

```typescript
// Errors are automatically caught and formatted
@Post()
async create(@Body() dto: CreateUserDto) {
  // If this throws, it's automatically handled
  const user = await this.usersService.create(dto);
  return this.responseService.created(user);
}
```

### **Custom Error Responses**

```typescript
@Get(':id')
async findOne(@Param('id') id: string) {
  const user = await this.usersService.findOne(id);
  
  if (!user) {
    // Custom error response
    return this.responseService.notFound('User', id);
  }
  
  return this.responseService.success(user);
}
```

---

**🎯 Following this implementation guide ensures consistent, enterprise-grade microservice development!**
