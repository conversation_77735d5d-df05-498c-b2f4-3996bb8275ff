export interface RequestContext {
  userId?: string;
  sessionId?: string;
  correlationId: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp?: Date;
  requestId?: string;
}

export interface PerformanceMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  operation: string;
  success: boolean;
  errorMessage?: string;
}

export interface BusinessRuleContext {
  operation: string;
  entityType: string;
  entityId?: string;
  userId?: string;
  data?: any;
}
