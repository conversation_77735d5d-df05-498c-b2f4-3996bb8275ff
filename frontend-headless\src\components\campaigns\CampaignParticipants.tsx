'use client'

import React, { useState } from 'react'
import {
  UsersIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  UserCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { useCampaignParticipants } from '@/hooks/useCampaigns'
import { ParticipationStatus } from '@/types/campaign.types'

interface CampaignParticipantsProps {
  campaignId: string
  className?: string
}

export default function CampaignParticipants({ campaignId, className = '' }: CampaignParticipantsProps) {
  const [filters, setFilters] = useState({
    search: '',
    status: undefined as ParticipationStatus | undefined,
    page: 1,
    limit: 20
  })

  const { data: participantsData, isLoading } = useCampaignParticipants(campaignId, filters)

  const participants = participantsData?.participants || []
  const total = participantsData?.total || 0

  const getStatusColor = (status: ParticipationStatus) => {
    switch (status) {
      case ParticipationStatus.ACTIVE: return 'text-green-600 bg-green-100'
      case ParticipationStatus.COMPLETED: return 'text-blue-600 bg-blue-100'
      case ParticipationStatus.PENDING: return 'text-yellow-600 bg-yellow-100'
      case ParticipationStatus.DISQUALIFIED: return 'text-red-600 bg-red-100'
      case ParticipationStatus.WITHDRAWN: return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: ParticipationStatus) => {
    switch (status) {
      case ParticipationStatus.ACTIVE: return <ClockIcon className="h-4 w-4" />
      case ParticipationStatus.COMPLETED: return <CheckCircleIcon className="h-4 w-4" />
      case ParticipationStatus.PENDING: return <ClockIcon className="h-4 w-4" />
      case ParticipationStatus.DISQUALIFIED: return <XCircleIcon className="h-4 w-4" />
      case ParticipationStatus.WITHDRAWN: return <XCircleIcon className="h-4 w-4" />
      default: return <UserCircleIcon className="h-4 w-4" />
    }
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Campaign Participants</h3>
          <p className="text-sm text-gray-600">{total} total participants</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            placeholder="Search participants..."
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <select
          value={filters.status || ''}
          onChange={(e) => setFilters(prev => ({ 
            ...prev, 
            status: e.target.value ? e.target.value as ParticipationStatus : undefined 
          }))}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">All Statuses</option>
          <option value={ParticipationStatus.ACTIVE}>Active</option>
          <option value={ParticipationStatus.COMPLETED}>Completed</option>
          <option value={ParticipationStatus.PENDING}>Pending</option>
          <option value={ParticipationStatus.DISQUALIFIED}>Disqualified</option>
          <option value={ParticipationStatus.WITHDRAWN}>Withdrawn</option>
        </select>
      </div>

      {/* Participants List */}
      {participants.length > 0 ? (
        <div className="space-y-3">
          {participants.map((participant) => (
            <div
              key={participant.id}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {participant.user?.avatar ? (
                    <img
                      src={participant.user.avatar}
                      alt={participant.user.displayName}
                      className="w-10 h-10 rounded-full"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <UserCircleIcon className="h-6 w-6 text-gray-400" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {participant.user?.displayName || participant.user?.username || 'Anonymous'}
                    </h4>
                    {participant.user?.twitterHandle && (
                      <span className="text-sm text-gray-500">
                        @{participant.user.twitterHandle}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>Joined: {new Date(participant.joinedAt).toLocaleDateString()}</span>
                    <span>Points: {participant.totalPoints}</span>
                    <span>Tasks: {participant.completedRequirements.length}</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(participant.status)}`}>
                  {getStatusIcon(participant.status)}
                  <span className="ml-1">{participant.status}</span>
                </span>

                <button
                  className="p-2 text-gray-400 hover:text-gray-600"
                  title="View Details"
                >
                  <EyeIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No participants found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {filters.search || filters.status
              ? 'Try adjusting your filters to see more participants.'
              : 'No one has joined this campaign yet.'}
          </p>
        </div>
      )}

      {/* Pagination */}
      {total > filters.limit && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((filters.page - 1) * filters.limit) + 1} to {Math.min(filters.page * filters.limit, total)} of {total} participants
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setFilters(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
              disabled={filters.page === 1}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Previous
            </button>
            <span className="px-3 py-2 text-sm text-gray-700">
              Page {filters.page} of {Math.ceil(total / filters.limit)}
            </span>
            <button
              onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={filters.page >= Math.ceil(total / filters.limit)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
