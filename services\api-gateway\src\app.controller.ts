import { <PERSON>, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('Gateway')
@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Get API Gateway information' })
  @ApiResponse({ 
    status: 200, 
    description: 'API Gateway information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        service: { type: 'string', example: 'API Gateway' },
        version: { type: 'string', example: '1.0.0' },
        status: { type: 'string', example: 'running' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        uptime: { type: 'number', example: 12345 },
        environment: { type: 'string', example: 'development' },
        services: {
          type: 'object',
          properties: {
            user: { type: 'string', example: 'http://localhost:3001' },
            project: { type: 'string', example: 'http://localhost:3002' },
            nft: { type: 'string', example: 'http://localhost:3003' },
            blockchain: { type: 'string', example: 'http://localhost:3004' },
            marketplace: { type: 'string', example: 'http://localhost:3005' },
            analytics: { type: 'string', example: 'http://localhost:3006' },
            profile: { type: 'string', example: 'http://localhost:3007' }
          }
        }
      }
    }
  })
  getGatewayInfo() {
    this.logger.log('Gateway info requested');
    return this.appService.getGatewayInfo();
  }

  @Get('status')
  @ApiOperation({ summary: 'Get API Gateway status' })
  @ApiResponse({ 
    status: 200, 
    description: 'Gateway status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        uptime: { type: 'number', example: 12345 },
        memory: {
          type: 'object',
          properties: {
            used: { type: 'number', example: 50000000 },
            total: { type: 'number', example: 100000000 }
          }
        }
      }
    }
  })
  getStatus() {
    this.logger.log('Gateway status requested');
    return this.appService.getStatus();
  }
}
