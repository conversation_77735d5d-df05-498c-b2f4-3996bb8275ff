# Social NFT Platform - Deployment Guide

## 🚀 Production Deployment Instructions

### **Prerequisites**
- Docker & Docker Compose installed
- PostgreSQL 13+ (production instance)
- Node.js 18+ for local development
- Load balancer (nginx/AWS ALB)

### **Environment Configuration**

#### **Production Environment Variables**
Create `.env.production` for each service:

```bash
# Database (Production)
DB_HOST=production-postgres-host
DB_PORT=5432
DB_USERNAME=postgres_user
DB_PASSWORD=secure_password
DB_DATABASE=service_database_name

# Security
NODE_ENV=production
JWT_SECRET=production-jwt-secret-key
API_RATE_LIMIT=1000

# External Services
TWITTER_API_KEY=production-twitter-key
SENDGRID_API_KEY=production-sendgrid-key
FIREBASE_PROJECT_ID=production-firebase-id
```

### **Docker Deployment**

#### **Create Dockerfile for each service:**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001
CMD ["npm", "run", "start:prod"]
```

#### **Docker Compose Configuration:**
```yaml
version: '3.8'
services:
  user-service:
    build: ./services/user-service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    depends_on:
      - postgres

  # Repeat for all 9 services...

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: social_nft_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### **Kubernetes Deployment**

#### **Service Deployment Example:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: social-nft/user-service:latest
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
spec:
  selector:
    app: user-service
  ports:
  - port: 3001
    targetPort: 3001
```

### **Load Balancer Configuration**

#### **Nginx Configuration:**
```nginx
upstream api_gateway {
    server localhost:3010;
}

upstream user_service {
    server localhost:3001;
}

# Repeat for all services...

server {
    listen 80;
    server_name api.socialnft.com;

    location /api/ {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### **Database Setup**

#### **Production Database Creation:**
```sql
-- Create databases for each service
CREATE DATABASE social_nft_users;
CREATE DATABASE profile_analysis_db;
CREATE DATABASE nft_generation_db;
CREATE DATABASE blockchain_service_db;
CREATE DATABASE project_service_db;
CREATE DATABASE marketplace_service_db;
CREATE DATABASE notification_service_db;
CREATE DATABASE analytics_service_db;

-- Create dedicated users
CREATE USER social_nft_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE social_nft_users TO social_nft_user;
-- Repeat for all databases...
```

### **Monitoring & Logging**

#### **Health Check Endpoints:**
```bash
# Production health checks
curl https://api.socialnft.com/api/health
curl https://user.socialnft.com/health
curl https://analytics.socialnft.com/api/health
```

#### **Log Aggregation:**
- **ELK Stack** for centralized logging
- **Prometheus** for metrics collection
- **Grafana** for monitoring dashboards

### **Security Configuration**

#### **SSL/TLS Setup:**
```bash
# Let's Encrypt SSL certificates
certbot --nginx -d api.socialnft.com
certbot --nginx -d user.socialnft.com
```

#### **Firewall Rules:**
```bash
# Allow only necessary ports
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 3001:3010/tcp  # Block direct service access
```

### **Backup Strategy**

#### **Database Backups:**
```bash
# Daily automated backups
pg_dump -h production-host -U postgres social_nft_users > backup_$(date +%Y%m%d).sql
```

#### **Application Backups:**
- **Code:** Git repository with tags
- **Config:** Environment variables in secure vault
- **Data:** Daily database dumps to S3/cloud storage

### **Deployment Checklist**

#### **Pre-Deployment:**
- [ ] All services tested locally
- [ ] Environment variables configured
- [ ] Database migrations completed
- [ ] SSL certificates installed
- [ ] Load balancer configured

#### **Post-Deployment:**
- [ ] Health checks passing
- [ ] Monitoring alerts configured
- [ ] Backup systems operational
- [ ] Performance testing completed
- [ ] Security audit passed

### **Rollback Procedure**

#### **Quick Rollback:**
```bash
# Docker rollback
docker-compose down
docker-compose up -d --scale user-service=0
docker-compose up -d

# Kubernetes rollback
kubectl rollout undo deployment/user-service
```

---

**Production deployment ready for Social NFT Platform** 🚀
