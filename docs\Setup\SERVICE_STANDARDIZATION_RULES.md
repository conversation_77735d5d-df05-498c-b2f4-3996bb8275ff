# SERVICE STANDARDIZATION RULES
*Comprehensive standards derived from real issues and fixes across the Social NFT Platform*

## 📋 **DOCUMENT PURPOSE**
This document captures **every configuration issue, routing problem, and integration fix** discovered during development to ensure:
- ✅ **Consistent service architecture** across all microservices
- ✅ **Standardized API Gateway routing** patterns
- ✅ **Unified health check implementations**
- ✅ **Predictable endpoint structures**
- ✅ **Reliable integration patterns**

---

## 🔧 **API GATEWAY ROUTING STANDARDS**

### **ISSUE DISCOVERED: Inconsistent Service Endpoint Paths**
**Problem:** API Gateway controllers forwarding to incorrect service endpoints
**Example:** User Service endpoints at `/api/auth/*` but API Gateway forwarding to `/auth/*`

### **STANDARDIZATION RULE: API-GW-001**
```typescript
// ✅ CORRECT: Always include /api prefix when forwarding to services
const response = await this.proxyService.forwardRequest(
  'user-service',
  '/api/auth/register',  // ✅ Include /api prefix
  'POST',
  registerDto,
  headers
);

// ❌ INCORRECT: Missing /api prefix
const response = await this.proxyService.forwardRequest(
  'user-service',
  '/auth/register',      // ❌ Missing /api prefix
  'POST',
  registerDto,
  headers
);
```

### **STANDARDIZATION RULE: API-GW-002**
**All API Gateway controllers MUST include health endpoints:**
```typescript
@Get('health')
@ApiOperation({ summary: 'Health check for [service-name]' })
@ApiResponse({ status: 200, description: 'Service is healthy' })
async healthCheck(@Headers() headers: any, @Res() res: Response) {
  try {
    const response = await this.proxyService.forwardRequest(
      '[service-name]',
      '/api/health',
      'GET',
      null,
      headers
    );

    return res.status(response.status).json({
      success: true,
      data: {
        ...response.data,
        gateway: 'api-gateway',
        timestamp: new Date().toISOString(),
      }
    });
  } catch (error) {
    return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
      success: false,
      error: {
        message: '[Service-name] unavailable',
        timestamp: new Date().toISOString(),
      }
    });
  }
}
```

---

## 🏥 **HEALTH CHECK STANDARDS**

### **ISSUE DISCOVERED: Inconsistent Health Endpoint Paths**
**Problem:** Services using different health endpoint paths (`/health` vs `/api/health`)

### **STANDARDIZATION RULE: HEALTH-001**
**All services MUST implement health checks at `/api/health`:**
```typescript
// ✅ STANDARD HEALTH ENDPOINT
@Get('health')
@ApiOperation({ summary: 'Service health check' })
@ApiResponse({ status: 200, description: 'Service is healthy' })
async healthCheck(): Promise<any> {
  return {
    status: 'ok',
    service: '[service-name]',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    uptime: process.uptime(),
    port: process.env.PORT || '[default-port]',
    environment: process.env.NODE_ENV || 'development'
  };
}
```

### **STANDARDIZATION RULE: HEALTH-002**
**Health responses MUST follow standard format:**
```json
{
  "status": "ok",
  "service": "service-name",
  "timestamp": "2025-06-06T12:00:00.000Z",
  "version": "1.0.0",
  "uptime": 1234.567,
  "port": "3011",
  "environment": "development"
}
```

---

## 🌐 **PORT CONFIGURATION STANDARDS**

### **ISSUE DISCOVERED: Documentation vs Actual Port Mismatches**
**Problem:** Service documentation showing incorrect ports vs actual .env configuration

### **STANDARDIZATION RULE: PORT-001**
**Port assignments MUST match .env configuration:**
```bash
# ✅ STANDARD PORT ASSIGNMENTS (from .env)
USER_SERVICE_PORT=3011
PROFILE_ANALYSIS_SERVICE_PORT=3002
NFT_GENERATION_SERVICE_PORT=3003
API_GATEWAY_PORT=3010
MOCK_TWITTER_SERVICE_PORT=3020
FRONTEND_PORT=3000
```

### **STANDARDIZATION RULE: PORT-002**
**All documentation MUST reference .env for port numbers:**
```markdown
❌ INCORRECT: "User Service runs on port 3001"
✅ CORRECT: "User Service runs on port 3011 (see .env configuration)"
```

---

## 🔗 **ENDPOINT STRUCTURE STANDARDS**

### **ISSUE DISCOVERED: Inconsistent User-Specific Endpoint Patterns**
**Problem:** Different services using different patterns for user-specific data

### **STANDARDIZATION RULE: ENDPOINT-001**
**User-specific endpoints MUST follow standard patterns:**
```typescript
// ✅ STANDARD USER ENDPOINT PATTERNS
@Get('user/:userId')           // Get user-specific data
@Get('user/:userId/history')   // Get user history/activity
@Post('user/:userId/action')   // Perform user-specific action

// ❌ AVOID INCONSISTENT PATTERNS
@Get(':userId/user')           // ❌ Wrong order
@Get('users/:userId')          // ❌ Plural confusion
@Get('user-data/:userId')      // ❌ Non-standard naming
```

### **STANDARDIZATION RULE: ENDPOINT-002**
**All endpoints MUST include proper API documentation:**
```typescript
@Get('user/:userId/nfts')
@ApiOperation({ summary: 'Get NFTs owned by specific user' })
@ApiParam({ name: 'userId', description: 'User ID' })
@ApiResponse({ status: 200, description: 'User NFTs retrieved successfully' })
@ApiResponse({ status: 404, description: 'User not found' })
async getUserNFTs(@Param('userId') userId: string) {
  // Implementation
}
```

---

## 🧪 **TESTING STANDARDS**

### **ISSUE DISCOVERED: Integration Tests Not Following Platform Structure**
**Problem:** Tests created in wrong directories, not following libs/testing structure

### **STANDARDIZATION RULE: TEST-001**
**All integration tests MUST be placed in `libs/testing/integration/`:**
```
✅ CORRECT STRUCTURE:
libs/testing/
├── integration/
│   ├── nft-generation-integration-test.js
│   ├── user-service-integration-test.js
│   └── comprehensive-integration-test.js
├── unit/
└── e2e/
```

### **STANDARDIZATION RULE: TEST-002**
**Integration tests MUST follow established patterns:**
```javascript
// ✅ STANDARD INTEGRATION TEST STRUCTURE
class ServiceIntegrationTest {
  constructor() {
    this.testResults = [];
    this.authToken = null;
    this.userId = null;
  }

  async runAllTests() {
    try {
      await this.testServiceHealth();
      await this.testAuthentication();
      await this.testCoreEndpoints();
      this.printSummary();
    } catch (error) {
      console.error('Test suite failed:', error.message);
    }
  }

  logSuccess(testName, data) {
    console.log(`✅ ${testName}:`, data);
    this.testResults.push({ test: testName, status: 'PASS', data });
  }

  logError(testName, message) {
    console.log(`❌ ${testName}:`, message);
    this.testResults.push({ test: testName, status: 'FAIL', message });
  }
}
```

---

## 📊 **RESPONSE FORMAT STANDARDS**

### **ISSUE DISCOVERED: Inconsistent API Response Formats**
**Problem:** Different services returning different response structures

### **STANDARDIZATION RULE: RESPONSE-001**
**All API responses MUST follow standard format:**
```json
{
  "success": true,
  "data": {
    // Actual response data
  },
  "message": "Optional success message",
  "correlationId": "unique-request-id",
  "timestamp": "2025-06-06T12:00:00.000Z"
}
```

### **STANDARDIZATION RULE: RESPONSE-002**
**Error responses MUST follow standard format:**
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": "Additional error details"
  },
  "service": "service-name",
  "timestamp": "2025-06-06T12:00:00.000Z"
}
```

---

## 🔄 **INTEGRATION PATTERNS**

### **ISSUE DISCOVERED: Inconsistent Service Communication**
**Problem:** Some services communicating directly instead of through API Gateway

### **STANDARDIZATION RULE: INTEGRATION-001**
**All external service communication MUST go through API Gateway:**
```typescript
// ✅ CORRECT: Route through API Gateway
const nftResponse = await axios.get(`${API_GATEWAY_URL}/api/nfts`);

// ❌ INCORRECT: Direct service communication
const nftResponse = await axios.get('http://localhost:3003/api/nfts');
```

### **STANDARDIZATION RULE: INTEGRATION-002**
**Service discovery MUST use environment variables:**
```typescript
// ✅ CORRECT: Use environment configuration
const API_GATEWAY_URL = process.env.API_GATEWAY_URL || 'http://localhost:3010';

// ❌ INCORRECT: Hardcoded URLs
const API_GATEWAY_URL = 'http://localhost:3010';
```

---

## 📝 **DOCUMENTATION STANDARDS**

### **ISSUE DISCOVERED: Outdated Documentation**
**Problem:** Documentation showing incorrect ports and endpoints

### **STANDARDIZATION RULE: DOC-001**
**All documentation MUST be updated when configurations change:**
```markdown
✅ CORRECT: Always reference current .env configuration
❌ INCORRECT: Hardcoded values that may become outdated
```

### **STANDARDIZATION RULE: DOC-002**
**Service documentation MUST include:**
- Current port from .env
- All available endpoints
- Health check endpoint
- Integration patterns
- Example requests/responses

---

## 🚀 **DEPLOYMENT STANDARDS**

### **STANDARDIZATION RULE: DEPLOY-001**
**Service startup order MUST follow dependencies:**
```bash
# ✅ CORRECT STARTUP ORDER
1. Core Services (User, Profile Analysis, NFT Generation)
2. Mock Services (Mock Twitter)
3. API Gateway
4. Frontend
```

### **STANDARDIZATION RULE: DEPLOY-002**
**All services MUST have health checks before marking as ready:**
```bash
# ✅ HEALTH CHECK VERIFICATION
curl -s http://localhost:3011/api/health  # User Service
curl -s http://localhost:3002/api/health  # Profile Analysis
curl -s http://localhost:3003/api/health  # NFT Generation
curl -s http://localhost:3020/health      # Mock Twitter
curl -s http://localhost:3010/api/health  # API Gateway
```

---

## 📋 **IMPLEMENTATION CHECKLIST**

### **For New Services:**
- [ ] Health endpoint at `/api/health` with standard format
- [ ] Port configuration in .env
- [ ] API Gateway controller with health endpoint
- [ ] Standard response formats
- [ ] Integration tests in `libs/testing/integration/`
- [ ] Documentation with current configuration

### **For Existing Services:**
- [ ] Verify health endpoint paths
- [ ] Update API Gateway routing
- [ ] Standardize response formats
- [ ] Update documentation
- [ ] Add missing integration tests

---

## 🔍 **VALIDATION COMMANDS**

### **Quick Health Check All Services:**
```bash
echo "=== SERVICE HEALTH VALIDATION ===" && \
curl -s http://localhost:3011/api/health | jq '.service' && \
curl -s http://localhost:3002/api/health | jq '.service' && \
curl -s http://localhost:3003/api/health | jq '.service' && \
curl -s http://localhost:3020/health | jq '.service' && \
curl -s http://localhost:3010/api/health | jq '.service'
```

### **API Gateway Routing Validation:**
```bash
echo "=== API GATEWAY ROUTING VALIDATION ===" && \
curl -s http://localhost:3010/api/users/health && \
curl -s http://localhost:3010/api/nft-generation/health && \
curl -s http://localhost:3010/api/nfts
```

---

## 📊 **METRICS & MONITORING**

### **STANDARDIZATION RULE: MONITOR-001**
**All services MUST include performance metrics in health checks:**
```json
{
  "status": "ok",
  "service": "service-name",
  "uptime": 1234.567,
  "memory": {
    "used": "45.2 MB",
    "total": "512 MB"
  },
  "requests": {
    "total": 1500,
    "errors": 3,
    "avgResponseTime": "120ms"
  }
}
```

---

## 🎯 **NEXT STEPS**

1. **Apply to All Services** - Systematically update each service
2. **Create Templates** - Generate service templates following these rules
3. **Automated Validation** - Create scripts to verify compliance
4. **Continuous Updates** - Add new rules as issues are discovered

---

*This document is a living standard - update it every time a new issue is discovered and fixed.*
