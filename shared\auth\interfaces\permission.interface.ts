/**
 * Permission and Role Interfaces
 * Defines the standard RBAC structure for all services
 */

/**
 * System permissions enumeration
 */
export enum Permission {
  // ===== USER MANAGEMENT =====
  USER_READ = 'user:read',
  USER_WRITE = 'user:write',
  USER_DELETE = 'user:delete',
  USER_ADMIN = 'user:admin',
  USER_PROFILE_READ = 'user:profile:read',
  USER_PROFILE_WRITE = 'user:profile:write',
  
  // ===== NFT MANAGEMENT =====
  NFT_READ = 'nft:read',
  NFT_WRITE = 'nft:write',
  NFT_DELETE = 'nft:delete',
  NFT_ADMIN = 'nft:admin',
  NFT_MINT = 'nft:mint',
  NFT_TRANSFER = 'nft:transfer',
  NFT_BURN = 'nft:burn',
  
  // ===== PROJECT MANAGEMENT =====
  PROJECT_READ = 'project:read',
  PROJECT_WRITE = 'project:write',
  PROJECT_DELETE = 'project:delete',
  PROJECT_ADMIN = 'project:admin',
  PROJECT_PUBLISH = 'project:publish',
  PROJECT_MODERATE = 'project:moderate',
  
  // ===== MARKETPLACE =====
  MARKETPLACE_READ = 'marketplace:read',
  MARKETPLACE_WRITE = 'marketplace:write',
  MARKETPLACE_ADMIN = 'marketplace:admin',
  MARKETPLACE_LIST = 'marketplace:list',
  MARKETPLACE_BUY = 'marketplace:buy',
  MARKETPLACE_SELL = 'marketplace:sell',
  MARKETPLACE_MODERATE = 'marketplace:moderate',
  
  // ===== BLOCKCHAIN =====
  BLOCKCHAIN_READ = 'blockchain:read',
  BLOCKCHAIN_WRITE = 'blockchain:write',
  BLOCKCHAIN_ADMIN = 'blockchain:admin',
  BLOCKCHAIN_DEPLOY = 'blockchain:deploy',
  BLOCKCHAIN_INTERACT = 'blockchain:interact',
  
  // ===== ANALYTICS =====
  ANALYTICS_READ = 'analytics:read',
  ANALYTICS_WRITE = 'analytics:write',
  ANALYTICS_ADMIN = 'analytics:admin',
  ANALYTICS_EXPORT = 'analytics:export',
  
  // ===== NOTIFICATIONS =====
  NOTIFICATION_READ = 'notification:read',
  NOTIFICATION_WRITE = 'notification:write',
  NOTIFICATION_ADMIN = 'notification:admin',
  NOTIFICATION_SEND = 'notification:send',
  
  // ===== PROFILE ANALYSIS =====
  PROFILE_ANALYSIS_READ = 'profile_analysis:read',
  PROFILE_ANALYSIS_WRITE = 'profile_analysis:write',
  PROFILE_ANALYSIS_ADMIN = 'profile_analysis:admin',
  PROFILE_ANALYSIS_ANALYZE = 'profile_analysis:analyze',
  
  // ===== SYSTEM ADMINISTRATION =====
  SYSTEM_READ = 'system:read',
  SYSTEM_WRITE = 'system:write',
  SYSTEM_ADMIN = 'system:admin',
  SYSTEM_CONFIG = 'system:config',
  SYSTEM_MONITOR = 'system:monitor',
  
  // ===== AUDIT AND COMPLIANCE =====
  AUDIT_READ = 'audit:read',
  AUDIT_WRITE = 'audit:write',
  AUDIT_ADMIN = 'audit:admin',
  AUDIT_EXPORT = 'audit:export',
  
  // ===== API ACCESS =====
  API_READ = 'api:read',
  API_WRITE = 'api:write',
  API_ADMIN = 'api:admin',
  API_KEY_MANAGE = 'api:key:manage',
}

/**
 * System roles enumeration
 */
export enum Role {
  // Basic roles
  GUEST = 'guest',
  USER = 'user',
  
  // Creator roles
  CREATOR = 'creator',
  VERIFIED_CREATOR = 'verified_creator',
  
  // Business roles
  COLLECTOR = 'collector',
  TRADER = 'trader',
  
  // Moderation roles
  MODERATOR = 'moderator',
  COMMUNITY_MANAGER = 'community_manager',
  
  // Administrative roles
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
  
  // System roles
  SYSTEM = 'system',
  SERVICE = 'service',
}

/**
 * Permission scope interface
 */
export interface PermissionScope {
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
}

/**
 * Permission condition interface
 */
export interface PermissionCondition {
  field: string;
  operator: ConditionOperator;
  value: any;
}

/**
 * Condition operators
 */
export enum ConditionOperator {
  EQUALS = 'eq',
  NOT_EQUALS = 'ne',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  IN = 'in',
  NOT_IN = 'nin',
  CONTAINS = 'contains',
  STARTS_WITH = 'starts_with',
  ENDS_WITH = 'ends_with',
  REGEX = 'regex',
}

/**
 * Role permissions mapping
 */
export interface RolePermissions {
  role: Role;
  permissions: Permission[];
  description: string;
  isSystemRole: boolean;
  isDefault: boolean;
  hierarchy: number; // Higher number = more permissions
}

/**
 * User role assignment
 */
export interface UserRole {
  userId: string;
  role: Role;
  assignedBy: string;
  assignedAt: Date;
  expiresAt?: Date;
  isActive: boolean;
  metadata?: Record<string, any>;
}

/**
 * Permission check result
 */
export interface PermissionCheckResult {
  granted: boolean;
  reason?: string;
  conditions?: PermissionCondition[];
  metadata?: Record<string, any>;
}

/**
 * Resource ownership interface
 */
export interface ResourceOwnership {
  resourceType: string;
  resourceId: string;
  ownerId: string;
  ownerType: 'user' | 'role' | 'group';
  permissions: Permission[];
  inheritedFrom?: string;
}

/**
 * Permission context
 */
export interface PermissionContext {
  user: {
    id: string;
    roles: string[];
    permissions: string[];
  };
  resource?: {
    type: string;
    id: string;
    ownerId?: string;
    metadata?: Record<string, any>;
  };
  request?: {
    method: string;
    path: string;
    ipAddress: string;
    userAgent: string;
  };
  environment?: {
    service: string;
    environment: string;
    timestamp: Date;
  };
}

/**
 * Default role permissions configuration
 */
export const DEFAULT_ROLE_PERMISSIONS: RolePermissions[] = [
  {
    role: Role.GUEST,
    permissions: [
      Permission.USER_READ,
      Permission.NFT_READ,
      Permission.PROJECT_READ,
      Permission.MARKETPLACE_READ,
    ],
    description: 'Guest user with read-only access',
    isSystemRole: true,
    isDefault: true,
    hierarchy: 0,
  },
  {
    role: Role.USER,
    permissions: [
      Permission.USER_READ,
      Permission.USER_PROFILE_READ,
      Permission.USER_PROFILE_WRITE,
      Permission.NFT_READ,
      Permission.PROJECT_READ,
      Permission.MARKETPLACE_READ,
      Permission.MARKETPLACE_BUY,
      Permission.NOTIFICATION_READ,
    ],
    description: 'Regular user with basic permissions',
    isSystemRole: true,
    isDefault: true,
    hierarchy: 10,
  },
  {
    role: Role.CREATOR,
    permissions: [
      Permission.USER_READ,
      Permission.USER_PROFILE_READ,
      Permission.USER_PROFILE_WRITE,
      Permission.NFT_READ,
      Permission.NFT_WRITE,
      Permission.NFT_MINT,
      Permission.PROJECT_READ,
      Permission.PROJECT_WRITE,
      Permission.MARKETPLACE_READ,
      Permission.MARKETPLACE_BUY,
      Permission.MARKETPLACE_SELL,
      Permission.MARKETPLACE_LIST,
      Permission.NOTIFICATION_READ,
      Permission.PROFILE_ANALYSIS_READ,
    ],
    description: 'Content creator with NFT creation permissions',
    isSystemRole: true,
    isDefault: false,
    hierarchy: 20,
  },
  {
    role: Role.MODERATOR,
    permissions: [
      Permission.USER_READ,
      Permission.NFT_READ,
      Permission.NFT_ADMIN,
      Permission.PROJECT_READ,
      Permission.PROJECT_MODERATE,
      Permission.MARKETPLACE_READ,
      Permission.MARKETPLACE_MODERATE,
      Permission.NOTIFICATION_READ,
      Permission.NOTIFICATION_SEND,
      Permission.AUDIT_READ,
    ],
    description: 'Content moderator with moderation permissions',
    isSystemRole: true,
    isDefault: false,
    hierarchy: 50,
  },
  {
    role: Role.ADMIN,
    permissions: [
      Permission.USER_READ,
      Permission.USER_WRITE,
      Permission.USER_ADMIN,
      Permission.NFT_READ,
      Permission.NFT_WRITE,
      Permission.NFT_ADMIN,
      Permission.PROJECT_READ,
      Permission.PROJECT_WRITE,
      Permission.PROJECT_ADMIN,
      Permission.MARKETPLACE_READ,
      Permission.MARKETPLACE_WRITE,
      Permission.MARKETPLACE_ADMIN,
      Permission.BLOCKCHAIN_READ,
      Permission.BLOCKCHAIN_WRITE,
      Permission.ANALYTICS_READ,
      Permission.ANALYTICS_WRITE,
      Permission.NOTIFICATION_READ,
      Permission.NOTIFICATION_WRITE,
      Permission.NOTIFICATION_ADMIN,
      Permission.PROFILE_ANALYSIS_READ,
      Permission.PROFILE_ANALYSIS_WRITE,
      Permission.AUDIT_READ,
      Permission.AUDIT_WRITE,
      Permission.API_READ,
      Permission.API_WRITE,
    ],
    description: 'Administrator with full application permissions',
    isSystemRole: true,
    isDefault: false,
    hierarchy: 80,
  },
  {
    role: Role.SUPER_ADMIN,
    permissions: Object.values(Permission),
    description: 'Super administrator with all system permissions',
    isSystemRole: true,
    isDefault: false,
    hierarchy: 100,
  },
];

/**
 * Permission utility functions
 */
export class PermissionUtils {
  /**
   * Check if permission is valid
   */
  static isValidPermission(permission: string): permission is Permission {
    return Object.values(Permission).includes(permission as Permission);
  }

  /**
   * Check if role is valid
   */
  static isValidRole(role: string): role is Role {
    return Object.values(Role).includes(role as Role);
  }

  /**
   * Get permissions for role
   */
  static getRolePermissions(role: Role): Permission[] {
    const roleConfig = DEFAULT_ROLE_PERMISSIONS.find(rp => rp.role === role);
    return roleConfig ? roleConfig.permissions : [];
  }

  /**
   * Check if role has permission
   */
  static roleHasPermission(role: Role, permission: Permission): boolean {
    const permissions = this.getRolePermissions(role);
    return permissions.includes(permission);
  }

  /**
   * Get role hierarchy level
   */
  static getRoleHierarchy(role: Role): number {
    const roleConfig = DEFAULT_ROLE_PERMISSIONS.find(rp => rp.role === role);
    return roleConfig ? roleConfig.hierarchy : 0;
  }

  /**
   * Check if role A has higher hierarchy than role B
   */
  static isHigherRole(roleA: Role, roleB: Role): boolean {
    return this.getRoleHierarchy(roleA) > this.getRoleHierarchy(roleB);
  }

  /**
   * Parse permission string
   */
  static parsePermission(permission: string): PermissionScope | null {
    const parts = permission.split(':');
    if (parts.length < 2) return null;

    return {
      resource: parts[0],
      action: parts[1],
    };
  }

  /**
   * Build permission string
   */
  static buildPermission(resource: string, action: string): string {
    return `${resource}:${action}`;
  }
}
