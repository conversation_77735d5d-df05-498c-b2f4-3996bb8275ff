#!/bin/bash

# Environment Switching Script for Approach 3: Independent Mock Services
# Location: tools/scripts/switch-environment.sh
# Usage: ./tools/scripts/switch-environment.sh [mock|real]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to display usage
show_usage() {
    echo "Usage: $0 [mock|real]"
    echo ""
    echo "Environments:"
    echo "  mock  - Use mock services for development (fast, no external dependencies)"
    echo "  real  - Use real services for testing (requires external API keys)"
    echo ""
    echo "Examples:"
    echo "  $0 mock   # Switch to mock services"
    echo "  $0 real   # Switch to real services"
}

# Function to backup current .env
backup_env() {
    if [ -f .env ]; then
        cp .env .env.backup
        print_status "Backed up current .env to .env.backup"
    fi
}

# Function to switch to mock environment
switch_to_mock() {
    print_status "Switching to MOCK services environment..."

    if [ ! -f .env.development.mock ]; then
        print_error ".env.development.mock file not found!"
        exit 1
    fi

    backup_env
    cp .env.development.mock .env

    # Update environment header
    sed -i 's/# Current Environment: .*/# Current Environment: MOCK SERVICES/' .env
    sed -i "s/# Last Updated: .*/# Last Updated: $(date)/" .env

    print_success "Switched to MOCK services environment"
    print_status "Configuration:"
    print_status "  ✅ USE_MOCK_SERVICES=true"
    print_status "  ✅ SERVICE_ENVIRONMENT=mock"
    print_status "  ✅ External services use mock implementations"
    print_status ""
    print_status "Mock Services:"
    print_status "  - Mock Twitter Service (Port 3020)"
    print_status "  - Mock Blockchain Service (Port 3021)"
    print_status "  - Mock NFT Storage Service (Port 3022)"
    print_warning "Make sure to start the mock services before testing!"
}

# Function to switch to real environment
switch_to_real() {
    print_status "Switching to REAL services environment..."

    if [ ! -f .env.development.real ]; then
        print_error ".env.development.real file not found!"
        exit 1
    fi

    backup_env
    cp .env.development.real .env

    # Update environment header
    sed -i 's/# Current Environment: .*/# Current Environment: REAL SERVICES/' .env
    sed -i "s/# Last Updated: .*/# Last Updated: $(date)/" .env

    print_success "Switched to REAL services environment"
    print_status "Configuration:"
    print_status "  ✅ USE_MOCK_SERVICES=false"
    print_status "  ✅ SERVICE_ENVIRONMENT=real"
    print_status "  ✅ External services use real implementations"
    print_status ""
    print_status "Real Services:"
    print_status "  - Real Twitter API"
    print_status "  - Real Blockchain RPC"
    print_status "  - Real NFT Storage API"
    print_warning "Make sure you have configured external API keys!"
    print_warning "Check .env.development.real for required API credentials"
}

# Function to show current environment
show_current_env() {
    if [ -f .env ]; then
        local env_type=""
        local service_env=""

        if grep -q "USE_MOCK_SERVICES=true" .env; then
            env_type="MOCK services"
        elif grep -q "USE_MOCK_SERVICES=false" .env; then
            env_type="REAL services"
        else
            env_type="UNKNOWN (USE_MOCK_SERVICES not set)"
        fi

        if grep -q "SERVICE_ENVIRONMENT=" .env; then
            service_env=$(grep "SERVICE_ENVIRONMENT=" .env | cut -d= -f2)
            print_status "Current environment: $env_type (SERVICE_ENVIRONMENT=$service_env)"
        else
            print_status "Current environment: $env_type"
        fi

        # Show additional environment info
        if grep -q "NODE_ENV=" .env; then
            local node_env=$(grep "NODE_ENV=" .env | cut -d= -f2)
            print_status "Node environment: $node_env"
        fi

        if grep -q "GATEWAY_SECRET=" .env; then
            local gateway_secret=$(grep "GATEWAY_SECRET=" .env | cut -d= -f2)
            if [ "$gateway_secret" = "dev-gateway-secret-change-in-production" ]; then
                print_warning "Using development gateway secret"
            fi
        fi
    else
        print_warning "No .env file found"
    fi
}

# Main script logic
main() {
    echo "🔄 Environment Switching Script for Social NFT Platform"
    echo "=================================================="
    
    show_current_env
    echo ""
    
    case "$1" in
        "mock")
            switch_to_mock
            ;;
        "real")
            switch_to_real
            ;;
        "")
            print_error "No environment specified!"
            echo ""
            show_usage
            exit 1
            ;;
        *)
            print_error "Invalid environment: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
    
    echo ""
    print_success "Environment switch completed!"
    print_status "Restart your services to apply the new configuration."
}

# Run main function with all arguments
main "$@"
