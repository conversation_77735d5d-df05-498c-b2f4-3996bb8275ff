#!/bin/bash

# Deploy Security Middleware to All Services
# This script copies GatewayAuthMiddleware to all services and updates their app.module.ts

set -e

echo "🔒 Deploying Security Middleware to All Services"
echo "================================================"

# Define services that need security middleware
SERVICES=(
    "nft-generation-service"
    "blockchain-service" 
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Base directory
BASE_DIR="$(pwd)"

# Function to create middleware files for a service
create_middleware_files() {
    local service=$1
    local service_dir="services/$service"
    
    echo "📁 Creating middleware files for $service..."
    
    # Create common/middleware directory
    mkdir -p "$service_dir/src/common/middleware"
    
    # Create GatewayAuthMiddleware
    cat > "$service_dir/src/common/middleware/gateway-auth.middleware.ts" << 'EOF'
import { Injectable, NestMiddleware, ForbiddenException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response, NextFunction } from 'express';

/**
 * Gateway Authentication Middleware
 * Ensures requests only come through the API Gateway
 */
@Injectable()
export class GatewayAuthMiddleware implements NestMiddleware {
  private readonly logger = new Logger(GatewayAuthMiddleware.name);
  private readonly gatewaySecret: string;
  private readonly allowDirectAccess: boolean;
  private readonly trustedIPs: string[];

  constructor(private readonly configService: ConfigService) {
    this.gatewaySecret = this.configService.get<string>('GATEWAY_SECRET') || 'default-gateway-secret-change-in-production';
    this.allowDirectAccess = this.configService.get<string>('ALLOW_DIRECT_ACCESS') === 'true';
    this.trustedIPs = (this.configService.get<string>('TRUSTED_IPS') || '127.0.0.1,::1').split(',');
  }

  use(req: Request, res: Response, next: NextFunction) {
    // Skip validation in development if explicitly allowed
    if (this.allowDirectAccess && process.env.NODE_ENV === 'development') {
      this.logger.debug('Direct access allowed in development mode');
      return next();
    }

    // Allow health checks to bypass gateway authentication
    if (req.path === '/api/health' || req.path === '/health') {
      this.logger.debug(`Health check request allowed: ${req.path}`);
      return next();
    }

    // Check for gateway authentication header
    const gatewayAuth = req.headers['x-gateway-auth'] as string;
    const forwardedBy = req.headers['x-forwarded-by'] as string;
    const clientIP = this.getClientIP(req);

    // Validate gateway authentication
    if (!this.isValidGatewayRequest(gatewayAuth, forwardedBy, clientIP)) {
      this.logger.warn(`Unauthorized direct access attempt from ${clientIP} to ${req.path}`, {
        headers: {
          'x-gateway-auth': gatewayAuth ? '[PRESENT]' : '[MISSING]',
          'x-forwarded-by': forwardedBy,
          'user-agent': req.headers['user-agent'],
        },
        ip: clientIP,
        path: req.path,
        method: req.method,
      });

      throw new ForbiddenException({
        message: 'Direct service access is not allowed. Please use the API Gateway.',
        error: 'DIRECT_ACCESS_FORBIDDEN',
        gateway: 'http://localhost:3010',
        service: SERVICE_NAME,
        timestamp: new Date().toISOString(),
      });
    }

    // Log successful gateway request
    this.logger.debug(`Valid gateway request from ${clientIP} to ${req.path}`);
    next();
  }

  private isValidGatewayRequest(gatewayAuth: string, forwardedBy: string, clientIP: string): boolean {
    // Check 1: Gateway authentication header
    if (gatewayAuth !== this.gatewaySecret) {
      return false;
    }

    // Check 2: Forwarded by API Gateway
    if (forwardedBy !== 'api-gateway') {
      return false;
    }

    // Check 3: Request from trusted IP (localhost in development)
    if (!this.trustedIPs.includes(clientIP)) {
      return false;
    }

    return true;
  }

  private getClientIP(req: Request): string {
    return (
      req.headers['x-forwarded-for'] as string ||
      req.headers['x-real-ip'] as string ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      '127.0.0.1'
    );
  }
}
EOF

    # Replace SERVICE_NAME placeholder with actual service name
    sed -i "s/SERVICE_NAME/'$service'/g" "$service_dir/src/common/middleware/gateway-auth.middleware.ts"
    
    # Create CorrelationIdMiddleware
    cat > "$service_dir/src/common/middleware/correlation-id.middleware.ts" << 'EOF'
import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

/**
 * Correlation ID Middleware
 * Adds correlation ID to requests for tracing
 */
@Injectable()
export class CorrelationIdMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CorrelationIdMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    // Generate or use existing correlation ID
    const correlationId = req.headers['x-correlation-id'] as string || uuidv4();
    
    // Add to request headers
    req.headers['x-correlation-id'] = correlationId;
    
    // Add to response headers
    res.setHeader('x-correlation-id', correlationId);
    
    // Log request with correlation ID
    this.logger.log(`${req.method} ${req.path}`, {
      correlationId,
      userAgent: req.headers['user-agent'],
      ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
    });
    
    next();
  }
}
EOF

    echo "✅ Middleware files created for $service"
}

# Function to install dependencies
install_dependencies() {
    local service=$1
    local service_dir="services/$service"
    
    echo "📦 Installing dependencies for $service..."
    cd "$service_dir"
    npm install uuid @types/uuid --silent
    cd "$BASE_DIR"
    echo "✅ Dependencies installed for $service"
}

# Function to update app.module.ts
update_app_module() {
    local service=$1
    local service_dir="services/$service"
    local app_module="$service_dir/src/app.module.ts"
    
    echo "🔧 Updating app.module.ts for $service..."
    
    # Create backup
    cp "$app_module" "$app_module.backup"
    
    # Check if middleware is already imported
    if grep -q "GatewayAuthMiddleware" "$app_module"; then
        echo "⚠️  GatewayAuthMiddleware already imported in $service"
        return
    fi
    
    # Add imports at the top
    sed -i '1i import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '\''@nestjs/common'\'';' "$app_module"
    sed -i '/import.*@nestjs\/common/a\\n// Security Middleware\nimport { GatewayAuthMiddleware } from '\''./common/middleware/gateway-auth.middleware'\'';\nimport { CorrelationIdMiddleware } from '\''./common/middleware/correlation-id.middleware'\'';' "$app_module"
    
    # Add NestModule implementation
    sed -i 's/export class AppModule {}/export class AppModule implements NestModule {\n  configure(consumer: MiddlewareConsumer) {\n    \/\/ Apply security middleware first (before correlation ID)\n    \/\/ Apply to all routes except health checks\n    consumer.apply(GatewayAuthMiddleware)\n      .exclude(\n        { path: '\''health'\'', method: RequestMethod.GET },\n        { path: '\''health\/simple'\'', method: RequestMethod.GET },\n        { path: '\''status'\'', method: RequestMethod.GET },\n      )\n      .forRoutes('\''*'\'');\n    consumer.apply(CorrelationIdMiddleware).forRoutes('\''*'\'');\n  }\n}/' "$app_module"
    
    echo "✅ app.module.ts updated for $service"
}

# Main execution
echo "🚀 Starting security middleware deployment..."

for service in "${SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service" ]; then
        echo "❌ Service directory not found: services/$service"
        continue
    fi
    
    # Create middleware files
    create_middleware_files "$service"
    
    # Install dependencies
    install_dependencies "$service"
    
    # Update app.module.ts
    update_app_module "$service"
    
    echo "✅ $service security deployment complete"
done

echo ""
echo "🎉 Security middleware deployment completed!"
echo "============================================"
echo ""
echo "📋 Summary:"
echo "- Deployed GatewayAuthMiddleware to ${#SERVICES[@]} services"
echo "- Installed required dependencies (uuid, @types/uuid)"
echo "- Updated app.module.ts files with middleware configuration"
echo ""
echo "🔍 Next steps:"
echo "1. Restart all services to apply changes"
echo "2. Run security tests to verify implementation"
echo "3. Test direct access blocking"
