import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';

// Controllers
import { ProjectQueryController } from './controllers/project-query.controller';
import { ProjectCommandController } from './controllers/project-command.controller';
import { CampaignQueryController } from './controllers/campaign-query.controller';
import { CampaignCommandController } from './controllers/campaign-command.controller';

// Services
import { ProjectQueryService } from './services/project-query.service';
import { ProjectCommandService } from './services/project-command.service';
import { CampaignQueryService } from './services/campaign-query.service';
import { CampaignCommandService } from './services/campaign-command.service';

@Module({
  imports: [
    HttpModule, // For external API calls
  ],
  controllers: [
    // Project Management Controllers
    ProjectQueryController,
    ProjectCommandController,
    
    // Campaign Management Controllers
    CampaignQueryController,
    CampaignCommandController,
  ],
  providers: [
    // Project Management Services
    ProjectQueryService,
    ProjectCommandService,
    
    // Campaign Management Services
    CampaignQueryService,
    CampaignCommandService,
  ],
  exports: [
    // Export services for use in other modules
    ProjectQueryService,
    ProjectCommandService,
    CampaignQueryService,
    CampaignCommandService,
  ],
})
export class ProjectModule {}
