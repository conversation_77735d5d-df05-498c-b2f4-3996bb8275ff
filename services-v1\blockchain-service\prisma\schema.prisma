// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model SmartContract {
  id          String   @id @default(cuid())
  name        String
  address     String   @unique
  network     String
  type        String
  abi         Json?
  bytecode    String?
  verified    Boolean  @default(false)
  deployedAt  DateTime @default(now())
  deployedBy  String?
  status      String   @default("active")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  transactions Transaction[]

  @@map("smart_contracts")
}

model Transaction {
  id            String   @id @default(cuid())
  hash          String   @unique
  blockNumber   Int?
  blockHash     String?
  from          String
  to            String
  value         String
  gasUsed       Int?
  gasPrice      String?
  status        String   @default("pending")
  confirmations Int      @default(0)
  network       String
  contractId    String?
  methodName    String?
  parameters    Json?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  contract      SmartContract? @relation(fields: [contractId], references: [id])

  @@map("transactions")
}

model Wallet {
  id        String   @id @default(cuid())
  address   String   @unique
  name      String?
  type      String
  network   String
  balance   String   @default("0")
  isActive  Boolean  @default(true)
  ownerId   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("wallets")
}

model NetworkStatus {
  id          String   @id @default(cuid())
  networkId   String   @unique
  name        String
  chainId     Int
  rpcUrl      String
  status      String   @default("active")
  blockHeight Int?
  gasPrice    String?
  peers       Int?
  syncStatus  String?
  lastChecked DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("network_status")
}

model GasEstimate {
  id           String   @id @default(cuid())
  network      String
  transactionType String
  gasLimit     Int
  gasPrice     String
  estimatedCost String
  currency     String
  usdCost      String?
  createdAt    DateTime @default(now())

  @@map("gas_estimates")
}
