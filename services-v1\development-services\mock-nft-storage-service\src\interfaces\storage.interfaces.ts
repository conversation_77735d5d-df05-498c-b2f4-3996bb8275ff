// NFT Storage Service Interfaces for Mock Implementation

export interface NFTMetadata {
  name: string;
  description: string;
  image: string;
  attributes: Array<{
    trait_type: string;
    value: string | number;
  }>;
  external_url?: string;
  animation_url?: string;
  background_color?: string;
  youtube_url?: string;
}

export interface StorageUploadRequest {
  metadata: NFTMetadata;
  imageFile?: Express.Multer.File;
  animationFile?: Express.Multer.File;
}

export interface StorageUploadResult {
  success: boolean;
  metadataUri?: string;
  imageUri?: string;
  animationUri?: string;
  ipfsHash?: string;
  error?: string;
}

export interface IPFSFile {
  hash: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: string;
}
