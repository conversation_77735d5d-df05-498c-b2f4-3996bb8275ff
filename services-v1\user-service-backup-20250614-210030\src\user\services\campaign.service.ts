import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../common/services/cache.service';
import { AuditService } from '../../common/services/audit.service';
import { RBACService } from './rbac.service';
import { 
  CreateCampaignDto, 
  UpdateCampaignDto, 
  CampaignType, 
  CampaignStatus,
  RewardType 
} from '../dto/campaign.dto';
import { 
  JoinCampaignDto, 
  SubmitRequirementDto, 
  ReviewSubmissionDto,
  ClaimRewardDto,
  CampaignParticipationResponseDto,
  ParticipationStatus,
  SubmissionStatus 
} from '../dto/campaign-participation.dto';
import { CampaignAnalyticsDto } from '../dto/campaign-analytics.dto';
import { Permission } from '../dto/rbac.dto';
import { RequestContext } from '../interfaces/request-context.interface';

export interface CampaignResult {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

@Injectable()
export class CampaignService {
  private readonly logger = new Logger(CampaignService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
    private readonly auditService: AuditService,
    private readonly rbacService: RBACService,
  ) {}

  /**
   * Create a new campaign
   */
  async createCampaign(createCampaignDto: CreateCampaignDto, context: RequestContext): Promise<CampaignResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Creating campaign: ${createCampaignDto.name}`, { correlationId });

      // Check permissions
      const canCreateCampaigns = await this.rbacService.hasPermission(context.userId, Permission.CAMPAIGN_WRITE);
      if (!canCreateCampaigns) {
        throw new ForbiddenException('Insufficient permissions to create campaigns');
      }

      // Validate campaign dates
      const startDate = new Date(createCampaignDto.startDate);
      const endDate = new Date(createCampaignDto.endDate);
      
      if (startDate >= endDate) {
        throw new BadRequestException('Campaign end date must be after start date');
      }

      if (startDate < new Date()) {
        throw new BadRequestException('Campaign start date cannot be in the past');
      }

      // Validate requirements and rewards
      if (!createCampaignDto.requirements || createCampaignDto.requirements.length === 0) {
        throw new BadRequestException('Campaign must have at least one requirement');
      }

      if (!createCampaignDto.rewards || createCampaignDto.rewards.length === 0) {
        throw new BadRequestException('Campaign must have at least one reward');
      }

      // Create campaign
      const campaign = await this.prisma.$transaction(async (tx) => {
        // Create main campaign record
        const newCampaign = await tx.campaign.create({
          data: {
            title: createCampaignDto.name,
            name: createCampaignDto.name,
            description: createCampaignDto.description,
            type: createCampaignDto.type,
            status: CampaignStatus.DRAFT,
            startDate: startDate,
            endDate: endDate,
            bannerImage: createCampaignDto.bannerImage,
            maxParticipants: createCampaignDto.maxParticipants,
            minAge: createCampaignDto.minAge,
            geoRestrictions: createCampaignDto.geoRestrictions || [],
            tags: createCampaignDto.tags || [],
            priority: createCampaignDto.priority || 5,
            featured: createCampaignDto.featured || false,
            metadata: createCampaignDto.metadata || {},
            createdBy: context.userId,
          },
        });

        // Create campaign requirements
        for (const requirement of createCampaignDto.requirements) {
          await tx.campaignRequirement.create({
            data: {
              campaignId: newCampaign.id,
              type: requirement.type,
              target: requirement.target,
              minValue: requirement.minValue,
              maxValue: requirement.maxValue,
              mandatory: requirement.mandatory ?? true,
              points: requirement.points || 0,
            },
          });
        }

        // Create campaign rewards
        for (const reward of createCampaignDto.rewards) {
          await tx.campaignReward.create({
            data: {
              campaignId: newCampaign.id,
              type: reward.type,
              name: reward.name,
              description: reward.description,
              value: String(reward.value),
              rarity: reward.rarity,
              maxQuantity: reward.maxQuantity,
              minPointsRequired: reward.minPointsRequired || 0,
              metadata: reward.metadata || {},
            },
          });
        }

        return newCampaign;
      });

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'Campaign',
        entityId: campaign.id,
        action: 'CREATE',
        resource: 'campaigns',
        newValues: campaign,
        userId: context.userId,
        correlationId: context.correlationId,
        metadata: { campaignType: createCampaignDto.type },
      });

      this.logger.log(`Campaign created successfully: ${campaign.id}`, {
        correlationId,
        campaignId: campaign.id,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: campaign,
        message: 'Campaign created successfully',
      };

    } catch (error) {
      this.logger.error(`Campaign creation failed: ${error.message}`, {
        correlationId,
        createCampaignDto,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof BadRequestException || error instanceof ForbiddenException) {
        throw error;
      }

      return { success: false, error: 'Campaign creation failed' };
    }
  }

  /**
   * Get campaign by ID with full details
   */
  async getCampaignById(campaignId: string, context: RequestContext): Promise<CampaignResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Getting campaign: ${campaignId}`, { correlationId });

      // Try cache first
      const cacheKey = `campaign:${campaignId}`;
      let campaign = await this.cacheService.get<any>(cacheKey);

      if (!campaign) {
        // Get from database with all related data
        campaign = await this.prisma.campaign.findUnique({
          where: { id: campaignId },
          include: {
            requirements: true,
            rewards: true,
            participations: {
              include: {
                user: {
                  select: {
                    id: true,
                    username: true,
                    displayName: true,
                  },
                },
                submissions: true,
              },
            },
            _count: {
              select: {
                participations: true,
              },
            },
          },
        });

        if (!campaign) {
          throw new NotFoundException(`Campaign with ID ${campaignId} not found`);
        }

        // Cache for 5 minutes
        await this.cacheService.set(cacheKey, campaign, 300);
      }

      // Calculate additional metrics
      const enrichedCampaign = {
        ...campaign,
        participantCount: campaign._count?.participations || 0,
        completionRate: this.calculateCompletionRate(campaign.participations || []),
        daysRemaining: this.calculateDaysRemaining(campaign.endDate),
        isActive: this.isCampaignActive(campaign),
      };

      this.logger.log(`Campaign retrieved successfully: ${campaignId}`, {
        correlationId,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: enrichedCampaign,
      };

    } catch (error) {
      this.logger.error(`Failed to get campaign ${campaignId}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof NotFoundException) {
        throw error;
      }

      return { success: false, error: 'Failed to retrieve campaign' };
    }
  }

  /**
   * Get all campaigns with filtering and pagination
   */
  async getCampaigns(
    filters: {
      status?: CampaignStatus;
      type?: CampaignType;
      featured?: boolean;
      tags?: string[];
      search?: string;
      page?: number;
      limit?: number;
    },
    context: RequestContext
  ): Promise<CampaignResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log('Getting campaigns with filters', { correlationId, filters });

      const page = filters.page || 1;
      const limit = Math.min(filters.limit || 20, 100); // Max 100 per page
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.type) {
        where.type = filters.type;
      }

      if (filters.featured !== undefined) {
        where.featured = filters.featured;
      }

      if (filters.tags && filters.tags.length > 0) {
        where.tags = {
          hasSome: filters.tags,
        };
      }

      if (filters.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      // Get campaigns with counts
      const [campaigns, totalCount] = await Promise.all([
        this.prisma.campaign.findMany({
          where,
          include: {
            _count: {
              select: {
                participations: true,
              },
            },
          },
          orderBy: [
            { featured: 'desc' },
            { priority: 'desc' },
            { createdAt: 'desc' },
          ],
          skip,
          take: limit,
        }),
        this.prisma.campaign.count({ where }),
      ]);

      // Enrich campaigns with additional data
      const enrichedCampaigns = campaigns.map(campaign => ({
        ...campaign,
        participantCount: campaign._count.participations,
        daysRemaining: this.calculateDaysRemaining(campaign.endDate),
        isActive: this.isCampaignActive(campaign),
      }));

      const result = {
        campaigns: enrichedCampaigns,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: page * limit < totalCount,
          hasPrev: page > 1,
        },
      };

      this.logger.log(`Retrieved ${campaigns.length} campaigns`, {
        correlationId,
        totalCount,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: result,
      };

    } catch (error) {
      this.logger.error(`Failed to get campaigns: ${error.message}`, {
        correlationId,
        filters,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      return { success: false, error: 'Failed to retrieve campaigns' };
    }
  }

  /**
   * Helper method to calculate completion rate
   */
  private calculateCompletionRate(participations: any[]): number {
    if (participations.length === 0) return 0;
    
    const completed = participations.filter(p => p.status === ParticipationStatus.COMPLETED).length;
    return (completed / participations.length) * 100;
  }

  /**
   * Helper method to calculate days remaining
   */
  private calculateDaysRemaining(endDate: Date): number {
    const now = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  /**
   * Helper method to check if campaign is active
   */
  private isCampaignActive(campaign: any): boolean {
    const now = new Date();
    const start = new Date(campaign.startDate);
    const end = new Date(campaign.endDate);
    
    return campaign.status === CampaignStatus.ACTIVE && 
           now >= start && 
           now <= end;
  }

  /**
   * Join a campaign
   */
  async joinCampaign(joinCampaignDto: JoinCampaignDto, userId: string, context: RequestContext): Promise<CampaignResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`User ${userId} joining campaign: ${joinCampaignDto.campaignId}`, { correlationId });

      // Get campaign details
      const campaign = await this.prisma.campaign.findUnique({
        where: { id: joinCampaignDto.campaignId },
        include: {
          requirements: true,
          _count: { select: { participations: true } },
        },
      });

      if (!campaign) {
        throw new NotFoundException(`Campaign with ID ${joinCampaignDto.campaignId} not found`);
      }

      // Validate campaign eligibility
      await this.validateCampaignEligibility(campaign, userId);

      // Check if user already participating
      const existingParticipation = await this.prisma.campaignParticipation.findUnique({
        where: {
          userId_campaignId: {
            userId: userId,
            campaignId: joinCampaignDto.campaignId,
          },
        },
      });

      if (existingParticipation) {
        throw new BadRequestException('User is already participating in this campaign');
      }

      // Create participation record
      const participation = await this.prisma.$transaction(async (tx) => {
        const newParticipation = await tx.campaignParticipation.create({
          data: {
            userId: userId,
            campaignId: joinCampaignDto.campaignId,
            status: ParticipationStatus.IN_PROGRESS,
            referralCode: joinCampaignDto.referralCode,
            acceptedTerms: joinCampaignDto.acceptTerms,
            joinedAt: new Date(),
          },
        });

        // Process initial submissions if provided
        if (joinCampaignDto.initialSubmissions && joinCampaignDto.initialSubmissions.length > 0) {
          for (const submission of joinCampaignDto.initialSubmissions) {
            await tx.requirementSubmission.create({
              data: {
                userId: userId,
                requirementId: `req_${submission.requirementType}_${Date.now()}`,
                participationId: newParticipation.id,
                requirementType: submission.requirementType,
                submissionValue: submission.submissionValue,
                proofUrls: submission.proofUrls || [],
                notes: submission.notes,
                metadata: submission.metadata || {},
                status: SubmissionStatus.PENDING_REVIEW,
                submittedAt: new Date(),
              },
            });
          }
        }

        return newParticipation;
      });

      // Invalidate campaign cache
      await this.invalidateCampaignCache(joinCampaignDto.campaignId);

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'CampaignParticipation',
        entityId: participation.id,
        action: 'CREATE',
        resource: 'campaigns',
        newValues: participation,
        userId: userId,
        correlationId: context.correlationId,
        metadata: {
          campaignId: joinCampaignDto.campaignId,
          referralCode: joinCampaignDto.referralCode
        },
      });

      this.logger.log(`User ${userId} joined campaign successfully: ${joinCampaignDto.campaignId}`, {
        correlationId,
        participationId: participation.id,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: participation,
        message: 'Successfully joined campaign',
      };

    } catch (error) {
      this.logger.error(`Failed to join campaign: ${error.message}`, {
        correlationId,
        userId,
        joinCampaignDto,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      return { success: false, error: 'Failed to join campaign' };
    }
  }

  /**
   * Validate campaign eligibility for user
   */
  private async validateCampaignEligibility(campaign: any, userId: string): Promise<void> {
    // Check if campaign is active and within date range
    if (!this.isCampaignActive(campaign)) {
      throw new BadRequestException('Campaign is not currently active');
    }

    // Check participant limit
    if (campaign.maxParticipants && campaign._count.participations >= campaign.maxParticipants) {
      throw new BadRequestException('Campaign has reached maximum participants');
    }

    // Get user details for additional validation
    const user = await this.prisma.userQuery.findUnique({
      where: { id: userId },
      select: {
        isActive: true,
        isEmailVerified: true,
        // Add other fields as needed for validation
      },
    });

    if (!user || !user.isActive) {
      throw new BadRequestException('User account is not active');
    }

    if (!user.isEmailVerified) {
      throw new BadRequestException('Email verification required to participate in campaigns');
    }

    // TODO: Add more validation logic:
    // - Age verification if minAge is set
    // - Geographic restrictions
    // - NFT ownership requirements
    // - Other custom requirements
  }

  /**
   * Get user's campaign participation
   */
  async getUserParticipation(campaignId: string, userId: string, context: RequestContext): Promise<CampaignResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Getting participation for user ${userId} in campaign ${campaignId}`, { correlationId });

      const participation = await this.prisma.campaignParticipation.findUnique({
        where: {
          userId_campaignId: {
            userId: userId,
            campaignId: campaignId,
          },
        },
        include: {
          campaign: {
            select: {
              name: true,
              requirements: true,
              rewards: true,
            },
          },
          submissions: {
            orderBy: { submittedAt: 'desc' },
          },
          rewardsEarned: {
            include: {
              reward: true,
            },
          },
        },
      });

      if (!participation) {
        throw new NotFoundException('Participation not found');
      }

      // Calculate progress metrics
      const totalRequirements = participation.campaign.requirements.length;
      const completedRequirements = participation.submissions.filter(
        s => s.status === SubmissionStatus.APPROVED
      ).length;
      const progressPercentage = totalRequirements > 0 ?
        (completedRequirements / totalRequirements) * 100 : 0;

      const enrichedParticipation: CampaignParticipationResponseDto = {
        id: participation.id,
        userId: participation.userId,
        campaignId: participation.campaignId,
        campaignName: participation.campaign.name,
        status: participation.status as ParticipationStatus,
        totalPoints: participation.totalPoints,
        progressPercentage,
        requirementsCompleted: completedRequirements,
        totalRequirements,
        joinedAt: participation.joinedAt.toISOString(),
        completedAt: participation.completedAt?.toISOString(),
        referralCode: participation.referralCode,
        submissions: participation.submissions.map(s => ({
          id: s.id,
          requirementType: s.requirementType as any,
          status: s.status as SubmissionStatus,
          submissionValue: s.submissionValue,
          pointsAwarded: s.pointsAwarded,
          submittedAt: s.submittedAt.toISOString(),
          reviewedAt: s.reviewedAt?.toISOString(),
          reviewerComments: s.reviewerComments,
          feedback: s.feedback,
        })),
        rewardsEarned: participation.rewardsEarned.map(r => ({
          id: r.id,
          name: r.reward.name,
          type: r.reward.type,
          value: parseFloat(r.reward.value) || 0,
          rarity: r.reward.rarity,
          earnedAt: r.earnedAt.toISOString(),
          claimedAt: r.claimedAt?.toISOString(),
          claimed: r.claimed,
          metadata: r.metadata as Record<string, any>,
        })),
      };

      this.logger.log(`Participation retrieved successfully`, {
        correlationId,
        participationId: participation.id,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: enrichedParticipation,
      };

    } catch (error) {
      this.logger.error(`Failed to get participation: ${error.message}`, {
        correlationId,
        campaignId,
        userId,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof NotFoundException) {
        throw error;
      }

      return { success: false, error: 'Failed to retrieve participation' };
    }
  }

  /**
   * Invalidate campaign cache
   */
  async invalidateCampaignCache(campaignId: string): Promise<void> {
    const cacheKey = `campaign:${campaignId}`;
    await this.cacheService.del(cacheKey);
    this.logger.debug(`Invalidated cache for campaign: ${campaignId}`);
  }
}
