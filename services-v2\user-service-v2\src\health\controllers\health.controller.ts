/**
 * Health Controller - User Service V2
 * 
 * Health check endpoints using shared infrastructure
 */

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  ResponseService,
  Public,
} from '../../../../../shared';
import { HealthService } from '../services/health.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly healthService: HealthService,
    private readonly responseService: ResponseService,
  ) {}

  @Get('simple')
  @Public()
  @ApiOperation({ summary: 'Simple health check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async getSimpleHealth() {
    const health = await this.healthService.getSimpleHealth();
    return this.responseService.success(health, 'Service is healthy');
  }

  @Get('detailed')
  @Public()
  @ApiOperation({ summary: 'Detailed health check with dependencies' })
  @ApiResponse({ status: 200, description: 'Detailed health information' })
  async getDetailedHealth() {
    const health = await this.healthService.getDetailedHealth();
    
    if (health.status === 'healthy') {
      return this.responseService.success(health, 'Service is healthy');
    } else {
      return this.responseService.error('Service is unhealthy', undefined, {
        details: health,
        statusCode: 503,
      });
    }
  }

  @Get('database')
  @Public()
  @ApiOperation({ summary: 'Database health check' })
  @ApiResponse({ status: 200, description: 'Database health information' })
  async getDatabaseHealth() {
    const health = await this.healthService.getDatabaseHealth();
    
    if (health.status === 'healthy') {
      return this.responseService.success(health, 'Database is healthy');
    } else {
      return this.responseService.error('Database is unhealthy', undefined, {
        details: health,
        statusCode: 503,
      });
    }
  }

  @Get('ready')
  @Public()
  @ApiOperation({ summary: 'Readiness probe for Kubernetes' })
  @ApiResponse({ status: 200, description: 'Service is ready' })
  @ApiResponse({ status: 503, description: 'Service is not ready' })
  async getReadiness() {
    const readiness = await this.healthService.getReadiness();
    
    if (readiness.status === 'ready') {
      return this.responseService.success(readiness, 'Service is ready');
    } else {
      return this.responseService.error('Service is not ready', undefined, {
        details: readiness,
        statusCode: 503,
      });
    }
  }

  @Get('live')
  @Public()
  @ApiOperation({ summary: 'Liveness probe for Kubernetes' })
  @ApiResponse({ status: 200, description: 'Service is alive' })
  async getLiveness() {
    const liveness = await this.healthService.getLiveness();
    return this.responseService.success(liveness, 'Service is alive');
  }
}
