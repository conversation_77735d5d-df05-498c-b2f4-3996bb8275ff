'use client'

import React, { useState } from 'react'
import {
  CalendarIcon,
  UsersIcon,
  GiftIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  EyeIcon,
  PencilIcon,
  ChartBarIcon,
  ClockIcon,
  SparklesIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { Campaign, CampaignStatus, CampaignType } from '@/types/campaign.types'

interface CampaignCardProps {
  campaign: Campaign
  onView?: () => void
  onEdit?: () => void
  onStatusChange?: (status: CampaignStatus) => void
  isUpdating?: boolean
  className?: string
}

export default function CampaignCard({
  campaign,
  onView,
  onEdit,
  onStatusChange,
  isUpdating = false,
  className = ''
}: CampaignCardProps) {
  const [showActions, setShowActions] = useState(false)

  const getStatusColor = (status: CampaignStatus) => {
    switch (status) {
      case CampaignStatus.ACTIVE: return 'text-green-600 bg-green-100 border-green-200'
      case CampaignStatus.PAUSED: return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      case CampaignStatus.COMPLETED: return 'text-blue-600 bg-blue-100 border-blue-200'
      case CampaignStatus.CANCELLED: return 'text-red-600 bg-red-100 border-red-200'
      case CampaignStatus.DRAFT: return 'text-gray-600 bg-gray-100 border-gray-200'
      case CampaignStatus.SCHEDULED: return 'text-purple-600 bg-purple-100 border-purple-200'
      default: return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getTypeIcon = (type: CampaignType) => {
    switch (type) {
      case CampaignType.SOCIAL_ENGAGEMENT: return '📱'
      case CampaignType.CONTENT_CREATION: return '🎨'
      case CampaignType.COMMUNITY_BUILDING: return '👥'
      case CampaignType.TRADING_ACTIVITY: return '💰'
      case CampaignType.REFERRAL_PROGRAM: return '🔗'
      case CampaignType.SEASONAL_EVENT: return '🎉'
      case CampaignType.MILESTONE_ACHIEVEMENT: return '🏆'
      case CampaignType.BRAND_COLLABORATION: return '🤝'
      default: return '📋'
    }
  }

  const getTypeLabel = (type: CampaignType) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ')
  }

  const calculateProgress = () => {
    const now = new Date()
    const start = new Date(campaign.startDate)
    const end = new Date(campaign.endDate)
    
    if (now < start) return 0
    if (now > end) return 100
    
    const total = end.getTime() - start.getTime()
    const elapsed = now.getTime() - start.getTime()
    return Math.round((elapsed / total) * 100)
  }

  const getDaysRemaining = () => {
    const now = new Date()
    const end = new Date(campaign.endDate)
    const diffTime = end.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 0) return 'Ended'
    if (diffDays === 0) return 'Ends today'
    if (diffDays === 1) return '1 day left'
    return `${diffDays} days left`
  }

  const getCompletionRate = () => {
    if (campaign.participantCount === 0) return 0
    return Math.round((campaign.completionRate || 0) * 100)
  }

  const isActive = campaign.status === CampaignStatus.ACTIVE
  const isPaused = campaign.status === CampaignStatus.PAUSED
  const isDraft = campaign.status === CampaignStatus.DRAFT
  const isCompleted = campaign.status === CampaignStatus.COMPLETED

  const progress = calculateProgress()
  const daysRemaining = getDaysRemaining()
  const completionRate = getCompletionRate()

  return (
    <div
      className={`bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all ${className}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{getTypeIcon(campaign.type)}</span>
          <div>
            <h3 className="font-semibold text-gray-900 line-clamp-1">{campaign.name}</h3>
            <p className="text-sm text-gray-600">{getTypeLabel(campaign.type)}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {campaign.featured && (
            <SparklesIcon className="h-4 w-4 text-yellow-500" title="Featured Campaign" />
          )}
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(campaign.status)}`}>
            {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
          </span>
        </div>
      </div>

      {/* Description */}
      <p className="text-sm text-gray-600 mb-4 line-clamp-2">
        {campaign.description}
      </p>

      {/* Campaign Image */}
      {campaign.bannerImage && (
        <div className="mb-4">
          <img
            src={campaign.bannerImage}
            alt={campaign.name}
            className="w-full h-32 object-cover rounded-md"
          />
        </div>
      )}

      {/* Progress Bar */}
      {isActive && (
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Campaign Progress</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-gray-600 mb-1">
            <UsersIcon className="h-4 w-4" />
            <span className="text-xs">Participants</span>
          </div>
          <div className="text-lg font-semibold text-gray-900">
            {campaign.participantCount.toLocaleString()}
          </div>
          {campaign.maxParticipants && (
            <div className="text-xs text-gray-500">
              of {campaign.maxParticipants.toLocaleString()}
            </div>
          )}
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-gray-600 mb-1">
            <ChartBarIcon className="h-4 w-4" />
            <span className="text-xs">Completion</span>
          </div>
          <div className="text-lg font-semibold text-gray-900">
            {completionRate}%
          </div>
          <div className="text-xs text-gray-500">completion rate</div>
        </div>
      </div>

      {/* Timeline */}
      <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
        <div className="flex items-center space-x-1">
          <CalendarIcon className="h-4 w-4" />
          <span>{new Date(campaign.startDate).toLocaleDateString()}</span>
        </div>
        <div className="flex items-center space-x-1">
          <ClockIcon className="h-4 w-4" />
          <span className={daysRemaining === 'Ended' ? 'text-red-600' : ''}>{daysRemaining}</span>
        </div>
      </div>

      {/* Rewards Summary */}
      {campaign.rewards && campaign.rewards.length > 0 && (
        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
          <GiftIcon className="h-4 w-4" />
          <span>{campaign.rewards.length} reward types</span>
          <span>•</span>
          <span>{campaign.totalRewardsDistributed || 0} distributed</span>
        </div>
      )}

      {/* Tags */}
      {campaign.tags && campaign.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-4">
          {campaign.tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700"
            >
              {tag}
            </span>
          ))}
          {campaign.tags.length > 3 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
              +{campaign.tags.length - 3}
            </span>
          )}
        </div>
      )}

      {/* Warnings */}
      {isActive && progress > 90 && (
        <div className="flex items-center space-x-2 text-sm text-orange-600 mb-4 p-2 bg-orange-50 rounded-md">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <span>Campaign ending soon</span>
        </div>
      )}

      {/* Actions */}
      <div className={`flex items-center justify-between transition-opacity ${showActions ? 'opacity-100' : 'opacity-0'}`}>
        <div className="flex items-center space-x-2">
          <button
            onClick={onView}
            className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <EyeIcon className="h-3 w-3 mr-1" />
            View
          </button>
          
          <button
            onClick={onEdit}
            className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <PencilIcon className="h-3 w-3 mr-1" />
            Edit
          </button>
        </div>

        <div className="flex items-center space-x-1">
          {isDraft && (
            <button
              onClick={() => onStatusChange?.(CampaignStatus.ACTIVE)}
              disabled={isUpdating}
              className="p-2 text-green-600 hover:text-green-700 disabled:opacity-50"
              title="Start Campaign"
            >
              <PlayIcon className="h-4 w-4" />
            </button>
          )}

          {isActive && (
            <button
              onClick={() => onStatusChange?.(CampaignStatus.PAUSED)}
              disabled={isUpdating}
              className="p-2 text-yellow-600 hover:text-yellow-700 disabled:opacity-50"
              title="Pause Campaign"
            >
              <PauseIcon className="h-4 w-4" />
            </button>
          )}

          {isPaused && (
            <button
              onClick={() => onStatusChange?.(CampaignStatus.ACTIVE)}
              disabled={isUpdating}
              className="p-2 text-green-600 hover:text-green-700 disabled:opacity-50"
              title="Resume Campaign"
            >
              <PlayIcon className="h-4 w-4" />
            </button>
          )}

          {(isActive || isPaused) && (
            <button
              onClick={() => onStatusChange?.(CampaignStatus.COMPLETED)}
              disabled={isUpdating}
              className="p-2 text-blue-600 hover:text-blue-700 disabled:opacity-50"
              title="Complete Campaign"
            >
              <StopIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
