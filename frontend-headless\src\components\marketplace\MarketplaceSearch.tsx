'use client'

import React, { useState } from 'react'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline'
import { useMarketplaceSearch } from '@/hooks/useMarketplaceIntegration'
import { MarketplaceCategory, SortOption } from '@/types/marketplace-integration.types'

interface MarketplaceSearchProps {
  onViewListing?: (id: string) => void
  className?: string
}

export default function MarketplaceSearch({
  onViewListing,
  className = ''
}: MarketplaceSearchProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState({
    category: [] as MarketplaceCategory[],
    minPrice: '',
    maxPrice: '',
    sortBy: SortOption.NEWEST,
    tags: [] as string[]
  })

  const searchRequest = {
    query: searchQuery,
    filters: {
      category: filters.category.length > 0 ? filters.category : undefined,
      minPrice: filters.minPrice ? parseFloat(filters.minPrice) : undefined,
      maxPrice: filters.maxPrice ? parseFloat(filters.maxPrice) : undefined,
      sortBy: filters.sortBy,
      tags: filters.tags.length > 0 ? filters.tags : undefined
    }
  }

  const { data: searchResults, isLoading } = useMarketplaceSearch(searchRequest)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Search is automatically triggered by the query hook
  }

  const categories = Object.values(MarketplaceCategory)
  const sortOptions = [
    { value: SortOption.NEWEST, label: 'Newest First' },
    { value: SortOption.OLDEST, label: 'Oldest First' },
    { value: SortOption.PRICE_LOW_HIGH, label: 'Price: Low to High' },
    { value: SortOption.PRICE_HIGH_LOW, label: 'Price: High to Low' },
    { value: SortOption.MOST_POPULAR, label: 'Most Popular' },
    { value: SortOption.MOST_VIEWED, label: 'Most Viewed' }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search Header */}
      <div>
        <h2 className="text-lg font-medium text-gray-900">Search Marketplace</h2>
        <p className="text-sm text-gray-600">Find campaigns, collections, and NFTs</p>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search campaigns, collections, creators..."
            />
          </div>

          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium ${
              showFilters ? 'bg-gray-100 text-gray-900' : 'bg-white text-gray-700'
            } hover:bg-gray-50`}
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
          </button>

          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Search
          </button>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">Advanced Filters</h3>
              <button
                type="button"
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Categories */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Categories
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {categories.map((category) => (
                    <label key={category} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.category.includes(category)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFilters(prev => ({
                              ...prev,
                              category: [...prev.category, category]
                            }))
                          } else {
                            setFilters(prev => ({
                              ...prev,
                              category: prev.category.filter(c => c !== category)
                            }))
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 capitalize">
                        {category.replace('_', ' ')}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Range
                </label>
                <div className="space-y-2">
                  <input
                    type="number"
                    value={filters.minPrice}
                    onChange={(e) => setFilters(prev => ({ ...prev, minPrice: e.target.value }))}
                    placeholder="Min price"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                  <input
                    type="number"
                    value={filters.maxPrice}
                    onChange={(e) => setFilters(prev => ({ ...prev, maxPrice: e.target.value }))}
                    placeholder="Max price"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Sort */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sort By
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as SortOption }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Clear Filters */}
            <div className="flex justify-end">
              <button
                type="button"
                onClick={() => setFilters({
                  category: [],
                  minPrice: '',
                  maxPrice: '',
                  sortBy: SortOption.NEWEST,
                  tags: []
                })}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Clear all filters
              </button>
            </div>
          </div>
        )}
      </form>

      {/* Search Results */}
      {isLoading ? (
        <div className="animate-pulse space-y-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded"></div>
          ))}
        </div>
      ) : searchResults ? (
        <div className="space-y-6">
          {/* Results Summary */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {searchResults.total} results found
              {searchQuery && ` for "${searchQuery}"`}
            </div>
            {searchResults.suggestions && searchResults.suggestions.length > 0 && (
              <div className="text-sm text-gray-600">
                Suggestions: {searchResults.suggestions.slice(0, 3).join(', ')}
              </div>
            )}
          </div>

          {/* Search Facets */}
          {searchResults.facets && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Refine Results</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Category Facets */}
                {searchResults.facets.categories && searchResults.facets.categories.length > 0 && (
                  <div>
                    <h4 className="text-xs font-medium text-gray-700 mb-2">Categories</h4>
                    <div className="space-y-1">
                      {searchResults.facets.categories.slice(0, 5).map((facet) => (
                        <button
                          key={facet.category}
                          className="block text-xs text-blue-600 hover:text-blue-700"
                        >
                          {facet.category.replace('_', ' ')} ({facet.count})
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Price Range Facets */}
                {searchResults.facets.priceRanges && searchResults.facets.priceRanges.length > 0 && (
                  <div>
                    <h4 className="text-xs font-medium text-gray-700 mb-2">Price Ranges</h4>
                    <div className="space-y-1">
                      {searchResults.facets.priceRanges.slice(0, 5).map((facet) => (
                        <button
                          key={facet.range}
                          className="block text-xs text-blue-600 hover:text-blue-700"
                        >
                          {facet.range} ({facet.count})
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Creator Facets */}
                {searchResults.facets.creators && searchResults.facets.creators.length > 0 && (
                  <div>
                    <h4 className="text-xs font-medium text-gray-700 mb-2">Creators</h4>
                    <div className="space-y-1">
                      {searchResults.facets.creators.slice(0, 5).map((facet) => (
                        <button
                          key={facet.creatorId}
                          className="block text-xs text-blue-600 hover:text-blue-700"
                        >
                          {facet.creatorName} ({facet.count})
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Tag Facets */}
                {searchResults.facets.tags && searchResults.facets.tags.length > 0 && (
                  <div>
                    <h4 className="text-xs font-medium text-gray-700 mb-2">Tags</h4>
                    <div className="space-y-1">
                      {searchResults.facets.tags.slice(0, 5).map((facet) => (
                        <button
                          key={facet.tag}
                          className="block text-xs text-blue-600 hover:text-blue-700"
                        >
                          #{facet.tag} ({facet.count})
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Results List */}
          {searchResults.listings.length > 0 ? (
            <div className="space-y-4">
              {searchResults.listings.map((listing) => (
                <div
                  key={listing.id}
                  className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
                  onClick={() => onViewListing?.(listing.id)}
                >
                  <div className="flex items-start space-x-4">
                    <img
                      src={listing.coverImage}
                      alt={listing.title}
                      className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                    />
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 mb-1">{listing.title}</h3>
                          <p className="text-sm text-gray-600 mb-2 line-clamp-2">{listing.description}</p>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>by {listing.creatorName}</span>
                            <span>{listing.category.replace('_', ' ')}</span>
                            <span>{listing.campaignData.participantCount} participants</span>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900">
                            {listing.basePrice} {listing.currency}
                          </div>
                          <div className="text-sm text-gray-500">
                            {listing.views} views
                          </div>
                        </div>
                      </div>
                      
                      {/* Tags */}
                      {listing.tags && listing.tags.length > 0 && (
                        <div className="mt-3 flex flex-wrap gap-2">
                          {listing.tags.slice(0, 5).map((tag) => (
                            <span
                              key={tag}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700"
                            >
                              #{tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No results found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search terms or filters.
              </p>
            </div>
          )}

          {/* Pagination */}
          {searchResults.hasMore && (
            <div className="text-center">
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Load More Results
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Start searching</h3>
          <p className="mt-1 text-sm text-gray-500">
            Enter keywords to find campaigns, collections, and NFTs.
          </p>
        </div>
      )}
    </div>
  )
}
