import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RBACService } from '../services/rbac.service';
import { Permission } from '../dto/rbac.dto';

export const PERMISSIONS_KEY = 'permissions';
export const REQUIRE_ALL_PERMISSIONS_KEY = 'requireAllPermissions';

@Injectable()
export class RBACGuard implements CanActivate {
  private readonly logger = new Logger(RBACGuard.name);

  constructor(
    private readonly rbacService: RBACService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    try {
      // Get required permissions from decorator
      const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(
        PERMISSIONS_KEY,
        [context.getHandler(), context.getClass()],
      );

      // If no permissions required, allow access
      if (!requiredPermissions || requiredPermissions.length === 0) {
        return true;
      }

      // Check if user is authenticated
      const user = request.user;
      if (!user || !user.sub) {
        throw new ForbiddenException('User not authenticated');
      }

      // Check if all permissions are required or just any
      const requireAllPermissions = this.reflector.getAllAndOverride<boolean>(
        REQUIRE_ALL_PERMISSIONS_KEY,
        [context.getHandler(), context.getClass()],
      ) ?? false;

      // Extract resource ID from request parameters if available
      const resourceId = request.params?.id || request.params?.resourceId;

      let hasAccess: boolean;

      if (requireAllPermissions) {
        // User must have ALL required permissions
        hasAccess = await this.rbacService.hasAllPermissions(
          user.sub,
          requiredPermissions,
          resourceId,
        );
      } else {
        // User must have ANY of the required permissions
        hasAccess = await this.rbacService.hasAnyPermission(
          user.sub,
          requiredPermissions,
          resourceId,
        );
      }

      if (!hasAccess) {
        this.logger.warn(`Access denied for user ${user.sub}`, {
          userId: user.sub,
          requiredPermissions,
          requireAllPermissions,
          resourceId,
          path: request.url,
          method: request.method,
        });

        throw new ForbiddenException('Insufficient permissions to access this resource');
      }

      this.logger.debug(`Access granted for user ${user.sub}`, {
        userId: user.sub,
        requiredPermissions,
        requireAllPermissions,
        resourceId,
      });

      return true;

    } catch (error) {
      this.logger.error(`RBAC check failed: ${error.message}`, {
        path: request.url,
        method: request.method,
        userId: request.user?.sub,
        error: error.stack,
      });

      if (error instanceof ForbiddenException) {
        throw error;
      }

      throw new ForbiddenException('Access control check failed');
    }
  }
}
