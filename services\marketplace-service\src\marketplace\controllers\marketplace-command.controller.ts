import {
  Controller,
  Post,
  Body,
  Res,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { MarketplaceCommandService } from '../services/marketplace-command.service';

@ApiTags('Marketplace')
@Controller('marketplace')
export class MarketplaceCommandController {
  private readonly logger = new Logger(MarketplaceCommandController.name);

  constructor(private readonly marketplaceCommandService: MarketplaceCommandService) {}

  @Post('list-nft')
  @ApiOperation({
    summary: 'List NFT for sale',
    description: 'Create a new listing for an NFT in the marketplace'
  })
  @ApiBody({
    description: 'NFT listing data',
    schema: {
      type: 'object',
      properties: {
        nftId: { type: 'string', example: 'nft-12345' },
        price: { type: 'string', example: '1.5' },
        currency: { type: 'string', example: 'ETH' },
        duration: { type: 'number', example: 2592000 },
        category: { type: 'string', example: 'art' }
      },
      required: ['nftId', 'price', 'currency']
    }
  })
  @ApiResponse({ status: 201, description: 'NFT listed successfully' })
  async listNFT(@Body() listingData: any, @Res() res: Response) {
    try {
      this.logger.log('Creating NFT listing');
      
      const result = await this.marketplaceCommandService.listNFT(listingData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`NFT listing failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Post('create-auction')
  @ApiOperation({
    summary: 'Create NFT auction',
    description: 'Create a new auction for an NFT'
  })
  @ApiBody({
    description: 'Auction creation data',
    schema: {
      type: 'object',
      properties: {
        nftId: { type: 'string', example: 'nft-12345' },
        startingPrice: { type: 'string', example: '0.1' },
        reservePrice: { type: 'string', example: '1.0' },
        currency: { type: 'string', example: 'ETH' },
        duration: { type: 'number', example: 604800 }
      },
      required: ['nftId', 'startingPrice', 'currency', 'duration']
    }
  })
  @ApiResponse({ status: 201, description: 'Auction created successfully' })
  async createAuction(@Body() auctionData: any, @Res() res: Response) {
    try {
      this.logger.log('Creating NFT auction');
      
      const result = await this.marketplaceCommandService.createAuction(auctionData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Auction creation failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Post('purchase')
  @ApiOperation({
    summary: 'Purchase NFT',
    description: 'Purchase an NFT from a listing'
  })
  @ApiBody({
    description: 'Purchase data',
    schema: {
      type: 'object',
      properties: {
        listingId: { type: 'string', example: 'listing-12345' },
        paymentMethod: { type: 'string', example: 'crypto' },
        buyerAddress: { type: 'string', example: '******************************************' }
      },
      required: ['listingId', 'paymentMethod', 'buyerAddress']
    }
  })
  @ApiResponse({ status: 201, description: 'Purchase completed successfully' })
  async purchaseNFT(@Body() purchaseData: any, @Res() res: Response) {
    try {
      this.logger.log('Processing NFT purchase');
      
      const result = await this.marketplaceCommandService.purchaseNFT(purchaseData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`NFT purchase failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }
}
