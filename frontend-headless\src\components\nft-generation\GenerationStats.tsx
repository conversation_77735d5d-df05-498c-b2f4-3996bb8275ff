'use client'

import React from 'react'
import {
  SparklesIcon,
  ClockIcon,
  CheckCircleIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { GenerationStats } from '@/types/nft-generation.types'

interface GenerationStatsProps {
  stats: GenerationStats
  className?: string
}

export default function GenerationStatsComponent({ stats, className = '' }: GenerationStatsProps) {
  const statCards = [
    {
      title: 'Total Generated',
      value: stats.totalGenerated.toLocaleString(),
      icon: SparklesIcon,
      color: 'text-purple-600 bg-purple-100',
      description: 'NFTs created'
    },
    {
      title: 'Total Minted',
      value: stats.totalMinted.toLocaleString(),
      icon: CheckCircleIcon,
      color: 'text-green-600 bg-green-100',
      description: 'On blockchain'
    },
    {
      title: 'Total Distributed',
      value: stats.totalDistributed.toLocaleString(),
      icon: TrendingUpIcon,
      color: 'text-blue-600 bg-blue-100',
      description: 'To participants'
    },
    {
      title: 'Success Rate',
      value: `${(stats.successRate * 100).toFixed(1)}%`,
      icon: ChartBarIcon,
      color: 'text-green-600 bg-green-100',
      trend: stats.successRate > 0.8 ? 'up' : stats.successRate > 0.6 ? 'stable' : 'down',
      description: 'Generation success'
    },
    {
      title: 'Avg Quality Score',
      value: stats.averageQualityScore.toFixed(1),
      icon: SparklesIcon,
      color: 'text-yellow-600 bg-yellow-100',
      trend: stats.averageQualityScore > 8 ? 'up' : stats.averageQualityScore > 6 ? 'stable' : 'down',
      description: 'Out of 10'
    },
    {
      title: 'Approval Rate',
      value: `${(stats.approvalRate * 100).toFixed(1)}%`,
      icon: CheckCircleIcon,
      color: 'text-green-600 bg-green-100',
      trend: stats.approvalRate > 0.9 ? 'up' : stats.approvalRate > 0.7 ? 'stable' : 'down',
      description: 'Review approval'
    },
    {
      title: 'Avg Generation Time',
      value: `${Math.round(stats.averageGenerationTime / 60)}m`,
      icon: ClockIcon,
      color: 'text-blue-600 bg-blue-100',
      description: 'Per NFT'
    },
    {
      title: 'Total Cost',
      value: `$${stats.totalCost.toLocaleString()}`,
      icon: CurrencyDollarIcon,
      color: 'text-orange-600 bg-orange-100',
      description: 'Generation costs'
    }
  ]

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon className="h-4 w-4 text-green-600" />
      case 'down':
        return <TrendingDownIcon className="h-4 w-4 text-red-600" />
      default:
        return null
    }
  }

  const getHealthStatus = () => {
    const overallScore = (stats.successRate + stats.approvalRate + (stats.averageQualityScore / 10)) / 3
    
    if (overallScore >= 0.8) {
      return { status: 'Excellent', color: 'text-green-600 bg-green-100', icon: CheckCircleIcon }
    } else if (overallScore >= 0.6) {
      return { status: 'Good', color: 'text-yellow-600 bg-yellow-100', icon: ChartBarIcon }
    } else {
      return { status: 'Needs Attention', color: 'text-red-600 bg-red-100', icon: ExclamationTriangleIcon }
    }
  }

  const healthStatus = getHealthStatus()

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Generation Pipeline Statistics</h2>
        <div className="text-sm text-gray-500">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {statCards.map((stat, index) => (
          <div
            key={index}
            className="relative bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all"
          >
            {/* Icon */}
            <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${stat.color} mb-4`}>
              <stat.icon className="h-6 w-6" />
            </div>

            {/* Value and Title */}
            <div className="mb-2">
              <div className="flex items-baseline space-x-2">
                <h3 className="text-2xl font-bold text-gray-900">{stat.value}</h3>
                {stat.trend && getTrendIcon(stat.trend)}
              </div>
              <p className="text-sm font-medium text-gray-700">{stat.title}</p>
            </div>

            {/* Description */}
            <p className="text-sm text-gray-500">{stat.description}</p>

            {/* Trend Indicator */}
            {stat.trend && (
              <div className="absolute top-4 right-4">
                <div className={`w-2 h-2 rounded-full ${
                  stat.trend === 'up' ? 'bg-green-400' : 
                  stat.trend === 'down' ? 'bg-red-400' : 'bg-yellow-400'
                }`}></div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Provider Performance */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Provider Usage</h3>
          <div className="space-y-3">
            {Object.entries(stats.providerUsage).map(([provider, usage]) => {
              const total = Object.values(stats.providerUsage).reduce((sum, val) => sum + val, 0)
              const percentage = total > 0 ? (usage / total) * 100 : 0
              
              return (
                <div key={provider} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">{provider.replace('_', ' ')}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-purple-600 h-2 rounded-full"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-8 text-right">{usage}</span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Provider Success Rates</h3>
          <div className="space-y-3">
            {Object.entries(stats.providerSuccessRates).map(([provider, rate]) => (
              <div key={provider} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 capitalize">{provider.replace('_', ' ')}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        rate > 0.8 ? 'bg-green-500' : rate > 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${rate * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12 text-right">
                    {(rate * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Cost Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <CurrencyDollarIcon className="h-4 w-4 text-orange-600" />
            <span className="text-sm font-medium text-orange-900">Cost Analysis</span>
          </div>
          <div className="space-y-2 text-sm text-orange-800">
            <div className="flex justify-between">
              <span>Avg per NFT:</span>
              <span className="font-medium">${stats.averageCostPerNFT.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Total spent:</span>
              <span className="font-medium">${stats.totalCost.toLocaleString()}</span>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <ClockIcon className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">Time Analysis</span>
          </div>
          <div className="space-y-2 text-sm text-blue-800">
            <div className="flex justify-between">
              <span>Avg Generation:</span>
              <span className="font-medium">{Math.round(stats.averageGenerationTime / 60)}m</span>
            </div>
            <div className="flex justify-between">
              <span>Avg Minting:</span>
              <span className="font-medium">{Math.round(stats.averageMintingTime / 60)}m</span>
            </div>
          </div>
        </div>

        <div className={`border rounded-lg p-4 ${healthStatus.color.replace('text-', 'border-').replace('bg-', 'bg-opacity-20 border-opacity-50')}`}>
          <div className="flex items-center space-x-2 mb-2">
            <healthStatus.icon className={`h-4 w-4 ${healthStatus.color.split(' ')[0]}`} />
            <span className={`text-sm font-medium ${healthStatus.color.split(' ')[0].replace('text-', 'text-').replace('-600', '-900')}`}>
              Pipeline Health
            </span>
          </div>
          <div className={`text-lg font-bold ${healthStatus.color.split(' ')[0]}`}>
            {healthStatus.status}
          </div>
          <div className={`text-sm ${healthStatus.color.split(' ')[0].replace('text-', 'text-').replace('-600', '-800')}`}>
            {stats.successRate > 0.8 ? 'All systems optimal' : 
             stats.successRate > 0.6 ? 'Minor issues detected' : 'Requires attention'}
          </div>
        </div>
      </div>

      {/* Generation Timeline */}
      {stats.generationOverTime && stats.generationOverTime.length > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Generation Timeline (Last 7 Days)</h3>
          <div className="h-32 flex items-end justify-between space-x-1">
            {stats.generationOverTime.slice(-7).map((day, index) => {
              const maxGenerated = Math.max(...stats.generationOverTime.map(d => d.generated))
              const height = maxGenerated > 0 ? (day.generated / maxGenerated) * 100 : 0
              
              return (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-purple-600 rounded-t"
                    style={{ height: `${height}%`, minHeight: height > 0 ? '4px' : '0px' }}
                    title={`${day.generated} generated on ${day.date}`}
                  ></div>
                  <div className="text-xs text-gray-600 mt-1">
                    {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Quick Insights */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUpIcon className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">Performance Highlight</span>
            </div>
            <p className="text-sm text-green-800">
              {stats.successRate > 0.9 
                ? `Excellent success rate of ${(stats.successRate * 100).toFixed(1)}% with high-quality outputs`
                : stats.successRate > 0.7
                ? `Good success rate of ${(stats.successRate * 100).toFixed(1)}% with room for optimization`
                : `Success rate of ${(stats.successRate * 100).toFixed(1)}% needs improvement`
              }
            </p>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <CurrencyDollarIcon className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Cost Efficiency</span>
            </div>
            <p className="text-sm text-blue-800">
              {stats.averageCostPerNFT < 5
                ? `Excellent cost efficiency at $${stats.averageCostPerNFT.toFixed(2)} per NFT`
                : stats.averageCostPerNFT < 10
                ? `Good cost efficiency at $${stats.averageCostPerNFT.toFixed(2)} per NFT`
                : `High cost per NFT at $${stats.averageCostPerNFT.toFixed(2)} - consider optimization`
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
