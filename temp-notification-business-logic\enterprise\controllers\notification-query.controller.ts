// Enterprise Notification Query Controller (Read Side) - Enhanced
import { <PERSON>, <PERSON>, Param, Query, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus, Lo<PERSON> } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';

// Enhanced notification services
import { NotificationManagementService, NotificationFilters } from '../../notification/services/notification-management.service';
import { NotificationAnalyticsService } from '../../notification/services/notification-analytics.service';
import { EventNotificationService } from '../../notification/services/event-notification.service';

@ApiTags('Notification Queries (Read Operations)')
@Controller('enterprise/notifications')
export class NotificationQueryController {
  private readonly logger = new Logger(NotificationQueryController.name);

  constructor(
    private readonly notificationManagement: NotificationManagementService,
    private readonly notificationAnalytics: NotificationAnalyticsService,
    private readonly eventNotification: EventNotificationService
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get notifications with filters (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  async getNotifications(
    @Query('userId') userId: string,
    @Query('type') type: string,
    @Query('category') category: string,
    @Query('priority') priority: string,
    @Query('status') status: string,
    @Query('dateFrom') dateFrom: string,
    @Query('dateTo') dateTo: string,
    @Query('limit') limit: string,
    @Query('offset') offset: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📧 Get notifications request received`, { correlationId });

      // Build filters
      const filters: NotificationFilters = {};
      if (userId) filters.userId = userId;
      if (type) filters.type = type;
      if (category) filters.category = category;
      if (priority) filters.priority = priority;
      if (status) filters.status = status;
      if (dateFrom) filters.dateFrom = new Date(dateFrom);
      if (dateTo) filters.dateTo = new Date(dateTo);

      const result = await this.notificationManagement.getNotifications(
        filters,
        parseInt(limit) || 50,
        parseInt(offset) || 0
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        data: result,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get notifications failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get(':notificationId')
  @ApiOperation({ summary: 'Get notification by ID (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Notification retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async getNotificationById(
    @Param('notificationId') notificationId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🔍 Get notification by ID request received`, { correlationId, notificationId });

      const notification = await this.notificationManagement.getNotificationById(notificationId);

      if (!notification) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          error: 'Notification not found',
          correlationId
        });
      }

      return res.status(HttpStatus.OK).json({
        success: true,
        data: notification,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get notification by ID failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('users/:userId')
  @ApiOperation({ summary: 'Get user notifications (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'User notifications retrieved successfully' })
  async getUserNotifications(
    @Param('userId') userId: string,
    @Query('limit') limit: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 Get user notifications request received`, { correlationId, userId });

      const notifications = await this.notificationManagement.getNotificationsByUser(
        userId,
        parseInt(limit) || 50
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          userId,
          notifications,
          count: notifications.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get user notifications failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('analytics/overview')
  @ApiOperation({ summary: 'Get notification analytics (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  async getNotificationAnalytics(
    @Query('dateFrom') dateFrom: string,
    @Query('dateTo') dateTo: string,
    @Query('userId') userId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Get notification analytics request received`, { correlationId });

      const analytics = await this.notificationAnalytics.getNotificationAnalytics(
        dateFrom ? new Date(dateFrom) : undefined,
        dateTo ? new Date(dateTo) : undefined,
        userId
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        data: analytics,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get notification analytics failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('analytics/performance')
  @ApiOperation({ summary: 'Get notification performance (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Performance data retrieved successfully' })
  async getNotificationPerformance(
    @Query('limit') limit: string,
    @Query('sortBy') sortBy: 'deliveryTime' | 'engagementTime' | 'clickTime',
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`⚡ Get notification performance request received`, { correlationId });

      const performance = await this.notificationAnalytics.getNotificationPerformance(
        parseInt(limit) || 100,
        sortBy || 'engagementTime'
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          performance,
          count: performance.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get notification performance failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('analytics/dashboard')
  @ApiOperation({ summary: 'Get notification dashboard (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved successfully' })
  async getNotificationDashboard(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Get notification dashboard request received`, { correlationId });

      const dashboard = await this.notificationAnalytics.getNotificationDashboard();

      return res.status(HttpStatus.OK).json({
        success: true,
        data: dashboard,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get notification dashboard failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('templates')
  @ApiOperation({ summary: 'Get notification templates (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getNotificationTemplates(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📋 Get notification templates request received`, { correlationId });

      const templates = this.eventNotification.getTemplates();

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          templates,
          count: templates.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get notification templates failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('templates/:templateId')
  @ApiOperation({ summary: 'Get notification template by ID (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Template retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async getNotificationTemplate(
    @Param('templateId') templateId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📋 Get notification template request received`, { correlationId, templateId });

      const template = this.eventNotification.getTemplate(templateId);

      if (!template) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          error: 'Template not found',
          correlationId
        });
      }

      return res.status(HttpStatus.OK).json({
        success: true,
        data: template,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get notification template failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('stats/:userId?')
  @ApiOperation({ summary: 'Get notification statistics (Enterprise CQRS Query)' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getNotificationStats(
    @Param('userId') userId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📈 Get notification stats request received`, { correlationId, userId });

      const stats = await this.notificationManagement.getNotificationStats(userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: stats,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get notification stats failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for notification service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          service: 'notification-service',
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        },
        correlationId
      });
    } catch (error) {
      return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: 'Service unavailable',
        correlationId: headers['x-correlation-id']
      });
    }
  }
}
