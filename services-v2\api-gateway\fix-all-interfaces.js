#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Helper method to add to all service files
const helperMethod = `
  /**
   * Create metric tags with required properties
   */
  private createMetricTags(additionalTags: Record<string, string | number> = {}) {
    return {
      service: 'api-gateway-v2',
      environment: process.env.NODE_ENV || 'development',
      version: '2.0.0',
      ...additionalTags,
    };
  }`;

// Function to process a file
function processFile(filePath) {
  console.log(`Processing ${filePath}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Add helper method after constructor if it doesn't exist
  if (!content.includes('createMetricTags') && content.includes('constructor(')) {
    content = content.replace(
      /constructor\([^}]+\}\s*\n/,
      match => match + helperMethod + '\n'
    );
  }
  
  // Fix metric calls to use proper tags
  content = content.replace(
    /this\.metrics\.(increment|gauge|histogram|counter)\('([^']+)',\s*\{([^}]+)\}\)/g,
    (match, method, metricName, tags) => {
      // Convert tags to use createMetricTags
      const cleanTags = tags.replace(/\s+/g, ' ').trim();
      return `this.metrics.${method}('${metricName}', this.createMetricTags({${cleanTags}}))`;
    }
  );
  
  // Fix metric calls without tags
  content = content.replace(
    /this\.metrics\.(gauge|histogram)\('([^']+)',\s*([^,\)]+)\);/g,
    (match, method, metricName, value) => {
      return `this.metrics.${method}('${metricName}', ${value}, this.createMetricTags());`;
    }
  );
  
  // Fix LogContext issues - remove invalid properties
  const invalidLogProps = ['key', 'totalInstances', 'strategy', 'failureCount', 'evictedCount', 'expiredCount', 'instanceUrl'];
  invalidLogProps.forEach(prop => {
    const regex = new RegExp(`\\s*${prop}[^,}]*,?`, 'g');
    content = content.replace(regex, '');
  });
  
  // Clean up empty objects and trailing commas
  content = content.replace(/\{\s*,/g, '{');
  content = content.replace(/,\s*\}/g, '}');
  content = content.replace(/\{\s*\}/g, '{}');
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ Fixed ${filePath}`);
}

// Find all service files
function findServiceFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...findServiceFiles(fullPath));
    } else if (item.endsWith('.service.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Main execution
console.log('🔧 Fixing all service interface issues...');

const serviceFiles = findServiceFiles('./src');
serviceFiles.forEach(processFile);

console.log(`✅ Fixed ${serviceFiles.length} service files!`);
