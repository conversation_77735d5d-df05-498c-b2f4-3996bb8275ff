# 🤖 **<PERSON><PERSON><PERSON><PERSON><PERSON> AI AGENT GUIDELINES**

## **🚨 CRITICAL: READ THIS BEFORE ANY IMPLEMENTATION**

### **📋 INTELLIGENT MIGRATION & IMPROVEMENT APPROACH**
**This project follows the "Intelligent Migration & Improvement" methodology for V1 to V2 transformation**

**🔗 MANDATORY READING: [DEVELOPMENT-RULES-AND-STANDARDS.md](./DEVELOPMENT-RULES-AND-STANDARDS.md)**
**🔗 MANDATORY READING: [QUICK-REFERENCE-CARD.md](./QUICK-REFERENCE-CARD.md)**
**🔗 MANDATORY READING: [CLEANUP-RULES-AND-STANDARDS.md](./CLEANUP-RULES-AND-STANDARDS.md)**

---

## 🔍 **STEP 1: MANDATORY PRE-IMPLEMENTATION ANALYSIS**

### **Before writing ANY code, you MUST:**

#### **1.1 Read Migration Documentation (CRITICAL)**
```bash
# ALWAYS start by reading these files IN ORDER:
1. docs/COMPREHENSIVE-V1-TO-V2-MIGRATION-PLAN.md  # MIGRATION STRATEGY
2. docs/DEVELOPMENT-RULES-AND-STANDARDS.md        # DEVELOPMENT RULES
3. docs/IMPLEMENTATION-STATUS.md                  # CURRENT STATUS
4. docs/AI-AGENT-GUIDELINES.md                    # THIS FILE
5. README.md                                      # PROJECT OVERVIEW
6. .env.example                                   # CONFIGURATION
```

#### **1.2 Analyze V1 Implementation (MANDATORY FOR MIGRATION)**
```bash
# Study V1 codebase for business logic extraction:
1. ../social-commerce-refined/services/[service-name]/  # V1 service implementation
2. ../social-commerce-refined/services/api-gateway/     # V1 API Gateway
3. ../social-commerce-refined/frontend/                 # V1 Frontend
4. ../social-commerce-refined/shared/                   # V1 Shared components
5. ../social-commerce-refined/docker-compose.yml        # V1 Deployment
```

#### **1.3 Perform Intelligent Migration Analysis**
```bash
# MANDATORY MIGRATION ANALYSIS PROCESS:

1. ANALYZE V1 BUSINESS LOGIC:
   - What business logic works well? (KEEP)
   - What design issues exist? (FIX)
   - What can be improved? (IMPROVE)
   - What enterprise features are missing? (ENHANCE)

2. STUDY V2 ARCHITECTURE:
   - Review shared/ directory - V2 shared infrastructure
   - Check services/ directory - V2 service patterns
   - Examine package.json - V2 workspace structure
   - Review tsconfig.json - V2 TypeScript configuration

3. PLAN INTELLIGENT MIGRATION:
   - How to preserve V1 business logic?
   - How to fix V1 design issues?
   - How to improve implementation quality?
   - How to integrate V2 enterprise features?
```

#### **1.4 Document Migration Decisions**
```typescript
// MANDATORY: Document your migration analysis
interface MigrationAnalysis {
  // What V1 business logic to preserve
  businessLogicToKeep: {
    workflows: string[];
    validationRules: string[];
    businessRules: string[];
    functionalBehavior: string[];
  };

  // What V1 design issues to fix
  designIssuesToFix: {
    architecturalProblems: string[];
    implementationIssues: string[];
    securityVulnerabilities: string[];
    performanceBottlenecks: string[];
  };

  // What implementation improvements to make
  improvementsToMake: {
    codeQuality: string[];
    performance: string[];
    maintainability: string[];
    testability: string[];
  };

  // What enterprise features to add
  enterpriseEnhancements: {
    securityFeatures: string[];
    scalabilityFeatures: string[];
    observabilityFeatures: string[];
    devopsFeatures: string[];
  };
}
```

---

## 🎯 **STEP 2: INTELLIGENT MIGRATION IMPLEMENTATION**

### **2.1 MIGRATION IMPLEMENTATION METHODOLOGY**
```typescript
/**
 * INTELLIGENT MIGRATION APPROACH:
 * 🔧 KEEP: Proven business logic and functional behavior
 * 🚨 FIX: Architectural problems and design mistakes
 * 🔄 IMPROVE: Implementation quality and performance
 * 🚀 ENHANCE: Add missing enterprise features and best practices
 */

// EXAMPLE: User Service Migration
class UserServiceV2 {
  constructor(
    // ENHANCE: Use V2 shared infrastructure
    private readonly responseService: ResponseService,
    private readonly logger: ServiceLoggerService,
    private readonly configService: StandardizedConfigService,
    // IMPROVE: Use repository pattern instead of direct Prisma
    private readonly userRepository: UserRepository
  ) {}

  // KEEP: V1 registration workflow logic
  // FIX: V1 hardcoded configuration → V2 dynamic configuration
  // IMPROVE: V1 basic validation → V2 comprehensive validation
  // ENHANCE: Add MFA support using V2 authentication infrastructure
  async register(registerDto: RegisterDto): Promise<StandardResponse<UserResponseDto>> {
    // KEEP: V1 registration business logic
    const registrationWorkflow = this.preserveV1RegistrationLogic(registerDto);

    // FIX: V1 hardcoded secrets → V2 configuration service
    const jwtConfig = this.configService.getJwtConfig(); // Fixed!

    // IMPROVE: V1 basic validation → V2 comprehensive validation
    await this.improvedValidation(registerDto); // Improved!

    // ENHANCE: Add V2 enterprise features
    await this.addEnterpriseFeatures(user); // Enhanced!

    return this.responseService.success(user, 'User registered successfully');
  }
}

// ❌ FORBIDDEN MIGRATION APPROACHES:
class BadMigrationExample {
  // ❌ Copy-paste V1 code without analysis
  async register(data: any) {
    // Direct copy from V1 - FORBIDDEN!
  }

  // ❌ Start from scratch without preserving business logic
  async register(data: any) {
    // Completely new implementation - FORBIDDEN!
  }

  // ❌ Custom implementations instead of V2 shared infrastructure
  private customResponseHandler() { ... } // FORBIDDEN!
}
```

### **2.2 ALWAYS Use V2 Shared Infrastructure**
```typescript
// ✅ CORRECT: Import from V2 shared infrastructure
import { ResponseService } from '@shared/responses/services/response.service';
import { ServiceLoggerService } from '@shared/logging/services/service-logger.service';
import { StandardizedConfigService } from '@shared/config/services/config.service';
import { StandardizedJwtAuthGuard } from '@shared/auth/guards/jwt-auth.guard';

// ❌ NEVER: Create custom implementations or copy V1 patterns
class MyCustomResponseService { ... } // FORBIDDEN!
class V1StyleResponseHandler { ... } // FORBIDDEN!
```

### **2.3 ALWAYS Follow V2 Established Patterns**
```typescript
// ✅ CORRECT: V2 Controller pattern with migration improvements
@Controller('users')
@UseGuards(StandardizedJwtAuthGuard, PermissionsGuard)
export class UserControllerV2 {
  constructor(
    private readonly userService: UserServiceV2,
    private readonly responseService: ResponseService,
    private readonly logger: ServiceLoggerService
  ) {}

  // KEEP: V1 endpoint concept
  // IMPROVE: V1 basic response → V2 standardized response with correlation tracking
  @Get()
  @RequireUserRead()
  async findAll(
    @CurrentUser() user: AuthenticatedUser,
    @CorrelationId() correlationId: string
  ): Promise<StandardResponse<UserResponseDto[]>> {
    const startTime = Date.now();

    try {
      // KEEP: V1 business logic for getting users
      // IMPROVE: V1 direct service call → V2 with enhanced logging
      const users = await this.userService.findAll(correlationId);

      this.logger.logBusinessEvent({
        message: 'Users retrieved successfully',
        eventType: EventType.USER_LIST_ACCESS,
        domain: 'user',
        action: 'find_all',
        outcome: BusinessOutcome.SUCCESS,
        correlationId,
        context: { requestedBy: user.id, count: users.length }
      });

      return this.responseService.success(users, 'Users retrieved successfully', {
        correlationId,
        duration: Date.now() - startTime
      });

    } catch (error) {
      this.logger.logBusinessEvent({
        message: `Failed to retrieve users: ${error.message}`,
        eventType: EventType.USER_LIST_ACCESS,
        domain: 'user',
        action: 'find_all',
        outcome: BusinessOutcome.FAILURE,
        correlationId,
        context: { requestedBy: user.id, error: error.message }
      });

      throw error;
    }
  }
}

// ❌ FORBIDDEN: V1-style patterns or custom implementations
@Controller('users')
export class BadUserController {
  @Get()
  async findAll() {
    return { data: users }; // FORBIDDEN! No standardized response
  }

  @Get()
  async findAllV1Style() {
    console.log('Getting users'); // FORBIDDEN! No proper logging
    const users = await this.prisma.user.findMany(); // FORBIDDEN! Direct DB access
    return users; // FORBIDDEN! No response standardization
  }
}
```

### **2.4 MANDATORY Comprehensive Logging (Per Development Rules)**
```typescript
// ✅ CORRECT: V2 Enhanced logging with migration context
@Injectable()
export class UserServiceV2 {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly responseService: ResponseService,
    private readonly logger: ServiceLoggerService
  ) {}

  // INTELLIGENT MIGRATION: Enhanced V1 user creation with V2 logging
  async createUser(
    userData: CreateUserDto,
    currentUser: AuthenticatedUser,
    correlationId: string
  ): Promise<StandardResponse<UserResponseDto>> {
    const operation = this.logger.startOperation('user_creation', {
      userId: currentUser.id,
      correlationId,
      migrationContext: 'v1_to_v2_enhanced'
    });

    try {
      // KEEP: V1 user creation business logic
      // IMPROVE: V1 basic creation → V2 with enhanced validation and logging
      const user = await this.userRepository.create(userData);

      // V2 ENHANCEMENT: Comprehensive business event logging
      this.logger.logBusinessEvent({
        message: 'User created successfully with V2 enhancements',
        eventType: EventType.USER_REGISTRATION,
        domain: 'user',
        action: 'create',
        outcome: BusinessOutcome.SUCCESS,
        resourceId: user.id,
        correlationId,
        context: {
          createdBy: currentUser.id,
          migrationSource: 'v1_business_logic_enhanced',
          enhancementsApplied: ['validation', 'audit_logging', 'security_checks']
        }
      });

      // V2 ENHANCEMENT: Performance metrics
      this.logger.logPerformanceMetric({
        message: 'User creation performance',
        operation: 'user_creation',
        duration: operation.getDuration(),
        success: true,
        correlationId,
        context: { userId: user.id }
      });

      operation.end();

      return this.responseService.created(user, 'User created successfully', {
        correlationId,
        resourceId: user.id
      });

    } catch (error) {
      // V2 ENHANCEMENT: Comprehensive error logging
      this.logger.logBusinessEvent({
        message: `User creation failed: ${error.message}`,
        eventType: EventType.USER_REGISTRATION,
        domain: 'user',
        action: 'create',
        outcome: BusinessOutcome.FAILURE,
        correlationId,
        context: {
          createdBy: currentUser.id,
          error: error.message,
          errorType: error.constructor.name,
          userData: { email: userData.email } // Safe logging without sensitive data
        }
      });

      operation.end();
      throw error;
    }
  }
}

// ❌ FORBIDDEN: V1-style logging or no logging
async createUserBadExample(userData: CreateUserDto) {
  console.log('Creating user'); // FORBIDDEN! Use ServiceLoggerService

  try {
    const user = await this.userRepository.create(userData);
    return user; // FORBIDDEN! No business event logging
  } catch (error) {
    console.error(error); // FORBIDDEN! No structured error logging
    throw error;
  }
}
```

### **2.5 MANDATORY Repository Pattern (Per Development Rules)**
```typescript
// ✅ CORRECT: V2 Repository pattern with migration enhancements
@Injectable()
export class UserRepositoryV2 extends BaseRepository<User, CreateUserDto, UpdateUserDto> {
  constructor(
    private readonly prisma: PrismaService,
    logger: ServiceLoggerService
  ) {
    super(logger, 'User');
  }

  protected getModel() {
    return this.prisma.user;
  }

  protected toEntity(data: any): User {
    return new User(data);
  }

  // KEEP: V1 business logic for finding by email
  // IMPROVE: V1 basic query → V2 with enhanced logging and error handling
  async findByEmail(email: string, correlationId?: string): Promise<User | null> {
    try {
      this.logger.logPerformanceMetric({
        message: 'Finding user by email',
        operation: 'user_find_by_email',
        correlationId,
        context: { email }
      });

      const data = await this.getModel().findUnique({
        where: { email },
        // V2 ENHANCEMENT: Include related data for better performance
        include: {
          profile: true,
          stores: true
        }
      });

      const user = data ? this.toEntity(data) : null;

      this.logger.logBusinessEvent({
        message: user ? 'User found by email' : 'User not found by email',
        eventType: EventType.USER_LOOKUP,
        domain: 'user',
        action: 'find_by_email',
        outcome: user ? BusinessOutcome.SUCCESS : BusinessOutcome.NOT_FOUND,
        resourceId: user?.id,
        correlationId,
        context: { email, found: !!user }
      });

      return user;
    } catch (error) {
      this.logger.logBusinessEvent({
        message: `Failed to find user by email: ${error.message}`,
        eventType: EventType.USER_LOOKUP,
        domain: 'user',
        action: 'find_by_email',
        outcome: BusinessOutcome.FAILURE,
        correlationId,
        context: { email, error: error.message }
      });
      throw error;
    }
  }

  // V2 ENHANCEMENT: Advanced query methods with business context
  async findActiveUsersByRole(
    role: UserRole,
    correlationId?: string
  ): Promise<User[]> {
    try {
      const users = await this.getModel().findMany({
        where: {
          role,
          isActive: true,
          deletedAt: null
        },
        include: {
          profile: true
        }
      });

      this.logger.logBusinessEvent({
        message: `Found ${users.length} active users with role ${role}`,
        eventType: EventType.USER_QUERY,
        domain: 'user',
        action: 'find_by_role',
        outcome: BusinessOutcome.SUCCESS,
        correlationId,
        context: { role, count: users.length }
      });

      return users.map(user => this.toEntity(user));
    } catch (error) {
      this.logger.error(`Failed to find users by role: ${error.message}`, error, {
        correlationId,
        context: { role }
      });
      throw error;
    }
  }
}

// ❌ FORBIDDEN: Direct Prisma usage or V1-style patterns
@Injectable()
export class BadUserService {
  constructor(private readonly prisma: PrismaService) {}

  async findAll() {
    return this.prisma.user.findMany(); // FORBIDDEN! Direct Prisma usage
  }

  async findByEmailV1Style(email: string) {
    const user = await this.prisma.user.findUnique({ where: { email } });
    return user; // FORBIDDEN! No logging, no error handling, no entity conversion
  }
}
```

### **2.6 MANDATORY Git Workflow (Per Development Rules)**
```typescript
// ✅ CORRECT: Git workflow for AI agents and developers
class GitWorkflowExample {
  async implementFeature() {
    // 1. Start with clean working directory
    // git status (ensure clean)

    // 2. Create feature branch for major work
    // git checkout -b feature/api-gateway-v2-migration

    // 3. Implement in small chunks (TRUE Template-First)
    await this.implementBusinessLogicAnalysis(); // 30 lines max
    // git add . && git commit -m "feat: add API gateway business logic analysis"

    await this.implementDesignIssuesAnalysis(); // 30 lines max
    // git add . && git commit -m "feat: add API gateway design issues analysis"

    await this.implementMigrationStrategy(); // 30 lines max
    // git add . && git commit -m "feat: add API gateway migration strategy"

    // 4. Major milestone commit
    // git add . && git commit -m "feat: complete Phase 0 API Gateway analysis
    //
    // - Business logic analysis (KEEP, FIX, IMPROVE, ENHANCE)
    // - Design issues identification and prioritization
    // - Migration strategy with 3-phase approach
    // - Documentation following TRUE Template-First approach"
  }
}

// ❌ FORBIDDEN: Large commits without regular checkpoints
async badGitWorkflow() {
  // Implement entire feature without commits - FORBIDDEN!
  await this.implementMassiveFeature(); // 500+ lines
  // git add . && git commit -m "feat: everything" - FORBIDDEN!
}
```

### **2.7 MANDATORY Error Handling (Per Development Rules)**
```typescript
// ✅ CORRECT: V2 Comprehensive error handling
@Injectable()
export class UserServiceV2 {
  constructor(
    private readonly userRepository: UserRepositoryV2,
    private readonly responseService: ResponseService,
    private readonly logger: ServiceLoggerService
  ) {}

  async getUserById(
    id: string,
    correlationId: string
  ): Promise<StandardResponse<UserResponseDto>> {
    try {
      // KEEP: V1 business logic for getting user by ID
      // IMPROVE: V1 basic error handling → V2 comprehensive error handling
      const user = await this.userRepository.findById(id, correlationId);

      if (!user) {
        // V2 ENHANCEMENT: Structured not found handling
        this.logger.logBusinessEvent({
          message: 'User not found',
          eventType: EventType.USER_LOOKUP,
          domain: 'user',
          action: 'get_by_id',
          outcome: BusinessOutcome.NOT_FOUND,
          correlationId,
          context: { userId: id }
        });

        return this.responseService.notFound('User not found', correlationId);
      }

      return this.responseService.success(user, 'User retrieved successfully', {
        correlationId,
        resourceId: user.id
      });

    } catch (error) {
      // V2 ENHANCEMENT: Comprehensive error categorization and handling
      if (error instanceof ValidationError) {
        return this.responseService.badRequest(error.message, correlationId, {
          validationErrors: error.details
        });
      }

      if (error instanceof DatabaseError) {
        this.logger.logBusinessEvent({
          message: `Database error while getting user: ${error.message}`,
          eventType: EventType.DATABASE_ERROR,
          domain: 'user',
          action: 'get_by_id',
          outcome: BusinessOutcome.FAILURE,
          correlationId,
          context: { userId: id, errorType: 'database' }
        });

        return this.responseService.internalServerError(
          'Database error occurred',
          correlationId
        );
      }

      // Generic error handling
      this.logger.logBusinessEvent({
        message: `Unexpected error while getting user: ${error.message}`,
        eventType: EventType.SYSTEM_ERROR,
        domain: 'user',
        action: 'get_by_id',
        outcome: BusinessOutcome.FAILURE,
        correlationId,
        context: { userId: id, error: error.message }
      });

      return this.responseService.internalServerError(
        'An unexpected error occurred',
        correlationId
      );
    }
  }
}

// ❌ FORBIDDEN: Basic error handling or no error handling
async getUserByIdBadExample(id: string) {
  const user = await this.userRepository.findById(id);
  if (!user) {
    throw new Error('User not found'); // FORBIDDEN! No structured error handling
  }
  return user; // FORBIDDEN! No response standardization
}
```

---

## 📋 **STEP 3: MANDATORY MIGRATION CHECKLIST**

### **Before Submitting ANY Migration Code:**

```
🔍 MIGRATION ANALYSIS CHECKLIST:
□ Read docs/COMPREHENSIVE-V1-TO-V2-MIGRATION-PLAN.md
□ Read docs/DEVELOPMENT-RULES-AND-STANDARDS.md
□ Analyzed V1 implementation thoroughly
□ Identified business logic to preserve (KEEP)
□ Identified design issues to fix (FIX)
□ Identified improvements to make (IMPROVE)
□ Identified enterprise features to add (ENHANCE)
□ Documented migration decisions and rationale
□ Planned integration with V2 shared infrastructure

💻 MIGRATION IMPLEMENTATION CHECKLIST:
□ Preserves V1 business logic and functional behavior
□ Fixes identified V1 design issues
□ Improves implementation quality over V1
□ Uses V2 shared infrastructure components
□ Follows established V2 naming conventions
□ Includes proper error handling with ResponseService
□ Has correlation ID tracking in all operations
□ Uses standardized response format
□ Includes comprehensive logging with ServiceLoggerService
□ Has proper authentication/authorization decorators
□ Follows repository pattern for data access
□ Adds appropriate enterprise features
□ Includes unit and integration tests
□ Updates migration documentation
□ Regular Git commits after each major progress
□ Descriptive commit messages with implementation details

🚫 MIGRATION REJECTION CRITERIA:
□ No V1 implementation analysis
□ Copy-paste V1 code without improvement
□ Ignores V1 design issues
□ Starts from scratch without preserving business logic
□ Business logic regression from V1
□ Custom authentication (use StandardizedJwtAuthGuard)
□ Custom response handling (use ResponseService)
□ Bypassing V2 shared infrastructure
□ Inconsistent naming conventions
□ Missing error handling
□ Missing correlation ID tracking
□ Missing logging implementation
□ Direct database access (use repositories)
□ Missing tests
□ Temporary fixes or TODO comments
□ No migration documentation updates
□ No Git commits during implementation
□ Poor commit messages without context
□ Large commits without incremental progress
```

---

## 🏗️ **STEP 4: SERVICE CREATION GUIDELINES**

### **4.1 If Creating a New Service:**
```bash
# Follow this exact structure:
services/[service-name]/
├── src/
│   ├── features/[entity]/
│   │   ├── controllers/[entity].controller.ts
│   │   ├── services/[entity].service.ts
│   │   ├── repositories/[entity].repository.ts
│   │   ├── dto/
│   │   │   ├── create-[entity].dto.ts
│   │   │   └── update-[entity].dto.ts
│   │   └── entities/[entity].entity.ts
│   ├── health/health.controller.ts
│   ├── config/
│   ├── app.module.ts
│   └── main.ts
├── package.json
├── Dockerfile
├── .env.example
└── README.md
```

### **4.2 Service Module Template:**
```typescript
// ✅ CORRECT: Service module structure
import { Module } from '@nestjs/common';
import { SharedModule } from '@shared/shared.module';
import { UserController } from './features/user/controllers/user.controller';
import { UserService } from './features/user/services/user.service';
import { UserRepository } from './features/user/repositories/user.repository';
import { HealthController } from './health/health.controller';

@Module({
  imports: [SharedModule],
  controllers: [UserController, HealthController],
  providers: [UserService, UserRepository],
})
export class AppModule {}
```

---

## 🔄 **STEP 5: INTEGRATION GUIDELINES**

### **5.1 Service-to-Service Communication:**
```typescript
// ✅ CORRECT: HTTP communication with proper error handling
@Injectable()
export class UserService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: StandardizedConfigService,
    private readonly logger: ServiceLoggerService
  ) {}

  async getStoresByUserId(userId: string, correlationId: string): Promise<Store[]> {
    try {
      const storeServiceUrl = this.configService.getExternalServiceConfig('store-service').url;
      
      const response = await firstValueFrom(
        this.httpService.get(`${storeServiceUrl}/stores/user/${userId}`, {
          headers: {
            'X-Correlation-ID': correlationId,
            'Authorization': `Bearer ${this.getServiceToken()}`
          }
        })
      );

      this.logger.logPerformanceMetric({
        message: 'Store service call completed',
        operation: 'get_stores_by_user',
        duration: Date.now() - startTime,
        success: true,
        context: { correlationId, userId }
      });

      return response.data.data;
    } catch (error) {
      this.logger.error('Store service call failed', error, { correlationId, userId });
      throw new ServiceUnavailableException('Store service unavailable');
    }
  }
}

// ❌ NEVER: Direct service calls without proper error handling
```

---

## 📊 **STEP 6: TESTING REQUIREMENTS**

### **6.1 Mandatory Test Structure:**
```typescript
// ✅ CORRECT: Comprehensive test structure
describe('UserController', () => {
  let controller: UserController;
  let service: UserService;
  let responseService: ResponseService;
  let logger: ServiceLoggerService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: {
            findAll: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: ResponseService,
          useValue: {
            success: jest.fn(),
            created: jest.fn(),
          },
        },
        {
          provide: ServiceLoggerService,
          useValue: {
            logBusinessEvent: jest.fn(),
            startOperation: jest.fn(() => ({ end: jest.fn() })),
          },
        },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    service = module.get<UserService>(UserService);
    responseService = module.get<ResponseService>(ResponseService);
    logger = module.get<ServiceLoggerService>(ServiceLoggerService);
  });

  describe('findAll', () => {
    it('should return standardized response', async () => {
      const mockUsers = [{ id: '1', email: '<EMAIL>' }];
      const mockResponse = { success: true, data: mockUsers };
      
      jest.spyOn(service, 'findAll').mockResolvedValue(mockUsers);
      jest.spyOn(responseService, 'success').mockReturnValue(mockResponse);

      const result = await controller.findAll({} as AuthenticatedUser);

      expect(service.findAll).toHaveBeenCalled();
      expect(responseService.success).toHaveBeenCalledWith(mockUsers, 'Users retrieved successfully');
      expect(result).toEqual(mockResponse);
    });
  });
});
```

---

## 🚨 **CRITICAL MIGRATION REMINDERS**

### **🔧 INTELLIGENT MIGRATION METHODOLOGY (MANDATORY)**
**Follow the "KEEP, FIX, IMPROVE, ENHANCE" approach from DEVELOPMENT-RULES-AND-STANDARDS.md**

#### **🔧 KEEP (Preserve Business Value)**
- ✅ **Proven business logic** - Registration workflows, authentication flows, validation rules
- ✅ **Functional behavior** - Same inputs produce same outputs
- ✅ **Working patterns** - Successful V1 patterns that deliver business value
- ✅ **Data relationships** - Entity relationships and business rules
- ✅ **API contracts** - Endpoint behavior and response structures

#### **🚨 FIX (Resolve Design Issues)**
- ✅ **Hardcoded configurations** → Dynamic configuration management
- ✅ **Basic error handling** → Comprehensive error categorization
- ✅ **No rate limiting** → Advanced rate limiting per user/service/global
- ✅ **Simple circuit breaker** → Enterprise circuit breaker with bulkhead pattern
- ✅ **Basic logging** → Structured logging with correlation IDs
- ✅ **No connection pooling** → Optimized database connection management
- ✅ **Weak security** → Strong authentication, validation, and encryption

#### **🔄 IMPROVE (Enhance Implementation Quality)**
- ✅ **Performance optimization** - Caching, query optimization, connection pooling
- ✅ **Code structure** - Better patterns, organization, and maintainability
- ✅ **Error handling** - Standardized responses, proper categorization
- ✅ **Monitoring** - Comprehensive observability and metrics collection
- ✅ **Testing** - Unit, integration, and E2E test coverage
- ✅ **Documentation** - API documentation, code comments, architecture docs

#### **🚀 ENHANCE (Add Enterprise Features)**
- ✅ **Advanced security** - MFA, OAuth2, API key management, secrets management
- ✅ **Scalability features** - Load balancing, auto-scaling, multi-region support
- ✅ **Event-driven architecture** - Event sourcing, CQRS, saga patterns
- ✅ **Advanced caching** - Multi-level caching with intelligent invalidation
- ✅ **Real-time features** - WebSocket integration, live notifications
- ✅ **DevOps automation** - CI/CD pipelines, automated testing, deployment

### **❌ MIGRATION APPROACH - NEVER DO:**
- ❌ Copy-paste V1 code without analysis and improvement
- ❌ Ignore V1 design issues and implementation problems
- ❌ Start from scratch without preserving proven business logic
- ❌ Lose V1 functionality during migration
- ❌ Create custom implementations instead of using V2 shared infrastructure
- ❌ Skip V1 business logic analysis
- ❌ Implement without understanding V1 → V2 improvement plan

### **❌ IMPLEMENTATION QUALITY - NEVER DO:**
- ❌ Create custom authentication logic (use StandardizedJwtAuthGuard)
- ❌ Bypass V2 shared infrastructure (use ResponseService, ServiceLoggerService, etc.)
- ❌ Use console.log for logging (use ServiceLoggerService with correlation IDs)
- ❌ Create custom response formats (use ResponseService)
- ❌ Access database directly without repositories (use BaseRepository pattern)
- ❌ Ignore correlation ID tracking (mandatory in all operations)
- ❌ Skip comprehensive error handling (use try-catch with proper categorization)
- ❌ Create temporary fixes or TODO comments
- ❌ Start implementation without migration analysis

### **✅ MANDATORY IMPLEMENTATION STANDARDS:**
- ✅ **Read DEVELOPMENT-RULES-AND-STANDARDS.md** before any implementation
- ✅ **Analyze V1 implementation** thoroughly before starting migration
- ✅ **Document migration decisions** and rationale for each choice
- ✅ **Use V2 shared infrastructure** components exclusively
- ✅ **Follow established V2 patterns** from shared infrastructure
- ✅ **Include comprehensive logging** with ServiceLoggerService and correlation tracking
- ✅ **Implement proper error handling** with ResponseService and error categorization
- ✅ **Follow repository pattern** for all data access operations
- ✅ **Include comprehensive tests** (unit, integration, E2E)
- ✅ **Update migration documentation** with implementation details
- ✅ **Commit regularly to Git** after each major progress and implementation milestone

---

## 📚 **MANDATORY DOCUMENTATION LINKS**

### **🔗 CRITICAL - MUST READ BEFORE ANY IMPLEMENTATION:**

#### **📋 Primary Documentation (Read in Order):**
1. **[DEVELOPMENT-RULES-AND-STANDARDS.md](./DEVELOPMENT-RULES-AND-STANDARDS.md)**
   - **CRITICAL**: Complete development rules and standards
   - **Contains**: Intelligent Migration methodology, AI agent rules, code quality gates
   - **Status**: MANDATORY for all developers and AI agents

2. **[COMPREHENSIVE-V1-TO-V2-MIGRATION-PLAN.md](./COMPREHENSIVE-V1-TO-V2-MIGRATION-PLAN.md)**
   - **CRITICAL**: Complete migration strategy and implementation plan
   - **Contains**: Phase-by-phase migration approach, business logic analysis, improvement strategies
   - **Status**: MANDATORY for understanding migration approach

3. **[AI-AGENT-GUIDELINES.md](./AI-AGENT-GUIDELINES.md)**
   - **CRITICAL**: This document - AI agent specific implementation guidelines
   - **Contains**: Step-by-step implementation process, examples, checklists
   - **Status**: MANDATORY for all AI agents

4. **[DOCUMENTATION-STRATEGY-AND-CLASSIFICATION.md](./DOCUMENTATION-STRATEGY-AND-CLASSIFICATION.md)**
   - **CRITICAL**: Documentation standards and TRUE Template-First approach
   - **Contains**: Naming conventions, structure guidelines, classification rules
   - **Status**: MANDATORY for all file operations and documentation

#### **📊 Status and Reference Documentation:**
4. **[IMPLEMENTATION-STATUS.md](./IMPLEMENTATION-STATUS.md)** - Current implementation progress
5. **[README.md](../README.md)** - Project overview and setup instructions
6. **[.env.example](../.env.example)** - Environment configuration standards

#### **🔍 V1 Analysis Documentation (For Migration):**
7. **V1 Codebase**: `../social-commerce-refined/` - Study for business logic extraction
8. **V1 Services**: `../social-commerce-refined/services/` - Analyze service implementations
9. **V1 API Gateway**: `../social-commerce-refined/services/api-gateway/` - Study routing patterns
10. **V1 Frontend**: `../social-commerce-refined/frontend/` - Analyze UI patterns and flows

#### **🏗️ V2 Architecture Reference:**
11. **V2 Shared Infrastructure**: `shared/` - V2 enterprise patterns and components
12. **V2 Services**: `services/` - V2 service implementations and patterns
13. **V2 Configuration**: `package.json`, `tsconfig.json` - V2 workspace standards

---

### **🚨 ENFORCEMENT NOTICE**

**ALL AI AGENTS MUST:**
- ✅ Read and understand ALL mandatory documentation before implementation
- ✅ Follow the Intelligent Migration & Improvement approach
- ✅ Use V2 shared infrastructure exclusively
- ✅ Document migration decisions and rationale
- ✅ Ensure no business logic regression from V1

**CODE WILL BE REJECTED IF:**
- ❌ Documentation not read or understood
- ❌ V1 analysis not performed
- ❌ Migration approach not followed
- ❌ V2 standards not implemented
- ❌ Business logic regression detected

---

## **🧹 STEP 6: MANDATORY CLEANUP AFTER ANY CHANGE**

### **🚨 CRITICAL: CLEANUP IS MANDATORY AFTER ANY IMPLEMENTATION**

**Following [CLEANUP-RULES-AND-STANDARDS.md](./CLEANUP-RULES-AND-STANDARDS.md) is MANDATORY for ALL AI agents and developers.**

#### **✅ MANDATORY CLEANUP CHECKLIST BEFORE COMMITTING:**

```bash
# 🗑️ File Structure Cleanup
- [ ] No files in wrong directories
- [ ] No duplicate directories or files
- [ ] No temporary or backup files (*.tmp, *.backup, *_old.*)
- [ ] No empty directories
- [ ] No IDE-specific files (unless project-wide)

# 💻 Code Quality Cleanup
- [ ] No unused imports
- [ ] No commented-out code
- [ ] No duplicate functions or logic
- [ ] No debug console.log statements
- [ ] No TODO comments without tickets

# 📦 Dependencies Cleanup
- [ ] No unused dependencies in package.json
- [ ] No deprecated packages
- [ ] No security vulnerabilities
- [ ] No duplicate dependencies across services

# 🧪 Testing Cleanup
- [ ] No obsolete test files
- [ ] No duplicate test logic
- [ ] No test artifacts or temporary files
- [ ] Coverage reports in correct location

# 📚 Documentation Cleanup
- [ ] No outdated documentation
- [ ] No duplicate documentation files
- [ ] No broken links or references
- [ ] README files updated and accurate
```

#### **🛠️ AUTOMATED CLEANUP COMMANDS:**
```bash
# Run before every commit
find . -name "*.tmp" -delete
find . -name "*.temp" -delete
find . -name "*.backup" -delete
find . -name "*_old.*" -delete
find . -type d -empty -delete

# Check dependencies
npx depcheck

# Format and lint
npm run lint:fix
npm run format
```

#### **⚠️ ENFORCEMENT:**
- **ALL commits** must pass cleanup checklist
- **ALL AI agents** must run cleanup after any changes
- **ALL pull requests** must include cleanup verification
- **ZERO TOLERANCE** for duplicate code, dead code, or wrong structure

---

**🚀 SUCCESS = Following the Intelligent Migration & Improvement approach ensures we preserve business value while achieving enterprise-grade quality!**

**📖 Remember: This is not just migration - it's intelligent improvement that keeps what works, fixes what's broken, improves what's basic, and enhances with enterprise features!**

**🧹 CLEANUP = A clean codebase is a maintainable codebase. Every cleanup action contributes to long-term project success!**
