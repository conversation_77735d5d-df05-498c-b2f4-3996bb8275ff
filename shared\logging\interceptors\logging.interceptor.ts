/**
 * Logging Interceptor
 * Provides comprehensive request/response logging with correlation tracking
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { StructuredLoggerService } from '../services/structured-logger.service';
import { MetricsService } from '../services/metrics.service';
import {
  LogContext,
  RequestContext,
  ResponseContext,
  PerformanceMetrics,
  SecurityContext,
  SecurityEventType,
  SecuritySeverity,
  SecurityOutcome,
} from '../interfaces/logger.interface';
import {
  PerformanceMetricCategory,
  SecurityMetricCategory,
} from '../interfaces/metrics.interface';

/**
 * Comprehensive logging interceptor
 */
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  constructor(
    private readonly structuredLogger: StructuredLoggerService,
    private readonly metricsService: MetricsService,
    private readonly configService: ConfigService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const startTime = Date.now();

    // Generate correlation ID if not present
    const correlationId = this.getOrGenerateCorrelationId(request);
    request.correlationId = correlationId;

    // Build request context
    const requestContext = this.buildRequestContext(request);
    const logContext: LogContext = {
      correlationId,
      service: this.configService.get<string>('SERVICE_NAME') || 'unknown-service',
      operation: `${request.method} ${request.route?.path || request.path}`,
      request: requestContext,
      performance: {
        startTime,
        duration: 0,
      },
    };

    // Log incoming request
    this.logIncomingRequest(logContext);

    // Record request metrics
    this.recordRequestMetrics(request, startTime);

    return next.handle().pipe(
      tap((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // Build response context
        const responseContext = this.buildResponseContext(response, data);
        
        // Update log context with response and performance data
        const completeLogContext: LogContext = {
          ...logContext,
          response: responseContext,
          performance: {
            ...logContext.performance!,
            endTime,
            duration,
          },
        };

        // Log successful response
        this.logSuccessfulResponse(completeLogContext, duration);

        // Record response metrics
        this.recordResponseMetrics(request, response, duration, 'success');
      }),
      catchError((error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // Build error context
        const errorLogContext: LogContext = {
          ...logContext,
          error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
            code: error.code,
            statusCode: error.status || error.statusCode || 500,
          },
          performance: {
            ...logContext.performance!,
            endTime,
            duration,
          },
        };

        // Log error response
        this.logErrorResponse(errorLogContext, error, duration);

        // Record error metrics
        this.recordResponseMetrics(request, response, duration, 'error');
        this.recordErrorMetrics(error, logContext);

        // Check for security events
        this.checkSecurityEvents(error, request, logContext);

        throw error;
      })
    );
  }

  /**
   * Get or generate correlation ID
   */
  private getOrGenerateCorrelationId(request: Request): string {
    return (
      request.headers['x-correlation-id'] as string ||
      request.headers['x-request-id'] as string ||
      `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    );
  }

  /**
   * Build request context
   */
  private buildRequestContext(request: Request): RequestContext {
    return {
      method: request.method,
      path: request.path,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ipAddress: this.getClientIP(request),
      headers: request.headers as Record<string, string>,
      query: request.query as Record<string, any>,
      params: request.params,
      body: this.shouldLogBody() ? request.body : undefined,
      contentType: request.headers['content-type'],
      contentLength: request.headers['content-length'] ? parseInt(request.headers['content-length']) : undefined,
      referer: request.headers['referer'],
    };
  }

  /**
   * Build response context
   */
  private buildResponseContext(response: Response, data: any): ResponseContext {
    return {
      statusCode: response.statusCode,
      contentType: response.getHeader('content-type') as string,
      contentLength: response.getHeader('content-length') ? parseInt(response.getHeader('content-length') as string) : undefined,
      headers: response.getHeaders() as Record<string, string>,
      body: this.shouldLogResponseBody() ? data : undefined,
      cached: response.getHeader('x-cache-status') === 'hit',
      compressed: !!response.getHeader('content-encoding'),
    };
  }

  /**
   * Log incoming request
   */
  private logIncomingRequest(context: LogContext): void {
    const { method, path, userAgent, ipAddress } = context.request!;
    
    this.structuredLogger.info(
      `Incoming request: ${method} ${path}`,
      {
        ...context,
        metadata: {
          userAgent,
          ipAddress,
          timestamp: new Date().toISOString(),
        },
      }
    );
  }

  /**
   * Log successful response
   */
  private logSuccessfulResponse(context: LogContext, duration: number): void {
    const { method, path } = context.request!;
    const { statusCode } = context.response!;
    
    this.structuredLogger.info(
      `Request completed: ${method} ${path} - ${statusCode} (${duration}ms)`,
      {
        ...context,
        metadata: {
          duration,
          statusCode,
          timestamp: new Date().toISOString(),
        },
      }
    );
  }

  /**
   * Log error response
   */
  private logErrorResponse(context: LogContext, error: any, duration: number): void {
    const { method, path } = context.request!;
    const statusCode = error.status || error.statusCode || 500;
    
    this.structuredLogger.error(
      `Request failed: ${method} ${path} - ${statusCode} (${duration}ms): ${error.message}`,
      error,
      {
        ...context,
        metadata: {
          duration,
          statusCode,
          errorType: error.constructor.name,
          timestamp: new Date().toISOString(),
        },
      }
    );
  }

  /**
   * Record request metrics
   */
  private recordRequestMetrics(request: Request, startTime: number): void {
    // Record request count
    this.metricsService.increment('http_requests_total', {
      method: request.method,
      route: request.route?.path || request.path,
      service: this.configService.get<string>('SERVICE_NAME') || 'unknown-service',
      environment: this.configService.get<string>('NODE_ENV') || 'development',
      version: this.configService.get<string>('SERVICE_VERSION') || '1.0.0',
    });

    // Record concurrent requests
    this.metricsService.increment('http_requests_active', {
      service: this.configService.get<string>('SERVICE_NAME') || 'unknown-service',
      environment: this.configService.get<string>('NODE_ENV') || 'development',
      version: this.configService.get<string>('SERVICE_VERSION') || '1.0.0',
    });
  }

  /**
   * Record response metrics
   */
  private recordResponseMetrics(
    request: Request,
    response: Response,
    duration: number,
    status: 'success' | 'error'
  ): void {
    const tags = {
      method: request.method,
      route: request.route?.path || request.path,
      status_code: response.statusCode.toString(),
      status,
      service: this.configService.get<string>('SERVICE_NAME') || 'unknown-service',
      environment: this.configService.get<string>('NODE_ENV') || 'development',
      version: this.configService.get<string>('SERVICE_VERSION') || '1.0.0',
    };

    // Record response time
    this.metricsService.histogram('http_request_duration_seconds', duration / 1000, tags);

    // Record performance metric
    this.metricsService.recordPerformanceMetric({
      name: 'request_duration',
      value: duration,
      type: 'histogram' as any,
      category: PerformanceMetricCategory.RESPONSE_TIME,
      unit: 'ms',
      timestamp: new Date(),
      tags,
    });

    // Decrement active requests
    this.metricsService.decrement('http_requests_active', {
      service: this.configService.get<string>('SERVICE_NAME') || 'unknown-service',
      environment: this.configService.get<string>('NODE_ENV') || 'development',
      version: this.configService.get<string>('SERVICE_VERSION') || '1.0.0',
    });
  }

  /**
   * Record error metrics
   */
  private recordErrorMetrics(error: any, context: LogContext): void {
    const errorType = error.constructor.name;
    const statusCode = error.status || error.statusCode || 500;

    this.metricsService.increment('http_errors_total', {
      error_type: errorType,
      status_code: statusCode.toString(),
      service: context.service,
      environment: this.configService.get<string>('NODE_ENV') || 'development',
      version: this.configService.get<string>('SERVICE_VERSION') || '1.0.0',
    });
  }

  /**
   * Check for security events
   */
  private checkSecurityEvents(error: any, request: Request, context: LogContext): void {
    const statusCode = error.status || error.statusCode || 500;

    // Check for authentication/authorization failures
    if (statusCode === 401 || statusCode === 403) {
      const securityContext: SecurityContext = {
        eventType: statusCode === 401 ? SecurityEventType.AUTHENTICATION : SecurityEventType.AUTHORIZATION,
        severity: SecuritySeverity.MEDIUM,
        actor: {
          ipAddress: this.getClientIP(request),
          userAgent: request.headers['user-agent'],
          userId: (request as any).user?.id,
        },
        resource: {
          type: 'endpoint',
          id: `${request.method} ${request.path}`,
        },
        action: request.method,
        outcome: SecurityOutcome.FAILURE,
        details: {
          error: error.message,
          statusCode,
        },
      };

      this.structuredLogger.warn(
        `Security event: ${securityContext.eventType} failure`,
        {
          ...context,
          security: securityContext,
        }
      );

      // Record security metric
      this.metricsService.recordSecurityMetric({
        name: 'auth_failure',
        value: 1,
        type: 'counter' as any,
        category: SecurityMetricCategory.AUTHENTICATION,
        severity: 'medium' as any,
        timestamp: new Date(),
        tags: {
          event_type: securityContext.eventType,
          outcome: securityContext.outcome,
          service: context.service,
          environment: this.configService.get<string>('NODE_ENV') || 'development',
          version: this.configService.get<string>('SERVICE_VERSION') || '1.0.0',
        },
      });
    }
  }

  /**
   * Get client IP address
   */
  private getClientIP(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
      request.headers['x-real-ip'] as string ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * Check if request body should be logged
   */
  private shouldLogBody(): boolean {
    return this.configService.get<boolean>('LOG_REQUEST_BODY', false);
  }

  /**
   * Check if response body should be logged
   */
  private shouldLogResponseBody(): boolean {
    return this.configService.get<boolean>('LOG_RESPONSE_BODY', false);
  }
}
