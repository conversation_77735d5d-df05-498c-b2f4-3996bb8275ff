'use client'

import React, { useState } from 'react'
import {
  ChartBarIcon,
  UsersIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  FunnelIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import { useCampaigns, useStartCampaign, usePauseCampaign, useCompleteCampaign } from '@/hooks/useCampaigns'
import { Campaign, CampaignStatus, CampaignType } from '@/types/campaign.types'
import CampaignCard from './CampaignCard'
import CampaignFilters from './CampaignFilters'
import CampaignStats from './CampaignStats'

interface CampaignDashboardProps {
  projectId?: string
  onCreateCampaign?: () => void
  onViewCampaign?: (campaign: Campaign) => void
  onEditCampaign?: (campaign: Campaign) => void
  className?: string
}

export default function CampaignDashboard({
  projectId,
  onCreateCampaign,
  onViewCampaign,
  onEditCampaign,
  className = ''
}: CampaignDashboardProps) {
  const [filters, setFilters] = useState({
    search: '',
    status: [] as CampaignStatus[],
    type: [] as CampaignType[],
    featured: undefined as boolean | undefined,
    sortBy: 'created_at' as string,
    sortOrder: 'desc' as 'asc' | 'desc'
  })
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  const { data: campaignsData, isLoading, refetch } = useCampaigns({
    ...filters,
    projectId
  })

  const startCampaignMutation = useStartCampaign()
  const pauseCampaignMutation = usePauseCampaign()
  const completeCampaignMutation = useCompleteCampaign()

  const campaigns = campaignsData?.campaigns || []
  const totalCampaigns = campaignsData?.total || 0

  const handleStatusChange = async (campaign: Campaign, newStatus: CampaignStatus) => {
    try {
      switch (newStatus) {
        case CampaignStatus.ACTIVE:
          await startCampaignMutation.mutateAsync(campaign.id)
          break
        case CampaignStatus.PAUSED:
          await pauseCampaignMutation.mutateAsync(campaign.id)
          break
        case CampaignStatus.COMPLETED:
          await completeCampaignMutation.mutateAsync(campaign.id)
          break
      }
      refetch()
    } catch (error) {
      console.error('Failed to change campaign status:', error)
    }
  }

  const getStatusCounts = () => {
    return campaigns.reduce((counts, campaign) => {
      counts[campaign.status] = (counts[campaign.status] || 0) + 1
      return counts
    }, {} as Record<CampaignStatus, number>)
  }

  const statusCounts = getStatusCounts()

  const getStatusColor = (status: CampaignStatus) => {
    switch (status) {
      case CampaignStatus.ACTIVE: return 'text-green-600 bg-green-100'
      case CampaignStatus.PAUSED: return 'text-yellow-600 bg-yellow-100'
      case CampaignStatus.COMPLETED: return 'text-blue-600 bg-blue-100'
      case CampaignStatus.CANCELLED: return 'text-red-600 bg-red-100'
      case CampaignStatus.DRAFT: return 'text-gray-600 bg-gray-100'
      case CampaignStatus.SCHEDULED: return 'text-purple-600 bg-purple-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Campaign Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Manage and monitor your campaigns
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => refetch()}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              Refresh
            </button>
            
            {onCreateCampaign && (
              <button
                onClick={onCreateCampaign}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Campaign
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Campaign Stats */}
      <CampaignStats campaigns={campaigns} />

      {/* Status Overview */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Campaign Status Overview</h2>
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          {Object.values(CampaignStatus).map((status) => (
            <div key={status} className="text-center">
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status)}`}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </div>
              <div className="text-2xl font-bold text-gray-900 mt-2">
                {statusCounts[status] || 0}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Campaigns ({totalCampaigns})</h2>
          
          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex items-center border border-gray-300 rounded-md">
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 text-sm font-medium ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                Grid
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 text-sm font-medium ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                List
              </button>
            </div>

            {/* Sort Options */}
            <select
              value={`${filters.sortBy}-${filters.sortOrder}`}
              onChange={(e) => {
                const [sortBy, sortOrder] = e.target.value.split('-')
                setFilters(prev => ({ ...prev, sortBy, sortOrder: sortOrder as 'asc' | 'desc' }))
              }}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="created_at-desc">Newest First</option>
              <option value="created_at-asc">Oldest First</option>
              <option value="name-asc">Name A-Z</option>
              <option value="name-desc">Name Z-A</option>
              <option value="participant_count-desc">Most Participants</option>
              <option value="start_date-desc">Starting Soon</option>
            </select>
          </div>
        </div>

        <CampaignFilters
          filters={filters}
          onFiltersChange={setFilters}
        />
      </div>

      {/* Campaigns List/Grid */}
      <div className="bg-white rounded-lg border border-gray-200">
        {campaigns.length > 0 ? (
          <div className="p-6">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {campaigns.map((campaign) => (
                  <CampaignCard
                    key={campaign.id}
                    campaign={campaign}
                    onView={() => onViewCampaign?.(campaign)}
                    onEdit={() => onEditCampaign?.(campaign)}
                    onStatusChange={(status) => handleStatusChange(campaign, status)}
                    isUpdating={
                      startCampaignMutation.isPending ||
                      pauseCampaignMutation.isPending ||
                      completeCampaignMutation.isPending
                    }
                  />
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {campaigns.map((campaign) => (
                  <div
                    key={campaign.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`}>
                          {campaign.status}
                        </span>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {campaign.name}
                        </h3>
                        <p className="text-sm text-gray-500 truncate">
                          {campaign.type} • {campaign.participantCount} participants
                        </p>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div>
                          <UsersIcon className="h-4 w-4 inline mr-1" />
                          {campaign.participantCount}
                        </div>
                        <div>
                          {new Date(campaign.startDate).toLocaleDateString()}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onViewCampaign?.(campaign)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                        title="View Campaign"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      
                      <button
                        onClick={() => onEditCampaign?.(campaign)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                        title="Edit Campaign"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>

                      {campaign.status === CampaignStatus.DRAFT && (
                        <button
                          onClick={() => handleStatusChange(campaign, CampaignStatus.ACTIVE)}
                          className="p-2 text-green-400 hover:text-green-600"
                          title="Start Campaign"
                        >
                          <PlayIcon className="h-4 w-4" />
                        </button>
                      )}

                      {campaign.status === CampaignStatus.ACTIVE && (
                        <button
                          onClick={() => handleStatusChange(campaign, CampaignStatus.PAUSED)}
                          className="p-2 text-yellow-400 hover:text-yellow-600"
                          title="Pause Campaign"
                        >
                          <PauseIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No campaigns found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.search || filters.status.length > 0 || filters.type.length > 0
                ? 'Try adjusting your filters to see more campaigns.'
                : 'Get started by creating your first campaign.'}
            </p>
            {onCreateCampaign && (
              <div className="mt-6">
                <button
                  onClick={onCreateCampaign}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create Campaign
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
