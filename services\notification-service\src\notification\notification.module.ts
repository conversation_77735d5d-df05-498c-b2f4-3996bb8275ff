import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';

// Controllers
import { NotificationController } from './controllers/notification.controller';
import { TemplateController } from './controllers/template.controller';
import { PreferenceController } from './controllers/preference.controller';
import { ChannelController } from './controllers/channel.controller';
import { EventController } from './controllers/event.controller';

// Services
import { NotificationManagementService } from './services/notification-management.service';
import { EventNotificationService } from './services/event-notification.service';
import { EmailService } from './services/email.service';
import { SMSService } from './services/sms.service';
import { PushNotificationService } from './services/push-notification.service';
import { TemplateService } from './services/template.service';
import { PreferenceService } from './services/preference.service';

// Shared modules
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [
    HttpModule,
    PrismaModule,
  ],
  controllers: [
    NotificationController,
    TemplateController,
    PreferenceController,
    ChannelController,
    EventController,
  ],
  providers: [
    NotificationManagementService,
    EventNotificationService,
    EmailService,
    SMSService,
    PushNotificationService,
    TemplateService,
    PreferenceService,
  ],
  exports: [
    NotificationManagementService,
    EventNotificationService,
    EmailService,
    SMSService,
    PushNotificationService,
    TemplateService,
    PreferenceService,
  ],
})
export class NotificationModule {}
