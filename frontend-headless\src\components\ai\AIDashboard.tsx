'use client'

import React, { useState } from 'react'
import {
  SparklesIcon,
  ChartBarIcon,
  CogIcon,
  LightBulbIcon,
  TrendingUpIcon,
  BellIcon,
  UserGroupIcon,
  CubeIcon
} from '@heroicons/react/24/outline'
import {
  usePersonalizedRecommendations,
  useAIInsights,
  useMarketSentiment,
  useTrendingTopics,
  usePortfolioOptimization,
  useRiskAssessment,
  useSmartNotifications
} from '@/hooks/useAI'
import AIRecommendations from './AIRecommendations'
import PredictiveAnalytics from './PredictiveAnalytics'
import PersonalizationSettings from './PersonalizationSettings'
import AutomationManager from './AutomationManager'
import AIInsights from './AIInsights'
import SmartNotifications from './SmartNotifications'

interface AIDashboardProps {
  className?: string
}

export default function AIDashboard({
  className = ''
}: AIDashboardProps) {
  const [activeTab, setActiveTab] = useState<'recommendations' | 'analytics' | 'insights' | 'automation' | 'personalization' | 'notifications'>('recommendations')

  // Mock user ID - in real app this would come from auth context
  const userId = 'current-user-id'

  const { data: recommendations, isLoading: recommendationsLoading } = usePersonalizedRecommendations(userId, 5)
  const { data: insights, isLoading: insightsLoading } = useAIInsights(userId)
  const { data: marketSentiment } = useMarketSentiment()
  const { data: trendingTopics } = useTrendingTopics(5)
  const { data: portfolioOptimization } = usePortfolioOptimization(userId)
  const { data: riskAssessment } = useRiskAssessment(userId)
  const { data: smartNotifications } = useSmartNotifications(userId)

  const tabs = [
    {
      id: 'recommendations',
      name: 'AI Recommendations',
      icon: SparklesIcon,
      count: recommendations?.length || 0,
      description: 'Personalized suggestions'
    },
    {
      id: 'analytics',
      name: 'Predictive Analytics',
      icon: ChartBarIcon,
      description: 'Market predictions & trends'
    },
    {
      id: 'insights',
      name: 'AI Insights',
      icon: LightBulbIcon,
      description: 'Smart analysis & reports'
    },
    {
      id: 'automation',
      name: 'Automation',
      icon: CogIcon,
      description: 'Automated trading & actions'
    },
    {
      id: 'personalization',
      name: 'Personalization',
      icon: UserGroupIcon,
      description: 'Customize your experience'
    },
    {
      id: 'notifications',
      name: 'Smart Alerts',
      icon: BellIcon,
      count: smartNotifications?.length || 0,
      description: 'Intelligent notifications'
    }
  ]

  const quickStats = [
    {
      label: 'AI Recommendations',
      value: recommendations?.length || 0,
      change: '+12%',
      trend: 'up',
      icon: SparklesIcon,
      color: 'text-blue-600'
    },
    {
      label: 'Portfolio Score',
      value: portfolioOptimization?.score || 0,
      change: '****%',
      trend: 'up',
      icon: TrendingUpIcon,
      color: 'text-green-600'
    },
    {
      label: 'Risk Level',
      value: riskAssessment?.overallRisk || 'Medium',
      change: 'Stable',
      trend: 'stable',
      icon: ChartBarIcon,
      color: 'text-yellow-600'
    },
    {
      label: 'Market Sentiment',
      value: marketSentiment?.overall || 'Neutral',
      change: marketSentiment?.change || '0%',
      trend: marketSentiment?.trend || 'stable',
      icon: CubeIcon,
      color: 'text-purple-600'
    }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <SparklesIcon className="h-8 w-8 mr-3 text-blue-600" />
            AI & ML Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Intelligent insights, predictions, and automation for your NFT journey
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">AI Status</div>
            <div className="text-xs text-green-600 flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              All systems operational
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat) => (
          <div key={stat.label} className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                <div className={`text-sm mt-1 flex items-center ${
                  stat.trend === 'up' ? 'text-green-600' :
                  stat.trend === 'down' ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {stat.change}
                </div>
              </div>
              <div className={`p-3 rounded-lg bg-gray-50`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Trending Topics */}
      {trendingTopics && trendingTopics.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <TrendingUpIcon className="h-5 w-5 mr-2 text-orange-600" />
            Trending Topics
          </h3>
          <div className="flex flex-wrap gap-2">
            {trendingTopics.map((topic, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800"
              >
                #{topic.name}
                <span className="ml-1 text-xs">({topic.volume})</span>
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
              {tab.count !== undefined && tab.count > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-96">
        {activeTab === 'recommendations' && (
          <AIRecommendations
            userId={userId}
            recommendations={recommendations}
            isLoading={recommendationsLoading}
          />
        )}

        {activeTab === 'analytics' && (
          <PredictiveAnalytics userId={userId} />
        )}

        {activeTab === 'insights' && (
          <AIInsights
            userId={userId}
            insights={insights}
            isLoading={insightsLoading}
            marketSentiment={marketSentiment}
            portfolioOptimization={portfolioOptimization}
            riskAssessment={riskAssessment}
          />
        )}

        {activeTab === 'automation' && (
          <AutomationManager userId={userId} />
        )}

        {activeTab === 'personalization' && (
          <PersonalizationSettings userId={userId} />
        )}

        {activeTab === 'notifications' && (
          <SmartNotifications
            userId={userId}
            notifications={smartNotifications}
          />
        )}
      </div>

      {/* AI Status Footer */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <SparklesIcon className="h-5 w-5 text-blue-600 mr-2" />
            <div>
              <div className="text-sm font-medium text-gray-900">AI Engine Status</div>
              <div className="text-xs text-gray-600">
                Last updated: {new Date().toLocaleTimeString()} • Processing {Math.floor(Math.random() * 1000)} data points
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 text-xs text-gray-600">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Recommendations: Active
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Predictions: Active
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Automation: Active
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
