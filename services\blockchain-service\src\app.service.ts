import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);
  private readonly startTime = Date.now();

  constructor(private readonly configService: ConfigService) {}

  getServiceInfo() {
    const info = {
      service: 'Blockchain Service',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      port: this.configService.get<number>('SERVICE_PORT', 3004),
      features: {
        smartContracts: true,
        multiChain: true,
        walletManagement: true,
        transactionMonitoring: true,
        gasEstimation: true,
        healthChecks: true,
        swagger: this.configService.get<string>('NODE_ENV') === 'development',
        cors: true,
        validation: true,
        authentication: true,
      },
      blockchain: {
        networks: {
          ethereum: {
            mainnet: this.configService.get<string>('ETHEREUM_RPC_URL'),
            testnet: this.configService.get<string>('ETHEREUM_TESTNET_RPC_URL'),
          },
          polygon: {
            mainnet: this.configService.get<string>('POLYGON_RPC_URL'),
            testnet: this.configService.get<string>('POLYGON_TESTNET_RPC_URL'),
          },
        },
        contracts: {
          nft: this.configService.get<string>('NFT_CONTRACT_ADDRESS'),
          marketplace: this.configService.get<string>('MARKETPLACE_CONTRACT_ADDRESS'),
        },
        gas: {
          defaultLimit: this.configService.get<number>('DEFAULT_GAS_LIMIT', 500000),
          defaultPrice: this.configService.get<number>('DEFAULT_GAS_PRICE', 20000000000),
        },
      },
      externalServices: {
        userService: this.configService.get<string>('USER_SERVICE_URL'),
        projectService: this.configService.get<string>('PROJECT_SERVICE_URL'),
        nftService: this.configService.get<string>('NFT_GENERATION_SERVICE_URL'),
        marketplaceService: this.configService.get<string>('MARKETPLACE_SERVICE_URL'),
        analyticsService: this.configService.get<string>('ANALYTICS_SERVICE_URL'),
        profileService: this.configService.get<string>('PROFILE_ANALYSIS_SERVICE_URL'),
      },
    };

    this.logger.log('Service info retrieved', { 
      uptime: info.uptime,
      environment: info.environment 
    });

    return info;
  }

  getStatus() {
    const memoryUsage = process.memoryUsage();
    
    const status = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
      },
      cpu: {
        usage: process.cpuUsage(),
      },
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      nodeVersion: process.version,
      platform: process.platform,
      blockchain: {
        connected: true, // Mock - replace with actual blockchain connection check
        networks: ['ethereum', 'polygon'],
        contractsLoaded: true,
        gasEstimationAvailable: true,
      },
    };

    this.logger.debug('Service status checked', {
      uptime: status.uptime,
      memoryUsed: Math.round(status.memory.used / 1024 / 1024) + 'MB',
    });

    return status;
  }
}
