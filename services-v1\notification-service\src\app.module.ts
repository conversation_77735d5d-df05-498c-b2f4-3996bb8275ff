import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { AppController } from './app.controller';
import { AppService } from './app.service';

// Business Logic Modules
import { NotificationModule } from './notification/notification.module';

// Infrastructure Modules
import { PrismaModule } from './prisma/prisma.module';
import { HealthModule } from './health/health.module';

// Configuration
import { AppConfig } from './config/app.config';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // Scheduling for background tasks
    ScheduleModule.forRoot(),

    // Infrastructure Modules
    PrismaModule,
    HealthModule,

    // Business Logic Modules
    NotificationModule,
  ],
  controllers: [AppController],
  providers: [AppConfig, AppService],
})
export class AppModule {}
