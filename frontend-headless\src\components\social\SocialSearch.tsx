'use client'

import React, { useState, useEffect } from 'react'
import {
  MagnifyingGlassIcon,
  UserIcon,
  DocumentTextIcon,
  UserGroupIcon,
  FunnelIcon,
  ClockIcon,
  TrendingUpIcon,
  CheckBadgeIcon,
  MapPinIcon,
  HashtagIcon
} from '@heroicons/react/24/outline'
import { useSocialSearch, useSuggestedUsers, useSuggestedCommunities } from '@/hooks/useSocial'
import { SocialSearchRequest, UserProfile, SocialPost, Community } from '@/types/social.types'

interface SocialSearchProps {
  className?: string
}

export default function SocialSearch({
  className = ''
}: SocialSearchProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchType, setSearchType] = useState<'all' | 'users' | 'posts' | 'communities'>('all')
  const [filters, setFilters] = useState({
    verified: false,
    hasNFTs: false,
    location: '',
    tags: [] as string[]
  })
  const [sortBy, setSortBy] = useState<'relevance' | 'recent' | 'popular'>('relevance')
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  const searchRequest: SocialSearchRequest = {
    query: searchQuery,
    type: searchType,
    filters,
    sortBy,
    limit: 20,
    offset: 0
  }

  const { data: searchResults, isLoading } = useSocialSearch(searchRequest)
  const { data: suggestedUsers } = useSuggestedUsers(5)
  const { data: suggestedCommunities } = useSuggestedCommunities(5)

  const searchTypes = [
    { value: 'all', label: 'All', icon: MagnifyingGlassIcon },
    { value: 'users', label: 'Users', icon: UserIcon },
    { value: 'posts', label: 'Posts', icon: DocumentTextIcon },
    { value: 'communities', label: 'Communities', icon: UserGroupIcon }
  ]

  const sortOptions = [
    { value: 'relevance', label: 'Most Relevant' },
    { value: 'recent', label: 'Most Recent' },
    { value: 'popular', label: 'Most Popular' }
  ]

  const hasResults = searchResults && (
    searchResults.users.length > 0 ||
    searchResults.posts.length > 0 ||
    searchResults.communities.length > 0
  )

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search Header */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Search</h2>
        
        {/* Search Input */}
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search users, posts, communities..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg text-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Search Filters */}
      <div className="space-y-4">
        {/* Search Type Tabs */}
        <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg">
          {searchTypes.map((type) => (
            <button
              key={type.value}
              onClick={() => setSearchType(type.value as any)}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                searchType === type.value
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <type.icon className="h-4 w-4 mr-2" />
              {type.label}
            </button>
          ))}
        </div>

        {/* Sort and Filters */}
        <div className="flex items-center justify-between">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          <button
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.verified}
                  onChange={(e) => setFilters(prev => ({ ...prev, verified: e.target.checked }))}
                  className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Verified accounts only</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.hasNFTs}
                  onChange={(e) => setFilters(prev => ({ ...prev, hasNFTs: e.target.checked }))}
                  className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Has NFTs</span>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
              <input
                type="text"
                placeholder="Enter location..."
                value={filters.location}
                onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        )}
      </div>

      {/* Search Results */}
      {searchQuery.length > 0 ? (
        <div className="space-y-6">
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : hasResults ? (
            <>
              {/* Users Results */}
              {(searchType === 'all' || searchType === 'users') && searchResults.users.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <UserIcon className="h-5 w-5 mr-2" />
                    Users ({searchResults.users.length})
                  </h3>
                  <div className="space-y-3">
                    {searchResults.users.map((user) => (
                      <UserSearchResult key={user.id} user={user} />
                    ))}
                  </div>
                </div>
              )}

              {/* Posts Results */}
              {(searchType === 'all' || searchType === 'posts') && searchResults.posts.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <DocumentTextIcon className="h-5 w-5 mr-2" />
                    Posts ({searchResults.posts.length})
                  </h3>
                  <div className="space-y-3">
                    {searchResults.posts.map((post) => (
                      <PostSearchResult key={post.id} post={post} />
                    ))}
                  </div>
                </div>
              )}

              {/* Communities Results */}
              {(searchType === 'all' || searchType === 'communities') && searchResults.communities.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <UserGroupIcon className="h-5 w-5 mr-2" />
                    Communities ({searchResults.communities.length})
                  </h3>
                  <div className="space-y-3">
                    {searchResults.communities.map((community) => (
                      <CommunitySearchResult key={community.id} community={community} />
                    ))}
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No results found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search terms or filters
              </p>
            </div>
          )}
        </div>
      ) : (
        /* Suggestions when no search query */
        <div className="space-y-6">
          {/* Trending Topics */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <TrendingUpIcon className="h-5 w-5 mr-2" />
              Trending Topics
            </h3>
            <div className="flex flex-wrap gap-2">
              {['#NFTArt', '#CryptoTrading', '#DigitalCollectibles', '#BlockchainGaming', '#DeFi'].map((tag) => (
                <button
                  key={tag}
                  onClick={() => setSearchQuery(tag)}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200"
                >
                  <HashtagIcon className="h-3 w-3 mr-1" />
                  {tag.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {/* Suggested Users */}
          {suggestedUsers && suggestedUsers.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Suggested Users</h3>
              <div className="space-y-3">
                {suggestedUsers.map((user) => (
                  <UserSearchResult key={user.id} user={user} showFollowButton />
                ))}
              </div>
            </div>
          )}

          {/* Suggested Communities */}
          {suggestedCommunities && suggestedCommunities.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Suggested Communities</h3>
              <div className="space-y-3">
                {suggestedCommunities.map((community) => (
                  <CommunitySearchResult key={community.id} community={community} showJoinButton />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

interface UserSearchResultProps {
  user: UserProfile
  showFollowButton?: boolean
}

function UserSearchResult({ user, showFollowButton = false }: UserSearchResultProps) {
  return (
    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
      <div className="flex items-center space-x-3">
        <img
          src={user.avatar || '/default-avatar.png'}
          alt={user.displayName}
          className="w-12 h-12 rounded-full"
        />
        <div>
          <div className="flex items-center space-x-2">
            <h4 className="text-sm font-medium text-gray-900">{user.displayName}</h4>
            {user.isVerified && (
              <CheckBadgeIcon className="h-4 w-4 text-blue-500" />
            )}
          </div>
          <p className="text-sm text-gray-600">@{user.username}</p>
          {user.bio && (
            <p className="text-xs text-gray-500 mt-1 line-clamp-1">{user.bio}</p>
          )}
          <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
            <span>{user.socialStats.followersCount} followers</span>
            <span>{user.socialStats.nftCount} NFTs</span>
            {user.location && (
              <span className="flex items-center">
                <MapPinIcon className="h-3 w-3 mr-1" />
                {user.location}
              </span>
            )}
          </div>
        </div>
      </div>
      
      {showFollowButton && (
        <button className="px-4 py-2 border border-blue-600 text-blue-600 text-sm font-medium rounded-md hover:bg-blue-50">
          Follow
        </button>
      )}
    </div>
  )
}

interface PostSearchResultProps {
  post: SocialPost
}

function PostSearchResult({ post }: PostSearchResultProps) {
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  return (
    <div className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
      <div className="flex items-start space-x-3">
        <img
          src={post.author.avatar || '/default-avatar.png'}
          alt={post.author.displayName}
          className="w-10 h-10 rounded-full"
        />
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <h4 className="text-sm font-medium text-gray-900">{post.author.displayName}</h4>
            <span className="text-sm text-gray-500">@{post.author.username}</span>
            <span className="text-sm text-gray-500">•</span>
            <span className="text-sm text-gray-500">{formatTimeAgo(post.createdAt)}</span>
          </div>
          
          <p className="text-sm text-gray-900 mb-2 line-clamp-2">{post.content}</p>
          
          {post.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {post.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}
          
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span>{post.engagement.likesCount} likes</span>
            <span>{post.engagement.commentsCount} comments</span>
            <span>{post.engagement.sharesCount} shares</span>
          </div>
        </div>
      </div>
    </div>
  )
}

interface CommunitySearchResultProps {
  community: Community
  showJoinButton?: boolean
}

function CommunitySearchResult({ community, showJoinButton = false }: CommunitySearchResultProps) {
  return (
    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
      <div className="flex items-center space-x-3">
        <img
          src={community.avatar || '/default-community.png'}
          alt={community.name}
          className="w-12 h-12 rounded-lg"
        />
        <div>
          <div className="flex items-center space-x-2">
            <h4 className="text-sm font-medium text-gray-900">{community.name}</h4>
            {community.isVerified && (
              <CheckBadgeIcon className="h-4 w-4 text-blue-500" />
            )}
          </div>
          <p className="text-sm text-gray-600 line-clamp-1">{community.description}</p>
          <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
            <span>{community.memberCount} members</span>
            <span>{community.postCount} posts</span>
            <span className="capitalize">{community.category}</span>
          </div>
        </div>
      </div>
      
      {showJoinButton && (
        <button className="px-4 py-2 border border-blue-600 text-blue-600 text-sm font-medium rounded-md hover:bg-blue-50">
          Join
        </button>
      )}
    </div>
  )
}
