import { Module } from '@nestjs/common';
import { LoadBalancerService } from './load-balancer.service';
import { LoadBalancerController } from './load-balancer.controller';

/**
 * Load Balancer Module
 * 
 * Provides intelligent load balancing across service instances
 * with health-aware routing and connection tracking.
 */
@Module({
  providers: [LoadBalancerService],
  controllers: [LoadBalancerController],
  exports: [LoadBalancerService],
})
export class LoadBalancerModule {}
