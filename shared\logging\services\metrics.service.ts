/**
 * Metrics Service
 * Provides enterprise-grade metrics collection with Prometheus
 */

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { register, Counter, Gauge, Histogram, Summary, collectDefaultMetrics } from 'prom-client';
import {
  IMetricsService,
  MetricTags,
  MetricTimer,
  MetricType,
  BusinessMetric,
  PerformanceMetric,
  SecurityMetric,
  HealthMetric,
  CustomMetric,
  MetricSnapshot,
} from '../interfaces/metrics.interface';

@Injectable()
export class MetricsService implements IMetricsService {
  private readonly counters = new Map<string, Counter>();
  private readonly gauges = new Map<string, Gauge>();
  private readonly histograms = new Map<string, Histogram>();
  private readonly summaries = new Map<string, Summary>();
  private readonly serviceName: string;
  private readonly environment: string;
  private readonly version: string;

  constructor(private readonly configService: ConfigService) {
    this.serviceName = this.configService.get<string>('SERVICE_NAME') || 'unknown-service';
    this.environment = this.configService.get<string>('NODE_ENV') || 'development';
    this.version = this.configService.get<string>('SERVICE_VERSION') || '1.0.0';

    // Initialize default metrics collection
    this.initializeDefaultMetrics();
  }

  /**
   * Record counter metric
   */
  counter(name: string, value: number = 1, tags: MetricTags = {}): void {
    const counter = this.getOrCreateCounter(name, tags);
    counter.inc(this.getDefaultTags(tags), value);
  }

  /**
   * Record gauge metric
   */
  gauge(name: string, value: number, tags: MetricTags = {}): void {
    const gauge = this.getOrCreateGauge(name, tags);
    gauge.set(this.getDefaultTags(tags), value);
  }

  /**
   * Record histogram metric
   */
  histogram(name: string, value: number, tags: MetricTags = {}): void {
    const histogram = this.getOrCreateHistogram(name, tags);
    histogram.observe(this.getDefaultTags(tags), value);
  }

  /**
   * Create timer for measuring duration
   */
  timer(name: string, tags: MetricTags = {}): MetricTimer {
    const startTime = Date.now();
    return {
      stop: (additionalTags: MetricTags = {}): number => {
        const duration = Date.now() - startTime;
        this.histogram(name, duration, { ...tags, ...additionalTags });
        return duration;
      },
      elapsed: (): number => Date.now() - startTime,
    };
  }

  /**
   * Increment counter by 1
   */
  increment(name: string, tags: MetricTags = {}): void {
    this.counter(name, 1, tags);
  }

  /**
   * Decrement counter by 1
   */
  decrement(name: string, tags: MetricTags = {}): void {
    this.counter(name, -1, tags);
  }

  /**
   * Record timing metric
   */
  timing(name: string, duration: number, tags: MetricTags = {}): void {
    this.histogram(name, duration, tags);
  }

  /**
   * Record business metric
   */
  recordBusinessMetric(metric: BusinessMetric): void {
    const metricName = `business_${metric.category}_${metric.name}`;
    const tags = {
      ...this.getDefaultTags(metric.tags),
      category: metric.category,
      unit: metric.unit || 'count',
    };

    switch (metric.type) {
      case MetricType.COUNTER:
        this.counter(metricName, metric.value, tags);
        break;
      case MetricType.GAUGE:
        this.gauge(metricName, metric.value, tags);
        break;
      case MetricType.HISTOGRAM:
        this.histogram(metricName, metric.value, tags);
        break;
      default:
        this.counter(metricName, metric.value, tags);
    }
  }

  /**
   * Record performance metric
   */
  recordPerformanceMetric(metric: PerformanceMetric): void {
    const metricName = `performance_${metric.category}_${metric.name}`;
    const tags = {
      ...this.getDefaultTags(metric.tags),
      category: metric.category,
      unit: metric.unit || 'ms',
    };

    // Add threshold tags if available
    if (metric.threshold) {
      tags.warning_threshold = metric.threshold.warning.toString();
      tags.critical_threshold = metric.threshold.critical.toString();
    }

    switch (metric.type) {
      case MetricType.COUNTER:
        this.counter(metricName, metric.value, tags);
        break;
      case MetricType.GAUGE:
        this.gauge(metricName, metric.value, tags);
        break;
      case MetricType.HISTOGRAM:
        this.histogram(metricName, metric.value, tags);
        break;
      default:
        this.histogram(metricName, metric.value, tags);
    }
  }

  /**
   * Record security metric
   */
  recordSecurityMetric(metric: SecurityMetric): void {
    const metricName = `security_${metric.category}_${metric.name}`;
    const tags = {
      ...this.getDefaultTags(metric.tags),
      category: metric.category,
      severity: metric.severity,
    };

    this.counter(metricName, metric.value, tags);
  }

  /**
   * Record health metric
   */
  recordHealthMetric(metric: HealthMetric): void {
    const metricName = `health_${metric.category}_${metric.name}`;
    const tags = {
      ...this.getDefaultTags(metric.tags),
      category: metric.category,
      status: metric.status,
    };

    switch (metric.type) {
      case MetricType.GAUGE:
        this.gauge(metricName, metric.value, tags);
        break;
      case MetricType.HISTOGRAM:
        this.histogram(metricName, metric.value, tags);
        break;
      default:
        this.gauge(metricName, metric.value, tags);
    }
  }

  /**
   * Get all metrics
   */
  async getMetrics(): Promise<MetricSnapshot[]> {
    const metrics = await register.getMetricsAsJSON();
    return metrics.map(metric => ({
      name: metric.name,
      type: metric.type as MetricType,
      value: metric.values,
      timestamp: new Date(),
      tags: this.getDefaultTags(),
      description: metric.help,
    }));
  }

  /**
   * Get specific metric
   */
  async getMetric(name: string): Promise<MetricSnapshot | null> {
    const metrics = await this.getMetrics();
    return metrics.find(metric => metric.name === name) || null;
  }

  /**
   * Register custom metric
   */
  register(metric: CustomMetric): void {
    switch (metric.type) {
      case MetricType.COUNTER:
        this.createCounter(metric.name, metric.description, metric.labelNames);
        break;
      case MetricType.GAUGE:
        this.createGauge(metric.name, metric.description, metric.labelNames);
        break;
      case MetricType.HISTOGRAM:
        this.createHistogram(metric.name, metric.description, metric.labelNames, metric.buckets);
        break;
      case MetricType.SUMMARY:
        this.createSummary(metric.name, metric.description, metric.labelNames, metric.percentiles);
        break;
    }
  }

  /**
   * Unregister metric
   */
  unregister(name: string): void {
    register.removeSingleMetric(name);
    this.counters.delete(name);
    this.gauges.delete(name);
    this.histograms.delete(name);
    this.summaries.delete(name);
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    register.clear();
    this.counters.clear();
    this.gauges.clear();
    this.histograms.clear();
    this.summaries.clear();
  }

  /**
   * Initialize default metrics
   */
  private initializeDefaultMetrics(): void {
    // Collect default Node.js metrics
    collectDefaultMetrics({
      register,
      prefix: `${this.serviceName}_`,
      labels: this.getDefaultTags(),
    });

    // Create standard application metrics
    this.createStandardMetrics();
  }

  /**
   * Create standard application metrics
   */
  private createStandardMetrics(): void {
    // HTTP request metrics
    this.createHistogram(
      'http_request_duration_seconds',
      'Duration of HTTP requests in seconds',
      ['method', 'route', 'status_code'],
      [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10]
    );

    this.createCounter(
      'http_requests_total',
      'Total number of HTTP requests',
      ['method', 'route', 'status_code']
    );

    // Database metrics
    this.createHistogram(
      'database_query_duration_seconds',
      'Duration of database queries in seconds',
      ['operation', 'table'],
      [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5]
    );

    this.createCounter(
      'database_queries_total',
      'Total number of database queries',
      ['operation', 'table', 'status']
    );

    // Cache metrics
    this.createCounter(
      'cache_operations_total',
      'Total number of cache operations',
      ['operation', 'status']
    );

    this.createHistogram(
      'cache_operation_duration_seconds',
      'Duration of cache operations in seconds',
      ['operation'],
      [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1]
    );

    // Business metrics
    this.createCounter(
      'business_events_total',
      'Total number of business events',
      ['event_type', 'status']
    );

    // Security metrics
    this.createCounter(
      'security_events_total',
      'Total number of security events',
      ['event_type', 'severity', 'outcome']
    );
  }

  /**
   * Get or create counter
   */
  private getOrCreateCounter(name: string, tags: MetricTags): Counter {
    if (!this.counters.has(name)) {
      this.createCounter(name, `Counter for ${name}`, Object.keys(tags));
    }
    return this.counters.get(name)!;
  }

  /**
   * Get or create gauge
   */
  private getOrCreateGauge(name: string, tags: MetricTags): Gauge {
    if (!this.gauges.has(name)) {
      this.createGauge(name, `Gauge for ${name}`, Object.keys(tags));
    }
    return this.gauges.get(name)!;
  }

  /**
   * Get or create histogram
   */
  private getOrCreateHistogram(name: string, tags: MetricTags): Histogram {
    if (!this.histograms.has(name)) {
      this.createHistogram(name, `Histogram for ${name}`, Object.keys(tags));
    }
    return this.histograms.get(name)!;
  }

  /**
   * Create counter
   */
  private createCounter(name: string, help: string, labelNames: string[] = []): Counter {
    const counter = new Counter({
      name,
      help,
      labelNames: [...labelNames, 'service', 'environment', 'version'],
      registers: [register],
    });
    this.counters.set(name, counter);
    return counter;
  }

  /**
   * Create gauge
   */
  private createGauge(name: string, help: string, labelNames: string[] = []): Gauge {
    const gauge = new Gauge({
      name,
      help,
      labelNames: [...labelNames, 'service', 'environment', 'version'],
      registers: [register],
    });
    this.gauges.set(name, gauge);
    return gauge;
  }

  /**
   * Create histogram
   */
  private createHistogram(name: string, help: string, labelNames: string[] = [], buckets?: number[]): Histogram {
    const histogram = new Histogram({
      name,
      help,
      labelNames: [...labelNames, 'service', 'environment', 'version'],
      buckets: buckets || [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10],
      registers: [register],
    });
    this.histograms.set(name, histogram);
    return histogram;
  }

  /**
   * Create summary
   */
  private createSummary(name: string, help: string, labelNames: string[] = [], percentiles?: number[]): Summary {
    const summary = new Summary({
      name,
      help,
      labelNames: [...labelNames, 'service', 'environment', 'version'],
      percentiles: percentiles || [0.5, 0.9, 0.95, 0.99],
      registers: [register],
    });
    this.summaries.set(name, summary);
    return summary;
  }

  /**
   * Get default tags
   */
  private getDefaultTags(additionalTags: MetricTags = {}): MetricTags {
    return {
      service: this.serviceName,
      environment: this.environment,
      version: this.version,
      ...additionalTags,
    };
  }
}
