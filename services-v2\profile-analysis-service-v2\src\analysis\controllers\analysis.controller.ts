/**
 * Analysis Controller - Profile Analysis Service V2
 * 
 * API endpoints for profile analysis operations
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AnalysisService } from '../services/analysis.service';
import { CreateAnalysisDto } from '../dto/create-analysis.dto';
import { UpdateAnalysisDto } from '../dto/update-analysis.dto';
import { QueryAnalysisDto } from '../dto/query-analysis.dto';

@ApiTags('analysis')
@Controller('analysis')
export class AnalysisController {
  constructor(private readonly analysisService: AnalysisService) {}

  @Post()
  @ApiOperation({ summary: 'Create new profile analysis' })
  @ApiResponse({ status: 201, description: 'Analysis created and queued successfully' })
  @ApiResponse({ status: 400, description: 'Invalid analysis request' })
  async createAnalysis(@Body() createAnalysisDto: CreateAnalysisDto) {
    const analysis = await this.analysisService.createAnalysis(createAnalysisDto);
    
    return {
      success: true,
      data: analysis,
      message: 'Profile analysis created and queued successfully',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all analyses with pagination' })
  @ApiResponse({ status: 200, description: 'Analyses retrieved successfully' })
  async findAll(@Query() queryDto: QueryAnalysisDto) {
    const result = await this.analysisService.findAll(queryDto);
    
    return {
      success: true,
      data: result.analyses,
      pagination: result.pagination,
      message: 'Analyses retrieved successfully',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get analysis by ID' })
  @ApiResponse({ status: 200, description: 'Analysis found' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async findOne(@Param('id') id: string) {
    const analysis = await this.analysisService.findOne(id);
    
    if (!analysis) {
      return {
        success: false,
        error: 'Analysis not found',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
    
    return {
      success: true,
      data: analysis,
      message: 'Analysis retrieved successfully',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get analyses by user ID' })
  @ApiResponse({ status: 200, description: 'User analyses retrieved' })
  async findByUser(@Param('userId') userId: string, @Query() queryDto: QueryAnalysisDto) {
    const result = await this.analysisService.findByUser(userId, queryDto);
    
    return {
      success: true,
      data: result.analyses,
      pagination: result.pagination,
      message: 'User analyses retrieved successfully',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update analysis' })
  @ApiResponse({ status: 200, description: 'Analysis updated successfully' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async update(@Param('id') id: string, @Body() updateAnalysisDto: UpdateAnalysisDto) {
    const analysis = await this.analysisService.update(id, updateAnalysisDto);
    
    if (!analysis) {
      return {
        success: false,
        error: 'Analysis not found',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
    
    return {
      success: true,
      data: analysis,
      message: 'Analysis updated successfully',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete analysis' })
  @ApiResponse({ status: 200, description: 'Analysis deleted successfully' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async remove(@Param('id') id: string) {
    const deleted = await this.analysisService.remove(id);
    
    if (!deleted) {
      return {
        success: false,
        error: 'Analysis not found',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
    
    return {
      success: true,
      message: 'Analysis deleted successfully',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }

  @Post(':id/process')
  @ApiOperation({ summary: 'Process analysis (trigger AI analysis)' })
  @ApiResponse({ status: 200, description: 'Analysis processing started' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async processAnalysis(@Param('id') id: string) {
    const result = await this.analysisService.processAnalysis(id);
    
    if (!result) {
      return {
        success: false,
        error: 'Analysis not found or cannot be processed',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
    
    return {
      success: true,
      data: result,
      message: 'Analysis processing started',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }

  @Get(':id/status')
  @ApiOperation({ summary: 'Get analysis status' })
  @ApiResponse({ status: 200, description: 'Analysis status retrieved' })
  async getStatus(@Param('id') id: string) {
    const status = await this.analysisService.getStatus(id);
    
    if (!status) {
      return {
        success: false,
        error: 'Analysis not found',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
    
    return {
      success: true,
      data: status,
      message: 'Analysis status retrieved',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }

  @Get(':id/results')
  @ApiOperation({ summary: 'Get analysis results' })
  @ApiResponse({ status: 200, description: 'Analysis results retrieved' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  @ApiResponse({ status: 202, description: 'Analysis still processing' })
  async getResults(@Param('id') id: string) {
    const results = await this.analysisService.getResults(id);
    
    if (!results) {
      return {
        success: false,
        error: 'Analysis not found',
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
    
    if (results.status !== 'completed') {
      return {
        success: false,
        error: 'Analysis still processing',
        data: { status: results.status, progress: results.progress },
        timestamp: new Date().toISOString(),
        service: 'profile-analysis-service-v2',
      };
    }
    
    return {
      success: true,
      data: results,
      message: 'Analysis results retrieved',
      timestamp: new Date().toISOString(),
      service: 'profile-analysis-service-v2',
    };
  }
}
