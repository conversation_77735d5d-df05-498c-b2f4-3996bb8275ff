import { <PERSON>, Get, Post, Body, Query, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { EventTrackingService } from '../services/event-tracking.service';

@ApiTags('events')
@Controller('events')
export class EventsController {
  private readonly logger = new Logger(EventsController.name);

  constructor(private readonly eventTracking: EventTrackingService) {}

  @Post('track')
  @ApiOperation({ summary: 'Track a single event' })
  @ApiResponse({ status: 201, description: 'Event tracked successfully' })
  async trackEvent(@Body() eventData: any) {
    try {
      this.logger.log('Tracking event', { eventType: eventData.eventType });

      const eventId = await this.eventTracking.trackEvent(eventData);

      return {
        success: true,
        data: {
          eventId,
          message: 'Event tracked successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to track event: ${error.message}`);
      return {
        success: false,
        error: 'Failed to track event',
        details: error.message,
      };
    }
  }

  @Post('track/batch')
  @ApiOperation({ summary: 'Track multiple events in batch' })
  @ApiResponse({ status: 201, description: 'Events tracked successfully' })
  async trackEventsBatch(@Body() eventsData: any[]) {
    try {
      this.logger.log(`Tracking batch of ${eventsData.length} events`);

      const eventIds = await this.eventTracking.trackEvents(eventsData);

      return {
        success: true,
        data: {
          eventIds,
          count: eventIds.length,
          message: 'Events tracked successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to track batch events: ${error.message}`);
      return {
        success: false,
        error: 'Failed to track events',
        details: error.message,
      };
    }
  }

  @Post('track/user-action')
  @ApiOperation({ summary: 'Track user action' })
  @ApiResponse({ status: 201, description: 'User action tracked successfully' })
  async trackUserAction(@Body() actionData: {
    userId: string;
    action: string;
    properties?: Record<string, any>;
    metadata?: Record<string, any>;
  }) {
    try {
      this.logger.log('Tracking user action', { 
        userId: actionData.userId, 
        action: actionData.action 
      });

      const eventId = await this.eventTracking.trackUserAction(
        actionData.userId,
        actionData.action,
        actionData.properties,
        actionData.metadata
      );

      return {
        success: true,
        data: {
          eventId,
          message: 'User action tracked successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to track user action: ${error.message}`);
      return {
        success: false,
        error: 'Failed to track user action',
        details: error.message,
      };
    }
  }

  @Post('track/page-view')
  @ApiOperation({ summary: 'Track page view' })
  @ApiResponse({ status: 201, description: 'Page view tracked successfully' })
  async trackPageView(@Body() pageData: {
    userId: string;
    page: string;
    sessionId: string;
    metadata?: Record<string, any>;
  }) {
    try {
      this.logger.log('Tracking page view', { 
        userId: pageData.userId, 
        page: pageData.page 
      });

      const eventId = await this.eventTracking.trackPageView(
        pageData.userId,
        pageData.page,
        pageData.sessionId,
        pageData.metadata
      );

      return {
        success: true,
        data: {
          eventId,
          message: 'Page view tracked successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to track page view: ${error.message}`);
      return {
        success: false,
        error: 'Failed to track page view',
        details: error.message,
      };
    }
  }

  @Post('track/nft')
  @ApiOperation({ summary: 'Track NFT-related event' })
  @ApiResponse({ status: 201, description: 'NFT event tracked successfully' })
  async trackNFTEvent(@Body() nftData: {
    eventType: 'nft_generated' | 'nft_minted' | 'nft_viewed' | 'nft_shared';
    userId: string;
    nftId: string;
    properties?: Record<string, any>;
  }) {
    try {
      this.logger.log('Tracking NFT event', { 
        eventType: nftData.eventType,
        userId: nftData.userId,
        nftId: nftData.nftId
      });

      const eventId = await this.eventTracking.trackNFTEvent(
        nftData.eventType,
        nftData.userId,
        nftData.nftId,
        nftData.properties
      );

      return {
        success: true,
        data: {
          eventId,
          message: 'NFT event tracked successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to track NFT event: ${error.message}`);
      return {
        success: false,
        error: 'Failed to track NFT event',
        details: error.message,
      };
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Get event metrics' })
  @ApiResponse({ status: 200, description: 'Event metrics retrieved successfully' })
  async getEventMetrics() {
    try {
      this.logger.log('Getting event metrics');

      const metrics = this.eventTracking.getEventMetrics();

      return {
        success: true,
        data: metrics,
      };
    } catch (error) {
      this.logger.error(`Failed to get event metrics: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve event metrics',
        details: error.message,
      };
    }
  }

  @Get('type/:eventType')
  @ApiOperation({ summary: 'Get events by type' })
  @ApiResponse({ status: 200, description: 'Events retrieved successfully' })
  @ApiParam({ name: 'eventType', description: 'Event type to filter by' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of events to return' })
  async getEventsByType(
    @Param('eventType') eventType: string,
    @Query('limit') limit: string = '100'
  ) {
    try {
      this.logger.log(`Getting events by type: ${eventType}`);

      const events = this.eventTracking.getEventsByType(eventType, parseInt(limit));

      return {
        success: true,
        data: {
          eventType,
          events,
          count: events.length,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get events by type: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve events',
        details: error.message,
      };
    }
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get events by user' })
  @ApiResponse({ status: 200, description: 'User events retrieved successfully' })
  @ApiParam({ name: 'userId', description: 'User ID to filter by' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of events to return' })
  async getEventsByUser(
    @Param('userId') userId: string,
    @Query('limit') limit: string = '100'
  ) {
    try {
      this.logger.log(`Getting events for user: ${userId}`);

      const events = this.eventTracking.getEventsByUser(userId, parseInt(limit));

      return {
        success: true,
        data: {
          userId,
          events,
          count: events.length,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get events by user: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve user events',
        details: error.message,
      };
    }
  }
}
