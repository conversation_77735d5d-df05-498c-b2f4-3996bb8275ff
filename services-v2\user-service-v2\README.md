# 👤 **User Service V2 - Clean Implementation**

## **📋 OVERVIEW**

User Service V2 is a clean, enterprise-grade implementation using shared infrastructure and following the **Database Per Service** pattern. This service handles all user-related operations including authentication, user management, and session handling.

### **🎯 Key Features**
- ✅ **Shared Infrastructure Integration** - Uses standardized patterns
- ✅ **Database Per Service** - Own PostgreSQL database (`user_service_v2`)
- ✅ **JWT Authentication** - Secure token-based authentication
- ✅ **Session Management** - Multi-device session tracking
- ✅ **Business Event Logging** - Comprehensive audit trail
- ✅ **Performance Monitoring** - Database query tracking
- ✅ **Health Checks** - Kubernetes-ready health endpoints
- ✅ **API Documentation** - Swagger/OpenAPI integration

---

## 🏗️ **ARCHITECTURE**

### **Database Per Service Pattern**
```
User Service V2 (Port 4001) ──► user_service_v2 Database
├── users table
├── user_sessions table
├── user_preferences table
├── user_roles table
├── user_activities table
├── user_verifications table
└── user_oauth table
```

### **Shared Infrastructure Components**
- **Authentication & Authorization** - JWT guards, RBAC, decorators
- **Response Standardization** - Consistent API responses with correlation IDs
- **Structured Logging** - Business events, security events, performance metrics
- **Configuration Management** - Type-safe environment configuration
- **Database Infrastructure** - Prisma service with performance tracking
- **Error Handling** - Global exception filter with security logging

---

## 🚀 **GETTING STARTED**

### **Prerequisites**
- Node.js 18+
- PostgreSQL 14+
- npm 8+

### **Installation**
```bash
# Navigate to service directory
cd services-v2/user-service-v2

# Install dependencies
npm install

# Install shared infrastructure
npm install ../../../shared

# Setup database
createdb user_service_v2

# Run database migrations
npm run prisma:migrate

# Generate Prisma client
npm run prisma:generate

# Seed database (optional)
npm run db:seed
```

### **Environment Setup**
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

### **Running the Service**
```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod

# Debug mode
npm run start:debug
```

---

## 📊 **API ENDPOINTS**

### **Authentication Endpoints**
```bash
POST /auth/login              # User login
POST /auth/logout             # User logout
POST /auth/refresh            # Refresh access token
GET  /auth/me                 # Get current user
GET  /auth/sessions           # Get user sessions
DELETE /auth/sessions/:id     # Revoke session
POST /auth/logout-all         # Logout from all devices
```

### **User Management Endpoints**
```bash
POST /users                   # Create user (registration)
GET  /users                   # Get all users (paginated)
GET  /users/me                # Get current user profile
GET  /users/:id               # Get user by ID
PUT  /users/:id               # Update user by ID
PUT  /users/me                # Update current user profile
DELETE /users/:id             # Delete user
POST /users/:id/activate      # Activate user
POST /users/:id/deactivate    # Deactivate user
```

### **Health Check Endpoints**
```bash
GET /health/simple            # Simple health check
GET /health/detailed          # Detailed health with dependencies
GET /health/database          # Database health check
GET /health/ready             # Readiness probe (Kubernetes)
GET /health/live              # Liveness probe (Kubernetes)
```

---

## 🔧 **CONFIGURATION**

### **Environment Variables**
```bash
# Service Configuration
SERVICE_NAME=user-service-v2
SERVICE_PORT=4001
NODE_ENV=development

# Database (Own Database)
DATABASE_URL=postgresql://postgres:password@localhost:5432/user_service_v2

# JWT Configuration
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:4010
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100
```

### **Database Schema**
The service uses its own PostgreSQL database with the following tables:
- `users` - User accounts and profiles
- `user_sessions` - Active user sessions
- `user_preferences` - User preferences and settings
- `user_roles` - User roles and permissions
- `user_activities` - User activity audit log
- `user_verifications` - Email verification and password reset tokens
- `user_oauth` - OAuth provider connections

---

## 🧪 **TESTING**

### **Running Tests**
```bash
# Unit tests
npm test

# Watch mode
npm run test:watch

# Coverage report
npm run test:cov

# E2E tests
npm run test:e2e
```

### **API Testing**
```bash
# Test health check
curl http://localhost:4001/health/simple

# Test user registration
curl -X POST http://localhost:4001/users \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","username":"testuser","password":"Test123!"}'

# Test login
curl -X POST http://localhost:4001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'
```

---

## 📚 **API DOCUMENTATION**

### **Swagger Documentation**
- **URL**: `http://localhost:4001/api/docs`
- **Interactive API Explorer**: Test endpoints directly
- **Schema Documentation**: Complete request/response schemas

### **Response Format**
All API responses follow the standardized format:
```json
{
  "success": true,
  "data": {...},
  "message": "Operation completed successfully",
  "correlationId": "user-service-v2_1234567890_abc123",
  "timestamp": "2024-06-14T10:30:00.000Z",
  "service": "user-service-v2",
  "version": "2.0.0"
}
```

---

## 🔍 **MONITORING & LOGGING**

### **Business Event Logging**
The service automatically logs business events:
- User registration, login, logout
- Profile updates and changes
- Account activation/deactivation
- Session management events

### **Security Event Logging**
Security events are tracked and logged:
- Failed login attempts
- Suspicious activities
- Token refresh events
- Session revocations

### **Performance Monitoring**
- Database query performance tracking
- API response time monitoring
- Memory and CPU usage metrics
- Health check response times

### **Health Monitoring**
```bash
# Check service health
curl http://localhost:4001/health/detailed

# Check database health
curl http://localhost:4001/health/database

# Kubernetes probes
curl http://localhost:4001/health/ready   # Readiness
curl http://localhost:4001/health/live    # Liveness
```

---

## 🔒 **SECURITY FEATURES**

### **Authentication & Authorization**
- JWT-based authentication with refresh tokens
- Role-based access control (RBAC)
- Permission-based endpoint protection
- Multi-device session management

### **Security Measures**
- Password hashing with bcrypt (12 rounds)
- Rate limiting on API endpoints
- CORS protection
- Security headers (Helmet.js)
- Input validation and sanitization

### **Audit Trail**
- Complete user activity logging
- Security event tracking
- Session management audit
- Failed authentication logging

---

## 🚀 **DEPLOYMENT**

### **Docker Deployment**
```bash
# Build Docker image
docker build -t user-service-v2 .

# Run container
docker run -p 4001:4001 \
  -e DATABASE_URL=postgresql://postgres:<EMAIL>:5432/user_service_v2 \
  user-service-v2
```

### **Kubernetes Deployment**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service-v2
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service-v2
  template:
    metadata:
      labels:
        app: user-service-v2
    spec:
      containers:
      - name: user-service-v2
        image: user-service-v2:latest
        ports:
        - containerPort: 4001
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: user-service-secrets
              key: database-url
        livenessProbe:
          httpGet:
            path: /health/live
            port: 4001
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 4001
```

---

## 🎯 **COMPARISON WITH V1**

### **Improvements in V2**
- ✅ **Shared Infrastructure** - Standardized patterns across services
- ✅ **Better Logging** - Structured business and security event logging
- ✅ **Performance Monitoring** - Database query tracking and metrics
- ✅ **Health Checks** - Kubernetes-ready health endpoints
- ✅ **Error Handling** - Standardized error responses with correlation IDs
- ✅ **Security** - Enhanced security event logging and monitoring
- ✅ **Documentation** - Comprehensive API documentation with Swagger

### **Migration from V1**
- **Port Change**: V1 (3001) → V2 (4001)
- **Database**: Separate database (`user_service_v2`)
- **API Compatibility**: Same endpoints, enhanced responses
- **Zero Downtime**: Can run both versions simultaneously

---

**🎯 User Service V2 provides enterprise-grade user management with shared infrastructure integration!**
