'use client'

import React, { useState } from 'react'
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { useCreateCampaign, useValidateCampaignData, usePreviewCampaign } from '@/hooks/useCampaigns'
import { CreateCampaignRequest, CampaignType, NFTStyle } from '@/types/campaign.types'
import BasicInfoStep from './steps/BasicInfoStep'
import TimelineStep from './steps/TimelineStep'
import RequirementsStep from './steps/RequirementsStep'
import RewardsStep from './steps/RewardsStep'
import NFTSettingsStep from './steps/NFTSettingsStep'
import ReviewStep from './steps/ReviewStep'

interface CampaignCreationWizardProps {
  projectId: string
  onComplete?: (campaign: any) => void
  onCancel?: () => void
  className?: string
}

interface WizardStep {
  id: string
  title: string
  description: string
  component: React.ComponentType<any>
  validation?: (data: any) => string[]
}

export default function CampaignCreationWizard({
  projectId,
  onComplete,
  onCancel,
  className = ''
}: CampaignCreationWizardProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [campaignData, setCampaignData] = useState<Partial<CreateCampaignRequest>>({
    projectId,
    type: CampaignType.SOCIAL_ENGAGEMENT,
    tags: [],
    requirements: [],
    rewards: [],
    nftSettings: {
      enabled: true,
      style: NFTStyle.REALISTIC,
      theme: 'default',
      rarityThresholds: {
        common: 0,
        rare: 50,
        epic: 75,
        legendary: 90,
        mythic: 95
      },
      customization: {
        backgroundColors: ['#ffffff'],
        primaryColors: ['#000000'],
        accentColors: ['#0066cc'],
        fonts: ['Arial'],
        effects: [],
        overlays: [],
        customPrompts: []
      },
      blockchain: 'ethereum',
      autoMint: true,
      batchSize: 10,
      generationDelay: 1000
    }
  })

  const createCampaignMutation = useCreateCampaign()
  const validateDataMutation = useValidateCampaignData()
  const previewCampaignMutation = usePreviewCampaign()

  const steps: WizardStep[] = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      description: 'Set up your campaign name, description, and type',
      component: BasicInfoStep,
      validation: (data) => {
        const errors = []
        if (!data.name?.trim()) errors.push('Campaign name is required')
        if (!data.description?.trim()) errors.push('Campaign description is required')
        if (!data.type) errors.push('Campaign type is required')
        return errors
      }
    },
    {
      id: 'timeline',
      title: 'Timeline & Limits',
      description: 'Configure campaign duration and participant limits',
      component: TimelineStep,
      validation: (data) => {
        const errors = []
        if (!data.startDate) errors.push('Start date is required')
        if (!data.endDate) errors.push('End date is required')
        if (data.startDate && data.endDate && new Date(data.startDate) >= new Date(data.endDate)) {
          errors.push('End date must be after start date')
        }
        return errors
      }
    },
    {
      id: 'requirements',
      title: 'Requirements',
      description: 'Define what participants need to do',
      component: RequirementsStep,
      validation: (data) => {
        const errors = []
        if (!data.requirements?.length) errors.push('At least one requirement is needed')
        return errors
      }
    },
    {
      id: 'rewards',
      title: 'Rewards',
      description: 'Set up rewards for participants',
      component: RewardsStep,
      validation: (data) => {
        const errors = []
        if (!data.rewards?.length) errors.push('At least one reward is needed')
        return errors
      }
    },
    {
      id: 'nft-settings',
      title: 'NFT Generation',
      description: 'Configure NFT generation settings',
      component: NFTSettingsStep
    },
    {
      id: 'review',
      title: 'Review & Launch',
      description: 'Review your campaign and launch it',
      component: ReviewStep
    }
  ]

  const currentStepData = steps[currentStep]
  const isLastStep = currentStep === steps.length - 1
  const isFirstStep = currentStep === 0

  const updateCampaignData = (updates: Partial<CreateCampaignRequest>) => {
    setCampaignData(prev => ({ ...prev, ...updates }))
  }

  const validateCurrentStep = () => {
    if (currentStepData.validation) {
      return currentStepData.validation(campaignData)
    }
    return []
  }

  const handleNext = async () => {
    const errors = validateCurrentStep()
    if (errors.length > 0) {
      // Show validation errors
      return
    }

    if (isLastStep) {
      await handleSubmit()
    } else {
      setCurrentStep(prev => prev + 1)
    }
  }

  const handlePrevious = () => {
    if (!isFirstStep) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const handleSubmit = async () => {
    try {
      const campaign = await createCampaignMutation.mutateAsync(campaignData as CreateCampaignRequest)
      onComplete?.(campaign)
    } catch (error) {
      console.error('Failed to create campaign:', error)
    }
  }

  const StepComponent = currentStepData.component

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Create New Campaign</h2>
            <p className="text-sm text-gray-600 mt-1">
              Step {currentStep + 1} of {steps.length}: {currentStepData.title}
            </p>
          </div>
          
          {onCancel && (
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              ✕
            </button>
          )}
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="flex items-center">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  index < currentStep 
                    ? 'bg-green-500 border-green-500 text-white'
                    : index === currentStep
                    ? 'bg-blue-500 border-blue-500 text-white'
                    : 'bg-white border-gray-300 text-gray-400'
                }`}>
                  {index < currentStep ? (
                    <CheckIcon className="h-4 w-4" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>
                
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-2 ${
                    index < currentStep ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-2">
            <p className="text-sm font-medium text-gray-900">{currentStepData.title}</p>
            <p className="text-xs text-gray-600">{currentStepData.description}</p>
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="p-6">
        <StepComponent
          data={campaignData}
          updateData={updateCampaignData}
          errors={validateCurrentStep()}
          previewData={currentStepData.id === 'review' ? previewCampaignMutation.data : undefined}
        />
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <button
            onClick={handlePrevious}
            disabled={isFirstStep}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeftIcon className="h-4 w-4 mr-2" />
            Previous
          </button>

          <div className="flex items-center space-x-3">
            {currentStep === steps.length - 2 && (
              <button
                onClick={() => previewCampaignMutation.mutate(campaignData as CreateCampaignRequest)}
                disabled={previewCampaignMutation.isPending}
                className="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100"
              >
                <InformationCircleIcon className="h-4 w-4 mr-2" />
                {previewCampaignMutation.isPending ? 'Generating...' : 'Preview'}
              </button>
            )}

            <button
              onClick={handleNext}
              disabled={createCampaignMutation.isPending || validateCurrentStep().length > 0}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLastStep ? (
                createCampaignMutation.isPending ? 'Creating...' : 'Create Campaign'
              ) : (
                <>
                  Next
                  <ChevronRightIcon className="h-4 w-4 ml-2" />
                </>
              )}
            </button>
          </div>
        </div>

        {/* Validation Errors */}
        {validateCurrentStep().length > 0 && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Please fix the following errors:
                </h3>
                <ul className="mt-1 text-sm text-red-700 list-disc list-inside">
                  {validateCurrentStep().map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Preview Results */}
        {previewCampaignMutation.data && (
          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Campaign Preview</h3>
            <div className="grid grid-cols-2 gap-4 text-sm text-blue-700">
              <div>
                <span className="font-medium">Estimated Participants:</span> {previewCampaignMutation.data.estimatedParticipants}
              </div>
              <div>
                <span className="font-medium">Estimated Duration:</span> {previewCampaignMutation.data.estimatedDuration} days
              </div>
              <div>
                <span className="font-medium">Estimated Cost:</span> ${previewCampaignMutation.data.estimatedCost}
              </div>
            </div>
            {previewCampaignMutation.data.recommendations.length > 0 && (
              <div className="mt-2">
                <span className="font-medium text-blue-800">Recommendations:</span>
                <ul className="mt-1 list-disc list-inside text-blue-700">
                  {previewCampaignMutation.data.recommendations.map((rec: string, index: number) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// All step components are now imported from separate files
