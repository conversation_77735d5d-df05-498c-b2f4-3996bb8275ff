/**
 * Paginated Response DTOs
 * Provides standardized pagination DTOs for API documentation
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsBoolean, IsOptional, IsArray, IsString, IsEnum, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { BaseResponseDto } from './base-response.dto';
import { SortDirection, FilterOperator } from '../interfaces/pagination.interface';

/**
 * Pagination Metadata DTO
 */
export class PaginationMetaDto {
  @ApiProperty({
    description: 'Current page number (1-based)',
    example: 1,
    minimum: 1,
  })
  @IsNumber()
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsNumber()
  limit: number;

  @ApiProperty({
    description: 'Total number of items across all pages',
    example: 150,
    minimum: 0,
  })
  @IsNumber()
  totalCount: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 8,
    minimum: 0,
  })
  @IsNumber()
  totalPages: number;

  @ApiProperty({
    description: 'Whether there is a next page',
    example: true,
  })
  @IsBoolean()
  hasNext: boolean;

  @ApiProperty({
    description: 'Whether there is a previous page',
    example: false,
  })
  @IsBoolean()
  hasPrev: boolean;

  @ApiPropertyOptional({
    description: 'Next page number (if available)',
    example: 2,
  })
  @IsOptional()
  @IsNumber()
  nextPage?: number;

  @ApiPropertyOptional({
    description: 'Previous page number (if available)',
    example: null,
  })
  @IsOptional()
  @IsNumber()
  prevPage?: number;

  @ApiPropertyOptional({
    description: 'Offset from the beginning of the dataset',
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  offset?: number;
}

/**
 * Sorting Options DTO
 */
export class SortingOptionsDto {
  @ApiProperty({
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @IsString()
  field: string;

  @ApiProperty({
    description: 'Sort direction',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsEnum(SortDirection)
  direction: SortDirection;

  @ApiPropertyOptional({
    description: 'Whether to put null values first',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  nullsFirst?: boolean;
}

/**
 * Filter Options DTO
 */
export class FilterOptionsDto {
  @ApiProperty({
    description: 'Field to filter by',
    example: 'status',
  })
  @IsString()
  field: string;

  @ApiProperty({
    description: 'Filter operator',
    enum: FilterOperator,
    example: FilterOperator.EQUALS,
  })
  @IsEnum(FilterOperator)
  operator: FilterOperator;

  @ApiProperty({
    description: 'Filter value',
    example: 'active',
  })
  value: any;

  @ApiPropertyOptional({
    description: 'Multiple values for IN/NOT_IN operators',
    example: ['active', 'pending'],
  })
  @IsOptional()
  @IsArray()
  values?: any[];
}

/**
 * Search Options DTO
 */
export class SearchOptionsDto {
  @ApiProperty({
    description: 'Search query string',
    example: 'john doe',
  })
  @IsString()
  query: string;

  @ApiPropertyOptional({
    description: 'Fields to search in',
    example: ['name', 'email', 'username'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fields?: string[];

  @ApiPropertyOptional({
    description: 'Enable fuzzy search',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  fuzzy?: boolean;

  @ApiPropertyOptional({
    description: 'Case sensitive search',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  caseSensitive?: boolean;

  @ApiPropertyOptional({
    description: 'Match whole words only',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  wholeWord?: boolean;

  @ApiPropertyOptional({
    description: 'Highlight search terms in results',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  highlight?: boolean;
}

/**
 * Paginated Response DTO
 */
export class PaginatedResponseDto<T = any> extends BaseResponseDto<T[]> {
  @ApiProperty({
    description: 'Always true for successful paginated responses',
    example: true,
  })
  success: true;

  @ApiProperty({
    description: 'Array of items for the current page',
    type: 'array',
    example: [],
  })
  data: T[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: PaginationMetaDto,
  })
  @ValidateNested()
  @Type(() => PaginationMetaDto)
  pagination: PaginationMetaDto;

  @ApiPropertyOptional({
    description: 'Applied filters',
    type: [FilterOptionsDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterOptionsDto)
  filters?: FilterOptionsDto[];

  @ApiPropertyOptional({
    description: 'Applied sorting',
    type: [SortingOptionsDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SortingOptionsDto)
  sorting?: SortingOptionsDto[];

  @ApiPropertyOptional({
    description: 'Applied search options',
    type: SearchOptionsDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SearchOptionsDto)
  search?: SearchOptionsDto;
}

/**
 * Cursor Pagination DTO
 */
export class CursorPaginationDto {
  @ApiPropertyOptional({
    description: 'Current cursor position',
    example: 'eyJpZCI6MTIzLCJjcmVhdGVkQXQiOiIyMDI1LTA2LTA4VDEyOjAwOjAwLjAwMFoifQ==',
  })
  @IsOptional()
  @IsString()
  cursor?: string;

  @ApiProperty({
    description: 'Number of items to return',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsNumber()
  limit: number;

  @ApiProperty({
    description: 'Whether there are more items after this cursor',
    example: true,
  })
  @IsBoolean()
  hasNext: boolean;

  @ApiProperty({
    description: 'Whether there are items before this cursor',
    example: false,
  })
  @IsBoolean()
  hasPrev: boolean;

  @ApiPropertyOptional({
    description: 'Cursor for the next page',
    example: 'eyJpZCI6MTQzLCJjcmVhdGVkQXQiOiIyMDI1LTA2LTA4VDEzOjAwOjAwLjAwMFoifQ==',
  })
  @IsOptional()
  @IsString()
  nextCursor?: string;

  @ApiPropertyOptional({
    description: 'Cursor for the previous page',
    example: null,
  })
  @IsOptional()
  @IsString()
  prevCursor?: string;
}

/**
 * Cursor Paginated Response DTO
 */
export class CursorPaginatedResponseDto<T = any> extends BaseResponseDto<T[]> {
  @ApiProperty({
    description: 'Always true for successful cursor paginated responses',
    example: true,
  })
  success: true;

  @ApiProperty({
    description: 'Array of items for the current cursor position',
    type: 'array',
  })
  data: T[];

  @ApiProperty({
    description: 'Cursor pagination metadata',
    type: CursorPaginationDto,
  })
  @ValidateNested()
  @Type(() => CursorPaginationDto)
  cursor: CursorPaginationDto;

  @ApiPropertyOptional({
    description: 'Applied filters',
    type: [FilterOptionsDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterOptionsDto)
  filters?: FilterOptionsDto[];

  @ApiPropertyOptional({
    description: 'Applied sorting',
    type: [SortingOptionsDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SortingOptionsDto)
  sorting?: SortingOptionsDto[];

  @ApiPropertyOptional({
    description: 'Applied search options',
    type: SearchOptionsDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SearchOptionsDto)
  search?: SearchOptionsDto;
}

/**
 * Aggregation Options DTO
 */
export class AggregationOptionsDto {
  @ApiProperty({
    description: 'Field to aggregate',
    example: 'price',
  })
  @IsString()
  field: string;

  @ApiProperty({
    description: 'Aggregation operation',
    example: 'sum',
    enum: ['count', 'sum', 'avg', 'min', 'max', 'group_by'],
  })
  @IsString()
  operation: string;

  @ApiPropertyOptional({
    description: 'Alias for the aggregation result',
    example: 'total_price',
  })
  @IsOptional()
  @IsString()
  alias?: string;
}

/**
 * Aggregated Response DTO
 */
export class AggregatedResponseDto<T = any> extends BaseResponseDto<T[]> {
  @ApiProperty({
    description: 'Always true for successful aggregated responses',
    example: true,
  })
  success: true;

  @ApiProperty({
    description: 'Array of aggregated data',
    type: 'array',
  })
  data: T[];

  @ApiProperty({
    description: 'Aggregation results',
    example: {
      total_price: 1500.50,
      avg_price: 75.025,
      count: 20,
    },
  })
  aggregations: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Applied filters',
    type: [FilterOptionsDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterOptionsDto)
  filters?: FilterOptionsDto[];

  @ApiPropertyOptional({
    description: 'Group by fields',
    example: ['category', 'status'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  groupBy?: string[];
}

/**
 * Empty List Response DTO
 */
export class EmptyListResponseDto extends PaginatedResponseDto<never> {
  @ApiProperty({
    description: 'Empty array for no results',
    example: [],
  })
  data: never[];

  @ApiProperty({
    description: 'Pagination metadata for empty results',
    example: {
      page: 1,
      limit: 20,
      totalCount: 0,
      totalPages: 0,
      hasNext: false,
      hasPrev: false,
    },
  })
  pagination: PaginationMetaDto;
}
