import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StandardizedPrismaService } from '../../../../../shared/data/services/prisma.service';
import { ServiceLoggerService } from '../../../logging/services/service-logger.service';
import { MetricsService } from '../../../../../shared/logging/services/metrics.service';

@Injectable()
export class RepositoryFactory {
  private readonly logger = new Logger(RepositoryFactory.name);
  private readonly repositories = new Map<string, any>();

  constructor(
    private readonly prisma: StandardizedPrismaService,
    private readonly serviceLogger: ServiceLoggerService,
    private readonly metricsService: MetricsService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Get or create repository instance
   */
  getRepository<T>(repositoryClass: new (...args: any[]) => T): T {
    const className = repositoryClass.name;
    
    if (!this.repositories.has(className)) {
      this.logger.debug(`Creating repository instance: ${className}`);
      
      const repository = new repositoryClass(
        this.prisma,
        this.serviceLogger,
        this.metricsService,
        this.configService,
      );
      
      this.repositories.set(className, repository);
      
      this.serviceLogger.getStructuredLogger().info(`Repository created: ${className}`, {
        metadata: {
          repository: className,
          timestamp: new Date().toISOString(),
        },
      });
    }

    return this.repositories.get(className);
  }

  /**
   * Register repository instance
   */
  registerRepository<T>(name: string, repository: T): void {
    this.repositories.set(name, repository);
    this.logger.debug(`Repository registered: ${name}`);
  }

  /**
   * Get all registered repositories
   */
  getAllRepositories(): Map<string, any> {
    return new Map(this.repositories);
  }

  /**
   * Clear all repositories (useful for testing)
   */
  clearRepositories(): void {
    this.repositories.clear();
    this.logger.debug('All repositories cleared');
  }

  /**
   * Get repository health status
   */
  async getRepositoriesHealthStatus(): Promise<Record<string, any>> {
    const healthStatus: Record<string, any> = {};

    for (const [name, repository] of this.repositories.entries()) {
      try {
        if (repository.healthCheck && typeof repository.healthCheck === 'function') {
          healthStatus[name] = await repository.healthCheck();
        } else {
          healthStatus[name] = {
            status: 'unknown',
            message: 'Health check not implemented',
          };
        }
      } catch (error) {
        healthStatus[name] = {
          status: 'unhealthy',
          error: error.message,
        };
      }
    }

    return healthStatus;
  }
}
