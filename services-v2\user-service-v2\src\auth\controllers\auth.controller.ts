/**
 * Auth Controller - User Service V2
 * 
 * Authentication endpoints using shared infrastructure
 */

import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Request } from 'express';
import {
  ResponseService,
  Public,
  CurrentUser,
  AuthenticatedUser,
  StandardizedJwtAuthGuard,
} from '../../../../../shared';
import { AuthService } from '../services/auth.service';
import { LoginDto } from '../dto/login.dto';
import { RefreshTokenDto } from '../dto/refresh-token.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly responseService: ResponseService,
  ) {}

  @Post('login')
  @Public()
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Body() loginDto: LoginDto, @Req() req: Request) {
    const ipAddress = this.getClientIP(req);
    const userAgent = req.headers['user-agent'];

    const result = await this.authService.login(loginDto, ipAddress, userAgent);
    return this.responseService.success(result, 'Login successful');
  }

  @Post('logout')
  @UseGuards(StandardizedJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'User logout' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  async logout(@CurrentUser() user: AuthenticatedUser, @Req() req: Request) {
    const refreshToken = req.headers['x-refresh-token'] as string;
    const result = await this.authService.logout(user.id, refreshToken);
    return this.responseService.success(result, 'Logout successful');
  }

  @Post('refresh')
  @Public()
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refresh(@Body() refreshTokenDto: RefreshTokenDto) {
    const result = await this.authService.refreshToken(refreshTokenDto.refreshToken);
    return this.responseService.success(result, 'Token refreshed successfully');
  }

  @Get('me')
  @UseGuards(StandardizedJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user information' })
  @ApiResponse({ status: 200, description: 'User information retrieved' })
  async getCurrentUser(@CurrentUser() user: AuthenticatedUser) {
    const userInfo = await this.authService.validateUser(user.id);
    return this.responseService.success(userInfo, 'User information retrieved');
  }

  @Get('sessions')
  @UseGuards(StandardizedJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user active sessions' })
  @ApiResponse({ status: 200, description: 'Sessions retrieved successfully' })
  async getSessions(@CurrentUser() user: AuthenticatedUser) {
    const sessions = await this.authService.getUserSessions(user.id);
    return this.responseService.success(sessions, 'Sessions retrieved successfully');
  }

  @Delete('sessions/:sessionId')
  @UseGuards(StandardizedJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Revoke a specific session' })
  @ApiResponse({ status: 200, description: 'Session revoked successfully' })
  @ApiResponse({ status: 401, description: 'Session not found or already revoked' })
  async revokeSession(
    @Param('sessionId') sessionId: string,
    @CurrentUser() user: AuthenticatedUser,
  ) {
    const result = await this.authService.revokeSession(user.id, sessionId);
    return this.responseService.success(result, 'Session revoked successfully');
  }

  @Post('logout-all')
  @UseGuards(StandardizedJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout from all devices' })
  @ApiResponse({ status: 200, description: 'Logged out from all devices' })
  async logoutAll(@CurrentUser() user: AuthenticatedUser) {
    const result = await this.authService.logout(user.id);
    return this.responseService.success(result, 'Logged out from all devices');
  }

  /**
   * Get client IP address
   */
  private getClientIP(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
      req.headers['x-real-ip'] as string ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    );
  }
}
