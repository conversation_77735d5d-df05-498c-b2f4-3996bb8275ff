#!/bin/bash

# Comprehensive Security Testing Script
# Tests all services for proper gateway authentication implementation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
GATEWAY_SECRET="default-gateway-secret-change-in-production"
API_GATEWAY_URL="http://localhost:3010"

# Services to test (with their direct ports)
declare -A SERVICES=(
    ["user-service"]="3011"
    ["profile-analysis-service"]="3002"
    ["nft-generation-service"]="3003"
    ["blockchain-service"]="3004"
    ["project-service"]="3005"
    ["marketplace-service"]="3006"
    ["notification-service"]="3008"
    ["analytics-service"]="3009"
)

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

echo -e "${BLUE}🔒 COMPREHENSIVE SECURITY TESTING${NC}"
echo -e "${BLUE}==================================${NC}"
echo ""

# Function to test direct access (should be blocked)
test_direct_access_blocked() {
    local service=$1
    local port=$2
    local endpoint="/"
    
    echo -e "${YELLOW}🔍 Testing direct access blocking for $service...${NC}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Test direct access without headers (should be blocked)
    response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:$port$endpoint" || echo "000")
    
    if [ "$response" = "403" ]; then
        echo -e "${GREEN}✅ $service: Direct access properly blocked (403)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    elif [ "$response" = "000" ]; then
        echo -e "${RED}❌ $service: Service not responding (connection failed)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    else
        echo -e "${RED}❌ $service: Direct access NOT blocked (got $response, expected 403)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Function to test gateway access (should work)
test_gateway_access_works() {
    local service=$1
    local port=$2
    local endpoint="/"
    
    echo -e "${YELLOW}🔍 Testing gateway access for $service...${NC}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Test access with proper gateway headers (should work)
    response=$(curl -s -w "%{http_code}" -o /dev/null \
        -H "x-gateway-auth: $GATEWAY_SECRET" \
        -H "x-forwarded-by: api-gateway" \
        "http://localhost:$port$endpoint" || echo "000")
    
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ $service: Gateway access works (200)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    elif [ "$response" = "000" ]; then
        echo -e "${RED}❌ $service: Service not responding (connection failed)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    else
        echo -e "${RED}❌ $service: Gateway access failed (got $response, expected 200)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Function to test API Gateway routing
test_api_gateway_routing() {
    local service=$1
    
    echo -e "${YELLOW}🔍 Testing API Gateway routing for $service...${NC}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Map service names to API Gateway endpoints
    local gateway_endpoint=""
    case $service in
        "user-service") gateway_endpoint="/api/users/health" ;;
        "profile-analysis-service") gateway_endpoint="/api/analysis/health" ;;
        "nft-generation-service") gateway_endpoint="/api/nft-generation/health" ;;
        "blockchain-service") gateway_endpoint="/api/blockchain/health" ;;
        "project-service") gateway_endpoint="/api/projects/health" ;;
        "marketplace-service") gateway_endpoint="/api/marketplace/health" ;;
        "notification-service") gateway_endpoint="/api/notifications/health" ;;
        "analytics-service") gateway_endpoint="/api/analytics/health" ;;
        *) 
            echo -e "${RED}❌ Unknown service: $service${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
            ;;
    esac
    
    # Test API Gateway routing
    response=$(curl -s -w "%{http_code}" -o /dev/null "$API_GATEWAY_URL$gateway_endpoint" || echo "000")
    
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ $service: API Gateway routing works (200)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    elif [ "$response" = "000" ]; then
        echo -e "${RED}❌ $service: API Gateway not responding${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    else
        echo -e "${RED}❌ $service: API Gateway routing failed (got $response, expected 200)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Function to test health check bypass
test_health_check_bypass() {
    local service=$1
    local port=$2
    
    echo -e "${YELLOW}🔍 Testing health check bypass for $service...${NC}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Test health check access without gateway headers (should work due to bypass)
    response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:$port/health" || echo "000")
    
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ $service: Health check bypass works (200)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    elif [ "$response" = "000" ]; then
        echo -e "${RED}❌ $service: Service not responding${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    else
        echo -e "${YELLOW}⚠️  $service: Health check bypass not working (got $response)${NC}"
        # This might be expected if health checks are also protected
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    fi
}

# Main testing loop
echo -e "${BLUE}📋 Testing ${#SERVICES[@]} services...${NC}"
echo ""

for service in "${!SERVICES[@]}"; do
    port=${SERVICES[$service]}
    
    echo -e "${BLUE}🔄 Testing $service (port $port)${NC}"
    echo "----------------------------------------"
    
    # Test 1: Direct access should be blocked
    test_direct_access_blocked "$service" "$port"
    
    # Test 2: Gateway access should work
    test_gateway_access_works "$service" "$port"
    
    # Test 3: API Gateway routing should work
    test_api_gateway_routing "$service"
    
    # Test 4: Health check bypass
    test_health_check_bypass "$service" "$port"
    
    echo ""
done

# Summary
echo -e "${BLUE}📊 SECURITY TEST SUMMARY${NC}"
echo -e "${BLUE}========================${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 ALL SECURITY TESTS PASSED!${NC}"
    echo -e "${GREEN}✅ Gateway authentication is properly implemented across all services${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}❌ SOME SECURITY TESTS FAILED${NC}"
    echo -e "${RED}⚠️  Please review the failed tests above${NC}"
    exit 1
fi
