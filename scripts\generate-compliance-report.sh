#!/bin/bash

# Generate Comprehensive Compliance Report
# Creates detailed compliance report for all enterprise standardization phases

set -e

echo "📊 Generating Enterprise Standardization Compliance Report"
echo "=========================================================="
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Services to analyze
SERVICES=(
    "api-gateway"
    "user-service"
    "profile-analysis-service"
    "nft-generation-service"
    "blockchain-service"
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Create reports directory
mkdir -p reports/compliance
REPORT_DATE=$(date +%Y%m%d_%H%M%S)
REPORT_FILE="reports/compliance/enterprise_compliance_report_$REPORT_DATE.md"
HTML_REPORT="reports/compliance/enterprise_compliance_report_$REPORT_DATE.html"

echo "📝 Generating report: $REPORT_FILE"
echo ""

# Function to print colored output
print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Initialize counters
TOTAL_SERVICES=${#SERVICES[@]}
COMPLIANT_SERVICES=0
PARTIAL_SERVICES=0
NON_COMPLIANT_SERVICES=0

# Function to check service compliance
check_service_compliance() {
    local service_name="$1"
    local service_dir="services/$service_name"
    local compliance_score=0
    local max_score=24  # 4 points per phase (6 phases)
    
    # Phase 1: Environment Variables (4 points)
    [ -f "$service_dir/.env.example" ] && ((compliance_score++))
    [ -f "$service_dir/.env" ] && ((compliance_score++))
    grep -q "SERVICE_NAME" "$service_dir/.env.example" 2>/dev/null && ((compliance_score++))
    grep -q "SERVICE_PORT" "$service_dir/.env.example" 2>/dev/null && ((compliance_score++))
    
    # Phase 2: Configuration (4 points)
    [ -d "$service_dir/src/config" ] && ((compliance_score++))
    [ -f "$service_dir/src/config/configuration.module.ts" ] && ((compliance_score++))
    [ -f "$service_dir/src/config/configuration.service.ts" ] && ((compliance_score++))
    [ -d "$service_dir/src/config/schemas" ] && ((compliance_score++))
    
    # Phase 3: Authentication (4 points)
    [ -d "$service_dir/src/auth" ] && ((compliance_score++))
    [ -f "$service_dir/src/auth/auth.module.ts" ] && ((compliance_score++))
    grep -q "JwtAuthGuard" "$service_dir/src/app.module.ts" 2>/dev/null && ((compliance_score++))
    grep -q "ServiceAuthModule" "$service_dir/src/app.module.ts" 2>/dev/null && ((compliance_score++))
    
    # Phase 4: Responses (4 points)
    [ -d "$service_dir/src/responses" ] && ((compliance_score++))
    [ -f "$service_dir/src/responses/responses.module.ts" ] && ((compliance_score++))
    [ -f "$service_dir/src/responses/services/response.service.ts" ] && ((compliance_score++))
    grep -q "ServiceResponseModule" "$service_dir/src/app.module.ts" 2>/dev/null && ((compliance_score++))
    
    # Phase 5: Logging (4 points)
    [ -d "$service_dir/src/logging" ] && ((compliance_score++))
    [ -f "$service_dir/src/logging/logging.module.ts" ] && ((compliance_score++))
    [ -f "$service_dir/src/logging/services/service-logger.service.ts" ] && ((compliance_score++))
    grep -q "ServiceLoggingModule" "$service_dir/src/app.module.ts" 2>/dev/null && ((compliance_score++))
    
    # Phase 6: Data Layer (4 points)
    [ -d "$service_dir/src/data" ] && ((compliance_score++))
    [ -f "$service_dir/src/data/data.module.ts" ] && ((compliance_score++))
    [ -f "$service_dir/src/data/services/database.service.ts" ] && ((compliance_score++))
    grep -q "ServiceDataModule" "$service_dir/src/app.module.ts" 2>/dev/null && ((compliance_score++))
    
    # Calculate percentage
    local compliance_percentage=$((compliance_score * 100 / max_score))
    
    # Determine compliance level
    local compliance_level
    if [ $compliance_percentage -ge 90 ]; then
        compliance_level="FULLY_COMPLIANT"
        ((COMPLIANT_SERVICES++))
    elif [ $compliance_percentage -ge 70 ]; then
        compliance_level="PARTIALLY_COMPLIANT"
        ((PARTIAL_SERVICES++))
    else
        compliance_level="NON_COMPLIANT"
        ((NON_COMPLIANT_SERVICES++))
    fi
    
    echo "$service_name,$compliance_score,$max_score,$compliance_percentage,$compliance_level"
}

# Start generating report
cat > "$REPORT_FILE" << 'EOF'
# 📊 Enterprise Standardization Compliance Report

**Generated on:** $(date)
**Platform:** Social NFT Platform
**Report Type:** Complete Enterprise Standardization Compliance

---

## 🎯 Executive Summary

This report provides a comprehensive analysis of enterprise standardization compliance across all microservices in the Social NFT Platform.

### **Standardization Phases Evaluated:**
1. **Environment Variables Standardization**
2. **Application Configuration Standardization**
3. **Authentication Patterns Standardization**
4. **API Response Format Standardization**
5. **Logging and Monitoring Standards**
6. **Data Layer Standardization**

---

## 📊 Overall Compliance Statistics

EOF

# Add current date to report
sed -i "s/\$(date)/$(date)/" "$REPORT_FILE"

# Generate service compliance data
echo "🔍 Analyzing service compliance..."
echo ""

# Create temporary file for compliance data
TEMP_COMPLIANCE_FILE=$(mktemp)

for service_name in "${SERVICES[@]}"; do
    print_info "Analyzing: $service_name"
    check_service_compliance "$service_name" >> "$TEMP_COMPLIANCE_FILE"
done

# Calculate overall statistics
OVERALL_COMPLIANCE_PERCENTAGE=$(awk -F',' '{sum+=$4; count++} END {if(count>0) print int(sum/count); else print 0}' "$TEMP_COMPLIANCE_FILE")

# Add statistics to report
cat >> "$REPORT_FILE" << EOF

| Metric | Value |
|--------|-------|
| **Total Services** | $TOTAL_SERVICES |
| **Fully Compliant Services** | $COMPLIANT_SERVICES |
| **Partially Compliant Services** | $PARTIAL_SERVICES |
| **Non-Compliant Services** | $NON_COMPLIANT_SERVICES |
| **Overall Compliance Rate** | $OVERALL_COMPLIANCE_PERCENTAGE% |

### **Compliance Distribution**
- 🟢 **Fully Compliant (90-100%)**: $COMPLIANT_SERVICES services
- 🟡 **Partially Compliant (70-89%)**: $PARTIAL_SERVICES services
- 🔴 **Non-Compliant (<70%)**: $NON_COMPLIANT_SERVICES services

---

## 📋 Service-by-Service Compliance Analysis

| Service | Compliance Score | Percentage | Status |
|---------|------------------|------------|--------|
EOF

# Add service compliance data to report
while IFS=',' read -r service_name score max_score percentage level; do
    case $level in
        "FULLY_COMPLIANT")
            status_icon="🟢 COMPLIANT"
            ;;
        "PARTIALLY_COMPLIANT")
            status_icon="🟡 PARTIAL"
            ;;
        "NON_COMPLIANT")
            status_icon="🔴 NON-COMPLIANT"
            ;;
    esac
    
    echo "| $service_name | $score/$max_score | $percentage% | $status_icon |" >> "$REPORT_FILE"
done < "$TEMP_COMPLIANCE_FILE"

# Add detailed phase analysis
cat >> "$REPORT_FILE" << 'EOF'

---

## 🔍 Phase-by-Phase Analysis

### **Phase 1: Environment Variables Standardization**
- **Objective**: Centralized environment variable management
- **Key Components**: `.env` files, environment validation, configuration documentation
- **Compliance Criteria**: 
  - ✅ `.env.example` file exists
  - ✅ Service-specific `.env` file exists
  - ✅ Required environment variables documented
  - ✅ Environment validation implemented

### **Phase 2: Application Configuration Standardization**
- **Objective**: Type-safe configuration classes with validation
- **Key Components**: Configuration modules, validation schemas, dependency injection
- **Compliance Criteria**:
  - ✅ Configuration directory structure
  - ✅ Configuration module implementation
  - ✅ Configuration service implementation
  - ✅ Configuration schemas with validation

### **Phase 3: Authentication Patterns Standardization**
- **Objective**: Enterprise-grade JWT authentication with RBAC
- **Key Components**: JWT guards, role-based access, permission system
- **Compliance Criteria**:
  - ✅ Authentication module structure
  - ✅ JWT authentication guards
  - ✅ Role-based access control
  - ✅ Permission management system

### **Phase 4: API Response Format Standardization**
- **Objective**: Unified API response formats with error handling
- **Key Components**: Response services, error handling, pagination
- **Compliance Criteria**:
  - ✅ Response module structure
  - ✅ Response service implementation
  - ✅ Standardized error handling
  - ✅ Pagination support

### **Phase 5: Logging and Monitoring Standards**
- **Objective**: Structured logging with comprehensive monitoring
- **Key Components**: Structured logging, metrics collection, health monitoring
- **Compliance Criteria**:
  - ✅ Logging module structure
  - ✅ Structured logging service
  - ✅ Metrics collection
  - ✅ Health monitoring implementation

### **Phase 6: Data Layer Standardization**
- **Objective**: Repository pattern with enterprise Prisma service
- **Key Components**: Repository pattern, database health monitoring, transaction management
- **Compliance Criteria**:
  - ✅ Data module structure
  - ✅ Database service implementation
  - ✅ Repository pattern implementation
  - ✅ Database health monitoring

---

## 🚨 Compliance Issues and Recommendations

EOF

# Analyze compliance issues
echo "🔍 Analyzing compliance issues..."

# Check for common issues
ISSUES_FOUND=0

# Check for missing shared infrastructure
if [ ! -d "shared/auth" ]; then
    echo "### ❌ Critical Issue: Missing Shared Authentication Infrastructure" >> "$REPORT_FILE"
    echo "**Impact**: High - Authentication patterns cannot be implemented consistently" >> "$REPORT_FILE"
    echo "**Recommendation**: Implement shared authentication modules" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    ((ISSUES_FOUND++))
fi

if [ ! -d "shared/responses" ]; then
    echo "### ❌ Critical Issue: Missing Shared Response Infrastructure" >> "$REPORT_FILE"
    echo "**Impact**: High - API responses cannot be standardized" >> "$REPORT_FILE"
    echo "**Recommendation**: Implement shared response modules" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    ((ISSUES_FOUND++))
fi

if [ ! -d "shared/logging" ]; then
    echo "### ❌ Critical Issue: Missing Shared Logging Infrastructure" >> "$REPORT_FILE"
    echo "**Impact**: High - Structured logging cannot be implemented" >> "$REPORT_FILE"
    echo "**Recommendation**: Implement shared logging modules" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    ((ISSUES_FOUND++))
fi

if [ ! -d "shared/data" ]; then
    echo "### ❌ Critical Issue: Missing Shared Data Infrastructure" >> "$REPORT_FILE"
    echo "**Impact**: High - Repository pattern cannot be implemented" >> "$REPORT_FILE"
    echo "**Recommendation**: Implement shared data modules" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    ((ISSUES_FOUND++))
fi

# Check for forbidden patterns
FORBIDDEN_PATTERNS=0

# Check for direct process.env usage
if find services -name "*.ts" -not -path "*/node_modules/*" -exec grep -l "process\.env\." {} \; >/dev/null 2>&1; then
    echo "### ⚠️ Pattern Violation: Direct process.env Usage" >> "$REPORT_FILE"
    echo "**Impact**: Medium - Configuration not centralized" >> "$REPORT_FILE"
    echo "**Recommendation**: Use ConfigService instead of direct process.env access" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    ((FORBIDDEN_PATTERNS++))
fi

# Check for console logging
if find services -name "*.ts" -not -path "*/node_modules/*" -exec grep -l "console\.log\|console\.error" {} \; >/dev/null 2>&1; then
    echo "### ⚠️ Pattern Violation: Console Logging Usage" >> "$REPORT_FILE"
    echo "**Impact**: Medium - Logging not structured" >> "$REPORT_FILE"
    echo "**Recommendation**: Use ServiceLoggerService instead of console logging" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    ((FORBIDDEN_PATTERNS++))
fi

if [ $ISSUES_FOUND -eq 0 ] && [ $FORBIDDEN_PATTERNS -eq 0 ]; then
    echo "### ✅ No Critical Issues Found" >> "$REPORT_FILE"
    echo "All services are following enterprise standardization patterns correctly." >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
fi

# Add recommendations section
cat >> "$REPORT_FILE" << 'EOF'

---

## 🎯 Recommendations for Improvement

### **For Fully Compliant Services**
- ✅ Continue following established patterns
- 📊 Monitor compliance through automated checks
- 🔄 Keep documentation updated
- 🧪 Maintain comprehensive test coverage

### **For Partially Compliant Services**
- 🔧 Address missing components identified in analysis
- 📋 Follow implementation guides for missing phases
- ✅ Run validation scripts to verify improvements
- 📚 Review documentation for proper implementation

### **For Non-Compliant Services**
- 🚨 **Priority**: Implement missing standardization phases
- 📖 Follow the Enterprise Standardization Guide
- 🛠️ Use implementation scripts for automated setup
- 🔍 Run frequent validation checks during implementation

---

## 🛠️ Next Steps

1. **Address Critical Issues**: Fix any missing shared infrastructure
2. **Improve Partial Compliance**: Complete missing standardization phases
3. **Eliminate Pattern Violations**: Remove forbidden patterns from codebase
4. **Implement Monitoring**: Set up continuous compliance monitoring
5. **Training**: Ensure all developers understand standardization requirements

---

## 📚 Resources

- [Enterprise Standardization Guide](docs/ENTERPRISE_STANDARDIZATION_GUIDE.md)
- [Development Rules and Enforcement](docs/DEVELOPMENT_RULES_AND_ENFORCEMENT.md)
- [Implementation Examples](docs/examples/COMPLETE_IMPLEMENTATION_EXAMPLES.md)
- [Troubleshooting Guide](docs/TROUBLESHOOTING_AND_FAQ.md)

---

**Report Generated**: $(date)
**Next Review**: Recommended within 30 days
**Compliance Target**: 95% overall compliance rate

EOF

# Add current date to final report
sed -i "s/\$(date)/$(date)/" "$REPORT_FILE"

# Clean up temporary file
rm "$TEMP_COMPLIANCE_FILE"

# Generate HTML version
print_info "Generating HTML report..."

# Simple HTML conversion (basic)
cat > "$HTML_REPORT" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Enterprise Standardization Compliance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1, h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f2f2f2; }
        .compliant { color: #28a745; }
        .partial { color: #ffc107; }
        .non-compliant { color: #dc3545; }
        .summary-box { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
EOF

# Convert markdown to basic HTML (simplified)
sed 's/^# /\<h1\>/g; s/^## /\<h2\>/g; s/^### /\<h3\>/g; s/$/\<\/h1\>/g' "$REPORT_FILE" | \
sed 's/\*\*\([^*]*\)\*\*/\<strong\>\1\<\/strong\>/g' | \
sed 's/`\([^`]*\)`/\<code\>\1\<\/code\>/g' >> "$HTML_REPORT"

echo "</body></html>" >> "$HTML_REPORT"

# Display results
echo ""
print_success "Compliance report generated successfully!"
echo ""
echo "📄 Reports created:"
echo "  • Markdown: $REPORT_FILE"
echo "  • HTML: $HTML_REPORT"
echo ""

# Display summary
echo "📊 Compliance Summary:"
echo "  • Total Services: $TOTAL_SERVICES"
echo "  • Fully Compliant: $COMPLIANT_SERVICES"
echo "  • Partially Compliant: $PARTIAL_SERVICES"
echo "  • Non-Compliant: $NON_COMPLIANT_SERVICES"
echo "  • Overall Compliance: $OVERALL_COMPLIANCE_PERCENTAGE%"
echo ""

# Determine exit status based on compliance
if [ $OVERALL_COMPLIANCE_PERCENTAGE -ge 90 ]; then
    print_success "Excellent compliance rate! 🎉"
    exit 0
elif [ $OVERALL_COMPLIANCE_PERCENTAGE -ge 70 ]; then
    print_warning "Good compliance rate, but room for improvement"
    exit 0
else
    print_error "Compliance rate below target - action required"
    exit 1
fi
