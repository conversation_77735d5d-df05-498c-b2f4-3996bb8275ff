import {
  <PERSON>,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Res,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { ProjectCommandService } from '../services/project-command.service';

@ApiTags('Projects')
@Controller('projects')
export class ProjectCommandController {
  private readonly logger = new Logger(ProjectCommandController.name);

  constructor(private readonly projectCommandService: ProjectCommandService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new project',
    description: 'Create a new project with configuration and settings'
  })
  @ApiBody({
    description: 'Project creation data',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'My NFT Project' },
        description: { type: 'string', example: 'A revolutionary NFT project' },
        category: { type: 'string', example: 'art' },
        blockchainNetwork: { type: 'string', example: 'ethereum' },
      },
      required: ['name', 'description']
    }
  })
  @ApiResponse({ status: 201, description: 'Project created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid project data' })
  async createProject(@Body() projectData: any, @Res() res: Response) {
    try {
      this.logger.log('Creating new project');
      
      const result = await this.projectCommandService.createProject(projectData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Project creation failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update project',
    description: 'Update project details and configuration'
  })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiBody({
    description: 'Project update data',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        description: { type: 'string' },
        category: { type: 'string' },
        status: { type: 'string' },
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Project updated successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async updateProject(
    @Param('id') id: string,
    @Body() updateData: any,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Updating project: ${id}`);
      
      const result = await this.projectCommandService.updateProject(id, updateData);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Project update failed for ID ${id}: ${error.message}`, error.stack);
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete project',
    description: 'Delete a project and all associated data'
  })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiResponse({ status: 200, description: 'Project deleted successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async deleteProject(@Param('id') id: string, @Res() res: Response) {
    try {
      this.logger.log(`Deleting project: ${id}`);
      
      const result = await this.projectCommandService.deleteProject(id);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Project deletion failed for ID ${id}: ${error.message}`, error.stack);
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
