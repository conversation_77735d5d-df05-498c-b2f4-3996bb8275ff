import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseService } from './database.service';
import { RepositoryFactory } from './repository-factory.service';
import { HealthMonitoringService } from '../../../logging/services/health-monitoring.service';
import { HealthStatus } from '../../../../../shared/logging/interfaces/metrics.interface';

@Injectable()
export class DataHealthService {
  private readonly logger = new Logger(DataHealthService.name);
  private readonly serviceName: string;

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly repositoryFactory: RepositoryFactory,
    private readonly healthMonitoring: HealthMonitoringService,
    private readonly configService: ConfigService,
  ) {
    this.serviceName = this.configService.get<string>('SERVICE_NAME') || 'unknown-service';
  }

  /**
   * Perform comprehensive data layer health check
   */
  async performHealthCheck(): Promise<{
    status: 'healthy' | 'unhealthy' | 'degraded';
    components: Record<string, any>;
    overall: {
      responseTime: number;
      timestamp: string;
    };
  }> {
    const startTime = Date.now();
    const components: Record<string, any> = {};
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';

    try {
      // Database health check
      const dbHealth = await this.databaseService.getHealthStatus();
      components.database = dbHealth;

      if (dbHealth.status !== 'healthy') {
        overallStatus = dbHealth.status === 'unhealthy' ? 'unhealthy' : 'degraded';
      }

      // Record database health metric
      this.healthMonitoring.recordDatabaseHealth(
        dbHealth.status as HealthStatus,
        dbHealth.responseTime,
        dbHealth.details
      );

      // Repository health checks
      const repoHealth = await this.repositoryFactory.getRepositoriesHealthStatus();
      components.repositories = repoHealth;

      // Check repository health status
      for (const [name, health] of Object.entries(repoHealth)) {
        if (health.status === 'unhealthy') {
          overallStatus = 'unhealthy';
        } else if (health.status === 'degraded' && overallStatus === 'healthy') {
          overallStatus = 'degraded';
        }
      }

      // Connection status
      components.connection = {
        status: this.databaseService.isConnected() ? 'connected' : 'disconnected',
        service: this.serviceName,
      };

      if (!this.databaseService.isConnected()) {
        overallStatus = 'unhealthy';
      }

      const responseTime = Date.now() - startTime;

      // Record overall service health
      this.healthMonitoring.recordServiceHealth(
        overallStatus as HealthStatus,
        {
          components,
          responseTime,
        }
      );

      return {
        status: overallStatus,
        components,
        overall: {
          responseTime,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error('Data health check failed:', error);

      // Record unhealthy status
      this.healthMonitoring.recordServiceHealth(HealthStatus.UNHEALTHY, {
        error: error.message,
        components,
      });

      return {
        status: 'unhealthy',
        components: {
          ...components,
          error: {
            message: error.message,
            timestamp: new Date().toISOString(),
          },
        },
        overall: {
          responseTime: Date.now() - startTime,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Get database metrics
   */
  async getDatabaseMetrics(): Promise<Record<string, any>> {
    try {
      const dbHealth = await this.databaseService.getHealthStatus();
      const repoHealth = await this.repositoryFactory.getRepositoriesHealthStatus();

      return {
        database: {
          status: dbHealth.status,
          responseTime: dbHealth.responseTime,
          details: dbHealth.details,
        },
        repositories: repoHealth,
        connection: {
          status: this.databaseService.isConnected() ? 'connected' : 'disconnected',
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Test database connectivity
   */
  async testConnectivity(): Promise<boolean> {
    try {
      const health = await this.databaseService.getHealthStatus();
      return health.status === 'healthy';
    } catch (error) {
      this.logger.error('Connectivity test failed:', error);
      return false;
    }
  }
}
