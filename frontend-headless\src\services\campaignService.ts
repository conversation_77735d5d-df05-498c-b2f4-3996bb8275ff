import { api } from '@/lib/api'
import {
  Campaign,
  CampaignAnalytics,
  CampaignParticipation,
  CampaignSubmission,
  CreateCampaignRequest,
  UpdateCampaignRequest,
  CampaignFilters,
  CampaignTemplate,
  CampaignStatus,
  ParticipationStatus,
  SubmissionStatus,
  RequirementType,
  RewardType
} from '@/types/campaign.types'

export class CampaignService {
  // ===== CAMPAIGN CRUD OPERATIONS =====
  
  async getCampaigns(filters?: CampaignFilters): Promise<{
    campaigns: Campaign[]
    total: number
    page: number
    limit: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get('/campaigns', { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaigns:', error)
      throw new Error('Failed to load campaigns')
    }
  }

  async getCampaign(id: string): Promise<Campaign> {
    try {
      const response = await api.get(`/campaigns/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign:', error)
      throw new Error('Failed to load campaign details')
    }
  }

  async createCampaign(data: CreateCampaignRequest): Promise<Campaign> {
    try {
      const response = await api.post('/campaigns', data)
      return response.data
    } catch (error) {
      console.error('Failed to create campaign:', error)
      throw new Error('Failed to create campaign')
    }
  }

  async updateCampaign(id: string, data: UpdateCampaignRequest): Promise<Campaign> {
    try {
      const response = await api.patch(`/campaigns/${id}`, data)
      return response.data
    } catch (error) {
      console.error('Failed to update campaign:', error)
      throw new Error('Failed to update campaign')
    }
  }

  async deleteCampaign(id: string): Promise<void> {
    try {
      await api.delete(`/campaigns/${id}`)
    } catch (error) {
      console.error('Failed to delete campaign:', error)
      throw new Error('Failed to delete campaign')
    }
  }

  // ===== CAMPAIGN STATUS MANAGEMENT =====

  async startCampaign(id: string): Promise<Campaign> {
    try {
      const response = await api.patch(`/campaigns/${id}/start`)
      return response.data
    } catch (error) {
      console.error('Failed to start campaign:', error)
      throw new Error('Failed to start campaign')
    }
  }

  async pauseCampaign(id: string): Promise<Campaign> {
    try {
      const response = await api.patch(`/campaigns/${id}/pause`)
      return response.data
    } catch (error) {
      console.error('Failed to pause campaign:', error)
      throw new Error('Failed to pause campaign')
    }
  }

  async resumeCampaign(id: string): Promise<Campaign> {
    try {
      const response = await api.patch(`/campaigns/${id}/resume`)
      return response.data
    } catch (error) {
      console.error('Failed to resume campaign:', error)
      throw new Error('Failed to resume campaign')
    }
  }

  async completeCampaign(id: string): Promise<Campaign> {
    try {
      const response = await api.patch(`/campaigns/${id}/complete`)
      return response.data
    } catch (error) {
      console.error('Failed to complete campaign:', error)
      throw new Error('Failed to complete campaign')
    }
  }

  async cancelCampaign(id: string, reason?: string): Promise<Campaign> {
    try {
      const response = await api.patch(`/campaigns/${id}/cancel`, { reason })
      return response.data
    } catch (error) {
      console.error('Failed to cancel campaign:', error)
      throw new Error('Failed to cancel campaign')
    }
  }

  // ===== CAMPAIGN PARTICIPATION =====

  async getCampaignParticipants(campaignId: string, filters?: {
    status?: ParticipationStatus
    search?: string
    page?: number
    limit?: number
  }): Promise<{
    participants: CampaignParticipation[]
    total: number
    page: number
    limit: number
  }> {
    try {
      const response = await api.get(`/campaigns/${campaignId}/participants`, { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign participants:', error)
      throw new Error('Failed to load campaign participants')
    }
  }

  async joinCampaign(campaignId: string, data: {
    twitterHandle?: string
    walletAddress?: string
    metadata?: Record<string, any>
  }): Promise<CampaignParticipation> {
    try {
      const response = await api.post(`/campaigns/${campaignId}/join`, data)
      return response.data
    } catch (error) {
      console.error('Failed to join campaign:', error)
      throw new Error('Failed to join campaign')
    }
  }

  async leaveCampaign(campaignId: string): Promise<void> {
    try {
      await api.delete(`/campaigns/${campaignId}/leave`)
    } catch (error) {
      console.error('Failed to leave campaign:', error)
      throw new Error('Failed to leave campaign')
    }
  }

  async getUserParticipation(campaignId: string, userId?: string): Promise<CampaignParticipation | null> {
    try {
      const endpoint = userId 
        ? `/campaigns/${campaignId}/participants/${userId}`
        : `/campaigns/${campaignId}/participation`
      const response = await api.get(endpoint)
      return response.data
    } catch (error) {
      if (error.response?.status === 404) {
        return null
      }
      console.error('Failed to fetch user participation:', error)
      throw new Error('Failed to load participation details')
    }
  }

  // ===== CAMPAIGN SUBMISSIONS =====

  async submitRequirement(campaignId: string, requirementId: string, data: {
    content: any
    metadata?: Record<string, any>
  }): Promise<CampaignSubmission> {
    try {
      const response = await api.post(`/campaigns/${campaignId}/requirements/${requirementId}/submit`, data)
      return response.data
    } catch (error) {
      console.error('Failed to submit requirement:', error)
      throw new Error('Failed to submit requirement')
    }
  }

  async getSubmissions(campaignId: string, filters?: {
    requirementId?: string
    status?: SubmissionStatus
    userId?: string
    page?: number
    limit?: number
  }): Promise<{
    submissions: CampaignSubmission[]
    total: number
    page: number
    limit: number
  }> {
    try {
      const response = await api.get(`/campaigns/${campaignId}/submissions`, { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch submissions:', error)
      throw new Error('Failed to load submissions')
    }
  }

  async reviewSubmission(submissionId: string, data: {
    status: SubmissionStatus
    notes?: string
    points?: number
  }): Promise<CampaignSubmission> {
    try {
      const response = await api.patch(`/submissions/${submissionId}/review`, data)
      return response.data
    } catch (error) {
      console.error('Failed to review submission:', error)
      throw new Error('Failed to review submission')
    }
  }

  // ===== CAMPAIGN ANALYTICS =====

  async getCampaignAnalytics(campaignId: string, timeframe?: '24h' | '7d' | '30d' | '90d'): Promise<CampaignAnalytics> {
    try {
      const response = await api.get(`/campaigns/${campaignId}/analytics`, { 
        params: { timeframe } 
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign analytics:', error)
      throw new Error('Failed to load campaign analytics')
    }
  }

  async getCampaignMetrics(campaignId: string): Promise<{
    participationRate: number
    completionRate: number
    engagementScore: number
    socialReach: number
    roi: number
    costPerParticipant: number
  }> {
    try {
      const response = await api.get(`/campaigns/${campaignId}/metrics`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign metrics:', error)
      throw new Error('Failed to load campaign metrics')
    }
  }

  async exportCampaignData(campaignId: string, format: 'json' | 'csv' | 'pdf'): Promise<Blob> {
    try {
      const response = await api.get(`/campaigns/${campaignId}/export`, {
        params: { format },
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to export campaign data:', error)
      throw new Error('Failed to export campaign data')
    }
  }

  // ===== CAMPAIGN TEMPLATES =====

  async getCampaignTemplates(filters?: {
    type?: string
    category?: string
    difficulty?: string
    search?: string
  }): Promise<CampaignTemplate[]> {
    try {
      const response = await api.get('/campaigns/templates', { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign templates:', error)
      throw new Error('Failed to load campaign templates')
    }
  }

  async createCampaignFromTemplate(templateId: string, data: {
    projectId: string
    name: string
    description?: string
    startDate: string
    endDate: string
    customizations?: Record<string, any>
  }): Promise<Campaign> {
    try {
      const response = await api.post(`/campaigns/templates/${templateId}/create`, data)
      return response.data
    } catch (error) {
      console.error('Failed to create campaign from template:', error)
      throw new Error('Failed to create campaign from template')
    }
  }

  // ===== PROJECT CAMPAIGNS =====

  async getProjectCampaigns(projectId: string, filters?: Partial<CampaignFilters>): Promise<Campaign[]> {
    try {
      const response = await api.get(`/projects/${projectId}/campaigns`, { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch project campaigns:', error)
      throw new Error('Failed to load project campaigns')
    }
  }

  // ===== UTILITY METHODS =====

  async searchCampaigns(query: string, filters?: Partial<CampaignFilters>): Promise<Campaign[]> {
    try {
      const response = await api.get('/campaigns/search', { 
        params: { q: query, ...filters } 
      })
      return response.data
    } catch (error) {
      console.error('Failed to search campaigns:', error)
      throw new Error('Failed to search campaigns')
    }
  }

  async getFeaturedCampaigns(limit = 10): Promise<Campaign[]> {
    try {
      const response = await api.get('/campaigns/featured', { params: { limit } })
      return response.data
    } catch (error) {
      console.error('Failed to fetch featured campaigns:', error)
      throw new Error('Failed to load featured campaigns')
    }
  }

  async getActiveCampaigns(limit = 20): Promise<Campaign[]> {
    try {
      const response = await api.get('/campaigns/active', { params: { limit } })
      return response.data
    } catch (error) {
      console.error('Failed to fetch active campaigns:', error)
      throw new Error('Failed to load active campaigns')
    }
  }

  async validateCampaignData(data: CreateCampaignRequest): Promise<{
    valid: boolean
    errors: Array<{
      field: string
      message: string
    }>
  }> {
    try {
      const response = await api.post('/campaigns/validate', data)
      return response.data
    } catch (error) {
      console.error('Failed to validate campaign data:', error)
      throw new Error('Failed to validate campaign data')
    }
  }

  async previewCampaign(data: CreateCampaignRequest): Promise<{
    estimatedParticipants: number
    estimatedDuration: number
    estimatedCost: number
    recommendations: string[]
  }> {
    try {
      const response = await api.post('/campaigns/preview', data)
      return response.data
    } catch (error) {
      console.error('Failed to preview campaign:', error)
      throw new Error('Failed to generate campaign preview')
    }
  }
}

export const campaignService = new CampaignService()
