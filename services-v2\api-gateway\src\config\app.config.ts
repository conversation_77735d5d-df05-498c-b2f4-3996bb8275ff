export const appConfig = () => ({
  // Server configuration
  port: parseInt(process.env.PORT, 10) || 3000,
  apiPrefix: process.env.API_PREFIX || 'api',
  nodeEnv: process.env.NODE_ENV || 'development',

  // Service URLs
  services: {
    user: process.env.USER_SERVICE_URL || 'http://localhost:3001',
    project: process.env.PROJECT_SERVICE_URL || 'http://localhost:3002',
    nftGenerator: process.env.NFT_GENERATOR_SERVICE_URL || 'http://localhost:3003',
    blockchain: process.env.BLOCKCHAIN_SERVICE_URL || 'http://localhost:3004',
    marketplace: process.env.MARKETPLACE_SERVICE_URL || 'http://localhost:3005',
    analytics: process.env.ANALYTICS_SERVICE_URL || 'http://localhost:3006',
    profileAnalysis: process.env.PROFILE_ANALYSIS_SERVICE_URL || 'http://localhost:3007',
  },

  // Mock services
  mockServices: {
    enabled: process.env.USE_MOCK_SERVICES === 'true',
    twitter: process.env.MOCK_TWITTER_SERVICE_URL || 'http://localhost:3010',
  },

  // Security configuration
  security: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },

  // Rate limiting
  throttle: {
    ttl: parseInt(process.env.THROTTLE_TTL, 10) || 60,
    limit: parseInt(process.env.THROTTLE_LIMIT, 10) || 100,
  },

  // CORS configuration
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: process.env.CORS_CREDENTIALS === 'true',
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'debug',
    format: process.env.LOG_FORMAT || 'combined',
  },

  // Health check configuration
  health: {
    timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT, 10) || 5000,
    interval: parseInt(process.env.HEALTH_CHECK_INTERVAL, 10) || 30000,
  },

  // Swagger configuration
  swagger: {
    enabled: process.env.SWAGGER_ENABLED === 'true',
    title: process.env.SWAGGER_TITLE || 'Social NFT Platform API',
    description: process.env.SWAGGER_DESCRIPTION || 'API Gateway for Social NFT Platform',
    version: process.env.SWAGGER_VERSION || '1.0.0',
  },
});
