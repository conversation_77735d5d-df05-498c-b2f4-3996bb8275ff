import { Controller, Post, Put, Get, Body, Req, UseGuards, HttpCode, HttpStatus, Res, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthService } from '../services/auth.service';
import { UserQueryService } from '../services/user-query.service';
import {
  LoginDto,
  RegisterDto,
  RefreshTokenDto,
  LogoutDto,
  ChangePasswordDto,
  ForgotPasswordDto,
  ResetPasswordDto,
  SocialAuthDto,
  AuthResponseDto
} from '../dto/auth.dto';
import { ErrorResponseDto } from '../../common/dto/error-response.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userQueryService: UserQueryService,
  ) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user account' })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({ status: 201, description: 'User registered successfully', type: AuthResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input data', type: ErrorResponseDto })
  @ApiResponse({ status: 409, description: 'User already exists', type: ErrorResponseDto })
  @HttpCode(HttpStatus.CREATED)
  async register(@Body() registerDto: RegisterDto, @Req() request: any) {
    const result = await this.authService.register(registerDto, request.context);
    return result;
  }

  @Post('login')
  @ApiOperation({ summary: 'Login with email/username and password' })
  @ApiBody({ type: LoginDto })
  @ApiResponse({ status: 200, description: 'Login successful', type: AuthResponseDto })
  @ApiResponse({ status: 401, description: 'Invalid credentials', type: ErrorResponseDto })
  @ApiResponse({ status: 423, description: 'Account locked', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: LoginDto, @Req() request: any) {
    const result = await this.authService.login(loginDto, request.context);
    return result;
  }

  @Post('validate-token')
  @ApiOperation({ summary: 'Validate JWT access token' })
  @ApiResponse({ status: 200, description: 'Token validation result' })
  @ApiResponse({ status: 401, description: 'Invalid or expired token' })
  @HttpCode(HttpStatus.OK)
  async validateToken(@Body() body: { token: string }, @Req() request: any) {
    const result = await this.authService.validateToken(body.token, request.context);
    return result;
  }

  @Post('refresh')
  @ApiOperation({ summary: 'Refresh access token using refresh token' })
  @ApiBody({ type: RefreshTokenDto })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully', type: AuthResponseDto })
  @ApiResponse({ status: 401, description: 'Invalid refresh token', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto, @Req() request: any) {
    const result = await this.authService.refreshToken(refreshTokenDto, request.context);
    return result;
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout and invalidate session' })
  @ApiBody({ type: LogoutDto })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  @ApiResponse({ status: 401, description: 'Unauthorized', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async logout(@Body() logoutDto: LogoutDto, @Req() request: any) {
    const userId = request.user.sub;
    const sessionId = request.user.sessionId;
    const result = await this.authService.logout(logoutDto, userId, sessionId, request.context);
    return result;
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized', type: ErrorResponseDto })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async getProfile(@Req() request: any) {
    const userId = request.user.sub;
    const result = await this.userQueryService.getUserById(userId, request.context);
    return result;
  }

  @Put('change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change user password' })
  @ApiBody({ type: ChangePasswordDto })
  @ApiResponse({ status: 200, description: 'Password changed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data', type: ErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Current password incorrect', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async changePassword(@Body() changePasswordDto: ChangePasswordDto, @Req() request: any) {
    const userId = request.user.sub;
    const result = await this.authService.changePassword(changePasswordDto, userId, request.context);
    return result;
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiBody({ type: ForgotPasswordDto })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  @ApiResponse({ status: 404, description: 'Email not found', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto, @Req() request: any) {
    // TODO: Implement forgot password functionality
    return { 
      success: true, 
      message: 'If the email exists, a password reset link has been sent' 
    };
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password using reset token' })
  @ApiBody({ type: ResetPasswordDto })
  @ApiResponse({ status: 200, description: 'Password reset successful' })
  @ApiResponse({ status: 400, description: 'Invalid or expired token', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto, @Req() request: any) {
    // TODO: Implement reset password functionality
    return { 
      success: true, 
      message: 'Password reset successful' 
    };
  }

  @Get('twitter')
  @ApiOperation({ summary: 'Initiate Twitter OAuth authentication' })
  @ApiResponse({ status: 302, description: 'Redirect to Twitter OAuth' })
  async twitterAuth(@Res() response: any) {
    // For now, return a simple mock response
    // In production, this would redirect to Twitter OAuth
    const mockTwitterAuthUrl = `https://twitter.com/i/oauth2/authorize?response_type=code&client_id=mock_client_id&redirect_uri=${encodeURIComponent('http://localhost:3000/auth/twitter/callback')}&scope=tweet.read%20users.read&state=mock_state`;

    return response.redirect(mockTwitterAuthUrl);
  }

  @Post('twitter/callback')
  @ApiOperation({ summary: 'Handle Twitter OAuth callback' })
  @ApiResponse({ status: 200, description: 'Twitter authentication successful', type: AuthResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid authorization code', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async twitterCallback(@Body() body: { code: string; state: string }, @Req() request: any) {
    // Mock Twitter authentication for now
    // In production, this would exchange the code for an access token
    // and create/login the user

    const mockUser = {
      id: 'twitter_user_' + Date.now(),
      username: 'twitter_user',
      email: '<EMAIL>',
      displayName: 'Twitter User',
      provider: 'twitter',
      providerId: 'mock_twitter_id',
      isVerified: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const mockToken = 'mock_jwt_token_' + Date.now();

    return {
      success: true,
      data: {
        accessToken: mockToken,
        refreshToken: 'mock_refresh_token',
        user: mockUser,
        expiresIn: 3600
      }
    };
  }

  @Post('social')
  @ApiOperation({ summary: 'Authenticate with social provider' })
  @ApiBody({ type: SocialAuthDto })
  @ApiResponse({ status: 200, description: 'Social authentication successful', type: AuthResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid authorization code', type: ErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Social authentication failed', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async socialAuth(@Body() socialAuthDto: SocialAuthDto, @Req() request: any) {
    // TODO: Implement social authentication
    return {
      success: true,
      message: 'Social authentication not yet implemented'
    };
  }

  @Post('verify-email')
  @ApiOperation({ summary: 'Verify email address' })
  @ApiResponse({ status: 200, description: 'Email verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid verification token', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async verifyEmail(@Body() body: { token: string }, @Req() request: any) {
    // TODO: Implement email verification
    return { 
      success: true, 
      message: 'Email verification not yet implemented' 
    };
  }

  @Post('resend-verification')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Resend email verification' })
  @ApiResponse({ status: 200, description: 'Verification email sent' })
  @ApiResponse({ status: 401, description: 'Unauthorized', type: ErrorResponseDto })
  @HttpCode(HttpStatus.OK)
  async resendVerification(@Req() request: any) {
    // TODO: Implement resend verification
    return { 
      success: true, 
      message: 'Verification email resend not yet implemented' 
    };
  }
}
