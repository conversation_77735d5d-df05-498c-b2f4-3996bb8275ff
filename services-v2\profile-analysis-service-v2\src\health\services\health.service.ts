/**
 * Health Service - Profile Analysis Service V2
 * 
 * Health check business logic
 */

import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class HealthService {
  constructor(private readonly prisma: PrismaService) {}

  async getSimpleHealth() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      service: 'profile-analysis-service-v2',
      version: '2.0.0',
      environment: process.env.NODE_ENV || 'development',
    };
  }

  async getDetailedHealth() {
    const startTime = Date.now();

    try {
      // Test database connection
      const dbHealth = await this.prisma.healthCheck();
      
      // Get database statistics
      const dbStats = await this.prisma.getDatabaseStats();
      
      // Get memory usage
      const memoryUsage = process.memoryUsage();
      
      const responseTime = Date.now() - startTime;

      const health = {
        status: dbHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        service: 'profile-analysis-service-v2',
        version: '2.0.0',
        environment: process.env.NODE_ENV || 'development',
        responseTime,
        dependencies: [
          {
            name: 'database',
            status: dbHealth.status,
            details: dbHealth,
          },
        ],
        metrics: {
          memoryUsage: {
            used: memoryUsage.heapUsed,
            total: memoryUsage.heapTotal,
            percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
          },
          database: dbStats,
        },
      };

      return health;
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        service: 'profile-analysis-service-v2',
        version: '2.0.0',
        environment: process.env.NODE_ENV || 'development',
        error: error.message,
      };
    }
  }

  async getDatabaseHealth() {
    try {
      const dbHealth = await this.prisma.healthCheck();
      const dbStats = await this.prisma.getDatabaseStats();
      
      return {
        status: dbHealth.status,
        details: {
          ...dbHealth,
          statistics: dbStats,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  async getReadiness() {
    try {
      // Check if service is ready to accept requests
      const dbHealth = await this.prisma.healthCheck();
      
      const isReady = dbHealth.status === 'healthy';
      
      return {
        status: isReady ? 'ready' : 'not-ready',
        timestamp: new Date().toISOString(),
        checks: {
          database: dbHealth.status === 'healthy',
        },
      };
    } catch (error) {
      return {
        status: 'not-ready',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  async getLiveness() {
    // Simple liveness check - service is alive if it can respond
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }
}
