// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AnalyticsEvent {
  id          String   @id @default(cuid())
  eventType   String
  userId      String?
  sessionId   String?
  properties  Json?
  metadata    Json?
  timestamp   DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("analytics_events")
}

model PerformanceMetric {
  id          String   @id @default(cuid())
  metricType  String
  value       Float
  unit        String
  source      String
  metadata    Json?
  timestamp   DateTime @default(now())
  createdAt   DateTime @default(now())

  @@map("performance_metrics")
}

model UserSession {
  id          String   @id @default(cuid())
  userId      String
  sessionId   String   @unique
  startTime   DateTime @default(now())
  endTime     DateTime?
  duration    Int?     // in seconds
  pageViews   Int      @default(0)
  events      Int      @default(0)
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("user_sessions")
}
