import {
  <PERSON>,
  Post,
  Get,
  Param,
  Body,
  Query,
  Res,
  HttpStatus,
  Logger,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { Response } from 'express';
import { NftGenerationService } from '../services/nft-generation.service';

@ApiTags('NFTs')
@Controller('nfts')
export class NftGenerationController {
  private readonly logger = new Logger(NftGenerationController.name);

  constructor(private readonly nftGenerationService: NftGenerationService) {}

  @Post('generate')
  @ApiOperation({
    summary: 'Generate NFT collection',
    description: 'Generate a complete NFT collection with AI-powered image processing'
  })
  @ApiBody({
    description: 'NFT generation parameters',
    schema: {
      type: 'object',
      properties: {
        projectId: { type: 'string', example: 'project-123' },
        collectionName: { type: 'string', example: 'My NFT Collection' },
        collectionSize: { type: 'number', example: 1000 },
        baseImageUrl: { type: 'string', example: 'https://example.com/base.png' },
        traits: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              values: { type: 'array', items: { type: 'string' } },
              rarity: { type: 'array', items: { type: 'number' } }
            }
          }
        }
      },
      required: ['projectId', 'collectionName', 'collectionSize']
    }
  })
  @ApiResponse({ status: 201, description: 'NFT generation started successfully' })
  @ApiResponse({ status: 400, description: 'Invalid generation parameters' })
  async generateNftCollection(@Body() generationData: any, @Res() res: Response) {
    try {
      this.logger.log('Starting NFT collection generation');
      
      const result = await this.nftGenerationService.generateCollection(generationData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`NFT generation failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Post('generate/single')
  @ApiOperation({
    summary: 'Generate single NFT',
    description: 'Generate a single NFT with custom parameters'
  })
  @ApiBody({
    description: 'Single NFT generation parameters',
    schema: {
      type: 'object',
      properties: {
        projectId: { type: 'string', example: 'project-123' },
        name: { type: 'string', example: 'My NFT #1' },
        description: { type: 'string', example: 'A unique NFT' },
        baseImageUrl: { type: 'string', example: 'https://example.com/base.png' },
        traits: {
          type: 'object',
          properties: {
            background: { type: 'string' },
            character: { type: 'string' },
            accessory: { type: 'string' }
          }
        }
      },
      required: ['projectId', 'name']
    }
  })
  @ApiResponse({ status: 201, description: 'Single NFT generated successfully' })
  async generateSingleNft(@Body() nftData: any, @Res() res: Response) {
    try {
      this.logger.log('Generating single NFT');
      
      const result = await this.nftGenerationService.generateSingle(nftData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Single NFT generation failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Post('upload-base-image')
  @UseInterceptors(FileInterceptor('image'))
  @ApiOperation({
    summary: 'Upload base image for NFT generation',
    description: 'Upload and process base image for NFT collection generation'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Base image file',
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
        },
        projectId: {
          type: 'string',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'Base image uploaded and processed successfully' })
  async uploadBaseImage(
    @UploadedFile() file: any,
    @Body('projectId') projectId: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log('Processing uploaded base image');
      
      const result = await this.nftGenerationService.processBaseImage(file, projectId);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Base image upload failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('generation-status/:jobId')
  @ApiOperation({
    summary: 'Get NFT generation status',
    description: 'Check the status of an ongoing NFT generation job'
  })
  @ApiParam({ name: 'jobId', description: 'Generation job ID' })
  @ApiResponse({ status: 200, description: 'Generation status retrieved successfully' })
  async getGenerationStatus(@Param('jobId') jobId: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting generation status for job: ${jobId}`);
      
      const result = await this.nftGenerationService.getGenerationStatus(jobId);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Status retrieval failed for job ${jobId}: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
