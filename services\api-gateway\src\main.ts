import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON><PERSON>Pipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import helmet from 'helmet';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);

    // Get configuration
    const port = configService.get<number>('PORT', 3000);
    const apiPrefix = configService.get<string>('API_PREFIX', 'api');
    const nodeEnv = configService.get<string>('NODE_ENV', 'development');
    const corsOrigin = configService.get<string>('CORS_ORIGIN', 'http://localhost:3000');
    const swaggerEnabled = configService.get<boolean>('SWAGGER_ENABLED', true);

    // Security middleware
    app.use(helmet());

    // CORS configuration
    app.enableCors({
      origin: corsOrigin.split(','),
      credentials: configService.get<boolean>('CORS_CREDENTIALS', true),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // API prefix
    app.setGlobalPrefix(apiPrefix);

    // Swagger documentation (only in development)
    if (swaggerEnabled && nodeEnv === 'development') {
      const config = new DocumentBuilder()
        .setTitle(configService.get<string>('SWAGGER_TITLE', 'Social NFT Platform API'))
        .setDescription(configService.get<string>('SWAGGER_DESCRIPTION', 'API Gateway for Social NFT Platform'))
        .setVersion(configService.get<string>('SWAGGER_VERSION', '1.0.0'))
        .addBearerAuth()
        .addTag('Gateway', 'API Gateway endpoints')
        .addTag('Health', 'Health check endpoints')
        .addTag('Proxy', 'Service proxy endpoints')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup(`${apiPrefix}/docs`, app, document, {
        swaggerOptions: {
          persistAuthorization: true,
        },
      });

      logger.log(`Swagger documentation available at http://localhost:${port}/${apiPrefix}/docs`);
    }

    // Start the application
    await app.listen(port);
    
    logger.log(`🚀 API Gateway started successfully`);
    logger.log(`🌐 Server running on http://localhost:${port}`);
    logger.log(`📚 API available at http://localhost:${port}/${apiPrefix}`);
    logger.log(`🏥 Health check at http://localhost:${port}/${apiPrefix}/health`);
    logger.log(`🔧 Environment: ${nodeEnv}`);
    
  } catch (error) {
    logger.error('❌ Failed to start API Gateway', error.stack);
    process.exit(1);
  }
}

bootstrap();
