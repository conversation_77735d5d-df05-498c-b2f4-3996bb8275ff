// Enterprise Analytics Command Controller (Write Side) - Enhanced
import { Controller, Post, Body, Headers, Res, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { CreateAnalyticsEventCommandDto } from '../models/analytics-command.model';

// Enhanced analytics services
import { EventTrackingService } from '../../analytics/services/event-tracking.service';
import { UserBehaviorService } from '../../analytics/services/user-behavior.service';
import { PerformanceAnalyticsService } from '../../analytics/services/performance-analytics.service';

@ApiTags('Analytics Commands (Write Operations)')
@Controller('enterprise/analytics')
export class AnalyticsCommandController {
  private readonly logger = new Logger(AnalyticsCommandController.name);

  constructor(
    private readonly eventTracking: EventTrackingService,
    private readonly userBehavior: UserBehaviorService,
    private readonly performanceAnalytics: PerformanceAnalyticsService
  ) {}

  @Post('events')
  @ApiOperation({ summary: 'Track analytics event (Enterprise CQRS Command)' })
  @ApiResponse({ status: 201, description: 'Analytics event tracked successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async trackEvent(
    @Body() eventData: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Analytics event tracking request received`, { correlationId });

      // Validate required fields
      if (!eventData.eventType) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: 'eventType is required',
          correlationId
        });
      }

      // Track the event
      const eventId = await this.eventTracking.trackEvent({
        eventType: eventData.eventType,
        userId: eventData.userId,
        sessionId: eventData.sessionId,
        properties: eventData.properties || {},
        metadata: {
          userAgent: headers['user-agent'],
          ipAddress: headers['x-forwarded-for'] || 'unknown',
          source: eventData.source || 'api',
          ...eventData.metadata
        }
      });

      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: { eventId },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Event tracking failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('events/batch')
  @ApiOperation({ summary: 'Track multiple analytics events (Enterprise CQRS Command)' })
  @ApiResponse({ status: 201, description: 'Batch events tracked successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async trackBatchEvents(
    @Body() batchData: { events: any[] },
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📊 Batch analytics tracking request received`, { correlationId, count: batchData.events?.length });

      if (!batchData.events || !Array.isArray(batchData.events)) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: 'events array is required',
          correlationId
        });
      }

      // Prepare events with metadata
      const eventsToTrack = batchData.events.map(event => ({
        eventType: event.eventType,
        userId: event.userId,
        sessionId: event.sessionId,
        properties: event.properties || {},
        metadata: {
          userAgent: headers['user-agent'],
          ipAddress: headers['x-forwarded-for'] || 'unknown',
          source: event.source || 'api',
          ...event.metadata
        }
      }));

      const eventIds = await this.eventTracking.trackEvents(eventsToTrack);

      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: {
          eventIds,
          count: eventIds.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Batch event tracking failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('user-actions')
  @ApiOperation({ summary: 'Track user action (Enterprise CQRS Command)' })
  @ApiResponse({ status: 201, description: 'User action tracked successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async trackUserAction(
    @Body() actionData: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👤 User action tracking request received`, { correlationId });

      if (!actionData.userId || !actionData.action) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: 'userId and action are required',
          correlationId
        });
      }

      const eventId = await this.eventTracking.trackUserAction(
        actionData.userId,
        actionData.action,
        actionData.properties || {},
        {
          userAgent: headers['user-agent'],
          ipAddress: headers['x-forwarded-for'] || 'unknown',
          ...actionData.metadata
        }
      );

      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: { eventId },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ User action tracking failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('sessions')
  @ApiOperation({ summary: 'Track user session (Enterprise CQRS Command)' })
  @ApiResponse({ status: 201, description: 'Session tracked successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async trackSession(
    @Body() sessionData: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🔄 Session tracking request received`, { correlationId });

      if (!sessionData.userId || !sessionData.sessionId || !sessionData.action) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: 'userId, sessionId, and action are required',
          correlationId
        });
      }

      await this.userBehavior.trackUserSession(
        sessionData.userId,
        sessionData.sessionId,
        sessionData.action
      );

      return res.status(HttpStatus.CREATED).json({
        success: true,
        message: `Session ${sessionData.action} tracked successfully`,
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Session tracking failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('performance/metrics')
  @ApiOperation({ summary: 'Record performance metric (Enterprise CQRS Command)' })
  @ApiResponse({ status: 201, description: 'Performance metric recorded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async recordPerformanceMetric(
    @Body() metricData: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`⚡ Performance metric recording request received`, { correlationId });

      if (!metricData.metricType || metricData.value === undefined || !metricData.unit || !metricData.source) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: 'metricType, value, unit, and source are required',
          correlationId
        });
      }

      const metricId = await this.performanceAnalytics.recordMetric(
        metricData.metricType,
        metricData.value,
        metricData.unit,
        metricData.source,
        metricData.metadata || {}
      );

      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: { metricId },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Performance metric recording failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }
}
