/**
 * Standardized RBAC (Role-Based Access Control) Guard
 * Provides consistent permission checking across all services
 */

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  UnauthorizedException,
  Logger,
  Inject,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import {
  AuthenticatedUser,
  IPermissionService,
  PermissionContext,
  PermissionCheckResult,
} from '../interfaces/auth.interface';
import { Permission, Role } from '../interfaces/permission.interface';

/**
 * RBAC Guard for permission-based access control
 */
@Injectable()
export class StandardizedRBACGuard implements CanActivate {
  private readonly logger = new Logger(StandardizedRBACGuard.name);

  constructor(
    private readonly reflector: Reflector,
    @Inject('PERMISSION_SERVICE') private readonly permissionService: IPermissionService,
  ) {}

  /**
   * Determine if the request can proceed based on permissions
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest<Request>();

      // Check if route is public (skip permission check)
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        this.logger.debug('Route is public, skipping permission check');
        return true;
      }

      // Get user from request (should be set by JWT guard)
      const user: AuthenticatedUser = request.user;
      if (!user) {
        this.logger.warn('No user found in request context');
        throw new UnauthorizedException('Authentication required for permission check');
      }

      // Get required permissions from decorators
      const requiredPermissions = this.getRequiredPermissions(context);
      const requiredRoles = this.getRequiredRoles(context);
      const requireOwnership = this.getOwnershipRequirement(context);

      // If no specific permissions or roles required, allow authenticated users
      if (requiredPermissions.length === 0 && requiredRoles.length === 0 && !requireOwnership) {
        this.logger.debug('No specific permissions required, allowing authenticated user');
        return true;
      }

      // Build permission context
      const permissionContext = this.buildPermissionContext(request, user);

      // Check role-based permissions
      if (requiredRoles.length > 0) {
        const hasRole = await this.checkRoles(user, requiredRoles);
        if (!hasRole) {
          this.logger.warn('User lacks required roles', {
            userId: user.id,
            userRoles: user.roles,
            requiredRoles,
          });
          await this.logPermissionDenied(permissionContext, 'insufficient_roles');
          throw new ForbiddenException('Insufficient role permissions');
        }
      }

      // Check specific permissions
      if (requiredPermissions.length > 0) {
        const permissionCheck = await this.checkPermissions(user, requiredPermissions, permissionContext);
        if (!permissionCheck.granted) {
          this.logger.warn('User lacks required permissions', {
            userId: user.id,
            userPermissions: user.permissions,
            requiredPermissions,
            reason: permissionCheck.reason,
          });
          await this.logPermissionDenied(permissionContext, permissionCheck.reason || 'insufficient_permissions');
          throw new ForbiddenException(permissionCheck.reason || 'Insufficient permissions');
        }
      }

      // Check resource ownership if required
      if (requireOwnership) {
        const ownershipCheck = await this.checkOwnership(user, request, permissionContext);
        if (!ownershipCheck) {
          this.logger.warn('User lacks resource ownership', {
            userId: user.id,
            resource: permissionContext.resource,
          });
          await this.logPermissionDenied(permissionContext, 'not_resource_owner');
          throw new ForbiddenException('Resource access denied - ownership required');
        }
      }

      // Log successful permission check
      await this.logPermissionGranted(permissionContext);

      this.logger.debug('Permission check passed', {
        userId: user.id,
        requiredPermissions,
        requiredRoles,
      });

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException || error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error('RBAC guard error', error);
      throw new ForbiddenException('Permission check failed');
    }
  }

  /**
   * Get required permissions from decorators
   */
  private getRequiredPermissions(context: ExecutionContext): Permission[] {
    const permissions = this.reflector.getAllAndOverride<Permission[]>('permissions', [
      context.getHandler(),
      context.getClass(),
    ]);

    const anyPermissions = this.reflector.getAllAndOverride<Permission[]>('anyPermissions', [
      context.getHandler(),
      context.getClass(),
    ]);

    return [...(permissions || []), ...(anyPermissions || [])];
  }

  /**
   * Get required roles from decorators
   */
  private getRequiredRoles(context: ExecutionContext): Role[] {
    return this.reflector.getAllAndOverride<Role[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]) || [];
  }

  /**
   * Get ownership requirement from decorators
   */
  private getOwnershipRequirement(context: ExecutionContext): boolean {
    return this.reflector.getAllAndOverride<boolean>('requireOwnership', [
      context.getHandler(),
      context.getClass(),
    ]) || false;
  }

  /**
   * Check if user has required roles
   */
  private async checkRoles(user: AuthenticatedUser, requiredRoles: Role[]): Promise<boolean> {
    // Check if user has any of the required roles
    return requiredRoles.some(role => user.roles.includes(role));
  }

  /**
   * Check if user has required permissions
   */
  private async checkPermissions(
    user: AuthenticatedUser,
    requiredPermissions: Permission[],
    context: PermissionContext
  ): Promise<PermissionCheckResult> {
    try {
      // Check if this is an "any permission" check or "all permissions" check
      const isAnyPermissionCheck = this.reflector.getAllAndOverride<boolean>('anyPermissions', [
        context.request?.method,
      ]);

      if (isAnyPermissionCheck) {
        // User needs ANY of the permissions
        const hasAnyPermission = await this.permissionService.hasAnyPermission(user, requiredPermissions);
        return {
          allowed: hasAnyPermission,
          granted: hasAnyPermission,
          reason: hasAnyPermission ? undefined : 'User lacks any of the required permissions',
        };
      } else {
        // User needs ALL of the permissions
        const hasAllPermissions = await this.permissionService.hasAllPermissions(user, requiredPermissions);
        return {
          allowed: hasAllPermissions,
          granted: hasAllPermissions,
          reason: hasAllPermissions ? undefined : 'User lacks some required permissions',
        };
      }
    } catch (error) {
      this.logger.error('Permission check error', error);
      return {
        allowed: false,
        granted: false,
        reason: 'Permission check failed',
      };
    }
  }

  /**
   * Check resource ownership
   */
  private async checkOwnership(
    user: AuthenticatedUser,
    request: Request,
    context: PermissionContext
  ): Promise<boolean> {
    try {
      // Extract resource information from request
      const resourceId = request.params.id || request.params.resourceId;
      const resourceType = this.extractResourceType(request.path);

      if (!resourceId || !resourceType) {
        this.logger.warn('Cannot determine resource for ownership check', {
          path: request.path,
          params: request.params,
        });
        return false;
      }

      // Check if user owns the resource
      // This would typically involve checking the database
      // For now, we'll implement a basic check
      return await this.checkResourceOwnership(user.id, resourceType, resourceId);
    } catch (error) {
      this.logger.error('Ownership check error', error);
      return false;
    }
  }

  /**
   * Extract resource type from request path
   */
  private extractResourceType(path: string): string | null {
    // Extract resource type from path like /api/users/123 -> users
    const pathParts = path.split('/').filter(part => part.length > 0);
    
    // Skip 'api' prefix if present
    const resourceIndex = pathParts[0] === 'api' ? 1 : 0;
    return pathParts[resourceIndex] || null;
  }

  /**
   * Check if user owns a specific resource
   */
  private async checkResourceOwnership(
    userId: string,
    resourceType: string,
    resourceId: string
  ): Promise<boolean> {
    // This is a placeholder implementation
    // In a real application, you would check the database
    // to see if the user owns the resource
    
    // For now, we'll implement basic logic:
    // - Users can access their own profile
    // - Users can access their own NFTs, projects, etc.
    
    if (resourceType === 'users' && resourceId === userId) {
      return true;
    }

    // For other resources, you would typically query the database
    // to check the owner_id field
    
    return false; // Default to deny access
  }

  /**
   * Build permission context for logging and advanced checks
   */
  private buildPermissionContext(request: Request, user: AuthenticatedUser): PermissionContext {
    return {
      user: {
        id: user.id,
        roles: user.roles,
        permissions: user.permissions,
      },
      resource: `${request.method}:${request.path}`,
      request: {
        method: request.method,
        path: request.path,
        ipAddress: this.getClientIP(request),
        userAgent: request.headers['user-agent'] || '',
      },
      environment: {
        service: process.env.SERVICE_NAME || 'unknown',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date(),
      },
    };
  }

  /**
   * Get client IP address
   */
  private getClientIP(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      request.connection.remoteAddress ||
      'unknown'
    );
  }

  /**
   * Log permission granted event
   */
  private async logPermissionGranted(context: PermissionContext): Promise<void> {
    try {
      this.logger.log('Permission granted', {
        userId: context.userId,
        resource: context.resource,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to log permission granted', error);
    }
  }

  /**
   * Log permission denied event
   */
  private async logPermissionDenied(context: PermissionContext, reason: string): Promise<void> {
    try {
      this.logger.warn('Permission denied', {
        userId: context.userId,
        resource: context.resource,
        reason,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to log permission denied', error);
    }
  }
}
