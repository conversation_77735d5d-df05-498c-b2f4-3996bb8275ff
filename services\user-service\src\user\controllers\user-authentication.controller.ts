import {
  <PERSON>,
  Post,
  Get,
  Body,
  Headers,
  <PERSON><PERSON>,
  HttpStatus,
  Logger,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response, Request } from 'express';
import {
  AuthenticationService,
  LoginRequest,
  RefreshTokenRequest,
  PasswordResetRequest,
  PasswordResetConfirmRequest,
  EmailVerificationRequest,
} from '../services/authentication.service';

@ApiTags('Authentication')
@Controller('auth')
export class UserAuthenticationController {
  private readonly logger = new Logger(UserAuthenticationController.name);

  constructor(private readonly authentication: AuthenticationService) {}

  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  @ApiResponse({ status: 423, description: 'Account locked' })
  async login(
    @Body() loginRequest: LoginRequest,
    @Headers() headers: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      const ipAddress = req.ip || headers['x-forwarded-for'] || 'unknown';
      const userAgent = headers['user-agent'] || 'unknown';

      this.logger.log(`🔐 Login attempt received`, { 
        correlationId, 
        email: loginRequest.email,
        ipAddress 
      });

      const loginResponse = await this.authentication.login(
        loginRequest,
        ipAddress,
        userAgent
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        data: loginResponse,
        message: 'Login successful',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Login failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('locked') ? HttpStatus.FORBIDDEN :
                        error.message.includes('Invalid credentials') ? HttpStatus.UNAUTHORIZED :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('refresh')
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refreshToken(
    @Body() refreshRequest: RefreshTokenRequest,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🔄 Token refresh request received`, { correlationId });

      const tokenResponse = await this.authentication.refreshToken(refreshRequest);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: tokenResponse,
        message: 'Token refreshed successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Token refresh failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.UNAUTHORIZED).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('logout')
  @ApiOperation({ summary: 'User logout' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  async logout(
    @Body() logoutData: { userId: string; sessionId: string },
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🚪 Logout request received`, { 
        correlationId, 
        userId: logoutData.userId,
        sessionId: logoutData.sessionId 
      });

      await this.authentication.logout(logoutData.userId, logoutData.sessionId);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Logout successful',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Logout failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('logout-all')
  @ApiOperation({ summary: 'Logout from all devices' })
  @ApiResponse({ status: 200, description: 'Logout from all devices successful' })
  async logoutAll(
    @Body() logoutData: { userId: string },
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🚪 Logout all devices request received`, { 
        correlationId, 
        userId: logoutData.userId 
      });

      await this.authentication.logoutAll(logoutData.userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Logged out from all devices successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Logout all failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('verify-token')
  @ApiOperation({ summary: 'Verify JWT token' })
  @ApiResponse({ status: 200, description: 'Token is valid' })
  @ApiResponse({ status: 401, description: 'Invalid token' })
  async verifyToken(
    @Body() tokenData: { token: string },
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🔍 Token verification request received`, { correlationId });

      const payload = await this.authentication.verifyToken(tokenData.token);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: payload,
        message: 'Token is valid',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Token verification failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.UNAUTHORIZED).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('password-reset/request')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  async requestPasswordReset(
    @Body() resetRequest: PasswordResetRequest,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🔒 Password reset request received`, { 
        correlationId, 
        email: resetRequest.email 
      });

      await this.authentication.requestPasswordReset(resetRequest);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'If the email exists, a password reset link has been sent',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Password reset request failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('password-reset/confirm')
  @ApiOperation({ summary: 'Confirm password reset' })
  @ApiResponse({ status: 200, description: 'Password reset successful' })
  @ApiResponse({ status: 400, description: 'Invalid or expired token' })
  async confirmPasswordReset(
    @Body() confirmRequest: PasswordResetConfirmRequest,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🔒 Password reset confirmation received`, { correlationId });

      await this.authentication.confirmPasswordReset(confirmRequest);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Password reset successful',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Password reset confirmation failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('Invalid or expired') ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('email-verification/send')
  @ApiOperation({ summary: 'Send email verification' })
  @ApiResponse({ status: 200, description: 'Verification email sent' })
  async sendEmailVerification(
    @Body() verificationData: { userId: string },
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📧 Email verification send request received`, { 
        correlationId, 
        userId: verificationData.userId 
      });

      await this.authentication.sendEmailVerification(verificationData.userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Verification email sent',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Send email verification failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        error.message.includes('already verified') ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('email-verification/verify')
  @ApiOperation({ summary: 'Verify email address' })
  @ApiResponse({ status: 200, description: 'Email verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid or expired token' })
  async verifyEmail(
    @Body() verificationRequest: EmailVerificationRequest,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📧 Email verification request received`, { correlationId });

      await this.authentication.verifyEmail(verificationRequest);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Email verified successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Email verification failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('Invalid or expired') ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('sessions/:userId')
  @ApiOperation({ summary: 'Get user sessions' })
  @ApiResponse({ status: 200, description: 'Sessions retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserSessions(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      const userId = headers['x-user-id']; // From JWT middleware
      
      this.logger.log(`📱 Get user sessions request received`, { correlationId, userId });

      const sessions = await this.authentication.getUserSessions(userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          sessions,
          count: sessions.length
        },
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Get user sessions failed: ${error.message}`, error.stack);
      
      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for authentication service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          service: 'user-authentication-service',
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        },
        correlationId
      });
    } catch (error) {
      return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: 'Service unavailable',
        correlationId: headers['x-correlation-id']
      });
    }
  }
}
