import { Controller, Get, Post, Body, Res, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { WalletManagerService } from '../services/wallet-manager.service';

@ApiTags('Wallets')
@Controller('wallets')
export class WalletController {
  private readonly logger = new Logger(WalletController.name);

  constructor(private readonly walletManagerService: WalletManagerService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create new wallet', description: 'Create a new blockchain wallet' })
  @ApiBody({
    description: 'Wallet creation data',
    schema: {
      type: 'object',
      properties: {
        type: { type: 'string', example: 'ethereum' },
        name: { type: 'string', example: 'My Wallet' }
      },
      required: ['type']
    }
  })
  @ApiResponse({ status: 201, description: 'Wallet created successfully' })
  async createWallet(@Body() walletData: any, @Res() res: Response) {
    try {
      this.logger.log('Creating new wallet');
      const result = await this.walletManagerService.createWallet(walletData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Wallet creation failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }
}
