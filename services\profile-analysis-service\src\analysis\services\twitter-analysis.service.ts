import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { TwitterApiClientService } from './twitter-api-client.service';

export interface TwitterProfileData {
  id: string;
  username: string;
  displayName: string;
  description?: string;
  followersCount: number;
  followingCount: number;
  tweetsCount: number;
  listedCount: number;
  verified: boolean;
  profileImageUrl?: string;
  location?: string;
  website?: string;
  createdAt: string;
}

export interface TwitterAnalysisResult {
  id: string;
  userId: string;
  twitterHandle: string;
  status: 'pending' | 'completed' | 'failed';
  score: number;
  analysisData: {
    profile: {
      followerCount: number;
      followingCount: number;
      tweetCount: number;
      engagementRate: number;
      accountAge: number;
      isVerified: boolean;
      hasProfileImage: boolean;
      hasBio: boolean;
    };
    metrics: {
      contentQuality: number;
      activityLevel: number;
      influenceScore: number;
      authenticity: number;
      engagement: number;
    };
    breakdown: {
      followerScore: number;
      engagementScore: number;
      contentScore: number;
      activityScore: number;
      profileScore: number;
    };
    nftRecommendation: {
      score: number;
      rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
      reasoning: string[];
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface AnalyzeTwitterProfileDto {
  twitterHandle: string;
  userId?: string;
}

@Injectable()
export class TwitterAnalysisService {
  private readonly logger = new Logger(TwitterAnalysisService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly twitterClient: TwitterApiClientService
  ) {}

  /**
   * Analyze Twitter profile and generate comprehensive analysis
   */
  async analyzeTwitterProfile(analyzeDto: AnalyzeTwitterProfileDto): Promise<TwitterAnalysisResult> {
    const startTime = Date.now();
    const correlationId = `analysis-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      this.logger.log(`Starting Twitter analysis for: ${analyzeDto.twitterHandle}`, { correlationId });

      // Clean Twitter handle (remove @ if present)
      const cleanHandle = analyzeDto.twitterHandle.replace('@', '');

      // Fetch Twitter profile data
      const profileData = await this.fetchTwitterProfile(cleanHandle, correlationId);

      // Perform comprehensive analysis
      const analysisResults = await this.performAnalysis(profileData, correlationId);

      // Create analysis record (simplified for now)
      const analysisId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const responseTime = Date.now() - startTime;
      this.logger.log(`Analysis completed successfully in ${responseTime}ms`, { 
        correlationId, 
        score: analysisResults.score,
        rarity: analysisResults.analysisData.nftRecommendation.rarity
      });

      return {
        id: analysisId,
        userId: analyzeDto.userId || 'anonymous',
        twitterHandle: cleanHandle,
        status: 'completed',
        score: analysisResults.score,
        analysisData: analysisResults.analysisData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`Analysis failed after ${responseTime}ms: ${error.message}`, {
        correlationId,
        error: error.stack,
        twitterHandle: analyzeDto.twitterHandle
      });

      throw new BadRequestException(`Twitter analysis failed: ${error.message}`);
    }
  }

  /**
   * Get analysis by ID
   */
  async getAnalysisById(id: string): Promise<TwitterAnalysisResult | null> {
    try {
      // For now, return mock data - will be replaced with actual database queries
      this.logger.log(`Getting analysis by ID: ${id}`);
      
      // Mock implementation
      return {
        id,
        userId: 'mock_user',
        twitterHandle: 'mock_handle',
        status: 'completed',
        score: 75.5,
        analysisData: {
          profile: {
            followerCount: 1000,
            followingCount: 500,
            tweetCount: 2000,
            engagementRate: 3.5,
            accountAge: 365,
            isVerified: false,
            hasProfileImage: true,
            hasBio: true,
          },
          metrics: {
            contentQuality: 80,
            activityLevel: 70,
            influenceScore: 75,
            authenticity: 85,
            engagement: 65,
          },
          breakdown: {
            followerScore: 60,
            engagementScore: 65,
            contentScore: 80,
            activityScore: 70,
            profileScore: 90,
          },
          nftRecommendation: {
            score: 75.5,
            rarity: 'rare',
            reasoning: ['Good engagement rate', 'Active user', 'Complete profile'],
          },
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get analysis by ID: ${error.message}`, { id, error: error.stack });
      return null;
    }
  }

  /**
   * Get user's analysis history
   */
  async getUserAnalysisHistory(userId: string, limit: number = 10, offset: number = 0): Promise<{
    analyses: TwitterAnalysisResult[];
    total: number;
  }> {
    try {
      this.logger.log(`Getting analysis history for user: ${userId}`);
      
      // Mock implementation - will be replaced with actual database queries
      const mockAnalysis = await this.getAnalysisById('mock_analysis_id');
      
      return {
        analyses: mockAnalysis ? [mockAnalysis] : [],
        total: mockAnalysis ? 1 : 0,
      };
    } catch (error) {
      this.logger.error(`Failed to get user analysis history: ${error.message}`, { userId, error: error.stack });
      throw error;
    }
  }

  /**
   * Fetch Twitter profile data from external service
   */
  private async fetchTwitterProfile(username: string, correlationId: string): Promise<TwitterProfileData> {
    try {
      this.logger.debug(`Fetching Twitter profile: ${username}`, { correlationId });

      // Use Twitter API client service
      const profileData = await this.twitterClient.getProfile(username);

      return {
        id: profileData.id || `mock_${username}`,
        username: profileData.username || username,
        displayName: profileData.displayName || username,
        description: profileData.description || profileData.bio,
        followersCount: profileData.followersCount || 0,
        followingCount: profileData.followingCount || 0,
        tweetsCount: profileData.tweetsCount || 0,
        listedCount: profileData.listedCount || 0,
        verified: profileData.verified || false,
        profileImageUrl: profileData.profileImageUrl,
        location: profileData.location,
        website: profileData.website,
        createdAt: profileData.createdAt || new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error(`Failed to fetch Twitter profile: ${error.message}`, { username, correlationId });
      throw error;
    }
  }

  /**
   * Perform comprehensive Twitter analysis
   */
  private async performAnalysis(profileData: TwitterProfileData, correlationId: string): Promise<{
    score: number;
    analysisData: TwitterAnalysisResult['analysisData'];
  }> {
    this.logger.debug(`Performing analysis for: ${profileData.username}`, { correlationId });

    try {
      // Calculate account age
      const accountAge = Math.floor((Date.now() - new Date(profileData.createdAt).getTime()) / (1000 * 60 * 60 * 24));
      const engagementRate = profileData.followersCount > 0
        ? Math.min(100, (profileData.tweetsCount / profileData.followersCount) * 100)
        : 0;

      // Calculate individual scores
      const followerScore = this.calculateFollowerScore(profileData.followersCount);
      const engagementScore = this.calculateEngagementScore(engagementRate);
      const contentScore = this.calculateContentScore(profileData.tweetsCount, accountAge);
      const activityScore = this.calculateActivityScore(profileData.tweetsCount, accountAge);
      const profileScore = this.calculateProfileScore(profileData);

      // Calculate metrics
      const contentQuality = Math.round((contentScore + profileScore) / 2);
      const activityLevel = activityScore;
      const influenceScore = Math.round((followerScore + engagementScore) / 2);
      const authenticity = this.calculateAuthenticity(profileData);
      const engagement = Math.round(engagementScore);

      // Calculate overall score
      const overallScore = Math.round(
        (followerScore * 0.25) +
        (engagementScore * 0.20) +
        (contentScore * 0.20) +
        (activityScore * 0.15) +
        (profileScore * 0.20)
      );

      // Generate NFT recommendation
      const nftRecommendation = this.generateNFTRecommendation(overallScore, profileData);

      return {
        score: overallScore,
        analysisData: {
          profile: {
            followerCount: profileData.followersCount,
            followingCount: profileData.followingCount,
            tweetCount: profileData.tweetsCount,
            engagementRate: Math.round(engagementRate * 100) / 100,
            accountAge,
            isVerified: profileData.verified,
            hasProfileImage: !!profileData.profileImageUrl,
            hasBio: !!profileData.description,
          },
          metrics: {
            contentQuality,
            activityLevel,
            influenceScore,
            authenticity,
            engagement,
          },
          breakdown: {
            followerScore,
            engagementScore,
            contentScore,
            activityScore,
            profileScore,
          },
          nftRecommendation,
        },
      };

    } catch (error) {
      this.logger.error(`Analysis failed: ${error.message}`, { correlationId });
      throw error;
    }
  }

  /**
   * Calculate follower score (0-100)
   */
  private calculateFollowerScore(followers: number): number {
    if (followers < 100) return 10;
    if (followers < 500) return 25;
    if (followers < 1000) return 40;
    if (followers < 5000) return 60;
    if (followers < 10000) return 75;
    if (followers < 50000) return 85;
    if (followers < 100000) return 90;
    return 95;
  }

  /**
   * Calculate engagement score (0-100)
   */
  private calculateEngagementScore(engagementRate: number): number {
    if (engagementRate < 1) return 20;
    if (engagementRate < 2) return 40;
    if (engagementRate < 3) return 60;
    if (engagementRate < 5) return 75;
    if (engagementRate < 8) return 85;
    return 95;
  }

  /**
   * Calculate content score (0-100)
   */
  private calculateContentScore(tweets: number, accountAge: number): number {
    const tweetsPerDay = accountAge > 0 ? tweets / accountAge : 0;

    if (tweetsPerDay < 0.1) return 20;
    if (tweetsPerDay < 0.5) return 40;
    if (tweetsPerDay < 1) return 60;
    if (tweetsPerDay < 3) return 80;
    if (tweetsPerDay < 10) return 90;
    return 85; // Too many tweets per day might indicate spam
  }

  /**
   * Calculate activity score (0-100)
   */
  private calculateActivityScore(tweets: number, accountAge: number): number {
    if (tweets < 10) return 10;
    if (tweets < 50) return 30;
    if (tweets < 100) return 50;
    if (tweets < 500) return 70;
    if (tweets < 1000) return 80;
    if (tweets < 5000) return 90;
    return 95;
  }

  /**
   * Calculate profile score (0-100)
   */
  private calculateProfileScore(profile: TwitterProfileData): number {
    let score = 0;

    // Profile image (20 points)
    if (profile.profileImageUrl) score += 20;

    // Bio/description (25 points)
    if (profile.description && profile.description.length > 10) score += 25;

    // Location (15 points)
    if (profile.location) score += 15;

    // Website (15 points)
    if (profile.website) score += 15;

    // Verification (25 points)
    if (profile.verified) score += 25;

    return Math.min(100, score);
  }

  /**
   * Calculate authenticity score (0-100)
   */
  private calculateAuthenticity(profile: TwitterProfileData): number {
    let score = 50; // Base score

    // Verified accounts get bonus
    if (profile.verified) score += 30;

    // Reasonable follower/following ratio
    const ratio = profile.followingCount > 0 ? profile.followersCount / profile.followingCount : 0;
    if (ratio > 0.1 && ratio < 10) score += 10;

    // Complete profile
    if (profile.description && profile.location) score += 10;

    return Math.min(100, score);
  }

  /**
   * Generate NFT recommendation based on score
   */
  private generateNFTRecommendation(score: number, profile: TwitterProfileData): {
    score: number;
    rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
    reasoning: string[];
  } {
    const reasoning: string[] = [];
    let rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic' = 'common';

    // Determine rarity based on score
    if (score >= 90) {
      rarity = 'mythic';
      reasoning.push('Exceptional social media presence');
    } else if (score >= 80) {
      rarity = 'legendary';
      reasoning.push('Outstanding social media influence');
    } else if (score >= 70) {
      rarity = 'epic';
      reasoning.push('Strong social media engagement');
    } else if (score >= 50) {
      rarity = 'rare';
      reasoning.push('Good social media activity');
    } else {
      rarity = 'common';
      reasoning.push('Basic social media presence');
    }

    // Add specific reasoning based on profile characteristics
    if (profile.verified) reasoning.push('Verified account');
    if (profile.followersCount > 10000) reasoning.push('Large follower base');
    if (profile.description) reasoning.push('Complete profile information');

    return {
      score,
      rarity,
      reasoning,
    };
  }
}
