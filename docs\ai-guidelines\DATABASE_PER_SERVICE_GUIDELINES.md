# 🗄️ Database Per Service Guidelines

## Overview
This document establishes systematic database creation and management rules for all microservices in the Social NFT Platform.

**Date:** May 26, 2025  
**Status:** MANDATORY for all AI agents and developers  
**Compliance:** Required for all new service implementations

## 🎯 CORE PRINCIPLE

### **Database Per Service Rule**
- ✅ **Each microservice MUST have its own dedicated database**
- ✅ **No shared databases between services**
- ✅ **Service-to-service communication via APIs only**
- ✅ **Clean separation of concerns and data**

## 📋 DATABASE NAMING CONVENTION

### **Standard Format:**
```
{service_name}
```

### **Examples:**
| Service | Database Name | Purpose |
|---------|---------------|---------|
| project-service | `project_service` | Campaign management, participants |
| user-service | `user_service` | User accounts, authentication |
| profile-analysis-service | `profile_analysis` | Twitter analysis, scores |
| nft-generation-service | `nft_generation` | NFT metadata, generation history |
| blockchain-service | `blockchain_service` | Transactions, contracts |
| marketplace-service | `marketplace_service` | Listings, trades |
| analytics-service | `analytics_service` | Platform analytics, reports |
| notification-service | `notification_service` | Notifications, alerts |

## 🔧 IMPLEMENTATION PROCESS

### **Step 1: Create Database**
```sql
-- Replace {service_name} with actual service name
CREATE DATABASE {service_name};
```

### **Step 2: Configure Service Environment**
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111
DB_DATABASE={service_name}
```

### **Step 3: Update Service Configuration**
```typescript
// app.module.ts
TypeOrmModule.forRoot({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_DATABASE || '{service_name}',
  autoLoadEntities: true,
  synchronize: process.env.NODE_ENV === 'development',
}),
```

### **Step 4: Test Database Connection**
```javascript
// test-connection.js
const client = new Client({
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: '1111',
  database: '{service_name}',
});
```

## 🚫 PROHIBITED PRACTICES

### **DON'T:**
- ❌ Use shared databases between services
- ❌ Access other service databases directly
- ❌ Use generic database names like `social_nft_platform`
- ❌ Skip database naming conventions
- ❌ Create cross-service database dependencies

### **DO:**
- ✅ Create dedicated database per service
- ✅ Follow naming convention: `{service_name}`
- ✅ Use APIs for service-to-service communication
- ✅ Maintain data isolation and independence
- ✅ Document database schema and purpose

## 📊 DATABASE CREDENTIALS

### **Standard Configuration:**
```env
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111
```

### **Security Notes:**
- Use environment variables for all credentials
- Never hardcode passwords in source code
- Use different credentials for production
- Implement proper access controls

## 🎯 BENEFITS

### **Microservices Best Practices:**
- ✅ **Data Independence** - Each service owns its data
- ✅ **Scalability** - Services can scale independently
- ✅ **Maintainability** - Clear boundaries and responsibilities
- ✅ **Fault Isolation** - Database issues don't affect other services

### **Development Benefits:**
- ✅ **Clear Ownership** - Each team owns their service data
- ✅ **Independent Deployment** - Services deploy without dependencies
- ✅ **Technology Freedom** - Each service can choose optimal database
- ✅ **Testing Isolation** - Test data doesn't interfere between services

## 📝 MANDATORY CHECKLIST

### **For All AI Agents:**
- [ ] Read and understand database per service principle
- [ ] Follow naming convention: `{service_name}`
- [ ] Create dedicated database for each new service
- [ ] Configure service to use dedicated database
- [ ] Test database connection before service deployment
- [ ] Document database schema and purpose
- [ ] Update this guideline with new services

### **For New Service Creation:**
- [ ] Create database: `CREATE DATABASE {service_name};`
- [ ] Update .env file with correct database name
- [ ] Configure TypeORM with service-specific database
- [ ] Test connection with test script
- [ ] Verify service starts with new database
- [ ] Add service to database registry table above

## 🚨 COMPLIANCE ENFORCEMENT

### **This is MANDATORY for:**
- All new microservice implementations
- All AI agent service creation tasks
- All database-related development work
- All service migration and refactoring

### **Non-compliance will result in:**
- Service rejection and rebuild requirement
- Architecture review and correction
- Documentation update requirement

## ✅ SUCCESS METRICS

### **Proper Implementation:**
- Each service has its own database
- Database names follow convention
- Services communicate via APIs only
- No cross-service database access
- Clean data separation maintained

**This guideline ensures clean, scalable, and maintainable microservices architecture!** 🎯
