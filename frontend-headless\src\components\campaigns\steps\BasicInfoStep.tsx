'use client'

import React from 'react'
import {
  InformationCircleIcon,
  PhotoIcon,
  TagIcon
} from '@heroicons/react/24/outline'
import { CampaignType, CreateCampaignRequest } from '@/types/campaign.types'

interface BasicInfoStepProps {
  data: Partial<CreateCampaignRequest>
  updateData: (updates: Partial<CreateCampaignRequest>) => void
  errors: string[]
}

export default function BasicInfoStep({ data, updateData, errors }: BasicInfoStepProps) {
  const campaignTypes = [
    {
      value: CampaignType.SOCIAL_ENGAGEMENT,
      label: 'Social Engagement',
      description: 'Focus on Twitter follows, likes, retweets, and comments',
      icon: '📱'
    },
    {
      value: CampaignType.CONTENT_CREATION,
      label: 'Content Creation',
      description: 'Encourage users to create and share content',
      icon: '🎨'
    },
    {
      value: CampaignType.COMMUNITY_BUILDING,
      label: 'Community Building',
      description: 'Build communities on Discord, Telegram, or other platforms',
      icon: '👥'
    },
    {
      value: CampaignType.TRADING_ACTIVITY,
      label: 'Trading Activity',
      description: 'Encourage NFT trading and marketplace activity',
      icon: '💰'
    },
    {
      value: CampaignType.REFERRAL_PROGRAM,
      label: 'Referral Program',
      description: 'Reward users for bringing in new participants',
      icon: '🔗'
    },
    {
      value: CampaignType.MILESTONE_ACHIEVEMENT,
      label: 'Milestone Achievement',
      description: 'Reward users for reaching specific goals',
      icon: '🏆'
    },
    {
      value: CampaignType.SEASONAL_EVENT,
      label: 'Seasonal Event',
      description: 'Time-limited campaigns for special occasions',
      icon: '🎉'
    },
    {
      value: CampaignType.BRAND_COLLABORATION,
      label: 'Brand Collaboration',
      description: 'Partner campaigns with other brands or projects',
      icon: '🤝'
    }
  ]

  const popularTags = [
    'NFT', 'Web3', 'Crypto', 'DeFi', 'Gaming', 'Art', 'Music', 'Sports',
    'Fashion', 'Photography', 'Collectibles', 'Utility', 'Community', 'Social'
  ]

  const handleTagToggle = (tag: string) => {
    const currentTags = data.tags || []
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag]
    updateData({ tags: newTags })
  }

  const handleCustomTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const input = e.target as HTMLInputElement
      const tag = input.value.trim()
      if (tag && !(data.tags || []).includes(tag)) {
        updateData({ tags: [...(data.tags || []), tag] })
        input.value = ''
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Campaign Name */}
      <div>
        <label htmlFor="campaign-name" className="block text-sm font-medium text-gray-700 mb-2">
          Campaign Name *
        </label>
        <input
          type="text"
          id="campaign-name"
          value={data.name || ''}
          onChange={(e) => updateData({ name: e.target.value })}
          placeholder="Enter a compelling campaign name"
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${
            errors.some(e => e.includes('name')) ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        <p className="mt-1 text-sm text-gray-500">
          Choose a clear, engaging name that describes your campaign's purpose
        </p>
      </div>

      {/* Campaign Description */}
      <div>
        <label htmlFor="campaign-description" className="block text-sm font-medium text-gray-700 mb-2">
          Campaign Description *
        </label>
        <textarea
          id="campaign-description"
          rows={4}
          value={data.description || ''}
          onChange={(e) => updateData({ description: e.target.value })}
          placeholder="Describe what your campaign is about, what participants will do, and what they'll gain"
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${
            errors.some(e => e.includes('description')) ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        <p className="mt-1 text-sm text-gray-500">
          Provide a detailed description to help participants understand your campaign
        </p>
      </div>

      {/* Campaign Type */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Campaign Type *
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {campaignTypes.map((type) => (
            <div
              key={type.value}
              onClick={() => updateData({ type: type.value })}
              className={`relative p-4 border rounded-lg cursor-pointer transition-all hover:border-blue-300 ${
                data.type === type.value
                  ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{type.icon}</span>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-900">{type.label}</h3>
                  <p className="text-xs text-gray-600 mt-1">{type.description}</p>
                </div>
              </div>
              {data.type === type.value && (
                <div className="absolute top-2 right-2">
                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Campaign Banner */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Campaign Banner (Optional)
        </label>
        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
          <div className="space-y-1 text-center">
            <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
            <div className="flex text-sm text-gray-600">
              <label
                htmlFor="banner-upload"
                className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
              >
                <span>Upload a banner</span>
                <input
                  id="banner-upload"
                  name="banner-upload"
                  type="file"
                  accept="image/*"
                  className="sr-only"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      // Handle file upload - would typically upload to storage service
                      const url = URL.createObjectURL(file)
                      updateData({ bannerImage: url })
                    }
                  }}
                />
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>
            <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
          </div>
        </div>
        {data.bannerImage && (
          <div className="mt-3">
            <img
              src={data.bannerImage}
              alt="Campaign banner preview"
              className="w-full h-32 object-cover rounded-md"
            />
          </div>
        )}
      </div>

      {/* Campaign Tags */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <TagIcon className="inline h-4 w-4 mr-1" />
          Campaign Tags
        </label>
        
        {/* Popular Tags */}
        <div className="mb-3">
          <p className="text-sm text-gray-600 mb-2">Popular tags:</p>
          <div className="flex flex-wrap gap-2">
            {popularTags.map((tag) => (
              <button
                key={tag}
                onClick={() => handleTagToggle(tag)}
                className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                  (data.tags || []).includes(tag)
                    ? 'bg-blue-100 border-blue-300 text-blue-700'
                    : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>

        {/* Custom Tag Input */}
        <div>
          <input
            type="text"
            placeholder="Add custom tag and press Enter"
            onKeyDown={handleCustomTag}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Selected Tags */}
        {data.tags && data.tags.length > 0 && (
          <div className="mt-3">
            <p className="text-sm text-gray-600 mb-2">Selected tags:</p>
            <div className="flex flex-wrap gap-2">
              {data.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-full"
                >
                  {tag}
                  <button
                    onClick={() => handleTagToggle(tag)}
                    className="ml-2 text-blue-500 hover:text-blue-700"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Priority and Featured */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-2">
            Priority Level
          </label>
          <select
            id="priority"
            value={data.priority || 5}
            onChange={(e) => updateData({ priority: parseInt(e.target.value) })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value={1}>1 - Lowest</option>
            <option value={2}>2 - Low</option>
            <option value={3}>3 - Below Normal</option>
            <option value={4}>4 - Normal</option>
            <option value={5}>5 - Above Normal</option>
            <option value={6}>6 - High</option>
            <option value={7}>7 - Highest</option>
          </select>
          <p className="mt-1 text-sm text-gray-500">
            Higher priority campaigns are shown more prominently
          </p>
        </div>

        <div>
          <label className="flex items-center space-x-3 mt-6">
            <input
              type="checkbox"
              checked={data.featured || false}
              onChange={(e) => updateData({ featured: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm font-medium text-gray-700">Featured Campaign</span>
          </label>
          <p className="mt-1 text-sm text-gray-500 ml-7">
            Featured campaigns appear in special sections and get more visibility
          </p>
        </div>
      </div>

      {/* Help Text */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-blue-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Campaign Setup Tips</h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Choose a campaign type that matches your goals</li>
                <li>Use clear, action-oriented language in your name and description</li>
                <li>Add relevant tags to help users discover your campaign</li>
                <li>A compelling banner image can increase participation rates</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
