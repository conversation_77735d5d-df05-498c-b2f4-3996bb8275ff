import { <PERSON>, Get, Post, Body, Res, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { TemplateService } from '../services/template.service';

@ApiTags('Templates')
@Controller('templates')
export class TemplateController {
  private readonly logger = new Logger(TemplateController.name);

  constructor(private readonly templateService: TemplateService) {}

  @Get()
  @ApiOperation({
    summary: 'Get NFT templates',
    description: 'Retrieve available NFT generation templates'
  })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getTemplates(@Res() res: Response) {
    try {
      this.logger.log('Getting NFT templates');
      
      const result = await this.templateService.getTemplates();
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Templates retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Post()
  @ApiOperation({
    summary: 'Create NFT template',
    description: 'Create a new NFT generation template'
  })
  @ApiBody({
    description: 'Template creation data',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Fantasy Collection Template' },
        description: { type: 'string', example: 'Template for fantasy-themed NFTs' },
        baseImageUrl: { type: 'string', example: 'https://example.com/base.png' },
        traits: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              type: { type: 'string' },
              options: { type: 'array', items: { type: 'string' } }
            }
          }
        }
      },
      required: ['name', 'description']
    }
  })
  @ApiResponse({ status: 201, description: 'Template created successfully' })
  async createTemplate(@Body() templateData: any, @Res() res: Response) {
    try {
      this.logger.log('Creating NFT template');
      
      const result = await this.templateService.createTemplate(templateData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Template creation failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }
}
