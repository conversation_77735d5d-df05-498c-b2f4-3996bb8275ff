import { Injectable, Logger } from '@nestjs/common';

export interface AnalyticsEvent {
  id: string;
  eventType: string;
  userId?: string;
  sessionId?: string;
  timestamp: Date;
  properties: Record<string, any>;
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    referrer?: string;
    platform?: string;
    source?: string;
  };
}

export interface EventTrackingOptions {
  batchSize?: number;
  flushInterval?: number; // milliseconds
  enableRealTime?: boolean;
  enableBatching?: boolean;
}

export interface EventMetrics {
  totalEvents: number;
  eventsPerSecond: number;
  uniqueUsers: number;
  topEvents: Array<{ eventType: string; count: number }>;
  recentEvents: AnalyticsEvent[];
}

@Injectable()
export class EventTrackingService {
  private readonly logger = new Logger(EventTrackingService.name);
  private eventBuffer: AnalyticsEvent[] = [];
  private eventMetrics: Map<string, number> = new Map();
  private userSessions: Map<string, Set<string>> = new Map();
  private flushTimer?: NodeJS.Timeout;

  private readonly options: EventTrackingOptions = {
    batchSize: 100,
    flushInterval: 5000, // 5 seconds
    enableRealTime: true,
    enableBatching: true,
  };

  constructor() {
    this.startBatchProcessor();
  }

  /**
   * Track a single analytics event
   */
  async trackEvent(eventData: Omit<AnalyticsEvent, 'id' | 'timestamp'>): Promise<string> {
    try {
      const event: AnalyticsEvent = {
        id: this.generateEventId(),
        timestamp: new Date(),
        ...eventData,
      };

      // Add to buffer for batch processing
      if (this.options.enableBatching) {
        this.eventBuffer.push(event);
      }

      // Process immediately if real-time is enabled
      if (this.options.enableRealTime) {
        await this.processEvent(event);
      }

      // Update metrics
      this.updateMetrics(event);

      this.logger.debug(`Event tracked: ${event.eventType}`, { eventId: event.id });
      return event.id;
    } catch (error) {
      this.logger.error(`Failed to track event: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Track multiple events in batch
   */
  async trackEvents(events: Array<Omit<AnalyticsEvent, 'id' | 'timestamp'>>): Promise<string[]> {
    try {
      const eventIds: string[] = [];

      for (const eventData of events) {
        const eventId = await this.trackEvent(eventData);
        eventIds.push(eventId);
      }

      this.logger.log(`Batch tracked ${events.length} events`);
      return eventIds;
    } catch (error) {
      this.logger.error(`Failed to track batch events: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Track user action with context
   */
  async trackUserAction(
    userId: string,
    action: string,
    properties: Record<string, any> = {},
    metadata: Record<string, any> = {}
  ): Promise<string> {
    return this.trackEvent({
      eventType: 'user_action',
      userId,
      properties: {
        action,
        ...properties,
      },
      metadata: {
        source: 'user_interaction',
        ...metadata,
      },
    });
  }

  /**
   * Track page view
   */
  async trackPageView(
    userId: string,
    page: string,
    sessionId: string,
    metadata: Record<string, any> = {}
  ): Promise<string> {
    return this.trackEvent({
      eventType: 'page_view',
      userId,
      sessionId,
      properties: {
        page,
        timestamp: new Date().toISOString(),
      },
      metadata: {
        source: 'navigation',
        ...metadata,
      },
    });
  }

  /**
   * Track NFT-related events
   */
  async trackNFTEvent(
    eventType: 'nft_generated' | 'nft_minted' | 'nft_viewed' | 'nft_shared',
    userId: string,
    nftId: string,
    properties: Record<string, any> = {}
  ): Promise<string> {
    return this.trackEvent({
      eventType,
      userId,
      properties: {
        nftId,
        category: 'nft',
        ...properties,
      },
      metadata: {
        source: 'nft_system',
      },
    });
  }

  /**
   * Track analysis events
   */
  async trackAnalysisEvent(
    eventType: 'analysis_started' | 'analysis_completed' | 'analysis_failed',
    userId: string,
    analysisId: string,
    properties: Record<string, any> = {}
  ): Promise<string> {
    return this.trackEvent({
      eventType,
      userId,
      properties: {
        analysisId,
        category: 'analysis',
        ...properties,
      },
      metadata: {
        source: 'analysis_system',
      },
    });
  }

  /**
   * Get current event metrics
   */
  getEventMetrics(): EventMetrics {
    const totalEvents = Array.from(this.eventMetrics.values()).reduce((sum, count) => sum + count, 0);
    const uniqueUsers = this.userSessions.size;
    
    // Calculate events per second (approximate)
    const eventsPerSecond = this.eventBuffer.length / (this.options.flushInterval! / 1000);

    // Get top events
    const topEvents = Array.from(this.eventMetrics.entries())
      .map(([eventType, count]) => ({ eventType, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Get recent events
    const recentEvents = this.eventBuffer.slice(-20);

    return {
      totalEvents,
      eventsPerSecond: Math.round(eventsPerSecond * 100) / 100,
      uniqueUsers,
      topEvents,
      recentEvents,
    };
  }

  /**
   * Get events by type
   */
  getEventsByType(eventType: string, limit: number = 100): AnalyticsEvent[] {
    return this.eventBuffer
      .filter(event => event.eventType === eventType)
      .slice(-limit);
  }

  /**
   * Get events by user
   */
  getEventsByUser(userId: string, limit: number = 100): AnalyticsEvent[] {
    return this.eventBuffer
      .filter(event => event.userId === userId)
      .slice(-limit);
  }

  /**
   * Clear event buffer (for testing or maintenance)
   */
  clearEventBuffer(): void {
    this.eventBuffer = [];
    this.eventMetrics.clear();
    this.userSessions.clear();
    this.logger.log('Event buffer cleared');
  }

  /**
   * Process individual event
   */
  private async processEvent(event: AnalyticsEvent): Promise<void> {
    try {
      // In a real implementation, this would send to analytics service
      // For now, we'll just log and store in memory
      this.logger.debug(`Processing event: ${event.eventType}`, {
        eventId: event.id,
        userId: event.userId,
        properties: event.properties,
      });

      // Track user sessions
      if (event.userId && event.sessionId) {
        if (!this.userSessions.has(event.userId)) {
          this.userSessions.set(event.userId, new Set());
        }
        this.userSessions.get(event.userId)!.add(event.sessionId);
      }
    } catch (error) {
      this.logger.error(`Failed to process event: ${error.message}`, error.stack);
    }
  }

  /**
   * Update event metrics
   */
  private updateMetrics(event: AnalyticsEvent): void {
    const currentCount = this.eventMetrics.get(event.eventType) || 0;
    this.eventMetrics.set(event.eventType, currentCount + 1);
  }

  /**
   * Start batch processor
   */
  private startBatchProcessor(): void {
    if (!this.options.enableBatching) return;

    this.flushTimer = setInterval(async () => {
      await this.flushEventBuffer();
    }, this.options.flushInterval);

    this.logger.log(`Batch processor started with ${this.options.flushInterval}ms interval`);
  }

  /**
   * Flush event buffer
   */
  private async flushEventBuffer(): Promise<void> {
    if (this.eventBuffer.length === 0) return;

    try {
      const eventsToFlush = this.eventBuffer.splice(0, this.options.batchSize);
      
      // In a real implementation, this would send to external analytics service
      this.logger.debug(`Flushed ${eventsToFlush.length} events to analytics store`);

      // Process each event if not already processed
      if (!this.options.enableRealTime) {
        for (const event of eventsToFlush) {
          await this.processEvent(event);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to flush event buffer: ${error.message}`, error.stack);
    }
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup on service destruction
   */
  onModuleDestroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    // Flush remaining events
    this.flushEventBuffer();
  }
}
