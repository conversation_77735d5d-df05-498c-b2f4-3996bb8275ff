import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);
  private readonly startTime = Date.now();

  constructor(private readonly configService: ConfigService) {}

  getGatewayInfo() {
    const serviceUrls = {
      user: this.configService.get<string>('USER_SERVICE_URL'),
      project: this.configService.get<string>('PROJECT_SERVICE_URL'),
      nft: this.configService.get<string>('NFT_GENERATOR_SERVICE_URL'),
      blockchain: this.configService.get<string>('BLOCKCHAIN_SERVICE_URL'),
      marketplace: this.configService.get<string>('MARKETPLACE_SERVICE_URL'),
      analytics: this.configService.get<string>('ANALYTICS_SERVICE_URL'),
      profile: this.configService.get<string>('PROFILE_ANALYSIS_SERVICE_URL'),
    };

    const info = {
      service: 'API Gateway',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      services: serviceUrls,
      features: {
        authentication: true,
        rateLimit: true,
        healthChecks: true,
        swagger: this.configService.get<boolean>('SWAGGER_ENABLED', true),
        cors: true,
        security: true,
      },
    };

    this.logger.log('Gateway info retrieved', { 
      uptime: info.uptime,
      environment: info.environment 
    });

    return info;
  }

  getStatus() {
    const memoryUsage = process.memoryUsage();
    
    const status = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
      },
      cpu: {
        usage: process.cpuUsage(),
      },
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      nodeVersion: process.version,
      platform: process.platform,
    };

    this.logger.debug('Gateway status checked', {
      uptime: status.uptime,
      memoryUsed: Math.round(status.memory.used / 1024 / 1024) + 'MB',
    });

    return status;
  }

  async checkServiceHealth(serviceUrl: string): Promise<boolean> {
    try {
      // This would make an actual HTTP call to check service health
      // For now, we'll simulate it
      this.logger.debug(`Checking health for service: ${serviceUrl}`);
      return true;
    } catch (error) {
      this.logger.error(`Health check failed for service: ${serviceUrl}`, error.message);
      return false;
    }
  }
}
