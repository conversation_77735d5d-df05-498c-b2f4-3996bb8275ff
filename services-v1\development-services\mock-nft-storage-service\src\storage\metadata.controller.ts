import { <PERSON>, Post, Get, Body, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { StorageService } from './storage.service';
import { NFTMetadata } from '../interfaces/storage.interfaces';

@ApiTags('metadata')
@Controller('metadata')
export class MetadataController {
  constructor(private readonly storageService: StorageService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload NFT metadata' })
  @ApiResponse({ status: 201, description: 'Metadata uploaded successfully' })
  async uploadMetadata(@Body() metadata: NFTMetadata) {
    const result = await this.storageService.uploadMetadata(metadata);
    return {
      status: result.success ? 'success' : 'error',
      data: result,
      message: result.success ? '<PERSON>ada<PERSON> uploaded successfully' : 'Failed to upload metadata'
    };
  }

  @Get(':hash')
  @ApiOperation({ summary: 'Get metadata by hash' })
  @ApiResponse({ status: 200, description: 'Metadata retrieved successfully' })
  async getMetadata(@Param('hash') hash: string) {
    const metadata = await this.storageService.getMetadata(hash);
    return {
      status: metadata ? 'success' : 'error',
      data: metadata,
      message: metadata ? 'Metadata retrieved successfully' : 'Metadata not found'
    };
  }
}
