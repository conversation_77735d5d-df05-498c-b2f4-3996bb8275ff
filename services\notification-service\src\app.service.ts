import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo() {
    return {
      name: 'Notification Service',
      version: '1.0.0',
      description: 'Real-time Notifications & Communication',
      status: 'running',
      timestamp: new Date().toISOString(),
      features: [
        'Email Notifications',
        'SMS Notifications', 
        'Push Notifications',
        'In-App Notifications',
        'Notification Templates',
        'User Preferences',
        'Event-Driven Notifications',
        'Scheduled Notifications',
        'Notification Analytics'
      ],
      channels: [
        'Email (SMTP)',
        'SMS (Twilio)',
        'Push (Firebase)',
        'In-App (WebSocket)',
        'Webhook'
      ],
      endpoints: {
        health: '/api/health',
        docs: '/api/docs',
        notifications: '/api/notifications',
        templates: '/api/templates',
        preferences: '/api/preferences',
        channels: '/api/channels',
        events: '/api/events'
      }
    };
  }
}
