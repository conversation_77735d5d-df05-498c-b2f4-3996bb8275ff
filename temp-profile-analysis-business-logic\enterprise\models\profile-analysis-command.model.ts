// Enterprise Profile Analysis Command Model (Write Side) - Template
import { IsString, IsOptional, IsEnum, IsNumber } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum Platform {
  TWITTER = 'twitter',
  INSTAGRAM = 'instagram',
  LINKEDIN = 'linkedin',
  GITHUB = 'github'
}

export class CreateProfileAnalysisCommandDto {
  @ApiProperty({ description: 'User ID to analyze' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'Social platform', enum: Platform })
  @IsEnum(Platform)
  platform: Platform;

  @ApiProperty({ description: 'Profile handle or username' })
  @IsString()
  profileHandle: string;
}
