'use client'

import React, { useState, useRef } from 'react'
import {
  XMarkIcon,
  PhotoIcon,
  GlobeAltIcon,
  LockClosedIcon,
  EyeSlashIcon,
  UserGroupIcon,
  HashtagIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { useCreateCommunity } from '@/hooks/useSocial'
import { CreateCommunityRequest, CommunityType, CommunityVisibility, CommunityCategory } from '@/types/social.types'

interface CreateCommunityModalProps {
  onClose: () => void
  onCommunityCreated: (community: any) => void
  className?: string
}

export default function CreateCommunityModal({
  onClose,
  onCommunityCreated,
  className = ''
}: CreateCommunityModalProps) {
  const [communityData, setCommunityData] = useState<CreateCommunityRequest>({
    name: '',
    description: '',
    category: CommunityCategory.GENERAL,
    type: CommunityType.PUBLIC,
    visibility: CommunityVisibility.PUBLIC,
    tags: [],
    rules: [],
    settings: {
      allowMemberPosts: true,
      requireApproval: false,
      allowInvites: true,
      showMemberList: true
    }
  })
  
  const [selectedAvatar, setSelectedAvatar] = useState<File | null>(null)
  const [selectedBanner, setSelectedBanner] = useState<File | null>(null)
  const [avatarPreviewUrl, setAvatarPreviewUrl] = useState<string>('')
  const [bannerPreviewUrl, setBannerPreviewUrl] = useState<string>('')
  const [tagInput, setTagInput] = useState('')
  const [ruleInput, setRuleInput] = useState('')
  const [currentStep, setCurrentStep] = useState(1)
  
  const avatarInputRef = useRef<HTMLInputElement>(null)
  const bannerInputRef = useRef<HTMLInputElement>(null)
  const createCommunityMutation = useCreateCommunity()

  const categories = [
    { value: CommunityCategory.GENERAL, label: 'General Discussion', description: 'Open discussions about various topics' },
    { value: CommunityCategory.TRADING, label: 'Trading & Markets', description: 'NFT trading, market analysis, and tips' },
    { value: CommunityCategory.COLLECTING, label: 'Collecting', description: 'NFT collecting strategies and showcases' },
    { value: CommunityCategory.ART, label: 'Art & Creativity', description: 'Digital art, creative processes, and inspiration' },
    { value: CommunityCategory.GAMING, label: 'Gaming', description: 'Gaming NFTs, play-to-earn, and game discussions' },
    { value: CommunityCategory.MUSIC, label: 'Music & Audio', description: 'Music NFTs, audio content, and artist communities' },
    { value: CommunityCategory.SPORTS, label: 'Sports', description: 'Sports NFTs, fantasy leagues, and athlete content' },
    { value: CommunityCategory.TECHNOLOGY, label: 'Technology', description: 'Blockchain tech, development, and innovation' },
    { value: CommunityCategory.EDUCATION, label: 'Education', description: 'Learning resources, tutorials, and knowledge sharing' },
    { value: CommunityCategory.ENTERTAINMENT, label: 'Entertainment', description: 'Movies, TV, celebrities, and entertainment NFTs' }
  ]

  const communityTypes = [
    {
      value: CommunityType.PUBLIC,
      label: 'Public',
      description: 'Anyone can join and participate',
      icon: <GlobeAltIcon className="h-5 w-5" />
    },
    {
      value: CommunityType.PRIVATE,
      label: 'Private',
      description: 'Members must be approved to join',
      icon: <LockClosedIcon className="h-5 w-5" />
    },
    {
      value: CommunityType.INVITE_ONLY,
      label: 'Invite Only',
      description: 'Members can only join by invitation',
      icon: <EyeSlashIcon className="h-5 w-5" />
    }
  ]

  const visibilityOptions = [
    {
      value: CommunityVisibility.PUBLIC,
      label: 'Public',
      description: 'Community appears in search and discovery'
    },
    {
      value: CommunityVisibility.UNLISTED,
      label: 'Unlisted',
      description: 'Community is accessible but not discoverable'
    },
    {
      value: CommunityVisibility.PRIVATE,
      label: 'Private',
      description: 'Community is completely hidden from non-members'
    }
  ]

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedAvatar(file)
      setAvatarPreviewUrl(URL.createObjectURL(file))
    }
  }

  const handleBannerUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedBanner(file)
      setBannerPreviewUrl(URL.createObjectURL(file))
    }
  }

  const addTag = () => {
    if (tagInput.trim() && !communityData.tags.includes(tagInput.trim()) && communityData.tags.length < 10) {
      setCommunityData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const removeTag = (tag: string) => {
    setCommunityData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }))
  }

  const addRule = () => {
    if (ruleInput.trim() && communityData.rules.length < 20) {
      setCommunityData(prev => ({
        ...prev,
        rules: [...prev.rules, ruleInput.trim()]
      }))
      setRuleInput('')
    }
  }

  const removeRule = (index: number) => {
    setCommunityData(prev => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!communityData.name.trim() || !communityData.description.trim()) {
      alert('Please fill in all required fields.')
      return
    }

    try {
      // In a real implementation, you would upload images first
      const avatarUrl = selectedAvatar ? `https://example.com/uploads/avatar-${Date.now()}.jpg` : undefined
      const bannerUrl = selectedBanner ? `https://example.com/uploads/banner-${Date.now()}.jpg` : undefined

      const community = await createCommunityMutation.mutateAsync({
        ...communityData,
        avatar: avatarUrl,
        banner: bannerUrl
      })

      onCommunityCreated(community)
    } catch (error) {
      console.error('Failed to create community:', error)
      alert('Failed to create community. Please try again.')
    }
  }

  const isSubmitting = createCommunityMutation.isPending
  const canProceed = (step: number) => {
    switch (step) {
      case 1:
        return communityData.name.trim().length >= 3 && communityData.description.trim().length >= 10
      case 2:
        return true
      case 3:
        return true
      default:
        return false
    }
  }

  const steps = [
    { number: 1, title: 'Basic Information', description: 'Name, description, and category' },
    { number: 2, title: 'Settings & Privacy', description: 'Type, visibility, and permissions' },
    { number: 3, title: 'Customization', description: 'Images, tags, and rules' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Create Community</h2>
            <p className="text-sm text-gray-600">Build your NFT community</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= step.number
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step.number}
                </div>
                <div className="ml-3">
                  <div className="text-sm font-medium text-gray-900">{step.title}</div>
                  <div className="text-xs text-gray-600">{step.description}</div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    currentStep > step.number ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Community Name *
                </label>
                <input
                  type="text"
                  value={communityData.name}
                  onChange={(e) => setCommunityData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter community name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  required
                />
                <div className="mt-1 text-xs text-gray-500">
                  {communityData.name.length}/50 characters (minimum 3)
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  value={communityData.description}
                  onChange={(e) => setCommunityData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your community's purpose and goals"
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  required
                />
                <div className="mt-1 text-xs text-gray-500">
                  {communityData.description.length}/500 characters (minimum 10)
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Category</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {categories.map((category) => (
                    <label key={category.value} className="flex items-start">
                      <input
                        type="radio"
                        name="category"
                        value={category.value}
                        checked={communityData.category === category.value}
                        onChange={(e) => setCommunityData(prev => ({ ...prev, category: e.target.value as CommunityCategory }))}
                        className="mt-1 mr-3 text-blue-600 focus:ring-blue-500"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{category.label}</div>
                        <div className="text-xs text-gray-600">{category.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Settings & Privacy */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Community Type</label>
                <div className="space-y-3">
                  {communityTypes.map((type) => (
                    <label key={type.value} className="flex items-center">
                      <input
                        type="radio"
                        name="type"
                        value={type.value}
                        checked={communityData.type === type.value}
                        onChange={(e) => setCommunityData(prev => ({ ...prev, type: e.target.value as CommunityType }))}
                        className="mr-3 text-blue-600 focus:ring-blue-500"
                      />
                      <div className="flex items-center">
                        {type.icon}
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">{type.label}</div>
                          <div className="text-xs text-gray-600">{type.description}</div>
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Visibility</label>
                <div className="space-y-3">
                  {visibilityOptions.map((option) => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="visibility"
                        value={option.value}
                        checked={communityData.visibility === option.value}
                        onChange={(e) => setCommunityData(prev => ({ ...prev, visibility: e.target.value as CommunityVisibility }))}
                        className="mr-3 text-blue-600 focus:ring-blue-500"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{option.label}</div>
                        <div className="text-xs text-gray-600">{option.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Community Settings</label>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Allow member posts</span>
                    <input
                      type="checkbox"
                      checked={communityData.settings.allowMemberPosts}
                      onChange={(e) => setCommunityData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, allowMemberPosts: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </label>
                  
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Require post approval</span>
                    <input
                      type="checkbox"
                      checked={communityData.settings.requireApproval}
                      onChange={(e) => setCommunityData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, requireApproval: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </label>
                  
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Allow member invites</span>
                    <input
                      type="checkbox"
                      checked={communityData.settings.allowInvites}
                      onChange={(e) => setCommunityData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, allowInvites: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </label>
                  
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Show member list</span>
                    <input
                      type="checkbox"
                      checked={communityData.settings.showMemberList}
                      onChange={(e) => setCommunityData(prev => ({
                        ...prev,
                        settings: { ...prev.settings, showMemberList: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Customization */}
          {currentStep === 3 && (
            <div className="space-y-6">
              {/* Images */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Community Avatar</label>
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                      {avatarPreviewUrl ? (
                        <img src={avatarPreviewUrl} alt="Avatar preview" className="w-full h-full object-cover" />
                      ) : (
                        <UserGroupIcon className="h-8 w-8 text-gray-400" />
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => avatarInputRef.current?.click()}
                      className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      Upload Avatar
                    </button>
                  </div>
                  <input
                    ref={avatarInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarUpload}
                    className="hidden"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Community Banner</label>
                  <div className="w-full h-16 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden mb-2">
                    {bannerPreviewUrl ? (
                      <img src={bannerPreviewUrl} alt="Banner preview" className="w-full h-full object-cover" />
                    ) : (
                      <PhotoIcon className="h-8 w-8 text-gray-400" />
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => bannerInputRef.current?.click()}
                    className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Upload Banner
                  </button>
                  <input
                    ref={bannerInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleBannerUpload}
                    className="hidden"
                  />
                </div>
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                <div className="flex items-center space-x-2 mb-2">
                  <div className="relative flex-1">
                    <HashtagIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      placeholder="Add a tag"
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 w-full"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={addTag}
                    className="px-3 py-2 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Add
                  </button>
                </div>
                
                {communityData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {communityData.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        #{tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-1 text-blue-600 hover:text-blue-800"
                        >
                          <XMarkIcon className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
                <div className="mt-1 text-xs text-gray-500">
                  {communityData.tags.length}/10 tags
                </div>
              </div>

              {/* Rules */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Community Rules</label>
                <div className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={ruleInput}
                    onChange={(e) => setRuleInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addRule())}
                    placeholder="Add a community rule"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={addRule}
                    className="px-3 py-2 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Add
                  </button>
                </div>
                
                {communityData.rules.length > 0 && (
                  <div className="space-y-2">
                    {communityData.rules.map((rule, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm text-gray-900">{index + 1}. {rule}</span>
                        <button
                          type="button"
                          onClick={() => removeRule(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
                <div className="mt-1 text-xs text-gray-500">
                  {communityData.rules.length}/20 rules
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="flex items-center space-x-3">
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={() => setCurrentStep(currentStep - 1)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Previous
                </button>
              )}
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              
              {currentStep < 3 ? (
                <button
                  type="button"
                  onClick={() => setCurrentStep(currentStep + 1)}
                  disabled={!canProceed(currentStep)}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={isSubmitting || !canProceed(currentStep)}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Creating...' : 'Create Community'}
                </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
