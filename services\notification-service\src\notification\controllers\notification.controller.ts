import { <PERSON>, Get, Post, Body, Query, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { NotificationManagementService } from '../services/notification-management.service';

@ApiTags('notifications')
@Controller('notifications')
export class NotificationController {
  private readonly logger = new Logger(NotificationController.name);

  constructor(private readonly notificationManagement: NotificationManagementService) {}

  @Post('send')
  @ApiOperation({ summary: 'Send a notification' })
  @ApiResponse({ status: 201, description: 'Notification sent successfully' })
  async sendNotification(@Body() notificationData: any) {
    try {
      this.logger.log('Sending notification', { type: notificationData.type });

      const result = await this.notificationManagement.sendNotification(notificationData);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to send notification: ${error.message}`);
      return {
        success: false,
        error: 'Failed to send notification',
        details: error.message,
      };
    }
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get notifications for a user' })
  @ApiResponse({ status: 200, description: 'User notifications retrieved successfully' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of notifications to return' })
  async getUserNotifications(
    @Param('userId') userId: string,
    @Query('limit') limit: string = '50'
  ) {
    try {
      this.logger.log(`Getting notifications for user: ${userId}`);

      const notifications = await this.notificationManagement.getUserNotifications(userId, parseInt(limit));

      return {
        success: true,
        data: {
          userId,
          notifications,
          count: notifications.length,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get user notifications: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve notifications',
        details: error.message,
      };
    }
  }

  @Get('status/:notificationId')
  @ApiOperation({ summary: 'Get notification status' })
  @ApiResponse({ status: 200, description: 'Notification status retrieved successfully' })
  @ApiParam({ name: 'notificationId', description: 'Notification ID' })
  async getNotificationStatus(@Param('notificationId') notificationId: string) {
    try {
      this.logger.log(`Getting status for notification: ${notificationId}`);

      const status = await this.notificationManagement.getNotificationStatus(notificationId);

      return {
        success: true,
        data: status,
      };
    } catch (error) {
      this.logger.error(`Failed to get notification status: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve notification status',
        details: error.message,
      };
    }
  }
}
