/**
 * Standardized Authentication Decorators
 * Provides consistent authentication and authorization decorators across all services
 */

import { SetMetadata, createParamDecorator, ExecutionContext, applyDecorators, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiUnauthorizedResponse, ApiForbiddenResponse } from '@nestjs/swagger';
import { Permission, Role } from '../interfaces/permission.interface';
import { AuthenticatedUser } from '../interfaces/auth.interface';
import { StandardizedJwtAuthGuard, OptionalJwtAuthGuard, ApiKeyAuthGuard } from '../guards/jwt-auth.guard';
import { StandardizedRBACGuard } from '../guards/rbac.guard';

/**
 * Mark a route as public (no authentication required)
 */
export const Public = () => SetMetadata('isPublic', true);

/**
 * Require authentication but no specific permissions
 */
export const Authenticated = () => applyDecorators(
  UseGuards(StandardizedJwtAuthGuard),
  ApiBearerAuth(),
  ApiUnauthorizedResponse({ description: 'Authentication required' }),
);

/**
 * Optional authentication (allows both authenticated and anonymous access)
 */
export const OptionalAuth = () => applyDecorators(
  UseGuards(OptionalJwtAuthGuard),
  ApiBearerAuth(),
);

/**
 * Require API key authentication (for service-to-service communication)
 */
export const ApiKeyAuth = () => applyDecorators(
  UseGuards(ApiKeyAuthGuard),
  ApiUnauthorizedResponse({ description: 'Valid API key required' }),
);

/**
 * Require specific permissions (user must have ALL permissions)
 */
export const RequirePermissions = (...permissions: Permission[]) => applyDecorators(
  SetMetadata('permissions', permissions),
  UseGuards(StandardizedJwtAuthGuard, StandardizedRBACGuard),
  ApiBearerAuth(),
  ApiUnauthorizedResponse({ description: 'Authentication required' }),
  ApiForbiddenResponse({ description: 'Insufficient permissions' }),
);

/**
 * Require any of the specified permissions (user must have AT LEAST ONE permission)
 */
export const RequireAnyPermission = (...permissions: Permission[]) => applyDecorators(
  SetMetadata('anyPermissions', permissions),
  UseGuards(StandardizedJwtAuthGuard, StandardizedRBACGuard),
  ApiBearerAuth(),
  ApiUnauthorizedResponse({ description: 'Authentication required' }),
  ApiForbiddenResponse({ description: 'Insufficient permissions' }),
);

/**
 * Require specific roles
 */
export const RequireRoles = (...roles: Role[]) => applyDecorators(
  SetMetadata('roles', roles),
  UseGuards(StandardizedJwtAuthGuard, StandardizedRBACGuard),
  ApiBearerAuth(),
  ApiUnauthorizedResponse({ description: 'Authentication required' }),
  ApiForbiddenResponse({ description: 'Insufficient role permissions' }),
);

/**
 * Require resource ownership (user must own the resource being accessed)
 */
export const RequireOwnership = () => applyDecorators(
  SetMetadata('requireOwnership', true),
  UseGuards(StandardizedJwtAuthGuard, StandardizedRBACGuard),
  ApiBearerAuth(),
  ApiUnauthorizedResponse({ description: 'Authentication required' }),
  ApiForbiddenResponse({ description: 'Resource access denied' }),
);

/**
 * Combine ownership requirement with permissions
 */
export const RequireOwnershipOrPermissions = (...permissions: Permission[]) => applyDecorators(
  SetMetadata('permissions', permissions),
  SetMetadata('requireOwnership', true),
  SetMetadata('ownershipOrPermissions', true),
  UseGuards(StandardizedJwtAuthGuard, StandardizedRBACGuard),
  ApiBearerAuth(),
  ApiUnauthorizedResponse({ description: 'Authentication required' }),
  ApiForbiddenResponse({ description: 'Insufficient permissions or ownership required' }),
);

// ===== CONVENIENCE DECORATORS FOR COMMON PERMISSIONS =====

/**
 * User Management Permissions
 */
export const RequireUserRead = () => RequirePermissions(Permission.USER_READ);
export const RequireUserWrite = () => RequirePermissions(Permission.USER_WRITE);
export const RequireUserDelete = () => RequirePermissions(Permission.USER_DELETE);
export const RequireUserAdmin = () => RequirePermissions(Permission.USER_ADMIN);

/**
 * NFT Management Permissions
 */
export const RequireNFTRead = () => RequirePermissions(Permission.NFT_READ);
export const RequireNFTWrite = () => RequirePermissions(Permission.NFT_WRITE);
export const RequireNFTDelete = () => RequirePermissions(Permission.NFT_DELETE);
export const RequireNFTAdmin = () => RequirePermissions(Permission.NFT_ADMIN);
export const RequireNFTMint = () => RequirePermissions(Permission.NFT_MINT);
export const RequireNFTTransfer = () => RequirePermissions(Permission.NFT_TRANSFER);

/**
 * Project Management Permissions
 */
export const RequireProjectRead = () => RequirePermissions(Permission.PROJECT_READ);
export const RequireProjectWrite = () => RequirePermissions(Permission.PROJECT_WRITE);
export const RequireProjectDelete = () => RequirePermissions(Permission.PROJECT_DELETE);
export const RequireProjectAdmin = () => RequirePermissions(Permission.PROJECT_ADMIN);
export const RequireProjectPublish = () => RequirePermissions(Permission.PROJECT_PUBLISH);
export const RequireProjectModerate = () => RequirePermissions(Permission.PROJECT_MODERATE);

/**
 * Marketplace Permissions
 */
export const RequireMarketplaceRead = () => RequirePermissions(Permission.MARKETPLACE_READ);
export const RequireMarketplaceWrite = () => RequirePermissions(Permission.MARKETPLACE_WRITE);
export const RequireMarketplaceAdmin = () => RequirePermissions(Permission.MARKETPLACE_ADMIN);
export const RequireMarketplaceBuy = () => RequirePermissions(Permission.MARKETPLACE_BUY);
export const RequireMarketplaceSell = () => RequirePermissions(Permission.MARKETPLACE_SELL);
export const RequireMarketplaceList = () => RequirePermissions(Permission.MARKETPLACE_LIST);

/**
 * Blockchain Permissions
 */
export const RequireBlockchainRead = () => RequirePermissions(Permission.BLOCKCHAIN_READ);
export const RequireBlockchainWrite = () => RequirePermissions(Permission.BLOCKCHAIN_WRITE);
export const RequireBlockchainAdmin = () => RequirePermissions(Permission.BLOCKCHAIN_ADMIN);
export const RequireBlockchainDeploy = () => RequirePermissions(Permission.BLOCKCHAIN_DEPLOY);
export const RequireBlockchainInteract = () => RequirePermissions(Permission.BLOCKCHAIN_INTERACT);

/**
 * Analytics Permissions
 */
export const RequireAnalyticsRead = () => RequirePermissions(Permission.ANALYTICS_READ);
export const RequireAnalyticsWrite = () => RequirePermissions(Permission.ANALYTICS_WRITE);
export const RequireAnalyticsAdmin = () => RequirePermissions(Permission.ANALYTICS_ADMIN);
export const RequireAnalyticsExport = () => RequirePermissions(Permission.ANALYTICS_EXPORT);

/**
 * System Administration Permissions
 */
export const RequireSystemRead = () => RequirePermissions(Permission.SYSTEM_READ);
export const RequireSystemWrite = () => RequirePermissions(Permission.SYSTEM_WRITE);
export const RequireSystemAdmin = () => RequirePermissions(Permission.SYSTEM_ADMIN);
export const RequireSystemConfig = () => RequirePermissions(Permission.SYSTEM_CONFIG);
export const RequireSystemMonitor = () => RequirePermissions(Permission.SYSTEM_MONITOR);

/**
 * Audit Permissions
 */
export const RequireAuditRead = () => RequirePermissions(Permission.AUDIT_READ);
export const RequireAuditWrite = () => RequirePermissions(Permission.AUDIT_WRITE);
export const RequireAuditAdmin = () => RequirePermissions(Permission.AUDIT_ADMIN);
export const RequireAuditExport = () => RequirePermissions(Permission.AUDIT_EXPORT);

// ===== ROLE-BASED DECORATORS =====

/**
 * Basic Role Requirements
 */
export const RequireUser = () => RequireRoles(Role.USER);
export const RequireCreator = () => RequireRoles(Role.CREATOR, Role.VERIFIED_CREATOR);
export const RequireModerator = () => RequireRoles(Role.MODERATOR, Role.COMMUNITY_MANAGER, Role.ADMIN, Role.SUPER_ADMIN);
export const RequireAdmin = () => RequireRoles(Role.ADMIN, Role.SUPER_ADMIN);
export const RequireSuperAdmin = () => RequireRoles(Role.SUPER_ADMIN);

/**
 * Combined Role and Permission Requirements
 */
export const RequireAdminOrOwnership = () => RequireOwnershipOrPermissions(Permission.USER_ADMIN, Permission.SYSTEM_ADMIN);
export const RequireModeratorOrOwnership = () => RequireOwnershipOrPermissions(
  Permission.PROJECT_MODERATE,
  Permission.NFT_ADMIN,
  Permission.MARKETPLACE_MODERATE
);

// ===== PARAMETER DECORATORS =====

/**
 * Get current authenticated user from request
 */
export const CurrentUser = createParamDecorator(
  (data: keyof AuthenticatedUser | undefined, ctx: ExecutionContext): AuthenticatedUser | any => {
    const request = ctx.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;

    if (!user) {
      return null;
    }

    return data ? user[data] : user;
  },
);

/**
 * Get current user ID
 */
export const CurrentUserId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string | null => {
    const request = ctx.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;
    return user?.id || null;
  },
);

/**
 * Get current user roles
 */
export const CurrentUserRoles = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string[] => {
    const request = ctx.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;
    return user?.roles || [];
  },
);

/**
 * Get current user permissions
 */
export const CurrentUserPermissions = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string[] => {
    const request = ctx.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;
    return user?.permissions || [];
  },
);

/**
 * Get current session information
 */
export const CurrentSession = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.session || null;
  },
);

/**
 * Get request correlation ID
 */
export const CorrelationId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.correlationId || 'unknown';
  },
);

// ===== UTILITY DECORATORS =====

/**
 * Mark endpoint as requiring elevated security (additional logging, monitoring)
 */
export const ElevatedSecurity = () => SetMetadata('elevatedSecurity', true);

/**
 * Mark endpoint as sensitive (additional audit logging)
 */
export const SensitiveOperation = () => SetMetadata('sensitiveOperation', true);

/**
 * Rate limiting decorator
 */
export const RateLimit = (limit: number, windowMs: number = 60000) => SetMetadata('rateLimit', { limit, windowMs });

/**
 * IP whitelist decorator
 */
export const IPWhitelist = (allowedIPs: string[]) => SetMetadata('ipWhitelist', allowedIPs);
