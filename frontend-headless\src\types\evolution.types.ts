import { NFTRarity, NFTEvolution } from './nft.types'

export interface EvolutionTimeline {
  id: string
  nftId: string
  events: EvolutionEvent[]
  totalEvolutions: number
  firstEvolutionDate?: string
  lastEvolutionDate?: string
  evolutionRate: number // evolutions per month
  nextPredictedEvolution?: PredictedEvolution
}

export interface EvolutionEvent {
  id: string
  nftId: string
  eventType: EvolutionEventType
  timestamp: string
  previousState: NFTState
  newState: NFTState
  trigger: EvolutionTrigger
  changes: EvolutionChange[]
  metadata?: EvolutionEventMetadata
}

export enum EvolutionEventType {
  SCORE_UPDATE = 'score_update',
  RARITY_EVOLUTION = 'rarity_evolution',
  ATTRIBUTE_CHANGE = 'attribute_change',
  VISUAL_UPDATE = 'visual_update',
  METADATA_UPDATE = 'metadata_update',
  MILESTONE_REACHED = 'milestone_reached'
}

export interface NFTState {
  score: number
  rarity: NFTRarity
  attributes: Record<string, any>
  imageUrl: string
  metadata: Record<string, any>
}

export interface EvolutionTrigger {
  type: EvolutionTriggerType
  source: string
  description: string
  threshold?: number
  automatic: boolean
  triggeredBy?: string // userId if manual
}

export enum EvolutionTriggerType {
  SCORE_THRESHOLD = 'score_threshold',
  TIME_BASED = 'time_based',
  ENGAGEMENT_BOOST = 'engagement_boost',
  MANUAL_UPDATE = 'manual_update',
  CAMPAIGN_MILESTONE = 'campaign_milestone',
  SOCIAL_ACTIVITY = 'social_activity',
  COMMUNITY_VOTE = 'community_vote'
}

export interface EvolutionChange {
  attribute: string
  oldValue: any
  newValue: any
  changeType: EvolutionChangeType
  impact: EvolutionImpact
  description: string
}

export enum EvolutionChangeType {
  VISUAL = 'visual',
  METADATA = 'metadata',
  RARITY = 'rarity',
  SCORE = 'score',
  ATTRIBUTE = 'attribute',
  SPECIAL_EFFECT = 'special_effect'
}

export enum EvolutionImpact {
  MINOR = 'minor',
  MODERATE = 'moderate',
  MAJOR = 'major',
  LEGENDARY = 'legendary'
}

export interface EvolutionEventMetadata {
  campaignId?: string
  socialMetrics?: SocialMetrics
  performanceData?: PerformanceData
  userActions?: UserAction[]
  systemNotes?: string[]
}

export interface SocialMetrics {
  followerCount: number
  engagementRate: number
  contentQuality: number
  influenceScore: number
  activityLevel: number
}

export interface PerformanceData {
  scoreGain: number
  timeToEvolution: number // in days
  evolutionEfficiency: number
  comparedToAverage: number
}

export interface UserAction {
  action: string
  timestamp: string
  impact: number
  description: string
}

export interface PredictedEvolution {
  estimatedDate: string
  confidence: number
  requiredScoreIncrease: number
  targetRarity: NFTRarity
  factors: PredictionFactor[]
  recommendations: string[]
}

export interface PredictionFactor {
  factor: string
  currentValue: number
  targetValue: number
  importance: number
  description: string
}

export interface EvolutionAnalytics {
  nftId: string
  totalEvolutions: number
  evolutionVelocity: number // evolutions per month
  averageScoreGain: number
  rarityProgression: RarityProgression[]
  evolutionPatterns: EvolutionPattern[]
  milestones: EvolutionMilestone[]
  comparativeRanking: ComparativeRanking
}

export interface RarityProgression {
  rarity: NFTRarity
  achievedAt: string
  daysToAchieve: number
  scoreAtAchievement: number
  evolutionCount: number
}

export interface EvolutionPattern {
  pattern: string
  frequency: number
  averageImpact: number
  description: string
  examples: string[]
}

export interface EvolutionMilestone {
  id: string
  name: string
  description: string
  achievedAt: string
  rarity: NFTRarity
  scoreRequired: number
  reward?: string
  badge?: string
}

export interface ComparativeRanking {
  overallRank: number
  totalNFTs: number
  percentile: number
  categoryRank: number
  evolutionSpeedRank: number
  scoreGrowthRank: number
}

export interface EvolutionFilters {
  eventTypes?: EvolutionEventType[]
  triggerTypes?: EvolutionTriggerType[]
  rarities?: NFTRarity[]
  dateRange?: {
    start: string
    end: string
  }
  scoreRange?: {
    min: number
    max: number
  }
  impactLevel?: EvolutionImpact[]
  automatic?: boolean
}

export interface EvolutionStats {
  totalEvents: number
  evolutionsByType: Record<EvolutionEventType, number>
  evolutionsByTrigger: Record<EvolutionTriggerType, number>
  averageEvolutionTime: number
  fastestEvolution: number
  slowestEvolution: number
  mostActiveMonth: string
  evolutionTrend: 'increasing' | 'decreasing' | 'stable'
}

export interface CreateEvolutionEventRequest {
  nftId: string
  eventType: EvolutionEventType
  trigger: EvolutionTrigger
  previousState: NFTState
  newState: NFTState
  changes: EvolutionChange[]
  metadata?: EvolutionEventMetadata
}

export interface UpdateEvolutionRequest {
  score?: number
  attributes?: Record<string, any>
  metadata?: Record<string, any>
  trigger: EvolutionTrigger
}

export interface EvolutionNotification {
  id: string
  nftId: string
  userId: string
  type: 'evolution_occurred' | 'milestone_reached' | 'prediction_update'
  title: string
  message: string
  data: {
    evolutionEvent?: EvolutionEvent
    milestone?: EvolutionMilestone
    prediction?: PredictedEvolution
  }
  read: boolean
  createdAt: string
}

export interface EvolutionInsights {
  nftId: string
  insights: Insight[]
  recommendations: Recommendation[]
  trends: Trend[]
  opportunities: Opportunity[]
  warnings: Warning[]
}

export interface Insight {
  type: 'positive' | 'neutral' | 'negative'
  title: string
  description: string
  impact: EvolutionImpact
  data: any
}

export interface Recommendation {
  priority: 'high' | 'medium' | 'low'
  action: string
  description: string
  expectedImpact: string
  timeframe: string
}

export interface Trend {
  metric: string
  direction: 'up' | 'down' | 'stable'
  change: number
  period: string
  significance: 'high' | 'medium' | 'low'
}

export interface Opportunity {
  title: string
  description: string
  potentialGain: number
  difficulty: 'easy' | 'medium' | 'hard'
  timeframe: string
}

export interface Warning {
  severity: 'high' | 'medium' | 'low'
  title: string
  description: string
  action: string
  deadline?: string
}
