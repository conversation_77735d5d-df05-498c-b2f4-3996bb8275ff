'use client'

import React, { useState } from 'react'
import {
  QueueListIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  PlusIcon,
  TrashIcon,
  CogIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import {
  useGenerationQueues,
  useCreateQueue,
  usePauseQueue,
  useResumeQueue,
  useGenerationBatches,
  useCreateBatch,
  useProcessBatch
} from '@/hooks/useNFTGeneration'
import { GenerationProvider } from '@/types/nft-generation.types'

interface QueueManagementProps {
  className?: string
}

export default function QueueManagement({ className = '' }: QueueManagementProps) {
  const [showCreateQueue, setShowCreateQueue] = useState(false)
  const [showCreateBatch, setShowCreateBatch] = useState(false)
  const [selectedQueueId, setSelectedQueueId] = useState<string>('')

  const { data: queues, isLoading: queuesLoading } = useGenerationQueues()
  const { data: batches, isLoading: batchesLoading } = useGenerationBatches(selectedQueueId)
  
  const createQueueMutation = useCreateQueue()
  const pauseQueueMutation = usePauseQueue()
  const resumeQueueMutation = useResumeQueue()
  const createBatchMutation = useCreateBatch()
  const processBatchMutation = useProcessBatch()

  const [newQueue, setNewQueue] = useState({
    name: '',
    campaignId: '',
    provider: GenerationProvider.STABLE_DIFFUSION,
    maxConcurrent: 5,
    priority: 5
  })

  const [newBatch, setNewBatch] = useState({
    queueId: '',
    campaignId: '',
    requestIds: [] as string[],
    priority: 5
  })

  const handleCreateQueue = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await createQueueMutation.mutateAsync(newQueue)
      setShowCreateQueue(false)
      setNewQueue({
        name: '',
        campaignId: '',
        provider: GenerationProvider.STABLE_DIFFUSION,
        maxConcurrent: 5,
        priority: 5
      })
    } catch (error) {
      console.error('Failed to create queue:', error)
    }
  }

  const handleCreateBatch = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await createBatchMutation.mutateAsync(newBatch)
      setShowCreateBatch(false)
      setNewBatch({
        queueId: '',
        campaignId: '',
        requestIds: [],
        priority: 5
      })
    } catch (error) {
      console.error('Failed to create batch:', error)
    }
  }

  const handleQueueAction = async (queueId: string, action: 'pause' | 'resume') => {
    try {
      if (action === 'pause') {
        await pauseQueueMutation.mutateAsync(queueId)
      } else {
        await resumeQueueMutation.mutateAsync(queueId)
      }
    } catch (error) {
      console.error(`Failed to ${action} queue:`, error)
    }
  }

  const handleProcessBatch = async (batchId: string) => {
    try {
      await processBatchMutation.mutateAsync(batchId)
    } catch (error) {
      console.error('Failed to process batch:', error)
    }
  }

  const getQueueStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100 border-green-200'
      case 'paused': return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      case 'stopped': return 'text-red-600 bg-red-100 border-red-200'
      default: return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getBatchStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-gray-600 bg-gray-100'
      case 'processing': return 'text-blue-600 bg-blue-100'
      case 'completed': return 'text-green-600 bg-green-100'
      case 'failed': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getProviderIcon = (provider: GenerationProvider) => {
    switch (provider) {
      case GenerationProvider.OPENAI_DALLE: return '🤖'
      case GenerationProvider.MIDJOURNEY: return '🎨'
      case GenerationProvider.STABLE_DIFFUSION: return '🔬'
      case GenerationProvider.CUSTOM_AI: return '⚙️'
      case GenerationProvider.MANUAL: return '✋'
      default: return '🖼️'
    }
  }

  if (queuesLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded mb-4"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Queue Management</h3>
          <p className="text-sm text-gray-600">Manage generation queues and batch processing</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowCreateBatch(true)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <QueueListIcon className="h-4 w-4 mr-2" />
            New Batch
          </button>
          
          <button
            onClick={() => setShowCreateQueue(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Queue
          </button>
        </div>
      </div>

      {/* Queues List */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900">Active Queues</h4>
        
        {queues && queues.length > 0 ? (
          <div className="space-y-4">
            {queues.map((queue) => (
              <div key={queue.id} className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getProviderIcon(queue.provider)}</span>
                    <div>
                      <h5 className="font-medium text-gray-900">{queue.name}</h5>
                      <p className="text-sm text-gray-600">{queue.provider.replace('_', ' ')}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getQueueStatusColor(queue.status)}`}>
                      {queue.status.charAt(0).toUpperCase() + queue.status.slice(1)}
                    </span>
                    
                    <div className="flex items-center space-x-1">
                      {queue.status === 'active' && (
                        <button
                          onClick={() => handleQueueAction(queue.id, 'pause')}
                          disabled={pauseQueueMutation.isPending}
                          className="p-2 text-yellow-600 hover:text-yellow-700 disabled:opacity-50"
                          title="Pause Queue"
                        >
                          <PauseIcon className="h-4 w-4" />
                        </button>
                      )}
                      
                      {queue.status === 'paused' && (
                        <button
                          onClick={() => handleQueueAction(queue.id, 'resume')}
                          disabled={resumeQueueMutation.isPending}
                          className="p-2 text-green-600 hover:text-green-700 disabled:opacity-50"
                          title="Resume Queue"
                        >
                          <PlayIcon className="h-4 w-4" />
                        </button>
                      )}
                      
                      <button
                        onClick={() => setSelectedQueueId(selectedQueueId === queue.id ? '' : queue.id)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                        title="View Batches"
                      >
                        <ChartBarIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Queue Stats */}
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">{queue.maxConcurrent}</div>
                    <div className="text-xs text-gray-600">Max Concurrent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">{queue.totalRequests}</div>
                    <div className="text-xs text-gray-600">Total Requests</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-600">{queue.completedRequests}</div>
                    <div className="text-xs text-gray-600">Completed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-red-600">{queue.failedRequests}</div>
                    <div className="text-xs text-gray-600">Failed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-blue-600">
                      {(queue.successRate * 100).toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-600">Success Rate</div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                    <span>Progress</span>
                    <span>{queue.totalRequests > 0 ? Math.round((queue.completedRequests / queue.totalRequests) * 100) : 0}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${queue.totalRequests > 0 ? (queue.completedRequests / queue.totalRequests) * 100 : 0}%` 
                      }}
                    ></div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center space-x-4">
                    <span>Avg Time: {Math.round(queue.averageGenerationTime / 60)}m</span>
                    <span>Priority: {queue.priority}</span>
                  </div>
                  <div>
                    Last processed: {queue.lastProcessedAt 
                      ? new Date(queue.lastProcessedAt).toLocaleString()
                      : 'Never'
                    }
                  </div>
                </div>

                {/* Batches for Selected Queue */}
                {selectedQueueId === queue.id && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                      <h6 className="text-sm font-medium text-gray-900">Queue Batches</h6>
                      <button
                        onClick={() => {
                          setNewBatch(prev => ({ ...prev, queueId: queue.id }))
                          setShowCreateBatch(true)
                        }}
                        className="text-sm text-purple-600 hover:text-purple-700"
                      >
                        Add Batch
                      </button>
                    </div>

                    {batchesLoading ? (
                      <div className="animate-pulse space-y-2">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="h-16 bg-gray-200 rounded"></div>
                        ))}
                      </div>
                    ) : batches && batches.length > 0 ? (
                      <div className="space-y-3">
                        {batches.map((batch) => (
                          <div key={batch.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getBatchStatusColor(batch.status)}`}>
                                {batch.status}
                              </span>
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  Batch #{batch.id.slice(-6)}
                                </div>
                                <div className="text-xs text-gray-600">
                                  {batch.completedRequests}/{batch.totalRequests} completed
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <div className="text-xs text-gray-600">
                                Priority: {batch.priority}
                              </div>
                              {batch.status === 'pending' && (
                                <button
                                  onClick={() => handleProcessBatch(batch.id)}
                                  disabled={processBatchMutation.isPending}
                                  className="p-1 text-green-600 hover:text-green-700 disabled:opacity-50"
                                  title="Process Batch"
                                >
                                  <PlayIcon className="h-3 w-3" />
                                </button>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4 text-sm text-gray-500">
                        No batches in this queue
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
            <QueueListIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No queues found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Create your first generation queue to start processing NFTs.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setShowCreateQueue(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Queue
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Create Queue Modal */}
      {showCreateQueue && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Create New Queue</h3>
              <button
                onClick={() => setShowCreateQueue(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleCreateQueue} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Queue Name
                </label>
                <input
                  type="text"
                  value={newQueue.name}
                  onChange={(e) => setNewQueue(prev => ({ ...prev, name: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                  placeholder="e.g., High Priority Queue"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Campaign ID
                </label>
                <input
                  type="text"
                  value={newQueue.campaignId}
                  onChange={(e) => setNewQueue(prev => ({ ...prev, campaignId: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Campaign ID"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Provider
                </label>
                <select
                  value={newQueue.provider}
                  onChange={(e) => setNewQueue(prev => ({ ...prev, provider: e.target.value as GenerationProvider }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                >
                  {Object.values(GenerationProvider).map((provider) => (
                    <option key={provider} value={provider}>
                      {provider.replace('_', ' ')}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Concurrent
                  </label>
                  <input
                    type="number"
                    value={newQueue.maxConcurrent}
                    onChange={(e) => setNewQueue(prev => ({ ...prev, maxConcurrent: parseInt(e.target.value) }))}
                    min="1"
                    max="20"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <input
                    type="number"
                    value={newQueue.priority}
                    onChange={(e) => setNewQueue(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
                    min="1"
                    max="10"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateQueue(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createQueueMutation.isPending}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
                >
                  {createQueueMutation.isPending ? 'Creating...' : 'Create Queue'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Create Batch Modal */}
      {showCreateBatch && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Create New Batch</h3>
              <button
                onClick={() => setShowCreateBatch(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleCreateBatch} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Queue
                </label>
                <select
                  value={newBatch.queueId}
                  onChange={(e) => setNewBatch(prev => ({ ...prev, queueId: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="">Select a queue</option>
                  {queues?.map((queue) => (
                    <option key={queue.id} value={queue.id}>
                      {queue.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Campaign ID
                </label>
                <input
                  type="text"
                  value={newBatch.campaignId}
                  onChange={(e) => setNewBatch(prev => ({ ...prev, campaignId: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Campaign ID"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Request IDs (comma-separated)
                </label>
                <textarea
                  value={newBatch.requestIds.join(', ')}
                  onChange={(e) => setNewBatch(prev => ({ 
                    ...prev, 
                    requestIds: e.target.value.split(',').map(id => id.trim()).filter(Boolean)
                  }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                  placeholder="req1, req2, req3..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Priority
                </label>
                <input
                  type="number"
                  value={newBatch.priority}
                  onChange={(e) => setNewBatch(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
                  min="1"
                  max="10"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                />
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateBatch(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createBatchMutation.isPending}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
                >
                  {createBatchMutation.isPending ? 'Creating...' : 'Create Batch'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
