/**
 * Global Exception Filter
 * 
 * Provides standardized error handling and response formatting across all microservices
 * Implements comprehensive error logging and security event tracking
 */

import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ResponseService } from '../responses/services/response.service';
import { ServiceLoggerService, SecurityEventType, SecuritySeverity } from '../logging/services/service-logger.service';
import { ErrorCode } from '../responses/interfaces/base-response.interface';

/**
 * Global Exception Filter
 * Catches all unhandled exceptions and formats them consistently
 */
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  constructor(
    private readonly responseService: ResponseService,
    private readonly serviceLogger: ServiceLoggerService
  ) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const correlationId = request.correlationId || this.generateCorrelationId();
    const startTime = request.startTime || Date.now();
    const duration = Date.now() - startTime;

    // Determine error details
    const errorDetails = this.extractErrorDetails(exception);
    
    // Log the error
    this.logError(exception, request, errorDetails, correlationId, duration);

    // Create standardized error response
    const errorResponse = this.createErrorResponse(errorDetails, correlationId);

    // Set response headers
    this.setErrorHeaders(response, errorDetails, correlationId);

    // Send response
    response.status(errorDetails.statusCode).json(errorResponse);
  }

  /**
   * Extract error details from exception
   */
  private extractErrorDetails(exception: unknown): {
    statusCode: number;
    code: ErrorCode;
    message: string;
    details?: any;
    validation?: any[];
    stack?: string;
    isOperational: boolean;
  } {
    // HTTP Exception
    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const response = exception.getResponse();
      
      return {
        statusCode: status,
        code: this.mapStatusToErrorCode(status),
        message: typeof response === 'string' ? response : (response as any).message || exception.message,
        details: typeof response === 'object' ? response : undefined,
        validation: (response as any)?.validation,
        stack: exception.stack,
        isOperational: true,
      };
    }

    // Validation Error (class-validator)
    if (this.isValidationError(exception)) {
      return {
        statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        code: ErrorCode.VALIDATION_ERROR,
        message: 'Validation failed',
        validation: this.extractValidationErrors(exception),
        stack: (exception as Error).stack,
        isOperational: true,
      };
    }

    // Database Error
    if (this.isDatabaseError(exception)) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        code: ErrorCode.DATABASE_ERROR,
        message: 'Database operation failed',
        details: this.sanitizeDatabaseError(exception),
        stack: (exception as Error).stack,
        isOperational: false,
      };
    }

    // Timeout Error
    if (this.isTimeoutError(exception)) {
      return {
        statusCode: HttpStatus.GATEWAY_TIMEOUT,
        code: ErrorCode.TIMEOUT_ERROR,
        message: 'Operation timed out',
        details: { timeout: true },
        stack: (exception as Error).stack,
        isOperational: true,
      };
    }

    // Generic Error
    const error = exception as Error;
    return {
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_SERVER_ERROR,
      message: error.message || 'Internal server error',
      details: { name: error.name },
      stack: error.stack,
      isOperational: false,
    };
  }

  /**
   * Log error with appropriate level and context
   */
  private logError(
    exception: unknown,
    request: Request,
    errorDetails: any,
    correlationId: string,
    duration: number
  ): void {
    const logContext = {
      correlationId,
      userId: request.user?.id,
      sessionId: request.session?.sessionId,
      operation: `${request.method}:${request.path}`,
      request: {
        method: request.method,
        path: request.path,
        url: request.url,
        userAgent: request.headers['user-agent'],
        ipAddress: this.getClientIP(request),
        headers: this.sanitizeHeaders(request.headers),
        query: request.query,
        params: request.params,
      },
      response: {
        statusCode: errorDetails.statusCode,
      },
      performance: {
        duration,
      },
      error: {
        name: (exception as Error).name,
        message: errorDetails.message,
        code: errorDetails.code,
        statusCode: errorDetails.statusCode,
        stack: errorDetails.stack,
        details: errorDetails.details,
      },
    };

    // Log as security event if it's a security-related error
    if (this.isSecurityError(errorDetails)) {
      this.serviceLogger.logSecurityEvent(
        this.getSecurityEventType(errorDetails),
        this.getSecuritySeverity(errorDetails),
        errorDetails.message,
        logContext
      );
    }

    // Log based on error severity
    if (errorDetails.statusCode >= 500) {
      this.serviceLogger.error(`Server error: ${errorDetails.message}`, exception as Error, logContext);
    } else if (errorDetails.statusCode >= 400) {
      this.serviceLogger.warn(`Client error: ${errorDetails.message}`, logContext);
    } else {
      this.serviceLogger.info(`Request completed with error: ${errorDetails.message}`, logContext);
    }
  }

  /**
   * Create standardized error response
   */
  private createErrorResponse(errorDetails: any, correlationId: string) {
    return this.responseService.error(
      errorDetails.message,
      errorDetails.code,
      {
        details: errorDetails.details,
        validation: errorDetails.validation,
        statusCode: errorDetails.statusCode,
        correlationId,
      }
    );
  }

  /**
   * Set error response headers
   */
  private setErrorHeaders(response: Response, errorDetails: any, correlationId: string): void {
    response.setHeader('X-Correlation-ID', correlationId);
    response.setHeader('X-Error-Code', errorDetails.code);
    
    // Add retry-after header for rate limiting
    if (errorDetails.statusCode === HttpStatus.TOO_MANY_REQUESTS) {
      response.setHeader('Retry-After', '60');
    }

    // Add security headers
    response.setHeader('X-Content-Type-Options', 'nosniff');
    response.setHeader('X-Frame-Options', 'DENY');
  }

  /**
   * Map HTTP status to error code
   */
  private mapStatusToErrorCode(status: number): ErrorCode {
    const statusMap: Record<number, ErrorCode> = {
      400: ErrorCode.BAD_REQUEST,
      401: ErrorCode.UNAUTHORIZED,
      403: ErrorCode.FORBIDDEN,
      404: ErrorCode.NOT_FOUND,
      409: ErrorCode.CONFLICT,
      422: ErrorCode.VALIDATION_ERROR,
      429: ErrorCode.RATE_LIMIT_EXCEEDED,
      500: ErrorCode.INTERNAL_SERVER_ERROR,
      502: ErrorCode.BAD_GATEWAY,
      503: ErrorCode.SERVICE_UNAVAILABLE,
      504: ErrorCode.GATEWAY_TIMEOUT,
    };

    return statusMap[status] || ErrorCode.INTERNAL_SERVER_ERROR;
  }

  /**
   * Check if error is validation error
   */
  private isValidationError(exception: unknown): boolean {
    return (
      exception &&
      typeof exception === 'object' &&
      'constraints' in (exception as any)
    );
  }

  /**
   * Check if error is database error
   */
  private isDatabaseError(exception: unknown): boolean {
    const error = exception as Error;
    return (
      error.name?.includes('Prisma') ||
      error.name?.includes('Database') ||
      error.message?.includes('database') ||
      error.message?.includes('connection')
    );
  }

  /**
   * Check if error is timeout error
   */
  private isTimeoutError(exception: unknown): boolean {
    const error = exception as Error;
    return (
      error.name?.includes('Timeout') ||
      error.message?.includes('timeout') ||
      error.message?.includes('ETIMEDOUT')
    );
  }

  /**
   * Check if error is security-related
   */
  private isSecurityError(errorDetails: any): boolean {
    return [
      ErrorCode.UNAUTHORIZED,
      ErrorCode.FORBIDDEN,
      ErrorCode.INVALID_TOKEN,
      ErrorCode.TOKEN_EXPIRED,
      ErrorCode.RATE_LIMIT_EXCEEDED,
    ].includes(errorDetails.code);
  }

  /**
   * Get security event type
   */
  private getSecurityEventType(errorDetails: any): SecurityEventType {
    switch (errorDetails.code) {
      case ErrorCode.UNAUTHORIZED:
      case ErrorCode.INVALID_TOKEN:
      case ErrorCode.TOKEN_EXPIRED:
        return SecurityEventType.AUTHENTICATION;
      case ErrorCode.FORBIDDEN:
        return SecurityEventType.AUTHORIZATION;
      case ErrorCode.RATE_LIMIT_EXCEEDED:
        return SecurityEventType.SUSPICIOUS_ACTIVITY;
      default:
        return SecurityEventType.SECURITY_VIOLATION;
    }
  }

  /**
   * Get security severity
   */
  private getSecuritySeverity(errorDetails: any): SecuritySeverity {
    switch (errorDetails.code) {
      case ErrorCode.RATE_LIMIT_EXCEEDED:
        return SecuritySeverity.HIGH;
      case ErrorCode.UNAUTHORIZED:
      case ErrorCode.FORBIDDEN:
        return SecuritySeverity.MEDIUM;
      default:
        return SecuritySeverity.LOW;
    }
  }

  /**
   * Extract validation errors
   */
  private extractValidationErrors(exception: unknown): any[] {
    // Implementation depends on validation library used
    return [];
  }

  /**
   * Sanitize database error
   */
  private sanitizeDatabaseError(exception: unknown): any {
    const error = exception as Error;
    return {
      type: 'database_error',
      name: error.name,
      // Don't expose sensitive database details in production
    };
  }

  /**
   * Get client IP address
   */
  private getClientIP(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      request.connection.remoteAddress ||
      'unknown'
    );
  }

  /**
   * Sanitize headers
   */
  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
    
    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  /**
   * Generate correlation ID
   */
  private generateCorrelationId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
