import { <PERSON>, Post, Put, Body, Param, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { UserCommandService } from '../services/user-command.service';
import { CreateUserDto, UpdateUserDto } from '../dto';
import { CompleteProfileDto } from '../dto/complete-profile.dto';
import { LinkSocialAccountDto } from '../dto/link-social-account.dto';
import { NotificationPreferencesDto } from '../dto/complete-profile.dto';
import { ErrorResponseDto } from '../../common/dto/error-response.dto';

@ApiTags('Users - Command')
@Controller('users')
export class UserCommandController {
  constructor(private readonly userCommandService: UserCommandService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input', type: ErrorResponseDto })
  @ApiResponse({ status: 409, description: 'User already exists', type: ErrorResponseDto })
  async createUser(@Body() createUserDto: CreateUserDto, @Req() request: any) {
    const result = await this.userCommandService.createUser(createUserDto, request.context);
    return result;
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update user by ID' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({ type: UpdateUserDto })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input', type: ErrorResponseDto })
  async updateUser(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto, @Req() request: any) {
    const result = await this.userCommandService.updateUser(id, updateUserDto, request.context);
    return result;
  }

  @Post(':id/profile/complete')
  @ApiOperation({ summary: 'Complete user profile with enhanced information' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({ type: CompleteProfileDto })
  @ApiResponse({ status: 200, description: 'Profile completed successfully' })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input', type: ErrorResponseDto })
  async completeProfile(@Param('id') id: string, @Body() profileData: CompleteProfileDto, @Req() request: any) {
    const result = await this.userCommandService.completeUserProfile(id, profileData, request.context);
    return result;
  }

  @Post(':id/social/link')
  @ApiOperation({ summary: 'Link social media account to user' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({ type: LinkSocialAccountDto })
  @ApiResponse({ status: 200, description: 'Social account linked successfully' })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @ApiResponse({ status: 409, description: 'Social account already linked', type: ErrorResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input', type: ErrorResponseDto })
  async linkSocialAccount(@Param('id') id: string, @Body() socialData: LinkSocialAccountDto, @Req() request: any) {
    const result = await this.userCommandService.linkSocialAccount(id, socialData, request.context);
    return result;
  }

  @Put(':id/preferences')
  @ApiOperation({ summary: 'Update user notification preferences' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({ type: NotificationPreferencesDto })
  @ApiResponse({ status: 200, description: 'Preferences updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input', type: ErrorResponseDto })
  async updatePreferences(@Param('id') id: string, @Body() preferences: NotificationPreferencesDto, @Req() request: any) {
    const result = await this.userCommandService.updateNotificationPreferences(id, preferences, request.context);
    return result;
  }
}
