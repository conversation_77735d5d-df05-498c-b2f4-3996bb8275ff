import { <PERSON>, Get, Post, Query, Res, Body, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { TwitterApiClientService } from '../services/twitter-api-client.service';
import { Public } from '../../auth/public.decorator';

@ApiTags('twitter')
@Controller('auth/twitter')
export class TwitterAuthController {
  private readonly logger = new Logger(TwitterAuthController.name);

  constructor(
    private readonly twitterApiClient: TwitterApiClientService,
  ) {}
  
  @Get('login')
  @Public()
  @ApiOperation({ summary: 'Initiate Twitter OAuth authentication' })
  @ApiResponse({ status: 302, description: 'Redirect to Twitter OAuth' })
  async initiateTwitterAuth(@Res() res: Response) {
    try {
      this.logger.log('Twitter OAuth initiation requested');
      
      // Check if we should use mock or real Twitter OAuth
      const useMockTwitter = process.env.USE_MOCK_TWITTER === 'true';
      
      if (useMockTwitter) {
        this.logger.log('Using Mock Twitter OAuth for development');

        // Redirect to our own callback endpoint first (through API Gateway)
        const mockCallbackUrl = `http://localhost:3010/api/auth/twitter/callback?code=mock_auth_code_${Date.now()}&state=mock_state`;

        this.logger.log(`Redirecting to API Gateway callback: ${mockCallbackUrl}`);
        return res.redirect(mockCallbackUrl);
        
      } else {
        this.logger.log('Using Real Twitter OAuth API');
        
        // Real Twitter OAuth configuration
        const twitterClientId = process.env.TWITTER_CLIENT_ID;
        const redirectUri = encodeURIComponent('http://localhost:3010/api/auth/twitter/callback');
        const state = `twitter_oauth_${Date.now()}`;
        const scope = encodeURIComponent('tweet.read users.read');
        
        if (!twitterClientId) {
          this.logger.error('Twitter Client ID not configured');
          return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
            success: false,
            error: 'Twitter OAuth not configured. Please set TWITTER_CLIENT_ID environment variable.'
          });
        }
        
        const twitterAuthUrl = `https://twitter.com/i/oauth2/authorize?response_type=code&client_id=${twitterClientId}&redirect_uri=${redirectUri}&scope=${scope}&state=${state}&code_challenge=challenge&code_challenge_method=plain`;
        
        this.logger.log(`Redirecting to Twitter OAuth: ${twitterAuthUrl}`);
        return res.redirect(twitterAuthUrl);
      }
      
    } catch (error) {
      this.logger.error('Twitter OAuth initiation error:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Failed to initiate Twitter authentication',
        details: error.message
      });
    }
  }

  @Get('callback')
  @Public()
  @ApiOperation({ summary: 'Handle Twitter OAuth callback' })
  @ApiResponse({ status: 302, description: 'Redirect to frontend with auth result' })
  async handleTwitterCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Query('error') error: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log('Twitter OAuth callback received');
      this.logger.log(`Callback params: code=${code ? 'present' : 'missing'}, state=${state}, error=${error}`);
      
      if (error) {
        this.logger.error(`Twitter OAuth error: ${error}`);
        const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent(error)}`;
        return res.redirect(errorUrl);
      }
      
      if (!code || !state) {
        this.logger.error('Missing required OAuth parameters');
        const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Missing authorization code or state')}`;
        return res.redirect(errorUrl);
      }
      
      // Check if we're using mock services
      const useMockTwitter = process.env.USE_MOCK_TWITTER === 'true';
      
      if (useMockTwitter) {
        this.logger.log('Processing Mock Twitter OAuth callback');

        try {
          // Call Mock Twitter Service for token exchange
          this.logger.log('Calling Twitter API Client for token exchange');
          const tokenResponse = await this.twitterApiClient.exchangeCodeForToken(code, state);

          if (!tokenResponse.success) {
            this.logger.error(`Token exchange failed: ${tokenResponse.error}`);
            const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent(`Token exchange failed: ${tokenResponse.error}`)}`;
            return res.redirect(errorUrl);
          }

          // Process service response with user data
          const { access_token, user } = tokenResponse.data;
          this.logger.log(`Token exchange successful, user: ${user.username}`);

          // Redirect with success
          const successUrl = `http://localhost:3000/auth/twitter/callback?token=${access_token}&user=${encodeURIComponent(JSON.stringify(user))}`;
          this.logger.log('Authentication successful, redirecting with token');
          return res.redirect(successUrl);

        } catch (error) {
          this.logger.error(`Mock Twitter API call failed: ${error.message}`);
          const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Service call failed: ' + error.message)}`;
          return res.redirect(errorUrl);
        }
        
      } else {
        this.logger.log('Processing Real Twitter OAuth callback');
        
        // Real Twitter OAuth token exchange
        const twitterClientId = process.env.TWITTER_CLIENT_ID;
        const twitterClientSecret = process.env.TWITTER_CLIENT_SECRET;
        
        if (!twitterClientId || !twitterClientSecret) {
          this.logger.error('Twitter OAuth credentials not configured');
          const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Twitter OAuth not properly configured')}`;
          return res.redirect(errorUrl);
        }
        
        // TODO: Implement real Twitter OAuth token exchange
        const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Real Twitter OAuth implementation pending')}`;
        this.logger.log(`Real Twitter OAuth not yet implemented, redirecting to: ${errorUrl}`);
        return res.redirect(errorUrl);
      }
      
    } catch (error) {
      this.logger.error('Twitter OAuth callback error:', error);
      const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Authentication failed: ' + error.message)}`;
      return res.redirect(errorUrl);
    }
  }

  @Post('exchange')
  @Public()
  @ApiOperation({ summary: 'Exchange Twitter OAuth code for access token' })
  @ApiResponse({ status: 200, description: 'Token exchange successful' })
  async exchangeCodeForToken(
    @Body() body: { code: string; state: string },
    @Res() res: Response
  ) {
    try {
      this.logger.log('Token exchange requested');
      
      // Check if we're using mock services
      const useMockTwitter = process.env.USE_MOCK_TWITTER === 'true';
      
      if (useMockTwitter) {
        // Return mock authentication data
        const mockAuthData = {
          success: true,
          data: {
            accessToken: 'mock_jwt_token_' + Date.now(),
            refreshToken: 'mock_refresh_token_' + Date.now(),
            user: {
              id: 'twitter_user_' + Date.now(),
              username: 'mock_twitter_user',
              email: '<EMAIL>',
              displayName: 'Mock Twitter User',
              profileImage: 'https://via.placeholder.com/150',
              provider: 'twitter',
              isVerified: true
            },
            expiresIn: 3600
          }
        };
        
        this.logger.log('Mock token exchange successful');
        return res.status(HttpStatus.OK).json(mockAuthData);
        
      } else {
        // TODO: Implement real Twitter OAuth token exchange
        this.logger.log('Real Twitter OAuth token exchange not yet implemented');
        return res.status(HttpStatus.NOT_IMPLEMENTED).json({
          success: false,
          error: 'Real Twitter OAuth token exchange not yet implemented'
        });
      }
      
    } catch (error) {
      this.logger.error('Token exchange error:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Token exchange failed',
        details: error.message
      });
    }
  }
}
