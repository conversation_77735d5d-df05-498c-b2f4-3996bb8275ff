import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { analyticsService } from '@/services/analyticsService'
import {
  CampaignAnalytics,
  AnalyticsQuery,
  AnalyticsTimeframe,
  ReportTemplate,
  GeneratedReport,
  ReportFormat,
  ChartConfig,
  ReportFilter,
  ReportSchedule
} from '@/types/analytics.types'

// ===== CAMPAIGN ANALYTICS HOOKS =====

export function useCampaignAnalytics(
  campaignId: string,
  timeframe: AnalyticsTimeframe = AnalyticsTimeframe.MONTH
) {
  return useQuery({
    queryKey: ['campaign-analytics', campaignId, timeframe],
    queryFn: () => analyticsService.getCampaignAnalytics(campaignId, timeframe),
    enabled: !!campaignId,
    staleTime: 300000, // 5 minutes
    refetchInterval: 300000, // 5 minutes for regular updates
  })
}

export function useMultipleCampaignAnalytics(query: AnalyticsQuery) {
  return useQuery({
    queryKey: ['multiple-campaign-analytics', query],
    queryFn: () => analyticsService.getMultipleCampaignAnalytics(query),
    enabled: !!query.campaignIds && query.campaignIds.length > 0,
    staleTime: 300000,
  })
}

export function useCampaignComparison(
  campaignIds: string[],
  timeframe: AnalyticsTimeframe = AnalyticsTimeframe.MONTH
) {
  return useQuery({
    queryKey: ['campaign-comparison', campaignIds, timeframe],
    queryFn: () => analyticsService.getCampaignComparison(campaignIds, timeframe),
    enabled: campaignIds.length > 1,
    staleTime: 300000,
  })
}

// ===== REAL-TIME ANALYTICS HOOKS =====

export function useRealTimeMetrics(campaignId: string) {
  return useQuery({
    queryKey: ['realtime-metrics', campaignId],
    queryFn: () => analyticsService.getRealTimeMetrics(campaignId),
    enabled: !!campaignId,
    refetchInterval: 30000, // 30 seconds for real-time updates
    staleTime: 15000, // 15 seconds
  })
}

export function useActivityFeed(campaignId: string, limit: number = 50) {
  return useQuery({
    queryKey: ['activity-feed', campaignId, limit],
    queryFn: () => analyticsService.getActivityFeed(campaignId, limit),
    enabled: !!campaignId,
    refetchInterval: 60000, // 1 minute
    staleTime: 30000,
  })
}

// ===== ENGAGEMENT ANALYTICS HOOKS =====

export function useEngagementAnalytics(campaignId: string, timeframe: AnalyticsTimeframe) {
  return useQuery({
    queryKey: ['engagement-analytics', campaignId, timeframe],
    queryFn: () => analyticsService.getEngagementAnalytics(campaignId, timeframe),
    enabled: !!campaignId,
    staleTime: 300000,
  })
}

export function useParticipantJourney(campaignId: string, participantId?: string) {
  return useQuery({
    queryKey: ['participant-journey', campaignId, participantId],
    queryFn: () => analyticsService.getParticipantJourney(campaignId, participantId),
    enabled: !!campaignId,
    staleTime: 600000, // 10 minutes
  })
}

export function useFunnelAnalysis(campaignId: string) {
  return useQuery({
    queryKey: ['funnel-analysis', campaignId],
    queryFn: () => analyticsService.getFunnelAnalysis(campaignId),
    enabled: !!campaignId,
    staleTime: 600000,
  })
}

// ===== CONVERSION ANALYTICS HOOKS =====

export function useConversionAnalytics(campaignId: string, timeframe: AnalyticsTimeframe) {
  return useQuery({
    queryKey: ['conversion-analytics', campaignId, timeframe],
    queryFn: () => analyticsService.getConversionAnalytics(campaignId, timeframe),
    enabled: !!campaignId,
    staleTime: 300000,
  })
}

export function useCohortAnalysis(campaignId: string) {
  return useQuery({
    queryKey: ['cohort-analysis', campaignId],
    queryFn: () => analyticsService.getCohortAnalysis(campaignId),
    enabled: !!campaignId,
    staleTime: 3600000, // 1 hour
  })
}

export function useRetentionAnalysis(campaignId: string) {
  return useQuery({
    queryKey: ['retention-analysis', campaignId],
    queryFn: () => analyticsService.getRetentionAnalysis(campaignId),
    enabled: !!campaignId,
    staleTime: 3600000,
  })
}

// ===== REVENUE & ROI HOOKS =====

export function useRevenueAnalytics(campaignId: string, timeframe: AnalyticsTimeframe) {
  return useQuery({
    queryKey: ['revenue-analytics', campaignId, timeframe],
    queryFn: () => analyticsService.getRevenueAnalytics(campaignId, timeframe),
    enabled: !!campaignId,
    staleTime: 300000,
  })
}

export function useROIAnalysis(campaignId: string) {
  return useQuery({
    queryKey: ['roi-analysis', campaignId],
    queryFn: () => analyticsService.getROIAnalysis(campaignId),
    enabled: !!campaignId,
    staleTime: 600000,
  })
}

export function useCostAnalysis(campaignId: string, timeframe: AnalyticsTimeframe) {
  return useQuery({
    queryKey: ['cost-analysis', campaignId, timeframe],
    queryFn: () => analyticsService.getCostAnalysis(campaignId, timeframe),
    enabled: !!campaignId,
    staleTime: 300000,
  })
}

// ===== SOCIAL & GEOGRAPHIC HOOKS =====

export function useSocialAnalytics(campaignId: string, timeframe: AnalyticsTimeframe) {
  return useQuery({
    queryKey: ['social-analytics', campaignId, timeframe],
    queryFn: () => analyticsService.getSocialAnalytics(campaignId, timeframe),
    enabled: !!campaignId,
    staleTime: 300000,
  })
}

export function useGeographicAnalytics(campaignId: string) {
  return useQuery({
    queryKey: ['geographic-analytics', campaignId],
    queryFn: () => analyticsService.getGeographicAnalytics(campaignId),
    enabled: !!campaignId,
    staleTime: 3600000,
  })
}

export function useMarketAnalysis(campaignId: string) {
  return useQuery({
    queryKey: ['market-analysis', campaignId],
    queryFn: () => analyticsService.getMarketAnalysis(campaignId),
    enabled: !!campaignId,
    staleTime: 3600000,
  })
}

// ===== BENCHMARKING HOOKS =====

export function useBenchmarkData(campaignId: string) {
  return useQuery({
    queryKey: ['benchmark-data', campaignId],
    queryFn: () => analyticsService.getBenchmarkData(campaignId),
    enabled: !!campaignId,
    staleTime: 3600000,
  })
}

export function useIndustryBenchmarks(industry: string) {
  return useQuery({
    queryKey: ['industry-benchmarks', industry],
    queryFn: () => analyticsService.getIndustryBenchmarks(industry),
    enabled: !!industry,
    staleTime: 86400000, // 24 hours
  })
}

export function useCompetitorAnalysis(campaignId: string) {
  return useQuery({
    queryKey: ['competitor-analysis', campaignId],
    queryFn: () => analyticsService.getCompetitorAnalysis(campaignId),
    enabled: !!campaignId,
    staleTime: 3600000,
  })
}

// ===== REPORT TEMPLATE HOOKS =====

export function useReportTemplates() {
  return useQuery({
    queryKey: ['report-templates'],
    queryFn: () => analyticsService.getReportTemplates(),
    staleTime: 600000,
  })
}

export function useReportTemplate(id: string) {
  return useQuery({
    queryKey: ['report-template', id],
    queryFn: () => analyticsService.getReportTemplate(id),
    enabled: !!id,
    staleTime: 600000,
  })
}

export function useCreateReportTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (template: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>) =>
      analyticsService.createReportTemplate(template),
    onSuccess: (newTemplate) => {
      queryClient.invalidateQueries({ queryKey: ['report-templates'] })
      queryClient.setQueryData(['report-template', newTemplate.id], newTemplate)
    },
    onError: (error: any) => {
      console.error('Failed to create report template:', error)
    },
  })
}

export function useUpdateReportTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, template }: { id: string; template: Partial<ReportTemplate> }) =>
      analyticsService.updateReportTemplate(id, template),
    onSuccess: (updatedTemplate, { id }) => {
      queryClient.setQueryData(['report-template', id], updatedTemplate)
      queryClient.invalidateQueries({ queryKey: ['report-templates'] })
    },
    onError: (error: any) => {
      console.error('Failed to update report template:', error)
    },
  })
}

export function useDeleteReportTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => analyticsService.deleteReportTemplate(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: ['report-template', id] })
      queryClient.invalidateQueries({ queryKey: ['report-templates'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete report template:', error)
    },
  })
}

// ===== REPORT GENERATION HOOKS =====

export function useGenerateReport() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      templateId,
      campaignId,
      format = ReportFormat.PDF,
      filters
    }: {
      templateId: string
      campaignId: string
      format?: ReportFormat
      filters?: ReportFilter[]
    }) => analyticsService.generateReport(templateId, campaignId, format, filters),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['generated-reports'] })
    },
    onError: (error: any) => {
      console.error('Failed to generate report:', error)
    },
  })
}

export function useGeneratedReports(campaignId?: string) {
  return useQuery({
    queryKey: ['generated-reports', campaignId],
    queryFn: () => analyticsService.getGeneratedReports(campaignId),
    staleTime: 300000,
  })
}

export function useDownloadReport() {
  return useMutation({
    mutationFn: (reportId: string) => analyticsService.downloadReport(reportId),
    onError: (error: any) => {
      console.error('Failed to download report:', error)
    },
  })
}

export function useScheduleReport() {
  return useMutation({
    mutationFn: ({
      templateId,
      campaignId,
      schedule
    }: {
      templateId: string
      campaignId: string
      schedule: ReportSchedule
    }) => analyticsService.scheduleReport(templateId, campaignId, schedule),
    onError: (error: any) => {
      console.error('Failed to schedule report:', error)
    },
  })
}

// ===== CUSTOM QUERY HOOKS =====

export function useCustomQuery() {
  return useMutation({
    mutationFn: (query: AnalyticsQuery) => analyticsService.executeCustomQuery(query),
    onError: (error: any) => {
      console.error('Failed to execute custom query:', error)
    },
  })
}

export function useQuerySuggestions(campaignId: string) {
  return useQuery({
    queryKey: ['query-suggestions', campaignId],
    queryFn: () => analyticsService.getQuerySuggestions(campaignId),
    enabled: !!campaignId,
    staleTime: 600000,
  })
}

// ===== DATA EXPORT HOOKS =====

export function useExportAnalyticsData() {
  return useMutation({
    mutationFn: ({
      campaignId,
      format = ReportFormat.CSV,
      query
    }: {
      campaignId: string
      format?: ReportFormat
      query?: Partial<AnalyticsQuery>
    }) => analyticsService.exportAnalyticsData(campaignId, format, query),
    onError: (error: any) => {
      console.error('Failed to export analytics data:', error)
    },
  })
}

export function useExportChartData() {
  return useMutation({
    mutationFn: ({
      campaignId,
      chartConfig,
      format = ReportFormat.PNG
    }: {
      campaignId: string
      chartConfig: ChartConfig
      format?: ReportFormat
    }) => analyticsService.exportChartData(campaignId, chartConfig, format),
    onError: (error: any) => {
      console.error('Failed to export chart data:', error)
    },
  })
}

// ===== ALERTS HOOKS =====

export function useCreateAlert() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ campaignId, alert }: { campaignId: string; alert: any }) =>
      analyticsService.createAlert(campaignId, alert),
    onSuccess: (_, { campaignId }) => {
      queryClient.invalidateQueries({ queryKey: ['alerts', campaignId] })
    },
    onError: (error: any) => {
      console.error('Failed to create alert:', error)
    },
  })
}

export function useAlerts(campaignId: string) {
  return useQuery({
    queryKey: ['alerts', campaignId],
    queryFn: () => analyticsService.getAlerts(campaignId),
    enabled: !!campaignId,
    staleTime: 300000,
  })
}

export function useUpdateAlert() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ campaignId, alertId, updates }: { campaignId: string; alertId: string; updates: any }) =>
      analyticsService.updateAlert(campaignId, alertId, updates),
    onSuccess: (_, { campaignId }) => {
      queryClient.invalidateQueries({ queryKey: ['alerts', campaignId] })
    },
    onError: (error: any) => {
      console.error('Failed to update alert:', error)
    },
  })
}

export function useDeleteAlert() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ campaignId, alertId }: { campaignId: string; alertId: string }) =>
      analyticsService.deleteAlert(campaignId, alertId),
    onSuccess: (_, { campaignId }) => {
      queryClient.invalidateQueries({ queryKey: ['alerts', campaignId] })
    },
    onError: (error: any) => {
      console.error('Failed to delete alert:', error)
    },
  })
}

// ===== DASHBOARD HOOKS =====

export function useDashboardConfig(campaignId: string) {
  return useQuery({
    queryKey: ['dashboard-config', campaignId],
    queryFn: () => analyticsService.getDashboardConfig(campaignId),
    enabled: !!campaignId,
    staleTime: 600000,
  })
}

export function useUpdateDashboardConfig() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ campaignId, config }: { campaignId: string; config: any }) =>
      analyticsService.updateDashboardConfig(campaignId, config),
    onSuccess: (_, { campaignId }) => {
      queryClient.invalidateQueries({ queryKey: ['dashboard-config', campaignId] })
    },
    onError: (error: any) => {
      console.error('Failed to update dashboard config:', error)
    },
  })
}

// ===== SYSTEM HOOKS =====

export function useAnalyticsHealth() {
  return useQuery({
    queryKey: ['analytics-health'],
    queryFn: () => analyticsService.getAnalyticsHealth(),
    refetchInterval: 60000, // 1 minute
    staleTime: 30000,
  })
}

export function useRefreshAnalyticsCache() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (campaignId: string) => analyticsService.refreshAnalyticsCache(campaignId),
    onSuccess: (_, campaignId) => {
      // Invalidate all analytics queries for this campaign
      queryClient.invalidateQueries({ queryKey: ['campaign-analytics', campaignId] })
      queryClient.invalidateQueries({ queryKey: ['engagement-analytics', campaignId] })
      queryClient.invalidateQueries({ queryKey: ['conversion-analytics', campaignId] })
      queryClient.invalidateQueries({ queryKey: ['revenue-analytics', campaignId] })
      queryClient.invalidateQueries({ queryKey: ['social-analytics', campaignId] })
    },
    onError: (error: any) => {
      console.error('Failed to refresh analytics cache:', error)
    },
  })
}
