# 📚 Complete Implementation Examples

**Real-world examples following all 6 phases of enterprise standardization**

## 🎯 Overview

This document provides complete, working examples that demonstrate proper implementation of all enterprise standards across the Social NFT Platform.

---

## 🏗️ Complete Service Implementation

### **User Service - Full Implementation**

#### **1. Configuration Schema**
```typescript
// services/user-service/src/config/schemas/user.config.ts
import { IsString, IsNumber, IsBoolean, IsOptional, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';

export class UserConfig {
  @IsString()
  serviceName: string;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1000)
  @Max(65535)
  port: number;

  @IsString()
  jwtSecret: string;

  @IsString()
  @IsOptional()
  jwtExpiresIn?: string = '24h';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @IsOptional()
  maxLoginAttempts?: number = 5;

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  @IsOptional()
  enableEmailVerification?: boolean = true;
}
```

#### **2. User Entity Interface**
```typescript
// services/user-service/src/users/interfaces/user.interface.ts
export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  profileImage?: string;
  isActive: boolean;
  isEmailVerified: boolean;
  lastLoginAt?: Date;
  loginAttempts: number;
  roles: Role[];
  permissions: Permission[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserRequest {
  email: string;
  username: string;
  password: string;
  firstName: string;
  lastName: string;
  profileImage?: string;
}

export interface UpdateUserRequest {
  username?: string;
  firstName?: string;
  lastName?: string;
  profileImage?: string;
}

export interface UserLoginRequest {
  email: string;
  password: string;
}

export interface UserLoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
}
```

#### **3. User DTOs**
```typescript
// services/user-service/src/users/dto/user.dto.ts
import { IsEmail, IsString, IsOptional, MinLength, MaxLength, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUserDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: 'johndoe' })
  @IsString()
  @MinLength(3)
  @MaxLength(30)
  username: string;

  @ApiProperty({ example: 'SecurePassword123!' })
  @IsString()
  @MinLength(8)
  password: string;

  @ApiProperty({ example: 'John' })
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  firstName: string;

  @ApiProperty({ example: 'Doe' })
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  lastName: string;

  @ApiPropertyOptional({ example: 'https://example.com/profile.jpg' })
  @IsOptional()
  @IsString()
  profileImage?: string;
}

export class UpdateUserDto {
  @ApiPropertyOptional({ example: 'johndoe_updated' })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(30)
  username?: string;

  @ApiPropertyOptional({ example: 'John Updated' })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  firstName?: string;

  @ApiPropertyOptional({ example: 'Doe Updated' })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  lastName?: string;

  @ApiPropertyOptional({ example: 'https://example.com/new-profile.jpg' })
  @IsOptional()
  @IsString()
  profileImage?: string;
}

export class UserDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  username: string;

  @ApiProperty()
  firstName: string;

  @ApiProperty()
  lastName: string;

  @ApiPropertyOptional()
  profileImage?: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  isEmailVerified: boolean;

  @ApiPropertyOptional()
  lastLoginAt?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class LoginDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: 'SecurePassword123!' })
  @IsString()
  @MinLength(8)
  password: string;
}

export class LoginResponseDto {
  @ApiProperty()
  user: UserDto;

  @ApiProperty()
  accessToken: string;

  @ApiProperty()
  refreshToken: string;

  @ApiProperty()
  expiresIn: string;
}
```

#### **4. User Repository**
```typescript
// services/user-service/src/users/repositories/user.repository.ts
import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../shared/data/repositories/base.repository';
import { StandardizedPrismaService } from '../../../../shared/data/services/prisma.service';
import { ServiceLoggerService } from '../../logging/services/service-logger.service';
import { MetricsService } from '../../../../shared/logging/services/metrics.service';
import { ConfigService } from '@nestjs/config';
import { User } from '../interfaces/user.interface';
import { CreateUserDto, UpdateUserDto } from '../dto/user.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UserRepository extends BaseRepository<User, CreateUserDto, UpdateUserDto> {
  constructor(
    prisma: StandardizedPrismaService,
    serviceLogger: ServiceLoggerService,
    metricsService: MetricsService,
    configService: ConfigService,
  ) {
    super(prisma, serviceLogger, metricsService, configService, 'User');
  }

  protected getModel() {
    return this.prisma.user;
  }

  protected toDto(entity: any): User {
    return {
      id: entity.id,
      email: entity.email,
      username: entity.username,
      firstName: entity.firstName,
      lastName: entity.lastName,
      profileImage: entity.profileImage,
      isActive: entity.isActive,
      isEmailVerified: entity.isEmailVerified,
      lastLoginAt: entity.lastLoginAt,
      loginAttempts: entity.loginAttempts,
      roles: entity.roles || [],
      permissions: entity.permissions || [],
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  protected toCreateData(dto: CreateUserDto): any {
    return {
      email: dto.email,
      username: dto.username,
      firstName: dto.firstName,
      lastName: dto.lastName,
      profileImage: dto.profileImage,
      isActive: true,
      isEmailVerified: false,
      loginAttempts: 0,
    };
  }

  protected toUpdateData(dto: UpdateUserDto): any {
    const data: any = {};
    if (dto.username !== undefined) data.username = dto.username;
    if (dto.firstName !== undefined) data.firstName = dto.firstName;
    if (dto.lastName !== undefined) data.lastName = dto.lastName;
    if (dto.profileImage !== undefined) data.profileImage = dto.profileImage;
    return data;
  }

  // Custom repository methods
  async findByEmail(email: string): Promise<User | null> {
    return this.findOne({
      where: { email },
      include: { roles: true, permissions: true },
    });
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.findOne({
      where: { username },
      include: { roles: true, permissions: true },
    });
  }

  async findActiveUsers(): Promise<User[]> {
    return this.findMany({
      where: { isActive: true },
      include: { roles: true, permissions: true },
    });
  }

  async updateLoginAttempts(userId: string, attempts: number): Promise<void> {
    await this.update(userId, { loginAttempts: attempts } as any);
  }

  async updateLastLogin(userId: string): Promise<void> {
    await this.getModel().update({
      where: { id: userId },
      data: {
        lastLoginAt: new Date(),
        loginAttempts: 0,
      },
    });
  }

  async verifyEmail(userId: string): Promise<void> {
    await this.update(userId, { isEmailVerified: true } as any);
  }

  async deactivateUser(userId: string): Promise<void> {
    await this.update(userId, { isActive: false } as any);
  }

  async createWithHashedPassword(dto: CreateUserDto & { hashedPassword: string }): Promise<User> {
    const createData = {
      ...this.toCreateData(dto),
      password: dto.hashedPassword,
    };

    const result = await this.getModel().create({
      data: createData,
      include: { roles: true, permissions: true },
    });

    return this.toDto(result);
  }
}
```

#### **5. User Service**
```typescript
// services/user-service/src/users/services/user.service.ts
import { Injectable, ConflictException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServiceLoggerService } from '../../logging/services/service-logger.service';
import { UserRepository } from '../repositories/user.repository';
import { JwtService } from '../../../shared/auth/services/jwt.service';
import { BusinessOutcome, SecurityEventType, SecuritySeverity, SecurityOutcome } from '../../../../shared/logging/interfaces/logger.interface';
import { CreateUserDto, UpdateUserDto, LoginDto } from '../dto/user.dto';
import { User, UserLoginResponse } from '../interfaces/user.interface';
import { PaginationOptions, PaginatedResult } from '../../../../shared/data/interfaces/repository.interface';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly serviceLogger: ServiceLoggerService,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    try {
      this.serviceLogger.logOperationStart('user_creation', {
        metadata: { email: createUserDto.email, username: createUserDto.username }
      });

      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(createUserDto.email);
      if (existingUser) {
        throw new ConflictException('User with this email already exists');
      }

      const existingUsername = await this.userRepository.findByUsername(createUserDto.username);
      if (existingUsername) {
        throw new ConflictException('Username already taken');
      }

      // Hash password
      const saltRounds = this.configService.get<number>('BCRYPT_ROUNDS') || 12;
      const hashedPassword = await bcrypt.hash(createUserDto.password, saltRounds);

      // Create user
      const user = await this.userRepository.createWithHashedPassword({
        ...createUserDto,
        hashedPassword,
      });

      this.serviceLogger.logBusinessEvent(
        'user',
        'registration',
        BusinessOutcome.SUCCESS,
        { userId: user.id, email: user.email }
      );

      this.serviceLogger.logSecurityEvent(
        SecurityEventType.USER_REGISTRATION,
        SecuritySeverity.LOW,
        SecurityOutcome.SUCCESS,
        { userId: user.id, email: user.email }
      );

      return user;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        'user',
        'registration',
        BusinessOutcome.FAILURE,
        { error: error.message, email: createUserDto.email }
      );

      this.serviceLogger.logSecurityEvent(
        SecurityEventType.USER_REGISTRATION,
        SecuritySeverity.MEDIUM,
        SecurityOutcome.FAILURE,
        { error: error.message, email: createUserDto.email }
      );

      throw error;
    }
  }

  async login(loginDto: LoginDto): Promise<UserLoginResponse> {
    try {
      this.serviceLogger.logOperationStart('user_login', {
        metadata: { email: loginDto.email }
      });

      // Find user
      const user = await this.userRepository.findByEmail(loginDto.email);
      if (!user) {
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check if user is active
      if (!user.isActive) {
        throw new UnauthorizedException('Account is deactivated');
      }

      // Check login attempts
      const maxAttempts = this.configService.get<number>('MAX_LOGIN_ATTEMPTS') || 5;
      if (user.loginAttempts >= maxAttempts) {
        throw new UnauthorizedException('Account locked due to too many failed attempts');
      }

      // Verify password
      const userWithPassword = await this.userRepository.getModel().findUnique({
        where: { id: user.id },
        select: { password: true },
      });

      const isPasswordValid = await bcrypt.compare(loginDto.password, userWithPassword.password);
      if (!isPasswordValid) {
        // Increment login attempts
        await this.userRepository.updateLoginAttempts(user.id, user.loginAttempts + 1);

        this.serviceLogger.logSecurityEvent(
          SecurityEventType.AUTHENTICATION,
          SecuritySeverity.MEDIUM,
          SecurityOutcome.FAILURE,
          { userId: user.id, email: user.email, reason: 'invalid_password' }
        );

        throw new UnauthorizedException('Invalid credentials');
      }

      // Generate tokens
      const tokens = await this.jwtService.generateTokens({
        userId: user.id,
        email: user.email,
        roles: user.roles,
        permissions: user.permissions,
      });

      // Update last login
      await this.userRepository.updateLastLogin(user.id);

      this.serviceLogger.logBusinessEvent(
        'user',
        'login',
        BusinessOutcome.SUCCESS,
        { userId: user.id, email: user.email }
      );

      this.serviceLogger.logSecurityEvent(
        SecurityEventType.AUTHENTICATION,
        SecuritySeverity.LOW,
        SecurityOutcome.SUCCESS,
        { userId: user.id, email: user.email }
      );

      return {
        user,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn,
      };
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        'user',
        'login',
        BusinessOutcome.FAILURE,
        { error: error.message, email: loginDto.email }
      );

      throw error;
    }
  }

  async findAll(pagination: PaginationOptions, filters?: any, sorting?: any): Promise<PaginatedResult<User>> {
    try {
      this.serviceLogger.logOperationStart('user_list_retrieval');

      const result = await this.userRepository.paginate(
        { where: filters },
        pagination
      );

      this.serviceLogger.logBusinessEvent(
        'user',
        'list_retrieval',
        BusinessOutcome.SUCCESS,
        { count: result.data.length, page: pagination.page }
      );

      return result;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        'user',
        'list_retrieval',
        BusinessOutcome.FAILURE,
        { error: error.message }
      );
      throw error;
    }
  }

  async findById(id: string): Promise<User | null> {
    try {
      this.serviceLogger.logOperationStart('user_retrieval', {
        metadata: { userId: id }
      });

      const user = await this.userRepository.findById(id);

      if (user) {
        this.serviceLogger.logBusinessEvent(
          'user',
          'retrieval',
          BusinessOutcome.SUCCESS,
          { userId: id }
        );
      }

      return user;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        'user',
        'retrieval',
        BusinessOutcome.FAILURE,
        { error: error.message, userId: id }
      );
      throw error;
    }
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    try {
      this.serviceLogger.logOperationStart('user_update', {
        metadata: { userId: id, ...updateUserDto }
      });

      // Check if user exists
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        throw new NotFoundException('User not found');
      }

      // Check username uniqueness if updating
      if (updateUserDto.username && updateUserDto.username !== existingUser.username) {
        const existingUsername = await this.userRepository.findByUsername(updateUserDto.username);
        if (existingUsername) {
          throw new ConflictException('Username already taken');
        }
      }

      const user = await this.userRepository.update(id, updateUserDto);

      this.serviceLogger.logBusinessEvent(
        'user',
        'update',
        BusinessOutcome.SUCCESS,
        { userId: id }
      );

      return user;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        'user',
        'update',
        BusinessOutcome.FAILURE,
        { error: error.message, userId: id }
      );
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      this.serviceLogger.logOperationStart('user_deletion', {
        metadata: { userId: id }
      });

      const result = await this.userRepository.delete(id);

      if (result) {
        this.serviceLogger.logBusinessEvent(
          'user',
          'deletion',
          BusinessOutcome.SUCCESS,
          { userId: id }
        );

        this.serviceLogger.logSecurityEvent(
          SecurityEventType.USER_DELETION,
          SecuritySeverity.MEDIUM,
          SecurityOutcome.SUCCESS,
          { userId: id }
        );
      }

      return result;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        'user',
        'deletion',
        BusinessOutcome.FAILURE,
        { error: error.message, userId: id }
      );
      throw error;
    }
  }

  async verifyEmail(userId: string): Promise<void> {
    try {
      await this.userRepository.verifyEmail(userId);

      this.serviceLogger.logBusinessEvent(
        'user',
        'email_verification',
        BusinessOutcome.SUCCESS,
        { userId }
      );

      this.serviceLogger.logSecurityEvent(
        SecurityEventType.EMAIL_VERIFICATION,
        SecuritySeverity.LOW,
        SecurityOutcome.SUCCESS,
        { userId }
      );
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        'user',
        'email_verification',
        BusinessOutcome.FAILURE,
        { error: error.message, userId }
      );
      throw error;
    }
  }

  async deactivateUser(userId: string): Promise<void> {
    try {
      await this.userRepository.deactivateUser(userId);

      this.serviceLogger.logBusinessEvent(
        'user',
        'deactivation',
        BusinessOutcome.SUCCESS,
        { userId }
      );

      this.serviceLogger.logSecurityEvent(
        SecurityEventType.USER_DEACTIVATION,
        SecuritySeverity.MEDIUM,
        SecurityOutcome.SUCCESS,
        { userId }
      );
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        'user',
        'deactivation',
        BusinessOutcome.FAILURE,
        { error: error.message, userId }
      );
      throw error;
    }
  }
}
```

#### **6. User Controller**
```typescript
// services/user-service/src/users/controllers/user.controller.ts
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../../shared/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../../shared/auth/guards/roles.guard';
import { PermissionsGuard } from '../../../../shared/auth/guards/permissions.guard';
import { RequirePermissions, RequireRoles } from '../../../../shared/auth/decorators/auth.decorator';
import { Permission } from '../../../../shared/auth/interfaces/permission.interface';
import { Role } from '../../../../shared/auth/interfaces/role.interface';
import {
  ApiCreateResponse,
  ApiReadResponse,
  ApiUpdateResponse,
  ApiDeleteResponse,
  ApiListResponse
} from '../../../../shared/responses/decorators/api-response.decorator';
import { ResponseService } from '../../responses/services/response.service';
import { PaginationService } from '../../responses/services/pagination.service';
import { UserService } from '../services/user.service';
import { CreateUserDto, UpdateUserDto, UserDto, LoginDto, LoginResponseDto } from '../dto/user.dto';

@Controller('users')
@ApiTags('Users')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly responseService: ResponseService,
    private readonly paginationService: PaginationService,
  ) {}

  @Post('register')
  @ApiOperation({ summary: 'Register new user' })
  @ApiCreateResponse(UserDto, 'User registered successfully')
  @HttpCode(HttpStatus.CREATED)
  async register(@Body() createUserDto: CreateUserDto) {
    const user = await this.userService.create(createUserDto);
    return this.responseService.created(user, 'User registered successfully');
  }

  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiCreateResponse(LoginResponseDto, 'Login successful')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: LoginDto) {
    const result = await this.userService.login(loginDto);
    return this.responseService.success(result, 'Login successful');
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @RequirePermissions(Permission.USER_READ)
  @RequireRoles(Role.ADMIN, Role.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get users list with pagination' })
  @ApiListResponse(UserDto, 'Users retrieved successfully')
  async findAll(@Query() query: any) {
    const { pagination, filters, sorting } = this.paginationService.parseQuery(query);
    const result = await this.userService.findAll(pagination, filters, sorting);

    return this.responseService.paginated(
      result.data,
      result.pagination,
      {
        filters,
        sorting,
        message: 'Users retrieved successfully'
      }
    );
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @RequirePermissions(Permission.USER_READ)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiReadResponse(UserDto, 'User retrieved successfully')
  async findOne(@Param('id') id: string) {
    const user = await this.userService.findById(id);

    if (!user) {
      return this.responseService.notFound('User', id);
    }

    return this.responseService.success(user, 'User retrieved successfully');
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @RequirePermissions(Permission.USER_WRITE)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user' })
  @ApiUpdateResponse(UserDto, 'User updated successfully')
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto
  ) {
    const user = await this.userService.update(id, updateUserDto);
    return this.responseService.updated(user, 'User updated successfully');
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @RequirePermissions(Permission.USER_DELETE)
  @RequireRoles(Role.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete user' })
  @ApiDeleteResponse('User deleted successfully')
  async remove(@Param('id') id: string) {
    const result = await this.userService.delete(id);

    if (!result) {
      return this.responseService.notFound('User', id);
    }

    return this.responseService.deleted('User deleted successfully');
  }

  @Post(':id/verify-email')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @RequirePermissions(Permission.USER_WRITE)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Verify user email' })
  @HttpCode(HttpStatus.OK)
  async verifyEmail(@Param('id') id: string) {
    await this.userService.verifyEmail(id);
    return this.responseService.success(null, 'Email verified successfully');
  }

  @Post(':id/deactivate')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @RequirePermissions(Permission.USER_DELETE)
  @RequireRoles(Role.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Deactivate user' })
  @HttpCode(HttpStatus.OK)
  async deactivate(@Param('id') id: string) {
    await this.userService.deactivateUser(id);
    return this.responseService.success(null, 'User deactivated successfully');
  }
}
```

#### **7. User Module**
```typescript
// services/user-service/src/users/user.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Shared modules
import { ServiceAuthModule } from '../auth/auth.module';
import { ServiceResponseModule } from '../responses/responses.module';
import { ServiceLoggingModule } from '../logging/logging.module';
import { ServiceDataModule } from '../data/data.module';

// User components
import { UserController } from './controllers/user.controller';
import { UserService } from './services/user.service';
import { UserRepository } from './repositories/user.repository';

@Module({
  imports: [
    ConfigModule,
    ServiceAuthModule,
    ServiceResponseModule,
    ServiceLoggingModule,
    ServiceDataModule,
  ],
  controllers: [UserController],
  providers: [
    UserService,
    UserRepository,
    {
      provide: 'USER_SERVICE',
      useClass: UserService,
    },
    {
      provide: 'USER_REPOSITORY',
      useClass: UserRepository,
    },
  ],
  exports: [
    UserService,
    UserRepository,
    'USER_SERVICE',
    'USER_REPOSITORY',
  ],
})
export class UserModule {}
```
```