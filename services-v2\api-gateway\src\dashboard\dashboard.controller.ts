import { Controller, Get } from '@nestjs/common';
import { 
  ResponseService, 
  RequirePermissions, 
  Permission, 
  Public 
} from '../../shared';
import { ServiceDiscoveryService } from '../service-discovery/service-discovery.service';
import { CircuitBreakerService } from '../circuit-breaker/circuit-breaker.service';
import { LoadBalancerService } from '../load-balancer/load-balancer.service';
import { CacheService } from '../cache/cache.service';
import { RateLimitService } from '../rate-limit/rate-limit.service';
import { ProxyV2Service } from '../proxy-v2/proxy-v2.service';

/**
 * Dashboard Controller
 * 
 * Provides comprehensive overview of all API Gateway enterprise features.
 * Aggregates data from all subsystems for monitoring and management.
 */
@Controller('dashboard')
export class DashboardController {
  constructor(
    private readonly responseService: ResponseService,
    private readonly serviceDiscovery: ServiceDiscoveryService,
    private readonly circuitBreaker: CircuitBreakerService,
    private readonly loadBalancer: LoadBalancerService,
    private readonly cache: CacheService,
    private readonly rateLimit: RateLimitService,
    private readonly proxy: ProxyV2Service
  ) {}

  /**
   * Get comprehensive dashboard overview
   */
  @Get('overview')
  @RequirePermissions(Permission.ADMIN)
  async getOverview() {
    const [
      services,
      circuits,
      loadBalancerStats,
      cacheStats,
      rateLimitStats,
      proxyStats,
    ] = await Promise.all([
      this.getServiceDiscoveryData(),
      this.getCircuitBreakerData(),
      this.loadBalancer.getHealthStatus(),
      this.cache.getStats(),
      this.rateLimit.getStats(),
      this.proxy.getProxyStats(),
    ]);

    const overview = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      summary: {
        totalServices: services.totalServices,
        healthyServices: services.healthyServices,
        totalInstances: services.totalInstances,
        healthyInstances: services.healthyInstances,
        openCircuits: circuits.openCircuits,
        cacheHitRate: cacheStats.hitRate.toFixed(2) + '%',
        blockedRequests: rateLimitStats.blockedEntries,
      },
      services: services.details,
      circuitBreakers: circuits.details,
      loadBalancer: loadBalancerStats,
      cache: {
        ...cacheStats,
        hitRate: cacheStats.hitRate.toFixed(2) + '%',
      },
      rateLimit: rateLimitStats,
      proxy: proxyStats,
    };

    return this.responseService.success(overview, 'Dashboard overview retrieved');
  }

  /**
   * Get service health summary
   */
  @Get('health')
  @Public()
  async getHealthSummary() {
    const [
      serviceHealth,
      circuitHealth,
      cacheHealth,
      rateLimitHealth,
    ] = await Promise.all([
      this.serviceDiscovery.getAllServices(),
      this.circuitBreaker.getAllCircuitStats(),
      this.cache.getHealthStatus(),
      this.rateLimit.getHealthStatus(),
    ]);

    // Calculate overall health
    const totalServices = serviceHealth.size;
    const healthyServices = Array.from(serviceHealth.values())
      .filter(instances => instances.some(i => i.health === 'healthy')).length;
    
    const openCircuits = Array.from(circuitHealth.values())
      .filter(circuit => circuit.state === 'open').length;

    const overallHealth = {
      status: 'healthy',
      components: {
        serviceDiscovery: {
          status: healthyServices === totalServices ? 'healthy' : 'degraded',
          healthyServices,
          totalServices,
        },
        circuitBreakers: {
          status: openCircuits === 0 ? 'healthy' : 'degraded',
          openCircuits,
          totalCircuits: circuitHealth.size,
        },
        cache: {
          status: cacheHealth.status,
          memoryUsage: cacheHealth.memoryUsage.usagePercent,
        },
        rateLimit: {
          status: rateLimitHealth.status,
          blockRate: rateLimitHealth.blockRate,
        },
      },
      timestamp: new Date().toISOString(),
    };

    // Determine overall status
    const componentStatuses = Object.values(overallHealth.components).map(c => c.status);
    if (componentStatuses.includes('unhealthy')) {
      overallHealth.status = 'unhealthy';
    } else if (componentStatuses.includes('degraded')) {
      overallHealth.status = 'degraded';
    }

    return this.responseService.success(overallHealth, 'Health summary retrieved');
  }

  /**
   * Get performance metrics
   */
  @Get('metrics')
  @RequirePermissions(Permission.ADMIN)
  async getMetrics() {
    const circuits = this.circuitBreaker.getAllCircuitStats();
    const cacheStats = this.cache.getStats();
    const rateLimitStats = this.rateLimit.getStats();

    const metrics = {
      timestamp: new Date().toISOString(),
      performance: {
        totalRequests: Array.from(circuits.values())
          .reduce((sum, circuit) => sum + circuit.totalRequests, 0),
        totalFailures: Array.from(circuits.values())
          .reduce((sum, circuit) => sum + circuit.totalFailures, 0),
        successRate: this.calculateSuccessRate(circuits),
      },
      cache: {
        hitRate: cacheStats.hitRate,
        totalEntries: cacheStats.totalEntries,
        totalSize: cacheStats.totalSize,
        hitCount: cacheStats.hitCount,
        missCount: cacheStats.missCount,
      },
      rateLimit: {
        totalEntries: rateLimitStats.totalEntries,
        blockedEntries: rateLimitStats.blockedEntries,
        blockRate: rateLimitStats.totalEntries > 0 ? 
          (rateLimitStats.blockedEntries / rateLimitStats.totalEntries) * 100 : 0,
      },
    };

    return this.responseService.success(metrics, 'Performance metrics retrieved');
  }

  /**
   * Get system alerts
   */
  @Get('alerts')
  @RequirePermissions(Permission.ADMIN)
  async getAlerts() {
    const alerts = [];

    // Check for open circuits
    const circuits = this.circuitBreaker.getAllCircuitStats();
    for (const [service, circuit] of circuits.entries()) {
      if (circuit.state === 'open') {
        alerts.push({
          type: 'circuit_breaker',
          severity: 'high',
          service,
          message: `Circuit breaker is OPEN for ${service}`,
          timestamp: circuit.lastFailureTime,
          details: {
            failureCount: circuit.failureCount,
            nextAttemptTime: circuit.nextAttemptTime,
          },
        });
      }
    }

    // Check for unhealthy services
    const services = this.serviceDiscovery.getAllServices();
    for (const [serviceName, instances] of services.entries()) {
      const healthyCount = instances.filter(i => i.health === 'healthy').length;
      if (healthyCount === 0) {
        alerts.push({
          type: 'service_health',
          severity: 'critical',
          service: serviceName,
          message: `No healthy instances available for ${serviceName}`,
          timestamp: new Date(),
          details: {
            totalInstances: instances.length,
            healthyInstances: healthyCount,
          },
        });
      } else if (healthyCount < instances.length) {
        alerts.push({
          type: 'service_health',
          severity: 'medium',
          service: serviceName,
          message: `Some instances unhealthy for ${serviceName}`,
          timestamp: new Date(),
          details: {
            totalInstances: instances.length,
            healthyInstances: healthyCount,
          },
        });
      }
    }

    // Check cache memory usage
    const cacheHealth = this.cache.getHealthStatus();
    const memoryUsage = parseFloat(cacheHealth.memoryUsage.usagePercent);
    if (memoryUsage > 90) {
      alerts.push({
        type: 'cache_memory',
        severity: 'high',
        service: 'cache',
        message: `Cache memory usage is high: ${cacheHealth.memoryUsage.usagePercent}`,
        timestamp: new Date(),
        details: {
          used: cacheHealth.memoryUsage.used,
          max: cacheHealth.memoryUsage.max,
          usagePercent: cacheHealth.memoryUsage.usagePercent,
        },
      });
    }

    return this.responseService.success({
      alerts,
      totalAlerts: alerts.length,
      criticalAlerts: alerts.filter(a => a.severity === 'critical').length,
      highAlerts: alerts.filter(a => a.severity === 'high').length,
      mediumAlerts: alerts.filter(a => a.severity === 'medium').length,
      timestamp: new Date().toISOString(),
    }, 'System alerts retrieved');
  }

  private async getServiceDiscoveryData() {
    const services = this.serviceDiscovery.getAllServices();
    let totalInstances = 0;
    let healthyInstances = 0;
    const details = [];

    for (const [serviceName, instances] of services.entries()) {
      totalInstances += instances.length;
      const healthy = instances.filter(i => i.health === 'healthy').length;
      healthyInstances += healthy;

      details.push({
        name: serviceName,
        totalInstances: instances.length,
        healthyInstances: healthy,
        status: healthy > 0 ? 'healthy' : 'unhealthy',
        instances: instances.map(i => ({
          id: i.id,
          url: i.url,
          health: i.health,
          lastHealthCheck: i.lastHealthCheck,
          version: i.metadata.version,
        })),
      });
    }

    return {
      totalServices: services.size,
      healthyServices: details.filter(s => s.status === 'healthy').length,
      totalInstances,
      healthyInstances,
      details,
    };
  }

  private getCircuitBreakerData() {
    const circuits = this.circuitBreaker.getAllCircuitStats();
    const details = [];
    let openCircuits = 0;

    for (const [service, circuit] of circuits.entries()) {
      if (circuit.state === 'open') {
        openCircuits++;
      }

      details.push({
        service,
        state: circuit.state,
        failureCount: circuit.failureCount,
        successCount: circuit.successCount,
        totalRequests: circuit.totalRequests,
        successRate: circuit.totalRequests > 0 ? 
          ((circuit.totalSuccesses / circuit.totalRequests) * 100).toFixed(2) + '%' : '0%',
        lastFailureTime: circuit.lastFailureTime,
        nextAttemptTime: circuit.nextAttemptTime,
      });
    }

    return {
      totalCircuits: circuits.size,
      openCircuits,
      closedCircuits: details.filter(c => c.state === 'closed').length,
      halfOpenCircuits: details.filter(c => c.state === 'half-open').length,
      details,
    };
  }

  private calculateSuccessRate(circuits: Map<string, any>): string {
    const totals = Array.from(circuits.values()).reduce(
      (acc, circuit) => ({
        requests: acc.requests + circuit.totalRequests,
        successes: acc.successes + circuit.totalSuccesses,
      }),
      { requests: 0, successes: 0 }
    );

    return totals.requests > 0 ? 
      ((totals.successes / totals.requests) * 100).toFixed(2) + '%' : '0%';
  }
}
