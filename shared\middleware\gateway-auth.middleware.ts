import { Injectable, NestMiddleware, ForbiddenException, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

/**
 * Gateway Authentication Middleware
 * Ensures requests only come through the API Gateway
 */
@Injectable()
export class GatewayAuthMiddleware implements NestMiddleware {
  private readonly logger = new Logger(GatewayAuthMiddleware.name);
  private readonly gatewaySecret: string;
  private readonly allowDirectAccess: boolean;
  private readonly trustedIPs: string[];

  constructor(private readonly configService: ConfigService) {
    this.gatewaySecret = this.configService.get<string>('GATEWAY_SECRET') || 'default-gateway-secret-change-in-production';
    this.allowDirectAccess = this.configService.get<string>('ALLOW_DIRECT_ACCESS') === 'true';
    this.trustedIPs = (this.configService.get<string>('TRUSTED_IPS') || '127.0.0.1,::1').split(',');
  }

  use(req: Request, res: Response, next: NextFunction) {
    // Skip validation in development if explicitly allowed
    if (this.allowDirectAccess && process.env.NODE_ENV === 'development') {
      this.logger.debug('Direct access allowed in development mode');
      return next();
    }

    // Check for gateway authentication header
    const gatewayAuth = req.headers['x-gateway-auth'] as string;
    const forwardedBy = req.headers['x-forwarded-by'] as string;
    const clientIP = this.getClientIP(req);

    // Validate gateway authentication
    if (!this.isValidGatewayRequest(gatewayAuth, forwardedBy, clientIP)) {
      this.logger.warn(`Unauthorized direct access attempt from ${clientIP} to ${req.path}`, {
        headers: {
          'x-gateway-auth': gatewayAuth ? '[PRESENT]' : '[MISSING]',
          'x-forwarded-by': forwardedBy,
          'user-agent': req.headers['user-agent'],
        },
        ip: clientIP,
        path: req.path,
        method: req.method,
      });

      throw new ForbiddenException({
        message: 'Direct service access is not allowed. Please use the API Gateway.',
        error: 'DIRECT_ACCESS_FORBIDDEN',
        gateway: 'http://localhost:3010',
        service: this.getServiceName(),
        timestamp: new Date().toISOString(),
      });
    }

    // Log successful gateway request
    this.logger.debug(`Valid gateway request from ${clientIP} to ${req.path}`);
    next();
  }

  private isValidGatewayRequest(gatewayAuth: string, forwardedBy: string, clientIP: string): boolean {
    // Check 1: Gateway authentication header
    if (gatewayAuth !== this.gatewaySecret) {
      return false;
    }

    // Check 2: Forwarded by API Gateway
    if (forwardedBy !== 'api-gateway') {
      return false;
    }

    // Check 3: Request from trusted IP (localhost in development)
    if (!this.trustedIPs.includes(clientIP)) {
      return false;
    }

    return true;
  }

  private getServiceName(): string {
    // Try to determine service name from environment or package.json
    return process.env.SERVICE_NAME || 'unknown-service';
  }

  private getClientIP(req: Request): string {
    return (
      req.headers['x-forwarded-for'] as string ||
      req.headers['x-real-ip'] as string ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      '127.0.0.1'
    );
  }
}

/**
 * Health Check Middleware
 * Allows health checks to bypass gateway authentication
 */
@Injectable()
export class HealthCheckMiddleware implements NestMiddleware {
  private readonly logger = new Logger(HealthCheckMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    // Allow health checks to bypass gateway authentication
    if (req.path === '/api/health' || req.path === '/health') {
      this.logger.debug(`Health check request allowed: ${req.path}`);
      return next();
    }

    next();
  }
}
