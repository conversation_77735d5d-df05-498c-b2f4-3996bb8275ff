'use client'

import React, { useState } from 'react'
import {
  BoltIcon,
  LightBulbIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  PauseIcon,
  InformationCircleIcon,
  CubeIcon,
  ServerIcon,
  GlobeAltIcon,
  CodeBracketIcon,
  CogIcon
} from '@heroicons/react/24/outline'
import {
  useOptimizationRecommendations,
  useCreateOptimizationRecommendation,
  useUpdateOptimizationRecommendation,
  useImplementOptimization
} from '@/hooks/usePerformance'
import {
  OptimizationRecommendation,
  OptimizationType,
  RecommendationPriority,
  RecommendationStatus,
  ImplementationComplexity
} from '@/types/performance.types'

interface OptimizationRecommendationsProps {
  className?: string
}

export default function OptimizationRecommendations({
  className = ''
}: OptimizationRecommendationsProps) {
  const [selectedType, setSelectedType] = useState<OptimizationType | 'all'>('all')
  const [selectedPriority, setSelectedPriority] = useState<RecommendationPriority | 'all'>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedRecommendation, setSelectedRecommendation] = useState<OptimizationRecommendation | null>(null)

  const { data: recommendations = [], isLoading } = useOptimizationRecommendations(
    selectedType === 'all' ? undefined : selectedType
  )
  const createRecommendationMutation = useCreateOptimizationRecommendation()
  const updateRecommendationMutation = useUpdateOptimizationRecommendation()
  const implementOptimizationMutation = useImplementOptimization()

  // Mock recommendations if none provided
  const mockRecommendations: OptimizationRecommendation[] = [
    {
      id: '1',
      type: OptimizationType.CACHING,
      priority: RecommendationPriority.HIGH,
      title: 'Implement Redis Caching for NFT Metadata',
      description: 'Cache frequently accessed NFT metadata to reduce database load and improve response times by up to 60%.',
      impact: {
        performance: 85,
        cost: 20,
        reliability: 75,
        scalability: 90,
        userExperience: 80
      },
      implementation: {
        steps: [
          {
            order: 1,
            title: 'Set up Redis cluster',
            description: 'Deploy Redis cluster with high availability configuration',
            estimatedTime: '2 hours',
            resources: ['DevOps Engineer', 'Backend Developer']
          },
          {
            order: 2,
            title: 'Implement caching layer',
            description: 'Add caching middleware to NFT metadata endpoints',
            estimatedTime: '4 hours',
            resources: ['Backend Developer']
          },
          {
            order: 3,
            title: 'Configure cache invalidation',
            description: 'Set up cache invalidation rules for data consistency',
            estimatedTime: '2 hours',
            resources: ['Backend Developer']
          }
        ],
        prerequisites: ['Redis infrastructure', 'Monitoring setup'],
        risks: ['Cache invalidation complexity', 'Memory usage increase'],
        rollbackPlan: 'Disable caching middleware and fall back to direct database queries',
        testing: ['Load testing', 'Cache hit rate validation', 'Data consistency checks']
      },
      metrics: ['response_time', 'database_load', 'cache_hit_rate'],
      estimatedSavings: {
        cpu: 30,
        memory: -10,
        storage: 0,
        network: 15,
        cost: 25,
        responseTime: 60
      },
      complexity: ImplementationComplexity.MEDIUM,
      timeline: '1 week',
      status: RecommendationStatus.PENDING,
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '2',
      type: OptimizationType.DATABASE,
      priority: RecommendationPriority.CRITICAL,
      title: 'Optimize Database Queries with Indexing',
      description: 'Add composite indexes to frequently queried NFT tables to reduce query execution time by 70%.',
      impact: {
        performance: 95,
        cost: 10,
        reliability: 85,
        scalability: 80,
        userExperience: 90
      },
      implementation: {
        steps: [
          {
            order: 1,
            title: 'Analyze slow queries',
            description: 'Identify and analyze the slowest database queries',
            estimatedTime: '1 hour',
            resources: ['Database Administrator']
          },
          {
            order: 2,
            title: 'Design optimal indexes',
            description: 'Create composite indexes for query optimization',
            estimatedTime: '2 hours',
            resources: ['Database Administrator', 'Backend Developer']
          },
          {
            order: 3,
            title: 'Deploy indexes',
            description: 'Deploy indexes during maintenance window',
            estimatedTime: '30 minutes',
            resources: ['Database Administrator']
          }
        ],
        prerequisites: ['Database maintenance window', 'Query analysis'],
        risks: ['Index maintenance overhead', 'Storage increase'],
        rollbackPlan: 'Drop newly created indexes if performance degrades',
        testing: ['Query performance testing', 'Index usage validation']
      },
      metrics: ['query_time', 'database_cpu', 'index_hit_ratio'],
      estimatedSavings: {
        cpu: 40,
        memory: 5,
        storage: -5,
        network: 0,
        cost: 15,
        responseTime: 70
      },
      complexity: ImplementationComplexity.LOW,
      timeline: '3 days',
      status: RecommendationStatus.IN_PROGRESS,
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '3',
      type: OptimizationType.FRONTEND,
      priority: RecommendationPriority.MEDIUM,
      title: 'Implement Image Lazy Loading',
      description: 'Add lazy loading for NFT images to improve initial page load times and reduce bandwidth usage.',
      impact: {
        performance: 70,
        cost: 30,
        reliability: 60,
        scalability: 75,
        userExperience: 85
      },
      implementation: {
        steps: [
          {
            order: 1,
            title: 'Install lazy loading library',
            description: 'Add and configure react-lazyload or similar library',
            estimatedTime: '1 hour',
            resources: ['Frontend Developer']
          },
          {
            order: 2,
            title: 'Update image components',
            description: 'Wrap NFT image components with lazy loading',
            estimatedTime: '3 hours',
            resources: ['Frontend Developer']
          },
          {
            order: 3,
            title: 'Add loading placeholders',
            description: 'Implement skeleton loading states',
            estimatedTime: '2 hours',
            resources: ['Frontend Developer', 'UI/UX Designer']
          }
        ],
        prerequisites: ['Component audit', 'Design system update'],
        risks: ['SEO impact', 'User experience changes'],
        rollbackPlan: 'Remove lazy loading and revert to eager loading',
        testing: ['Performance testing', 'User experience testing', 'SEO validation']
      },
      metrics: ['page_load_time', 'bandwidth_usage', 'user_engagement'],
      estimatedSavings: {
        cpu: 10,
        memory: 15,
        storage: 0,
        network: 40,
        cost: 20,
        responseTime: 35
      },
      complexity: ImplementationComplexity.LOW,
      timeline: '1 week',
      status: RecommendationStatus.COMPLETED,
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
    }
  ]

  const displayRecommendations = recommendations.length > 0 ? recommendations : mockRecommendations

  const typeOptions = [
    { value: 'all', label: 'All Types' },
    { value: OptimizationType.CACHING, label: 'Caching' },
    { value: OptimizationType.DATABASE, label: 'Database' },
    { value: OptimizationType.NETWORK, label: 'Network' },
    { value: OptimizationType.FRONTEND, label: 'Frontend' },
    { value: OptimizationType.BACKEND, label: 'Backend' },
    { value: OptimizationType.INFRASTRUCTURE, label: 'Infrastructure' },
    { value: OptimizationType.CODE, label: 'Code' },
    { value: OptimizationType.CONFIGURATION, label: 'Configuration' }
  ]

  const priorityOptions = [
    { value: 'all', label: 'All Priorities' },
    { value: RecommendationPriority.CRITICAL, label: 'Critical' },
    { value: RecommendationPriority.HIGH, label: 'High' },
    { value: RecommendationPriority.MEDIUM, label: 'Medium' },
    { value: RecommendationPriority.LOW, label: 'Low' }
  ]

  const filteredRecommendations = displayRecommendations.filter(rec => {
    const typeMatch = selectedType === 'all' || rec.type === selectedType
    const priorityMatch = selectedPriority === 'all' || rec.priority === selectedPriority
    return typeMatch && priorityMatch
  })

  const getTypeIcon = (type: OptimizationType) => {
    switch (type) {
      case OptimizationType.CACHING:
        return <CubeIcon className="h-5 w-5 text-blue-600" />
      case OptimizationType.DATABASE:
        return <ServerIcon className="h-5 w-5 text-green-600" />
      case OptimizationType.NETWORK:
        return <GlobeAltIcon className="h-5 w-5 text-purple-600" />
      case OptimizationType.FRONTEND:
        return <CodeBracketIcon className="h-5 w-5 text-orange-600" />
      case OptimizationType.BACKEND:
        return <ServerIcon className="h-5 w-5 text-indigo-600" />
      case OptimizationType.INFRASTRUCTURE:
        return <CogIcon className="h-5 w-5 text-gray-600" />
      case OptimizationType.CODE:
        return <CodeBracketIcon className="h-5 w-5 text-teal-600" />
      case OptimizationType.CONFIGURATION:
        return <CogIcon className="h-5 w-5 text-pink-600" />
      default:
        return <BoltIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: RecommendationPriority) => {
    switch (priority) {
      case RecommendationPriority.CRITICAL:
        return 'border-red-200 bg-red-50 text-red-800'
      case RecommendationPriority.HIGH:
        return 'border-orange-200 bg-orange-50 text-orange-800'
      case RecommendationPriority.MEDIUM:
        return 'border-yellow-200 bg-yellow-50 text-yellow-800'
      case RecommendationPriority.LOW:
        return 'border-gray-200 bg-gray-50 text-gray-800'
      default:
        return 'border-gray-200 bg-gray-50 text-gray-800'
    }
  }

  const getStatusColor = (status: RecommendationStatus) => {
    switch (status) {
      case RecommendationStatus.PENDING:
        return 'bg-gray-100 text-gray-800'
      case RecommendationStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800'
      case RecommendationStatus.COMPLETED:
        return 'bg-green-100 text-green-800'
      case RecommendationStatus.REJECTED:
        return 'bg-red-100 text-red-800'
      case RecommendationStatus.DEFERRED:
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getComplexityColor = (complexity: ImplementationComplexity) => {
    switch (complexity) {
      case ImplementationComplexity.LOW:
        return 'text-green-600'
      case ImplementationComplexity.MEDIUM:
        return 'text-yellow-600'
      case ImplementationComplexity.HIGH:
        return 'text-orange-600'
      case ImplementationComplexity.VERY_HIGH:
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const handleImplement = (recommendationId: string) => {
    implementOptimizationMutation.mutate(recommendationId)
  }

  const handleUpdateStatus = (recommendationId: string, status: RecommendationStatus) => {
    updateRecommendationMutation.mutate({
      id: recommendationId,
      status
    })
  }

  const stats = {
    total: displayRecommendations.length,
    pending: displayRecommendations.filter(r => r.status === RecommendationStatus.PENDING).length,
    inProgress: displayRecommendations.filter(r => r.status === RecommendationStatus.IN_PROGRESS).length,
    completed: displayRecommendations.filter(r => r.status === RecommendationStatus.COMPLETED).length
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg mb-4"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900">Optimization Recommendations</h2>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <LightBulbIcon className="h-4 w-4 mr-2" />
          New Recommendation
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <BoltIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{stats.total}</div>
              <div className="text-sm text-gray-600">Total Recommendations</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-gray-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{stats.pending}</div>
              <div className="text-sm text-gray-600">Pending</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <PlayIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{stats.inProgress}</div>
              <div className="text-sm text-gray-600">In Progress</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{stats.completed}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {typeOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        <select
          value={selectedPriority}
          onChange={(e) => setSelectedPriority(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {priorityOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        <div className="text-sm text-gray-500">
          {filteredRecommendations.length} recommendations
        </div>
      </div>

      {/* Recommendations List */}
      {filteredRecommendations.length > 0 ? (
        <div className="space-y-4">
          {filteredRecommendations.map((recommendation) => (
            <RecommendationCard
              key={recommendation.id}
              recommendation={recommendation}
              onImplement={() => handleImplement(recommendation.id)}
              onUpdateStatus={(status) => handleUpdateStatus(recommendation.id, status)}
              onViewDetails={() => setSelectedRecommendation(recommendation)}
              getTypeIcon={getTypeIcon}
              getPriorityColor={getPriorityColor}
              getStatusColor={getStatusColor}
              getComplexityColor={getComplexityColor}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <LightBulbIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No recommendations found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {selectedType !== 'all' || selectedPriority !== 'all'
              ? 'Try adjusting your filters to see more recommendations.'
              : 'All optimizations are up to date. Check back later for new recommendations.'}
          </p>
        </div>
      )}

      {/* Recommendation Details Modal */}
      {selectedRecommendation && (
        <RecommendationDetailsModal
          recommendation={selectedRecommendation}
          onClose={() => setSelectedRecommendation(null)}
          onImplement={() => {
            handleImplement(selectedRecommendation.id)
            setSelectedRecommendation(null)
          }}
        />
      )}
    </div>
  )
}

interface RecommendationCardProps {
  recommendation: OptimizationRecommendation
  onImplement: () => void
  onUpdateStatus: (status: RecommendationStatus) => void
  onViewDetails: () => void
  getTypeIcon: (type: OptimizationType) => React.ReactNode
  getPriorityColor: (priority: RecommendationPriority) => string
  getStatusColor: (status: RecommendationStatus) => string
  getComplexityColor: (complexity: ImplementationComplexity) => string
}

function RecommendationCard({
  recommendation,
  onImplement,
  onUpdateStatus,
  onViewDetails,
  getTypeIcon,
  getPriorityColor,
  getStatusColor,
  getComplexityColor
}: RecommendationCardProps) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex-shrink-0">
            {getTypeIcon(recommendation.type)}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-sm font-medium text-gray-900">{recommendation.title}</h3>
              
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${
                getPriorityColor(recommendation.priority)
              }`}>
                {recommendation.priority}
              </span>
              
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                getStatusColor(recommendation.status)
              }`}>
                {recommendation.status}
              </span>
            </div>
            
            <p className="text-sm text-gray-700 mb-3">{recommendation.description}</p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600">
                  +{recommendation.estimatedSavings.responseTime}%
                </div>
                <div className="text-xs text-gray-600">Response Time</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600">
                  {recommendation.estimatedSavings.cost}%
                </div>
                <div className="text-xs text-gray-600">Cost Savings</div>
              </div>
              <div className="text-center">
                <div className={`text-lg font-semibold ${getComplexityColor(recommendation.complexity)}`}>
                  {recommendation.complexity}
                </div>
                <div className="text-xs text-gray-600">Complexity</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-purple-600">
                  {recommendation.timeline}
                </div>
                <div className="text-xs text-gray-600">Timeline</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <span className="capitalize">{recommendation.type}</span>
              <span>{recommendation.implementation.steps.length} steps</span>
              <span>{recommendation.metrics.length} metrics</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 ml-4">
          <button
            onClick={onViewDetails}
            className="text-blue-600 hover:text-blue-700"
            title="View Details"
          >
            <InformationCircleIcon className="h-4 w-4" />
          </button>
          
          {recommendation.status === RecommendationStatus.PENDING && (
            <button
              onClick={onImplement}
              className="inline-flex items-center px-3 py-1 border border-green-600 text-xs font-medium rounded text-green-600 bg-white hover:bg-green-50"
            >
              <PlayIcon className="h-3 w-3 mr-1" />
              Implement
            </button>
          )}
          
          {recommendation.status === RecommendationStatus.IN_PROGRESS && (
            <button
              onClick={() => onUpdateStatus(RecommendationStatus.COMPLETED)}
              className="inline-flex items-center px-3 py-1 border border-green-600 text-xs font-medium rounded text-green-600 bg-white hover:bg-green-50"
            >
              <CheckCircleIcon className="h-3 w-3 mr-1" />
              Complete
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

interface RecommendationDetailsModalProps {
  recommendation: OptimizationRecommendation
  onClose: () => void
  onImplement: () => void
}

function RecommendationDetailsModal({
  recommendation,
  onClose,
  onImplement
}: RecommendationDetailsModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">{recommendation.title}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          
          <div className="space-y-6">
            {/* Implementation Steps */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Implementation Steps</h3>
              <div className="space-y-3">
                {recommendation.implementation.steps.map((step) => (
                  <div key={step.order} className="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium">
                      {step.order}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900">{step.title}</div>
                      <div className="text-sm text-gray-600 mt-1">{step.description}</div>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span>Time: {step.estimatedTime}</span>
                        <span>Resources: {step.resources.join(', ')}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Impact Assessment */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Impact Assessment</h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {Object.entries(recommendation.impact).map(([key, value]) => (
                  <div key={key} className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{value}%</div>
                    <div className="text-sm text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1')}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Prerequisites and Risks */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Prerequisites</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {recommendation.implementation.prerequisites.map((prereq, index) => (
                    <li key={index}>• {prereq}</li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Risks</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {recommendation.implementation.risks.map((risk, index) => (
                    <li key={index}>• {risk}</li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Close
              </button>
              
              {recommendation.status === RecommendationStatus.PENDING && (
                <button
                  onClick={onImplement}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                >
                  Implement Recommendation
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
