'use client'

import React, { useState } from 'react'
import {
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  UsersIcon,
  GiftIcon,
  ClockIcon,
  GlobeAltIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'
import { useCampaignAnalytics, useCampaignMetrics, useExportCampaignData } from '@/hooks/useCampaigns'

interface CampaignAnalyticsProps {
  campaignId: string
  className?: string
}

export default function CampaignAnalytics({ campaignId, className = '' }: CampaignAnalyticsProps) {
  const [timeframe, setTimeframe] = useState<'24h' | '7d' | '30d' | '90d'>('30d')

  const { data: analytics, isLoading: analyticsLoading } = useCampaignAnalytics(campaignId, timeframe)
  const { data: metrics, isLoading: metricsLoading } = useCampaignMetrics(campaignId)
  const exportDataMutation = useExportCampaignData()

  const handleExport = async (format: 'json' | 'csv' | 'pdf') => {
    try {
      const blob = await exportDataMutation.mutateAsync({ campaignId, format })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `campaign-${campaignId}-analytics.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to export data:', error)
    }
  }

  if (analyticsLoading || metricsLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  const metricCards = [
    {
      title: 'Participation Rate',
      value: `${((metrics?.participationRate || 0) * 100).toFixed(1)}%`,
      icon: UsersIcon,
      color: 'text-blue-600 bg-blue-100',
      trend: 'up',
      trendValue: '12%'
    },
    {
      title: 'Completion Rate',
      value: `${((metrics?.completionRate || 0) * 100).toFixed(1)}%`,
      icon: ChartBarIcon,
      color: 'text-green-600 bg-green-100',
      trend: 'up',
      trendValue: '8%'
    },
    {
      title: 'Engagement Score',
      value: `${((metrics?.engagementScore || 0) * 100).toFixed(0)}`,
      icon: TrendingUpIcon,
      color: 'text-purple-600 bg-purple-100',
      trend: 'down',
      trendValue: '3%'
    },
    {
      title: 'Social Reach',
      value: (metrics?.socialReach || 0).toLocaleString(),
      icon: GlobeAltIcon,
      color: 'text-orange-600 bg-orange-100',
      trend: 'up',
      trendValue: '25%'
    }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Campaign Analytics</h3>
          <p className="text-sm text-gray-600">Performance insights and metrics</p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Timeframe Selector */}
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>

          {/* Export Button */}
          <div className="relative">
            <button
              onClick={() => handleExport('csv')}
              disabled={exportDataMutation.isPending}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              {exportDataMutation.isPending ? 'Exporting...' : 'Export'}
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metricCards.map((metric, index) => (
          <div
            key={index}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${metric.color} mb-4`}>
              <metric.icon className="h-6 w-6" />
            </div>

            <div className="mb-2">
              <div className="flex items-baseline space-x-2">
                <h3 className="text-2xl font-bold text-gray-900">{metric.value}</h3>
                <div className={`flex items-center text-sm font-medium ${
                  metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {metric.trend === 'up' ? (
                    <TrendingUpIcon className="h-4 w-4 mr-1" />
                  ) : (
                    <TrendingDownIcon className="h-4 w-4 mr-1" />
                  )}
                  {metric.trendValue}
                </div>
              </div>
              <p className="text-sm font-medium text-gray-700">{metric.title}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Participation Over Time Chart */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Participation Over Time</h4>
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">Chart visualization would be implemented here</p>
            <p className="text-xs text-gray-500">Integration with charting library (Chart.js, Recharts, etc.)</p>
          </div>
        </div>
      </div>

      {/* Detailed Analytics */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Engagement Metrics */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Engagement Metrics</h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Average Completion Time</span>
                <span className="text-sm font-medium text-gray-900">
                  {Math.round(analytics.averageCompletionTime / 60)} minutes
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Average Points per Participant</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.averagePointsPerParticipant.toFixed(1)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Dropoff Rate</span>
                <span className="text-sm font-medium text-gray-900">
                  {(analytics.dropoffRate * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>

          {/* Social Media Metrics */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Social Media Impact</h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Tweets</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.socialEngagement.tweets.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Retweets</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.socialEngagement.retweets.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Reach</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.socialEngagement.reach.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Impressions</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.socialEngagement.impressions.toLocaleString()}
                </span>
              </div>
            </div>
          </div>

          {/* Geographic Distribution */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Geographic Distribution</h4>
            <div className="space-y-3">
              {Object.entries(analytics.participantsByRegion || {})
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([region, count]) => (
                  <div key={region} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{region}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ 
                            width: `${(count / Math.max(...Object.values(analytics.participantsByRegion || {}))) * 100}%` 
                          }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-8 text-right">{count}</span>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          {/* Performance Indicators */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Performance Indicators</h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">ROI</span>
                <span className={`text-sm font-medium ${
                  analytics.roi > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {analytics.roi > 0 ? '+' : ''}{(analytics.roi * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Cost per Participant</span>
                <span className="text-sm font-medium text-gray-900">
                  ${analytics.costPerParticipant.toFixed(2)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Conversion Rate</span>
                <span className="text-sm font-medium text-gray-900">
                  {(analytics.conversionRate * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Virality Score</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.viralityScore.toFixed(1)}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Requirement Completion Rates */}
      {analytics?.requirementCompletionRates && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Requirement Completion Rates</h4>
          <div className="space-y-3">
            {Object.entries(analytics.requirementCompletionRates)
              .sort(([,a], [,b]) => b - a)
              .map(([requirement, rate]) => (
                <div key={requirement} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{requirement}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${rate * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-12 text-right">
                      {(rate * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  )
}
