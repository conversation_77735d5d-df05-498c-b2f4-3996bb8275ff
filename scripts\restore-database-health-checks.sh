#!/bin/bash

# Restore Database Health Checks with Proper Dependency Injection
# This script properly implements database health checks with correct module imports

set -e

echo "🏥 Restoring Database Health Checks with Proper Dependencies"
echo "==========================================================="

# Services that need database health checks restored
SERVICES=(
    "profile-analysis-service"
    "blockchain-service" 
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Function to create proper database health service
create_database_health_service() {
    local service=$1
    local service_dir="services/$service"
    
    echo "🗄️  Creating database health service for $service..."
    
    cat > "$service_dir/src/health/database-health.service.ts" << 'EOF'
import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { PrismaService } from '../enterprise/shared/prisma.service';

@Injectable()
export class DatabaseHealthService extends HealthIndicator {
  constructor(private readonly prisma: PrismaService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const startTime = Date.now();
      
      // Test database connection with a simple query
      await this.prisma.$queryRaw`SELECT 1 as health_check`;
      
      const responseTime = Date.now() - startTime;
      
      const result = this.getStatus(key, true, {
        database: 'postgresql',
        status: 'up',
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      const result = this.getStatus(key, false, {
        database: 'postgresql',
        status: 'down',
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      throw new HealthCheckError('Database health check failed', result);
    }
  }
}
EOF
    
    echo "✅ Database health service created for $service"
}

# Function to create comprehensive health controller with database checks
create_comprehensive_health_controller() {
    local service=$1
    local service_dir="services/$service"
    
    echo "🏥 Creating comprehensive health controller for $service..."
    
    # Get port number based on service
    local port=""
    case $service in
        "profile-analysis-service") port="3002" ;;
        "blockchain-service") port="3004" ;;
        "project-service") port="3005" ;;
        "marketplace-service") port="3006" ;;
        "notification-service") port="3008" ;;
        "analytics-service") port="3009" ;;
        *) port="3000" ;;
    esac
    
    cat > "$service_dir/src/health/health.controller.ts" << EOF
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthCheck, HealthCheckService } from '@nestjs/terminus';
import { DatabaseHealthService } from './database-health.service';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    private readonly databaseHealthService: DatabaseHealthService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get comprehensive service health status' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  @ApiResponse({ status: 503, description: 'Service is unhealthy' })
  @HealthCheck()
  check() {
    return this.healthCheckService.check([
      () => this.databaseHealthService.isHealthy('database'),
    ]);
  }

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check without dependencies' })
  @ApiResponse({ status: 200, description: 'Service is running' })
  simple() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: '$service',
      version: '1.0.0',
      uptime: process.uptime(),
      port: process.env.PORT || '$port',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        memory: {
          status: 'ok',
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB',
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + 'MB',
        },
        uptime: {
          status: 'ok',
          seconds: Math.floor(process.uptime()),
        },
      },
    };
  }

  @Get('database')
  @ApiOperation({ summary: 'Database-only health check' })
  @ApiResponse({ status: 200, description: 'Database is healthy' })
  @ApiResponse({ status: 503, description: 'Database is unhealthy' })
  async database() {
    try {
      const result = await this.databaseHealthService.isHealthy('database');
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: '$service',
        ...result,
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        service: '$service',
        error: error.message,
      };
    }
  }
}
EOF
    
    echo "✅ Comprehensive health controller created for $service"
}

# Function to create proper health module with correct imports
create_proper_health_module() {
    local service=$1
    local service_dir="services/$service"
    
    echo "📦 Creating proper health module for $service..."
    
    cat > "$service_dir/src/health/health.module.ts" << 'EOF'
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HealthController } from './health.controller';
import { DatabaseHealthService } from './database-health.service';
import { EnterpriseModule } from '../enterprise/enterprise.module';

@Module({
  imports: [
    TerminusModule,
    EnterpriseModule, // This provides PrismaService
  ],
  controllers: [HealthController],
  providers: [DatabaseHealthService],
  exports: [DatabaseHealthService],
})
export class HealthModule {}
EOF
    
    echo "✅ Proper health module created for $service"
}

# Main execution
echo "🚀 Starting database health checks restoration..."

for service in "${SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service" ]; then
        echo "❌ Service directory not found: services/$service"
        continue
    fi
    
    # Create proper health components with database checks
    create_database_health_service "$service"
    create_comprehensive_health_controller "$service"
    create_proper_health_module "$service"
    
    echo "✅ $service database health checks restored"
done

echo ""
echo "🎉 Database Health Checks Restoration Completed!"
echo "==============================================="
echo ""
echo "📋 Summary:"
echo "- Restored database health services for ${#SERVICES[@]} services"
echo "- Created comprehensive health controllers with database checks"
echo "- Fixed dependency injection by properly importing EnterpriseModule"
echo "- All services now have proper database monitoring"
echo ""
echo "🏥 Available Health Endpoints:"
echo "- GET /api/health (comprehensive check including database)"
echo "- GET /api/health/simple (basic service status without dependencies)"
echo "- GET /api/health/database (database-only health check)"
echo ""
echo "🔄 Services will restart automatically due to watch mode"
echo ""
echo "💡 Why Database Health Checks are Essential:"
echo "- Monitor database connectivity in production"
echo "- Detect database performance issues"
echo "- Enable proper load balancer health checks"
echo "- Support automated failover and scaling decisions"
