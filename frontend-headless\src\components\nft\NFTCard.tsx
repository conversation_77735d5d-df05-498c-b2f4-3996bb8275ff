'use client'

import React from 'react'
import { NFT, NFTRarity, NFTStatus } from '@/types/nft.types'
import {
  SparklesIcon,
  ChartBarIcon,
  CalendarIcon,
  HashtagIcon,
  GlobeAltIcon,
  CheckBadgeIcon,
  CubeIcon,
  EyeIcon,
  PencilIcon,
  ShareIcon,
  TagIcon,
  BanknotesIcon,
  HeartIcon
} from '@heroicons/react/24/outline'

interface NFTCardProps {
  nft: NFT
  variant?: 'default' | 'compact' | 'detailed'
  showActions?: boolean
  showStats?: boolean
  onClick?: (nft: NFT) => void
  onView?: (nft: NFT) => void
  onEdit?: (nft: NFT) => void
  onShare?: (nft: NFT) => void
  onMint?: (nft: NFT) => void
  onList?: (nft: NFT) => void
  onFavorite?: (nft: NFT) => void
  className?: string
}

export default function NFTCard({
  nft,
  variant = 'default',
  showActions = true,
  showStats = true,
  onClick,
  onView,
  onEdit,
  onShare,
  onMint,
  onList,
  onFavorite,
  className = ''
}: NFTCardProps) {

  const getRarityColor = (rarity: NFTRarity) => {
    const colors = {
      [NFTRarity.MYTHIC]: 'from-purple-600 to-pink-600',
      [NFTRarity.LEGENDARY]: 'from-yellow-400 to-orange-500',
      [NFTRarity.EPIC]: 'from-purple-500 to-pink-500',
      [NFTRarity.RARE]: 'from-blue-500 to-cyan-500',
      [NFTRarity.COMMON]: 'from-gray-400 to-gray-600'
    }
    return colors[rarity] || colors[NFTRarity.COMMON]
  }

  const getRarityBadgeColor = (rarity: NFTRarity) => {
    const colors = {
      [NFTRarity.MYTHIC]: 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-200',
      [NFTRarity.LEGENDARY]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      [NFTRarity.EPIC]: 'bg-purple-100 text-purple-800 border-purple-200',
      [NFTRarity.RARE]: 'bg-blue-100 text-blue-800 border-blue-200',
      [NFTRarity.COMMON]: 'bg-gray-100 text-gray-800 border-gray-200'
    }
    return colors[rarity] || colors[NFTRarity.COMMON]
  }

  const getStatusColor = (status: NFTStatus) => {
    const colors = {
      [NFTStatus.MINTED]: 'bg-green-100 text-green-800 border-green-200',
      [NFTStatus.LISTED]: 'bg-orange-100 text-orange-800 border-orange-200',
      [NFTStatus.SOLD]: 'bg-red-100 text-red-800 border-red-200',
      [NFTStatus.GENERATING]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      [NFTStatus.MINTING]: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      [NFTStatus.GENERATED]: 'bg-blue-100 text-blue-800 border-blue-200',
      [NFTStatus.TRANSFERRED]: 'bg-purple-100 text-purple-800 border-purple-200',
      [NFTStatus.BURNED]: 'bg-gray-100 text-gray-800 border-gray-200'
    }
    return colors[status] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation()
    action()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  // Compact variant for list views
  if (variant === 'compact') {
    return (
      <div 
        className={`bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer ${className}`}
        onClick={() => onClick?.(nft)}
      >
        <div className="flex items-center space-x-4">
          {/* NFT Image */}
          <div className="flex-shrink-0">
            <img
              src={nft.imageUrl || '/api/placeholder/60/60'}
              alt={nft.name}
              className="w-15 h-15 rounded-lg object-cover"
            />
          </div>

          {/* NFT Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-base font-semibold text-gray-900 truncate">
                  {nft.name}
                </h3>
                <p className="text-sm text-gray-600">@{nft.twitterHandle}</p>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getRarityBadgeColor(nft.rarity)}`}>
                  {nft.rarity.charAt(0).toUpperCase() + nft.rarity.slice(1)}
                </span>
                <span className="text-sm font-medium text-gray-900">
                  {nft.currentScore}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Default card variant
  return (
    <div 
      className={`bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-[1.02] ${className}`}
      onClick={() => onClick?.(nft)}
    >
      {/* NFT Image */}
      <div className="relative overflow-hidden group">
        <div className={`h-48 bg-gradient-to-br ${getRarityColor(nft.rarity)} p-1`}>
          <img
            src={nft.imageUrl || '/api/placeholder/300/300'}
            alt={nft.name}
            className="w-full h-full object-cover rounded-lg transition-transform duration-300 group-hover:scale-105"
          />
        </div>

        {/* Badges */}
        <div className="absolute top-3 right-3">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRarityBadgeColor(nft.rarity)}`}>
            <SparklesIcon className="h-3 w-3 mr-1" />
            {nft.rarity.charAt(0).toUpperCase() + nft.rarity.slice(1)}
          </span>
        </div>

        <div className="absolute top-3 left-3">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-gray-800 border border-gray-200">
            <ChartBarIcon className="h-3 w-3 mr-1" />
            {nft.currentScore}
          </span>
        </div>

        {nft.status && (
          <div className="absolute bottom-3 left-3">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(nft.status)}`}>
              <CubeIcon className="h-3 w-3 mr-1" />
              {nft.status.charAt(0).toUpperCase() + nft.status.slice(1)}
            </span>
          </div>
        )}

        {/* Action Buttons Overlay */}
        {showActions && (
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex space-x-2">
              {onView && (
                <button
                  onClick={(e) => handleActionClick(e, () => onView(nft))}
                  className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  title="View Details"
                >
                  <EyeIcon className="h-4 w-4 text-gray-600" />
                </button>
              )}
              {onEdit && (
                <button
                  onClick={(e) => handleActionClick(e, () => onEdit(nft))}
                  className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  title="Edit NFT"
                >
                  <PencilIcon className="h-4 w-4 text-gray-600" />
                </button>
              )}
              {onShare && (
                <button
                  onClick={(e) => handleActionClick(e, () => onShare(nft))}
                  className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  title="Share NFT"
                >
                  <ShareIcon className="h-4 w-4 text-gray-600" />
                </button>
              )}
              {onFavorite && (
                <button
                  onClick={(e) => handleActionClick(e, () => onFavorite(nft))}
                  className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  title="Add to Favorites"
                >
                  <HeartIcon className="h-4 w-4 text-gray-600" />
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* NFT Details */}
      <div className="p-4">
        {/* Header */}
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {nft.name}
          </h3>
          <p className="text-sm text-gray-600 flex items-center mt-1">
            <span className="mr-2">@{nft.twitterHandle}</span>
            {nft.tokenId && (
              <>
                <HashtagIcon className="h-3 w-3 mr-1" />
                <span className="text-xs">#{nft.tokenId}</span>
              </>
            )}
          </p>
        </div>

        {/* Description */}
        {nft.description && variant === 'detailed' && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {nft.description}
          </p>
        )}

        {/* Evolution Indicator */}
        {nft.evolution && (
          <div className="mb-3 p-2 bg-purple-50 rounded-lg border border-purple-200">
            <div className="flex items-center text-xs text-purple-700">
              <SparklesIcon className="h-3 w-3 mr-1" />
              <span className="font-medium">Evolved NFT</span>
            </div>
            <div className="text-xs text-purple-600 mt-1">
              Score: {nft.evolution.previousScore} → {nft.evolution.newScore}
            </div>
          </div>
        )}

        {/* Marketplace Data */}
        {nft.marketplaceData?.isListed && (
          <div className="mb-3 p-2 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center justify-between text-xs">
              <span className="text-green-700 font-medium">Listed for Sale</span>
              <span className="text-green-800 font-bold">
                {nft.marketplaceData.price} {nft.marketplaceData.currency}
              </span>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center text-xs text-gray-500">
            <CalendarIcon className="h-3 w-3 mr-1" />
            {formatDate(nft.createdAt)}
          </div>
          
          <div className="flex items-center space-x-1">
            {nft.blockchain && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                <GlobeAltIcon className="h-3 w-3 mr-1" />
                {nft.blockchain.charAt(0).toUpperCase() + nft.blockchain.slice(1)}
              </span>
            )}
            
            {nft.isMinted && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                <CheckBadgeIcon className="h-3 w-3 mr-1" />
                Minted
              </span>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        {showActions && (variant === 'detailed') && (
          <div className="mt-4 flex items-center justify-between">
            <div className="flex space-x-2">
              {onMint && nft.status === NFTStatus.GENERATED && (
                <button
                  onClick={(e) => handleActionClick(e, () => onMint(nft))}
                  className="px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 transition-colors"
                >
                  Mint NFT
                </button>
              )}
              {onList && nft.status === NFTStatus.MINTED && !nft.marketplaceData?.isListed && (
                <button
                  onClick={(e) => handleActionClick(e, () => onList(nft))}
                  className="px-3 py-1 bg-green-600 text-white text-xs font-medium rounded-md hover:bg-green-700 transition-colors"
                >
                  List for Sale
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
