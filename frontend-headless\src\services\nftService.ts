import { api, ApiResponse } from '../lib/api'
import {
  NFT,
  NFTCollection,
  NFTAnalytics,
  NFTFilters,
  NFTSortOptions,
  GetNFTCollectionRequest,
  UpdateNFTRequest,
  MintNFTRequest,
  NFTGenerationRequest,
  NFTEvolution,
  NFTRarity,
  NFTStatus,
  BlockchainNetwork
} from '../types/nft.types'

export class NFTService {
  /**
   * Get user's NFT collection with filtering and pagination
   */
  async getUserCollection(
    userId: string,
    options?: {
      filters?: NFTFilters
      sort?: NFTSortOptions
      page?: number
      limit?: number
    }
  ): Promise<ApiResponse<NFTCollection>> {
    const params: any = {}

    // Apply filters
    if (options?.filters) {
      if (options.filters.rarity?.length) {
        params.rarity = options.filters.rarity.join(',')
      }
      if (options.filters.status?.length) {
        params.status = options.filters.status.join(',')
      }
      if (options.filters.blockchain?.length) {
        params.blockchain = options.filters.blockchain.join(',')
      }
      if (options.filters.isMinted !== undefined) {
        params.isMinted = options.filters.isMinted
      }
      if (options.filters.isListed !== undefined) {
        params.isListed = options.filters.isListed
      }
      if (options.filters.searchQuery) {
        params.search = options.filters.searchQuery
      }
      if (options.filters.scoreRange) {
        params.minScore = options.filters.scoreRange.min
        params.maxScore = options.filters.scoreRange.max
      }
    }

    // Apply sorting
    if (options?.sort) {
      params.sortBy = options.sort.field
      params.sortOrder = options.sort.order
    }

    // Apply pagination
    if (options?.page) params.page = options.page
    if (options?.limit) params.limit = options.limit

    try {
      const response = await api.get<NFTCollection>(`/users/${userId}/nfts`, { params })
      return response
    } catch (error) {
      console.error('NFTService: Error fetching user collection:', error)
      return {
        success: false,
        error: 'Failed to fetch NFT collection'
      }
    }
  }

  /**
   * Get current user's NFT collection (authenticated)
   */
  async getMyCollection(options?: {
    filters?: NFTFilters
    sort?: NFTSortOptions
    page?: number
    limit?: number
  }): Promise<ApiResponse<NFTCollection>> {
    const params: any = {}

    // Apply filters
    if (options?.filters) {
      if (options.filters.rarity?.length) {
        params.rarity = options.filters.rarity.join(',')
      }
      if (options.filters.status?.length) {
        params.status = options.filters.status.join(',')
      }
      if (options.filters.blockchain?.length) {
        params.blockchain = options.filters.blockchain.join(',')
      }
      if (options.filters.searchQuery) {
        params.search = options.filters.searchQuery
      }
    }

    // Apply sorting
    if (options?.sort) {
      params.sortBy = options.sort.field
      params.sortOrder = options.sort.order
    }

    // Apply pagination
    if (options?.page) params.page = options.page
    if (options?.limit) params.limit = options.limit

    try {
      const response = await api.get<NFTCollection>('/nfts/my/collection', { params })
      return response
    } catch (error) {
      console.error('NFTService: Error fetching my collection:', error)
      return {
        success: false,
        error: 'Failed to fetch your NFT collection'
      }
    }
  }

  /**
   * Get detailed information about a specific NFT
   */
  async getNFTDetails(userId: string, nftId: string): Promise<ApiResponse<NFT>> {
    try {
      const response = await api.get<NFT>(`/users/${userId}/nfts/${nftId}`)
      return response
    } catch (error) {
      console.error('NFTService: Error fetching NFT details:', error)
      return {
        success: false,
        error: 'Failed to fetch NFT details'
      }
    }
  }

  /**
   * Update NFT information
   */
  async updateNFT(
    userId: string,
    nftId: string,
    updates: UpdateNFTRequest
  ): Promise<ApiResponse<NFT>> {
    try {
      const response = await api.put<NFT>(`/users/${userId}/nfts/${nftId}`, updates)
      return response
    } catch (error) {
      console.error('NFTService: Error updating NFT:', error)
      return {
        success: false,
        error: 'Failed to update NFT'
      }
    }
  }

  /**
   * Get NFT analytics for a user
   */
  async getNFTAnalytics(userId: string, timeframe?: string): Promise<ApiResponse<NFTAnalytics>> {
    const params = timeframe ? { timeframe } : {}

    try {
      const response = await api.get<NFTAnalytics>(`/users/${userId}/nfts/analytics`, { params })
      return response
    } catch (error) {
      console.error('NFTService: Error fetching NFT analytics:', error)
      return {
        success: false,
        error: 'Failed to fetch NFT analytics'
      }
    }
  }

  /**
   * Generate NFT from analysis
   */
  async generateNFTFromAnalysis(request: NFTGenerationRequest): Promise<ApiResponse<NFT>> {
    try {
      const response = await api.post<NFT>('/nfts/generate-from-analysis', request)
      return response
    } catch (error) {
      console.error('NFTService: Error generating NFT from analysis:', error)
      return {
        success: false,
        error: 'Failed to generate NFT from analysis'
      }
    }
  }

  /**
   * Mint NFT on blockchain
   */
  async mintNFT(request: MintNFTRequest): Promise<ApiResponse<NFT>> {
    try {
      const response = await api.post<NFT>(`/nfts/${request.nftId}/mint`, {
        blockchain: request.blockchain,
        contractAddress: request.contractAddress,
        gasPrice: request.gasPrice,
        gasLimit: request.gasLimit
      })
      return response
    } catch (error) {
      console.error('NFTService: Error minting NFT:', error)
      return {
        success: false,
        error: 'Failed to mint NFT'
      }
    }
  }

  /**
   * Update NFT score (triggers evolution check)
   */
  async updateNFTScore(nftId: string, newScore: number): Promise<ApiResponse<NFT>> {
    try {
      const response = await api.patch<NFT>(`/nfts/${nftId}/score`, { score: newScore })
      return response
    } catch (error) {
      console.error('NFTService: Error updating NFT score:', error)
      return {
        success: false,
        error: 'Failed to update NFT score'
      }
    }
  }

  /**
   * Get NFT evolution history
   */
  async getEvolutionHistory(nftId: string): Promise<ApiResponse<NFTEvolution[]>> {
    try {
      const response = await api.get<NFTEvolution[]>(`/nfts/${nftId}/evolution`)
      return response
    } catch (error) {
      console.error('NFTService: Error fetching evolution history:', error)
      return {
        success: false,
        error: 'Failed to fetch evolution history'
      }
    }
  }

  /**
   * Get available filter options for NFT collection
   */
  async getFilterOptions(userId?: string): Promise<{
    rarities: NFTRarity[]
    statuses: NFTStatus[]
    blockchains: BlockchainNetwork[]
  }> {
    return {
      rarities: Object.values(NFTRarity),
      statuses: Object.values(NFTStatus),
      blockchains: Object.values(BlockchainNetwork)
    }
  }

  /**
   * Search NFTs across collections
   */
  async searchNFTs(
    query: string,
    filters?: NFTFilters,
    limit: number = 20
  ): Promise<ApiResponse<NFT[]>> {
    const params: any = {
      search: query,
      limit
    }

    if (filters?.rarity?.length) {
      params.rarity = filters.rarity.join(',')
    }
    if (filters?.status?.length) {
      params.status = filters.status.join(',')
    }

    try {
      const response = await api.get<NFT[]>('/nfts/search', { params })
      return response
    } catch (error) {
      console.error('NFTService: Error searching NFTs:', error)
      return {
        success: false,
        error: 'Failed to search NFTs'
      }
    }
  }

  /**
   * Get trending NFTs
   */
  async getTrendingNFTs(limit: number = 10): Promise<ApiResponse<NFT[]>> {
    try {
      const response = await api.get<NFT[]>('/nfts/trending', { params: { limit } })
      return response
    } catch (error) {
      console.error('NFTService: Error fetching trending NFTs:', error)
      return {
        success: false,
        error: 'Failed to fetch trending NFTs'
      }
    }
  }
}

// Export singleton instance
export const nftService = new NFTService()
export default nftService
