import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { HttpModule } from '@nestjs/axios';

// Enhanced User Services
import { UserManagementService } from './services/user-management.service';
import { AuthenticationService } from './services/authentication.service';
import { UserProfileService } from './services/user-profile.service';

// Legacy Services (to be migrated)
import { UserCommandService } from './services/user-command.service';
import { UserQueryService } from './services/user-query.service';
import { UserAnalyticsService } from './services/user-analytics.service';
import { AuthService } from './services/auth.service';
import { RBACService } from './services/rbac.service';
import { CampaignService } from './services/campaign.service';
import { NFTService } from './services/nft.service';
import { MarketplaceService } from './services/marketplace.service';
import { AnalyticsService } from './services/analytics.service';
import { RealtimeService } from './services/realtime.service';
import { SearchService } from './services/search.service';
import { AuditService } from '../common/services/audit.service';
import { CacheService } from '../common/services/cache.service';
import { TwitterUserService } from '../twitter/twitter-user.service';

// Enhanced Controllers
import { UserManagementController } from './controllers/user-management.controller';
import { UserAuthenticationController } from './controllers/user-authentication.controller';
import { UserProfileController } from './controllers/user-profile.controller';

// Legacy Controllers
import { UserQueryController } from './controllers/user-query.controller';
import { UserCommandController } from './controllers/user-command.controller';
import { AuthController } from './controllers/auth.controller';
import { RBACController } from './controllers/rbac.controller';
import { CampaignController } from './controllers/campaign.controller';
import { NFTController } from './controllers/nft.controller';
import { MarketplaceController } from './controllers/marketplace.controller';
import { AnalyticsController } from './controllers/analytics.controller';
import { RealtimeController } from './controllers/realtime.controller';
import { SearchController } from './controllers/search.controller';
import { TwitterUserController } from './controllers/twitter-user.controller';

// Gateways
import { RealtimeGateway } from './gateways/realtime.gateway';

@Module({
  imports: [
    HttpModule, // For external API calls
  ],
  controllers: [
    // Enhanced Controllers (preserve business logic)
    UserManagementController,
    UserAuthenticationController,
    UserProfileController,

    // Legacy Controllers (preserve business logic)
    UserQueryController,
    UserCommandController,
    AuthController,
    RBACController,
    CampaignController,
    NFTController,
    MarketplaceController,
    AnalyticsController,
    RealtimeController,
    SearchController,
    TwitterUserController,
  ],
  providers: [
    // Enhanced User Services (preserve business logic)
    UserManagementService,
    AuthenticationService,
    UserProfileService,

    // Legacy Services (preserve business logic)
    UserCommandService,
    UserQueryService,
    UserAnalyticsService,
    AuthService,
    RBACService,
    CampaignService,
    NFTService,
    MarketplaceService,
    AnalyticsService,
    RealtimeService,
    SearchService,
    AuditService,
    CacheService,
    TwitterUserService,
    RealtimeGateway,
  ],
  exports: [
    // Enhanced User Services
    UserManagementService,
    AuthenticationService,
    UserProfileService,

    // Legacy Services
    UserCommandService,
    UserQueryService,
    AuthService,
    RBACService,
    CampaignService,
    NFTService,
    MarketplaceService,
    AnalyticsService,
    RealtimeService,
    SearchService,
    AuditService,
    TwitterUserService,
    RealtimeGateway
  ],
})
export class UserModule {}
