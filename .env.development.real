# ===== REAL SERVICES ENVIRONMENT =====
# Development with real external services - requires API keys and credentials
# Use: ./scripts/switch-environment.sh real

# ===== ENVIRONMENT CONFIGURATION =====
NODE_ENV=development
SERVICE_ENVIRONMENT=real
USE_MOCK_SERVICES=false

# ===== SERVICE DISCOVERY =====
API_GATEWAY_PORT=3010
API_GATEWAY_URL=http://localhost:3010

# ===== REAL SERVICE PORTS =====
USER_SERVICE_PORT=3011
PROFILE_ANALYSIS_SERVICE_PORT=3002
NFT_GENERATION_SERVICE_PORT=3003
BLOCKCHAIN_SERVICE_PORT=3004
PROJECT_SERVICE_PORT=3005
MARKETPLACE_SERVICE_PORT=3006
NOTIFICATION_SERVICE_PORT=3008
ANALYTICS_SERVICE_PORT=3009

# ===== SERVICE URLS (REAL CONFIGURATION) =====
# All services use real implementations
USER_SERVICE_URL=http://localhost:3011
PROFILE_ANALYSIS_SERVICE_URL=http://localhost:3002
NFT_GENERATION_SERVICE_URL=http://localhost:3003
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
PROJECT_SERVICE_URL=http://localhost:3005
MARKETPLACE_SERVICE_URL=http://localhost:3006
NOTIFICATION_SERVICE_URL=http://localhost:3008
ANALYTICS_SERVICE_URL=http://localhost:3009

# External services use real endpoints
TWITTER_SERVICE_URL=http://localhost:3002
NFT_STORAGE_SERVICE_URL=http://localhost:3006

# ===== DATABASE CONFIGURATION =====
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111

# Service-Specific Database Names
USER_DB_NAME=user_service
PROFILE_ANALYSIS_DB_NAME=profile_analysis_service
NFT_GENERATION_DB_NAME=nft_generation_service
BLOCKCHAIN_DB_NAME=blockchain_service
PROJECT_DB_NAME=project_service
MARKETPLACE_DB_NAME=marketplace_service
NOTIFICATION_DB_NAME=notification_service
ANALYTICS_DB_NAME=analytics_service

# ===== SECURITY CONFIGURATION =====
# JWT Configuration
JWT_SECRET=dev-jwt-secret-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Gateway Security
GATEWAY_SECRET=dev-gateway-secret-change-in-production
ALLOW_DIRECT_ACCESS=true
TRUSTED_IPS=127.0.0.1,::1,localhost
ENABLE_GATEWAY_AUTH=true

# ===== CACHING CONFIGURATION =====
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379
ENABLE_REDIS_CACHE=false
CACHE_TTL=300

# ===== LOGGING CONFIGURATION =====
LOG_LEVEL=info
LOG_FORMAT=combined
ENABLE_DEBUG_LOGGING=false

# ===== DEVELOPMENT FLAGS =====
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_HOT_RELOAD=true
ENABLE_PERFORMANCE_MONITORING=true

# ===== REAL EXTERNAL SERVICES =====
# ⚠️ IMPORTANT: Configure these with your actual API keys
# Twitter API Configuration
TWITTER_API_KEY=*************************
TWITTER_API_SECRET=1WzbsQXOrskAx4tP231AtUSKm6QOnDklbvXMHozDwP8nOpnK3c
TWITTER_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAAKEC1AEAAAAAfnWqV8LvjCmxyIUfLf1cslix%2FTI%3DmgfXwKx3Mm5warOLTUyqcbUzbiN7lrQ1UmE5GoMTOOY051IkPZ

# Blockchain Configuration
BLOCKCHAIN_RPC_URL=your-blockchain-rpc-url
BLOCKCHAIN_PRIVATE_KEY=your-blockchain-private-key
BLOCKCHAIN_NETWORK=testnet

# NFT Storage Configuration
NFT_STORAGE_API_KEY=your-nft-storage-api-key
NFT_STORAGE_ENDPOINT=https://api.nft.storage

# ===== EMAIL SERVICE CONFIGURATION =====
# Configure with your SMTP provider
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_SECURE=true

# ===== MONITORING & OBSERVABILITY =====
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=30

# ===== FEATURE FLAGS =====
ENABLE_AUDIT_LOGGING=true
ENABLE_EVENT_SOURCING=true
ENABLE_RATE_LIMITING=true
ENABLE_API_VERSIONING=true

# ===== EXTERNAL API RATE LIMITS =====
TWITTER_RATE_LIMIT=300
BLOCKCHAIN_RATE_LIMIT=100
NFT_STORAGE_RATE_LIMIT=50

# ===== REAL SERVICE CONFIGURATION =====
# Real services may have delays and rate limits
API_TIMEOUT=30000
MAX_RETRIES=3
RETRY_DELAY=1000
