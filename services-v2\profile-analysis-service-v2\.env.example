# Profile Analysis Service V2 Configuration
# Database Per Service Pattern - This service has its OWN database

# Service Configuration
SERVICE_NAME=profile-analysis-service-v2
SERVICE_PORT=4002
NODE_ENV=development
LOG_LEVEL=info

# Database Configuration (Profile Analysis Service Database ONLY)
DATABASE_URL=postgresql://postgres:password@localhost:5432/profile_analysis_service_v2

# JWT Configuration
JWT_SECRET=profile-analysis-service-v2-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=profile-analysis-service-v2
JWT_AUDIENCE=social-nft-platform

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:4010
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100
ENABLE_HELMET=true
ENABLE_CSRF=false

# Monitoring Configuration
ENABLE_METRICS=true
ENABLE_TRACING=true
ENABLE_HEALTH_CHECKS=true
METRICS_PORT=9092

# Logging Configuration
LOG_DIRECTORY=logs
ENABLE_CONSOLE_LOGGING=true
ENABLE_FILE_LOGGING=false
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false

# Database Configuration Details
DB_SSL=false
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=30000

# External Service URLs (API Communication Only)
USER_SERVICE_URL=http://localhost:4001
API_GATEWAY_URL=http://localhost:4010
API_GATEWAY_TIMEOUT=30000
API_GATEWAY_RETRIES=3

# AI/ML Configuration
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key
ANALYSIS_TIMEOUT=300000
MAX_CONCURRENT_ANALYSES=5

# Social Media API Keys
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
TWITTER_BEARER_TOKEN=your-twitter-bearer-token
INSTAGRAM_ACCESS_TOKEN=your-instagram-access-token
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Cache Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=1
CACHE_TTL=3600

# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=2
QUEUE_CONCURRENCY=3
