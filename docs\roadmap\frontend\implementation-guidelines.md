# Implementation Guidelines

## TRUE Template-First Approach

### Component Creation Process
1. **Create skeleton** (10-20 lines max)
2. **Add basic structure** (20-30 lines max)
3. **Implement functionality** (small chunks)
4. **Test component** (verify rendering)
5. **Integrate with system** (connect to pages)

### File Size Limits
- **Initial creation:** 30 lines maximum
- **Subsequent edits:** 20-30 lines maximum per operation
- **Break down large components** into smaller files

## Development Rules

### Systematic Approach
- ✅ Plan before implementing
- ✅ Create components incrementally
- ✅ Test each component individually
- ✅ Document issues and solutions
- ✅ Follow established patterns

### Error Prevention
- ✅ Use TRUE Template-First for all files
- ✅ Break complex tasks into micro-steps
- ✅ Verify each step before continuing
- ✅ Handle dependencies properly
- ✅ Test frequently during development

## Component Standards

### Structure Template
```typescript
'use client'

import { Box } from '@chakra-ui/react'

export default function ComponentName() {
  return <Box>Component content</Box>
}
```

### Naming Conventions
- **Components:** PascalCase (e.g., ProjectCard.tsx)
- **Props:** camelCase (e.g., projectId)
- **Files:** kebab-case for pages (e.g., project-detail)
- **Directories:** lowercase (e.g., components/projects)

## Next Steps
1. Implement Navigation System (Header, Footer, Layout)
2. Create Projects System (List, Detail, Filters)
3. Enhance Dashboard with new components
4. Build NFT management system
5. Implement marketplace functionality
