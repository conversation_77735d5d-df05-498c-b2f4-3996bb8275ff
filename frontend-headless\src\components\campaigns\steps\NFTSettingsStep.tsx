'use client'

import React from 'react'
import {
  SparklesIcon,
  SwatchIcon,
  CogIcon,
  InformationCircleIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import { CreateCampaignRequest, NFTStyle } from '@/types/campaign.types'

interface NFTSettingsStepProps {
  data: Partial<CreateCampaignRequest>
  updateData: (updates: Partial<CreateCampaignRequest>) => void
  errors: string[]
}

export default function NFTSettingsStep({ data, updateData, errors }: NFTSettingsStepProps) {
  const nftStyles = [
    {
      style: NFTStyle.REALISTIC,
      label: 'Realistic',
      description: 'Photorealistic portraits and artwork',
      preview: '🖼️'
    },
    {
      style: NFTStyle.CARTOON,
      label: 'Cartoon',
      description: 'Fun, animated cartoon style',
      preview: '🎨'
    },
    {
      style: NFTStyle.ABSTRACT,
      label: 'Abstract',
      description: 'Modern abstract art style',
      preview: '🌈'
    },
    {
      style: NFTStyle.PIXEL_ART,
      label: 'Pixel Art',
      description: '8-bit retro pixel art style',
      preview: '🕹️'
    },
    {
      style: NFTStyle.MINIMALIST,
      label: 'Minimalist',
      description: 'Clean, simple minimalist design',
      preview: '⚪'
    },
    {
      style: NFTStyle.CYBERPUNK,
      label: 'Cyberpunk',
      description: 'Futuristic cyberpunk aesthetic',
      preview: '🤖'
    },
    {
      style: NFTStyle.FANTASY,
      label: 'Fantasy',
      description: 'Magical fantasy themes',
      preview: '🧙'
    },
    {
      style: NFTStyle.CUSTOM,
      label: 'Custom',
      description: 'Define your own custom style',
      preview: '⚙️'
    }
  ]

  const themes = [
    'Default', 'Space', 'Ocean', 'Forest', 'City', 'Desert', 'Mountains', 
    'Neon', 'Vintage', 'Steampunk', 'Gothic', 'Tropical', 'Arctic', 'Custom'
  ]

  const blockchains = [
    { value: 'ethereum', label: 'Ethereum', symbol: 'ETH' },
    { value: 'polygon', label: 'Polygon', symbol: 'MATIC' },
    { value: 'binance', label: 'Binance Smart Chain', symbol: 'BNB' },
    { value: 'avalanche', label: 'Avalanche', symbol: 'AVAX' },
    { value: 'solana', label: 'Solana', symbol: 'SOL' },
    { value: 'arbitrum', label: 'Arbitrum', symbol: 'ETH' },
    { value: 'optimism', label: 'Optimism', symbol: 'ETH' }
  ]

  const colorPalettes = [
    { name: 'Vibrant', colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'] },
    { name: 'Pastel', colors: ['#FFB3BA', '#BAFFC9', '#BAE1FF', '#FFFFBA', '#FFD1FF'] },
    { name: 'Dark', colors: ['#2C3E50', '#34495E', '#7F8C8D', '#95A5A6', '#BDC3C7'] },
    { name: 'Neon', colors: ['#FF073A', '#39FF14', '#00FFFF', '#FF1493', '#FFFF00'] },
    { name: 'Earth', colors: ['#8B4513', '#228B22', '#4682B4', '#DAA520', '#CD853F'] }
  ]

  const updateNFTSettings = (updates: any) => {
    updateData({
      nftSettings: {
        ...data.nftSettings,
        ...updates
      }
    })
  }

  const updateCustomization = (updates: any) => {
    updateNFTSettings({
      customization: {
        ...data.nftSettings?.customization,
        ...updates
      }
    })
  }

  const updateRarityThreshold = (rarity: string, value: number) => {
    updateNFTSettings({
      rarityThresholds: {
        ...data.nftSettings?.rarityThresholds,
        [rarity]: value
      }
    })
  }

  const addCustomPrompt = () => {
    const prompts = data.nftSettings?.customization?.customPrompts || []
    updateCustomization({
      customPrompts: [...prompts, '']
    })
  }

  const updateCustomPrompt = (index: number, value: string) => {
    const prompts = [...(data.nftSettings?.customization?.customPrompts || [])]
    prompts[index] = value
    updateCustomization({ customPrompts: prompts })
  }

  const removeCustomPrompt = (index: number) => {
    const prompts = [...(data.nftSettings?.customization?.customPrompts || [])]
    prompts.splice(index, 1)
    updateCustomization({ customPrompts: prompts })
  }

  const addColorToPalette = (type: 'background' | 'primary' | 'accent', color: string) => {
    const currentColors = data.nftSettings?.customization?.[`${type}Colors`] || []
    if (!currentColors.includes(color)) {
      updateCustomization({
        [`${type}Colors`]: [...currentColors, color]
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* NFT Generation Toggle */}
      <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center space-x-3">
          <SparklesIcon className="h-6 w-6 text-blue-600" />
          <div>
            <h3 className="text-lg font-medium text-blue-900">NFT Generation</h3>
            <p className="text-sm text-blue-700">Generate unique NFTs for campaign participants</p>
          </div>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={data.nftSettings?.enabled || false}
            onChange={(e) => updateNFTSettings({ enabled: e.target.checked })}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
        </label>
      </div>

      {data.nftSettings?.enabled && (
        <>
          {/* NFT Style Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              <SwatchIcon className="inline h-4 w-4 mr-1" />
              NFT Art Style
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {nftStyles.map((styleOption) => (
                <div
                  key={styleOption.style}
                  onClick={() => updateNFTSettings({ style: styleOption.style })}
                  className={`relative p-4 border rounded-lg cursor-pointer transition-all hover:border-blue-300 ${
                    data.nftSettings?.style === styleOption.style
                      ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-3xl mb-2">{styleOption.preview}</div>
                    <h4 className="text-sm font-medium text-gray-900">{styleOption.label}</h4>
                    <p className="text-xs text-gray-600 mt-1">{styleOption.description}</p>
                  </div>
                  {data.nftSettings?.style === styleOption.style && (
                    <div className="absolute top-2 right-2">
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Theme Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Theme
            </label>
            <select
              value={data.nftSettings?.theme || 'Default'}
              onChange={(e) => updateNFTSettings({ theme: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            >
              {themes.map((theme) => (
                <option key={theme} value={theme}>{theme}</option>
              ))}
            </select>
          </div>

          {/* Rarity Thresholds */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Rarity Thresholds (Score Requirements)
            </label>
            <div className="grid grid-cols-5 gap-3">
              {['common', 'rare', 'epic', 'legendary', 'mythic'].map((rarity) => (
                <div key={rarity}>
                  <label className="block text-xs text-gray-600 mb-1 capitalize">{rarity}</label>
                  <input
                    type="number"
                    value={data.nftSettings?.rarityThresholds?.[rarity] || 0}
                    onChange={(e) => updateRarityThreshold(rarity, parseInt(e.target.value))}
                    min="0"
                    max="100"
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                </div>
              ))}
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Set minimum scores required for each rarity level
            </p>
          </div>

          {/* Color Customization */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Color Palette
            </label>
            
            {/* Preset Palettes */}
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">Quick palettes:</p>
              <div className="flex flex-wrap gap-2">
                {colorPalettes.map((palette) => (
                  <button
                    key={palette.name}
                    onClick={() => {
                      updateCustomization({
                        backgroundColors: [palette.colors[0]],
                        primaryColors: [palette.colors[1]],
                        accentColors: [palette.colors[2]]
                      })
                    }}
                    className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <div className="flex space-x-1">
                      {palette.colors.slice(0, 3).map((color, index) => (
                        <div
                          key={index}
                          className="w-4 h-4 rounded-full border border-gray-300"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <span className="text-sm">{palette.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Custom Colors */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {['background', 'primary', 'accent'].map((type) => (
                <div key={type}>
                  <label className="block text-sm font-medium text-gray-700 mb-2 capitalize">
                    {type} Colors
                  </label>
                  <div className="space-y-2">
                    {(data.nftSettings?.customization?.[`${type}Colors`] || []).map((color, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <input
                          type="color"
                          value={color}
                          onChange={(e) => {
                            const colors = [...(data.nftSettings?.customization?.[`${type}Colors`] || [])]
                            colors[index] = e.target.value
                            updateCustomization({ [`${type}Colors`]: colors })
                          }}
                          className="w-8 h-8 border border-gray-300 rounded"
                        />
                        <input
                          type="text"
                          value={color}
                          onChange={(e) => {
                            const colors = [...(data.nftSettings?.customization?.[`${type}Colors`] || [])]
                            colors[index] = e.target.value
                            updateCustomization({ [`${type}Colors`]: colors })
                          }}
                          className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                        />
                        <button
                          onClick={() => {
                            const colors = [...(data.nftSettings?.customization?.[`${type}Colors`] || [])]
                            colors.splice(index, 1)
                            updateCustomization({ [`${type}Colors`]: colors })
                          }}
                          className="text-red-500 hover:text-red-700"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                    <button
                      onClick={() => addColorToPalette(type as any, '#000000')}
                      className="text-sm text-blue-600 hover:text-blue-500"
                    >
                      + Add {type} color
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Custom Prompts */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Custom AI Prompts
            </label>
            <div className="space-y-3">
              {(data.nftSettings?.customization?.customPrompts || []).map((prompt, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={prompt}
                    onChange={(e) => updateCustomPrompt(index, e.target.value)}
                    placeholder="Enter custom prompt for AI generation..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                  <button
                    onClick={() => removeCustomPrompt(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    ×
                  </button>
                </div>
              ))}
              <button
                onClick={addCustomPrompt}
                className="text-sm text-blue-600 hover:text-blue-500"
              >
                + Add custom prompt
              </button>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Add specific prompts to guide AI generation (e.g., "wearing sunglasses", "in space setting")
            </p>
          </div>

          {/* Blockchain Settings */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              <CogIcon className="inline h-4 w-4 mr-1" />
              Blockchain Configuration
            </label>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Blockchain Network
                </label>
                <select
                  value={data.nftSettings?.blockchain || 'ethereum'}
                  onChange={(e) => updateNFTSettings({ blockchain: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {blockchains.map((blockchain) => (
                    <option key={blockchain.value} value={blockchain.value}>
                      {blockchain.label} ({blockchain.symbol})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contract Address (Optional)
                </label>
                <input
                  type="text"
                  value={data.nftSettings?.contractAddress || ''}
                  onChange={(e) => updateNFTSettings({ contractAddress: e.target.value })}
                  placeholder="0x... (leave empty for auto-deployment)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Generation Settings */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Generation Settings
            </label>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Batch Size
                </label>
                <input
                  type="number"
                  value={data.nftSettings?.batchSize || 10}
                  onChange={(e) => updateNFTSettings({ batchSize: parseInt(e.target.value) })}
                  min="1"
                  max="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">NFTs generated per batch</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Generation Delay (ms)
                </label>
                <input
                  type="number"
                  value={data.nftSettings?.generationDelay || 1000}
                  onChange={(e) => updateNFTSettings({ generationDelay: parseInt(e.target.value) })}
                  min="100"
                  max="10000"
                  step="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">Delay between generations</p>
              </div>

              <div className="flex items-end">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={data.nftSettings?.autoMint || false}
                    onChange={(e) => updateNFTSettings({ autoMint: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Auto-mint NFTs</span>
                </label>
              </div>
            </div>
          </div>

          {/* Preview Section */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <EyeIcon className="h-4 w-4 mr-2" />
              NFT Configuration Preview
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
              <div>
                <span className="font-medium">Style:</span> {data.nftSettings?.style || 'Not set'}
              </div>
              <div>
                <span className="font-medium">Theme:</span> {data.nftSettings?.theme || 'Not set'}
              </div>
              <div>
                <span className="font-medium">Blockchain:</span> {
                  blockchains.find(b => b.value === data.nftSettings?.blockchain)?.label || 'Not set'
                }
              </div>
              <div>
                <span className="font-medium">Auto-mint:</span> {data.nftSettings?.autoMint ? 'Yes' : 'No'}
              </div>
            </div>
            
            {data.nftSettings?.customization?.customPrompts && data.nftSettings.customization.customPrompts.length > 0 && (
              <div className="mt-3">
                <span className="font-medium text-gray-700">Custom Prompts:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {data.nftSettings.customization.customPrompts.map((prompt, index) => (
                    <span key={index} className="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                      {prompt}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </>
      )}

      {/* Help Text */}
      <div className="bg-purple-50 border border-purple-200 rounded-md p-4">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-purple-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-purple-800">NFT Generation Tips</h3>
            <div className="mt-2 text-sm text-purple-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Choose art styles that match your brand and audience</li>
                <li>Set rarity thresholds to create meaningful progression</li>
                <li>Use custom prompts to add unique elements to your NFTs</li>
                <li>Consider gas costs when choosing blockchain networks</li>
                <li>Test generation settings with small batches first</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
