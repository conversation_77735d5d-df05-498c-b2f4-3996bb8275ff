# Frontend Integration Guide

## 🎯 **Overview**

This guide provides step-by-step instructions for integrating the React/Next.js frontend with the Social NFT Platform backend. The backend is 85% operational with all core authentication and data retrieval endpoints working.

## 🏗️ **Backend Status Summary**

### ✅ **Working Backend Endpoints (Ready for Frontend)**
```bash
# Authentication
POST /auth/register     # User registration
POST /auth/login        # User authentication
GET  /auth/profile      # Get user profile (with JWT)

# Campaign Management
GET  /api/campaigns     # Get all active campaigns
GET  /api/campaigns/{id} # Get specific campaign details

# Profile Analysis
GET  /twitter-analysis/history # Get analysis history

# NFT Management
GET  /api/nft-generation/user/{userId} # Get user's NFTs

# Service Health
GET  /health           # Health check for all services
```

### 🔄 **Pending Backend Endpoints (Parallel Development)**
```bash
# These will be fixed while frontend development continues
POST /twitter-analysis/analyze      # Profile analysis
POST /api/campaigns/{id}/join       # Campaign joining
POST /api/nft-generation/generate   # NFT generation
```

## 🚀 **Frontend Integration Steps**

### **Step 1: Environment Setup**

1. **Check Frontend Directory**
```bash
cd frontend
npm install
```

2. **Configure API Base URL**
```typescript
// frontend/src/config/api.ts
export const API_CONFIG = {
  BASE_URL: 'http://localhost:3010', // API Gateway
  ENDPOINTS: {
    AUTH: {
      REGISTER: '/auth/register',
      LOGIN: '/auth/login',
      PROFILE: '/auth/profile'
    },
    CAMPAIGNS: {
      LIST: '/api/campaigns',
      DETAILS: '/api/campaigns'
    },
    NFTS: {
      USER_NFTS: '/api/nft-generation/user'
    },
    ANALYSIS: {
      HISTORY: '/twitter-analysis/history'
    }
  }
};
```

### **Step 2: Authentication Integration**

1. **Create Auth Service**
```typescript
// frontend/src/services/authService.ts
import axios from 'axios';
import { API_CONFIG } from '../config/api';

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  twitterUsername?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export class AuthService {
  private baseURL = API_CONFIG.BASE_URL;

  async register(data: RegisterData) {
    const response = await axios.post(
      `${this.baseURL}${API_CONFIG.ENDPOINTS.AUTH.REGISTER}`,
      data
    );
    return response.data;
  }

  async login(data: LoginData) {
    const response = await axios.post(
      `${this.baseURL}${API_CONFIG.ENDPOINTS.AUTH.LOGIN}`,
      data
    );

    // Store JWT token
    if (response.data.accessToken) {
      localStorage.setItem('token', response.data.accessToken);
      this.setAuthHeader(response.data.accessToken);
    }

    return response.data;
  }

  async getProfile() {
    const response = await axios.get(
      `${this.baseURL}${API_CONFIG.ENDPOINTS.AUTH.PROFILE}`
    );
    return response.data;
  }

  setAuthHeader(token: string) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  logout() {
    localStorage.removeItem('token');
    delete axios.defaults.headers.common['Authorization'];
  }

  getToken() {
    return localStorage.getItem('token');
  }

  isAuthenticated() {
    return !!this.getToken();
  }
}

export const authService = new AuthService();
```

2. **Create Auth Context**
```typescript
// frontend/src/contexts/AuthContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '../services/authService';

interface User {
  id: string;
  username: string;
  email: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (data: any) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const token = authService.getToken();
    if (token) {
      authService.setAuthHeader(token);
      // Optionally fetch user profile
      fetchUserProfile();
    } else {
      setIsLoading(false);
    }
  }, []);

  const fetchUserProfile = async () => {
    try {
      const userData = await authService.getProfile();
      setUser(userData);
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      authService.logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    const response = await authService.login({ email, password });
    setUser(response.user);
  };

  const register = async (data: any) => {
    const response = await authService.register(data);
    setUser(response.user);
  };

  const logout = () => {
    authService.logout();
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### **Step 3: Campaign Integration**

```typescript
// frontend/src/services/campaignService.ts
import axios from 'axios';
import { API_CONFIG } from '../config/api';

export interface Campaign {
  id: string;
  name: string;
  description: string;
  status: string;
  startDate: string;
  endDate: string;
  participantCount: number;
  maxParticipants: number;
}

export class CampaignService {
  private baseURL = API_CONFIG.BASE_URL;

  async getCampaigns(): Promise<Campaign[]> {
    const response = await axios.get(
      `${this.baseURL}${API_CONFIG.ENDPOINTS.CAMPAIGNS.LIST}`
    );
    return response.data;
  }

  async getCampaignById(id: string): Promise<Campaign> {
    const response = await axios.get(
      `${this.baseURL}${API_CONFIG.ENDPOINTS.CAMPAIGNS.DETAILS}/${id}`
    );
    return response.data;
  }
}

export const campaignService = new CampaignService();
```

### **Step 4: NFT Integration**

```typescript
// frontend/src/services/nftService.ts
import axios from 'axios';
import { API_CONFIG } from '../config/api';

export interface NFT {
  id: string;
  userId: string;
  campaignId: string;
  twitterHandle: string;
  name: string;
  description: string;
  imageUrl: string;
  rarity: string;
  currentScore: number;
  isMinted: boolean;
  createdAt: string;
}

export class NFTService {
  private baseURL = API_CONFIG.BASE_URL;

  async getUserNFTs(userId: string): Promise<NFT[]> {
    const response = await axios.get(
      `${this.baseURL}${API_CONFIG.ENDPOINTS.NFTS.USER_NFTS}/${userId}`
    );
    return response.data;
  }
}

export const nftService = new NFTService();
```

### **Step 5: Main App Integration**

```typescript
// frontend/src/App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { Navbar } from './components/layout/Navbar';
import { HomePage } from './pages/HomePage';
import { LoginPage } from './pages/LoginPage';
import { RegisterPage } from './pages/RegisterPage';
import { CampaignsPage } from './pages/CampaignsPage';
import { NFTsPage } from './pages/NFTsPage';
import { ProfilePage } from './pages/ProfilePage';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Navbar />
          <main>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="/campaigns" element={<CampaignsPage />} />
              <Route path="/nfts" element={<NFTsPage />} />
              <Route path="/profile" element={<ProfilePage />} />
            </Routes>
          </main>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
```

## 🎯 **Implementation Priority**

### **Phase 1: Core Authentication (Immediate)**
1. ✅ User registration form
2. ✅ User login form
3. ✅ JWT token management
4. ✅ Protected routes
5. ✅ User profile display

### **Phase 2: Campaign Browsing (Next)**
1. ✅ Campaign list display
2. ✅ Campaign details view
3. 🔄 Campaign joining (when backend POST is fixed)

### **Phase 3: NFT Management (Following)**
1. ✅ NFT gallery display
2. ✅ NFT details view
3. 🔄 NFT generation (when backend POST is fixed)

### **Phase 4: Advanced Features (Future)**
1. 🔄 Profile analysis integration
2. 🔄 NFT evolution tracking
3. 🔄 Marketplace integration

## 🚀 **Getting Started**

1. **Start Backend Services** (if not running)
```bash
# In separate terminals:
cd services/user-service && npm run start:dev
cd services/profile-analysis-service && npm run start:dev
cd services/project-service && npm run start:dev
cd services/nft-generation-service && npm run start:dev
cd services/api-gateway && npm run start:dev
```

2. **Start Frontend Development**
```bash
cd frontend
npm install
npm run dev
```

3. **Test Integration**
- Visit http://localhost:3000
- Test user registration/login
- Browse campaigns
- View NFT gallery

## 📋 **Next Steps**

1. **Implement authentication UI components**
2. **Create campaign browsing interface**
3. **Build NFT gallery and display**
4. **Add error handling and loading states**
5. **Implement responsive design**

---

**Created:** December 19, 2024
**Status:** Ready for Frontend Development
**Backend Compatibility:** 85% operational, core features working
