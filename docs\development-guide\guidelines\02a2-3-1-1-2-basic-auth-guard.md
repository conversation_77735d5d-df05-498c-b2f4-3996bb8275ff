# 🛡️ Basic Authentication Guard

## Overview
Simple authentication guard for protecting routes across all services.

## Basic Auth Guard Implementation

### 1. Simple JWT Auth Guard
```typescript
// shared/guards/auth.guard.ts
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { LoggerService } from '../services/logger.service';
import { AppConfigService } from '../services/config.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: AppConfigService,
    private readonly logger: LoggerService,
    private readonly reflector: Reflector,
  ) {
    this.logger.setContext(AuthGuard.name);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is public
    const isPublic = this.reflector.get<boolean>('isPublic', context.getHandler());
    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      this.logger.warn('No token provided in request');
      throw new UnauthorizedException('Access token required');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: this.configService.getJwtConfig().secret,
      });

      // Attach user to request
      request.user = payload;
      
      this.logger.debug('Token validated successfully', {
        userId: payload.sub,
        username: payload.username,
      });

      return true;
    } catch (error) {
      this.logger.warn('Token validation failed', {
        error: error.message,
        token: token.substring(0, 10) + '...',
      });
      
      throw new UnauthorizedException('Invalid access token');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

### 2. Public Route Decorator
```typescript
// shared/decorators/public.decorator.ts
import { SetMetadata } from '@nestjs/common';

export const IS_PUBLIC_KEY = 'isPublic';
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);
```

### 3. User Decorator for Request Context
```typescript
// shared/decorators/user.decorator.ts
import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const User = createParamDecorator(
  (data: string, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;

    return data ? user?.[data] : user;
  },
);
```

## Usage Examples

### 1. Protecting Controller Routes
```typescript
// feature/controllers/feature.controller.ts
import { Controller, Get, UseGuards } from '@nestjs/common';
import { AuthGuard } from '../../shared/guards/auth.guard';
import { Public } from '../../shared/decorators/public.decorator';
import { User } from '../../shared/decorators/user.decorator';

@Controller('feature')
@UseGuards(AuthGuard)
export class FeatureController {
  
  @Get('public')
  @Public()
  getPublicData() {
    return { message: 'This is public data' };
  }

  @Get('protected')
  getProtectedData(@User() user: any) {
    return { 
      message: 'This is protected data',
      userId: user.sub,
      username: user.username,
    };
  }

  @Get('user-id')
  getUserId(@User('sub') userId: string) {
    return { userId };
  }
}
```

### 2. Global Guard Setup
```typescript
// app.module.ts
import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { AuthGuard } from './shared/guards/auth.guard';

@Module({
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
})
export class AppModule {}
```

## JWT Module Configuration

### Add JWT Module to Shared Module
```typescript
// shared/shared.module.ts
import { Global, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AppConfigService } from './services/config.service';

@Global()
@Module({
  imports: [
    JwtModule.registerAsync({
      useFactory: (configService: AppConfigService) => ({
        secret: configService.getJwtConfig().secret,
        signOptions: {
          expiresIn: configService.getJwtConfig().expiresIn,
        },
      }),
      inject: [AppConfigService],
    }),
  ],
  providers: [
    // ... other providers
    AuthGuard,
  ],
  exports: [
    // ... other exports
    AuthGuard,
    JwtModule,
  ],
})
export class SharedModule {}
```

## AI Agent Implementation Rules

### Guard Creation Steps
1. Implement CanActivate interface
2. Inject required dependencies (JwtService, ConfigService, Logger)
3. Check for public route metadata
4. Extract and validate JWT token
5. Attach user payload to request
6. Handle errors with appropriate exceptions

### Security Best Practices
- Always validate token signature
- Log authentication attempts (success/failure)
- Don't expose sensitive token data in logs
- Use proper HTTP status codes (401 for unauthorized)
- Support public routes with decorator

### Implementation Checklist
- [ ] Guard implements CanActivate interface
- [ ] JWT token extraction from Authorization header
- [ ] Token validation with proper secret
- [ ] User payload attached to request object
- [ ] Public route decorator support
- [ ] Proper error handling and logging
- [ ] User decorator for easy access to user data

### Testing Considerations
- Mock JwtService for unit tests
- Test both valid and invalid tokens
- Verify public routes bypass authentication
- Test user data extraction from request
- Validate error responses for unauthorized access

## Error Handling

### Common Authentication Errors
```typescript
// No token provided
throw new UnauthorizedException('Access token required');

// Invalid token format
throw new UnauthorizedException('Invalid token format');

// Expired token
throw new UnauthorizedException('Token has expired');

// Invalid signature
throw new UnauthorizedException('Invalid access token');
```

This basic authentication guard provides a solid foundation for securing routes across all microservices while maintaining simplicity and consistency.
