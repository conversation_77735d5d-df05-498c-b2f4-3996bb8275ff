'use client'

import React, { useState } from 'react'
import {
  CircleStackIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlusIcon,
  EyeIcon,
  BoltIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  CogIcon
} from '@heroicons/react/24/outline'
import {
  useDatabaseMetrics,
  useSlowQueries,
  useOptimizeQuery,
  useIndexAnalysis,
  useCreateIndex
} from '@/hooks/usePerformance'

interface DatabaseOptimizationProps {
  className?: string
}

export default function DatabaseOptimization({
  className = ''
}: DatabaseOptimizationProps) {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'queries' | 'indexes' | 'optimization'>('overview')
  const [showCreateIndexModal, setShowCreateIndexModal] = useState(false)
  const [newIndex, setNewIndex] = useState({
    table: '',
    columns: [''],
    options: {}
  })

  const { data: dbMetrics, isLoading: metricsLoading } = useDatabaseMetrics()
  const { data: slowQueries = [], isLoading: queriesLoading } = useSlowQueries(20)
  const { data: indexAnalysis, isLoading: indexLoading } = useIndexAnalysis()
  const optimizeQueryMutation = useOptimizeQuery()
  const createIndexMutation = useCreateIndex()

  // Mock data if not available
  const mockDbMetrics = {
    connections: {
      active: 25,
      idle: 15,
      total: 40,
      maxConnections: 100
    },
    queries: {
      total: 150000,
      slow: 45,
      failed: 12,
      averageTime: 12.5
    },
    performance: {
      queryTime: 12.5,
      lockTime: 0.8,
      indexHitRatio: 98.5,
      cacheHitRatio: 94.2
    },
    storage: {
      size: 2048,
      growth: 5.2,
      tableCount: 25,
      indexCount: 67
    },
    replication: {
      lag: 0.2,
      status: 'healthy'
    }
  }

  const mockSlowQueries = [
    {
      id: '1',
      query: 'SELECT n.*, u.username FROM nfts n JOIN users u ON n.owner_id = u.id WHERE n.collection_id = ? AND n.price BETWEEN ? AND ? ORDER BY n.created_at DESC',
      executionTime: 2.45,
      frequency: 1250,
      lastExecuted: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      database: 'nft_platform',
      table: 'nfts',
      type: 'SELECT',
      impact: 'high',
      suggestions: [
        'Add composite index on (collection_id, price, created_at)',
        'Consider partitioning by collection_id',
        'Optimize JOIN with covering index'
      ]
    },
    {
      id: '2',
      query: 'UPDATE users SET last_activity = NOW() WHERE id = ?',
      executionTime: 0.85,
      frequency: 5000,
      lastExecuted: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
      database: 'nft_platform',
      table: 'users',
      type: 'UPDATE',
      impact: 'medium',
      suggestions: [
        'Batch updates to reduce frequency',
        'Consider using Redis for session tracking',
        'Add index on id column if missing'
      ]
    },
    {
      id: '3',
      query: 'SELECT COUNT(*) FROM transactions WHERE created_at >= ? AND status = "completed"',
      executionTime: 1.25,
      frequency: 800,
      lastExecuted: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
      database: 'nft_platform',
      table: 'transactions',
      type: 'SELECT',
      impact: 'medium',
      suggestions: [
        'Add composite index on (created_at, status)',
        'Consider materialized view for counts',
        'Use approximate counting for real-time stats'
      ]
    }
  ]

  const mockIndexAnalysis = {
    totalIndexes: 67,
    unusedIndexes: 8,
    duplicateIndexes: 3,
    missingIndexes: 12,
    recommendations: [
      {
        type: 'create',
        table: 'nfts',
        columns: ['collection_id', 'price'],
        reason: 'Frequently used in WHERE clauses together',
        impact: 'high',
        estimatedImprovement: '60% faster queries'
      },
      {
        type: 'drop',
        table: 'users',
        columns: ['old_email_index'],
        reason: 'Index not used in last 30 days',
        impact: 'low',
        estimatedImprovement: 'Reduced storage overhead'
      },
      {
        type: 'modify',
        table: 'transactions',
        columns: ['status', 'created_at'],
        reason: 'Reorder columns for better selectivity',
        impact: 'medium',
        estimatedImprovement: '25% faster queries'
      }
    ],
    indexUsage: [
      {
        table: 'nfts',
        index: 'idx_collection_id',
        usage: 95.2,
        scans: 125000,
        seeks: 118900
      },
      {
        table: 'users',
        index: 'idx_username',
        usage: 87.5,
        scans: 45000,
        seeks: 39375
      },
      {
        table: 'transactions',
        index: 'idx_status',
        usage: 72.3,
        scans: 30000,
        seeks: 21690
      }
    ]
  }

  const displayMetrics = dbMetrics || mockDbMetrics
  const displayQueries = slowQueries.length > 0 ? slowQueries : mockSlowQueries
  const displayIndexAnalysis = indexAnalysis || mockIndexAnalysis

  const tabs = [
    { id: 'overview', name: 'Overview', icon: CircleStackIcon },
    { id: 'queries', name: 'Slow Queries', icon: ClockIcon, count: displayQueries.length },
    { id: 'indexes', name: 'Index Analysis', icon: MagnifyingGlassIcon },
    { id: 'optimization', name: 'Optimization', icon: BoltIcon }
  ]

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-red-600 bg-red-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'low':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const formatExecutionTime = (time: number) => {
    if (time >= 1000) return `${(time / 1000).toFixed(2)}s`
    return `${time.toFixed(2)}ms`
  }

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const handleOptimizeQuery = (queryId: string) => {
    optimizeQueryMutation.mutate(queryId)
  }

  const handleCreateIndex = () => {
    if (newIndex.table && newIndex.columns[0]) {
      createIndexMutation.mutate({
        table: newIndex.table,
        columns: newIndex.columns.filter(col => col.trim().length > 0),
        options: newIndex.options
      })
      setShowCreateIndexModal(false)
      setNewIndex({ table: '', columns: [''], options: {} })
    }
  }

  const addColumn = () => {
    setNewIndex(prev => ({
      ...prev,
      columns: [...prev.columns, '']
    }))
  }

  const updateColumn = (index: number, value: string) => {
    setNewIndex(prev => ({
      ...prev,
      columns: prev.columns.map((col, i) => i === index ? value : col)
    }))
  }

  const removeColumn = (index: number) => {
    setNewIndex(prev => ({
      ...prev,
      columns: prev.columns.filter((_, i) => i !== index)
    }))
  }

  if (metricsLoading || queriesLoading || indexLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900">Database Optimization</h2>

        <button
          onClick={() => setShowCreateIndexModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Index
        </button>
      </div>

      {/* Database Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Connections</p>
              <p className="text-2xl font-bold text-blue-600">
                {displayMetrics.connections.active}/{displayMetrics.connections.maxConnections}
              </p>
            </div>
            <CircleStackIcon className="h-8 w-8 text-blue-600" />
          </div>
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{
                  width: `${(displayMetrics.connections.active / displayMetrics.connections.maxConnections) * 100}%`
                }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Query Time</p>
              <p className="text-2xl font-bold text-green-600">
                {displayMetrics.queries.averageTime.toFixed(1)}ms
              </p>
            </div>
            <ClockIcon className="h-8 w-8 text-green-600" />
          </div>
          <div className="mt-2 text-xs text-gray-600">
            Target: &lt; 50ms
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Index Hit Ratio</p>
              <p className="text-2xl font-bold text-purple-600">
                {displayMetrics.performance.indexHitRatio.toFixed(1)}%
              </p>
            </div>
            <ChartBarIcon className="h-8 w-8 text-purple-600" />
          </div>
          <div className="mt-2 text-xs text-gray-600">
            Target: &gt; 95%
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Slow Queries</p>
              <p className="text-2xl font-bold text-red-600">
                {displayMetrics.queries.slow}
              </p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
          </div>
          <div className="mt-2 text-xs text-gray-600">
            Last 24 hours
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
              {tab.count !== undefined && tab.count > 0 && (
                <span className="ml-2 bg-red-100 text-red-900 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-96">
        {selectedTab === 'overview' && (
          <div className="space-y-6">
            {/* Database Statistics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Database Statistics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Queries (24h)</span>
                    <span className="text-sm font-medium text-gray-900">
                      {displayMetrics.queries.total.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Failed Queries</span>
                    <span className="text-sm font-medium text-gray-900">
                      {displayMetrics.queries.failed}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Database Size</span>
                    <span className="text-sm font-medium text-gray-900">
                      {formatBytes(displayMetrics.storage.size * 1024 * 1024)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Growth Rate</span>
                    <span className="text-sm font-medium text-gray-900">
                      {displayMetrics.storage.growth}% / month
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Tables</span>
                    <span className="text-sm font-medium text-gray-900">
                      {displayMetrics.storage.tableCount}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Indexes</span>
                    <span className="text-sm font-medium text-gray-900">
                      {displayMetrics.storage.indexCount}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Cache Hit Ratio</span>
                    <span className="text-sm font-medium text-gray-900">
                      {displayMetrics.performance.cacheHitRatio.toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Lock Time</span>
                    <span className="text-sm font-medium text-gray-900">
                      {displayMetrics.performance.lockTime.toFixed(1)}ms
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Replication Lag</span>
                    <span className="text-sm font-medium text-gray-900">
                      {displayMetrics.replication.lag.toFixed(1)}ms
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Replication Status</span>
                    <span className={`text-sm font-medium ${
                      displayMetrics.replication.status === 'healthy' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {displayMetrics.replication.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedTab === 'queries' && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Slow Queries Analysis</h3>

            {displayQueries.length > 0 ? (
              <div className="space-y-4">
                {displayQueries.map((query) => (
                  <div key={query.id} className="bg-white border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            getImpactColor(query.impact)
                          }`}>
                            {query.impact} impact
                          </span>
                          <span className="text-xs text-gray-500">{query.type}</span>
                          <span className="text-xs text-gray-500">{query.table}</span>
                        </div>

                        <div className="bg-gray-50 rounded p-3 mb-3">
                          <code className="text-sm text-gray-800 break-all">
                            {query.query}
                          </code>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                          <div className="text-center">
                            <div className="text-lg font-semibold text-red-600">
                              {formatExecutionTime(query.executionTime)}
                            </div>
                            <div className="text-xs text-gray-600">Execution Time</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold text-blue-600">
                              {query.frequency.toLocaleString()}
                            </div>
                            <div className="text-xs text-gray-600">Frequency (24h)</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold text-purple-600">
                              {new Date(query.lastExecuted).toLocaleTimeString()}
                            </div>
                            <div className="text-xs text-gray-600">Last Executed</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold text-orange-600">
                              {((query.executionTime * query.frequency) / 1000).toFixed(1)}s
                            </div>
                            <div className="text-xs text-gray-600">Total Impact</div>
                          </div>
                        </div>

                        {query.suggestions && query.suggestions.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Optimization Suggestions</h4>
                            <ul className="space-y-1">
                              {query.suggestions.map((suggestion, index) => (
                                <li key={index} className="text-sm text-gray-700 flex items-start">
                                  <span className="text-blue-600 mr-2">•</span>
                                  {suggestion}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>

                      <div className="ml-4">
                        <button
                          onClick={() => handleOptimizeQuery(query.id)}
                          className="inline-flex items-center px-3 py-1 border border-blue-600 text-xs font-medium rounded text-blue-600 bg-white hover:bg-blue-50"
                        >
                          <BoltIcon className="h-3 w-3 mr-1" />
                          Optimize
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                <CheckCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No slow queries detected</h3>
                <p className="mt-1 text-sm text-gray-500">
                  All queries are performing within acceptable thresholds.
                </p>
              </div>
            )}
          </div>
        )}

        {selectedTab === 'indexes' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Index Analysis</h3>

            {/* Index Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{displayIndexAnalysis.totalIndexes}</div>
                <div className="text-sm text-gray-600">Total Indexes</div>
              </div>
              <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-red-600">{displayIndexAnalysis.unusedIndexes}</div>
                <div className="text-sm text-gray-600">Unused Indexes</div>
              </div>
              <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-yellow-600">{displayIndexAnalysis.duplicateIndexes}</div>
                <div className="text-sm text-gray-600">Duplicate Indexes</div>
              </div>
              <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{displayIndexAnalysis.missingIndexes}</div>
                <div className="text-sm text-gray-600">Missing Indexes</div>
              </div>
            </div>

            {/* Index Recommendations */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Index Recommendations</h4>
              <div className="space-y-3">
                {displayIndexAnalysis.recommendations.map((rec, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            rec.type === 'create' ? 'bg-green-100 text-green-800' :
                            rec.type === 'drop' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {rec.type.toUpperCase()}
                          </span>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            getImpactColor(rec.impact)
                          }`}>
                            {rec.impact} impact
                          </span>
                        </div>

                        <div className="text-sm text-gray-900 mb-1">
                          <strong>Table:</strong> {rec.table} | <strong>Columns:</strong> {rec.columns.join(', ')}
                        </div>
                        <div className="text-sm text-gray-700 mb-2">{rec.reason}</div>
                        <div className="text-sm text-green-600">{rec.estimatedImprovement}</div>
                      </div>

                      {rec.type === 'create' && (
                        <button
                          onClick={() => {
                            setNewIndex({
                              table: rec.table,
                              columns: rec.columns,
                              options: {}
                            })
                            setShowCreateIndexModal(true)
                          }}
                          className="inline-flex items-center px-3 py-1 border border-green-600 text-xs font-medium rounded text-green-600 bg-white hover:bg-green-50"
                        >
                          <PlusIcon className="h-3 w-3 mr-1" />
                          Create
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Index Usage */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Index Usage Statistics</h4>
              <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Table
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Index
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Usage %
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Scans
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Seeks
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {displayIndexAnalysis.indexUsage.map((usage, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {usage.table}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {usage.index}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                              <div
                                className={`h-2 rounded-full ${
                                  usage.usage >= 80 ? 'bg-green-500' :
                                  usage.usage >= 50 ? 'bg-yellow-500' :
                                  'bg-red-500'
                                }`}
                                style={{ width: `${usage.usage}%` }}
                              ></div>
                            </div>
                            {usage.usage.toFixed(1)}%
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {usage.scans.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {usage.seeks.toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {selectedTab === 'optimization' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Database Optimization Tools</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Quick Actions */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h4 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h4>
                <div className="space-y-3">
                  <button className="w-full text-left p-3 border border-gray-200 rounded hover:bg-gray-50">
                    <div className="flex items-center">
                      <BoltIcon className="h-5 w-5 text-blue-600 mr-3" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">Analyze Query Performance</div>
                        <div className="text-xs text-gray-600">Run comprehensive query analysis</div>
                      </div>
                    </div>
                  </button>

                  <button className="w-full text-left p-3 border border-gray-200 rounded hover:bg-gray-50">
                    <div className="flex items-center">
                      <MagnifyingGlassIcon className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">Index Optimization</div>
                        <div className="text-xs text-gray-600">Optimize existing indexes</div>
                      </div>
                    </div>
                  </button>

                  <button className="w-full text-left p-3 border border-gray-200 rounded hover:bg-gray-50">
                    <div className="flex items-center">
                      <ChartBarIcon className="h-5 w-5 text-purple-600 mr-3" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">Statistics Update</div>
                        <div className="text-xs text-gray-600">Update table statistics</div>
                      </div>
                    </div>
                  </button>

                  <button className="w-full text-left p-3 border border-gray-200 rounded hover:bg-gray-50">
                    <div className="flex items-center">
                      <CogIcon className="h-5 w-5 text-orange-600 mr-3" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">Configuration Tuning</div>
                        <div className="text-xs text-gray-600">Optimize database settings</div>
                      </div>
                    </div>
                  </button>
                </div>
              </div>

              {/* Optimization History */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h4 className="text-lg font-medium text-gray-900 mb-4">Recent Optimizations</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded">
                    <div>
                      <div className="text-sm font-medium text-gray-900">Index created on nfts table</div>
                      <div className="text-xs text-gray-600">2 hours ago</div>
                    </div>
                    <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  </div>

                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded">
                    <div>
                      <div className="text-sm font-medium text-gray-900">Query optimization applied</div>
                      <div className="text-xs text-gray-600">1 day ago</div>
                    </div>
                    <CheckCircleIcon className="h-5 w-5 text-blue-600" />
                  </div>

                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded">
                    <div>
                      <div className="text-sm font-medium text-gray-900">Statistics updated</div>
                      <div className="text-xs text-gray-600">3 days ago</div>
                    </div>
                    <CheckCircleIcon className="h-5 w-5 text-yellow-600" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create Index Modal */}
      {showCreateIndexModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Create Database Index</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Table Name
                  </label>
                  <input
                    type="text"
                    value={newIndex.table}
                    onChange={(e) => setNewIndex(prev => ({ ...prev, table: e.target.value }))}
                    placeholder="Enter table name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Columns
                  </label>
                  <div className="space-y-2">
                    {newIndex.columns.map((column, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={column}
                          onChange={(e) => updateColumn(index, e.target.value)}
                          placeholder="Column name"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                        {newIndex.columns.length > 1 && (
                          <button
                            onClick={() => removeColumn(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            ×
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      onClick={addColumn}
                      className="text-blue-600 hover:text-blue-700 text-sm"
                    >
                      + Add Column
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowCreateIndexModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateIndex}
                  disabled={!newIndex.table || !newIndex.columns[0]}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  Create Index
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}