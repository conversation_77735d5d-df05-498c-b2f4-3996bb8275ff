'use client'

import React from 'react'
import {
  ChartBarIcon,
  UsersIcon,
  GiftIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ClockIcon,
  CurrencyDollarIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import { Campaign, CampaignStatus } from '@/types/campaign.types'

interface CampaignStatsProps {
  campaigns: Campaign[]
  className?: string
}

export default function CampaignStats({ campaigns, className = '' }: CampaignStatsProps) {
  const calculateStats = () => {
    const totalCampaigns = campaigns.length
    const activeCampaigns = campaigns.filter(c => c.status === CampaignStatus.ACTIVE).length
    const totalParticipants = campaigns.reduce((sum, c) => sum + c.participantCount, 0)
    const totalRewards = campaigns.reduce((sum, c) => sum + (c.totalRewardsDistributed || 0), 0)
    
    const completedCampaigns = campaigns.filter(c => c.status === CampaignStatus.COMPLETED)
    const avgCompletionRate = completedCampaigns.length > 0
      ? completedCampaigns.reduce((sum, c) => sum + (c.completionRate || 0), 0) / completedCampaigns.length
      : 0

    const featuredCampaigns = campaigns.filter(c => c.featured).length
    
    // Calculate engagement trends (mock data for now)
    const engagementTrend = Math.random() > 0.5 ? 'up' : 'down'
    const engagementChange = Math.floor(Math.random() * 20) + 1

    const participantTrend = Math.random() > 0.5 ? 'up' : 'down'
    const participantChange = Math.floor(Math.random() * 15) + 1

    return {
      totalCampaigns,
      activeCampaigns,
      totalParticipants,
      totalRewards,
      avgCompletionRate: avgCompletionRate * 100,
      featuredCampaigns,
      engagementTrend,
      engagementChange,
      participantTrend,
      participantChange
    }
  }

  const stats = calculateStats()

  const statCards = [
    {
      title: 'Total Campaigns',
      value: stats.totalCampaigns.toLocaleString(),
      icon: ChartBarIcon,
      color: 'text-blue-600 bg-blue-100',
      description: `${stats.activeCampaigns} currently active`
    },
    {
      title: 'Total Participants',
      value: stats.totalParticipants.toLocaleString(),
      icon: UsersIcon,
      color: 'text-green-600 bg-green-100',
      trend: stats.participantTrend,
      trendValue: `${stats.participantChange}%`,
      description: 'Across all campaigns'
    },
    {
      title: 'Rewards Distributed',
      value: stats.totalRewards.toLocaleString(),
      icon: GiftIcon,
      color: 'text-purple-600 bg-purple-100',
      description: 'Total rewards given out'
    },
    {
      title: 'Avg Completion Rate',
      value: `${stats.avgCompletionRate.toFixed(1)}%`,
      icon: TrendingUpIcon,
      color: 'text-yellow-600 bg-yellow-100',
      trend: stats.engagementTrend,
      trendValue: `${stats.engagementChange}%`,
      description: 'Campaign completion rate'
    },
    {
      title: 'Featured Campaigns',
      value: stats.featuredCampaigns.toLocaleString(),
      icon: SparklesIcon,
      color: 'text-indigo-600 bg-indigo-100',
      description: 'Currently featured'
    },
    {
      title: 'Active This Month',
      value: stats.activeCampaigns.toLocaleString(),
      icon: ClockIcon,
      color: 'text-orange-600 bg-orange-100',
      description: 'Running campaigns'
    }
  ]

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Campaign Analytics</h2>
        <div className="text-sm text-gray-500">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((stat, index) => (
          <div
            key={index}
            className="relative bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all"
          >
            {/* Icon */}
            <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${stat.color} mb-4`}>
              <stat.icon className="h-6 w-6" />
            </div>

            {/* Value and Title */}
            <div className="mb-2">
              <div className="flex items-baseline space-x-2">
                <h3 className="text-2xl font-bold text-gray-900">{stat.value}</h3>
                {stat.trend && (
                  <div className={`flex items-center text-sm font-medium ${
                    stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.trend === 'up' ? (
                      <TrendingUpIcon className="h-4 w-4 mr-1" />
                    ) : (
                      <TrendingDownIcon className="h-4 w-4 mr-1" />
                    )}
                    {stat.trendValue}
                  </div>
                )}
              </div>
              <p className="text-sm font-medium text-gray-700">{stat.title}</p>
            </div>

            {/* Description */}
            <p className="text-sm text-gray-500">{stat.description}</p>

            {/* Trend Indicator */}
            {stat.trend && (
              <div className="absolute top-4 right-4">
                <div className={`w-2 h-2 rounded-full ${
                  stat.trend === 'up' ? 'bg-green-400' : 'bg-red-400'
                }`}></div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Quick Insights */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUpIcon className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Performance Highlight</span>
            </div>
            <p className="text-sm text-blue-800">
              {stats.avgCompletionRate > 70 
                ? `Excellent completion rate of ${stats.avgCompletionRate.toFixed(1)}% across campaigns`
                : stats.avgCompletionRate > 50
                ? `Good completion rate of ${stats.avgCompletionRate.toFixed(1)}% with room for improvement`
                : `Completion rate of ${stats.avgCompletionRate.toFixed(1)}% suggests need for campaign optimization`
              }
            </p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <UsersIcon className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">Engagement Status</span>
            </div>
            <p className="text-sm text-green-800">
              {stats.totalParticipants > 1000
                ? `Strong engagement with ${stats.totalParticipants.toLocaleString()} total participants`
                : stats.totalParticipants > 100
                ? `Growing community with ${stats.totalParticipants.toLocaleString()} participants`
                : `Building momentum with ${stats.totalParticipants.toLocaleString()} early participants`
              }
            </p>
          </div>
        </div>
      </div>

      {/* Campaign Health Score */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700">Overall Campaign Health</h3>
          <span className="text-xs text-gray-500">Based on completion rates and engagement</span>
        </div>
        
        <div className="relative">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Health Score</span>
            <span>{Math.round(stats.avgCompletionRate)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-500 ${
                stats.avgCompletionRate >= 80 ? 'bg-green-500' :
                stats.avgCompletionRate >= 60 ? 'bg-yellow-500' :
                stats.avgCompletionRate >= 40 ? 'bg-orange-500' : 'bg-red-500'
              }`}
              style={{ width: `${Math.min(100, stats.avgCompletionRate)}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Poor</span>
            <span>Good</span>
            <span>Excellent</span>
          </div>
        </div>
      </div>
    </div>
  )
}
