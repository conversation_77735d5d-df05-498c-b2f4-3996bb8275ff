'use client'

import React, { useState } from 'react'
import {
  LinkIcon,
  ArrowsRightLeftIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  InformationCircleIcon,
  PlayIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import {
  useBridges,
  useBridgeNFT,
  useBridgeHistory,
  useEstimateBridgeFee,
  useBridgeTransaction,
  useConnectedWallets
} from '@/hooks/useBlockchainIntegration'
import { CrossChainBridge as Bridge, BridgeTransaction, BlockchainNetwork } from '@/types/blockchain-integration.types'

interface CrossChainBridgeProps {
  className?: string
}

export default function CrossChainBridge({
  className = ''
}: CrossChainBridgeProps) {
  const [selectedBridge, setSelectedBridge] = useState<Bridge | null>(null)
  const [showBridgeModal, setShowBridgeModal] = useState(false)
  const [bridgeData, setBridgeData] = useState({
    nftContract: '',
    tokenId: '',
    sourceNetwork: BlockchainNetwork.ETHEREUM,
    targetNetwork: BlockchainNetwork.POLYGON,
    recipient: ''
  })

  const { data: bridges, isLoading: bridgesLoading } = useBridges()
  const { data: connectedWallets } = useConnectedWallets()
  const activeWallet = connectedWallets?.[0]
  
  const { data: bridgeHistory, isLoading: historyLoading } = useBridgeHistory(
    activeWallet?.address || ''
  )
  
  const bridgeNFTMutation = useBridgeNFT()
  const estimateFeeMutation = useEstimateBridgeFee()

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-blue-600" />
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
      default:
        return <ClockIcon className="h-5 w-5 text-yellow-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100'
      case 'processing':
        return 'text-blue-600 bg-blue-100'
      case 'failed':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-yellow-600 bg-yellow-100'
    }
  }

  const getSecurityColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'text-green-600 bg-green-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'low':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const handleBridgeNFT = async () => {
    if (!selectedBridge) return

    try {
      const transaction = await bridgeNFTMutation.mutateAsync({
        ...bridgeData,
        bridgeId: selectedBridge.id
      })
      
      setShowBridgeModal(false)
      setBridgeData({
        nftContract: '',
        tokenId: '',
        sourceNetwork: BlockchainNetwork.ETHEREUM,
        targetNetwork: BlockchainNetwork.POLYGON,
        recipient: ''
      })
    } catch (error) {
      console.error('Failed to bridge NFT:', error)
    }
  }

  const handleEstimateFee = async () => {
    if (!selectedBridge) return

    try {
      const estimate = await estimateFeeMutation.mutateAsync({
        ...bridgeData,
        bridgeId: selectedBridge.id
      })
      
      // Handle fee estimation result
      console.log('Bridge fee estimate:', estimate)
    } catch (error) {
      console.error('Failed to estimate bridge fee:', error)
    }
  }

  if (bridgesLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Cross-Chain Bridge</h2>
          <p className="text-sm text-gray-600">Bridge NFTs and assets across different blockchain networks</p>
        </div>
        
        <button
          onClick={() => setShowBridgeModal(true)}
          disabled={!activeWallet}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
        >
          <LinkIcon className="h-4 w-4 mr-2" />
          Bridge Asset
        </button>
      </div>

      {/* Available Bridges */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Available Bridges</h3>
        
        {bridges && bridges.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {bridges.map((bridge) => (
              <BridgeCard
                key={bridge.id}
                bridge={bridge}
                onSelect={() => {
                  setSelectedBridge(bridge)
                  setBridgeData(prev => ({
                    ...prev,
                    sourceNetwork: bridge.sourceNetwork,
                    targetNetwork: bridge.targetNetwork
                  }))
                  setShowBridgeModal(true)
                }}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
            <LinkIcon className="mx-auto h-8 w-8 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No bridges available</h3>
            <p className="mt-1 text-sm text-gray-500">
              Cross-chain bridges will appear here when available.
            </p>
          </div>
        )}
      </div>

      {/* Bridge History */}
      {bridgeHistory && bridgeHistory.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Bridge History</h3>
          
          <div className="space-y-4">
            {bridgeHistory.slice(0, 5).map((transaction) => (
              <BridgeTransactionCard
                key={transaction.id}
                transaction={transaction}
              />
            ))}
          </div>
        </div>
      )}

      {/* Bridge Statistics */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Bridge Statistics</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{bridges?.length || 0}</div>
            <div className="text-sm text-gray-600">Available Bridges</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {bridges?.filter(b => b.isActive).length || 0}
            </div>
            <div className="text-sm text-gray-600">Active Bridges</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {bridgeHistory?.length || 0}
            </div>
            <div className="text-sm text-gray-600">Your Bridges</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {bridgeHistory?.filter(tx => tx.status === 'completed').length || 0}
            </div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
        </div>
      </div>

      {/* Bridge NFT Modal */}
      {showBridgeModal && selectedBridge && (
        <BridgeNFTModal
          bridge={selectedBridge}
          bridgeData={bridgeData}
          onDataChange={setBridgeData}
          onClose={() => {
            setShowBridgeModal(false)
            setSelectedBridge(null)
          }}
          onBridge={handleBridgeNFT}
          onEstimateFee={handleEstimateFee}
          isLoading={bridgeNFTMutation.isPending}
          feeEstimate={estimateFeeMutation.data}
        />
      )}
    </div>
  )
}

interface BridgeCardProps {
  bridge: Bridge
  onSelect: () => void
}

function BridgeCard({ bridge, onSelect }: BridgeCardProps) {
  const getSecurityColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'text-green-600 bg-green-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'low':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">{bridge.name}</h3>
          <p className="text-sm text-gray-600">
            {bridge.sourceNetwork} → {bridge.targetNetwork}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSecurityColor(bridge.securityLevel)}`}>
            {bridge.securityLevel} security
          </span>
          
          {bridge.isActive ? (
            <CheckCircleIcon className="h-4 w-4 text-green-500" />
          ) : (
            <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Bridge Fee</div>
          <div className="text-sm text-gray-600">{parseFloat(bridge.bridgeFee).toFixed(4)} ETH</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Est. Time</div>
          <div className="text-sm text-gray-600">{Math.round(bridge.estimatedTime / 60)} min</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Success Rate</div>
          <div className="text-sm text-gray-600">{(bridge.successRate * 100).toFixed(1)}%</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Total Volume</div>
          <div className="text-sm text-gray-600">${parseFloat(bridge.totalVolume).toLocaleString()}</div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-xs text-gray-500">
          {bridge.totalTransactions.toLocaleString()} transactions
        </div>
        
        <button
          onClick={onSelect}
          disabled={!bridge.isActive}
          className="text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50"
        >
          Use Bridge
        </button>
      </div>
    </div>
  )
}

interface BridgeTransactionCardProps {
  transaction: BridgeTransaction
}

function BridgeTransactionCard({ transaction }: BridgeTransactionCardProps) {
  const { data: bridgeTransaction } = useBridgeTransaction(transaction.id)
  const currentTransaction = bridgeTransaction || transaction

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100'
      case 'processing':
        return 'text-blue-600 bg-blue-100'
      case 'failed':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-yellow-600 bg-yellow-100'
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="text-sm font-medium text-gray-900">
            Token #{currentTransaction.tokenAddress.slice(-6)}
          </h4>
          <p className="text-xs text-gray-600">
            {currentTransaction.sourceNetwork} → {currentTransaction.targetNetwork}
          </p>
        </div>
        
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(currentTransaction.status)}`}>
          {currentTransaction.status}
        </span>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-3">
        <div>
          <div className="text-xs font-medium text-gray-700">Amount</div>
          <div className="text-xs text-gray-600">{parseFloat(currentTransaction.amount).toFixed(4)}</div>
        </div>
        
        <div>
          <div className="text-xs font-medium text-gray-700">Bridge Fee</div>
          <div className="text-xs text-gray-600">{parseFloat(currentTransaction.bridgeFee).toFixed(4)}</div>
        </div>
        
        <div>
          <div className="text-xs font-medium text-gray-700">Progress</div>
          <div className="text-xs text-gray-600">{currentTransaction.progress}%</div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-xs text-gray-500">
          {new Date(currentTransaction.initiatedAt).toLocaleDateString()}
        </div>
        
        <div className="flex items-center space-x-2">
          {currentTransaction.sourceHash && (
            <a
              href={`#/tx/${currentTransaction.sourceHash}`}
              className="text-xs text-blue-600 hover:text-blue-700"
            >
              Source Tx
            </a>
          )}
          
          {currentTransaction.targetHash && (
            <a
              href={`#/tx/${currentTransaction.targetHash}`}
              className="text-xs text-blue-600 hover:text-blue-700"
            >
              Target Tx
            </a>
          )}
        </div>
      </div>
    </div>
  )
}

interface BridgeNFTModalProps {
  bridge: Bridge
  bridgeData: any
  onDataChange: (data: any) => void
  onClose: () => void
  onBridge: () => void
  onEstimateFee: () => void
  isLoading: boolean
  feeEstimate?: any
}

function BridgeNFTModal({
  bridge,
  bridgeData,
  onDataChange,
  onClose,
  onBridge,
  onEstimateFee,
  isLoading,
  feeEstimate
}: BridgeNFTModalProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onBridge()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Bridge NFT</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">×</button>
        </div>

        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
          <div className="text-sm font-medium text-blue-900">{bridge.name}</div>
          <div className="text-xs text-blue-700">
            {bridge.sourceNetwork} → {bridge.targetNetwork}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">NFT Contract Address</label>
            <input
              type="text"
              required
              value={bridgeData.nftContract}
              onChange={(e) => onDataChange(prev => ({ ...prev, nftContract: e.target.value }))}
              placeholder="0x..."
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Token ID</label>
            <input
              type="text"
              required
              value={bridgeData.tokenId}
              onChange={(e) => onDataChange(prev => ({ ...prev, tokenId: e.target.value }))}
              placeholder="1"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Recipient Address (Optional)</label>
            <input
              type="text"
              value={bridgeData.recipient}
              onChange={(e) => onDataChange(prev => ({ ...prev, recipient: e.target.value }))}
              placeholder="0x... (leave empty to use your address)"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <button
            type="button"
            onClick={onEstimateFee}
            className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Estimate Fees
          </button>

          {feeEstimate && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-900 mb-2">Fee Estimate</div>
              <div className="space-y-1 text-xs text-gray-600">
                <div className="flex justify-between">
                  <span>Bridge Fee:</span>
                  <span>{feeEstimate.bridgeFee} ETH</span>
                </div>
                <div className="flex justify-between">
                  <span>Source Gas:</span>
                  <span>{feeEstimate.gasFees.source} ETH</span>
                </div>
                <div className="flex justify-between">
                  <span>Target Gas:</span>
                  <span>{feeEstimate.gasFees.target} ETH</span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>Total:</span>
                  <span>{feeEstimate.totalFee} ETH</span>
                </div>
                <div className="flex justify-between">
                  <span>Est. Time:</span>
                  <span>{Math.round(feeEstimate.estimatedTime / 60)} min</span>
                </div>
              </div>
            </div>
          )}

          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Bridging...' : 'Bridge NFT'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
