import { <PERSON>, <PERSON>, Get, Body, Param, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { TwitterUserService } from '../../twitter/twitter-user.service';

@ApiTags('Twitter User Management')
@Controller('twitter-users')
export class TwitterUserController {
  private readonly logger = new Logger(TwitterUserController.name);

  constructor(private readonly twitterUserService: TwitterUserService) {}

  @Post('find-or-create')
  @ApiOperation({ summary: 'Find or create Twitter user' })
  @ApiResponse({ status: 200, description: 'User found or created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid Twitter data' })
  @ApiBody({
    description: 'Twitter user data from OAuth',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', description: 'Twitter user ID' },
        username: { type: 'string', description: 'Twitter username' },
        email: { type: 'string', description: 'User email' },
        displayName: { type: 'string', description: 'Display name' },
        profileImage: { type: 'string', description: 'Profile image URL' },
        followerCount: { type: 'number', description: 'Follower count' },
        followingCount: { type: 'number', description: 'Following count' },
        tweetCount: { type: 'number', description: 'Tweet count' },
        isVerified: { type: 'boolean', description: 'Verification status' },
        description: { type: 'string', description: 'Bio description' },
        location: { type: 'string', description: 'Location' }
      },
      required: ['id', 'username', 'email']
    }
  })
  async findOrCreateTwitterUser(@Body() twitterData: any) {
    try {
      this.logger.log('🐦 Twitter user find/create request received');
      this.logger.log(`📝 Twitter user: ${twitterData.username} (${twitterData.id})`);

      if (!twitterData.id || !twitterData.username || !twitterData.email) {
        this.logger.error('❌ Missing required Twitter user data');
        return {
          success: false,
          error: 'Missing required fields: id, username, email',
          timestamp: new Date().toISOString()
        };
      }

      const user = await this.twitterUserService.findOrCreateTwitterUser(twitterData);

      this.logger.log(`✅ Twitter user processed successfully: ${user.username}`);
      return {
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          displayName: user.displayName,
          profileImage: user.profileImage,
          provider: user.provider,
          providerId: user.providerId,
          isActive: user.isActive,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt
        },
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('❌ Error processing Twitter user:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User found' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(@Param('id') id: string) {
    try {
      this.logger.log(`🔍 Finding user by ID: ${id}`);

      const user = await this.twitterUserService.findById(id);

      if (!user) {
        this.logger.log(`❌ User not found: ${id}`);
        return {
          success: false,
          error: 'User not found',
          timestamp: new Date().toISOString()
        };
      }

      this.logger.log(`✅ User found: ${user.username}`);
      return {
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          displayName: user.displayName,
          profileImage: user.profileImage,
          provider: user.provider,
          providerId: user.providerId,
          isActive: user.isActive,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt
        },
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('❌ Error finding user by ID:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Get('provider/:providerId')
  @ApiOperation({ summary: 'Get user by provider ID' })
  @ApiResponse({ status: 200, description: 'User found' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserByProviderId(@Param('providerId') providerId: string) {
    try {
      this.logger.log(`🔍 Finding user by provider ID: ${providerId}`);

      const user = await this.twitterUserService.findByProviderId(providerId, 'twitter');

      if (!user) {
        this.logger.log(`❌ User not found by provider ID: ${providerId}`);
        return {
          success: false,
          error: 'User not found',
          timestamp: new Date().toISOString()
        };
      }

      this.logger.log(`✅ User found by provider ID: ${user.username}`);
      return {
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          displayName: user.displayName,
          profileImage: user.profileImage,
          provider: user.provider,
          providerId: user.providerId,
          isActive: user.isActive,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt
        },
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('❌ Error finding user by provider ID:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Get('stats/overview')
  @ApiOperation({ summary: 'Get user statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved' })
  async getUserStats() {
    try {
      this.logger.log('📊 Getting user statistics');

      const stats = await this.twitterUserService.getUserStats();

      this.logger.log(`✅ Statistics retrieved: ${stats.totalUsers} total users`);
      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('❌ Error getting user statistics:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Post(':id/update-login')
  @ApiOperation({ summary: 'Update user last login time' })
  @ApiResponse({ status: 200, description: 'Last login updated' })
  async updateLastLogin(@Param('id') id: string) {
    try {
      this.logger.log(`🔄 Updating last login for user: ${id}`);

      await this.twitterUserService.updateLastLogin(id);

      this.logger.log(`✅ Last login updated for user: ${id}`);
      return {
        success: true,
        message: 'Last login updated successfully',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('❌ Error updating last login:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}
