/**
 * Response Transform Interceptor
 * Automatically transforms all responses to follow the standardized format
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ResponseBuilder } from '../utils/response-builder.util';
import {
  BaseResponse,
  SuccessResponse,
  ErrorResponse,
  ResponseTransformContext,
} from '../interfaces/base-response.interface';

/**
 * Response Transform Interceptor
 * Ensures all responses follow the standardized format
 */
@Injectable()
export class ResponseTransformInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ResponseTransformInterceptor.name);

  constructor(private readonly configService: ConfigService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const startTime = Date.now();

    // Build transform context
    const transformContext = this.buildTransformContext(request, startTime);

    return next.handle().pipe(
      map((data) => this.transformSuccessResponse(data, transformContext, request, response)),
      catchError((error) => {
        this.logger.error('Response transform error', {
          error: error.message,
          stack: error.stack,
          correlationId: request.correlationId,
          path: request.path,
        });
        throw error; // Let the error filter handle it
      })
    );
  }

  /**
   * Transform success response to standardized format
   */
  private transformSuccessResponse(
    data: any,
    context: ResponseTransformContext,
    request: Request,
    response: Response
  ): SuccessResponse<any> {
    // If data is already a standardized response, return as-is
    if (this.isStandardizedResponse(data)) {
      this.addPerformanceMetrics(data, context);
      this.setResponseHeaders(response, data);
      return data;
    }

    // Handle different response types
    if (this.isPaginatedData(data)) {
      return this.transformPaginatedResponse(data, context, request, response);
    }

    // Transform regular data response
    const transformedResponse = ResponseBuilder.success(data, {
      correlationId: request.correlationId || context.service.name + '_' + Date.now(),
      service: context.service.name,
      version: context.service.version,
    });

    this.addPerformanceMetrics(transformedResponse, context);
    this.setResponseHeaders(response, transformedResponse);

    return transformedResponse;
  }

  /**
   * Transform paginated response
   */
  private transformPaginatedResponse(
    data: any,
    context: ResponseTransformContext,
    request: Request,
    response: Response
  ): SuccessResponse<any> {
    const { items, pagination, filters, sorting, search } = data;

    const transformedResponse = ResponseBuilder.paginated(
      items || data.data || [],
      pagination,
      {
        filters,
        sorting,
        search,
        correlationId: request.correlationId,
        service: context.service.name,
        version: context.service.version,
      }
    );

    this.addPerformanceMetrics(transformedResponse, context);
    this.setResponseHeaders(response, transformedResponse);

    return transformedResponse;
  }

  /**
   * Check if data is already a standardized response
   */
  private isStandardizedResponse(data: any): data is BaseResponse {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.success === 'boolean' &&
      data.correlationId &&
      data.timestamp &&
      data.service &&
      data.version
    );
  }

  /**
   * Check if data is paginated
   */
  private isPaginatedData(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      (data.pagination || (data.items && data.totalCount !== undefined))
    );
  }

  /**
   * Build transform context
   */
  private buildTransformContext(
    request: Request,
    startTime: number
  ): ResponseTransformContext {
    return {
      request: {
        method: request.method,
        path: request.path,
        query: request.query as Record<string, any>,
        headers: request.headers as Record<string, string>,
        user: request.user,
      },
      service: {
        name: this.configService.get<string>('SERVICE_NAME') || 'unknown-service',
        version: this.configService.get<string>('SERVICE_VERSION') || '1.0.0',
        environment: this.configService.get<string>('NODE_ENV') || 'development',
      },
      performance: {
        startTime,
      },
    };
  }

  /**
   * Add performance metrics to response
   */
  private addPerformanceMetrics(
    response: BaseResponse,
    context: ResponseTransformContext
  ): void {
    const endTime = Date.now();
    const duration = endTime - context.performance.startTime;

    // Add performance metadata
    (response as any).metadata = {
      ...((response as any).metadata || {}),
      performance: {
        startTime: context.performance.startTime,
        endTime,
        duration,
      },
    };

    // Log performance if it's slow
    if (duration > 1000) {
      this.logger.warn('Slow response detected', {
        path: context.request.path,
        method: context.request.method,
        duration,
        correlationId: response.correlationId,
      });
    }
  }

  /**
   * Set response headers
   */
  private setResponseHeaders(response: Response, data: BaseResponse): void {
    // Set correlation ID header
    response.setHeader('X-Correlation-ID', data.correlationId);
    
    // Set service information headers
    response.setHeader('X-Service-Name', data.service);
    response.setHeader('X-Service-Version', data.version);
    
    // Set timestamp header
    response.setHeader('X-Response-Time', data.timestamp);

    // Set content type
    response.setHeader('Content-Type', 'application/json');

    // Set cache headers for GET requests
    if (response.req.method === 'GET') {
      this.setCacheHeaders(response, data);
    }

    // Set security headers
    this.setSecurityHeaders(response);
  }

  /**
   * Set cache headers
   */
  private setCacheHeaders(response: Response, data: BaseResponse): void {
    const isPublicEndpoint = this.isPublicEndpoint(response.req.path);
    const cacheMaxAge = this.getCacheMaxAge(response.req.path);

    if (cacheMaxAge > 0) {
      const cacheControl = isPublicEndpoint
        ? `public, max-age=${cacheMaxAge}`
        : `private, max-age=${cacheMaxAge}`;

      response.setHeader('Cache-Control', cacheControl);
      response.setHeader('ETag', this.generateETag(data));
    } else {
      response.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    }
  }

  /**
   * Set security headers
   */
  private setSecurityHeaders(response: Response): void {
    response.setHeader('X-Content-Type-Options', 'nosniff');
    response.setHeader('X-Frame-Options', 'DENY');
    response.setHeader('X-XSS-Protection', '1; mode=block');
    response.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  }

  /**
   * Check if endpoint is public
   */
  private isPublicEndpoint(path: string): boolean {
    const publicPaths = ['/health', '/status', '/api/health', '/api/status'];
    return publicPaths.some(publicPath => path.includes(publicPath));
  }

  /**
   * Get cache max age for path
   */
  private getCacheMaxAge(path: string): number {
    // Health endpoints can be cached for 30 seconds
    if (path.includes('/health') || path.includes('/status')) {
      return 30;
    }

    // Static data can be cached for 5 minutes
    if (path.includes('/static') || path.includes('/public')) {
      return 300;
    }

    // User-specific data should not be cached
    if (path.includes('/users/') || path.includes('/profile')) {
      return 0;
    }

    // Default: 1 minute cache
    return 60;
  }

  /**
   * Generate ETag for response
   */
  private generateETag(data: any): string {
    const content = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return `"${Math.abs(hash).toString(16)}"`;
  }
}

/**
 * Conditional Response Transform Interceptor
 * Only transforms responses that are not already standardized
 */
@Injectable()
export class ConditionalResponseTransformInterceptor extends ResponseTransformInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    
    // Skip transformation for certain paths
    if (this.shouldSkipTransformation(request.path)) {
      return next.handle();
    }

    return super.intercept(context, next);
  }

  /**
   * Check if transformation should be skipped
   */
  private shouldSkipTransformation(path: string): boolean {
    const skipPaths = [
      '/swagger',
      '/api-docs',
      '/favicon.ico',
      '/robots.txt',
    ];

    return skipPaths.some(skipPath => path.includes(skipPath));
  }
}

/**
 * Development Response Transform Interceptor
 * Includes additional debugging information in development
 */
@Injectable()
export class DevelopmentResponseTransformInterceptor extends ResponseTransformInterceptor {
  private transformSuccessResponse(
    data: any,
    context: ResponseTransformContext,
    request: Request,
    response: Response
  ): SuccessResponse<any> {
    const transformedResponse = super['transformSuccessResponse'](data, context, request, response);

    // Add development metadata
    if (this.configService.get<string>('NODE_ENV') === 'development') {
      (transformedResponse as any).debug = {
        requestId: request.correlationId,
        userId: request.user?.id,
        sessionId: request.session?.sessionId,
        query: request.query,
        params: request.params,
        headers: this.sanitizeHeaders(request.headers),
      };
    }

    return transformedResponse;
  }

  /**
   * Sanitize headers for debugging
   */
  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    
    // Remove sensitive headers
    delete sanitized.authorization;
    delete sanitized.cookie;
    delete sanitized['x-api-key'];
    
    return sanitized;
  }
}
