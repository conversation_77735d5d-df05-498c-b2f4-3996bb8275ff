/**
 * API Gateway V2 - App Module
 *
 * Implements shared infrastructure following SHARED-INFRASTRUCTURE-IMPLEMENTATION-PLAN.md
 * Step 1.3: Update App Module with setupAPIGateway
 */

import { Module } from '@nestjs/common';
import { setupAPIGateway } from '@shared';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthModule } from './health/health.module';
import { ProxyModule } from './proxy/proxy.module';

@Module({
  imports: [
    // Shared Infrastructure Setup (as per implementation plan)
    setupAPIGateway('api-gateway-v2', '2.0.0'),

    // Core Gateway Modules
    HealthModule,
    ProxyModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  constructor() {
    console.log('🚀 API Gateway V2 initialized with shared infrastructure');
    console.log('🗄️ Database: api_gateway_v2 (Database Per Service Pattern)');
    console.log('🔧 Features: Auth, Logging, Responses, Config, Database, Error Handling');
    console.log('🌐 Proxy: Service routing and communication');
  }
}
