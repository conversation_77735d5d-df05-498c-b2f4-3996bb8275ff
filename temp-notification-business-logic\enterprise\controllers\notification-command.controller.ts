// Enterprise Notification Command Controller (Write Side) - Enhanced
import { <PERSON>, Post, Put, Delete, Body, Param, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { CreateNotificationCommandDto } from '../models/notification-command.model';

// Enhanced notification services
import { NotificationManagementService, CreateNotificationRequest } from '../../notification/services/notification-management.service';
import { EventNotificationService, NotificationEvent } from '../../notification/services/event-notification.service';

@ApiTags('Notification Commands (Write Operations)')
@Controller('enterprise/notifications')
export class NotificationCommandController {
  private readonly logger = new Logger(NotificationCommandController.name);

  constructor(
    private readonly notificationManagement: NotificationManagementService,
    private readonly eventNotification: EventNotificationService
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create notification (Enterprise CQRS Command)' })
  @ApiResponse({ status: 201, description: 'Notification created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async createNotification(
    @Body() createNotificationData: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`📧 Create notification request received`, { correlationId, userId: createNotificationData.userId });

      // Validate required fields
      if (!createNotificationData.userId || !createNotificationData.title || !createNotificationData.message) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: 'User ID, title, and message are required',
          correlationId
        });
      }

      const notificationRequest: CreateNotificationRequest = {
        userId: createNotificationData.userId,
        type: createNotificationData.type || 'in_app',
        category: createNotificationData.category || 'system',
        priority: createNotificationData.priority || 'medium',
        title: createNotificationData.title,
        message: createNotificationData.message,
        data: createNotificationData.data || {},
        channels: createNotificationData.channels || {
          inApp: {
            title: createNotificationData.title,
            message: createNotificationData.message,
          }
        },
        scheduling: createNotificationData.scheduling,
        source: createNotificationData.source || 'manual',
        correlationId
      };

      const notification = await this.notificationManagement.createNotification(notificationRequest);

      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: notification,
        message: 'Notification created successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Create notification failed: ${error.message}`, error.stack);

      const statusCode = error.message.includes('Validation failed') ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Post('events')
  @ApiOperation({ summary: 'Handle notification event (Enterprise CQRS Command)' })
  @ApiResponse({ status: 201, description: 'Event processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid event data' })
  async handleEvent(
    @Body() eventData: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🎯 Handle notification event request received`, { correlationId, eventType: eventData.type });

      // Validate required fields
      if (!eventData.type || !eventData.userId) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: 'Event type and user ID are required',
          correlationId
        });
      }

      const notificationEvent: NotificationEvent = {
        type: eventData.type,
        userId: eventData.userId,
        data: eventData.data || {},
        timestamp: new Date(),
        source: eventData.source || 'unknown',
        correlationId
      };

      await this.eventNotification.handleEvent(notificationEvent);

      return res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'Event processed successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Handle event failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Put(':notificationId/read')
  @ApiOperation({ summary: 'Mark notification as read (Enterprise CQRS Command)' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async markAsRead(
    @Param('notificationId') notificationId: string,
    @Body() readData: { userId: string },
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`👁️ Mark as read request received`, { correlationId, notificationId });

      if (!readData.userId) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: 'User ID is required',
          correlationId
        });
      }

      await this.notificationManagement.markAsRead(notificationId, readData.userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Notification marked as read',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Mark as read failed: ${error.message}`, error.stack);

      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        error.message.includes('Cannot mark') ? HttpStatus.FORBIDDEN :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Put(':notificationId/click')
  @ApiOperation({ summary: 'Mark notification as clicked (Enterprise CQRS Command)' })
  @ApiResponse({ status: 200, description: 'Notification marked as clicked' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async markAsClicked(
    @Param('notificationId') notificationId: string,
    @Body() clickData: { userId: string },
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🖱️ Mark as clicked request received`, { correlationId, notificationId });

      if (!clickData.userId) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          error: 'User ID is required',
          correlationId
        });
      }

      await this.notificationManagement.markAsClicked(notificationId, clickData.userId);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Notification marked as clicked',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Mark as clicked failed: ${error.message}`, error.stack);

      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        error.message.includes('Cannot mark') ? HttpStatus.FORBIDDEN :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Delete(':notificationId')
  @ApiOperation({ summary: 'Cancel notification (Enterprise CQRS Command)' })
  @ApiResponse({ status: 200, description: 'Notification cancelled successfully' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  @ApiResponse({ status: 400, description: 'Cannot cancel notification' })
  async cancelNotification(
    @Param('notificationId') notificationId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || 'unknown';
      this.logger.log(`🗑️ Cancel notification request received`, { correlationId, notificationId });

      await this.notificationManagement.cancelNotification(notificationId);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Notification cancelled successfully',
        correlationId
      });
    } catch (error) {
      this.logger.error(`❌ Cancel notification failed: ${error.message}`, error.stack);

      const statusCode = error.message.includes('not found') ? HttpStatus.NOT_FOUND :
                        error.message.includes('Cannot cancel') ? HttpStatus.BAD_REQUEST :
                        HttpStatus.INTERNAL_SERVER_ERROR;

      return res.status(statusCode).json({
        success: false,
        error: error.message,
        correlationId: headers['x-correlation-id']
      });
    }
  }
}
