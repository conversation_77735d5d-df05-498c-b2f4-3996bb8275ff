import { <PERSON>, Get, Post, Body, Query, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { UserBehaviorService } from '../services/user-behavior.service';

@ApiTags('behavior')
@Controller('behavior')
export class BehaviorController {
  private readonly logger = new Logger(BehaviorController.name);

  constructor(private readonly userBehavior: UserBehaviorService) {}

  @Get('insights')
  @ApiOperation({ summary: 'Get behavior insights for all users' })
  @ApiResponse({ status: 200, description: 'Behavior insights retrieved successfully' })
  async getBehaviorInsights() {
    try {
      this.logger.log('Getting behavior insights');

      const insights = await this.userBehavior.getBehaviorInsights();

      return {
        success: true,
        data: insights,
      };
    } catch (error) {
      this.logger.error(`Failed to get behavior insights: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve behavior insights',
        details: error.message,
      };
    }
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Analyze behavior for specific user' })
  @ApiResponse({ status: 200, description: 'User behavior analysis retrieved successfully' })
  @ApiParam({ name: 'userId', description: 'User ID to analyze' })
  async analyzeUserBehavior(@Param('userId') userId: string) {
    try {
      this.logger.log(`Analyzing behavior for user: ${userId}`);

      const profile = await this.userBehavior.analyzeUserBehavior(userId);

      return {
        success: true,
        data: profile,
      };
    } catch (error) {
      this.logger.error(`Failed to analyze user behavior: ${error.message}`);
      return {
        success: false,
        error: 'Failed to analyze user behavior',
        details: error.message,
      };
    }
  }

  @Get('user/:userId/trends')
  @ApiOperation({ summary: 'Get user behavior trends' })
  @ApiResponse({ status: 200, description: 'User behavior trends retrieved successfully' })
  @ApiParam({ name: 'userId', description: 'User ID to get trends for' })
  @ApiQuery({ name: 'days', required: false, description: 'Number of days to get trends for' })
  async getUserBehaviorTrends(
    @Param('userId') userId: string,
    @Query('days') days: string = '30'
  ) {
    try {
      const daysNum = parseInt(days);
      this.logger.log(`Getting behavior trends for user ${userId} (${daysNum} days)`);

      const trends = this.userBehavior.getUserBehaviorTrends(userId, daysNum);

      return {
        success: true,
        data: {
          userId,
          timeRange: `${daysNum} days`,
          trends,
          count: trends.length,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get user behavior trends: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve user behavior trends',
        details: error.message,
      };
    }
  }

  @Get('user/:userId/engagement')
  @ApiOperation({ summary: 'Get user engagement score' })
  @ApiResponse({ status: 200, description: 'User engagement score retrieved successfully' })
  @ApiParam({ name: 'userId', description: 'User ID to get engagement score for' })
  async getUserEngagementScore(@Param('userId') userId: string) {
    try {
      this.logger.log(`Getting engagement score for user: ${userId}`);

      const engagementScore = this.userBehavior.calculateEngagementScore(userId);
      const userSegment = this.userBehavior.getUserSegment(userId);

      return {
        success: true,
        data: {
          userId,
          engagementScore,
          userSegment,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get user engagement score: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve user engagement score',
        details: error.message,
      };
    }
  }

  @Post('session/track')
  @ApiOperation({ summary: 'Track user session' })
  @ApiResponse({ status: 201, description: 'User session tracked successfully' })
  async trackUserSession(@Body() sessionData: {
    userId: string;
    sessionId: string;
    action: 'start' | 'end';
  }) {
    try {
      this.logger.log('Tracking user session', { 
        userId: sessionData.userId,
        sessionId: sessionData.sessionId,
        action: sessionData.action
      });

      await this.userBehavior.trackUserSession(
        sessionData.userId,
        sessionData.sessionId,
        sessionData.action
      );

      return {
        success: true,
        data: {
          message: 'User session tracked successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to track user session: ${error.message}`);
      return {
        success: false,
        error: 'Failed to track user session',
        details: error.message,
      };
    }
  }

  @Get('segments')
  @ApiOperation({ summary: 'Get user segments analysis' })
  @ApiResponse({ status: 200, description: 'User segments retrieved successfully' })
  async getUserSegments() {
    try {
      this.logger.log('Getting user segments');

      const insights = await this.userBehavior.getBehaviorInsights();

      return {
        success: true,
        data: {
          segments: insights.userSegments,
          totalUsers: insights.totalUsers,
          activeUsers: insights.activeUsers,
          newUsers: insights.newUsers,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get user segments: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve user segments',
        details: error.message,
      };
    }
  }

  @Get('funnel')
  @ApiOperation({ summary: 'Get conversion funnel analysis' })
  @ApiResponse({ status: 200, description: 'Conversion funnel retrieved successfully' })
  async getConversionFunnel() {
    try {
      this.logger.log('Getting conversion funnel');

      const insights = await this.userBehavior.getBehaviorInsights();

      return {
        success: true,
        data: {
          funnel: insights.conversionFunnel,
          totalUsers: insights.totalUsers,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get conversion funnel: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve conversion funnel',
        details: error.message,
      };
    }
  }

  @Get('actions/top')
  @ApiOperation({ summary: 'Get top user actions' })
  @ApiResponse({ status: 200, description: 'Top user actions retrieved successfully' })
  async getTopUserActions() {
    try {
      this.logger.log('Getting top user actions');

      const insights = await this.userBehavior.getBehaviorInsights();

      return {
        success: true,
        data: {
          topActions: insights.topUserActions,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get top user actions: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve top user actions',
        details: error.message,
      };
    }
  }

  @Get('engagement/trends')
  @ApiOperation({ summary: 'Get engagement trends' })
  @ApiResponse({ status: 200, description: 'Engagement trends retrieved successfully' })
  async getEngagementTrends() {
    try {
      this.logger.log('Getting engagement trends');

      const insights = await this.userBehavior.getBehaviorInsights();

      return {
        success: true,
        data: {
          trends: insights.engagementTrends,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get engagement trends: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve engagement trends',
        details: error.message,
      };
    }
  }
}
