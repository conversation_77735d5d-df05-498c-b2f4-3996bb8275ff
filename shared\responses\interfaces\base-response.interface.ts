/**
 * Base Response Interfaces
 * Defines the standard response structure for all services
 */

/**
 * Base response interface that all API responses must implement
 */
export interface BaseResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  correlationId: string;
  timestamp: string;
  service: string;
  version: string;
}

/**
 * Success response interface
 */
export interface SuccessResponse<T = any> extends BaseResponse<T> {
  success: true;
  data: T;
}

/**
 * Error response interface
 */
export interface ErrorResponse extends BaseResponse<never> {
  success: false;
  error: ErrorDetails;
  statusCode: number;
}

/**
 * Error details interface
 */
export interface ErrorDetails {
  code: string;
  message: string;
  details?: any;
  stack?: string; // Only in development
  validation?: ValidationError[];
  context?: Record<string, any>;
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
  constraints?: Record<string, string>;
}

/**
 * Response metadata interface
 */
export interface ResponseMetadata {
  correlationId: string;
  timestamp: string;
  service: string;
  version: string;
  requestId?: string;
  userId?: string;
  sessionId?: string;
  executionTime?: number;
}

/**
 * API response wrapper interface
 */
export interface ApiResponseWrapper<T = any> {
  response: BaseResponse<T>;
  metadata: ResponseMetadata;
  headers?: Record<string, string>;
}

/**
 * Response builder options
 */
export interface ResponseBuilderOptions {
  service?: string;
  version?: string;
  correlationId?: string;
  message?: string;
  metadata?: Record<string, any>;
}

/**
 * Error response builder options
 */
export interface ErrorResponseBuilderOptions extends ResponseBuilderOptions {
  code: string;
  statusCode: number;
  details?: any;
  stack?: string;
  validation?: ValidationError[];
  context?: Record<string, any>;
}

/**
 * Standard error codes enumeration
 */
export enum ErrorCode {
  // Client Errors (4xx)
  BAD_REQUEST = 'BAD_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  METHOD_NOT_ALLOWED = 'METHOD_NOT_ALLOWED',
  CONFLICT = 'CONFLICT',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  PAYLOAD_TOO_LARGE = 'PAYLOAD_TOO_LARGE',
  UNSUPPORTED_MEDIA_TYPE = 'UNSUPPORTED_MEDIA_TYPE',
  
  // Server Errors (5xx)
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  NOT_IMPLEMENTED = 'NOT_IMPLEMENTED',
  BAD_GATEWAY = 'BAD_GATEWAY',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  GATEWAY_TIMEOUT = 'GATEWAY_TIMEOUT',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  
  // Business Logic Errors
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  RESOURCE_LOCKED = 'RESOURCE_LOCKED',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',
  RESOURCE_EXPIRED = 'RESOURCE_EXPIRED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  
  // Authentication & Authorization Errors
  INVALID_TOKEN = 'INVALID_TOKEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_SUSPENDED = 'ACCOUNT_SUSPENDED',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  
  // External Service Errors
  THIRD_PARTY_SERVICE_ERROR = 'THIRD_PARTY_SERVICE_ERROR',
  PAYMENT_PROCESSING_ERROR = 'PAYMENT_PROCESSING_ERROR',
  BLOCKCHAIN_ERROR = 'BLOCKCHAIN_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  NOTIFICATION_ERROR = 'NOTIFICATION_ERROR',
}

/**
 * HTTP status code mappings for error codes
 */
export const ERROR_CODE_STATUS_MAP: Record<ErrorCode, number> = {
  // Client Errors (4xx)
  [ErrorCode.BAD_REQUEST]: 400,
  [ErrorCode.UNAUTHORIZED]: 401,
  [ErrorCode.FORBIDDEN]: 403,
  [ErrorCode.NOT_FOUND]: 404,
  [ErrorCode.METHOD_NOT_ALLOWED]: 405,
  [ErrorCode.CONFLICT]: 409,
  [ErrorCode.VALIDATION_ERROR]: 422,
  [ErrorCode.RATE_LIMIT_EXCEEDED]: 429,
  [ErrorCode.PAYLOAD_TOO_LARGE]: 413,
  [ErrorCode.UNSUPPORTED_MEDIA_TYPE]: 415,
  
  // Server Errors (5xx)
  [ErrorCode.INTERNAL_SERVER_ERROR]: 500,
  [ErrorCode.NOT_IMPLEMENTED]: 501,
  [ErrorCode.BAD_GATEWAY]: 502,
  [ErrorCode.SERVICE_UNAVAILABLE]: 503,
  [ErrorCode.GATEWAY_TIMEOUT]: 504,
  [ErrorCode.DATABASE_ERROR]: 500,
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: 502,
  
  // Business Logic Errors (mapped to appropriate 4xx codes)
  [ErrorCode.BUSINESS_RULE_VIOLATION]: 422,
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: 403,
  [ErrorCode.RESOURCE_LOCKED]: 423,
  [ErrorCode.OPERATION_NOT_ALLOWED]: 405,
  [ErrorCode.DUPLICATE_RESOURCE]: 409,
  [ErrorCode.RESOURCE_EXPIRED]: 410,
  [ErrorCode.QUOTA_EXCEEDED]: 429,
  
  // Authentication & Authorization Errors
  [ErrorCode.INVALID_TOKEN]: 401,
  [ErrorCode.TOKEN_EXPIRED]: 401,
  [ErrorCode.INVALID_CREDENTIALS]: 401,
  [ErrorCode.ACCOUNT_LOCKED]: 423,
  [ErrorCode.ACCOUNT_SUSPENDED]: 403,
  [ErrorCode.EMAIL_NOT_VERIFIED]: 403,
  
  // External Service Errors
  [ErrorCode.THIRD_PARTY_SERVICE_ERROR]: 502,
  [ErrorCode.PAYMENT_PROCESSING_ERROR]: 502,
  [ErrorCode.BLOCKCHAIN_ERROR]: 502,
  [ErrorCode.STORAGE_ERROR]: 502,
  [ErrorCode.NOTIFICATION_ERROR]: 502,
};

/**
 * Response type enumeration
 */
export enum ResponseType {
  SUCCESS = 'success',
  ERROR = 'error',
  PAGINATED = 'paginated',
  STREAM = 'stream',
  REDIRECT = 'redirect',
}

/**
 * Response format options
 */
export interface ResponseFormatOptions {
  includeMetadata?: boolean;
  includeStack?: boolean;
  includeValidation?: boolean;
  includeContext?: boolean;
  compressResponse?: boolean;
  cacheHeaders?: boolean;
}

/**
 * Response transformation context
 */
export interface ResponseTransformContext {
  request: {
    method: string;
    path: string;
    query: Record<string, any>;
    headers: Record<string, string>;
    user?: any;
  };
  service: {
    name: string;
    version: string;
    environment: string;
  };
  performance: {
    startTime: number;
    endTime?: number;
    duration?: number;
  };
}

/**
 * Response cache options
 */
export interface ResponseCacheOptions {
  ttl?: number; // Time to live in seconds
  key?: string; // Custom cache key
  tags?: string[]; // Cache tags for invalidation
  vary?: string[]; // Vary headers
  private?: boolean; // Private cache
}

/**
 * Response compression options
 */
export interface ResponseCompressionOptions {
  enabled?: boolean;
  threshold?: number; // Minimum size to compress
  algorithms?: string[]; // Compression algorithms
}

/**
 * Response security options
 */
export interface ResponseSecurityOptions {
  sanitizeData?: boolean;
  removeStackTrace?: boolean;
  maskSensitiveFields?: string[];
  addSecurityHeaders?: boolean;
}

/**
 * Complete response configuration
 */
export interface ResponseConfiguration {
  format: ResponseFormatOptions;
  cache: ResponseCacheOptions;
  compression: ResponseCompressionOptions;
  security: ResponseSecurityOptions;
  transform: ResponseTransformContext;
}
