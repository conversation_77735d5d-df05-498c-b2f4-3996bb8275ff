import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';

// Controllers
import { AnalyticsController } from './controllers/analytics.controller';
import { EventsController } from './controllers/events.controller';
import { PerformanceController } from './controllers/performance.controller';
import { BehaviorController } from './controllers/behavior.controller';

// Services
import { EventTrackingService } from './services/event-tracking.service';
import { PerformanceAnalyticsService } from './services/performance-analytics.service';
import { UserBehaviorService } from './services/user-behavior.service';

// Shared modules
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [
    HttpModule,
    PrismaModule,
  ],
  controllers: [
    AnalyticsController,
    EventsController,
    PerformanceController,
    BehaviorController,
  ],
  providers: [
    EventTrackingService,
    PerformanceAnalyticsService,
    UserBehaviorService,
  ],
  exports: [
    EventTrackingService,
    PerformanceAnalyticsService,
    UserBehaviorService,
  ],
})
export class AnalyticsModule {}
