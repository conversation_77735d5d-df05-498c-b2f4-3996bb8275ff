import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);

    // Get configuration
    const port = configService.get<number>('SERVICE_PORT', 3004);
    const apiPrefix = configService.get<string>('API_PREFIX', 'api');
    const nodeEnv = configService.get<string>('NODE_ENV', 'development');
    const corsOrigin = configService.get<string>('CORS_ORIGIN', '*');

    // CORS configuration
    app.enableCors({
      origin: corsOrigin === '*' ? true : corsOrigin.split(','),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      credentials: true,
    });

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // API prefix
    app.setGlobalPrefix(apiPrefix);

    // Swagger documentation
    if (nodeEnv === 'development') {
      const config = new DocumentBuilder()
        .setTitle('Blockchain Service API')
        .setDescription('Smart Contract Integration and Blockchain Operations Service for Social NFT Platform')
        .setVersion('1.0.0')
        .addBearerAuth()
        .addTag('Blockchain', 'Blockchain operations and smart contract interactions')
        .addTag('Contracts', 'Smart contract management endpoints')
        .addTag('Transactions', 'Transaction monitoring and management')
        .addTag('Wallets', 'Wallet management and operations')
        .addTag('Health', 'Health check endpoints')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup(`${apiPrefix}/docs`, app, document, {
        swaggerOptions: {
          persistAuthorization: true,
        },
      });

      logger.log(`Swagger documentation available at http://localhost:${port}/${apiPrefix}/docs`);
    }

    // Start the service
    await app.listen(port);
    
    logger.log(`🚀 Blockchain Service (Industry Standard) running on port ${port}`);
    logger.log(`📚 API Documentation: http://localhost:${port}/${apiPrefix}/docs`);
    logger.log(`❤️  Health Check: http://localhost:${port}/${apiPrefix}/health`);
    logger.log(`⛓️  Blockchain: Smart contract integration enabled`);
    logger.log(`🏗️  Architecture: Independent Microservice (No Shared Dependencies)`);
    
  } catch (error) {
    logger.error('❌ Failed to start Blockchain Service:', error);
    process.exit(1);
  }
}

bootstrap();
