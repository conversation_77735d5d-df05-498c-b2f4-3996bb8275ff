import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';

export interface Notification {
  id: string;
  userId: string;
  type: 'email' | 'sms' | 'push' | 'in_app';
  category: 'trade' | 'bid' | 'listing' | 'account' | 'system' | 'marketing';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'cancelled';
  title: string;
  message: string;
  data: Record<string, any>;
  channels: {
    email?: {
      to: string;
      subject: string;
      template?: string;
      templateData?: Record<string, any>;
    };
    sms?: {
      to: string;
      message: string;
    };
    push?: {
      deviceTokens: string[];
      title: string;
      body: string;
      data?: Record<string, any>;
    };
    inApp?: {
      title: string;
      message: string;
      actionUrl?: string;
      imageUrl?: string;
    };
  };
  scheduling: {
    sendAt?: Date;
    timezone?: string;
    recurring?: {
      frequency: 'daily' | 'weekly' | 'monthly';
      endDate?: Date;
    };
  };
  tracking: {
    sentAt?: Date;
    deliveredAt?: Date;
    readAt?: Date;
    clickedAt?: Date;
    failureReason?: string;
    retryCount: number;
    maxRetries: number;
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy?: string;
    source: string;
    correlationId?: string;
  };
}

export interface CreateNotificationRequest {
  userId: string;
  type: 'email' | 'sms' | 'push' | 'in_app';
  category: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  message: string;
  data?: Record<string, any>;
  channels: Notification['channels'];
  scheduling?: {
    sendAt?: Date;
    timezone?: string;
  };
  source: string;
  correlationId?: string;
}

export interface NotificationFilters {
  userId?: string;
  type?: string;
  category?: string;
  priority?: string;
  status?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface NotificationStats {
  total: number;
  byStatus: Record<string, number>;
  byType: Record<string, number>;
  byCategory: Record<string, number>;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  recentNotifications: Notification[];
}

@Injectable()
export class NotificationManagementService {
  private readonly logger = new Logger(NotificationManagementService.name);
  private notifications: Map<string, Notification> = new Map();
  private notificationsByUser: Map<string, string[]> = new Map(); // userId -> notificationIds[]
  private notificationQueue: string[] = []; // Queue for processing

  constructor() {
    this.initializeMockNotifications();
    this.startNotificationProcessor();
  }

  /**
   * Create a new notification
   */
  async createNotification(createRequest: CreateNotificationRequest): Promise<Notification> {
    try {
      this.logger.log(`Creating notification for user: ${createRequest.userId}`);

      // Validate request
      this.validateCreateNotificationRequest(createRequest);

      // Generate notification ID
      const notificationId = this.generateNotificationId();

      // Create notification
      const notification: Notification = {
        id: notificationId,
        userId: createRequest.userId,
        type: createRequest.type,
        category: createRequest.category as any,
        priority: createRequest.priority || 'medium',
        status: 'pending',
        title: createRequest.title,
        message: createRequest.message,
        data: createRequest.data || {},
        channels: createRequest.channels,
        scheduling: {
          sendAt: createRequest.scheduling?.sendAt || new Date(),
          timezone: createRequest.scheduling?.timezone || 'UTC',
        },
        tracking: {
          retryCount: 0,
          maxRetries: 3,
        },
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          source: createRequest.source,
          correlationId: createRequest.correlationId,
        },
      };

      // Store notification
      this.notifications.set(notificationId, notification);

      // Update user notifications
      if (!this.notificationsByUser.has(createRequest.userId)) {
        this.notificationsByUser.set(createRequest.userId, []);
      }
      this.notificationsByUser.get(createRequest.userId)!.push(notificationId);

      // Add to processing queue if immediate send
      if (!createRequest.scheduling?.sendAt || createRequest.scheduling.sendAt <= new Date()) {
        this.notificationQueue.push(notificationId);
      }

      this.logger.log(`Notification created successfully: ${notificationId}`);
      return notification;
    } catch (error) {
      this.logger.error(`Failed to create notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get notification by ID
   */
  async getNotificationById(notificationId: string): Promise<Notification | null> {
    return this.notifications.get(notificationId) || null;
  }

  /**
   * Get notifications with filters and pagination
   */
  async getNotifications(
    filters: NotificationFilters = {},
    limit: number = 50,
    offset: number = 0
  ): Promise<{ notifications: Notification[]; total: number }> {
    try {
      this.logger.log(`Getting notifications with filters`, { filters, limit, offset });

      let filteredNotifications = Array.from(this.notifications.values());

      // Apply filters
      if (filters.userId) {
        filteredNotifications = filteredNotifications.filter(n => n.userId === filters.userId);
      }

      if (filters.type) {
        filteredNotifications = filteredNotifications.filter(n => n.type === filters.type);
      }

      if (filters.category) {
        filteredNotifications = filteredNotifications.filter(n => n.category === filters.category);
      }

      if (filters.priority) {
        filteredNotifications = filteredNotifications.filter(n => n.priority === filters.priority);
      }

      if (filters.status) {
        filteredNotifications = filteredNotifications.filter(n => n.status === filters.status);
      }

      if (filters.dateFrom) {
        filteredNotifications = filteredNotifications.filter(n => n.metadata.createdAt >= filters.dateFrom!);
      }

      if (filters.dateTo) {
        filteredNotifications = filteredNotifications.filter(n => n.metadata.createdAt <= filters.dateTo!);
      }

      // Sort by creation date (newest first)
      filteredNotifications.sort((a, b) => b.metadata.createdAt.getTime() - a.metadata.createdAt.getTime());

      const total = filteredNotifications.length;
      const paginatedNotifications = filteredNotifications.slice(offset, offset + limit);

      return { notifications: paginatedNotifications, total };
    } catch (error) {
      this.logger.error(`Failed to get notifications: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get notifications by user
   */
  async getNotificationsByUser(userId: string, limit: number = 50): Promise<Notification[]> {
    try {
      const userNotificationIds = this.notificationsByUser.get(userId) || [];
      const notifications = userNotificationIds
        .map(id => this.notifications.get(id))
        .filter(notification => notification !== undefined) as Notification[];

      return notifications
        .sort((a, b) => b.metadata.createdAt.getTime() - a.metadata.createdAt.getTime())
        .slice(0, limit);
    } catch (error) {
      this.logger.error(`Failed to get user notifications: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    try {
      const notification = this.notifications.get(notificationId);
      if (!notification) {
        throw new NotFoundException('Notification not found');
      }

      if (notification.userId !== userId) {
        throw new BadRequestException('Cannot mark other user\'s notification as read');
      }

      notification.tracking.readAt = new Date();
      notification.metadata.updatedAt = new Date();
      this.notifications.set(notificationId, notification);

      this.logger.log(`Notification marked as read: ${notificationId}`);
    } catch (error) {
      this.logger.error(`Failed to mark notification as read: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mark notification as clicked
   */
  async markAsClicked(notificationId: string, userId: string): Promise<void> {
    try {
      const notification = this.notifications.get(notificationId);
      if (!notification) {
        throw new NotFoundException('Notification not found');
      }

      if (notification.userId !== userId) {
        throw new BadRequestException('Cannot mark other user\'s notification as clicked');
      }

      notification.tracking.clickedAt = new Date();
      notification.metadata.updatedAt = new Date();
      this.notifications.set(notificationId, notification);

      this.logger.log(`Notification marked as clicked: ${notificationId}`);
    } catch (error) {
      this.logger.error(`Failed to mark notification as clicked: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cancel notification
   */
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      const notification = this.notifications.get(notificationId);
      if (!notification) {
        throw new NotFoundException('Notification not found');
      }

      if (notification.status === 'sent' || notification.status === 'delivered') {
        throw new BadRequestException('Cannot cancel already sent notification');
      }

      notification.status = 'cancelled';
      notification.metadata.updatedAt = new Date();
      this.notifications.set(notificationId, notification);

      // Remove from queue
      const queueIndex = this.notificationQueue.indexOf(notificationId);
      if (queueIndex > -1) {
        this.notificationQueue.splice(queueIndex, 1);
      }

      this.logger.log(`Notification cancelled: ${notificationId}`);
    } catch (error) {
      this.logger.error(`Failed to cancel notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(userId?: string): Promise<NotificationStats> {
    try {
      let notifications = Array.from(this.notifications.values());
      
      if (userId) {
        notifications = notifications.filter(n => n.userId === userId);
      }

      const total = notifications.length;
      
      // Count by status
      const byStatus: Record<string, number> = {};
      notifications.forEach(n => {
        byStatus[n.status] = (byStatus[n.status] || 0) + 1;
      });

      // Count by type
      const byType: Record<string, number> = {};
      notifications.forEach(n => {
        byType[n.type] = (byType[n.type] || 0) + 1;
      });

      // Count by category
      const byCategory: Record<string, number> = {};
      notifications.forEach(n => {
        byCategory[n.category] = (byCategory[n.category] || 0) + 1;
      });

      // Calculate rates
      const sentNotifications = notifications.filter(n => n.status === 'sent' || n.status === 'delivered');
      const deliveredNotifications = notifications.filter(n => n.status === 'delivered');
      const readNotifications = notifications.filter(n => n.tracking.readAt);
      const clickedNotifications = notifications.filter(n => n.tracking.clickedAt);

      const deliveryRate = sentNotifications.length > 0 ? (deliveredNotifications.length / sentNotifications.length) * 100 : 0;
      const openRate = deliveredNotifications.length > 0 ? (readNotifications.length / deliveredNotifications.length) * 100 : 0;
      const clickRate = readNotifications.length > 0 ? (clickedNotifications.length / readNotifications.length) * 100 : 0;

      const recentNotifications = notifications
        .sort((a, b) => b.metadata.createdAt.getTime() - a.metadata.createdAt.getTime())
        .slice(0, 10);

      return {
        total,
        byStatus,
        byType,
        byCategory,
        deliveryRate,
        openRate,
        clickRate,
        recentNotifications,
      };
    } catch (error) {
      this.logger.error(`Failed to get notification stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process notification (simulate sending)
   */
  private async processNotification(notificationId: string): Promise<void> {
    try {
      const notification = this.notifications.get(notificationId);
      if (!notification || notification.status !== 'pending') {
        return;
      }

      this.logger.log(`Processing notification: ${notificationId}`);

      // Simulate sending delay
      await new Promise(resolve => setTimeout(resolve, 100));

      // Simulate success/failure (95% success rate)
      const success = Math.random() > 0.05;

      if (success) {
        notification.status = 'sent';
        notification.tracking.sentAt = new Date();
        
        // Simulate delivery confirmation after a short delay
        setTimeout(() => {
          notification.status = 'delivered';
          notification.tracking.deliveredAt = new Date();
          this.notifications.set(notificationId, notification);
        }, 500);
      } else {
        notification.status = 'failed';
        notification.tracking.failureReason = 'Simulated delivery failure';
        notification.tracking.retryCount += 1;

        // Retry if under max retries
        if (notification.tracking.retryCount < notification.tracking.maxRetries) {
          setTimeout(() => {
            notification.status = 'pending';
            this.notificationQueue.push(notificationId);
          }, 5000); // Retry after 5 seconds
        }
      }

      notification.metadata.updatedAt = new Date();
      this.notifications.set(notificationId, notification);

      this.logger.log(`Notification processed: ${notificationId} - Status: ${notification.status}`);
    } catch (error) {
      this.logger.error(`Failed to process notification: ${error.message}`, error.stack);
    }
  }

  /**
   * Start notification processor
   */
  private startNotificationProcessor(): void {
    setInterval(async () => {
      if (this.notificationQueue.length > 0) {
        const notificationId = this.notificationQueue.shift();
        if (notificationId) {
          await this.processNotification(notificationId);
        }
      }
    }, 1000); // Process every second
  }

  /**
   * Validate create notification request
   */
  private validateCreateNotificationRequest(request: CreateNotificationRequest): void {
    const errors: string[] = [];

    if (!request.userId) errors.push('User ID is required');
    if (!request.type) errors.push('Notification type is required');
    if (!request.category) errors.push('Category is required');
    if (!request.title) errors.push('Title is required');
    if (!request.message) errors.push('Message is required');
    if (!request.source) errors.push('Source is required');

    // Validate channels
    if (!request.channels || Object.keys(request.channels).length === 0) {
      errors.push('At least one notification channel is required');
    }

    if (request.channels.email && !request.channels.email.to) {
      errors.push('Email recipient is required');
    }

    if (request.channels.sms && !request.channels.sms.to) {
      errors.push('SMS recipient is required');
    }

    if (errors.length > 0) {
      throw new BadRequestException(`Validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Send notification (wrapper method for controller)
   */
  async sendNotification(notificationData: any): Promise<Notification> {
    return await this.createNotification(notificationData);
  }

  /**
   * Get user notifications (wrapper method for controller)
   */
  async getUserNotifications(userId: string, limit: number = 50): Promise<Notification[]> {
    return await this.getNotificationsByUser(userId, limit);
  }

  /**
   * Get notification status
   */
  async getNotificationStatus(notificationId: string): Promise<any> {
    const notification = await this.getNotificationById(notificationId);
    if (!notification) {
      throw new Error('Notification not found');
    }

    return {
      id: notification.id,
      status: notification.status,
      tracking: notification.tracking,
      metadata: notification.metadata,
    };
  }

  /**
   * Initialize mock notifications
   */
  private initializeMockNotifications(): void {
    const mockNotifications = [
      {
        userId: 'user_demo_1',
        type: 'email' as const,
        category: 'trade',
        title: 'NFT Purchase Successful',
        message: 'Your NFT purchase has been completed successfully.',
        channels: {
          email: {
            to: '<EMAIL>',
            subject: 'NFT Purchase Confirmation',
          },
        },
        source: 'marketplace-service',
      },
      {
        userId: 'user_demo_2',
        type: 'push' as const,
        category: 'bid',
        title: 'New Bid Received',
        message: 'Someone placed a bid on your NFT listing.',
        channels: {
          push: {
            deviceTokens: ['device_token_123'],
            title: 'New Bid Received',
            body: 'Someone placed a bid on your NFT listing.',
          },
        },
        source: 'marketplace-service',
      },
    ];

    mockNotifications.forEach(async (notificationData) => {
      try {
        await this.createNotification(notificationData);
      } catch (error) {
        // Ignore errors for mock data
      }
    });
  }

  /**
   * Generate unique notification ID
   */
  private generateNotificationId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
