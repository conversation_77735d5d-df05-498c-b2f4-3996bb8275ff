'use client'

import React, { useState } from 'react'
import {
  CubeIcon,
  PlusIcon,
  PlayIcon,
  PauseIcon,
  ArrowUpIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  EyeIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import {
  useDeployContract,
  useUpgradeContract,
  useVerifyContract,
  usePauseContract,
  useUnpauseContract
} from '@/hooks/useBlockchainIntegration'
import { SmartContract, ContractType, BlockchainNetwork } from '@/types/blockchain-integration.types'

interface ContractManagerProps {
  contracts?: SmartContract[]
  isLoading: boolean
  className?: string
}

export default function ContractManager({
  contracts = [],
  isLoading,
  className = ''
}: ContractManagerProps) {
  const [selectedContract, setSelectedContract] = useState<SmartContract | null>(null)
  const [showDeployModal, setShowDeployModal] = useState(false)
  const [filterType, setFilterType] = useState<ContractType | 'all'>('all')
  const [filterNetwork, setFilterNetwork] = useState<BlockchainNetwork | 'all'>('all')

  const deployContractMutation = useDeployContract()
  const upgradeContractMutation = useUpgradeContract()
  const verifyContractMutation = useVerifyContract()
  const pauseContractMutation = usePauseContract()
  const unpauseContractMutation = useUnpauseContract()

  const getContractTypeIcon = (type: ContractType) => {
    switch (type) {
      case ContractType.ERC721:
      case ContractType.ERC1155:
        return <CubeIcon className="h-5 w-5" />
      case ContractType.MARKETPLACE:
        return <DocumentTextIcon className="h-5 w-5" />
      case ContractType.EVOLUTION:
        return <ArrowUpIcon className="h-5 w-5" />
      default:
        return <CubeIcon className="h-5 w-5" />
    }
  }

  const getContractTypeColor = (type: ContractType) => {
    switch (type) {
      case ContractType.ERC721:
        return 'text-blue-600 bg-blue-100'
      case ContractType.ERC1155:
        return 'text-purple-600 bg-purple-100'
      case ContractType.MARKETPLACE:
        return 'text-green-600 bg-green-100'
      case ContractType.EVOLUTION:
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getRiskLevelColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'text-green-600 bg-green-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'high':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const filteredContracts = contracts.filter(contract => {
    const typeMatch = filterType === 'all' || contract.type === filterType
    const networkMatch = filterNetwork === 'all' || contract.network === filterNetwork
    return typeMatch && networkMatch
  })

  const handlePauseContract = async (contractId: string) => {
    if (confirm('Are you sure you want to pause this contract?')) {
      try {
        await pauseContractMutation.mutateAsync(contractId)
      } catch (error) {
        console.error('Failed to pause contract:', error)
      }
    }
  }

  const handleUnpauseContract = async (contractId: string) => {
    try {
      await unpauseContractMutation.mutateAsync(contractId)
    } catch (error) {
      console.error('Failed to unpause contract:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header and Filters */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Smart Contract Management</h2>
          <p className="text-sm text-gray-600">Deploy, manage, and monitor smart contracts</p>
        </div>
        
        <button
          onClick={() => setShowDeployModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Deploy Contract
        </button>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Types</option>
          <option value={ContractType.ERC721}>ERC721</option>
          <option value={ContractType.ERC1155}>ERC1155</option>
          <option value={ContractType.MARKETPLACE}>Marketplace</option>
          <option value={ContractType.EVOLUTION}>Evolution</option>
          <option value={ContractType.UTILITY}>Utility</option>
        </select>

        <select
          value={filterNetwork}
          onChange={(e) => setFilterNetwork(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Networks</option>
          <option value={BlockchainNetwork.ETHEREUM}>Ethereum</option>
          <option value={BlockchainNetwork.POLYGON}>Polygon</option>
          <option value={BlockchainNetwork.BSC}>BSC</option>
          <option value={BlockchainNetwork.BASE}>Base</option>
        </select>
      </div>

      {/* Contract Grid */}
      {filteredContracts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredContracts.map((contract) => (
            <ContractCard
              key={contract.id}
              contract={contract}
              onPause={() => handlePauseContract(contract.id)}
              onUnpause={() => handleUnpauseContract(contract.id)}
              onUpgrade={() => setSelectedContract(contract)}
              onViewDetails={() => setSelectedContract(contract)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No contracts found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Deploy your first smart contract to get started.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowDeployModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Deploy Contract
            </button>
          </div>
        </div>
      )}

      {/* Contract Statistics */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Contract Statistics</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{contracts.length}</div>
            <div className="text-sm text-gray-600">Total Contracts</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {contracts.filter(c => c.isVerified).length}
            </div>
            <div className="text-sm text-gray-600">Verified</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {contracts.filter(c => c.isUpgradeable).length}
            </div>
            <div className="text-sm text-gray-600">Upgradeable</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {contracts.filter(c => c.isActive).length}
            </div>
            <div className="text-sm text-gray-600">Active</div>
          </div>
        </div>
      </div>

      {/* Deploy Contract Modal */}
      {showDeployModal && (
        <DeployContractModal
          onClose={() => setShowDeployModal(false)}
          onDeploy={(contractData) => {
            deployContractMutation.mutate(contractData)
            setShowDeployModal(false)
          }}
        />
      )}

      {/* Contract Details Modal */}
      {selectedContract && (
        <ContractDetailsModal
          contract={selectedContract}
          onClose={() => setSelectedContract(null)}
          onUpgrade={(newImplementation) => {
            upgradeContractMutation.mutate({
              contractId: selectedContract.id,
              newImplementation
            })
            setSelectedContract(null)
          }}
          onVerify={(sourceCode) => {
            verifyContractMutation.mutate({
              contractId: selectedContract.id,
              sourceCode
            })
          }}
        />
      )}
    </div>
  )
}

interface ContractCardProps {
  contract: SmartContract
  onPause: () => void
  onUnpause: () => void
  onUpgrade: () => void
  onViewDetails: () => void
}

function ContractCard({ contract, onPause, onUnpause, onUpgrade, onViewDetails }: ContractCardProps) {
  const getContractTypeColor = (type: ContractType) => {
    switch (type) {
      case ContractType.ERC721:
        return 'text-blue-600 bg-blue-100'
      case ContractType.ERC1155:
        return 'text-purple-600 bg-purple-100'
      case ContractType.MARKETPLACE:
        return 'text-green-600 bg-green-100'
      case ContractType.EVOLUTION:
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getRiskLevelColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'text-green-600 bg-green-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'high':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">{contract.name}</h3>
          <p className="text-sm text-gray-600">{contract.address.slice(0, 10)}...{contract.address.slice(-8)}</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getContractTypeColor(contract.type)}`}>
            {contract.type.toUpperCase()}
          </span>
          
          {contract.isVerified && (
            <ShieldCheckIcon className="h-4 w-4 text-green-500" />
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Network</div>
          <div className="text-sm text-gray-600 capitalize">{contract.network}</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Version</div>
          <div className="text-sm text-gray-600">{contract.version}</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Transactions</div>
          <div className="text-sm text-gray-600">{contract.totalTransactions.toLocaleString()}</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Risk Level</div>
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(contract.riskLevel)}`}>
            {contract.riskLevel}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {contract.isUpgradeable && (
            <button
              onClick={onUpgrade}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              <ArrowUpIcon className="h-4 w-4" />
            </button>
          )}
          
          <button
            onClick={contract.isActive ? onPause : onUnpause}
            className={`text-sm ${contract.isActive ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}`}
          >
            {contract.isActive ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
          </button>
        </div>
        
        <button
          onClick={onViewDetails}
          className="text-sm text-gray-600 hover:text-gray-700"
        >
          <EyeIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
  )
}

interface DeployContractModalProps {
  onClose: () => void
  onDeploy: (contractData: any) => void
}

function DeployContractModal({ onClose, onDeploy }: DeployContractModalProps) {
  const [contractData, setContractData] = useState({
    contractType: ContractType.ERC721,
    network: BlockchainNetwork.ETHEREUM,
    constructorArgs: [],
    gasStrategy: 'standard' as any,
    verifyContract: true
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onDeploy(contractData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Deploy Smart Contract</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">×</button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Contract Type</label>
            <select
              value={contractData.contractType}
              onChange={(e) => setContractData(prev => ({ ...prev, contractType: e.target.value as ContractType }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={ContractType.ERC721}>ERC721 NFT</option>
              <option value={ContractType.ERC1155}>ERC1155 Multi-Token</option>
              <option value={ContractType.MARKETPLACE}>Marketplace</option>
              <option value={ContractType.EVOLUTION}>Evolution</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Network</label>
            <select
              value={contractData.network}
              onChange={(e) => setContractData(prev => ({ ...prev, network: e.target.value as BlockchainNetwork }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={BlockchainNetwork.ETHEREUM}>Ethereum</option>
              <option value={BlockchainNetwork.POLYGON}>Polygon</option>
              <option value={BlockchainNetwork.BSC}>BSC</option>
              <option value={BlockchainNetwork.BASE}>Base</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Gas Strategy</label>
            <select
              value={contractData.gasStrategy}
              onChange={(e) => setContractData(prev => ({ ...prev, gasStrategy: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="slow">Slow</option>
              <option value="standard">Standard</option>
              <option value="fast">Fast</option>
              <option value="instant">Instant</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="verifyContract"
              checked={contractData.verifyContract}
              onChange={(e) => setContractData(prev => ({ ...prev, verifyContract: e.target.checked }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="verifyContract" className="ml-2 text-sm text-gray-700">
              Verify contract on deployment
            </label>
          </div>

          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Deploy Contract
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

interface ContractDetailsModalProps {
  contract: SmartContract
  onClose: () => void
  onUpgrade: (newImplementation: string) => void
  onVerify: (sourceCode: string) => void
}

function ContractDetailsModal({ contract, onClose, onUpgrade, onVerify }: ContractDetailsModalProps) {
  const [activeTab, setActiveTab] = useState<'details' | 'upgrade' | 'verify'>('details')

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">{contract.name}</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">×</button>
        </div>

        <div className="border-b border-gray-200 mb-4">
          <nav className="-mb-px flex space-x-8">
            {['details', 'upgrade', 'verify'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </nav>
        </div>

        {activeTab === 'details' && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Address</label>
                <div className="mt-1 text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded">
                  {contract.address}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Network</label>
                <div className="mt-1 text-sm text-gray-900 capitalize">{contract.network}</div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Type</label>
                <div className="mt-1 text-sm text-gray-900">{contract.type}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Version</label>
                <div className="mt-1 text-sm text-gray-900">{contract.version}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Risk Level</label>
                <div className="mt-1 text-sm text-gray-900 capitalize">{contract.riskLevel}</div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <div className="mt-1 text-sm text-gray-900">{contract.description}</div>
            </div>
          </div>
        )}

        {activeTab === 'upgrade' && contract.isUpgradeable && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">New Implementation Address</label>
              <input
                type="text"
                placeholder="0x..."
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              onClick={() => onUpgrade('0x...')}
              className="w-full px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Upgrade Contract
            </button>
          </div>
        )}

        {activeTab === 'verify' && !contract.isVerified && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Source Code</label>
              <textarea
                rows={6}
                placeholder="Paste your contract source code here..."
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              onClick={() => onVerify('source code')}
              className="w-full px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
            >
              Verify Contract
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
