'use client'

import React, { useState } from 'react'
import {
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  ShareIcon,
  BookmarkIcon,
  EllipsisHorizontalIcon,
  PhotoIcon,
  GlobeAltIcon,
  UserGroupIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import {
  HeartIcon as HeartIconSolid,
  BookmarkIcon as BookmarkIconSolid
} from '@heroicons/react/24/solid'
import {
  useSocialFeed,
  useLikePost,
  useUnlikePost,
  useSharePost,
  useBookmarkPost,
  useUnbookmarkPost,
  useDeletePost
} from '@/hooks/useSocial'
import { FeedFilters, PostType, PostVisibility, SocialPost } from '@/types/social.types'
import PostComments from './PostComments'

interface SocialFeedProps {
  userId?: string
  communityId?: string
  className?: string
}

export default function SocialFeed({
  userId,
  communityId,
  className = ''
}: SocialFeedProps) {
  const [filters, setFilters] = useState<FeedFilters>({
    sortBy: 'recent',
    includeFollowing: true,
    includeCommunities: true
  })
  const [showComments, setShowComments] = useState<Record<string, boolean>>({})

  const {
    data: feedData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading
  } = useSocialFeed(filters)

  const likePostMutation = useLikePost()
  const unlikePostMutation = useUnlikePost()
  const sharePostMutation = useSharePost()
  const bookmarkPostMutation = useBookmarkPost()
  const unbookmarkPostMutation = useUnbookmarkPost()
  const deletePostMutation = useDeletePost()

  const posts = feedData?.pages.flatMap(page => page.posts) || []

  const handleLike = (post: SocialPost) => {
    if (post.engagement.isLikedByUser) {
      unlikePostMutation.mutate(post.id)
    } else {
      likePostMutation.mutate(post.id)
    }
  }

  const handleShare = (postId: string) => {
    sharePostMutation.mutate({ postId })
  }

  const handleBookmark = (post: SocialPost) => {
    if (post.engagement.isBookmarkedByUser) {
      unbookmarkPostMutation.mutate(post.id)
    } else {
      bookmarkPostMutation.mutate(post.id)
    }
  }

  const handleDelete = (postId: string) => {
    if (confirm('Are you sure you want to delete this post?')) {
      deletePostMutation.mutate(postId)
    }
  }

  const toggleComments = (postId: string) => {
    setShowComments(prev => ({
      ...prev,
      [postId]: !prev[postId]
    }))
  }

  const getPostTypeIcon = (type: PostType) => {
    switch (type) {
      case PostType.IMAGE:
        return <PhotoIcon className="h-4 w-4" />
      case PostType.NFT_SHOWCASE:
        return <GlobeAltIcon className="h-4 w-4" />
      default:
        return null
    }
  }

  const getVisibilityIcon = (visibility: PostVisibility) => {
    switch (visibility) {
      case PostVisibility.PUBLIC:
        return <GlobeAltIcon className="h-4 w-4 text-green-600" />
      case PostVisibility.FRIENDS:
        return <UserGroupIcon className="h-4 w-4 text-blue-600" />
      case PostVisibility.FOLLOWERS:
        return <EyeIcon className="h-4 w-4 text-purple-600" />
      default:
        return <EyeIcon className="h-4 w-4 text-gray-600" />
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
              <div className="space-y-2 mb-4">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Feed Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <select
            value={filters.sortBy}
            onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="recent">Most Recent</option>
            <option value="popular">Most Popular</option>
            <option value="trending">Trending</option>
          </select>

          <select
            value={filters.timeRange}
            onChange={(e) => setFilters(prev => ({ ...prev, timeRange: e.target.value as any }))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Time</option>
            <option value="day">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <label className="flex items-center text-sm text-gray-600">
            <input
              type="checkbox"
              checked={filters.includeFollowing}
              onChange={(e) => setFilters(prev => ({ ...prev, includeFollowing: e.target.checked }))}
              className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            Following
          </label>
          
          <label className="flex items-center text-sm text-gray-600">
            <input
              type="checkbox"
              checked={filters.includeCommunities}
              onChange={(e) => setFilters(prev => ({ ...prev, includeCommunities: e.target.checked }))}
              className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            Communities
          </label>
        </div>
      </div>

      {/* Posts */}
      {posts.length > 0 ? (
        <div className="space-y-6">
          {posts.map((post) => (
            <div key={post.id} className="bg-white border border-gray-200 rounded-lg">
              {/* Post Header */}
              <div className="p-6 pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <img
                      src={post.author.avatar || '/default-avatar.png'}
                      alt={post.author.displayName}
                      className="w-10 h-10 rounded-full"
                    />
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="text-sm font-medium text-gray-900">{post.author.displayName}</h3>
                        {post.author.isVerified && (
                          <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs">✓</span>
                          </div>
                        )}
                        {getPostTypeIcon(post.type)}
                      </div>
                      <div className="flex items-center space-x-2 text-xs text-gray-500">
                        <span>@{post.author.username}</span>
                        <span>•</span>
                        <span>{formatTimeAgo(post.createdAt)}</span>
                        <span>•</span>
                        {getVisibilityIcon(post.visibility)}
                        {post.isEdited && (
                          <>
                            <span>•</span>
                            <span>edited</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {post.isPinned && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        Pinned
                      </span>
                    )}
                    
                    <button className="text-gray-400 hover:text-gray-600">
                      <EllipsisHorizontalIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Post Content */}
              <div className="px-6 pb-4">
                <p className="text-gray-900 whitespace-pre-wrap">{post.content}</p>
                
                {/* Tags */}
                {post.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-3">
                    {post.tags.map((tag) => (
                      <span
                        key={tag}
                        className="text-blue-600 text-sm hover:text-blue-700 cursor-pointer"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Post Images */}
              {post.images && post.images.length > 0 && (
                <div className="px-6 pb-4">
                  <div className={`grid gap-2 ${
                    post.images.length === 1 ? 'grid-cols-1' :
                    post.images.length === 2 ? 'grid-cols-2' :
                    'grid-cols-2'
                  }`}>
                    {post.images.slice(0, 4).map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={image}
                          alt={`Post image ${index + 1}`}
                          className="w-full h-48 object-cover rounded-lg"
                        />
                        {post.images.length > 4 && index === 3 && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                            <span className="text-white text-lg font-medium">
                              +{post.images.length - 4} more
                            </span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Post Engagement */}
              <div className="px-6 py-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <button
                      onClick={() => handleLike(post)}
                      className={`flex items-center space-x-2 text-sm ${
                        post.engagement.isLikedByUser
                          ? 'text-red-600'
                          : 'text-gray-600 hover:text-red-600'
                      }`}
                    >
                      {post.engagement.isLikedByUser ? (
                        <HeartIconSolid className="h-5 w-5" />
                      ) : (
                        <HeartIcon className="h-5 w-5" />
                      )}
                      <span>{post.engagement.likesCount}</span>
                    </button>

                    <button
                      onClick={() => toggleComments(post.id)}
                      className="flex items-center space-x-2 text-sm text-gray-600 hover:text-blue-600"
                    >
                      <ChatBubbleOvalLeftIcon className="h-5 w-5" />
                      <span>{post.engagement.commentsCount}</span>
                    </button>

                    <button
                      onClick={() => handleShare(post.id)}
                      className="flex items-center space-x-2 text-sm text-gray-600 hover:text-green-600"
                    >
                      <ShareIcon className="h-5 w-5" />
                      <span>{post.engagement.sharesCount}</span>
                    </button>
                  </div>

                  <div className="flex items-center space-x-4">
                    <span className="text-sm text-gray-500">
                      {post.engagement.viewsCount} views
                    </span>
                    
                    <button
                      onClick={() => handleBookmark(post)}
                      className={`text-sm ${
                        post.engagement.isBookmarkedByUser
                          ? 'text-blue-600'
                          : 'text-gray-600 hover:text-blue-600'
                      }`}
                    >
                      {post.engagement.isBookmarkedByUser ? (
                        <BookmarkIconSolid className="h-5 w-5" />
                      ) : (
                        <BookmarkIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {/* Comments Section */}
              {showComments[post.id] && (
                <div className="border-t border-gray-200">
                  <PostComments postId={post.id} />
                </div>
              )}
            </div>
          ))}

          {/* Load More */}
          {hasNextPage && (
            <div className="text-center">
              <button
                onClick={() => fetchNextPage()}
                disabled={isFetchingNextPage}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                {isFetchingNextPage ? 'Loading...' : 'Load More Posts'}
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <ChatBubbleOvalLeftIcon className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
          <p className="text-gray-600">
            {userId ? 'This user hasn\'t posted anything yet.' : 'Be the first to share something with the community!'}
          </p>
        </div>
      )}
    </div>
  )
}
