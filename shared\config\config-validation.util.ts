/**
 * Configuration Validation Utilities
 * Provides validation functions for configuration values
 */

import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { ConfigValidationResult } from './base-config.interface';
import { Environment, ServiceEnvironment, LogLevel } from './environment.enum';

/**
 * Configuration validation utility class
 */
export class ConfigValidationUtil {
  /**
   * Validate configuration object using class-validator
   */
  static async validateConfig<T extends object>(
    ConfigClass: new () => T,
    configData: any
  ): Promise<ConfigValidationResult> {
    try {
      // Transform plain object to class instance
      const configInstance = plainToClass(ConfigClass, configData);
      
      // Validate the instance
      const errors = await validate(configInstance);
      
      if (errors.length > 0) {
        const errorMessages = this.formatValidationErrors(errors);
        return {
          isValid: false,
          errors: errorMessages,
          warnings: [],
        };
      }
      
      return {
        isValid: true,
        errors: [],
        warnings: [],
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Configuration validation failed: ${error.message}`],
        warnings: [],
      };
    }
  }

  /**
   * Format validation errors into readable messages
   */
  private static formatValidationErrors(errors: ValidationError[]): string[] {
    const messages: string[] = [];
    
    for (const error of errors) {
      if (error.constraints) {
        for (const constraint of Object.values(error.constraints)) {
          messages.push(`${error.property}: ${constraint}`);
        }
      }
      
      // Handle nested validation errors
      if (error.children && error.children.length > 0) {
        const childMessages = this.formatValidationErrors(error.children);
        messages.push(...childMessages.map(msg => `${error.property}.${msg}`));
      }
    }
    
    return messages;
  }

  /**
   * Validate required environment variables
   */
  static validateRequiredEnvVars(requiredVars: string[]): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    for (const varName of requiredVars) {
      const value = process.env[varName];
      
      if (!value || value.trim() === '') {
        errors.push(`Required environment variable ${varName} is not set`);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate environment variable format
   */
  static validateEnvVarFormat(varName: string, pattern: RegExp): boolean {
    const value = process.env[varName];
    if (!value) return false;
    return pattern.test(value);
  }

  /**
   * Validate URL format
   */
  static validateUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate port number
   */
  static validatePort(port: number): boolean {
    return Number.isInteger(port) && port > 0 && port <= 65535;
  }

  /**
   * Validate database URL
   */
  static validateDatabaseUrl(url: string): boolean {
    if (!url) return false;
    
    // Basic database URL validation
    const dbUrlPattern = /^(postgresql|mysql|sqlite|mongodb):\/\/.+/;
    return dbUrlPattern.test(url);
  }

  /**
   * Validate Redis URL
   */
  static validateRedisUrl(url: string): boolean {
    if (!url) return false;
    
    // Basic Redis URL validation
    const redisUrlPattern = /^redis:\/\/.+/;
    return redisUrlPattern.test(url);
  }

  /**
   * Validate JWT secret strength
   */
  static validateJwtSecret(secret: string): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!secret) {
      errors.push('JWT secret is required');
      return { isValid: false, errors, warnings };
    }
    
    if (secret.length < 32) {
      warnings.push('JWT secret should be at least 32 characters long for security');
    }
    
    if (secret === 'your-secret-key' || secret === 'default-jwt-secret') {
      errors.push('JWT secret must be changed from default value');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate environment consistency
   */
  static validateEnvironmentConsistency(): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    const nodeEnv = process.env.NODE_ENV as Environment;
    const serviceEnv = process.env.SERVICE_ENVIRONMENT as ServiceEnvironment;
    const useMockServices = process.env.USE_MOCK_SERVICES === 'true';
    
    // Check environment consistency
    if (nodeEnv === Environment.PRODUCTION) {
      if (useMockServices) {
        errors.push('Mock services should not be used in production environment');
      }
      
      if (serviceEnv === ServiceEnvironment.MOCK) {
        errors.push('Service environment should not be mock in production');
      }
      
      if (process.env.ENABLE_DEBUG_LOGGING === 'true') {
        warnings.push('Debug logging should be disabled in production');
      }
      
      if (process.env.ENABLE_SWAGGER === 'true') {
        warnings.push('Swagger should be disabled in production');
      }
    }
    
    // Check development environment
    if (nodeEnv === Environment.DEVELOPMENT) {
      if (!useMockServices && serviceEnv === ServiceEnvironment.MOCK) {
        warnings.push('USE_MOCK_SERVICES and SERVICE_ENVIRONMENT are inconsistent');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate service dependencies
   */
  static validateServiceDependencies(serviceUrls: Record<string, string>): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    for (const [serviceName, url] of Object.entries(serviceUrls)) {
      if (!url) {
        errors.push(`${serviceName} URL is not configured`);
        continue;
      }
      
      if (!this.validateUrl(url)) {
        errors.push(`${serviceName} URL is not a valid URL: ${url}`);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate configuration completeness
   */
  static validateConfigCompleteness(config: any, requiredFields: string[]): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    for (const field of requiredFields) {
      const value = this.getNestedValue(config, field);
      
      if (value === undefined || value === null || value === '') {
        errors.push(`Required configuration field '${field}' is missing or empty`);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get nested value from object using dot notation
   */
  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Validate numeric range
   */
  static validateNumericRange(value: number, min: number, max: number): boolean {
    return value >= min && value <= max;
  }

  /**
   * Validate string length
   */
  static validateStringLength(value: string, minLength: number, maxLength?: number): boolean {
    if (!value) return false;
    if (value.length < minLength) return false;
    if (maxLength && value.length > maxLength) return false;
    return true;
  }

  /**
   * Validate array length
   */
  static validateArrayLength(value: any[], minLength: number, maxLength?: number): boolean {
    if (!Array.isArray(value)) return false;
    if (value.length < minLength) return false;
    if (maxLength && value.length > maxLength) return false;
    return true;
  }
}
