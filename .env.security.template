# Security Configuration Template
# Copy this to your .env file and customize for your environment

# =============================================================================
# GATEWAY SECURITY CONFIGURATION
# =============================================================================

# Gateway Secret for Service-to-Service Authentication
# IMPORTANT: Change this in production!
GATEWAY_SECRET=your-super-secret-gateway-key-change-in-production

# Allow Direct Service Access (Development Only)
# Set to 'false' in production to block direct service access
ALLOW_DIRECT_ACCESS=true

# Trusted IP Addresses (comma-separated)
# IPs that are allowed to make requests to services
TRUSTED_IPS=127.0.0.1,::1,localhost

# =============================================================================
# SERVICE SECURITY CONFIGURATION
# =============================================================================

# Enable Security Middleware
ENABLE_GATEWAY_AUTH=true

# Security Logging Level
SECURITY_LOG_LEVEL=debug

# Request Timeout (milliseconds)
REQUEST_TIMEOUT=30000

# =============================================================================
# PRODUCTION SECURITY SETTINGS
# =============================================================================

# Enable HTTPS Only (Production)
FORCE_HTTPS=false

# Enable CORS Restrictions (Production)
CORS_ORIGIN=http://localhost:3000,http://localhost:3010

# Enable Rate Limiting
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# NETWORK SECURITY (Docker/Production)
# =============================================================================

# Internal Network Name (Docker)
INTERNAL_NETWORK=social-nft-internal

# Service Discovery Method
SERVICE_DISCOVERY=static

# Load Balancer Configuration
LOAD_BALANCER_ENABLED=false

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Enable Request Tracing
ENABLE_REQUEST_TRACING=true

# Log Security Events
LOG_SECURITY_EVENTS=true

# Audit Trail Configuration
ENABLE_AUDIT_TRAIL=true

# =============================================================================
# DEVELOPMENT OVERRIDES
# =============================================================================

# Skip Security in Development
SKIP_SECURITY_IN_DEV=false

# Allow Health Check Bypass
ALLOW_HEALTH_CHECK_BYPASS=true

# Debug Security Middleware
DEBUG_SECURITY=true
