import { <PERSON>, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('Projects')
@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Get Project Service information' })
  @ApiResponse({ 
    status: 200, 
    description: 'Service information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        service: { type: 'string', example: 'Project Service' },
        version: { type: 'string', example: '1.0.0' },
        status: { type: 'string', example: 'running' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        uptime: { type: 'number', example: 12345 },
        environment: { type: 'string', example: 'development' },
        features: {
          type: 'object',
          properties: {
            projectManagement: { type: 'boolean', example: true },
            campaignManagement: { type: 'boolean', example: true },
            healthChecks: { type: 'boolean', example: true },
            swagger: { type: 'boolean', example: true }
          }
        }
      }
    }
  })
  getServiceInfo() {
    this.logger.log('Service info requested');
    return this.appService.getServiceInfo();
  }

  @Get('status')
  @ApiOperation({ summary: 'Get Project Service status' })
  @ApiResponse({ 
    status: 200, 
    description: 'Service status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        uptime: { type: 'number', example: 12345 },
        memory: {
          type: 'object',
          properties: {
            used: { type: 'number', example: 50000000 },
            total: { type: 'number', example: 100000000 }
          }
        }
      }
    }
  })
  getStatus() {
    this.logger.log('Service status requested');
    return this.appService.getStatus();
  }
}
