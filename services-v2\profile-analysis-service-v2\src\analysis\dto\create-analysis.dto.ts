/**
 * Create Analysis DTO - Profile Analysis Service V2
 * 
 * Data Transfer Object for creating profile analysis
 */

import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsIn,
  IsObject,
  IsUUID,
} from 'class-validator';

export class CreateAnalysisDto {
  @ApiProperty({
    description: 'User ID from User Service',
    example: 'clp123abc456def789',
  })
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Type of analysis to perform',
    example: 'twitter',
    enum: ['twitter', 'instagram', 'linkedin', 'tiktok', 'youtube', 'general'],
  })
  @IsString()
  @IsIn(['twitter', 'instagram', 'linkedin', 'tiktok', 'youtube', 'general'])
  analysisType: string;

  @ApiProperty({
    description: 'Source URL of the profile to analyze',
    example: 'https://twitter.com/username',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  sourceUrl?: string;

  @ApiProperty({
    description: 'Additional metadata for the analysis',
    example: { priority: 'high', tags: ['influencer', 'tech'] },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
