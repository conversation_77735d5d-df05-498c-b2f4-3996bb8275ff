/**
 * Response Service
 * 
 * Provides standardized response formatting across all microservices
 * Implements correlation ID tracking and consistent response structure
 */

import { Injectable } from '@nestjs/common';
import { StandardizedConfigService } from '../../config/services/standardized-config.service';
import { 
  BaseResponse, 
  SuccessResponse, 
  ErrorResponse, 
  PaginatedResponse,
  ErrorCode,
  ResponseBuilderOptions,
  ErrorResponseBuilderOptions
} from '../interfaces/base-response.interface';
import { 
  PaginationMeta,
  FilterOptions,
  SortingOptions 
} from '../interfaces/pagination.interface';
import { ResponseBuilder } from '../utils/response-builder.util';

/**
 * Response Service
 * Centralized service for creating standardized API responses
 */
@Injectable()
export class ResponseService {
  private readonly serviceName: string;
  private readonly serviceVersion: string;

  constructor(private readonly configService: StandardizedConfigService) {
    const serviceConfig = this.configService.getServiceConfig();
    this.serviceName = serviceConfig.serviceName;
    this.serviceVersion = serviceConfig.version;
  }

  /**
   * Create a success response
   */
  success<T>(
    data: T,
    message?: string,
    correlationId?: string
  ): SuccessResponse<T> {
    return ResponseBuilder.success(data, {
      message,
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a created response (201)
   */
  created<T>(
    data: T,
    message?: string,
    correlationId?: string
  ): SuccessResponse<T> {
    return ResponseBuilder.created(data, {
      message: message || 'Resource created successfully',
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create an updated response (200)
   */
  updated<T>(
    data: T,
    message?: string,
    correlationId?: string
  ): SuccessResponse<T> {
    return ResponseBuilder.updated(data, {
      message: message || 'Resource updated successfully',
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a deleted response (200)
   */
  deleted(
    message?: string,
    correlationId?: string
  ): SuccessResponse<{ deleted: boolean }> {
    return ResponseBuilder.deleted({
      message: message || 'Resource deleted successfully',
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a paginated response
   */
  paginated<T>(
    data: T[],
    pagination: PaginationMeta,
    options?: {
      filters?: FilterOptions[];
      sorting?: SortingOptions[];
      message?: string;
      correlationId?: string;
    }
  ): PaginatedResponse<T> {
    return ResponseBuilder.paginated(data, pagination, {
      filters: options?.filters,
      sorting: options?.sorting,
      message: options?.message || 'Data retrieved successfully',
      correlationId: options?.correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create an error response
   */
  error(
    message: string,
    code: ErrorCode = ErrorCode.INTERNAL_SERVER_ERROR,
    options?: {
      details?: any;
      validation?: any[];
      statusCode?: number;
      correlationId?: string;
    }
  ): ErrorResponse {
    return ResponseBuilder.error(message, code, {
      details: options?.details,
      validation: options?.validation,
      statusCode: options?.statusCode,
      correlationId: options?.correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a validation error response
   */
  validationError(
    validationErrors: any[],
    correlationId?: string
  ): ErrorResponse {
    return ResponseBuilder.validationError(validationErrors, {
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a not found error response
   */
  notFound(
    resource: string,
    id?: string,
    correlationId?: string
  ): ErrorResponse {
    return ResponseBuilder.notFound(resource, id, {
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create an unauthorized error response
   */
  unauthorized(
    message: string = 'Authentication required',
    correlationId?: string
  ): ErrorResponse {
    return ResponseBuilder.unauthorized(message, {
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a forbidden error response
   */
  forbidden(
    message: string = 'Access denied',
    correlationId?: string
  ): ErrorResponse {
    return ResponseBuilder.forbidden(message, {
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a conflict error response
   */
  conflict(
    message: string,
    correlationId?: string
  ): ErrorResponse {
    return ResponseBuilder.conflict(message, {
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a rate limit exceeded error response
   */
  rateLimitExceeded(
    retryAfter?: number,
    correlationId?: string
  ): ErrorResponse {
    return ResponseBuilder.rateLimitExceeded(retryAfter, {
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a service unavailable error response
   */
  serviceUnavailable(
    service: string,
    correlationId?: string
  ): ErrorResponse {
    return ResponseBuilder.serviceUnavailable(service, {
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Create a business rule violation error response
   */
  businessRuleViolation(
    rule: string,
    details?: any,
    correlationId?: string
  ): ErrorResponse {
    return ResponseBuilder.businessRuleViolation(rule, details, {
      correlationId: correlationId || this.generateCorrelationId(),
      service: this.serviceName,
      version: this.serviceVersion,
    });
  }

  /**
   * Build pagination metadata
   */
  buildPaginationMeta(
    page: number,
    limit: number,
    totalCount: number
  ): PaginationMeta {
    return ResponseBuilder.buildPaginationMeta(page, limit, totalCount);
  }

  /**
   * Sanitize response data
   */
  sanitizeResponse<T>(response: T, sensitiveFields?: string[]): T {
    return ResponseBuilder.sanitizeResponse(response, sensitiveFields);
  }

  /**
   * Generate correlation ID
   */
  private generateCorrelationId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `${this.serviceName}_${timestamp}_${random}`;
  }

  /**
   * Create base response metadata
   */
  private createBaseResponse(): Omit<BaseResponse, 'success' | 'data'> {
    return {
      correlationId: this.generateCorrelationId(),
      timestamp: new Date().toISOString(),
      service: this.serviceName,
      version: this.serviceVersion,
    };
  }

  /**
   * Transform any data to standardized response format
   */
  transform<T>(data: any): SuccessResponse<T> {
    // If already standardized, return as-is
    if (this.isStandardizedResponse(data)) {
      return data as any;
    }

    // Transform to standardized format
    return this.success(data);
  }

  /**
   * Check if data is already a standardized response
   */
  private isStandardizedResponse(data: any): data is BaseResponse {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.success === 'boolean' &&
      data.correlationId &&
      data.timestamp &&
      data.service &&
      data.version
    );
  }
}
