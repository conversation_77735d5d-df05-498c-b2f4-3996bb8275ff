'use client'

import React from 'react'
import {
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  PlayIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import { useEvolutionTimeline } from '@/hooks/useNFTEvolution'
import { NFTEvolution, EvolutionStatus } from '@/types/nft-evolution.types'

interface EvolutionTimelineProps {
  nftId: string
  evolutions?: NFTEvolution[]
  isLoading: boolean
  onEvolutionCreated?: (evolutionId: string) => void
  onEvolutionUpdated?: (evolutionId: string) => void
  className?: string
}

export default function EvolutionTimeline({
  nftId,
  evolutions = [],
  isLoading,
  onEvolutionCreated,
  onEvolutionUpdated,
  className = ''
}: EvolutionTimelineProps) {
  const { data: timeline, isLoading: timelineLoading } = useEvolutionTimeline(nftId)

  const getStatusIcon = (status: EvolutionStatus) => {
    switch (status) {
      case EvolutionStatus.COMPLETED:
        return <CheckCircleIcon className="h-6 w-6 text-green-600" />
      case EvolutionStatus.IN_PROGRESS:
        return <PlayIcon className="h-6 w-6 text-blue-600" />
      case EvolutionStatus.PENDING:
        return <ClockIcon className="h-6 w-6 text-yellow-600" />
      case EvolutionStatus.FAILED:
        return <XCircleIcon className="h-6 w-6 text-red-600" />
      case EvolutionStatus.CANCELLED:
        return <ExclamationTriangleIcon className="h-6 w-6 text-orange-600" />
      default:
        return <ClockIcon className="h-6 w-6 text-gray-600" />
    }
  }

  const getStatusColor = (status: EvolutionStatus) => {
    switch (status) {
      case EvolutionStatus.COMPLETED:
        return 'text-green-600 bg-green-100'
      case EvolutionStatus.IN_PROGRESS:
        return 'text-blue-600 bg-blue-100'
      case EvolutionStatus.PENDING:
        return 'text-yellow-600 bg-yellow-100'
      case EvolutionStatus.FAILED:
        return 'text-red-600 bg-red-100'
      case EvolutionStatus.CANCELLED:
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (isLoading || timelineLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg">
              <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Timeline Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Evolution Timeline</h2>
          <p className="text-sm text-gray-600">Track your NFT's evolutionary journey</p>
        </div>
        
        {timeline && (
          <div className="text-sm text-gray-600">
            Stage {timeline.currentStage} of {timeline.stages.length}
          </div>
        )}
      </div>

      {/* Current Stage Progress */}
      {timeline && timeline.stages.length > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-900">Evolution Progress</span>
            <span className="text-sm text-gray-600">
              Stage {timeline.currentStage} / {timeline.stages.length}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(timeline.currentStage / timeline.stages.length) * 100}%` }}
            ></div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {timeline.currentStage === timeline.stages.length 
              ? 'Evolution complete!' 
              : `${timeline.stages.length - timeline.currentStage} stages remaining`
            }
          </div>
        </div>
      )}

      {/* Evolution Timeline */}
      {evolutions.length > 0 ? (
        <div className="space-y-4">
          {evolutions
            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
            .map((evolution, index) => (
              <div
                key={evolution.id}
                className="relative bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all"
              >
                {/* Timeline Connector */}
                {index < evolutions.length - 1 && (
                  <div className="absolute left-9 top-16 w-0.5 h-8 bg-gray-200"></div>
                )}

                <div className="flex items-start space-x-4">
                  {/* Status Icon */}
                  <div className="flex-shrink-0 mt-1">
                    {getStatusIcon(evolution.status)}
                  </div>

                  {/* Evolution Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          Evolution Stage {evolution.evolutionStage}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {evolution.triggerType.replace('_', ' ')} trigger
                        </p>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(evolution.status)}`}>
                          {evolution.status.replace('_', ' ')}
                        </span>
                        
                        {evolution.status === EvolutionStatus.IN_PROGRESS && (
                          <div className="text-sm text-gray-600">
                            {evolution.progress}% complete
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Progress Bar for Active Evolutions */}
                    {evolution.status === EvolutionStatus.IN_PROGRESS && (
                      <div className="mt-3">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${evolution.progress}%` }}
                          ></div>
                        </div>
                        {evolution.estimatedCompletion && (
                          <div className="mt-1 text-xs text-gray-500">
                            Estimated completion: {new Date(evolution.estimatedCompletion).toLocaleString()}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Evolution Changes Summary */}
                    <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                      {evolution.traitChanges.length > 0 && (
                        <div className="bg-blue-50 rounded-lg p-3">
                          <div className="text-sm font-medium text-blue-900">Trait Changes</div>
                          <div className="text-sm text-blue-700">
                            {evolution.traitChanges.length} traits modified
                          </div>
                        </div>
                      )}

                      {evolution.visualChanges.length > 0 && (
                        <div className="bg-purple-50 rounded-lg p-3">
                          <div className="text-sm font-medium text-purple-900">Visual Changes</div>
                          <div className="text-sm text-purple-700">
                            {evolution.visualChanges.length} visual elements updated
                          </div>
                        </div>
                      )}

                      {evolution.metadataChanges.length > 0 && (
                        <div className="bg-green-50 rounded-lg p-3">
                          <div className="text-sm font-medium text-green-900">Metadata Changes</div>
                          <div className="text-sm text-green-700">
                            {evolution.metadataChanges.length} metadata fields updated
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Evolution Impact */}
                    <div className="mt-4 flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-4">
                        <div>
                          <span className="text-gray-600">Rarity Impact:</span>
                          <span className={`ml-1 font-medium ${
                            evolution.rarityImpact > 0 ? 'text-green-600' : 
                            evolution.rarityImpact < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {evolution.rarityImpact > 0 ? '+' : ''}{evolution.rarityImpact.toFixed(1)}
                          </span>
                        </div>
                        
                        <div>
                          <span className="text-gray-600">Value Impact:</span>
                          <span className={`ml-1 font-medium ${
                            evolution.valueImpact > 0 ? 'text-green-600' : 
                            evolution.valueImpact < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {evolution.valueImpact > 0 ? '+' : ''}{evolution.valueImpact.toFixed(1)}%
                          </span>
                        </div>

                        <div>
                          <span className="text-gray-600">Evolution Score:</span>
                          <span className="ml-1 font-medium text-purple-600">
                            {evolution.evolutionScore.toFixed(1)}/10
                          </span>
                        </div>
                      </div>

                      <div className="text-gray-500">
                        {new Date(evolution.createdAt).toLocaleDateString()}
                      </div>
                    </div>

                    {/* Community Reaction */}
                    {evolution.communityReaction && (
                      <div className="mt-4 flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <span>👍</span>
                          <span>{evolution.communityReaction.likes}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span>💬</span>
                          <span>{evolution.communityReaction.comments}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span>🔄</span>
                          <span>{evolution.communityReaction.shares}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span>Sentiment:</span>
                          <span className={`font-medium ${
                            evolution.communityReaction.sentimentScore > 0.5 ? 'text-green-600' :
                            evolution.communityReaction.sentimentScore < -0.5 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {evolution.communityReaction.sentimentScore > 0.5 ? 'Positive' :
                             evolution.communityReaction.sentimentScore < -0.5 ? 'Negative' : 'Neutral'}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No evolutions yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            Start your NFT's evolutionary journey by activating a trigger.
          </p>
        </div>
      )}

      {/* Next Possible Evolutions */}
      {timeline && timeline.nextPossibleEvolutions && timeline.nextPossibleEvolutions.length > 0 && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-purple-900 mb-3">
            Next Possible Evolutions ({timeline.nextPossibleEvolutions.length})
          </h3>
          <div className="space-y-2">
            {timeline.nextPossibleEvolutions.slice(0, 3).map((trigger) => (
              <div key={trigger.id} className="flex items-center justify-between p-2 bg-white rounded border border-purple-200">
                <div>
                  <span className="text-sm font-medium text-gray-900">{trigger.name}</span>
                  <span className="ml-2 text-xs text-gray-600">
                    {trigger.type.replace('_', ' ')}
                  </span>
                </div>
                <button
                  onClick={() => onEvolutionCreated?.(trigger.id)}
                  className="text-xs text-purple-600 hover:text-purple-700 font-medium"
                >
                  Activate
                </button>
              </div>
            ))}
            {timeline.nextPossibleEvolutions.length > 3 && (
              <div className="text-xs text-purple-600 text-center">
                +{timeline.nextPossibleEvolutions.length - 3} more triggers available
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
