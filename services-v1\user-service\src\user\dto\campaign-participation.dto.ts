import { IsString, IsOptional, <PERSON>Enum, IsNumber, IsBoolean, IsArray, IsDateString, ValidateNested, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ParticipationRequirement } from './campaign.dto';

export enum ParticipationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  DISQUALIFIED = 'disqualified',
}

export enum SubmissionStatus {
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  NEEDS_REVISION = 'needs_revision',
}

export class RequirementSubmissionDto {
  @ApiProperty({
    description: 'Requirement type being submitted',
    enum: ParticipationRequirement,
    example: ParticipationRequirement.TWITTER_RETWEET,
  })
  @IsEnum(ParticipationRequirement)
  requirementType: ParticipationRequirement;

  @ApiPropertyOptional({
    description: 'Submission value or proof',
    example: 'https://twitter.com/user/status/123456789',
  })
  @IsOptional()
  @IsString()
  submissionValue?: string;

  @ApiPropertyOptional({
    description: 'Additional proof or evidence',
    example: ['https://screenshot.com/proof1.png', 'https://screenshot.com/proof2.png'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  proofUrls?: string[];

  @ApiPropertyOptional({
    description: 'User notes or comments about the submission',
    example: 'Completed the retweet as requested',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata for the submission',
    example: { tweetId: '123456789', retweetCount: 5 },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class JoinCampaignDto {
  @ApiProperty({
    description: 'Campaign ID to join',
    example: 'campaign_123',
  })
  @IsString()
  campaignId: string;

  @ApiPropertyOptional({
    description: 'Referral code if joining through referral',
    example: 'REF123ABC',
  })
  @IsOptional()
  @IsString()
  referralCode?: string;

  @ApiPropertyOptional({
    description: 'User consent to campaign terms',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  acceptTerms?: boolean = true;

  @ApiPropertyOptional({
    description: 'Initial requirement submissions',
    type: [RequirementSubmissionDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RequirementSubmissionDto)
  initialSubmissions?: RequirementSubmissionDto[];
}

export class SubmitRequirementDto {
  @ApiProperty({
    description: 'Campaign participation ID',
    example: 'participation_123',
  })
  @IsString()
  participationId: string;

  @ApiProperty({
    description: 'Requirement submission details',
    type: RequirementSubmissionDto,
  })
  @ValidateNested()
  @Type(() => RequirementSubmissionDto)
  submission: RequirementSubmissionDto;
}

export class ReviewSubmissionDto {
  @ApiProperty({
    description: 'Submission ID to review',
    example: 'submission_123',
  })
  @IsString()
  submissionId: string;

  @ApiProperty({
    description: 'Review decision',
    enum: SubmissionStatus,
    example: SubmissionStatus.APPROVED,
  })
  @IsEnum(SubmissionStatus)
  status: SubmissionStatus;

  @ApiPropertyOptional({
    description: 'Points awarded for the submission',
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  pointsAwarded?: number;

  @ApiPropertyOptional({
    description: 'Reviewer comments',
    example: 'Great submission! All requirements met.',
  })
  @IsOptional()
  @IsString()
  reviewerComments?: string;

  @ApiPropertyOptional({
    description: 'Feedback for improvement if rejected',
    example: 'Please provide a clearer screenshot of the retweet.',
  })
  @IsOptional()
  @IsString()
  feedback?: string;
}

export class CampaignParticipationResponseDto {
  @ApiProperty({
    description: 'Participation ID',
    example: 'participation_123',
  })
  id: string;

  @ApiProperty({
    description: 'User ID',
    example: 'user_123',
  })
  userId: string;

  @ApiProperty({
    description: 'Campaign ID',
    example: 'campaign_123',
  })
  campaignId: string;

  @ApiProperty({
    description: 'Campaign name',
    example: 'Summer NFT Collection Launch',
  })
  campaignName: string;

  @ApiProperty({
    description: 'Participation status',
    enum: ParticipationStatus,
    example: ParticipationStatus.IN_PROGRESS,
  })
  status: ParticipationStatus;

  @ApiProperty({
    description: 'Total points earned',
    example: 75,
  })
  totalPoints: number;

  @ApiProperty({
    description: 'Progress percentage',
    example: 60.5,
  })
  progressPercentage: number;

  @ApiProperty({
    description: 'Requirements completed',
    example: 3,
  })
  requirementsCompleted: number;

  @ApiProperty({
    description: 'Total requirements',
    example: 5,
  })
  totalRequirements: number;

  @ApiProperty({
    description: 'Participation start date',
    example: '2025-06-03T20:30:00Z',
  })
  joinedAt: string;

  @ApiPropertyOptional({
    description: 'Participation completion date',
    example: '2025-06-15T14:20:00Z',
  })
  completedAt?: string;

  @ApiPropertyOptional({
    description: 'Referral code used',
    example: 'REF123ABC',
  })
  referralCode?: string;

  @ApiProperty({
    description: 'Requirement submissions',
  })
  submissions: RequirementSubmissionResponseDto[];

  @ApiProperty({
    description: 'Rewards earned',
  })
  rewardsEarned: CampaignRewardEarnedDto[];
}

export class RequirementSubmissionResponseDto {
  @ApiProperty({
    description: 'Submission ID',
    example: 'submission_123',
  })
  id: string;

  @ApiProperty({
    description: 'Requirement type',
    enum: ParticipationRequirement,
    example: ParticipationRequirement.TWITTER_RETWEET,
  })
  requirementType: ParticipationRequirement;

  @ApiProperty({
    description: 'Submission status',
    enum: SubmissionStatus,
    example: SubmissionStatus.APPROVED,
  })
  status: SubmissionStatus;

  @ApiPropertyOptional({
    description: 'Submission value',
    example: 'https://twitter.com/user/status/123456789',
  })
  submissionValue?: string;

  @ApiPropertyOptional({
    description: 'Points awarded',
    example: 10,
  })
  pointsAwarded?: number;

  @ApiProperty({
    description: 'Submission date',
    example: '2025-06-03T20:30:00Z',
  })
  submittedAt: string;

  @ApiPropertyOptional({
    description: 'Review date',
    example: '2025-06-04T09:15:00Z',
  })
  reviewedAt?: string;

  @ApiPropertyOptional({
    description: 'Reviewer comments',
    example: 'Excellent submission!',
  })
  reviewerComments?: string;

  @ApiPropertyOptional({
    description: 'Feedback for improvement',
    example: 'Consider adding more engagement next time.',
  })
  feedback?: string;
}

export class CampaignRewardEarnedDto {
  @ApiProperty({
    description: 'Reward ID',
    example: 'reward_123',
  })
  id: string;

  @ApiProperty({
    description: 'Reward name',
    example: 'Exclusive Campaign NFT',
  })
  name: string;

  @ApiProperty({
    description: 'Reward type',
    example: 'nft_generation',
  })
  type: string;

  @ApiPropertyOptional({
    description: 'Reward value',
    example: 100,
  })
  value?: number;

  @ApiPropertyOptional({
    description: 'Reward rarity',
    example: 'rare',
  })
  rarity?: string;

  @ApiProperty({
    description: 'Date reward was earned',
    example: '2025-06-15T14:20:00Z',
  })
  earnedAt: string;

  @ApiPropertyOptional({
    description: 'Date reward was claimed',
    example: '2025-06-16T10:30:00Z',
  })
  claimedAt?: string;

  @ApiProperty({
    description: 'Whether reward has been claimed',
    example: false,
  })
  claimed: boolean;

  @ApiPropertyOptional({
    description: 'Additional reward metadata',
    example: { nftTokenId: '12345', transactionHash: '0xabc...' },
  })
  metadata?: Record<string, any>;
}

export class ClaimRewardDto {
  @ApiProperty({
    description: 'Reward ID to claim',
    example: 'reward_123',
  })
  @IsString()
  rewardId: string;

  @ApiPropertyOptional({
    description: 'Wallet address for reward delivery',
    example: '******************************************',
  })
  @IsOptional()
  @IsString()
  walletAddress?: string;

  @ApiPropertyOptional({
    description: 'Preferred delivery method',
    example: 'wallet_transfer',
    enum: ['wallet_transfer', 'platform_credit', 'physical_delivery'],
  })
  @IsOptional()
  @IsString()
  deliveryMethod?: string;

  @ApiPropertyOptional({
    description: 'Additional claim preferences',
    example: { emailNotification: true, smsNotification: false },
  })
  @IsOptional()
  preferences?: Record<string, any>;
}
