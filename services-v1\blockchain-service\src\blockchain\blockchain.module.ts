import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';

// Controllers
import { BlockchainQueryController } from './controllers/blockchain-query.controller';
import { BlockchainCommandController } from './controllers/blockchain-command.controller';
import { ContractController } from './controllers/contract.controller';
import { TransactionController } from './controllers/transaction.controller';
import { WalletController } from './controllers/wallet.controller';

// Services
import { BlockchainQueryService } from './services/blockchain-query.service';
import { BlockchainCommandService } from './services/blockchain-command.service';
import { ContractManagerService } from './services/contract-manager.service';
import { TransactionMonitorService } from './services/transaction-monitor.service';
import { WalletManagerService } from './services/wallet-manager.service';
import { GasEstimatorService } from './services/gas-estimator.service';
import { MintingService } from './services/minting.service';

@Module({
  imports: [
    HttpModule, // For external API calls
  ],
  controllers: [
    // Blockchain Controllers
    BlockchainQueryController,
    BlockchainCommandController,
    
    // Smart Contract Controllers
    ContractController,
    TransactionController,
    WalletController,
  ],
  providers: [
    // Core Blockchain Services
    BlockchainQueryService,
    BlockchainCommandService,
    
    // Smart Contract Services
    ContractManagerService,
    TransactionMonitorService,
    WalletManagerService,
    GasEstimatorService,
    MintingService,
  ],
  exports: [
    // Export services for use in other modules
    BlockchainQueryService,
    BlockchainCommandService,
    ContractManagerService,
    TransactionMonitorService,
    WalletManagerService,
    GasEstimatorService,
    MintingService,
  ],
})
export class BlockchainModule {}
