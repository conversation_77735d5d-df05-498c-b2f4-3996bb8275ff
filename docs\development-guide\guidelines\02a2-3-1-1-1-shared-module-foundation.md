# 🔧 Basic Shared Module Foundation

## Overview
Minimal shared module structure that provides common functionality across all features.

## Basic Shared Module Structure

### File Organization
```
shared/
├── shared.module.ts              # Main shared module
├── config/
│   ├── app.config.ts            # Application configuration
│   └── database.config.ts       # Database configuration
├── services/
│   ├── logger.service.ts        # Logging service
│   └── config.service.ts        # Configuration service
├── guards/
│   └── auth.guard.ts            # Authentication guard
├── interceptors/
│   └── logging.interceptor.ts   # Request logging
└── constants/
    └── app.constants.ts         # Application constants
```

## Basic Shared Module Implementation

### 1. Main Shared Module
```typescript
// shared/shared.module.ts
import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LoggerService } from './services/logger.service';
import { AppConfigService } from './services/config.service';
import { AuthGuard } from './guards/auth.guard';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import appConfig from './config/app.config';

@Global()
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [appConfig],
      isGlobal: true,
    }),
  ],
  providers: [
    LoggerService,
    AppConfigService,
    AuthGuard,
    LoggingInterceptor,
  ],
  exports: [
    LoggerService,
    AppConfigService,
    AuthGuard,
    LoggingInterceptor,
  ],
})
export class SharedModule {}
```

### 2. Basic Configuration
```typescript
// shared/config/app.config.ts
export default () => ({
  app: {
    name: process.env.SERVICE_NAME || 'social-nft-service',
    port: parseInt(process.env.PORT, 10) || 3000,
    environment: process.env.NODE_ENV || 'development',
  },
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'social_nft_db',
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'default-secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },
});
```

### 3. Basic Logger Service
```typescript
// shared/services/logger.service.ts
import { Injectable, LogLevel } from '@nestjs/common';

@Injectable()
export class LoggerService {
  private context: string = '';

  setContext(context: string): void {
    this.context = context;
  }

  log(message: string, data?: any): void {
    console.log(`[${this.context}] ${message}`, data || '');
  }

  error(message: string, trace?: string, data?: any): void {
    console.error(`[${this.context}] ERROR: ${message}`, {
      trace,
      data,
    });
  }

  warn(message: string, data?: any): void {
    console.warn(`[${this.context}] WARN: ${message}`, data || '');
  }

  debug(message: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[${this.context}] DEBUG: ${message}`, data || '');
    }
  }
}
```

### 4. Basic Config Service
```typescript
// shared/services/config.service.ts
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppConfigService {
  constructor(private readonly configService: ConfigService) {}

  get<T>(key: string, defaultValue?: T): T {
    return this.configService.get<T>(key, defaultValue);
  }

  getAppConfig() {
    return {
      name: this.get<string>('app.name'),
      port: this.get<number>('app.port'),
      environment: this.get<string>('app.environment'),
    };
  }

  getDatabaseConfig() {
    return {
      host: this.get<string>('database.host'),
      port: this.get<number>('database.port'),
      username: this.get<string>('database.username'),
      password: this.get<string>('database.password'),
      database: this.get<string>('database.database'),
    };
  }

  getJwtConfig() {
    return {
      secret: this.get<string>('jwt.secret'),
      expiresIn: this.get<string>('jwt.expiresIn'),
    };
  }
}
```

## AI Agent Implementation Rules

### Module Creation Steps
1. Create shared.module.ts with @Global() decorator
2. Import ConfigModule with configuration files
3. Add basic services (logger, config)
4. Export all services for use in other modules
5. Keep initial implementation minimal

### File Naming Rules
- Module: `shared.module.ts`
- Services: `service-name.service.ts`
- Config: `config-name.config.ts`
- Guards: `guard-name.guard.ts`
- Interceptors: `interceptor-name.interceptor.ts`

### Implementation Checklist
- [ ] SharedModule has @Global() decorator
- [ ] ConfigModule is imported and configured
- [ ] Basic services are provided and exported
- [ ] Configuration follows environment variable pattern
- [ ] Logger service has context support
- [ ] All files follow naming conventions

### Validation Steps
1. Verify module can be imported without errors
2. Check that services are properly exported
3. Ensure configuration loads environment variables
4. Test logger service context functionality
5. Validate that other modules can inject shared services

## Next Steps
After basic foundation is complete:
1. Add database configuration service
2. Implement authentication guard
3. Create logging interceptor
4. Add application constants
5. Extend with additional shared utilities
