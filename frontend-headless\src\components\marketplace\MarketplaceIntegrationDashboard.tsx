'use client'

import React, { useState } from 'react'
import {
  ShoppingBagIcon,
  RectangleStackIcon,
  MagnifyingGlassIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  PlusIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import {
  useCampaignListings,
  useMarketplaceDiscovery,
  useMarketplaceOverview,
  useBulkOperations
} from '@/hooks/useMarketplaceIntegration'
import { MarketplaceFilters, SortOption } from '@/types/marketplace-integration.types'
import MarketplaceListings from './MarketplaceListings'
import MarketplaceDiscovery from './MarketplaceDiscovery'
import BulkOperationsManager from './BulkOperationsManager'
import MarketplaceSearch from './MarketplaceSearch'
import IntegrationSettings from './IntegrationSettings'
import MarketplaceAnalytics from './MarketplaceAnalytics'

interface MarketplaceIntegrationDashboardProps {
  campaignId?: string
  onCreateListing?: () => void
  onViewListing?: (id: string) => void
  className?: string
}

export default function MarketplaceIntegrationDashboard({
  campaignId,
  onCreateListing,
  onViewListing,
  className = ''
}: MarketplaceIntegrationDashboardProps) {
  const [activeTab, setActiveTab] = useState<'discovery' | 'listings' | 'search' | 'bulk' | 'analytics' | 'settings'>('discovery')
  const [filters, setFilters] = useState<MarketplaceFilters>({
    sortBy: SortOption.NEWEST,
    sortOrder: 'desc',
    page: 1,
    limit: 20
  })

  const { data: listingsData, isLoading: listingsLoading, refetch: refetchListings } = useCampaignListings(filters)
  const { data: discovery, isLoading: discoveryLoading } = useMarketplaceDiscovery()
  const { data: overview, isLoading: overviewLoading } = useMarketplaceOverview()
  const { data: bulkOps, isLoading: bulkOpsLoading } = useBulkOperations()

  const listings = listingsData?.listings || []
  const totalListings = listingsData?.total || 0

  const tabs = [
    { id: 'discovery', name: 'Discovery', icon: EyeIcon, description: 'Explore marketplace' },
    { id: 'listings', name: 'My Listings', icon: RectangleStackIcon, count: totalListings },
    { id: 'search', name: 'Search', icon: MagnifyingGlassIcon, description: 'Find campaigns' },
    { id: 'bulk', name: 'Bulk Operations', icon: ArrowPathIcon, count: bulkOps?.filter(op => op.status === 'processing').length },
    { id: 'analytics', name: 'Analytics', icon: ChartBarIcon, description: 'Performance insights' },
    { id: 'settings', name: 'Integration', icon: Cog6ToothIcon, description: 'Configure settings' }
  ]

  if (discoveryLoading && !discovery) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <ShoppingBagIcon className="h-8 w-8 mr-3 text-blue-600" />
              Marketplace Integration
            </h1>
            <p className="text-gray-600 mt-1">
              Manage campaign listings and marketplace presence
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Quick Stats */}
            {overview && !overviewLoading && (
              <div className="flex items-center space-x-6 text-sm text-gray-600 mr-6">
                <div className="text-center">
                  <div className="text-lg font-semibold text-gray-900">{overview.totalListings}</div>
                  <div>Total Listings</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">${overview.totalVolume.toLocaleString()}</div>
                  <div>Total Volume</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-blue-600">{overview.activeUsers}</div>
                  <div>Active Users</div>
                </div>
              </div>
            )}

            {/* Refresh Button */}
            <button
              onClick={() => refetchListings()}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              Refresh
            </button>

            {/* Create Listing Button */}
            {onCreateListing && (
              <button
                onClick={onCreateListing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Listing
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Marketplace Overview Cards */}
      {overview && !overviewLoading && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <RectangleStackIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">{overview.totalListings}</div>
                <div className="text-sm text-gray-600">Active Listings</div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">${overview.totalVolume.toLocaleString()}</div>
                <div className="text-sm text-gray-600">Total Volume</div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ShoppingBagIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">${overview.averagePrice.toFixed(2)}</div>
                <div className="text-sm text-gray-600">Average Price</div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <EyeIcon className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">{overview.activeUsers}</div>
                <div className="text-sm text-gray-600">Active Users</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'discovery' && (
            <MarketplaceDiscovery 
              discovery={discovery}
              isLoading={discoveryLoading}
              onViewListing={onViewListing}
            />
          )}

          {activeTab === 'listings' && (
            <MarketplaceListings 
              listings={listings}
              total={totalListings}
              isLoading={listingsLoading}
              filters={filters}
              onFiltersChange={setFilters}
              onViewListing={onViewListing}
              onCreateListing={onCreateListing}
            />
          )}

          {activeTab === 'search' && (
            <MarketplaceSearch 
              onViewListing={onViewListing}
            />
          )}

          {activeTab === 'bulk' && (
            <BulkOperationsManager 
              operations={bulkOps}
              isLoading={bulkOpsLoading}
            />
          )}

          {activeTab === 'analytics' && (
            <MarketplaceAnalytics 
              campaignId={campaignId}
            />
          )}

          {activeTab === 'settings' && (
            <IntegrationSettings 
              campaignId={campaignId}
            />
          )}
        </div>
      </div>

      {/* Recent Activity */}
      {overview && overview.recentSales && overview.recentSales.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Marketplace Activity</h3>
          <div className="space-y-3">
            {overview.recentSales.slice(0, 5).map((sale) => (
              <div key={sale.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {sale.type === 'purchase' ? 'Purchase' : 'Sale'} - {sale.itemType}
                    </div>
                    <div className="text-xs text-gray-600">
                      {sale.price} {sale.currency}
                    </div>
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(sale.createdAt).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button 
            onClick={onCreateListing}
            className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <div className="text-center">
              <PlusIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Create New Listing</span>
            </div>
          </button>
          
          <button 
            onClick={() => setActiveTab('bulk')}
            className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <div className="text-center">
              <ArrowPathIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Bulk Operations</span>
            </div>
          </button>
          
          <button 
            onClick={() => setActiveTab('analytics')}
            className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <div className="text-center">
              <ChartBarIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">View Analytics</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
