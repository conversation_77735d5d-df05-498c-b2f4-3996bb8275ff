/**
 * Standardized Prisma Service
 *
 * IMPORTANT: This is SHARED INFRASTRUCTURE CODE, NOT a shared database!
 *
 * Each microservice uses this infrastructure to connect to its OWN separate database.
 * This follows the "Database Per Service" pattern - industry best practice.
 *
 * What this provides:
 * - Standardized database connection patterns
 * - Query performance monitoring
 * - Health checks and error handling
 * - Configuration management
 *
 * What this does NOT provide:
 * - Shared database instances
 * - Cross-service data access
 * - Shared tables or schemas
 */

import { Injectable, OnModuleInit, OnModuleDestroy, Logger } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { StandardizedConfigService } from '../../config/services/standardized-config.service';
import { ServiceLoggerService } from '../../logging/services/service-logger.service';

/**
 * Query Performance Metrics
 */
export interface QueryMetrics {
  query: string;
  duration: number;
  success: boolean;
  rowsAffected?: number;
  error?: string;
}

/**
 * Connection Status
 */
export interface ConnectionStatus {
  connected: boolean;
  connectionCount: number;
  lastConnected?: Date;
  lastError?: string;
  databaseUrl: string;
  maxConnections: number;
}

/**
 * Standardized Prisma Service
 * Provides enterprise-grade database connection management
 */
@Injectable()
export class StandardizedPrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(StandardizedPrismaService.name);
  private readonly serviceName: string;
  private connectionStatus: ConnectionStatus;
  private queryMetrics: QueryMetrics[] = [];
  private readonly maxMetricsHistory = 1000;

  constructor(
    private readonly configService: StandardizedConfigService,
    private readonly serviceLogger?: ServiceLoggerService
  ) {
    const dbConfig = configService.getDatabaseConfig();
    const serviceConfig = configService.getServiceConfig();
    
    super({
      datasources: {
        db: {
          url: dbConfig.url,
        },
      },
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
      errorFormat: 'pretty',
    });

    this.serviceName = serviceConfig.serviceName;
    this.connectionStatus = {
      connected: false,
      connectionCount: 0,
      databaseUrl: this.sanitizeUrl(dbConfig.url),
      maxConnections: dbConfig.maxConnections,
    };

    this.setupEventListeners();
  }

  /**
   * Initialize database connection
   */
  async onModuleInit() {
    try {
      await this.$connect();
      this.connectionStatus.connected = true;
      this.connectionStatus.lastConnected = new Date();
      this.connectionStatus.connectionCount++;

      this.logger.log(`Database connected successfully for ${this.serviceName}`);
      
      if (this.serviceLogger) {
        this.serviceLogger.logBusinessEvent(
          'database',
          'connect',
          'success' as any,
          {
            metadata: {
              service: this.serviceName,
              connectionCount: this.connectionStatus.connectionCount,
            },
          }
        );
      }

      // Test connection with a simple query
      await this.testConnection();
      
    } catch (error) {
      this.connectionStatus.connected = false;
      this.connectionStatus.lastError = error.message;
      
      this.logger.error(`Failed to connect to database for ${this.serviceName}:`, error);
      
      if (this.serviceLogger) {
        this.serviceLogger.logBusinessEvent(
          'database',
          'connect',
          'failure' as any,
          {
            error: {
              name: error.name,
              message: error.message,
              stack: error.stack,
            },
          }
        );
      }
      
      throw error;
    }
  }

  /**
   * Cleanup database connection
   */
  async onModuleDestroy() {
    try {
      await this.$disconnect();
      this.connectionStatus.connected = false;
      
      this.logger.log(`Database disconnected for ${this.serviceName}`);
      
      if (this.serviceLogger) {
        this.serviceLogger.logBusinessEvent(
          'database',
          'disconnect',
          'success' as any,
          {
            metadata: {
              service: this.serviceName,
              totalQueries: this.queryMetrics.length,
            },
          }
        );
      }
      
    } catch (error) {
      this.logger.error(`Error disconnecting from database for ${this.serviceName}:`, error);
      
      if (this.serviceLogger) {
        this.serviceLogger.error(`Database disconnect error: ${error.message}`, error);
      }
    }
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error(`Database connection test failed for ${this.serviceName}:`, error);
      return false;
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  /**
   * Get query metrics
   */
  getQueryMetrics(): QueryMetrics[] {
    return [...this.queryMetrics];
  }

  /**
   * Get query performance summary
   */
  getQueryPerformanceSummary(): {
    totalQueries: number;
    averageDuration: number;
    slowQueries: number;
    errorRate: number;
    successRate: number;
  } {
    const totalQueries = this.queryMetrics.length;
    
    if (totalQueries === 0) {
      return {
        totalQueries: 0,
        averageDuration: 0,
        slowQueries: 0,
        errorRate: 0,
        successRate: 0,
      };
    }

    const totalDuration = this.queryMetrics.reduce((sum, metric) => sum + metric.duration, 0);
    const averageDuration = totalDuration / totalQueries;
    const slowQueries = this.queryMetrics.filter(metric => metric.duration > 1000).length;
    const successfulQueries = this.queryMetrics.filter(metric => metric.success).length;
    const errorRate = ((totalQueries - successfulQueries) / totalQueries) * 100;
    const successRate = (successfulQueries / totalQueries) * 100;

    return {
      totalQueries,
      averageDuration,
      slowQueries,
      errorRate,
      successRate,
    };
  }

  /**
   * Execute query with performance tracking
   */
  async executeWithMetrics<T>(
    operation: string,
    queryFn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    let success = false;
    let error: Error | undefined;
    let result: T;

    try {
      result = await queryFn();
      success = true;
      return result;
    } catch (err) {
      error = err as Error;
      throw err;
    } finally {
      const duration = Date.now() - startTime;
      
      // Record metrics
      const metrics: QueryMetrics = {
        query: operation,
        duration,
        success,
        error: error?.message,
      };

      this.addQueryMetrics(metrics);

      // Log performance
      if (this.serviceLogger) {
        this.serviceLogger.logDatabaseOperation(
          operation,
          'unknown', // Table name would need to be extracted from query
          duration,
          success,
          {
            correlationId: this.generateCorrelationId(),
            metadata: {
              rowsAffected: Array.isArray(result) ? result.length : undefined,
            },
          }
        );
      }

      // Log slow queries
      if (duration > 1000) {
        this.logger.warn(`Slow query detected: ${operation} took ${duration}ms`);
      }
    }
  }

  /**
   * Health check for database
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime: number;
      queryMetrics: any;
    };
  }> {
    const startTime = Date.now();
    
    try {
      const isConnected = await this.testConnection();
      const responseTime = Date.now() - startTime;
      const queryMetrics = this.getQueryPerformanceSummary();

      return {
        status: isConnected ? 'healthy' : 'unhealthy',
        details: {
          connected: isConnected,
          responseTime,
          queryMetrics,
        },
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          responseTime,
          queryMetrics: this.getQueryPerformanceSummary(),
        },
      };
    }
  }

  /**
   * Setup event listeners for Prisma
   */
  private setupEventListeners(): void {
    // Query event listener
    this.$on('query', (event) => {
      const metrics: QueryMetrics = {
        query: event.query,
        duration: event.duration,
        success: true,
      };
      
      this.addQueryMetrics(metrics);
    });

    // Error event listener
    this.$on('error', (event) => {
      this.logger.error(`Database error in ${this.serviceName}:`, event);
      
      if (this.serviceLogger) {
        this.serviceLogger.error(`Database error: ${event.message}`, undefined, {
          metadata: {
            target: event.target,
            timestamp: event.timestamp,
          },
        });
      }
    });

    // Info event listener
    this.$on('info', (event) => {
      this.logger.log(`Database info in ${this.serviceName}: ${event.message}`);
    });

    // Warning event listener
    this.$on('warn', (event) => {
      this.logger.warn(`Database warning in ${this.serviceName}: ${event.message}`);
    });
  }

  /**
   * Add query metrics with history management
   */
  private addQueryMetrics(metrics: QueryMetrics): void {
    this.queryMetrics.push(metrics);
    
    // Keep only the last N metrics to prevent memory leaks
    if (this.queryMetrics.length > this.maxMetricsHistory) {
      this.queryMetrics = this.queryMetrics.slice(-this.maxMetricsHistory);
    }
  }

  /**
   * Sanitize database URL for logging
   */
  private sanitizeUrl(url: string): string {
    try {
      const parsed = new URL(url);
      return `${parsed.protocol}//${parsed.username ? '***:***@' : ''}${parsed.host}${parsed.pathname}`;
    } catch {
      return 'invalid-url';
    }
  }

  /**
   * Generate correlation ID for database operations
   */
  private generateCorrelationId(): string {
    return `db_${this.serviceName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
