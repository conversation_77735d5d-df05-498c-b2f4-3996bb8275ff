#!/bin/bash

# API Gateway Integration Test Script
# Tests that all services are accessible through API Gateway and no direct communication occurs

set -e

API_GATEWAY_URL="http://localhost:3010"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 API Gateway Integration Test Suite${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run test
run_test() {
    local test_name="$1"
    local endpoint="$2"
    local expected_status="${3:-200}"
    local method="${4:-GET}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing: $test_name ... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/test_response.json "$API_GATEWAY_URL$endpoint" 2>/dev/null || echo "000")
    else
        response=$(curl -s -w "%{http_code}" -o /tmp/test_response.json -X "$method" "$API_GATEWAY_URL$endpoint" 2>/dev/null || echo "000")
    fi
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAIL (Expected: $expected_status, Got: $response)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        if [ -f /tmp/test_response.json ]; then
            echo -e "${YELLOW}Response:${NC} $(cat /tmp/test_response.json | head -c 200)..."
        fi
    fi
}

# Function to test service health through API Gateway
test_service_health() {
    local service_name="$1"
    echo -e "\n${YELLOW}🔍 Testing $service_name through API Gateway${NC}"
    
    case $service_name in
        "user-service")
            run_test "User Service Health" "/api/users/health"
            run_test "User Service Enterprise Health" "/api/users/health/enterprise"
            ;;
        "profile-analysis-service")
            run_test "Profile Analysis Health" "/api/analysis/health"
            ;;
        "nft-generation-service")
            run_test "NFT Generation Health" "/api/nft-generation/health"
            ;;
        "project-service")
            run_test "Project Service - Get Projects" "/api/projects"
            run_test "Project Service - Get Campaigns" "/api/campaigns"
            ;;
        "marketplace-service")
            # Note: Marketplace endpoints through API Gateway are not fully implemented yet
            echo -e "${YELLOW}⚠️  Marketplace service endpoints not fully implemented through API Gateway${NC}"
            echo -e "${YELLOW}    Direct service health check available at http://localhost:3006/api/health${NC}"
            ;;
        "blockchain-service")
            run_test "Blockchain Service Health" "/api/blockchain/health"
            run_test "Blockchain Status" "/api/blockchain/blockchain-status"
            ;;
        "analytics-service")
            run_test "Analytics Service Health" "/api/analytics/health"
            run_test "Analytics Platform Overview" "/api/analytics/platform-overview"
            ;;
        "notification-service")
            # Note: Notification service might not have exposed endpoints yet
            echo -e "${YELLOW}⚠️  Notification service endpoints not yet exposed through API Gateway${NC}"
            ;;
    esac
}

echo -e "${BLUE}📊 Step 1: Test API Gateway Core Endpoints${NC}"
run_test "API Gateway Root" "/api"
run_test "API Gateway Health" "/api/health"
run_test "API Gateway Services Status" "/api/services"
run_test "Environment Info" "/api/environment/info"

echo -e "\n${BLUE}🔄 Step 2: Test Mock/Real Service Routing${NC}"
run_test "Environment Services List" "/api/environment/services"

echo -e "\n${BLUE}🏥 Step 3: Test All Services Through API Gateway${NC}"

# Test each service
test_service_health "user-service"
test_service_health "profile-analysis-service"
test_service_health "nft-generation-service"
test_service_health "project-service"
test_service_health "marketplace-service"
test_service_health "blockchain-service"
test_service_health "analytics-service"
test_service_health "notification-service"

echo -e "\n${BLUE}🔍 Step 4: Test Direct Service Access (Should be blocked/discouraged)${NC}"
echo -e "${YELLOW}Testing direct access to services (bypassing API Gateway)${NC}"

# Test direct access to services (these should work but we want to discourage this)
direct_test() {
    local service_name="$1"
    local port="$2"
    local endpoint="$3"
    
    echo -n "Direct access to $service_name:$port$endpoint ... "
    response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:$port$endpoint" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        echo -e "${YELLOW}⚠️  ACCESSIBLE (Should use API Gateway instead)${NC}"
    else
        echo -e "${GREEN}✅ BLOCKED/UNAVAILABLE${NC}"
    fi
}

direct_test "user-service" "3011" "/api/health"
direct_test "nft-generation-service" "3003" "/api/health"
direct_test "project-service" "3005" "/api/health"
direct_test "marketplace-service" "3006" "/api/health"
direct_test "blockchain-service" "3004" "/api/health"
direct_test "analytics-service" "3009" "/api/health"

echo -e "\n${BLUE}📈 Test Results Summary${NC}"
echo -e "${BLUE}======================${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! API Gateway integration is working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please check the API Gateway configuration.${NC}"
    exit 1
fi
