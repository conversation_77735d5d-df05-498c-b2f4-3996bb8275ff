import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { marketplaceService } from '@/services/marketplaceService'
import {
  MarketplaceListing,
  MarketplaceOffer,
  MarketplaceTransaction,
  CreateListingRequest,
  UpdateListingRequest,
  CreateOfferRequest,
  PurchaseRequest,
  MarketplaceFilters,
  ListingStatus,
  OfferStatus,
  TransactionStatus
} from '@/types/marketplace.types'
import { toast } from 'react-hot-toast'

// ===== LISTING HOOKS =====

export function useMarketplaceListings(filters?: MarketplaceFilters) {
  return useQuery({
    queryKey: ['marketplace', 'listings', filters],
    queryFn: () => marketplaceService.getListings(filters),
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  })
}

export function useMarketplaceListing(id: string) {
  return useQuery({
    queryKey: ['marketplace', 'listing', id],
    queryFn: () => marketplaceService.getListing(id),
    enabled: !!id,
    staleTime: 60000, // 1 minute
  })
}

export function useCreateListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateListingRequest) => marketplaceService.createListing(data),
    onSuccess: (newListing) => {
      // Invalidate and refetch listings
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'listings'] })
      queryClient.invalidateQueries({ queryKey: ['nft', 'collection'] })
      queryClient.invalidateQueries({ queryKey: ['user', 'marketplace'] })
      
      toast.success('NFT listed successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create listing')
    },
  })
}

export function useUpdateListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateListingRequest }) => 
      marketplaceService.updateListing(id, data),
    onSuccess: (updatedListing, { id }) => {
      // Update the specific listing in cache
      queryClient.setQueryData(['marketplace', 'listing', id], updatedListing)
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'listings'] })
      
      toast.success('Listing updated successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update listing')
    },
  })
}

export function useCancelListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => marketplaceService.cancelListing(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'listing', id] })
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'listings'] })
      queryClient.invalidateQueries({ queryKey: ['user', 'marketplace'] })
      
      toast.success('Listing cancelled successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to cancel listing')
    },
  })
}

export function useDeleteListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => marketplaceService.deleteListing(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: ['marketplace', 'listing', id] })
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'listings'] })
      queryClient.invalidateQueries({ queryKey: ['user', 'marketplace'] })
      
      toast.success('Listing deleted successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete listing')
    },
  })
}

// ===== OFFER HOOKS =====

export function useListingOffers(listingId: string) {
  return useQuery({
    queryKey: ['marketplace', 'listing', listingId, 'offers'],
    queryFn: () => marketplaceService.getOffers(listingId),
    enabled: !!listingId,
    staleTime: 30000,
  })
}

export function useCreateOffer() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateOfferRequest) => marketplaceService.createOffer(data),
    onSuccess: (newOffer) => {
      // Invalidate offers for the listing
      queryClient.invalidateQueries({ 
        queryKey: ['marketplace', 'listing', newOffer.listingId, 'offers'] 
      })
      queryClient.invalidateQueries({ queryKey: ['user', 'marketplace'] })
      
      toast.success('Offer submitted successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create offer')
    },
  })
}

export function useAcceptOffer() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (offerId: string) => marketplaceService.acceptOffer(offerId),
    onSuccess: (transaction, offerId) => {
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'listings'] })
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'offers'] })
      queryClient.invalidateQueries({ queryKey: ['user', 'marketplace'] })
      queryClient.invalidateQueries({ queryKey: ['nft', 'collection'] })
      
      toast.success('Offer accepted! Transaction initiated.')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to accept offer')
    },
  })
}

export function useRejectOffer() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (offerId: string) => marketplaceService.rejectOffer(offerId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'offers'] })
      queryClient.invalidateQueries({ queryKey: ['user', 'marketplace'] })
      
      toast.success('Offer rejected')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reject offer')
    },
  })
}

export function useCancelOffer() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (offerId: string) => marketplaceService.cancelOffer(offerId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'offers'] })
      queryClient.invalidateQueries({ queryKey: ['user', 'marketplace'] })
      
      toast.success('Offer cancelled')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to cancel offer')
    },
  })
}

// ===== TRANSACTION HOOKS =====

export function usePurchaseListing() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: PurchaseRequest) => marketplaceService.purchaseListing(data),
    onSuccess: (transaction) => {
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'listings'] })
      queryClient.invalidateQueries({ queryKey: ['marketplace', 'listing', transaction.listingId] })
      queryClient.invalidateQueries({ queryKey: ['user', 'marketplace'] })
      queryClient.invalidateQueries({ queryKey: ['nft', 'collection'] })
      
      toast.success('Purchase initiated! Transaction processing...')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to complete purchase')
    },
  })
}

export function useMarketplaceTransaction(id: string) {
  return useQuery({
    queryKey: ['marketplace', 'transaction', id],
    queryFn: () => marketplaceService.getTransaction(id),
    enabled: !!id,
    staleTime: 30000,
  })
}

export function useMarketplaceTransactions(filters?: {
  userId?: string
  type?: 'purchases' | 'sales'
  status?: TransactionStatus
  page?: number
  limit?: number
}) {
  return useQuery({
    queryKey: ['marketplace', 'transactions', filters],
    queryFn: () => marketplaceService.getTransactions(filters),
    staleTime: 60000,
  })
}

// ===== USER DATA HOOKS =====

export function useUserMarketplaceData(userId?: string) {
  return useQuery({
    queryKey: ['user', 'marketplace', userId],
    queryFn: () => marketplaceService.getUserMarketplaceData(userId),
    staleTime: 300000, // 5 minutes
  })
}

export function useUserListings(userId?: string, status?: ListingStatus) {
  return useQuery({
    queryKey: ['user', 'listings', userId, status],
    queryFn: () => marketplaceService.getUserListings(userId, status),
    staleTime: 60000,
  })
}

export function useUserOffers(userId?: string, type?: 'made' | 'received') {
  return useQuery({
    queryKey: ['user', 'offers', userId, type],
    queryFn: () => marketplaceService.getUserOffers(userId, type),
    staleTime: 60000,
  })
}

// ===== ANALYTICS HOOKS =====

export function useMarketplaceAnalytics(timeframe?: '24h' | '7d' | '30d' | '90d') {
  return useQuery({
    queryKey: ['marketplace', 'analytics', timeframe],
    queryFn: () => marketplaceService.getMarketplaceAnalytics(timeframe),
    staleTime: 300000, // 5 minutes
  })
}

export function useNFTMarketHistory(nftId: string) {
  return useQuery({
    queryKey: ['marketplace', 'nft', nftId, 'history'],
    queryFn: () => marketplaceService.getNFTMarketHistory(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

// ===== UTILITY HOOKS =====

export function useSearchListings(query: string, filters?: Partial<MarketplaceFilters>) {
  return useQuery({
    queryKey: ['marketplace', 'search', query, filters],
    queryFn: () => marketplaceService.searchListings(query, filters),
    enabled: !!query && query.length > 2,
    staleTime: 30000,
  })
}

export function useFeaturedListings(limit = 10) {
  return useQuery({
    queryKey: ['marketplace', 'featured', limit],
    queryFn: () => marketplaceService.getFeaturedListings(limit),
    staleTime: 300000,
  })
}

export function useTrendingListings(timeframe = '24h', limit = 10) {
  return useQuery({
    queryKey: ['marketplace', 'trending', timeframe, limit],
    queryFn: () => marketplaceService.getTrendingListings(timeframe, limit),
    staleTime: 300000,
  })
}

export function useRecentSales(limit = 20) {
  return useQuery({
    queryKey: ['marketplace', 'recent-sales', limit],
    queryFn: () => marketplaceService.getRecentSales(limit),
    staleTime: 60000,
  })
}

export function useNFTPriceEstimate(nftId: string) {
  return useQuery({
    queryKey: ['marketplace', 'nft', nftId, 'price-estimate'],
    queryFn: () => marketplaceService.estimateNFTPrice(nftId),
    enabled: !!nftId,
    staleTime: 600000, // 10 minutes
  })
}
