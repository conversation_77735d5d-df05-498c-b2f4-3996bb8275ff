'use client'

import React, { useState } from 'react'
import {
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  EyeIcon,
  HeartIcon,
  ShareIcon,
  CurrencyDollarIcon,
  SparklesIcon,
  GlobeAltIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { useEvolutionPredictions } from '@/hooks/useNFTEvolution'
import { EvolutionAnalytics } from '@/types/nft-evolution.types'

interface EvolutionAnalyticsProps {
  nftId: string
  analytics?: EvolutionAnalytics
  globalStats?: any
  isLoading: boolean
  className?: string
}

export default function EvolutionAnalyticsComponent({
  nftId,
  analytics,
  globalStats,
  isLoading,
  className = ''
}: EvolutionAnalyticsProps) {
  const [timeframe, setTimeframe] = useState('30d')
  const { data: predictions, isLoading: predictionsLoading } = useEvolutionPredictions(nftId)

  const timeframeOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ]

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Evolution Analytics</h2>
          <p className="text-sm text-gray-600">Performance insights and evolution metrics</p>
        </div>
        
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
        >
          {timeframeOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Key Metrics */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <SparklesIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">{analytics.totalEvolutions}</div>
                <div className="text-sm text-gray-600">Total Evolutions</div>
                <div className="flex items-center mt-1">
                  <span className="text-xs text-green-600">
                    {analytics.successfulEvolutions}/{analytics.totalEvolutions} successful
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">+{analytics.valueIncrease.toFixed(1)}%</div>
                <div className="text-sm text-gray-600">Value Increase</div>
                <div className="flex items-center mt-1">
                  {analytics.valueIncrease > 0 ? (
                    <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-xs ${analytics.valueIncrease > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    ${analytics.averageValueGain.toFixed(2)} avg gain
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <HeartIcon className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">{analytics.communityEngagement.toFixed(1)}</div>
                <div className="text-sm text-gray-600">Engagement Score</div>
                <div className="flex items-center mt-1 space-x-2">
                  <span className="text-xs text-gray-600">{analytics.socialShares} shares</span>
                  <span className="text-xs text-gray-600">{analytics.positiveReactions} likes</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">
                  {(analytics.averageEvolutionTime / 3600).toFixed(1)}h
                </div>
                <div className="text-sm text-gray-600">Avg Evolution Time</div>
                <div className="flex items-center mt-1">
                  <span className="text-xs text-blue-600">
                    {(analytics.evolutionSuccessRate * 100).toFixed(1)}% success rate
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Evolution Trends */}
      {analytics && analytics.evolutionTrends && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Evolution Trends</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {analytics.evolutionTrends.slice(0, 3).map((trend, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-900">{trend.period}</span>
                  <div className="flex items-center">
                    {trend.trendDirection === 'up' ? (
                      <TrendingUpIcon className="h-4 w-4 text-green-500" />
                    ) : trend.trendDirection === 'down' ? (
                      <TrendingDownIcon className="h-4 w-4 text-red-500" />
                    ) : (
                      <div className="h-4 w-4 bg-gray-400 rounded-full"></div>
                    )}
                  </div>
                </div>
                
                <div className="space-y-1 text-sm text-gray-600">
                  <div>Evolutions: {trend.evolutionCount}</div>
                  <div>Avg Value: ${trend.averageValue.toFixed(2)}</div>
                  <div>Popular Traits: {trend.popularTraits.slice(0, 2).join(', ')}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Popular Triggers */}
      {analytics && analytics.popularTriggers && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Most Popular Triggers</h3>
          
          <div className="space-y-3">
            {analytics.popularTriggers.slice(0, 5).map((trigger, index) => (
              <div key={trigger.triggerId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">
                    #{index + 1}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{trigger.triggerName}</div>
                    <div className="text-xs text-gray-600">{trigger.usageCount} uses</div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {(trigger.successRate * 100).toFixed(1)}% success
                  </div>
                  <div className="text-xs text-green-600">
                    +{trigger.averageValueIncrease.toFixed(1)}% value
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Evolution Predictions */}
      {predictions && !predictionsLoading && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Evolution Predictions</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Next Evolution Prediction */}
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-purple-900 mb-3">Next Predicted Evolution</h4>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-purple-700">Trigger:</span>
                  <span className="text-sm font-medium text-purple-900">
                    {predictions.nextEvolution.triggerId}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-purple-700">Probability:</span>
                  <span className="text-sm font-medium text-purple-900">
                    {(predictions.nextEvolution.probability * 100).toFixed(1)}%
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-purple-700">Est. Time:</span>
                  <span className="text-sm font-medium text-purple-900">
                    {predictions.nextEvolution.estimatedTime}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-purple-700">Value Impact:</span>
                  <span className="text-sm font-medium text-green-600">
                    +{predictions.nextEvolution.expectedValueIncrease.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>

            {/* Recommendations */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-3">Recommendations</h4>
              
              <div className="space-y-2">
                {predictions.recommendations.slice(0, 3).map((rec, index) => (
                  <div key={index} className="bg-white rounded p-2">
                    <div className="text-sm font-medium text-gray-900">{rec.action}</div>
                    <div className="text-xs text-gray-600">{rec.reason}</div>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-blue-600">
                        Impact: +{rec.impact.toFixed(1)}%
                      </span>
                      <span className="text-xs text-gray-500">
                        {(rec.confidence * 100).toFixed(0)}% confidence
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Performance Chart Placeholder */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Over Time</h3>
        
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div className="text-center">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">Performance chart visualization</p>
            <p className="text-xs text-gray-500">Time series chart would be implemented here</p>
          </div>
        </div>
        
        {/* Chart Legend */}
        <div className="mt-4 flex items-center justify-center space-x-6 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-purple-600 rounded"></div>
            <span className="text-gray-600">Evolution Count</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-600 rounded"></div>
            <span className="text-gray-600">Value Increase</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-600 rounded"></div>
            <span className="text-gray-600">Community Engagement</span>
          </div>
        </div>
      </div>

      {/* Global Comparison */}
      {globalStats && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Comparison</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {globalStats.totalEvolutions?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-gray-600">Platform Total</div>
              <div className="text-xs text-gray-500 mt-1">
                Your NFT: {analytics?.totalEvolutions || 0} evolutions
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {((globalStats.averageEvolutionTime || 0) / 3600).toFixed(1)}h
              </div>
              <div className="text-sm text-gray-600">Platform Average</div>
              <div className="text-xs text-gray-500 mt-1">
                Your NFT: {analytics ? (analytics.averageEvolutionTime / 3600).toFixed(1) : '0'}h
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {globalStats.activeEvolutions?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-gray-600">Currently Active</div>
              <div className="text-xs text-gray-500 mt-1">
                Platform-wide active evolutions
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Export Options */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900">Export Analytics</h3>
            <p className="text-xs text-gray-600">Download your evolution data and insights</p>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Export CSV
            </button>
            <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Export JSON
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
