{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@shared/*": ["../../shared/*"], "@shared": ["../../shared"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "strict": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "src/cache/**/*", "src/circuit-breaker/**/*", "src/load-balancer/**/*", "src/rate-limit/**/*", "src/service-discovery/**/*", "src/proxy-v2/**/*", "src/dashboard/**/*", "test-enterprise-features.ts"]}