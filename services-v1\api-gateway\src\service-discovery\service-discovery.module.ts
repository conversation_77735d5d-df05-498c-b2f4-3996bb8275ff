import { Module } from '@nestjs/common';
import { ServiceDiscoveryService } from './service-discovery.service';
import { ServiceDiscoveryController } from './service-discovery.controller';

/**
 * Service Discovery Module
 * 
 * Provides dynamic service registration and health-based routing.
 * Manages service instances with automatic health monitoring.
 */
@Module({
  providers: [ServiceDiscoveryService],
  controllers: [ServiceDiscoveryController],
  exports: [ServiceDiscoveryService],
})
export class ServiceDiscoveryModule {}
