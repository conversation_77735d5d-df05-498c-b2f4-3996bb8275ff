'use client'

import React, { useState } from 'react'
import {
  SparklesIcon,
  ClockIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  DocumentTextIcon,
  EyeIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import {
  useNFTEvolutions,
  useEvolutionAnalytics,
  useGlobalEvolutionStats,
  useAvailableTriggersForNFT
} from '@/hooks/useNFTEvolution'
import { EvolutionStatus, EvolutionTriggerType } from '@/types/nft-evolution.types'
import EvolutionTimeline from './EvolutionTimeline'
import EvolutionTriggers from './EvolutionTriggers'
import EvolutionAnalytics from './EvolutionAnalytics'
import EvolutionHistory from './EvolutionHistory'
import EvolutionSimulator from './EvolutionSimulator'

interface NFTEvolutionDashboardProps {
  nftId: string
  onEvolutionCreated?: (evolutionId: string) => void
  onEvolutionUpdated?: (evolutionId: string) => void
  className?: string
}

export default function NFTEvolutionDashboard({
  nftId,
  onEvolutionCreated,
  onEvolutionUpdated,
  className = ''
}: NFTEvolutionDashboardProps) {
  const [activeTab, setActiveTab] = useState<'timeline' | 'triggers' | 'analytics' | 'history' | 'simulator'>('timeline')

  const { data: evolutions, isLoading: evolutionsLoading, refetch: refetchEvolutions } = useNFTEvolutions(nftId)
  const { data: analytics, isLoading: analyticsLoading } = useEvolutionAnalytics(nftId)
  const { data: globalStats, isLoading: globalStatsLoading } = useGlobalEvolutionStats()
  const { data: availableTriggers, isLoading: triggersLoading } = useAvailableTriggersForNFT(nftId)

  const activeEvolutions = evolutions?.filter(e => 
    e.status === EvolutionStatus.IN_PROGRESS || e.status === EvolutionStatus.PENDING
  ) || []

  const completedEvolutions = evolutions?.filter(e => 
    e.status === EvolutionStatus.COMPLETED
  ) || []

  const tabs = [
    { id: 'timeline', name: 'Evolution Timeline', icon: ClockIcon, description: 'View evolution history' },
    { id: 'triggers', name: 'Available Triggers', icon: SparklesIcon, count: availableTriggers?.length },
    { id: 'analytics', name: 'Analytics', icon: ChartBarIcon, description: 'Performance insights' },
    { id: 'history', name: 'History', icon: DocumentTextIcon, count: completedEvolutions.length },
    { id: 'simulator', name: 'Simulator', icon: EyeIcon, description: 'Preview evolutions' }
  ]

  if (evolutionsLoading && !evolutions) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <SparklesIcon className="h-8 w-8 mr-3 text-purple-600" />
              NFT Evolution System
            </h1>
            <p className="text-gray-600 mt-1">
              Manage and track your NFT's evolutionary journey
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Quick Stats */}
            {analytics && !analyticsLoading && (
              <div className="flex items-center space-x-6 text-sm text-gray-600 mr-6">
                <div className="text-center">
                  <div className="text-lg font-semibold text-gray-900">{analytics.totalEvolutions}</div>
                  <div>Total Evolutions</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">+{analytics.valueIncrease.toFixed(1)}%</div>
                  <div>Value Increase</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-blue-600">{(analytics.evolutionSuccessRate * 100).toFixed(1)}%</div>
                  <div>Success Rate</div>
                </div>
              </div>
            )}

            {/* Refresh Button */}
            <button
              onClick={() => refetchEvolutions()}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Evolution Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <SparklesIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{evolutions?.length || 0}</div>
              <div className="text-sm text-gray-600">Total Evolutions</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <PlayIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{activeEvolutions.length}</div>
              <div className="text-sm text-gray-600">Active Evolutions</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {analytics ? `+${analytics.valueIncrease.toFixed(1)}%` : '0%'}
              </div>
              <div className="text-sm text-gray-600">Value Increase</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Cog6ToothIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{availableTriggers?.length || 0}</div>
              <div className="text-sm text-gray-600">Available Triggers</div>
            </div>
          </div>
        </div>
      </div>

      {/* Active Evolutions Alert */}
      {activeEvolutions.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <PlayIcon className="h-5 w-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-900">
                Active Evolutions ({activeEvolutions.length})
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <div className="space-y-1">
                  {activeEvolutions.slice(0, 3).map((evolution) => (
                    <div key={evolution.id} className="flex items-center justify-between">
                      <span>Evolution #{evolution.evolutionStage} - {evolution.status}</span>
                      <span className="font-medium">{evolution.progress}% complete</span>
                    </div>
                  ))}
                  {activeEvolutions.length > 3 && (
                    <div className="text-blue-600">
                      +{activeEvolutions.length - 3} more active evolutions
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'timeline' && (
            <EvolutionTimeline 
              nftId={nftId}
              evolutions={evolutions}
              isLoading={evolutionsLoading}
              onEvolutionCreated={onEvolutionCreated}
              onEvolutionUpdated={onEvolutionUpdated}
            />
          )}

          {activeTab === 'triggers' && (
            <EvolutionTriggers 
              nftId={nftId}
              availableTriggers={availableTriggers}
              isLoading={triggersLoading}
              onTriggerActivated={onEvolutionCreated}
            />
          )}

          {activeTab === 'analytics' && (
            <EvolutionAnalytics 
              nftId={nftId}
              analytics={analytics}
              globalStats={globalStats}
              isLoading={analyticsLoading || globalStatsLoading}
            />
          )}

          {activeTab === 'history' && (
            <EvolutionHistory 
              nftId={nftId}
              evolutions={evolutions}
              isLoading={evolutionsLoading}
            />
          )}

          {activeTab === 'simulator' && (
            <EvolutionSimulator 
              nftId={nftId}
              availableTriggers={availableTriggers}
              onSimulationComplete={onEvolutionCreated}
            />
          )}
        </div>
      </div>

      {/* Global Evolution Stats */}
      {globalStats && !globalStatsLoading && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Evolution Statistics</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{globalStats.totalEvolutions.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Total Platform Evolutions</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{globalStats.activeEvolutions.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Currently Active</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{(globalStats.averageEvolutionTime / 3600).toFixed(1)}h</div>
              <div className="text-sm text-gray-600">Average Evolution Time</div>
            </div>
          </div>

          {/* Popular Triggers */}
          {globalStats.popularTriggers && globalStats.popularTriggers.length > 0 && (
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Most Popular Triggers</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {globalStats.popularTriggers.slice(0, 3).map((trigger, index) => (
                  <div key={trigger.triggerId} className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">#{index + 1} {trigger.name}</span>
                      <span className="text-sm text-gray-600">{trigger.count} uses</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button 
            onClick={() => setActiveTab('triggers')}
            className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <div className="text-center">
              <SparklesIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Start Evolution</span>
            </div>
          </button>
          
          <button 
            onClick={() => setActiveTab('simulator')}
            className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <div className="text-center">
              <EyeIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Preview Evolution</span>
            </div>
          </button>
          
          <button 
            onClick={() => setActiveTab('analytics')}
            className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <div className="text-center">
              <ChartBarIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">View Analytics</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
