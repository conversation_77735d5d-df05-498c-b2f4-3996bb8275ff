'use client'

import React from 'react'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CalendarIcon,
  UsersIcon,
  GiftIcon,
  SparklesIcon,
  TagIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'
import { CreateCampaignRequest, CampaignType, RewardType, RequirementType } from '@/types/campaign.types'

interface ReviewStepProps {
  data: Partial<CreateCampaignRequest>
  updateData: (updates: Partial<CreateCampaignRequest>) => void
  errors: string[]
  previewData?: any
}

export default function ReviewStep({ data, updateData, errors, previewData }: ReviewStepProps) {
  const calculateDuration = () => {
    if (data.startDate && data.endDate) {
      const start = new Date(data.startDate)
      const end = new Date(data.endDate)
      const diffTime = Math.abs(end.getTime() - start.getTime())
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays
    }
    return 0
  }

  const getTotalRewardValue = () => {
    return (data.rewards || []).reduce((total, reward) => {
      return total + (reward.value * reward.quantity)
    }, 0)
  }

  const getTotalPoints = () => {
    return (data.requirements || []).reduce((total, req) => total + req.points, 0)
  }

  const getRequiredTasks = () => {
    return (data.requirements || []).filter(req => req.isRequired).length
  }

  const getOptionalTasks = () => {
    return (data.requirements || []).filter(req => !req.isRequired).length
  }

  const getCampaignTypeLabel = (type: CampaignType) => {
    const typeLabels = {
      [CampaignType.SOCIAL_ENGAGEMENT]: 'Social Engagement',
      [CampaignType.CONTENT_CREATION]: 'Content Creation',
      [CampaignType.COMMUNITY_BUILDING]: 'Community Building',
      [CampaignType.TRADING_ACTIVITY]: 'Trading Activity',
      [CampaignType.REFERRAL_PROGRAM]: 'Referral Program',
      [CampaignType.MILESTONE_ACHIEVEMENT]: 'Milestone Achievement',
      [CampaignType.SEASONAL_EVENT]: 'Seasonal Event',
      [CampaignType.BRAND_COLLABORATION]: 'Brand Collaboration',
      [CampaignType.USER_ACQUISITION]: 'User Acquisition',
      [CampaignType.ENGAGEMENT]: 'Engagement',
      [CampaignType.RETENTION]: 'Retention'
    }
    return typeLabels[type] || type
  }

  const getRequirementTypeLabel = (type: RequirementType) => {
    const typeLabels = {
      [RequirementType.TWITTER_FOLLOW]: 'Follow on Twitter',
      [RequirementType.TWITTER_RETWEET]: 'Retweet Post',
      [RequirementType.TWITTER_LIKE]: 'Like Tweet',
      [RequirementType.TWITTER_COMMENT]: 'Comment on Tweet',
      [RequirementType.DISCORD_JOIN]: 'Join Discord',
      [RequirementType.TELEGRAM_JOIN]: 'Join Telegram',
      [RequirementType.WEBSITE_VISIT]: 'Visit Website',
      [RequirementType.EMAIL_SIGNUP]: 'Email Signup',
      [RequirementType.WALLET_CONNECT]: 'Connect Wallet',
      [RequirementType.NFT_HOLD]: 'Hold NFT',
      [RequirementType.TOKEN_HOLD]: 'Hold Tokens',
      [RequirementType.CONTENT_CREATION]: 'Create Content',
      [RequirementType.REFERRAL]: 'Refer Friends',
      [RequirementType.CUSTOM_TASK]: 'Custom Task'
    }
    return typeLabels[type] || type
  }

  const getRewardTypeLabel = (type: RewardType) => {
    const typeLabels = {
      [RewardType.NFT_GENERATION]: 'NFT Generation',
      [RewardType.TOKEN_REWARD]: 'Token Reward',
      [RewardType.POINTS]: 'Points',
      [RewardType.BADGE]: 'Badge',
      [RewardType.MARKETPLACE_CREDIT]: 'Marketplace Credit',
      [RewardType.EXCLUSIVE_ACCESS]: 'Exclusive Access',
      [RewardType.PHYSICAL_ITEM]: 'Physical Item'
    }
    return typeLabels[type] || type
  }

  const validationIssues = [
    ...errors,
    ...(data.requirements?.length === 0 ? ['No requirements defined'] : []),
    ...(data.rewards?.length === 0 ? ['No rewards defined'] : []),
    ...(!data.startDate ? ['Start date not set'] : []),
    ...(!data.endDate ? ['End date not set'] : [])
  ]

  const isReadyToLaunch = validationIssues.length === 0

  return (
    <div className="space-y-6">
      {/* Campaign Overview */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Overview</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">{data.name}</h4>
            <p className="text-sm text-gray-600 mb-4">{data.description}</p>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <TagIcon className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600">Type:</span>
                <span className="font-medium">{data.type ? getCampaignTypeLabel(data.type) : 'Not set'}</span>
              </div>
              
              {data.tags && data.tags.length > 0 && (
                <div className="flex items-start space-x-2">
                  <TagIcon className="h-4 w-4 text-gray-400 mt-0.5" />
                  <span className="text-gray-600">Tags:</span>
                  <div className="flex flex-wrap gap-1">
                    {data.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">Priority:</span>
                <span className="font-medium">{data.priority || 5}/7</span>
              </div>
              
              {data.featured && (
                <div className="flex items-center space-x-2">
                  <SparklesIcon className="h-4 w-4 text-yellow-500" />
                  <span className="text-yellow-700 font-medium">Featured Campaign</span>
                </div>
              )}
            </div>
          </div>

          <div className="space-y-4">
            {/* Timeline */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h5 className="font-medium text-blue-900 mb-2 flex items-center">
                <CalendarIcon className="h-4 w-4 mr-2" />
                Timeline
              </h5>
              <div className="space-y-1 text-sm text-blue-700">
                <div>Start: {data.startDate ? new Date(data.startDate).toLocaleDateString() : 'Not set'}</div>
                <div>End: {data.endDate ? new Date(data.endDate).toLocaleDateString() : 'Not set'}</div>
                <div>Duration: {calculateDuration()} days</div>
              </div>
            </div>

            {/* Limits */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h5 className="font-medium text-green-900 mb-2 flex items-center">
                <UsersIcon className="h-4 w-4 mr-2" />
                Participation
              </h5>
              <div className="space-y-1 text-sm text-green-700">
                <div>Max Participants: {data.maxParticipants ? data.maxParticipants.toLocaleString() : 'Unlimited'}</div>
                <div>Min Age: {data.minAge ? `${data.minAge}+ years` : 'No restriction'}</div>
                <div>
                  Regions: {
                    !data.geoRestrictions || data.geoRestrictions.length === 0 
                      ? 'Global' 
                      : data.geoRestrictions.join(', ')
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Requirements Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Requirements Summary</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{data.requirements?.length || 0}</div>
            <div className="text-sm text-gray-600">Total Tasks</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{getRequiredTasks()}</div>
            <div className="text-sm text-gray-600">Required</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{getOptionalTasks()}</div>
            <div className="text-sm text-gray-600">Optional</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{getTotalPoints()}</div>
            <div className="text-sm text-gray-600">Total Points</div>
          </div>
        </div>

        {data.requirements && data.requirements.length > 0 && (
          <div className="space-y-2">
            {data.requirements.map((req, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className={`w-2 h-2 rounded-full ${req.isRequired ? 'bg-red-500' : 'bg-yellow-500'}`}></span>
                  <span className="font-medium">{req.title}</span>
                  <span className="text-sm text-gray-600">({getRequirementTypeLabel(req.type)})</span>
                </div>
                <span className="text-sm font-medium text-purple-600">{req.points} pts</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Rewards Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Rewards Summary</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{data.rewards?.length || 0}</div>
            <div className="text-sm text-gray-600">Reward Types</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {data.rewards?.reduce((sum, r) => sum + r.quantity, 0) || 0}
            </div>
            <div className="text-sm text-gray-600">Total Recipients</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{getTotalRewardValue().toFixed(2)}</div>
            <div className="text-sm text-gray-600">Total Value</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {data.rewards?.filter(r => r.type === RewardType.NFT_GENERATION).length || 0}
            </div>
            <div className="text-sm text-gray-600">NFT Rewards</div>
          </div>
        </div>

        {data.rewards && data.rewards.length > 0 && (
          <div className="space-y-2">
            {data.rewards.map((reward, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <GiftIcon className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">{reward.title}</span>
                  <span className="text-sm text-gray-600">({getRewardTypeLabel(reward.type)})</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{reward.quantity} recipients</div>
                  <div className="text-xs text-gray-600">{reward.value} each</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* NFT Settings Summary */}
      {data.nftSettings?.enabled && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <SparklesIcon className="h-5 w-5 mr-2" />
            NFT Generation Settings
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <span className="text-sm text-gray-600">Style:</span>
              <div className="font-medium">{data.nftSettings.style}</div>
            </div>
            <div>
              <span className="text-sm text-gray-600">Theme:</span>
              <div className="font-medium">{data.nftSettings.theme}</div>
            </div>
            <div>
              <span className="text-sm text-gray-600">Blockchain:</span>
              <div className="font-medium">{data.nftSettings.blockchain}</div>
            </div>
            <div>
              <span className="text-sm text-gray-600">Auto-mint:</span>
              <div className="font-medium">{data.nftSettings.autoMint ? 'Yes' : 'No'}</div>
            </div>
          </div>

          {data.nftSettings.customization?.customPrompts && data.nftSettings.customization.customPrompts.length > 0 && (
            <div className="mt-4">
              <span className="text-sm text-gray-600">Custom Prompts:</span>
              <div className="mt-1 flex flex-wrap gap-1">
                {data.nftSettings.customization.customPrompts.map((prompt, index) => (
                  <span key={index} className="inline-block px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">
                    {prompt}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Preview Data */}
      {previewData && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-4">Campaign Predictions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <span className="text-sm text-blue-700">Est. Participants:</span>
              <div className="text-xl font-bold text-blue-900">{previewData.estimatedParticipants}</div>
            </div>
            <div>
              <span className="text-sm text-blue-700">Est. Duration:</span>
              <div className="text-xl font-bold text-blue-900">{previewData.estimatedDuration} days</div>
            </div>
            <div>
              <span className="text-sm text-blue-700">Est. Cost:</span>
              <div className="text-xl font-bold text-blue-900">${previewData.estimatedCost}</div>
            </div>
            <div>
              <span className="text-sm text-blue-700">Success Score:</span>
              <div className="text-xl font-bold text-blue-900">
                {Math.round((previewData.estimatedParticipants / (data.maxParticipants || 1000)) * 100)}%
              </div>
            </div>
          </div>
          
          {previewData.recommendations && previewData.recommendations.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-blue-900 mb-2">Recommendations:</h4>
              <ul className="list-disc list-inside text-sm text-blue-700 space-y-1">
                {previewData.recommendations.map((rec: string, index: number) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Validation Status */}
      <div className={`border rounded-lg p-6 ${
        isReadyToLaunch 
          ? 'bg-green-50 border-green-200' 
          : 'bg-red-50 border-red-200'
      }`}>
        <div className="flex items-center space-x-3 mb-4">
          {isReadyToLaunch ? (
            <CheckCircleIcon className="h-6 w-6 text-green-600" />
          ) : (
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
          )}
          <h3 className={`text-lg font-medium ${
            isReadyToLaunch ? 'text-green-900' : 'text-red-900'
          }`}>
            {isReadyToLaunch ? 'Ready to Launch!' : 'Issues Found'}
          </h3>
        </div>

        {isReadyToLaunch ? (
          <p className="text-green-700">
            Your campaign is properly configured and ready to launch. All requirements and rewards are set up correctly.
          </p>
        ) : (
          <div>
            <p className="text-red-700 mb-3">
              Please address the following issues before launching your campaign:
            </p>
            <ul className="list-disc list-inside text-red-700 space-y-1">
              {validationIssues.map((issue, index) => (
                <li key={index}>{issue}</li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Final Notes */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-yellow-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Before You Launch</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Double-check all social media links and requirements</li>
                <li>Ensure you have sufficient budget for rewards</li>
                <li>Test NFT generation settings if enabled</li>
                <li>Prepare promotional materials for campaign launch</li>
                <li>Set up monitoring for campaign performance</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
