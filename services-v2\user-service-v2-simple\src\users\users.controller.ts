/**
 * Users Controller - User Service V2 Simple
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new user (Registration)' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  async create(@Body() createUserDto: CreateUserDto) {
    const user = await this.usersService.create(createUserDto);
    
    return {
      success: true,
      data: user,
      message: 'User created successfully',
      timestamp: new Date().toISOString(),
      service: 'user-service-v2-simple',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  async findAll(@Query('page') page?: number, @Query('limit') limit?: number) {
    const users = await this.usersService.findAll(page, limit);
    
    return {
      success: true,
      data: users,
      message: 'Users retrieved successfully',
      timestamp: new Date().toISOString(),
      service: 'user-service-v2-simple',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User found' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id') id: string) {
    const user = await this.usersService.findOne(id);
    
    if (!user) {
      return {
        success: false,
        error: 'User not found',
        timestamp: new Date().toISOString(),
        service: 'user-service-v2-simple',
      };
    }
    
    return {
      success: true,
      data: user,
      message: 'User retrieved successfully',
      timestamp: new Date().toISOString(),
      service: 'user-service-v2-simple',
    };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update user by ID' })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    const user = await this.usersService.update(id, updateUserDto);
    
    if (!user) {
      return {
        success: false,
        error: 'User not found',
        timestamp: new Date().toISOString(),
        service: 'user-service-v2-simple',
      };
    }
    
    return {
      success: true,
      data: user,
      message: 'User updated successfully',
      timestamp: new Date().toISOString(),
      service: 'user-service-v2-simple',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete user by ID' })
  @ApiResponse({ status: 200, description: 'User deleted successfully' })
  async remove(@Param('id') id: string) {
    const deleted = await this.usersService.remove(id);
    
    if (!deleted) {
      return {
        success: false,
        error: 'User not found',
        timestamp: new Date().toISOString(),
        service: 'user-service-v2-simple',
      };
    }
    
    return {
      success: true,
      message: 'User deleted successfully',
      timestamp: new Date().toISOString(),
      service: 'user-service-v2-simple',
    };
  }
}
