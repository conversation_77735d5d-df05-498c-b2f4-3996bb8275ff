import { Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { AppConfig } from '../../config/app.config';

export interface EmailNotification {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  attachments?: any[];
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private readonly config: AppConfig) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    try {
      const emailConfig = this.config.email;
      
      if (!emailConfig.host || !emailConfig.user) {
        this.logger.warn('Email configuration not complete, using mock transporter');
        this.transporter = nodemailer.createTransport({
          streamTransport: true,
          newline: 'unix',
          buffer: true,
        });
        return;
      }

      this.transporter = nodemailer.createTransport({
        host: emailConfig.host,
        port: emailConfig.port,
        secure: emailConfig.port === 465,
        auth: {
          user: emailConfig.user,
          pass: emailConfig.pass,
        },
      });

      this.logger.log('Email transporter initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize email transporter', error);
      // Create mock transporter for development
      this.transporter = nodemailer.createTransport({
        streamTransport: true,
        newline: 'unix',
        buffer: true,
      });
    }
  }

  async sendEmail(notification: EmailNotification): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      this.logger.log(`Sending email to: ${Array.isArray(notification.to) ? notification.to.join(', ') : notification.to}`);

      const mailOptions = {
        from: this.config.email.from,
        to: notification.to,
        subject: notification.subject,
        html: notification.html,
        text: notification.text,
        attachments: notification.attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);

      this.logger.log(`Email sent successfully: ${result.messageId}`);
      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async sendBulkEmails(notifications: EmailNotification[]): Promise<{
    success: boolean;
    sent: number;
    failed: number;
    results: Array<{ success: boolean; messageId?: string; error?: string }>;
  }> {
    this.logger.log(`Sending bulk emails: ${notifications.length} emails`);

    const results = await Promise.allSettled(
      notifications.map(notification => this.sendEmail(notification))
    );

    const processedResults = results.map(result => 
      result.status === 'fulfilled' ? result.value : { success: false, error: 'Promise rejected' }
    );

    const sent = processedResults.filter(r => r.success).length;
    const failed = processedResults.filter(r => !r.success).length;

    this.logger.log(`Bulk email results: ${sent} sent, ${failed} failed`);

    return {
      success: failed === 0,
      sent,
      failed,
      results: processedResults,
    };
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      this.logger.log('Email connection verified successfully');
      return true;
    } catch (error) {
      this.logger.error('Email connection verification failed', error);
      return false;
    }
  }

  async sendWelcomeEmail(userEmail: string, userName: string): Promise<{ success: boolean; error?: string }> {
    const welcomeEmail: EmailNotification = {
      to: userEmail,
      subject: 'Welcome to Social NFT Platform!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Welcome to Social NFT Platform!</h1>
          <p>Hi ${userName},</p>
          <p>Welcome to our platform! We're excited to have you join our community.</p>
          <p>You can now:</p>
          <ul>
            <li>Analyze your social media profiles</li>
            <li>Generate unique NFTs based on your social presence</li>
            <li>Trade NFTs in our marketplace</li>
            <li>Connect with other creators</li>
          </ul>
          <p>Get started by visiting your dashboard and exploring the platform.</p>
          <p>Best regards,<br>The Social NFT Platform Team</p>
        </div>
      `,
      text: `Welcome to Social NFT Platform! Hi ${userName}, welcome to our platform! We're excited to have you join our community.`,
    };

    return await this.sendEmail(welcomeEmail);
  }

  async sendNFTGeneratedEmail(userEmail: string, userName: string, nftId: string): Promise<{ success: boolean; error?: string }> {
    const nftEmail: EmailNotification = {
      to: userEmail,
      subject: 'Your NFT has been generated!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Your NFT is Ready!</h1>
          <p>Hi ${userName},</p>
          <p>Great news! Your NFT has been successfully generated based on your social media analysis.</p>
          <p><strong>NFT ID:</strong> ${nftId}</p>
          <p>You can now view, mint, or trade your NFT in the marketplace.</p>
          <p>Visit your dashboard to see your new NFT and explore your options.</p>
          <p>Best regards,<br>The Social NFT Platform Team</p>
        </div>
      `,
      text: `Your NFT is Ready! Hi ${userName}, your NFT (ID: ${nftId}) has been successfully generated.`,
    };

    return await this.sendEmail(nftEmail);
  }

  async sendPasswordResetEmail(userEmail: string, resetToken: string): Promise<{ success: boolean; error?: string }> {
    const resetEmail: EmailNotification = {
      to: userEmail,
      subject: 'Password Reset Request',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Password Reset Request</h1>
          <p>You requested a password reset for your Social NFT Platform account.</p>
          <p>Click the link below to reset your password:</p>
          <p><a href="http://localhost:3000/reset-password?token=${resetToken}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
          <p>This link will expire in 1 hour.</p>
          <p>If you didn't request this reset, please ignore this email.</p>
          <p>Best regards,<br>The Social NFT Platform Team</p>
        </div>
      `,
      text: `Password reset requested. Use this link: http://localhost:3000/reset-password?token=${resetToken}`,
    };

    return await this.sendEmail(resetEmail);
  }
}
