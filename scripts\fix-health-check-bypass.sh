#!/bin/bash

# Fix Health Check Bypass in All Services
# This script ensures health endpoints are properly excluded from gateway authentication

set -e

echo "🏥 Fixing Health Check Bypass in All Services"
echo "============================================="

# Services that need health check bypass fixes
SERVICES=(
    "profile-analysis-service"
    "blockchain-service" 
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Function to fix health check bypass in middleware
fix_health_check_bypass() {
    local service=$1
    local middleware_file="services/$service/src/common/middleware/gateway-auth.middleware.ts"
    
    echo "🔧 Fixing health check bypass for $service..."
    
    if [ ! -f "$middleware_file" ]; then
        echo "⚠️  Gateway auth middleware not found for $service"
        return
    fi
    
    # Update the health check bypass logic to be more permissive
    sed -i 's|req.path === '"'"'/api/health'"'"' || req.path === '"'"'/health'"'"'|req.path === '"'"'/api/health'"'"' || req.path === '"'"'/health'"'"' || req.path.includes('"'"'/health'"'"') || req.path.includes('"'"'/status'"'"')|g' "$middleware_file"
    
    echo "✅ Fixed health check bypass for $service"
}

# Main execution
echo "🚀 Starting health check bypass fixes..."

for service in "${SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service" ]; then
        echo "❌ Service directory not found: services/$service"
        continue
    fi
    
    # Fix health check bypass
    fix_health_check_bypass "$service"
    
    echo "✅ $service health check bypass fixes complete"
done

echo ""
echo "🎉 Health Check Bypass Fixes Completed!"
echo "======================================"
echo ""
echo "📋 Summary:"
echo "- Fixed health check bypass logic for ${#SERVICES[@]} services"
echo "- Health endpoints now properly bypass gateway authentication"
echo "- Updated middleware to be more permissive for health-related paths"
echo ""
echo "🏥 Health endpoints that bypass authentication:"
echo "- /health (exact match)"
echo "- /api/health (exact match)"
echo "- Any path containing '/health'"
echo "- Any path containing '/status'"
echo ""
echo "🔄 Services will restart automatically due to watch mode"
