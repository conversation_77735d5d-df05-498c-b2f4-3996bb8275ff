/**
 * Environment Enumerations for Configuration Standardization
 * Provides type-safe environment and configuration enums
 */

/**
 * Node.js Environment Types
 */
export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test',
}

/**
 * Service Environment Types (Mock vs Real services)
 */
export enum ServiceEnvironment {
  MOCK = 'mock',
  REAL = 'real',
  HYBRID = 'hybrid',
}

/**
 * Log Level Types
 */
export enum LogLevel {
  TRACE = 'trace',
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

/**
 * Log Format Types
 */
export enum LogFormat {
  COMBINED = 'combined',
  JSON = 'json',
  SIMPLE = 'simple',
  STRUCTURED = 'structured',
}

/**
 * Database Types
 */
export enum DatabaseType {
  POSTGRESQL = 'postgresql',
  MYSQL = 'mysql',
  SQLITE = 'sqlite',
  MONGODB = 'mongodb',
}

/**
 * Cache Types
 */
export enum CacheType {
  REDIS = 'redis',
  MEMORY = 'memory',
  MEMCACHED = 'memcached',
  NONE = 'none',
}

/**
 * Service Types
 */
export enum ServiceType {
  API_GATEWAY = 'api-gateway',
  USER_SERVICE = 'user-service',
  PROFILE_ANALYSIS_SERVICE = 'profile-analysis-service',
  NFT_GENERATION_SERVICE = 'nft-generation-service',
  BLOCKCHAIN_SERVICE = 'blockchain-service',
  PROJECT_SERVICE = 'project-service',
  MARKETPLACE_SERVICE = 'marketplace-service',
  NOTIFICATION_SERVICE = 'notification-service',
  ANALYTICS_SERVICE = 'analytics-service',
}

/**
 * Data Classification Levels
 */
export enum DataClassification {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  RESTRICTED = 'restricted',
}

/**
 * Blockchain Networks
 */
export enum BlockchainNetwork {
  MAINNET = 'mainnet',
  TESTNET = 'testnet',
  LOCALHOST = 'localhost',
  POLYGON = 'polygon',
  BSC = 'bsc',
}

/**
 * Environment Validation Utilities
 */
export class EnvironmentValidator {
  /**
   * Validate if environment is valid
   */
  static isValidEnvironment(env: string): env is Environment {
    return Object.values(Environment).includes(env as Environment);
  }

  /**
   * Validate if service environment is valid
   */
  static isValidServiceEnvironment(env: string): env is ServiceEnvironment {
    return Object.values(ServiceEnvironment).includes(env as ServiceEnvironment);
  }

  /**
   * Validate if log level is valid
   */
  static isValidLogLevel(level: string): level is LogLevel {
    return Object.values(LogLevel).includes(level as LogLevel);
  }

  /**
   * Get default environment based on NODE_ENV
   */
  static getDefaultEnvironment(): Environment {
    const nodeEnv = process.env.NODE_ENV;
    if (this.isValidEnvironment(nodeEnv)) {
      return nodeEnv;
    }
    return Environment.DEVELOPMENT;
  }

  /**
   * Get default service environment
   */
  static getDefaultServiceEnvironment(): ServiceEnvironment {
    const serviceEnv = process.env.SERVICE_ENVIRONMENT;
    if (this.isValidServiceEnvironment(serviceEnv)) {
      return serviceEnv;
    }
    return ServiceEnvironment.MOCK;
  }

  /**
   * Check if environment is production
   */
  static isProduction(env: Environment): boolean {
    return env === Environment.PRODUCTION;
  }

  /**
   * Check if environment is development
   */
  static isDevelopment(env: Environment): boolean {
    return env === Environment.DEVELOPMENT;
  }

  /**
   * Check if using mock services
   */
  static isMockEnvironment(env: ServiceEnvironment): boolean {
    return env === ServiceEnvironment.MOCK || env === ServiceEnvironment.HYBRID;
  }

  /**
   * Check if using real services
   */
  static isRealEnvironment(env: ServiceEnvironment): boolean {
    return env === ServiceEnvironment.REAL || env === ServiceEnvironment.HYBRID;
  }
}
