import { Injectable } from '@nestjs/common';
import { ServiceLoggerService, MetricsService } from '../../shared';

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyGenerator: (req: any) => string;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
}

export interface RateLimitEntry {
  key: string;
  requests: number;
  windowStart: Date;
  lastRequest: Date;
  blocked: boolean;
}

export interface RateLimitResult {
  allowed: boolean;
  remainingRequests: number;
  resetTime: Date;
  totalRequests: number;
  windowMs: number;
}

/**
 * Advanced Rate Limit Service
 * 
 * Provides sophisticated rate limiting with:
 * - Sliding window algorithm
 * - Per-user and per-service limits
 * - Configurable key generation
 * - Comprehensive monitoring
 * 
 * @example
 * ```typescript
 * const result = await rateLimiter.checkLimit('user:123', {
 *   windowMs: 60000,
 *   maxRequests: 100
 * });
 * ```
 */
@Injectable()
export class RateLimitService {
  private limits = new Map<string, RateLimitEntry>();
  private configs = new Map<string, RateLimitConfig>();
  private cleanupInterval?: NodeJS.Timeout;

  constructor(
    private readonly logger: ServiceLoggerService,
    private readonly metrics: MetricsService
  ) {
    this.startCleanupTimer();
  }

  /**
   * Check if request is within rate limit
   */
  async checkLimit(
    key: string,
    config: Partial<RateLimitConfig>
  ): Promise<RateLimitResult> {
    const rateLimitConfig = this.getOrCreateConfig(key, config);
    const entry = this.getOrCreateEntry(key, rateLimitConfig);
    const now = new Date();

    // Check if we need to reset the window
    const windowElapsed = now.getTime() - entry.windowStart.getTime();
    if (windowElapsed >= rateLimitConfig.windowMs) {
      // Reset window
      entry.requests = 0;
      entry.windowStart = now;
      entry.blocked = false;
    }

    // Check if limit exceeded
    const allowed = entry.requests < rateLimitConfig.maxRequests;
    
    if (allowed) {
      entry.requests++;
      entry.lastRequest = now;
      entry.blocked = false;
    } else {
      entry.blocked = true;
      
      // Log rate limit exceeded
      this.logger.warn(`Rate limit exceeded`, {
        key,
        requests: entry.requests,
        maxRequests: rateLimitConfig.maxRequests,
        windowMs: rateLimitConfig.windowMs,
      });

      this.metrics.increment('rate_limit_exceeded_total', {
        key_type: this.getKeyType(key),
      });
    }

    // Update metrics
    this.metrics.increment('rate_limit_checks_total', {
      key_type: this.getKeyType(key),
      result: allowed ? 'allowed' : 'blocked',
    });

    this.metrics.gauge('rate_limit_current_requests', entry.requests, {
      key,
    });

    const remainingRequests = Math.max(0, rateLimitConfig.maxRequests - entry.requests);
    const resetTime = new Date(entry.windowStart.getTime() + rateLimitConfig.windowMs);

    return {
      allowed,
      remainingRequests,
      resetTime,
      totalRequests: entry.requests,
      windowMs: rateLimitConfig.windowMs,
    };
  }

  /**
   * Get rate limit status for a key
   */
  async getStatus(key: string): Promise<RateLimitEntry | null> {
    return this.limits.get(key) || null;
  }

  /**
   * Reset rate limit for a key
   */
  async resetLimit(key: string): Promise<void> {
    const entry = this.limits.get(key);
    if (entry) {
      entry.requests = 0;
      entry.windowStart = new Date();
      entry.blocked = false;
      
      this.logger.logBusinessEvent('rate-limit', 'reset', 'SUCCESS', {
        business: {
          domain: 'rate-limit',
          entity: 'rate-limit-entry',
          action: 'reset',
          outcome: 'SUCCESS',
          attributes: {
            key,
          },
        },
      });

      this.metrics.increment('rate_limit_resets_total', {
        key_type: this.getKeyType(key),
      });
    }
  }

  /**
   * Get all rate limit entries
   */
  getAllLimits(): Map<string, RateLimitEntry> {
    return new Map(this.limits);
  }

  /**
   * Get rate limit statistics
   */
  getStats(): any {
    const entries = Array.from(this.limits.values());
    const blockedEntries = entries.filter(entry => entry.blocked);
    const activeEntries = entries.filter(entry => {
      const now = new Date();
      const config = this.configs.get(entry.key);
      if (!config) return false;
      
      const windowElapsed = now.getTime() - entry.windowStart.getTime();
      return windowElapsed < config.windowMs;
    });

    return {
      totalEntries: entries.length,
      activeEntries: activeEntries.length,
      blockedEntries: blockedEntries.length,
      totalRequests: entries.reduce((sum, entry) => sum + entry.requests, 0),
      averageRequestsPerEntry: entries.length > 0 ? 
        (entries.reduce((sum, entry) => sum + entry.requests, 0) / entries.length).toFixed(2) : '0',
    };
  }

  /**
   * Get health status
   */
  getHealthStatus(): any {
    const stats = this.getStats();
    const blockRate = stats.totalEntries > 0 ? 
      (stats.blockedEntries / stats.totalEntries) * 100 : 0;

    return {
      status: 'healthy',
      stats,
      blockRate: blockRate.toFixed(2) + '%',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Clear all rate limits
   */
  async clearAll(): Promise<void> {
    const entriesCount = this.limits.size;
    this.limits.clear();
    this.configs.clear();

    this.logger.logBusinessEvent('rate-limit', 'clear-all', 'SUCCESS', {
      business: {
        domain: 'rate-limit',
        entity: 'rate-limit-entries',
        action: 'clear-all',
        outcome: 'SUCCESS',
        attributes: {
          entriesCleared: entriesCount,
        },
      },
    });

    this.metrics.increment('rate_limit_clears_total', {
      scope: 'all',
    });
  }

  private getOrCreateConfig(key: string, config: Partial<RateLimitConfig>): RateLimitConfig {
    if (!this.configs.has(key)) {
      this.configs.set(key, {
        windowMs: config.windowMs || 60000, // 1 minute default
        maxRequests: config.maxRequests || 100,
        keyGenerator: config.keyGenerator || ((req) => req.ip),
        skipSuccessfulRequests: config.skipSuccessfulRequests || false,
        skipFailedRequests: config.skipFailedRequests || false,
      });
    }
    return this.configs.get(key)!;
  }

  private getOrCreateEntry(key: string, config: RateLimitConfig): RateLimitEntry {
    if (!this.limits.has(key)) {
      this.limits.set(key, {
        key,
        requests: 0,
        windowStart: new Date(),
        lastRequest: new Date(),
        blocked: false,
      });
    }
    return this.limits.get(key)!;
  }

  private getKeyType(key: string): string {
    if (key.startsWith('user:')) return 'user';
    if (key.startsWith('service:')) return 'service';
    if (key.startsWith('ip:')) return 'ip';
    return 'unknown';
  }

  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpired();
    }, 300000); // Run every 5 minutes
  }

  private cleanupExpired(): void {
    const now = new Date();
    let expiredCount = 0;

    for (const [key, entry] of this.limits.entries()) {
      const config = this.configs.get(key);
      if (!config) continue;

      const windowElapsed = now.getTime() - entry.windowStart.getTime();
      
      // Remove entries that are well past their window (2x window time)
      if (windowElapsed > config.windowMs * 2) {
        this.limits.delete(key);
        this.configs.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      this.logger.debug(`Rate limit cleanup completed`, {
        expiredCount,
        remainingEntries: this.limits.size,
      });

      this.metrics.increment('rate_limit_cleanups_total', {
        expired_count: expiredCount.toString(),
      });
    }
  }
}
