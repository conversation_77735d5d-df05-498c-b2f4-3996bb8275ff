'use client'

import React, { useState, useRef, useEffect } from 'react'
import {
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  PhotoIcon,
  FaceSmileIcon,
  EllipsisVerticalIcon,
  MagnifyingGlassIcon,
  ArchiveBoxIcon,
  BellSlashIcon,
  TrashIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import {
  useConversations,
  useMessages,
  useSendMessage,
  useMarkConversationAsRead,
  useMarkMessageAsRead
} from '@/hooks/useSocial'
import { Conversation, DirectMessage, MessageType } from '@/types/social.types'

interface DirectMessagesProps {
  className?: string
}

export default function DirectMessages({
  className = ''
}: DirectMessagesProps) {
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null)
  const [messageText, setMessageText] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [showNewMessage, setShowNewMessage] = useState(false)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { data: conversations, isLoading: conversationsLoading } = useConversations(20, 0)
  const { data: messages, isLoading: messagesLoading } = useMessages(
    selectedConversation?.id || '',
    50,
    0
  )
  
  const sendMessageMutation = useSendMessage()
  const markConversationAsReadMutation = useMarkConversationAsRead()
  const markMessageAsReadMutation = useMarkMessageAsRead()

  const filteredConversations = conversations?.conversations.filter(conversation =>
    searchQuery === '' ||
    conversation.participants.some(participant =>
      participant.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      participant.username.toLowerCase().includes(searchQuery.toLowerCase())
    )
  ) || []

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (selectedConversation && selectedConversation.unreadCount > 0) {
      markConversationAsReadMutation.mutate(selectedConversation.id)
    }
  }, [selectedConversation])

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!messageText.trim() || !selectedConversation) return

    const otherParticipant = selectedConversation.participants.find(p => p.id !== 'current-user-id')
    if (!otherParticipant) return

    try {
      await sendMessageMutation.mutateAsync({
        receiverId: otherParticipant.id,
        content: messageText.trim(),
        messageType: MessageType.TEXT
      })
      
      setMessageText('')
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !selectedConversation) return

    // In a real implementation, you would upload the image and send the URL
    const otherParticipant = selectedConversation.participants.find(p => p.id !== 'current-user-id')
    if (!otherParticipant) return

    sendMessageMutation.mutate({
      receiverId: otherParticipant.id,
      content: 'Image shared',
      messageType: MessageType.IMAGE,
      attachments: [{
        id: Date.now().toString(),
        type: 'image',
        url: URL.createObjectURL(file)
      }]
    })
  }

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
      return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  if (conversationsLoading) {
    return (
      <div className={`h-96 ${className}`}>
        <div className="animate-pulse flex h-full">
          <div className="w-1/3 bg-gray-200 rounded-l-lg"></div>
          <div className="flex-1 bg-gray-100 rounded-r-lg"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg h-96 flex ${className}`}>
      {/* Conversations List */}
      <div className="w-1/3 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-medium text-gray-900">Messages</h3>
            <button
              onClick={() => setShowNewMessage(true)}
              className="text-blue-600 hover:text-blue-700"
            >
              <PlusIcon className="h-5 w-5" />
            </button>
          </div>
          
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Conversations */}
        <div className="flex-1 overflow-y-auto">
          {filteredConversations.length > 0 ? (
            <div className="space-y-1 p-2">
              {filteredConversations.map((conversation) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  isSelected={selectedConversation?.id === conversation.id}
                  onClick={() => setSelectedConversation(conversation)}
                />
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <ChatBubbleLeftRightIcon className="mx-auto h-8 w-8 mb-2" />
                <p className="text-sm">No conversations found</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Message Area */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {selectedConversation.participants
                    .filter(p => p.id !== 'current-user-id')
                    .map(participant => (
                      <div key={participant.id} className="flex items-center space-x-3">
                        <img
                          src={participant.avatar || '/default-avatar.png'}
                          alt={participant.displayName}
                          className="w-8 h-8 rounded-full"
                        />
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {participant.displayName}
                          </div>
                          <div className="text-xs text-gray-500">
                            {participant.isOnline ? 'Online' : 'Offline'}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
                
                <button className="text-gray-400 hover:text-gray-600">
                  <EllipsisVerticalIcon className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messagesLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : messages?.messages.length ? (
                <>
                  {messages.messages.map((message) => (
                    <MessageBubble
                      key={message.id}
                      message={message}
                      isOwnMessage={message.senderId === 'current-user-id'}
                    />
                  ))}
                  <div ref={messagesEndRef} />
                </>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <ChatBubbleLeftRightIcon className="mx-auto h-8 w-8 mb-2" />
                    <p className="text-sm">No messages yet</p>
                    <p className="text-xs">Start the conversation!</p>
                  </div>
                </div>
              )}
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200">
              <form onSubmit={handleSendMessage} className="flex items-center space-x-3">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <PhotoIcon className="h-5 w-5" />
                </button>
                
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={messageText}
                    onChange={(e) => setMessageText(e.target.value)}
                    placeholder="Type a message..."
                    className="w-full px-4 py-2 border border-gray-300 rounded-full text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <FaceSmileIcon className="h-4 w-4" />
                  </button>
                </div>
                
                <button
                  type="submit"
                  disabled={!messageText.trim() || sendMessageMutation.isPending}
                  className="text-blue-600 hover:text-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PaperAirplaneIcon className="h-5 w-5" />
                </button>
              </form>
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
              <p className="text-sm">Choose a conversation from the list to start messaging</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

interface ConversationItemProps {
  conversation: Conversation
  isSelected: boolean
  onClick: () => void
}

function ConversationItem({ conversation, isSelected, onClick }: ConversationItemProps) {
  const otherParticipant = conversation.participants.find(p => p.id !== 'current-user-id')
  
  if (!otherParticipant) return null

  return (
    <div
      onClick={onClick}
      className={`p-3 rounded-lg cursor-pointer transition-colors ${
        isSelected
          ? 'bg-blue-50 border border-blue-200'
          : 'hover:bg-gray-50'
      }`}
    >
      <div className="flex items-start space-x-3">
        <div className="relative">
          <img
            src={otherParticipant.avatar || '/default-avatar.png'}
            alt={otherParticipant.displayName}
            className="w-10 h-10 rounded-full"
          />
          {otherParticipant.isOnline && (
            <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {otherParticipant.displayName}
            </h4>
            {conversation.lastMessageAt && (
              <span className="text-xs text-gray-500">
                {formatMessageTime(conversation.lastMessageAt)}
              </span>
            )}
          </div>
          
          <div className="flex items-center justify-between mt-1">
            <p className="text-sm text-gray-600 truncate">
              {conversation.lastMessage?.content || 'No messages yet'}
            </p>
            {conversation.unreadCount > 0 && (
              <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-blue-600 rounded-full">
                {conversation.unreadCount}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

interface MessageBubbleProps {
  message: DirectMessage
  isOwnMessage: boolean
}

function MessageBubble({ message, isOwnMessage }: MessageBubbleProps) {
  return (
    <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
        isOwnMessage
          ? 'bg-blue-600 text-white'
          : 'bg-gray-100 text-gray-900'
      }`}>
        {message.messageType === MessageType.IMAGE && message.attachments?.[0] ? (
          <div className="space-y-2">
            <img
              src={message.attachments[0].url}
              alt="Shared image"
              className="rounded-lg max-w-full h-auto"
            />
            {message.content !== 'Image shared' && (
              <p className="text-sm">{message.content}</p>
            )}
          </div>
        ) : (
          <p className="text-sm">{message.content}</p>
        )}
        
        <div className={`text-xs mt-1 ${
          isOwnMessage ? 'text-blue-100' : 'text-gray-500'
        }`}>
          {formatMessageTime(message.createdAt)}
          {message.isRead && isOwnMessage && (
            <span className="ml-1">✓</span>
          )}
        </div>
      </div>
    </div>
  )
}

function formatMessageTime(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`
  } else {
    return date.toLocaleDateString()
  }
}
