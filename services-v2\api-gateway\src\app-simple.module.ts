/**
 * API Gateway V2 - Simple App Module
 * 
 * Implements shared infrastructure following SHARED-INFRASTRUCTURE-IMPLEMENTATION-PLAN.md
 * Step 1.3: Update App Module with setupAPIGateway
 * 
 * This follows the EXACT same pattern as user-service for guaranteed compatibility
 */

import { Module } from '@nestjs/common';
import { setupUtilityService } from '@shared';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthModule } from './health/health.module';
import { ProxyModule } from './proxy/proxy.module';

@Module({
  imports: [
    // Shared Infrastructure Setup (as per implementation plan)
    // Temporarily disable auth for API Gateway to get basic functionality working
    setupUtilityService('api-gateway-v2', '2.0.0'),

    // Business Logic Modules
    HealthModule,
    ProxyModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  constructor() {
    console.log('🚀 API Gateway V2 initialized with shared infrastructure');
    console.log('🔧 Features: Logging, Responses, Config, Error Handling (Auth disabled for now)');
    console.log('🌐 Proxy: Service routing and communication');
    console.log('📋 Mode: Utility Service (minimal features for gateway functionality)');
  }
}
