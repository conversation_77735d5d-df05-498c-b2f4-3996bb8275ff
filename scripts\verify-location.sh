#!/bin/bash

# 🎯 LOCATION VERIFICATION SCRIPT

echo "🔍 VERIFYING WORKING DIRECTORY..."
echo "=================================="

# Get current directory
CURRENT_DIR=$(pwd)
EXPECTED_DIR="/c/Users/<USER>/Documents/Augment/social-nft-platform-v2"

echo "Current Directory: $CURRENT_DIR"
echo "Expected Directory: $EXPECTED_DIR"

# Check if we're in the right place
if [ "$CURRENT_DIR" = "$EXPECTED_DIR" ]; then
    echo "✅ CORRECT LOCATION!"
else
    echo "❌ WRONG LOCATION!"
    echo "🚨 Please navigate to: $EXPECTED_DIR"
    exit 1
fi

echo ""
echo "🔍 VERIFYING PROJECT STRUCTURE..."
echo "================================="

# Check key directories exist
DIRS=("services" "frontend" "frontend-fresh" "docs" "tools")
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir/ exists"
    else
        echo "❌ $dir/ missing"
    fi
done

echo ""
echo "🔍 VERIFYING SERVICES..."
echo "======================="

# Check services
SERVICES=("user-service" "api-gateway" "profile-analysis-service")
for service in "${SERVICES[@]}"; do
    if [ -f "services/$service/package.json" ]; then
        echo "✅ services/$service/ ready"
    else
        echo "❌ services/$service/ missing or incomplete"
    fi
done

echo ""
echo "🎯 VERIFICATION COMPLETE!"
echo "========================"
