'use client'

import React, { useState } from 'react'
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ClockIcon,
  BoltIcon,
  GlobeAltIcon,
  WalletIcon,
  ArrowsRightLeftIcon,
  CubeIcon
} from '@heroicons/react/24/outline'
import {
  useBlockchainAnalytics,
  usePortfolioAnalytics,
  useConnectedWallets,
  useActiveNetworks
} from '@/hooks/useBlockchainIntegration'
import { BlockchainNetwork } from '@/types/blockchain-integration.types'

interface BlockchainAnalyticsProps {
  className?: string
}

export default function BlockchainAnalytics({
  className = ''
}: BlockchainAnalyticsProps) {
  const [selectedNetwork, setSelectedNetwork] = useState<BlockchainNetwork>(BlockchainNetwork.ETHEREUM)
  const [timeframe, setTimeframe] = useState('30d')

  const { data: connectedWallets } = useConnectedWallets()
  const { data: activeNetworks } = useActiveNetworks()
  const activeWallet = connectedWallets?.[0]

  const { data: analytics, isLoading: analyticsLoading } = useBlockchainAnalytics(
    selectedNetwork,
    timeframe
  )

  const { data: portfolio, isLoading: portfolioLoading } = usePortfolioAnalytics(
    activeWallet?.address || ''
  )

  const timeframeOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ]

  const formatCurrency = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value
    if (num >= 1000000) {
      return `$${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `$${(num / 1000).toFixed(1)}K`
    }
    return `$${num.toFixed(2)}`
  }

  const formatNumber = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    return value.toLocaleString()
  }

  if (analyticsLoading && !analytics) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header and Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Blockchain Analytics</h2>
          <p className="text-sm text-gray-600">Monitor blockchain activity and portfolio performance</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedNetwork}
            onChange={(e) => setSelectedNetwork(e.target.value as BlockchainNetwork)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {activeNetworks?.map((network) => (
              <option key={network.network} value={network.network}>
                {network.name}
              </option>
            ))}
          </select>
          
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {timeframeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Network Analytics */}
      {analytics && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Network Activity</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ArrowsRightLeftIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {formatNumber(analytics.totalTransactions)}
                  </div>
                  <div className="text-sm text-gray-600">Total Transactions</div>
                  <div className="text-xs text-green-600">
                    {formatNumber(analytics.successfulTransactions)} successful
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BoltIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {parseInt(analytics.averageGasPrice)} gwei
                  </div>
                  <div className="text-sm text-gray-600">Avg Gas Price</div>
                  <div className="text-xs text-blue-600">
                    {analytics.gasEfficiency.toFixed(1)}% efficiency
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CubeIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {formatNumber(analytics.nftMints)}
                  </div>
                  <div className="text-sm text-gray-600">NFT Mints</div>
                  <div className="text-xs text-purple-600">
                    {formatNumber(analytics.nftTransfers)} transfers
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {analytics.averageConfirmationTime}s
                  </div>
                  <div className="text-sm text-gray-600">Avg Confirmation</div>
                  <div className="text-xs text-green-600">
                    {analytics.networkUptime.toFixed(1)}% uptime
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Trends Chart Placeholder */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Activity Trends</h4>
            
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
              <div className="text-center">
                <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-600">Activity trends chart</p>
                <p className="text-xs text-gray-500">Time series visualization would be implemented here</p>
              </div>
            </div>
            
            {/* Trend Summary */}
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              {analytics.transactionTrends.slice(0, 3).map((trend, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">
                      {new Date(trend.timestamp).toLocaleDateString()}
                    </span>
                    <div className="flex items-center">
                      {trend.change > 0 ? (
                        <TrendingUpIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <TrendingDownIcon className="h-4 w-4 text-red-500" />
                      )}
                      <span className={`text-sm ml-1 ${trend.change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {trend.change > 0 ? '+' : ''}{trend.change.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    {formatNumber(trend.value)} transactions
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Portfolio Analytics */}
      {portfolio && activeWallet && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Portfolio Overview</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {formatCurrency(portfolio.totalValue)}
                  </div>
                  <div className="text-sm text-gray-600">Total Portfolio Value</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <GlobeAltIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {Object.keys(portfolio.networks).length}
                  </div>
                  <div className="text-sm text-gray-600">Networks</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CubeIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {Object.values(portfolio.networks).reduce((sum, network: any) => sum + network.nftCount, 0)}
                  </div>
                  <div className="text-sm text-gray-600">Total NFTs</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ArrowsRightLeftIcon className="h-8 w-8 text-orange-600" />
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {formatNumber(portfolio.transactionSummary.total)}
                  </div>
                  <div className="text-sm text-gray-600">Total Transactions</div>
                  <div className="text-xs text-green-600">
                    {((portfolio.transactionSummary.successful / portfolio.transactionSummary.total) * 100).toFixed(1)}% success
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Network Breakdown */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Network Breakdown</h4>
            
            <div className="space-y-4">
              {Object.entries(portfolio.networks).map(([network, data]: [string, any]) => (
                <div key={network} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                    <div>
                      <div className="text-sm font-medium text-gray-900 capitalize">{network}</div>
                      <div className="text-xs text-gray-600">{data.nftCount} NFTs</div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(parseFloat(data.nativeBalance) + parseFloat(data.tokenValue))}
                    </div>
                    <div className="text-xs text-gray-600">
                      {parseFloat(data.nativeBalance).toFixed(4)} native + {formatCurrency(data.tokenValue)} tokens
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Assets */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Top Tokens */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Top Tokens</h4>
              
              <div className="space-y-3">
                {portfolio.topTokens.slice(0, 5).map((token: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-gray-600">
                          {token.symbol.slice(0, 2)}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{token.symbol}</div>
                        <div className="text-xs text-gray-600">{token.name}</div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(token.value)}
                      </div>
                      <div className="text-xs text-gray-600">
                        {parseFloat(token.balance).toFixed(2)} {token.symbol}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top NFTs */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Top NFTs</h4>
              
              <div className="space-y-3">
                {portfolio.topNFTs.slice(0, 5).map((nft: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <CubeIcon className="h-4 w-4 text-purple-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{nft.name}</div>
                        <div className="text-xs text-gray-600">#{nft.tokenId}</div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(nft.estimatedValue)}
                      </div>
                      <div className="text-xs text-gray-600 capitalize">
                        {nft.collection}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Gas Analytics */}
      {analytics && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Gas Analytics</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {formatCurrency(analytics.gasSavings)}
              </div>
              <div className="text-sm text-gray-600">Gas Savings</div>
              <div className="text-xs text-gray-500">vs. standard rates</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {analytics.gasEfficiency.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Efficiency Score</div>
              <div className="text-xs text-gray-500">optimization rate</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {parseInt(analytics.averageGasUsed).toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Avg Gas Used</div>
              <div className="text-xs text-gray-500">per transaction</div>
            </div>
          </div>
        </div>
      )}

      {/* Export Options */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Export Analytics</h4>
            <p className="text-xs text-gray-600">Download your blockchain analytics and portfolio data</p>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Export CSV
            </button>
            <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Export JSON
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
