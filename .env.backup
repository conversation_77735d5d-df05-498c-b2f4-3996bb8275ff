# ===== MOCK SERVICES ENVIRONMENT =====
# Fast development with mock services - no external dependencies required
# Use: ./scripts/switch-environment.sh mock

# ===== ENVIRONMENT CONFIGURATION =====
NODE_ENV=development
SERVICE_ENVIRONMENT=mock
USE_MOCK_SERVICES=true

# ===== SERVICE DISCOVERY =====
API_GATEWAY_PORT=3010
API_GATEWAY_URL=http://localhost:3010

# ===== REAL SERVICE PORTS =====
USER_SERVICE_PORT=3011
PROFILE_ANALYSIS_SERVICE_PORT=3002
NFT_GENERATION_SERVICE_PORT=3003
BLOCKCHAIN_SERVICE_PORT=3004
PROJECT_SERVICE_PORT=3005
MARKETPLACE_SERVICE_PORT=3006
NOTIFICATION_SERVICE_PORT=3008
ANALYTICS_SERVICE_PORT=3009

# ===== MOCK SERVICE PORTS =====
MOCK_TWITTER_SERVICE_PORT=3020
MOCK_BLOCKCHAIN_SERVICE_PORT=3021
MOCK_NFT_STORAGE_SERVICE_PORT=3022

# ===== SERVICE URLS (MOCK CONFIGURATION) =====
# External services point to mock implementations
TWITTER_SERVICE_URL=http://localhost:3020
BLOCKCHAIN_SERVICE_URL=http://localhost:3021
NFT_STORAGE_SERVICE_URL=http://localhost:3022

# Internal services use real implementations
USER_SERVICE_URL=http://localhost:3011
PROFILE_ANALYSIS_SERVICE_URL=http://localhost:3002
NFT_GENERATION_SERVICE_URL=http://localhost:3003
PROJECT_SERVICE_URL=http://localhost:3005
MARKETPLACE_SERVICE_URL=http://localhost:3006
NOTIFICATION_SERVICE_URL=http://localhost:3008
ANALYTICS_SERVICE_URL=http://localhost:3009

# ===== DATABASE CONFIGURATION =====
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111

# Service-Specific Database Names
USER_DB_NAME=user_service
PROFILE_ANALYSIS_DB_NAME=profile_analysis_service
NFT_GENERATION_DB_NAME=nft_generation_service
BLOCKCHAIN_DB_NAME=blockchain_service
PROJECT_DB_NAME=project_service
MARKETPLACE_DB_NAME=marketplace_service
NOTIFICATION_DB_NAME=notification_service
ANALYTICS_DB_NAME=analytics_service

# ===== SECURITY CONFIGURATION =====
# JWT Configuration
JWT_SECRET=dev-jwt-secret-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Gateway Security
GATEWAY_SECRET=dev-gateway-secret-change-in-production
ALLOW_DIRECT_ACCESS=true
TRUSTED_IPS=127.0.0.1,::1,localhost
ENABLE_GATEWAY_AUTH=true

# ===== CACHING CONFIGURATION =====
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379
ENABLE_REDIS_CACHE=false
CACHE_TTL=300

# ===== LOGGING CONFIGURATION =====
LOG_LEVEL=debug
LOG_FORMAT=combined
ENABLE_DEBUG_LOGGING=true

# ===== DEVELOPMENT FLAGS =====
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_HOT_RELOAD=true
ENABLE_PERFORMANCE_MONITORING=true

# ===== MOCK EXTERNAL SERVICES =====
# No real API keys needed - mock services provide test data
TWITTER_API_KEY=mock-twitter-key
TWITTER_API_SECRET=mock-twitter-secret
TWITTER_BEARER_TOKEN=mock-twitter-bearer

BLOCKCHAIN_RPC_URL=http://localhost:3021/rpc
BLOCKCHAIN_PRIVATE_KEY=mock-private-key
BLOCKCHAIN_NETWORK=testnet

NFT_STORAGE_API_KEY=mock-nft-storage-key
NFT_STORAGE_ENDPOINT=http://localhost:3022

# ===== MOCK EMAIL SERVICE =====
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=mock-user
SMTP_PASS=mock-pass
SMTP_SECURE=false

# ===== MONITORING & OBSERVABILITY =====
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=30

# ===== FEATURE FLAGS =====
ENABLE_AUDIT_LOGGING=true
ENABLE_EVENT_SOURCING=true
ENABLE_RATE_LIMITING=false
ENABLE_API_VERSIONING=true

# ===== MOCK SERVICE CONFIGURATION =====
# Mock services provide predictable test data
MOCK_DATA_SEED=12345
MOCK_RESPONSE_DELAY=100
ENABLE_MOCK_ERRORS=false
MOCK_ERROR_RATE=0.1
