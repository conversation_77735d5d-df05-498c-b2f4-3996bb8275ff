# Campaign Management API Documentation

This document provides detailed information about the Campaign Management API endpoints for the Social NFT Platform.

## Table of Contents

1. [Introduction](#introduction)
2. [Authentication](#authentication)
3. [Base URL](#base-url)
4. [Campaigns](#campaigns)
5. [Campaign Analysis](#campaign-analysis)
6. [Campaign Participants](#campaign-participants)
7. [<PERSON><PERSON><PERSON>ling](#error-handling)

## Introduction

The Campaign Management API allows project owners to create and manage campaigns, analyze Twitter profiles, and track participant engagement. This API is part of the Social NFT Platform, which helps blockchain, crypto, and Web3 projects attract and retain users through upgradable NFTs.

## Authentication

All API requests require authentication. You must include a valid JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Base URL

The base URL for all API endpoints is:

```
https://api.socialnft.platform/api
```

## Campaigns

### List Campaigns

Retrieves a list of campaigns for a specific project.

**Endpoint:** `GET /campaigns`

**Query Parameters:**
- `projectId` (required): The ID of the project
- `userId` (required): The ID of the user making the request

**Response:**
```json
[
  {
    "id": "campaign-id",
    "name": "Campaign Name",
    "description": "Campaign Description",
    "projectId": "project-id",
    "logoUrl": "https://example.com/logo.png",
    "bannerUrl": "https://example.com/banner.png",
    "startDate": "2023-01-01T00:00:00Z",
    "endDate": "2023-12-31T23:59:59Z",
    "status": "active",
    "visibility": "public",
    "participationConditions": {},
    "rewards": {},
    "metadata": {},
    "collectionId": "collection-id",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
]
```

### Get Campaign

Retrieves a specific campaign by ID.

**Endpoint:** `GET /campaigns/{campaignId}`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Response:**
```json
{
  "id": "campaign-id",
  "name": "Campaign Name",
  "description": "Campaign Description",
  "projectId": "project-id",
  "logoUrl": "https://example.com/logo.png",
  "bannerUrl": "https://example.com/banner.png",
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-12-31T23:59:59Z",
  "status": "active",
  "visibility": "public",
  "participationConditions": {},
  "rewards": {},
  "metadata": {},
  "collectionId": "collection-id",
  "config": {
    "id": "config-id",
    "campaignId": "campaign-id",
    "nftGenerationStrategy": "automatic",
    "defaultTemplateId": "template-id",
    "defaultNetwork": "mumbai",
    "defaultContractId": "contract-id",
    "autoMintEnabled": false,
    "twitterAnalysisFixedParams": {},
    "twitterAnalysisVariableParams": {},
    "nftClassificationThresholds": {},
    "nftGenerationConfig": {},
    "blockchainConfig": {},
    "marketplaceConfig": {},
    "evolutionConfig": {},
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  },
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

### Create Campaign

Creates a new campaign for a project.

**Endpoint:** `POST /campaigns`

**Query Parameters:**
- `userId` (required): The ID of the user making the request

**Request Body:**
```json
{
  "name": "Campaign Name",
  "description": "Campaign Description",
  "projectId": "project-id",
  "logoUrl": "https://example.com/logo.png",
  "bannerUrl": "https://example.com/banner.png",
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-12-31T23:59:59Z",
  "status": "draft",
  "visibility": "private",
  "participationConditions": {},
  "rewards": {},
  "metadata": {},
  "collectionId": "collection-id",
  "config": {
    "nftGenerationStrategy": "automatic",
    "defaultTemplateId": "template-id",
    "defaultNetwork": "mumbai",
    "defaultContractId": "contract-id",
    "autoMintEnabled": false,
    "twitterAnalysisFixedParams": {
      "includeBio": true,
      "includeAvatar": true,
      "includeBanner": true,
      "includeSpecificText": false,
      "specificTextValue": "",
      "followerCountRanges": [
        { "min": 0, "max": 100, "score": 10 },
        { "min": 101, "max": 500, "score": 20 },
        { "min": 501, "max": 1000, "score": 30 }
      ],
      "totalPostsRanges": [
        { "min": 0, "max": 50, "score": 10 },
        { "min": 51, "max": 200, "score": 20 },
        { "min": 201, "max": 1000, "score": 30 }
      ],
      "engagementRateRanges": [
        { "min": 0, "max": 1, "score": 10 },
        { "min": 1.1, "max": 3, "score": 20 },
        { "min": 3.1, "max": 10, "score": 30 }
      ]
    },
    "twitterAnalysisVariableParams": {
      "earlyJoinBonus": true,
      "earlyJoinBonusScore": 10,
      "activeDaysWeight": 0.2,
      "projectInteractionsWeight": 0.3,
      "contentProductionWeight": 0.2,
      "contentQualityWeight": 0.2,
      "referralsWeight": 0.1
    },
    "nftClassificationThresholds": {
      "common": { "minScore": 0, "maxScore": 50 },
      "rare": { "minScore": 51, "maxScore": 80 },
      "legendary": { "minScore": 81 }
    },
    "evolutionConfig": {
      "maxEvolutionStages": 3,
      "autoEvolve": true,
      "checkIntervalHours": 24,
      "notifyOwnerOnEvolution": true,
      "requireOwnerApproval": false,
      "updateMetadataOnChain": true
    }
  }
}
```

**Response:**
```json
{
  "id": "campaign-id",
  "name": "Campaign Name",
  "description": "Campaign Description",
  "projectId": "project-id",
  "logoUrl": "https://example.com/logo.png",
  "bannerUrl": "https://example.com/banner.png",
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-12-31T23:59:59Z",
  "status": "draft",
  "visibility": "private",
  "participationConditions": {},
  "rewards": {},
  "metadata": {},
  "collectionId": "collection-id",
  "config": {
    "id": "config-id",
    "campaignId": "campaign-id",
    "nftGenerationStrategy": "automatic",
    "defaultTemplateId": "template-id",
    "defaultNetwork": "mumbai",
    "defaultContractId": "contract-id",
    "autoMintEnabled": false,
    "twitterAnalysisFixedParams": {},
    "twitterAnalysisVariableParams": {},
    "nftClassificationThresholds": {},
    "nftGenerationConfig": {},
    "blockchainConfig": {},
    "marketplaceConfig": {},
    "evolutionConfig": {},
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  },
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

### Update Campaign

Updates an existing campaign.

**Endpoint:** `PUT /campaigns/{campaignId}`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Query Parameters:**
- `userId` (required): The ID of the user making the request

**Request Body:**
```json
{
  "name": "Updated Campaign Name",
  "description": "Updated Campaign Description",
  "logoUrl": "https://example.com/updated-logo.png",
  "bannerUrl": "https://example.com/updated-banner.png",
  "startDate": "2023-02-01T00:00:00Z",
  "endDate": "2023-11-30T23:59:59Z",
  "status": "active",
  "visibility": "public",
  "participationConditions": {},
  "rewards": {},
  "metadata": {},
  "config": {
    "nftGenerationStrategy": "automatic",
    "defaultTemplateId": "updated-template-id",
    "defaultNetwork": "mumbai",
    "autoMintEnabled": true,
    "twitterAnalysisFixedParams": {},
    "twitterAnalysisVariableParams": {},
    "nftClassificationThresholds": {}
  }
}
```

**Response:**
```json
{
  "id": "campaign-id",
  "name": "Updated Campaign Name",
  "description": "Updated Campaign Description",
  "projectId": "project-id",
  "logoUrl": "https://example.com/updated-logo.png",
  "bannerUrl": "https://example.com/updated-banner.png",
  "startDate": "2023-02-01T00:00:00Z",
  "endDate": "2023-11-30T23:59:59Z",
  "status": "active",
  "visibility": "public",
  "participationConditions": {},
  "rewards": {},
  "metadata": {},
  "collectionId": "collection-id",
  "config": {
    "id": "config-id",
    "campaignId": "campaign-id",
    "nftGenerationStrategy": "automatic",
    "defaultTemplateId": "updated-template-id",
    "defaultNetwork": "mumbai",
    "defaultContractId": "contract-id",
    "autoMintEnabled": true,
    "twitterAnalysisFixedParams": {},
    "twitterAnalysisVariableParams": {},
    "nftClassificationThresholds": {},
    "nftGenerationConfig": {},
    "blockchainConfig": {},
    "marketplaceConfig": {},
    "evolutionConfig": {},
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  },
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

### Update Campaign Status

Updates the status of an existing campaign.

**Endpoint:** `PUT /campaigns/{campaignId}/status`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Query Parameters:**
- `userId` (required): The ID of the user making the request

**Request Body:**
```json
{
  "status": "active"
}
```

**Response:**
```json
{
  "id": "campaign-id",
  "name": "Campaign Name",
  "description": "Campaign Description",
  "projectId": "project-id",
  "logoUrl": "https://example.com/logo.png",
  "bannerUrl": "https://example.com/banner.png",
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-12-31T23:59:59Z",
  "status": "active",
  "visibility": "public",
  "participationConditions": {},
  "rewards": {},
  "metadata": {},
  "collectionId": "collection-id",
  "config": {
    "id": "config-id",
    "campaignId": "campaign-id",
    "nftGenerationStrategy": "automatic",
    "defaultTemplateId": "template-id",
    "defaultNetwork": "mumbai",
    "defaultContractId": "contract-id",
    "autoMintEnabled": false,
    "twitterAnalysisFixedParams": {},
    "twitterAnalysisVariableParams": {},
    "nftClassificationThresholds": {},
    "nftGenerationConfig": {},
    "blockchainConfig": {},
    "marketplaceConfig": {},
    "evolutionConfig": {},
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  },
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

### Delete Campaign

Deletes an existing campaign.

**Endpoint:** `DELETE /campaigns/{campaignId}`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Query Parameters:**
- `userId` (required): The ID of the user making the request

**Response:**
```json
{
  "message": "Campaign deleted successfully"
}
```## Campaign Analysis

The Campaign Analysis API allows project owners to analyze Twitter profiles and determine NFT types based on user engagement.

### Request Profile Analysis

Requests an analysis of a Twitter profile for a campaign.

**Endpoint:** `POST /campaigns/{campaignId}/analysis/request`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Query Parameters:**
- `userId` (required): The ID of the user making the request

**Request Body:**
```json
{
  "twitterUsername": "username",
  "nftId": "nft-id"  // Optional, for re-analysis of existing NFT
}
```

**Response:**
```json
{
  "message": "Analysis request accepted"
}
```

### Process Analysis Results

Processes analysis results and determines the NFT type.

**Endpoint:** `POST /campaigns/{campaignId}/analysis/process`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Query Parameters:**
- `userId` (required): The ID of the user making the request

**Request Body:**
```json
{
  "analysisId": "analysis-id",
  "results": {
    "overallScore": 75,
    "followerScore": 20,
    "engagementScore": 30,
    "activityScore": 15,
    "contentScore": 10
  },
  "nftId": "nft-id"  // Optional, for updating existing NFT
}
```

**Response:**
```json
{
  "nftType": "rare",
  "score": 75
}
```

### Check NFT Evolution

Checks if an NFT should evolve based on a new analysis score.

**Endpoint:** `POST /campaigns/{campaignId}/analysis/check-evolution`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Request Body:**
```json
{
  "currentNftType": "common",
  "newScore": 75
}
```

**Response:**
```json
{
  "shouldEvolve": true,
  "newType": "rare"
}
```

## Campaign Participants

The Campaign Participants API allows project owners to manage and track participants in their campaigns.

### Join Campaign

Allows a user to join a campaign.

**Endpoint:** `POST /campaigns/{campaignId}/participants/join`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Request Body:**
```json
{
  "userId": "user-id",
  "twitterUsername": "username"
}
```

**Response:**
```json
{
  "id": "participant-id",
  "userId": "user-id",
  "twitterUsername": "username",
  "campaignId": "campaign-id",
  "status": "active",
  "createdAt": "2023-01-01T00:00:00Z"
}
```

### Get Participants

Retrieves all participants for a campaign.

**Endpoint:** `GET /campaigns/{campaignId}/participants`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Response:**
```json
[
  {
    "id": "participant-id",
    "userId": "user-id",
    "twitterUsername": "username",
    "campaignId": "campaign-id",
    "nftId": "nft-id",
    "nftType": "rare",
    "currentScore": 75,
    "status": "active"
  }
]
```

### Get Participant

Retrieves a specific participant by ID.

**Endpoint:** `GET /campaigns/{campaignId}/participants/{participantId}`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign
- `participantId` (required): The ID of the participant

**Response:**
```json
{
  "id": "participant-id",
  "userId": "user-id",
  "twitterUsername": "username",
  "campaignId": "campaign-id",
  "nftId": "nft-id",
  "nftType": "rare",
  "currentScore": 75,
  "status": "active",
  "metrics": {
    "followerScore": 20,
    "engagementScore": 30,
    "activityScore": 15,
    "contentScore": 10,
    "overallScore": 75,
    "lastAnalysisDate": "2023-01-01T00:00:00Z",
    "analysisHistory": [
      {
        "date": "2023-01-01T00:00:00Z",
        "score": 75,
        "nftType": "rare"
      }
    ]
  },
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

### Get Top Participants

Retrieves the top participants for a campaign based on their score.

**Endpoint:** `GET /campaigns/{campaignId}/participants/top`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign

**Query Parameters:**
- `limit` (optional): The number of participants to return (default: 10)

**Response:**
```json
[
  {
    "id": "participant-id-1",
    "userId": "user-id-1",
    "twitterUsername": "username1",
    "nftType": "legendary",
    "currentScore": 90,
    "status": "active"
  },
  {
    "id": "participant-id-2",
    "userId": "user-id-2",
    "twitterUsername": "username2",
    "nftType": "rare",
    "currentScore": 75,
    "status": "active"
  }
]
```

### Update Participant Status

Updates the status of a participant.

**Endpoint:** `PUT /campaigns/{campaignId}/participants/{participantId}/status`

**Path Parameters:**
- `campaignId` (required): The ID of the campaign
- `participantId` (required): The ID of the participant

**Request Body:**
```json
{
  "status": "inactive"
}
```

**Response:**
```json
{
  "id": "participant-id",
  "userId": "user-id",
  "twitterUsername": "username",
  "status": "inactive",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

## Error Handling

The API uses standard HTTP status codes to indicate the success or failure of a request.

### Common Error Codes

- `400 Bad Request`: The request was malformed or contained invalid parameters.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to access the requested resource.
- `404 Not Found`: The requested resource was not found.
- `500 Internal Server Error`: An unexpected error occurred on the server.

### Error Response Format

```json
{
  "statusCode": 400,
  "message": "Error message describing what went wrong",
  "error": "Bad Request"
}
```
