import { <PERSON>, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('ipfs')
@Controller('ipfs')
export class IPFSController {

  @Get(':hash')
  @ApiOperation({ summary: 'Get IPFS file by hash' })
  @ApiResponse({ status: 200, description: 'IPFS file retrieved successfully' })
  async getIPFSFile(@Param('hash') hash: string) {
    return {
      status: 'success',
      data: {
        hash,
        url: `https://mock-ipfs.io/ipfs/${hash}`,
        gateway: 'mock-ipfs.io',
        pinned: true,
        size: 1024,
        timestamp: new Date().toISOString()
      }
    };
  }

  @Get('pin/:hash')
  @ApiOperation({ summary: 'Pin IPFS file' })
  @ApiResponse({ status: 200, description: 'File pinned successfully' })
  async pinFile(@Param('hash') hash: string) {
    return {
      status: 'success',
      data: {
        hash,
        pinned: true,
        timestamp: new Date().toISOString()
      },
      message: 'File pinned successfully'
    };
  }
}
