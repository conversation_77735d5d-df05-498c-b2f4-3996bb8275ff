/**
 * Analysis Service - Profile Analysis Service V2
 * 
 * Core business logic for profile analysis
 * Database Per Service Pattern - Uses profile_analysis_service_v2 database only
 */

import { Injectable, NotFoundException } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { PrismaService } from '../../prisma/prisma.service';
import { QueueService } from './queue.service';
import { AIService } from './ai.service';
import { CreateAnalysisDto } from '../dto/create-analysis.dto';
import { UpdateAnalysisDto } from '../dto/update-analysis.dto';
import { QueryAnalysisDto } from '../dto/query-analysis.dto';

@Injectable()
export class AnalysisService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly queueService: QueueService,
    private readonly aiService: AIService,
  ) {}

  async createAnalysis(createAnalysisDto: CreateAnalysisDto) {
    const requestId = uuidv4();

    // Create analysis record
    const analysis = await this.prisma.profileAnalysis.create({
      data: {
        userId: createAnalysisDto.userId,
        requestId,
        analysisType: createAnalysisDto.analysisType,
        sourceUrl: createAnalysisDto.sourceUrl,
        status: 'pending',
        progress: 0,
        metadata: createAnalysisDto.metadata || {},
      },
    });

    // Queue the analysis for processing
    await this.queueService.queueAnalysis({
      analysisId: analysis.id,
      userId: createAnalysisDto.userId,
      requestId,
      type: createAnalysisDto.analysisType,
      sourceUrl: createAnalysisDto.sourceUrl,
      metadata: createAnalysisDto.metadata,
    });

    console.log(`📊 Analysis created and queued: ${analysis.id} (${createAnalysisDto.analysisType})`);

    return {
      id: analysis.id,
      requestId: analysis.requestId,
      userId: analysis.userId,
      analysisType: analysis.analysisType,
      status: analysis.status,
      progress: analysis.progress,
      createdAt: analysis.createdAt,
    };
  }

  async findAll(queryDto: QueryAnalysisDto) {
    const {
      page = 1,
      limit = 20,
      userId,
      analysisType,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = queryDto;

    const skip = (page - 1) * limit;
    const where: any = {};

    // Apply filters
    if (userId) {
      where.userId = userId;
    }

    if (analysisType) {
      where.analysisType = analysisType;
    }

    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { requestId: { contains: search, mode: 'insensitive' } },
        { sourceUrl: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get analyses and total count
    const [analyses, totalCount] = await Promise.all([
      this.prisma.profileAnalysis.findMany({
        where,
        include: {
          metrics: {
            take: 5,
            orderBy: { createdAt: 'desc' },
          },
          insights: {
            take: 3,
            orderBy: { importance: 'desc' },
          },
          recommendations: {
            take: 3,
            orderBy: { priority: 'desc' },
          },
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.profileAnalysis.count({ where }),
    ]);

    // Build pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const pagination = {
      page,
      limit,
      totalCount,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return { analyses, pagination };
  }

  async findOne(id: string) {
    const analysis = await this.prisma.profileAnalysis.findUnique({
      where: { id },
      include: {
        metrics: {
          orderBy: { createdAt: 'desc' },
        },
        insights: {
          orderBy: { importance: 'desc' },
        },
        recommendations: {
          orderBy: { priority: 'desc' },
        },
      },
    });

    return analysis;
  }

  async findByUser(userId: string, queryDto: QueryAnalysisDto) {
    return this.findAll({ ...queryDto, userId });
  }

  async update(id: string, updateAnalysisDto: UpdateAnalysisDto) {
    try {
      const analysis = await this.prisma.profileAnalysis.update({
        where: { id },
        data: {
          ...updateAnalysisDto,
          updatedAt: new Date(),
          ...(updateAnalysisDto.status === 'completed' && {
            completedAt: new Date(),
          }),
        },
      });

      console.log(`📊 Analysis updated: ${id} - Status: ${analysis.status}, Progress: ${analysis.progress}%`);

      return analysis;
    } catch (error) {
      if (error.code === 'P2025') {
        return null; // Analysis not found
      }
      throw error;
    }
  }

  async remove(id: string) {
    try {
      await this.prisma.profileAnalysis.delete({
        where: { id },
      });

      console.log(`🗑️ Analysis deleted: ${id}`);
      return true;
    } catch (error) {
      if (error.code === 'P2025') {
        return false; // Analysis not found
      }
      throw error;
    }
  }

  async processAnalysis(id: string) {
    const analysis = await this.prisma.profileAnalysis.findUnique({
      where: { id },
    });

    if (!analysis) {
      return null;
    }

    if (analysis.status !== 'pending') {
      throw new Error(`Analysis ${id} is not in pending status`);
    }

    // Update status to processing
    await this.update(id, { status: 'processing', progress: 10 });

    // Start AI processing (async)
    this.aiService.processAnalysis(analysis).catch((error) => {
      console.error(`❌ AI processing failed for analysis ${id}:`, error);
      this.update(id, { status: 'failed', metadata: { error: error.message } });
    });

    return {
      id: analysis.id,
      status: 'processing',
      message: 'Analysis processing started',
    };
  }

  async getStatus(id: string) {
    const analysis = await this.prisma.profileAnalysis.findUnique({
      where: { id },
      select: {
        id: true,
        requestId: true,
        status: true,
        progress: true,
        createdAt: true,
        updatedAt: true,
        completedAt: true,
      },
    });

    return analysis;
  }

  async getResults(id: string) {
    const analysis = await this.prisma.profileAnalysis.findUnique({
      where: { id },
      include: {
        metrics: true,
        insights: true,
        recommendations: true,
      },
    });

    if (!analysis) {
      return null;
    }

    return {
      id: analysis.id,
      requestId: analysis.requestId,
      userId: analysis.userId,
      analysisType: analysis.analysisType,
      status: analysis.status,
      progress: analysis.progress,
      results: analysis.results,
      confidence: analysis.confidence,
      metrics: analysis.metrics,
      insights: analysis.insights,
      recommendations: analysis.recommendations,
      createdAt: analysis.createdAt,
      updatedAt: analysis.updatedAt,
      completedAt: analysis.completedAt,
    };
  }

  async getAnalyticsSummary() {
    const [
      totalAnalyses,
      completedAnalyses,
      pendingAnalyses,
      processingAnalyses,
      failedAnalyses,
      avgConfidence,
    ] = await Promise.all([
      this.prisma.profileAnalysis.count(),
      this.prisma.profileAnalysis.count({ where: { status: 'completed' } }),
      this.prisma.profileAnalysis.count({ where: { status: 'pending' } }),
      this.prisma.profileAnalysis.count({ where: { status: 'processing' } }),
      this.prisma.profileAnalysis.count({ where: { status: 'failed' } }),
      this.prisma.profileAnalysis.aggregate({
        where: { status: 'completed' },
        _avg: { confidence: true },
      }),
    ]);

    return {
      total: totalAnalyses,
      completed: completedAnalyses,
      pending: pendingAnalyses,
      processing: processingAnalyses,
      failed: failedAnalyses,
      successRate: totalAnalyses > 0 ? (completedAnalyses / totalAnalyses) * 100 : 0,
      averageConfidence: avgConfidence._avg.confidence || 0,
      timestamp: new Date().toISOString(),
    };
  }
}
