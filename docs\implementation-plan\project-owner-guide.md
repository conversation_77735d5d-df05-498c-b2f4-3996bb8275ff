# Social NFT Platform - Project Owner Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Creating a Project](#creating-a-project)
4. [Managing Campaigns](#managing-campaigns)
5. [Twitter Analysis Parameters](#twitter-analysis-parameters)
6. [NFT Classification](#nft-classification)
7. [Monitoring Participants](#monitoring-participants)
8. [Best Practices](#best-practices)

## Introduction

Welcome to the Social NFT Platform! This guide is designed to help project owners make the most of our platform to attract and retain users through upgradable NFTs.

The Social NFT Platform helps blockchain, crypto, and Web3 projects by:

- Attracting new users through Twitter engagement
- Retaining existing users with upgradable NFTs
- Creating a marketplace for NFTs to increase brand awareness
- Providing analytics on user engagement and NFT evolution

This guide will walk you through the process of creating and managing campaigns, configuring Twitter analysis parameters, and monitoring participant engagement.

## Getting Started

### Account Creation

To get started with the Social NFT Platform, you need to create an account:

1. Visit [https://socialnft.platform](https://socialnft.platform)
2. Click on "Sign Up" in the top-right corner
3. Fill in your details and click "Create Account"
4. Verify your email address by clicking the link in the verification email

### Dashboard Overview

After logging in, you'll be taken to your dashboard, which provides an overview of your projects and campaigns. The dashboard includes:

- Projects overview
- Recent campaigns
- Platform statistics
- Notifications

### Navigation

The main navigation menu includes:

- **Dashboard**: Overview of your projects and campaigns
- **Projects**: List of all your projects
- **Campaigns**: List of all your campaigns across projects
- **Analytics**: Detailed analytics on user engagement and NFT evolution
- **Settings**: Account and platform settings

## Creating a Project

Before you can create a campaign, you need to create a project:

1. Click on "Projects" in the main navigation menu
2. Click on "Create Project" button
3. Fill in the project details:
   - **Name**: Your project name
   - **Description**: A brief description of your project
   - **Website**: Your project website
   - **Social Media Links**: Links to your project's social media accounts
   - **Logo**: Upload your project logo
   - **Banner**: Upload a banner image for your project
4. Click "Create Project" to save your project

### Project Configuration

After creating a project, you can configure additional settings:

1. Go to your project page
2. Click on "Settings" tab
3. Configure the following settings:
   - **Team Members**: Add team members to help manage your project
   - **Blockchain Networks**: Select which blockchain networks you want to support
   - **Contract Settings**: Configure your NFT contract settings
   - **Marketplace Settings**: Configure marketplace settings for your NFTs
4. Click "Save Changes" to update your project configuration

## Managing Campaigns

Campaigns are the core of the Social NFT Platform. They allow you to engage with users on Twitter and reward them with NFTs based on their engagement.

### Creating a Campaign

To create a new campaign:

1. Go to your project page
2. Click on "Campaigns" tab
3. Click on "Create Campaign" button
4. Fill in the campaign details:
   - **Name**: Your campaign name
   - **Description**: A brief description of your campaign
   - **Logo**: Upload your campaign logo
   - **Banner**: Upload a banner image for your campaign
   - **Start Date**: When the campaign will start
   - **End Date**: When the campaign will end
   - **Status**: Draft, Scheduled, Active, Paused, Completed, or Cancelled
   - **Visibility**: Public or Private
   - **Participation Conditions**: Conditions for users to participate in your campaign
   - **Rewards**: Rewards for participants
5. Click "Next" to configure Twitter analysis parameters

### Campaign Status

Campaigns can have the following statuses:

- **Draft**: The campaign is being created and is not yet visible to users
- **Scheduled**: The campaign is scheduled to start at a future date
- **Active**: The campaign is currently active and users can participate
- **Paused**: The campaign is temporarily paused
- **Completed**: The campaign has ended
- **Cancelled**: The campaign has been cancelled

You can change the status of a campaign at any time by clicking on the "Status" dropdown on the campaign page.

### Campaign Dashboard

Each campaign has its own dashboard that provides an overview of the campaign's performance:

- **Overview**: General statistics about the campaign
- **Participants**: List of participants and their engagement metrics
- **NFTs**: List of NFTs minted for the campaign
- **Analytics**: Detailed analytics on user engagement and NFT evolution
- **Settings**: Campaign settings

### Editing a Campaign

To edit an existing campaign:

1. Go to your campaign page
2. Click on "Settings" tab
3. Update the campaign details
4. Click "Save Changes" to update your campaign

### Deleting a Campaign

To delete a campaign:

1. Go to your campaign page
2. Click on "Settings" tab
3. Scroll to the bottom and click "Delete Campaign"
4. Confirm the deletion

> **Warning**: Deleting a campaign is permanent and cannot be undone. All data associated with the campaign, including participant data and NFTs, will be deleted.

## Twitter Analysis Parameters

The Social NFT Platform uses Twitter profile analysis to determine the NFT type that a user will receive. You can configure these parameters to match your campaign goals.

### Fixed Parameters

Fixed parameters are used during the initial analysis of a Twitter profile. These parameters evaluate the profile's existing metrics:

#### Profile Elements

- **Include Bio**: Whether to include the user's bio in the analysis
- **Include Avatar**: Whether to include the user's avatar in the analysis
- **Include Banner**: Whether to include the user's banner in the analysis
- **Include Specific Text**: Whether to check for specific text in the user's profile
- **Specific Text Value**: The text to look for in the user's profile

#### Follower Count Ranges

You can define score ranges based on the number of followers:

| Range | Score |
|-------|-------|
| 0-100 | 10 |
| 101-500 | 20 |
| 501-1000 | 30 |
| 1001-5000 | 40 |
| 5001+ | 50 |

#### Total Posts Ranges

You can define score ranges based on the number of posts:

| Range | Score |
|-------|-------|
| 0-50 | 10 |
| 51-200 | 20 |
| 201-1000 | 30 |
| 1001+ | 40 |

#### Engagement Rate Ranges

You can define score ranges based on the engagement rate:

| Range | Score |
|-------|-------|
| 0-1% | 10 |
| 1.1-3% | 20 |
| 3.1-10% | 30 |
| 10.1%+ | 40 |

### Variable Parameters

Variable parameters are used to evaluate a user's ongoing engagement with your project:

#### Early Join Bonus

- **Apply Early Join Bonus**: Whether to give a bonus to users who join early
- **Early Join Bonus Score**: The score to add for early joiners

#### Engagement Weights

You can assign weights to different types of engagement:

- **Active Days Weight**: Weight for the number of days a user is active (0-1)
- **Project Interactions Weight**: Weight for interactions with your project's posts (0-1)
- **Content Production Weight**: Weight for content produced about your project (0-1)
- **Content Quality Weight**: Weight for the quality of content produced (0-1)
- **Referrals Weight**: Weight for referring new users to your project (0-1)

### Configuring Analysis Parameters

To configure Twitter analysis parameters:

1. Go to your campaign page
2. Click on "Settings" tab
3. Click on "Twitter Analysis" tab
4. Configure the fixed and variable parameters
5. Click "Save Changes" to update your campaign

## NFT Classification

The Social NFT Platform classifies NFTs into three types based on the user's engagement score: Common, Rare, and Legendary. You can configure the score thresholds for each type.

### NFT Types

- **Common**: Basic NFTs for users with low engagement scores
- **Rare**: Intermediate NFTs for users with moderate engagement scores
- **Legendary**: Premium NFTs for users with high engagement scores

### Score Thresholds

You can define the score thresholds for each NFT type:

| NFT Type | Score Range |
|----------|-------------|
| Common | 0-50 |
| Rare | 51-80 |
| Legendary | 81+ |

### NFT Evolution

NFTs can evolve over time based on the user's ongoing engagement with your project. The evolution process works as follows:

1. A user joins your campaign and receives an initial NFT based on their Twitter profile analysis
2. The user engages with your project on Twitter (likes, retweets, comments, mentions)
3. The platform periodically analyzes the user's engagement and updates their score
4. If the user's score crosses a threshold, their NFT evolves to a higher type
5. If the user's engagement decreases, their NFT can devolve to a lower type

### Configuring NFT Classification

To configure NFT classification:

1. Go to your campaign page
2. Click on "Settings" tab
3. Click on "NFT Classification" tab
4. Configure the score thresholds for each NFT type
5. Configure the evolution settings:
   - **Maximum Evolution Stages**: The maximum number of evolution stages
   - **Auto Evolve**: Whether to automatically evolve NFTs when thresholds are met
   - **Check Interval**: How often to check for evolution
   - **Notify Owner**: Whether to notify the owner when their NFT evolves
   - **Require Owner Approval**: Whether to require owner approval for evolution
   - **Update Metadata On Chain**: Whether to update the NFT metadata on the blockchain
6. Click "Save Changes" to update your campaign

## Monitoring Participants

The Social NFT Platform provides tools to monitor participant engagement and NFT evolution.

### Participants List

The participants list shows all users who have joined your campaign:

1. Go to your campaign page
2. Click on "Participants" tab
3. View the list of participants, including:
   - Twitter username
   - NFT type
   - Current score
   - Status
   - Join date

You can filter the list by:
- NFT type
- Score range
- Status
- Join date

### Top Participants

The top participants section shows the users with the highest engagement scores:

1. Go to your campaign page
2. Click on "Participants" tab
3. Click on "Top Participants" tab
4. View the list of top participants, including:
   - Twitter username
   - NFT type
   - Current score
   - Status
   - Rank change

### Participant Details

To view detailed information about a participant:

1. Go to your campaign page
2. Click on "Participants" tab
3. Click on a participant's username
4. View the participant details, including:
   - Twitter profile information
   - Engagement metrics
   - NFT details
   - Score history
   - Activity log

### Managing Participants

You can manage participants from the participant details page:

- **Update Status**: Change the participant's status (active, inactive, banned)
- **Manually Update Score**: Override the participant's score
- **Manually Update NFT Type**: Override the participant's NFT type
- **View Twitter Profile**: Open the participant's Twitter profile
- **Send Message**: Send a message to the participant

## Best Practices

Here are some best practices to help you make the most of the Social NFT Platform:

### Campaign Planning

- **Define Clear Goals**: Clearly define what you want to achieve with your campaign
- **Target Audience**: Identify your target audience and tailor your campaign to them
- **Timeline**: Plan your campaign timeline, including start and end dates
- **Rewards**: Define meaningful rewards that will motivate users to participate
- **Promotion**: Plan how you will promote your campaign on social media and other channels

### Twitter Analysis Configuration

- **Balance Fixed and Variable Parameters**: Ensure a good balance between fixed and variable parameters
- **Adjust Weights**: Adjust weights based on what's most important for your project
- **Test Different Configurations**: Test different configurations to find what works best
- **Regular Updates**: Regularly update your configuration based on campaign performance

### NFT Classification

- **Clear Thresholds**: Set clear thresholds for each NFT type
- **Achievable Goals**: Make sure the thresholds are achievable for your target audience
- **Evolution Path**: Create a clear evolution path for NFTs
- **Visual Differentiation**: Ensure each NFT type is visually distinct

### Participant Engagement

- **Regular Communication**: Regularly communicate with your participants
- **Highlight Top Performers**: Highlight top performers to motivate others
- **Provide Feedback**: Provide feedback to participants on how they can improve
- **Address Issues Quickly**: Address any issues or concerns quickly

### Campaign Monitoring

- **Regular Check-ins**: Regularly check your campaign dashboard
- **Analyze Metrics**: Analyze key metrics to understand campaign performance
- **Adjust as Needed**: Make adjustments to your campaign based on performance
- **Document Learnings**: Document what works and what doesn't for future campaigns

### Technical Considerations

- **Test on Multiple Networks**: Test your NFT contracts on multiple networks
- **Gas Fees**: Consider gas fees when choosing a network
- **Metadata Storage**: Ensure your NFT metadata is stored securely
- **Smart Contract Security**: Ensure your smart contracts are secure and audited
