import { Injectable, Logger, UnauthorizedException, ConflictException, BadRequestException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../common/services/cache.service';
import { AuditService } from '../../common/services/audit.service';
import { UserCommandService } from './user-command.service';
import { 
  LoginDto, 
  RegisterDto, 
  RefreshTokenDto, 
  LogoutDto, 
  ChangePasswordDto, 
  ForgotPasswordDto, 
  ResetPasswordDto,
  SocialAuthDto,
  AuthResponseDto,
  SocialProvider 
} from '../dto/auth.dto';
import { RequestContext } from '../interfaces/request-context.interface';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

export interface AuthResult {
  success: boolean;
  data?: AuthResponseDto;
  error?: string;
  message?: string;
}

export interface TokenPayload {
  sub: string; // User ID
  username: string;
  email: string;
  role: string;
  sessionId: string;
  iat?: number;
  exp?: number;
}

export interface SessionData {
  userId: string;
  sessionId: string;
  deviceInfo?: string;
  rememberMe: boolean;
  createdAt: Date;
  expiresAt: Date;
  isActive: boolean;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly saltRounds = 12;
  private readonly accessTokenExpiry = '15m'; // 15 minutes
  private readonly refreshTokenExpiry = '7d'; // 7 days
  private readonly rememberMeTokenExpiry = '30d'; // 30 days

  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
    private readonly auditService: AuditService,
    private readonly userCommandService: UserCommandService,
  ) {}

  /**
   * Register a new user
   */
  async register(registerDto: RegisterDto, context: RequestContext): Promise<AuthResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Registering new user: ${registerDto.email}`, { correlationId });

      // Validate password confirmation
      if (registerDto.password !== registerDto.confirmPassword) {
        throw new BadRequestException('Passwords do not match');
      }

      // Check if user already exists
      const existingUser = await this.prisma.userCommand.findFirst({
        where: {
          OR: [
            { email: registerDto.email },
            { username: registerDto.username },
          ],
        },
      });

      if (existingUser) {
        if (existingUser.email === registerDto.email) {
          throw new ConflictException('Email is already registered');
        }
        if (existingUser.username === registerDto.username) {
          throw new ConflictException('Username is already taken');
        }
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(registerDto.password, this.saltRounds);

      // Create user
      const createUserDto = {
        username: registerDto.username,
        email: registerDto.email,
        password: hashedPassword,
        displayName: registerDto.displayName || registerDto.username,
      };

      const userResult = await this.userCommandService.createUser(createUserDto, context);
      
      if (!userResult.success) {
        throw new BadRequestException('Failed to create user');
      }

      const user = userResult.data;

      // Create session
      const sessionId = uuidv4();
      const session = await this.createUserSession(user.id, sessionId, registerDto.deviceInfo, false, context);

      // Generate tokens
      const tokens = await this.generateTokens(user, sessionId);

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: user.id,
        action: 'CREATE',
        resource: 'auth',
        newValues: { email: user.email, username: user.username },
        userId: user.id,
        correlationId: context.correlationId,
        metadata: { 
          action: 'user_registration',
          deviceInfo: registerDto.deviceInfo,
          sessionId 
        },
      });

      this.logger.log(`User registered successfully: ${registerDto.email}`, { 
        correlationId,
        userId: user.id,
        duration: Date.now() - startTime 
      });

      return {
        success: true,
        data: {
          ...tokens,
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            displayName: user.displayName,
            role: user.role,
            isEmailVerified: user.isEmailVerified,
            isProfileComplete: user.isProfileComplete || false,
          },
          session: {
            id: sessionId,
            deviceInfo: registerDto.deviceInfo,
            createdAt: session.createdAt.toISOString(),
            expiresAt: session.expiresAt.toISOString(),
          },
        },
      };

    } catch (error) {
      this.logger.error(`Registration failed for ${registerDto.email}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });

      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }

      return { success: false, error: 'Registration failed' };
    }
  }

  /**
   * Login user with email/username and password
   */
  async login(loginDto: LoginDto, context: RequestContext): Promise<AuthResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Login attempt for: ${loginDto.emailOrUsername}`, { correlationId });

      // Find user by email or username
      const user = await this.prisma.userCommand.findFirst({
        where: {
          OR: [
            { email: loginDto.emailOrUsername },
            { username: loginDto.emailOrUsername },
          ],
        },
      });

      if (!user) {
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check if account is active
      if (!user.isActive) {
        throw new UnauthorizedException('Account is deactivated');
      }

      // Check if account is locked
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        throw new UnauthorizedException('Account is temporarily locked');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(loginDto.password, user.password);
      
      if (!isPasswordValid) {
        // Increment failed login attempts
        await this.handleFailedLogin(user.id);
        throw new UnauthorizedException('Invalid credentials');
      }

      // Reset failed login attempts on successful login
      await this.resetFailedLoginAttempts(user.id);

      // Create session
      const sessionId = uuidv4();
      const session = await this.createUserSession(
        user.id, 
        sessionId, 
        loginDto.deviceInfo, 
        loginDto.rememberMe || false, 
        context
      );

      // Generate tokens
      const tokens = await this.generateTokens(user, sessionId, loginDto.rememberMe);

      // Update last login
      await this.prisma.userCommand.update({
        where: { id: user.id },
        data: { 
          lastLoginAt: new Date(),
          updatedBy: user.id,
          version: { increment: 1 },
        },
      });

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: user.id,
        action: 'READ',
        resource: 'auth',
        userId: user.id,
        correlationId: context.correlationId,
        metadata: { 
          action: 'user_login',
          deviceInfo: loginDto.deviceInfo,
          sessionId,
          rememberMe: loginDto.rememberMe 
        },
      });

      this.logger.log(`Login successful for: ${loginDto.emailOrUsername}`, { 
        correlationId,
        userId: user.id,
        duration: Date.now() - startTime 
      });

      return {
        success: true,
        data: {
          ...tokens,
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            displayName: user.displayName,
            role: user.role,
            isEmailVerified: user.isEmailVerified,
            isProfileComplete: user.isProfileComplete || false,
          },
          session: {
            id: sessionId,
            deviceInfo: loginDto.deviceInfo,
            createdAt: session.createdAt.toISOString(),
            expiresAt: session.expiresAt.toISOString(),
          },
        },
      };

    } catch (error) {
      this.logger.error(`Login failed for ${loginDto.emailOrUsername}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });

      if (error instanceof UnauthorizedException) {
        throw error;
      }

      return { success: false, error: 'Login failed' };
    }
  }

  /**
   * ✅ ENHANCED: Validate JWT access token
   */
  async validateToken(token: string, context: any): Promise<AuthResult> {
    const correlationId = context?.correlationId || 'unknown';
    const startTime = Date.now();

    try {
      this.logger.log(`Token validation attempt`, { correlationId });

      if (!token) {
        return {
          success: false,
          error: 'Token is required'
        };
      }

      // Verify and decode JWT token
      let decoded: TokenPayload;
      try {
        decoded = this.jwtService.verify(token) as TokenPayload;
      } catch (jwtError) {
        this.logger.warn(`Invalid JWT token: ${jwtError.message}`, { correlationId });
        return {
          success: false,
          error: 'Invalid or expired token'
        };
      }

      // Check if user exists and is active
      const user = await this.prisma.userCommand.findUnique({
        where: { id: decoded.sub },
      });

      if (!user) {
        this.logger.warn(`User not found for token: ${decoded.sub}`, { correlationId });
        return {
          success: false,
          error: 'User not found'
        };
      }

      if (!user.isActive) {
        this.logger.warn(`Inactive user attempted token validation: ${decoded.sub}`, { correlationId });
        return {
          success: false,
          error: 'Account is deactivated'
        };
      }

      // Check if session is still valid (if sessionId is present)
      if (decoded.sessionId) {
        // ✅ FIXED: Use cache-based session validation instead of database
        const sessionKey = `session:${decoded.sessionId}`;
        const sessionData = await this.cacheService.get<SessionData>(sessionKey);

        if (!sessionData || !sessionData.isActive) {
          this.logger.warn(`Invalid or expired session: ${decoded.sessionId}`, { correlationId });
          return {
            success: false,
            error: 'Session expired'
          };
        }
      }

      this.logger.log(`Token validation successful for user: ${user.username}`, {
        correlationId,
        userId: user.id,
        duration: Date.now() - startTime
      });

      return {
        success: true,
        data: {
          accessToken: token,
          refreshToken: '', // Not needed for validation
          tokenType: 'Bearer',
          expiresIn: 0, // Not needed for validation
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            displayName: user.displayName,
            role: user.role,
            isEmailVerified: user.isEmailVerified,
            isProfileComplete: user.isProfileComplete || false,
          },
          session: decoded.sessionId ? {
            id: decoded.sessionId,
            deviceInfo: '',
            createdAt: new Date().toISOString(),
            expiresAt: new Date().toISOString(),
          } : undefined,
        },
      };

    } catch (error) {
      this.logger.error(`Token validation failed: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });

      return {
        success: false,
        error: 'Token validation failed'
      };
    }
  }

  /**
   * Generate access and refresh tokens
   */
  private async generateTokens(user: any, sessionId: string, rememberMe: boolean = false): Promise<{
    accessToken: string;
    refreshToken: string;
    tokenType: string;
    expiresIn: number;
  }> {
    const payload: TokenPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      sessionId,
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: this.accessTokenExpiry,
    });

    const refreshTokenExpiry = rememberMe ? this.rememberMeTokenExpiry : this.refreshTokenExpiry;
    const refreshToken = this.jwtService.sign(
      { ...payload, type: 'refresh' },
      { expiresIn: refreshTokenExpiry }
    );

    // Store refresh token in cache
    const refreshTokenKey = `refresh_token:${sessionId}`;
    const refreshTokenTTL = rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60; // seconds
    await this.cacheService.set(refreshTokenKey, refreshToken, refreshTokenTTL);

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: 15 * 60, // 15 minutes in seconds
    };
  }

  /**
   * Create user session
   */
  private async createUserSession(
    userId: string,
    sessionId: string,
    deviceInfo?: string,
    rememberMe: boolean = false,
    context?: RequestContext
  ): Promise<SessionData> {
    const expiresAt = new Date();
    if (rememberMe) {
      expiresAt.setDate(expiresAt.getDate() + 30); // 30 days
    } else {
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days
    }

    // Store session in cache
    const sessionKey = `session:${sessionId}`;
    const sessionData = {
      userId,
      sessionId,
      deviceInfo,
      rememberMe,
      createdAt: new Date(),
      expiresAt,
      isActive: true,
    };

    const sessionTTL = rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60; // seconds
    await this.cacheService.set(sessionKey, sessionData, sessionTTL);

    return sessionData;
  }

  /**
   * Handle failed login attempts
   */
  private async handleFailedLogin(userId: string): Promise<void> {
    const user = await this.prisma.userCommand.findUnique({ where: { id: userId } });
    if (!user) return;

    const failedAttempts = user.failedLoginAttempts + 1;
    const updateData: any = {
      failedLoginAttempts: failedAttempts,
      updatedBy: userId,
      version: { increment: 1 },
    };

    // Lock account after 5 failed attempts for 15 minutes
    if (failedAttempts >= 5) {
      const lockUntil = new Date();
      lockUntil.setMinutes(lockUntil.getMinutes() + 15);
      updateData.lockedUntil = lockUntil;
    }

    await this.prisma.userCommand.update({
      where: { id: userId },
      data: updateData,
    });
  }

  /**
   * Reset failed login attempts
   */
  private async resetFailedLoginAttempts(userId: string): Promise<void> {
    await this.prisma.userCommand.update({
      where: { id: userId },
      data: {
        failedLoginAttempts: 0,
        lockedUntil: null,
        updatedBy: userId,
        version: { increment: 1 },
      },
    });
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshTokenDto: RefreshTokenDto, context: RequestContext): Promise<AuthResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log('Refreshing access token', { correlationId });

      // Verify refresh token
      const decoded = this.jwtService.verify(refreshTokenDto.refreshToken) as TokenPayload & { type: string };

      if (decoded.type !== 'refresh') {
        throw new UnauthorizedException('Invalid token type');
      }

      // Check if refresh token exists in cache
      const refreshTokenKey = `refresh_token:${decoded.sessionId}`;
      const storedToken = await this.cacheService.get(refreshTokenKey);

      if (!storedToken || storedToken !== refreshTokenDto.refreshToken) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Check if session is still active
      const sessionKey = `session:${decoded.sessionId}`;
      const session = await this.cacheService.get<SessionData>(sessionKey);

      if (!session || !session.isActive) {
        throw new UnauthorizedException('Session expired');
      }

      // Get user
      const user = await this.prisma.userCommand.findUnique({ where: { id: decoded.sub } });
      if (!user || !user.isActive) {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user, decoded.sessionId, session.rememberMe);

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: user.id,
        action: 'READ',
        resource: 'auth',
        userId: user.id,
        correlationId: context.correlationId,
        metadata: {
          action: 'token_refresh',
          sessionId: decoded.sessionId
        },
      });

      this.logger.log('Token refreshed successfully', {
        correlationId,
        userId: user.id,
        duration: Date.now() - startTime
      });

      return {
        success: true,
        data: {
          ...tokens,
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            displayName: user.displayName,
            role: user.role,
            isEmailVerified: user.isEmailVerified,
            isProfileComplete: user.isProfileComplete || false,
          },
        },
      };

    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });

      if (error instanceof UnauthorizedException) {
        throw error;
      }

      return { success: false, error: 'Token refresh failed' };
    }
  }

  /**
   * Logout user and invalidate session
   */
  async logout(logoutDto: LogoutDto, userId: string, sessionId: string, context: RequestContext): Promise<AuthResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Logging out user: ${userId}`, { correlationId });

      if (logoutDto.logoutFromAllDevices) {
        // Invalidate all sessions for the user
        await this.invalidateAllUserSessions(userId);
      } else {
        // Invalidate current session only
        await this.invalidateSession(sessionId);
      }

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: userId,
        action: 'READ',
        resource: 'auth',
        userId: userId,
        correlationId: context.correlationId,
        metadata: {
          action: 'user_logout',
          sessionId,
          logoutFromAllDevices: logoutDto.logoutFromAllDevices
        },
      });

      this.logger.log(`Logout successful for user: ${userId}`, {
        correlationId,
        userId,
        duration: Date.now() - startTime
      });

      return {
        success: true,
        message: logoutDto.logoutFromAllDevices ?
          'Logged out from all devices successfully' :
          'Logged out successfully'
      };

    } catch (error) {
      this.logger.error(`Logout failed for user ${userId}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });

      return { success: false, error: 'Logout failed' };
    }
  }

  /**
   * Change user password
   */
  async changePassword(changePasswordDto: ChangePasswordDto, userId: string, context: RequestContext): Promise<AuthResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Changing password for user: ${userId}`, { correlationId });

      // Validate password confirmation
      if (changePasswordDto.newPassword !== changePasswordDto.confirmNewPassword) {
        throw new BadRequestException('New passwords do not match');
      }

      // Get user
      const user = await this.prisma.userCommand.findUnique({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new UnauthorizedException('Current password is incorrect');
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, this.saltRounds);

      // Update password
      await this.prisma.userCommand.update({
        where: { id: userId },
        data: {
          password: hashedNewPassword,
          passwordChangedAt: new Date(),
          updatedBy: userId,
          version: { increment: 1 },
        },
      });

      // Invalidate all sessions except current one (force re-login on other devices)
      await this.invalidateAllUserSessionsExcept(userId, context.sessionId);

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: userId,
        action: 'UPDATE',
        resource: 'auth',
        userId: userId,
        correlationId: context.correlationId,
        metadata: { action: 'password_change' },
      });

      this.logger.log(`Password changed successfully for user: ${userId}`, {
        correlationId,
        userId,
        duration: Date.now() - startTime
      });

      return {
        success: true,
        message: 'Password changed successfully'
      };

    } catch (error) {
      this.logger.error(`Password change failed for user ${userId}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });

      if (error instanceof BadRequestException || error instanceof UnauthorizedException || error instanceof NotFoundException) {
        throw error;
      }

      return { success: false, error: 'Password change failed' };
    }
  }

  /**
   * Invalidate a specific session
   */
  private async invalidateSession(sessionId: string): Promise<void> {
    const sessionKey = `session:${sessionId}`;
    const refreshTokenKey = `refresh_token:${sessionId}`;

    await Promise.all([
      this.cacheService.del(sessionKey),
      this.cacheService.del(refreshTokenKey),
    ]);
  }

  /**
   * Invalidate all sessions for a user
   */
  private async invalidateAllUserSessions(userId: string): Promise<void> {
    // This is a simplified implementation
    // In production, you'd want to track sessions per user more efficiently
    // For now, we'll just log this action
    this.logger.warn(`Invalidating all sessions for user ${userId} - simplified implementation`);
    // TODO: Implement proper session tracking per user
  }

  /**
   * Invalidate all sessions for a user except the current one
   */
  private async invalidateAllUserSessionsExcept(userId: string, currentSessionId?: string): Promise<void> {
    // This is a simplified implementation
    // In production, you'd want to track sessions per user more efficiently
    if (currentSessionId) {
      // Keep current session, invalidate others
      // This would require more sophisticated session tracking
      this.logger.debug(`Keeping session ${currentSessionId} active for user ${userId}`);
    }
  }

}
