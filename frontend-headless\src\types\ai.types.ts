// AI & ML Enhancement Types for Social NFT Platform

export interface AIRecommendation {
  id: string
  type: RecommendationType
  title: string
  description: string
  confidence: number
  priority: RecommendationPriority
  category: RecommendationCategory
  targetUserId?: string
  targetNFTId?: string
  targetCommunityId?: string
  actionUrl?: string
  actionText?: string
  metadata: Record<string, any>
  createdAt: string
  expiresAt?: string
  isViewed: boolean
  isActioned: boolean
}

export enum RecommendationType {
  NFT_PURCHASE = 'nft_purchase',
  NFT_SELL = 'nft_sell',
  NFT_MINT = 'nft_mint',
  USER_FOLLOW = 'user_follow',
  COMMUNITY_JOIN = 'community_join',
  CONTENT_CREATE = 'content_create',
  CAMPAIGN_PARTICIPATE = 'campaign_participate',
  PRICE_OPTIMIZATION = 'price_optimization',
  PORTFOLIO_REBALANCE = 'portfolio_rebalance',
  SOCIAL_ENGAGEMENT = 'social_engagement'
}

export enum RecommendationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export enum RecommendationCategory {
  TRADING = 'trading',
  SOCIAL = 'social',
  CONTENT = 'content',
  PORTFOLIO = 'portfolio',
  COMMUNITY = 'community',
  PERSONALIZATION = 'personalization'
}

export interface PersonalizationProfile {
  userId: string
  preferences: UserPreferences
  behaviorPatterns: BehaviorPattern[]
  interests: Interest[]
  tradingStyle: TradingStyle
  socialActivity: SocialActivityPattern
  contentPreferences: ContentPreferences
  riskProfile: RiskProfile
  lastUpdated: string
  confidenceScore: number
}

export interface UserPreferences {
  nftCategories: string[]
  priceRanges: PriceRange[]
  blockchains: string[]
  artStyles: string[]
  rarityPreferences: string[]
  communityTypes: string[]
  notificationSettings: NotificationPreferences
  privacySettings: PrivacySettings
}

export interface BehaviorPattern {
  pattern: string
  frequency: number
  confidence: number
  timeOfDay: string[]
  daysOfWeek: string[]
  seasonality?: string
  triggers: string[]
  outcomes: string[]
}

export interface Interest {
  category: string
  subcategory?: string
  strength: number
  trending: boolean
  keywords: string[]
  relatedUsers: string[]
  relatedCommunities: string[]
  lastEngagement: string
}

export interface TradingStyle {
  type: TradingStyleType
  riskTolerance: RiskTolerance
  investmentHorizon: InvestmentHorizon
  preferredAssets: string[]
  averageTransactionSize: number
  tradingFrequency: TradingFrequency
  profitTargets: number[]
  stopLossLevels: number[]
}

export enum TradingStyleType {
  CONSERVATIVE = 'conservative',
  MODERATE = 'moderate',
  AGGRESSIVE = 'aggressive',
  SPECULATIVE = 'speculative'
}

export enum RiskTolerance {
  VERY_LOW = 'very_low',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  VERY_HIGH = 'very_high'
}

export enum InvestmentHorizon {
  SHORT_TERM = 'short_term',
  MEDIUM_TERM = 'medium_term',
  LONG_TERM = 'long_term'
}

export enum TradingFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly'
}

export interface SocialActivityPattern {
  postingFrequency: number
  engagementRate: number
  preferredContentTypes: string[]
  activeHours: string[]
  communityParticipation: number
  influenceScore: number
  networkSize: number
  interactionStyle: InteractionStyle
}

export enum InteractionStyle {
  LURKER = 'lurker',
  CASUAL = 'casual',
  ACTIVE = 'active',
  INFLUENCER = 'influencer',
  THOUGHT_LEADER = 'thought_leader'
}

export interface ContentPreferences {
  formats: ContentFormat[]
  topics: string[]
  complexity: ContentComplexity
  length: ContentLength
  visualStyle: VisualStyle[]
  languages: string[]
  sources: string[]
}

export enum ContentFormat {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  INTERACTIVE = 'interactive'
}

export enum ContentComplexity {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

export enum ContentLength {
  SHORT = 'short',
  MEDIUM = 'medium',
  LONG = 'long'
}

export enum VisualStyle {
  MINIMALIST = 'minimalist',
  COLORFUL = 'colorful',
  DARK = 'dark',
  LIGHT = 'light',
  ARTISTIC = 'artistic'
}

export interface RiskProfile {
  overallRisk: RiskTolerance
  portfolioVolatility: number
  maxDrawdown: number
  diversificationLevel: number
  leverageUsage: number
  riskMetrics: RiskMetric[]
  riskFactors: RiskFactor[]
}

export interface RiskMetric {
  name: string
  value: number
  threshold: number
  status: RiskStatus
  trend: TrendDirection
}

export enum RiskStatus {
  LOW = 'low',
  MODERATE = 'moderate',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum TrendDirection {
  INCREASING = 'increasing',
  DECREASING = 'decreasing',
  STABLE = 'stable'
}

export interface RiskFactor {
  factor: string
  impact: number
  probability: number
  mitigation: string[]
}

export interface PriceRange {
  min: number
  max: number
  currency: string
  preferred: boolean
}

export interface NotificationPreferences {
  email: boolean
  push: boolean
  sms: boolean
  inApp: boolean
  frequency: NotificationFrequency
  types: string[]
}

export enum NotificationFrequency {
  IMMEDIATE = 'immediate',
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly'
}

export interface PrivacySettings {
  profileVisibility: VisibilityLevel
  activityVisibility: VisibilityLevel
  portfolioVisibility: VisibilityLevel
  dataSharing: boolean
  analyticsOptIn: boolean
  marketingOptIn: boolean
}

export enum VisibilityLevel {
  PUBLIC = 'public',
  FRIENDS = 'friends',
  PRIVATE = 'private'
}

export interface PredictiveAnalytics {
  id: string
  type: PredictionType
  target: string
  prediction: PredictionResult
  confidence: number
  timeframe: string
  factors: PredictionFactor[]
  historicalAccuracy: number
  createdAt: string
  validUntil: string
}

export enum PredictionType {
  PRICE_MOVEMENT = 'price_movement',
  MARKET_TREND = 'market_trend',
  USER_BEHAVIOR = 'user_behavior',
  ENGAGEMENT_RATE = 'engagement_rate',
  PORTFOLIO_PERFORMANCE = 'portfolio_performance',
  COMMUNITY_GROWTH = 'community_growth',
  NFT_POPULARITY = 'nft_popularity'
}

export interface PredictionResult {
  value: number | string
  range?: {
    min: number
    max: number
  }
  probability: number
  scenarios: PredictionScenario[]
}

export interface PredictionScenario {
  name: string
  probability: number
  outcome: string
  impact: number
}

export interface PredictionFactor {
  name: string
  weight: number
  value: number
  impact: FactorImpact
  description: string
}

export enum FactorImpact {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
  NEUTRAL = 'neutral'
}

export interface AutomationRule {
  id: string
  name: string
  description: string
  type: AutomationType
  trigger: AutomationTrigger
  conditions: AutomationCondition[]
  actions: AutomationAction[]
  isActive: boolean
  priority: number
  executionCount: number
  lastExecuted?: string
  createdAt: string
  updatedAt: string
}

export enum AutomationType {
  TRADING = 'trading',
  SOCIAL = 'social',
  CONTENT = 'content',
  NOTIFICATION = 'notification',
  PORTFOLIO = 'portfolio'
}

export interface AutomationTrigger {
  type: TriggerType
  parameters: Record<string, any>
  schedule?: string
}

export enum TriggerType {
  PRICE_CHANGE = 'price_change',
  TIME_BASED = 'time_based',
  EVENT_BASED = 'event_based',
  THRESHOLD_REACHED = 'threshold_reached',
  USER_ACTION = 'user_action',
  MARKET_CONDITION = 'market_condition'
}

export interface AutomationCondition {
  field: string
  operator: ConditionOperator
  value: any
  logicalOperator?: LogicalOperator
}

export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  CONTAINS = 'contains',
  IN = 'in',
  NOT_IN = 'not_in'
}

export enum LogicalOperator {
  AND = 'and',
  OR = 'or',
  NOT = 'not'
}

export interface AutomationAction {
  type: ActionType
  parameters: Record<string, any>
  delay?: number
  retryPolicy?: RetryPolicy
}

export enum ActionType {
  SEND_NOTIFICATION = 'send_notification',
  CREATE_ORDER = 'create_order',
  CANCEL_ORDER = 'cancel_order',
  UPDATE_PORTFOLIO = 'update_portfolio',
  POST_CONTENT = 'post_content',
  JOIN_COMMUNITY = 'join_community',
  FOLLOW_USER = 'follow_user'
}

export interface RetryPolicy {
  maxRetries: number
  retryDelay: number
  backoffMultiplier: number
}

export interface MLModel {
  id: string
  name: string
  type: ModelType
  version: string
  status: ModelStatus
  accuracy: number
  trainingData: TrainingDataInfo
  features: ModelFeature[]
  hyperparameters: Record<string, any>
  metrics: ModelMetrics
  createdAt: string
  lastTrained: string
  nextTraining?: string
}

export enum ModelType {
  RECOMMENDATION = 'recommendation',
  PREDICTION = 'prediction',
  CLASSIFICATION = 'classification',
  CLUSTERING = 'clustering',
  ANOMALY_DETECTION = 'anomaly_detection'
}

export enum ModelStatus {
  TRAINING = 'training',
  ACTIVE = 'active',
  DEPRECATED = 'deprecated',
  FAILED = 'failed'
}

export interface TrainingDataInfo {
  size: number
  features: number
  timeRange: {
    start: string
    end: string
  }
  quality: DataQuality
}

export enum DataQuality {
  POOR = 'poor',
  FAIR = 'fair',
  GOOD = 'good',
  EXCELLENT = 'excellent'
}

export interface ModelFeature {
  name: string
  type: FeatureType
  importance: number
  description: string
}

export enum FeatureType {
  NUMERICAL = 'numerical',
  CATEGORICAL = 'categorical',
  TEXT = 'text',
  TEMPORAL = 'temporal'
}

export interface ModelMetrics {
  accuracy: number
  precision: number
  recall: number
  f1Score: number
  auc: number
  rmse?: number
  mae?: number
  customMetrics: Record<string, number>
}

// Request/Response Types
export interface GetRecommendationsRequest {
  userId: string
  types?: RecommendationType[]
  categories?: RecommendationCategory[]
  limit?: number
  offset?: number
}

export interface GetRecommendationsResponse {
  recommendations: AIRecommendation[]
  totalCount: number
  hasMore: boolean
}

export interface UpdatePersonalizationRequest {
  userId: string
  preferences?: Partial<UserPreferences>
  behaviorData?: Record<string, any>
}

export interface GetPredictionsRequest {
  types?: PredictionType[]
  targets?: string[]
  timeframe?: string
  limit?: number
}

export interface GetPredictionsResponse {
  predictions: PredictiveAnalytics[]
  totalCount: number
}

export interface CreateAutomationRequest {
  name: string
  description: string
  type: AutomationType
  trigger: AutomationTrigger
  conditions: AutomationCondition[]
  actions: AutomationAction[]
  priority?: number
}

export interface UpdateAutomationRequest {
  id: string
  name?: string
  description?: string
  trigger?: AutomationTrigger
  conditions?: AutomationCondition[]
  actions?: AutomationAction[]
  isActive?: boolean
  priority?: number
}
