'use client'

import React, { useState } from 'react'
import {
  CogIcon,
  PlusIcon,
  PlayIcon,
  PauseIcon,
  TrashIcon,
  PencilIcon,
  ClockIcon,
  BoltIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  XMarkIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  DocumentTextIcon,
  BellIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import {
  useAutomationRules,
  useCreateAutomationRule,
  useUpdateAutomationRule,
  useDeleteAutomationRule,
  useToggleAutomationRule,
  useAutomationExecutionHistory
} from '@/hooks/useAI'
import {
  AutomationRule,
  AutomationType,
  TriggerType,
  ActionType,
  ConditionOperator,
  LogicalOperator,
  CreateAutomationRequest
} from '@/types/ai.types'
import CreateAutomationModal from './CreateAutomationModal'

interface AutomationManagerProps {
  userId: string
  className?: string
}

export default function AutomationManager({
  userId,
  className = ''
}: AutomationManagerProps) {
  const [selectedType, setSelectedType] = useState<AutomationType | 'all'>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedRule, setSelectedRule] = useState<AutomationRule | null>(null)
  const [showHistory, setShowHistory] = useState<string | null>(null)

  const { data: rules, isLoading } = useAutomationRules(userId, selectedType === 'all' ? undefined : selectedType)
  const { data: executionHistory } = useAutomationExecutionHistory(
    userId,
    showHistory || '',
    20
  )

  const createRuleMutation = useCreateAutomationRule()
  const updateRuleMutation = useUpdateAutomationRule()
  const deleteRuleMutation = useDeleteAutomationRule()
  const toggleRuleMutation = useToggleAutomationRule()

  const automationTypes = [
    { value: 'all', label: 'All Types' },
    { value: AutomationType.TRADING, label: 'Trading' },
    { value: AutomationType.SOCIAL, label: 'Social' },
    { value: AutomationType.CONTENT, label: 'Content' },
    { value: AutomationType.NOTIFICATION, label: 'Notifications' },
    { value: AutomationType.PORTFOLIO, label: 'Portfolio' }
  ]

  const handleToggleRule = (ruleId: string, isActive: boolean) => {
    toggleRuleMutation.mutate({ userId, ruleId, isActive: !isActive })
  }

  const handleDeleteRule = (ruleId: string) => {
    if (confirm('Are you sure you want to delete this automation rule?')) {
      deleteRuleMutation.mutate({ userId, ruleId })
    }
  }

  const handleCreateRule = (request: CreateAutomationRequest) => {
    createRuleMutation.mutate({ userId, request })
    setShowCreateModal(false)
  }

  const getTypeIcon = (type: AutomationType) => {
    switch (type) {
      case AutomationType.TRADING:
        return <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
      case AutomationType.SOCIAL:
        return <UserGroupIcon className="h-5 w-5 text-blue-600" />
      case AutomationType.CONTENT:
        return <DocumentTextIcon className="h-5 w-5 text-purple-600" />
      case AutomationType.NOTIFICATION:
        return <BellIcon className="h-5 w-5 text-orange-600" />
      case AutomationType.PORTFOLIO:
        return <ChartBarIcon className="h-5 w-5 text-indigo-600" />
      default:
        return <CogIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusColor = (isActive: boolean, executionCount: number) => {
    if (!isActive) return 'text-gray-500 bg-gray-100'
    if (executionCount > 0) return 'text-green-600 bg-green-100'
    return 'text-blue-600 bg-blue-100'
  }

  const formatLastExecuted = (dateString?: string) => {
    if (!dateString) return 'Never'
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    return `${Math.floor(diffInHours / 24)}d ago`
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded-lg mb-4"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <CogIcon className="h-6 w-6 mr-2 text-blue-600" />
            Automation Manager
          </h2>
          <p className="text-sm text-gray-600">
            Create and manage automated actions and workflows
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Rule
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <CogIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{rules?.length || 0}</div>
              <div className="text-sm text-gray-600">Total Rules</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <PlayIcon className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">
                {rules?.filter(r => r.isActive).length || 0}
              </div>
              <div className="text-sm text-gray-600">Active Rules</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <BoltIcon className="h-8 w-8 text-orange-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">
                {rules?.reduce((sum, r) => sum + r.executionCount, 0) || 0}
              </div>
              <div className="text-sm text-gray-600">Total Executions</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">
                {rules?.filter(r => r.lastExecuted && new Date(r.lastExecuted) > new Date(Date.now() - 24 * 60 * 60 * 1000)).length || 0}
              </div>
              <div className="text-sm text-gray-600">Executed Today</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {automationTypes.map((type) => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>

        <div className="text-sm text-gray-500">
          {rules?.length || 0} automation rules
        </div>
      </div>

      {/* Rules List */}
      {rules && rules.length > 0 ? (
        <div className="space-y-4">
          {rules.map((rule) => (
            <AutomationRuleCard
              key={rule.id}
              rule={rule}
              onToggle={() => handleToggleRule(rule.id, rule.isActive)}
              onEdit={() => setSelectedRule(rule)}
              onDelete={() => handleDeleteRule(rule.id)}
              onViewHistory={() => setShowHistory(rule.id)}
              getTypeIcon={getTypeIcon}
              getStatusColor={getStatusColor}
              formatLastExecuted={formatLastExecuted}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No automation rules</h3>
          <p className="mt-1 text-sm text-gray-500">
            Create your first automation rule to get started with automated workflows.
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Create Rule
          </button>
        </div>
      )}

      {/* Execution History Modal */}
      {showHistory && executionHistory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Execution History</h3>
              <button
                onClick={() => setShowHistory(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
            
            <div className="p-6 max-h-96 overflow-y-auto">
              {executionHistory.length > 0 ? (
                <div className="space-y-3">
                  {executionHistory.map((execution, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <div className="flex items-center space-x-3">
                        {execution.status === 'success' ? (
                          <CheckCircleIcon className="h-5 w-5 text-green-600" />
                        ) : (
                          <XCircleIcon className="h-5 w-5 text-red-600" />
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {execution.action}
                          </div>
                          <div className="text-xs text-gray-600">
                            {new Date(execution.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-xs text-gray-500">
                        {execution.duration}ms
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <ClockIcon className="mx-auto h-8 w-8 mb-2" />
                  <p>No execution history available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Create Rule Modal */}
      {showCreateModal && (
        <CreateAutomationModal
          onClose={() => setShowCreateModal(false)}
          onCreateRule={handleCreateRule}
        />
      )}
    </div>
  )
}

interface AutomationRuleCardProps {
  rule: AutomationRule
  onToggle: () => void
  onEdit: () => void
  onDelete: () => void
  onViewHistory: () => void
  getTypeIcon: (type: AutomationType) => React.ReactNode
  getStatusColor: (isActive: boolean, executionCount: number) => string
  formatLastExecuted: (date?: string) => string
}

function AutomationRuleCard({
  rule,
  onToggle,
  onEdit,
  onDelete,
  onViewHistory,
  getTypeIcon,
  getStatusColor,
  formatLastExecuted
}: AutomationRuleCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex-shrink-0">
            {getTypeIcon(rule.type)}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-sm font-medium text-gray-900">{rule.name}</h3>
              
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                getStatusColor(rule.isActive, rule.executionCount)
              }`}>
                {rule.isActive ? 'Active' : 'Inactive'}
              </span>
              
              <span className="text-xs text-gray-500">
                Priority: {rule.priority}
              </span>
            </div>
            
            <p className="text-sm text-gray-700 mb-2">{rule.description}</p>
            
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <div className="flex items-center">
                <BoltIcon className="h-3 w-3 mr-1" />
                {rule.executionCount} executions
              </div>
              
              <div className="flex items-center">
                <ClockIcon className="h-3 w-3 mr-1" />
                Last: {formatLastExecuted(rule.lastExecuted)}
              </div>
              
              <span className="capitalize">{rule.type}</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 ml-4">
          <button
            onClick={onToggle}
            className={`p-2 rounded ${
              rule.isActive
                ? 'text-orange-600 hover:bg-orange-50'
                : 'text-green-600 hover:bg-green-50'
            }`}
            title={rule.isActive ? 'Pause' : 'Activate'}
          >
            {rule.isActive ? (
              <PauseIcon className="h-4 w-4" />
            ) : (
              <PlayIcon className="h-4 w-4" />
            )}
          </button>
          
          <button
            onClick={onEdit}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          
          <button
            onClick={onViewHistory}
            className="p-2 text-purple-600 hover:bg-purple-50 rounded"
            title="View History"
          >
            <ClockIcon className="h-4 w-4" />
          </button>
          
          <button
            onClick={onDelete}
            className="p-2 text-red-600 hover:bg-red-50 rounded"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
          
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 text-gray-400 hover:bg-gray-50 rounded"
            title="Details"
          >
            <InformationCircleIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Expanded Details */}
      {isExpanded && (
        <div className="mt-4 pt-4 border-t border-gray-200 space-y-3">
          {/* Trigger */}
          <div>
            <h4 className="text-xs font-medium text-gray-900 mb-1">Trigger</h4>
            <div className="text-xs text-gray-700 bg-gray-50 rounded p-2">
              <div>Type: {rule.trigger.type}</div>
              {rule.trigger.schedule && <div>Schedule: {rule.trigger.schedule}</div>}
              {Object.keys(rule.trigger.parameters).length > 0 && (
                <div>Parameters: {JSON.stringify(rule.trigger.parameters)}</div>
              )}
            </div>
          </div>

          {/* Conditions */}
          {rule.conditions.length > 0 && (
            <div>
              <h4 className="text-xs font-medium text-gray-900 mb-1">Conditions</h4>
              <div className="space-y-1">
                {rule.conditions.map((condition, index) => (
                  <div key={index} className="text-xs text-gray-700 bg-gray-50 rounded p-2">
                    {condition.field} {condition.operator} {String(condition.value)}
                    {condition.logicalOperator && ` ${condition.logicalOperator}`}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div>
            <h4 className="text-xs font-medium text-gray-900 mb-1">Actions</h4>
            <div className="space-y-1">
              {rule.actions.map((action, index) => (
                <div key={index} className="text-xs text-gray-700 bg-gray-50 rounded p-2">
                  <div>Type: {action.type}</div>
                  {action.delay && <div>Delay: {action.delay}ms</div>}
                  {Object.keys(action.parameters).length > 0 && (
                    <div>Parameters: {JSON.stringify(action.parameters)}</div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Timestamps */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div>Created: {new Date(rule.createdAt).toLocaleDateString()}</div>
            <div>Updated: {new Date(rule.updatedAt).toLocaleDateString()}</div>
          </div>
        </div>
      )}
    </div>
  )
}
