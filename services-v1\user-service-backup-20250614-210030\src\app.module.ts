import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { APP_INTERCEPTOR, APP_GUARD } from '@nestjs/core';

// Core application
import { AppController } from './app.controller';
import { AppService } from './app.service';

// Configuration (Industry Standard - Service Local)
import { AppConfig } from './config/app.config';

// Authentication (Industry Standard - Service Local)
import { JwtAuthGuard } from './auth/jwt-auth.guard';

// Health Checks (Industry Standard - Service Local)
import { HealthModule } from './health/health.module';

// Database (Industry Standard - Service Local)
import { PrismaModule } from './prisma/prisma.module';

// Business Logic (Preserve Existing Business Logic)
import { UserModule } from './user/user.module';

// Response Formatting (Industry Standard - Service Local)
import { ResponseInterceptor } from './common/response.interceptor';

@Module({
  imports: [
    // Configuration - Simple and service-local (Industry Standard)
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env'],
      validate: (config) => AppConfig.validate(config),
    }),
    
    // JWT - Service-local configuration (Industry Standard)
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET || 'dev-jwt-secret-change-in-production',
      signOptions: { 
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      },
    }),

    // Database - Service-local (Industry Standard)
    PrismaModule,

    // Health Checks - Service-local (Industry Standard)
    HealthModule,
    
    // Business Logic - Preserve existing functionality
    UserModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    
    // Global response formatting (Industry Standard)
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
    
    // Global authentication (Industry Standard)
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
})
export class AppModule {}
