// Enterprise Notification Module - Enhanced with Notification Services
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { NotificationCommandController } from './controllers/notification-command.controller';
import { NotificationQueryController } from './controllers/notification-query.controller';
import { PrismaService } from './shared/prisma.service';

// Enhanced notification module
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    }),
    // Enhanced notification services
    NotificationModule,
  ],
  controllers: [
    NotificationCommandController,
    NotificationQueryController
  ],
  providers: [PrismaService],
  exports: [PrismaService]
})
export class EnterpriseModule {}
