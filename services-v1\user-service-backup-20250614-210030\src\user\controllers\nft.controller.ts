import { Controller, Get, Post, Put, Delete, Body, Param, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { NFTService } from '../services/nft.service';
import { 
  GenerateNFTDto, 
  UpdateNFTDto, 
  MintNFTDto, 
  TransferNFTDto,
  NFTResponseDto,
  NFTRarity,
  NFTStatus,
  BlockchainNetwork 
} from '../dto/nft.dto';
import { ErrorResponseDto } from '../../common/dto/error-response.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RBACGuard } from '../guards/rbac.guard';
import { 
  RequireNFTRead,
  RequireNFTWrite,
  RequireNFTDelete,
  RequireNFTAdmin,
  Authenticated 
} from '../decorators/permissions.decorator';

@ApiTags('NFT Management')
@Controller('nfts')
@UseGuards(JwtAuthGuard, RBACGuard)
@ApiBearerAuth()
export class NFTController {
  constructor(private readonly nftService: NFTService) {}

  @Post('generate')
  @RequireNFTWrite()
  @ApiOperation({ summary: 'Generate NFT for user based on engagement score' })
  @ApiBody({ type: GenerateNFTDto })
  @ApiResponse({ status: 201, description: 'NFT generation initiated successfully', type: NFTResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input data', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async generateNFT(@Body() generateNFTDto: GenerateNFTDto, @Req() request: any) {
    const result = await this.nftService.generateNFT(generateNFTDto, request.context);
    return result;
  }

  @Get()
  @RequireNFTRead()
  @ApiOperation({ summary: 'Get NFTs with filtering and pagination' })
  @ApiQuery({ name: 'userId', type: String, required: false, description: 'Filter by user ID' })
  @ApiQuery({ name: 'campaignId', type: String, required: false, description: 'Filter by campaign ID' })
  @ApiQuery({ name: 'rarity', enum: NFTRarity, required: false, description: 'Filter by NFT rarity' })
  @ApiQuery({ name: 'status', enum: NFTStatus, required: false, description: 'Filter by NFT status' })
  @ApiQuery({ name: 'blockchain', enum: BlockchainNetwork, required: false, description: 'Filter by blockchain' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiResponse({ status: 200, description: 'NFTs retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getNFTs(
    @Query('userId') userId: string | undefined,
    @Query('campaignId') campaignId: string | undefined,
    @Query('rarity') rarity: NFTRarity | undefined,
    @Query('status') status: NFTStatus | undefined,
    @Query('blockchain') blockchain: BlockchainNetwork | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Req() request: any
  ) {
    // TODO: Implement get NFTs with filtering
    return {
      success: true,
      message: 'Get NFTs with filtering not yet implemented',
      data: {
        nfts: [],
        pagination: {
          page: page || 1,
          limit: limit || 20,
          totalCount: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      },
    };
  }

  @Get(':id')
  @RequireNFTRead()
  @ApiOperation({ summary: 'Get NFT by ID' })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiResponse({ status: 200, description: 'NFT retrieved successfully', type: NFTResponseDto })
  @ApiResponse({ status: 404, description: 'NFT not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getNFTById(@Param('id') id: string, @Req() request: any) {
    const result = await this.nftService.getNFTById(id, request.context);
    return result;
  }

  @Put(':id')
  @RequireNFTWrite()
  @ApiOperation({ summary: 'Update NFT metadata and status' })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiBody({ type: UpdateNFTDto })
  @ApiResponse({ status: 200, description: 'NFT updated successfully', type: NFTResponseDto })
  @ApiResponse({ status: 404, description: 'NFT not found', type: ErrorResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input data', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async updateNFT(
    @Param('id') id: string,
    @Body() updateNFTDto: UpdateNFTDto,
    @Req() request: any
  ) {
    // TODO: Implement NFT update
    return {
      success: true,
      message: 'NFT update not yet implemented',
    };
  }

  @Delete(':id')
  @RequireNFTDelete()
  @ApiOperation({ summary: 'Delete NFT (soft delete)' })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiResponse({ status: 200, description: 'NFT deleted successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async deleteNFT(@Param('id') id: string, @Req() request: any) {
    // TODO: Implement NFT deletion
    return {
      success: true,
      message: 'NFT deletion not yet implemented',
    };
  }

  @Post(':id/mint')
  @RequireNFTWrite()
  @ApiOperation({ summary: 'Mint NFT on blockchain' })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiBody({ type: MintNFTDto })
  @ApiResponse({ status: 200, description: 'NFT minted successfully', type: NFTResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid minting request', type: ErrorResponseDto })
  @ApiResponse({ status: 404, description: 'NFT not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async mintNFT(@Param('id') id: string, @Body() mintNFTDto: MintNFTDto, @Req() request: any) {
    // Override NFT ID from URL parameter
    mintNFTDto.nftId = id;
    const result = await this.nftService.mintNFT(mintNFTDto, request.context);
    return result;
  }

  @Post(':id/transfer')
  @RequireNFTWrite()
  @ApiOperation({ summary: 'Transfer NFT to another wallet' })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiBody({ type: TransferNFTDto })
  @ApiResponse({ status: 200, description: 'NFT transfer initiated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid transfer request', type: ErrorResponseDto })
  @ApiResponse({ status: 404, description: 'NFT not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async transferNFT(@Param('id') id: string, @Body() transferNFTDto: TransferNFTDto, @Req() request: any) {
    // TODO: Implement NFT transfer
    return {
      success: true,
      message: 'NFT transfer not yet implemented',
    };
  }

  @Get(':id/metadata')
  @RequireNFTRead()
  @ApiOperation({ summary: 'Get NFT metadata in standard format' })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiResponse({ status: 200, description: 'NFT metadata retrieved successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getNFTMetadata(@Param('id') id: string, @Req() request: any) {
    const result = await this.nftService.getNFTById(id, request.context);
    
    if (!result.success) {
      return result;
    }

    // Return just the metadata in standard NFT format
    return {
      success: true,
      data: result.data.metadata,
    };
  }

  @Get('user/:userId')
  @RequireNFTRead()
  @ApiOperation({ summary: 'Get NFTs owned by specific user' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiQuery({ name: 'rarity', enum: NFTRarity, required: false, description: 'Filter by NFT rarity' })
  @ApiQuery({ name: 'status', enum: NFTStatus, required: false, description: 'Filter by NFT status' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiResponse({ status: 200, description: 'User NFTs retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getUserNFTs(
    @Param('userId') userId: string,
    @Query('rarity') rarity: NFTRarity | undefined,
    @Query('status') status: NFTStatus | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Req() request: any
  ) {
    // TODO: Implement get user NFTs
    return {
      success: true,
      message: 'Get user NFTs not yet implemented',
      data: {
        nfts: [],
        pagination: {
          page: page || 1,
          limit: limit || 20,
          totalCount: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      },
    };
  }

  @Get('campaign/:campaignId')
  @RequireNFTRead()
  @ApiOperation({ summary: 'Get NFTs generated from specific campaign' })
  @ApiParam({ name: 'campaignId', description: 'Campaign ID' })
  @ApiQuery({ name: 'rarity', enum: NFTRarity, required: false, description: 'Filter by NFT rarity' })
  @ApiQuery({ name: 'status', enum: NFTStatus, required: false, description: 'Filter by NFT status' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiResponse({ status: 200, description: 'Campaign NFTs retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getCampaignNFTs(
    @Param('campaignId') campaignId: string,
    @Query('rarity') rarity: NFTRarity | undefined,
    @Query('status') status: NFTStatus | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Req() request: any
  ) {
    // TODO: Implement get campaign NFTs
    return {
      success: true,
      message: 'Get campaign NFTs not yet implemented',
      data: {
        nfts: [],
        pagination: {
          page: page || 1,
          limit: limit || 20,
          totalCount: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      },
    };
  }

  @Get('my/collection')
  @Authenticated()
  @ApiOperation({ summary: 'Get current user NFT collection' })
  @ApiQuery({ name: 'rarity', enum: NFTRarity, required: false, description: 'Filter by NFT rarity' })
  @ApiQuery({ name: 'status', enum: NFTStatus, required: false, description: 'Filter by NFT status' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiResponse({ status: 200, description: 'User NFT collection retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async getMyNFTCollection(
    @Query('rarity') rarity: NFTRarity | undefined,
    @Query('status') status: NFTStatus | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Req() request: any
  ) {
    const userId = request.user.sub;
    
    // TODO: Implement get user NFT collection
    return {
      success: true,
      message: 'Get user NFT collection not yet implemented',
      data: {
        userId,
        nfts: [],
        stats: {
          totalNFTs: 0,
          rarityBreakdown: {
            common: 0,
            rare: 0,
            epic: 0,
            legendary: 0,
            mythic: 0,
          },
          totalValue: 0,
        },
        pagination: {
          page: page || 1,
          limit: limit || 20,
          totalCount: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      },
    };
  }

  @Get('analytics/rarity-distribution')
  @RequireNFTRead()
  @ApiOperation({ summary: 'Get NFT rarity distribution analytics' })
  @ApiQuery({ name: 'campaignId', type: String, required: false, description: 'Filter by campaign ID' })
  @ApiQuery({ name: 'timeframe', type: String, required: false, description: 'Time frame (7d, 30d, 90d, all)', enum: ['7d', '30d', '90d', 'all'] })
  @ApiResponse({ status: 200, description: 'NFT rarity distribution retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getNFTRarityDistribution(
    @Query('campaignId') campaignId: string | undefined,
    @Query('timeframe') timeframe: string | undefined,
    @Req() request: any
  ) {
    // TODO: Implement NFT rarity distribution analytics
    return {
      success: true,
      message: 'NFT rarity distribution analytics not yet implemented',
      data: {
        distribution: {
          common: { count: 0, percentage: 0 },
          rare: { count: 0, percentage: 0 },
          epic: { count: 0, percentage: 0 },
          legendary: { count: 0, percentage: 0 },
          mythic: { count: 0, percentage: 0 },
        },
        totalNFTs: 0,
        timeframe: timeframe || 'all',
        generatedAt: new Date().toISOString(),
      },
    };
  }
}
