#!/bin/bash

# Standardize Health Endpoints Across All Services
# This script ensures all services have consistent /api/health endpoints

set -e

echo "🏥 Standardizing Health Endpoints Across All Services"
echo "===================================================="

# Services that need standardization
SERVICES=(
    "profile-analysis-service"
    "blockchain-service" 
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Base directory
BASE_DIR="$(pwd)"

# Function to create standard health controller
create_health_controller() {
    local service=$1
    local service_dir="services/$service"
    
    echo "🏥 Creating standard health controller for $service..."
    
    # Create health directory
    mkdir -p "$service_dir/src/health"
    
    # Create health controller
    cat > "$service_dir/src/health/health.controller.ts" << 'EOF'
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthCheck, HealthCheckService } from '@nestjs/terminus';
import { DatabaseHealthService } from './database-health.service';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    private readonly databaseHealthService: DatabaseHealthService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get service health status' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  @ApiResponse({ status: 503, description: 'Service is unhealthy' })
  @HealthCheck()
  check() {
    return this.healthCheckService.check([
      () => this.databaseHealthService.isHealthy('database'),
    ]);
  }

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check' })
  @ApiResponse({ status: 200, description: 'Service is running' })
  simple() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'SERVICE_NAME',
      version: '1.0.0',
      uptime: process.uptime(),
      port: process.env.PORT || 'PORT_NUMBER',
      environment: process.env.NODE_ENV || 'development',
    };
  }
}
EOF

    # Replace placeholders
    sed -i "s/SERVICE_NAME/$service/g" "$service_dir/src/health/health.controller.ts"
    
    # Set port number based on service
    local port=""
    case $service in
        "profile-analysis-service") port="3002" ;;
        "blockchain-service") port="3004" ;;
        "project-service") port="3005" ;;
        "marketplace-service") port="3006" ;;
        "notification-service") port="3008" ;;
        "analytics-service") port="3009" ;;
        *) port="3000" ;;
    esac
    
    sed -i "s/PORT_NUMBER/$port/g" "$service_dir/src/health/health.controller.ts"
    
    echo "✅ Health controller created for $service"
}

# Function to create database health service
create_database_health_service() {
    local service=$1
    local service_dir="services/$service"
    
    echo "🗄️  Creating database health service for $service..."
    
    cat > "$service_dir/src/health/database-health.service.ts" << 'EOF'
import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { PrismaService } from '../enterprise/shared/prisma.service';

@Injectable()
export class DatabaseHealthService extends HealthIndicator {
  constructor(private readonly prisma: PrismaService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const startTime = Date.now();
      
      // Test database connection
      await this.prisma.$queryRaw`SELECT 1`;
      
      const responseTime = Date.now() - startTime;
      
      const result = this.getStatus(key, true, {
        database: 'postgresql',
        status: 'up',
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      const result = this.getStatus(key, false, {
        database: 'postgresql',
        status: 'down',
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      throw new HealthCheckError('Database health check failed', result);
    }
  }
}
EOF
    
    echo "✅ Database health service created for $service"
}

# Function to create health module
create_health_module() {
    local service=$1
    local service_dir="services/$service"
    
    echo "📦 Creating health module for $service..."
    
    cat > "$service_dir/src/health/health.module.ts" << 'EOF'
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HealthController } from './health.controller';
import { DatabaseHealthService } from './database-health.service';

@Module({
  imports: [TerminusModule],
  controllers: [HealthController],
  providers: [DatabaseHealthService],
  exports: [DatabaseHealthService],
})
export class HealthModule {}
EOF
    
    echo "✅ Health module created for $service"
}

# Function to install required dependencies
install_health_dependencies() {
    local service=$1
    local service_dir="services/$service"
    
    echo "📦 Installing health dependencies for $service..."
    cd "$service_dir"
    npm install @nestjs/terminus --silent
    cd "$BASE_DIR"
    echo "✅ Health dependencies installed for $service"
}

# Function to update app.module.ts to include health module
update_app_module_with_health() {
    local service=$1
    local service_dir="services/$service"
    local app_module="$service_dir/src/app.module.ts"
    
    echo "🔧 Adding health module to app.module.ts for $service..."
    
    # Check if HealthModule is already imported
    if grep -q "HealthModule" "$app_module"; then
        echo "⚠️  HealthModule already imported in $service"
        return
    fi
    
    # Add HealthModule import
    sed -i '/import.*enterprise.module/a import { HealthModule } from '\''./health/health.module'\'';' "$app_module"
    
    # Add HealthModule to imports array
    sed -i '/EnterpriseModule,/a \    HealthModule,' "$app_module"
    
    echo "✅ Health module added to app.module.ts for $service"
}

# Function to remove old health endpoints from app.controller.ts
remove_old_health_endpoints() {
    local service=$1
    local service_dir="services/$service"
    local app_controller="$service_dir/src/app.controller.ts"
    
    if [ ! -f "$app_controller" ]; then
        return
    fi
    
    echo "🧹 Removing old health endpoints from app.controller.ts for $service..."
    
    # Remove health-related methods from app.controller.ts
    sed -i '/getHealth/,/}/d' "$app_controller"
    sed -i '/health/,/}/d' "$app_controller"
    
    echo "✅ Old health endpoints removed from $service"
}

# Main execution
echo "🚀 Starting health endpoint standardization..."

for service in "${SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service" ]; then
        echo "❌ Service directory not found: services/$service"
        continue
    fi
    
    # Create health components
    create_health_controller "$service"
    create_database_health_service "$service"
    create_health_module "$service"
    
    # Install dependencies
    install_health_dependencies "$service"
    
    # Update app.module.ts
    update_app_module_with_health "$service"
    
    # Remove old health endpoints
    remove_old_health_endpoints "$service"
    
    echo "✅ $service health standardization complete"
done

echo ""
echo "🎉 Health endpoint standardization completed!"
echo "============================================="
echo ""
echo "📋 Summary:"
echo "- Created standard /api/health endpoints for ${#SERVICES[@]} services"
echo "- Added TerminusModule for proper health checking"
echo "- Created database health services"
echo "- Updated app.module.ts files"
echo "- Removed old inconsistent health endpoints"
echo ""
echo "🔍 All services now have:"
echo "- GET /api/health (comprehensive health check)"
echo "- GET /api/health/simple (simple status check)"
echo ""
echo "🔄 Services will restart automatically due to watch mode"
