import { api } from '@/lib/api'
import {
  AIRecommendation,
  PersonalizationProfile,
  PredictiveAnalytics,
  AutomationRule,
  MLModel,
  GetRecommendationsRequest,
  GetRecommendationsResponse,
  UpdatePersonalizationRequest,
  GetPredictionsRequest,
  GetPredictionsResponse,
  CreateAutomationRequest,
  UpdateAutomationRequest,
  RecommendationType,
  PredictionType,
  AutomationType
} from '@/types/ai.types'

class AIService {
  // Recommendation Engine
  async getRecommendations(request: GetRecommendationsRequest): Promise<GetRecommendationsResponse> {
    try {
      const params = new URLSearchParams()
      if (request.types?.length) params.append('types', request.types.join(','))
      if (request.categories?.length) params.append('categories', request.categories.join(','))
      if (request.limit) params.append('limit', request.limit.toString())
      if (request.offset) params.append('offset', request.offset.toString())

      const response = await api.get(`/ai/recommendations/${request.userId}?${params}`)
      return response.data
    } catch (error) {
      console.error('Failed to get recommendations:', error)
      throw error
    }
  }

  async getPersonalizedRecommendations(userId: string, limit = 10): Promise<AIRecommendation[]> {
    try {
      const response = await this.getRecommendations({
        userId,
        limit
      })
      return response.recommendations
    } catch (error) {
      console.error('Failed to get personalized recommendations:', error)
      return []
    }
  }

  async markRecommendationViewed(recommendationId: string): Promise<void> {
    try {
      await api.patch(`/ai/recommendations/${recommendationId}/viewed`)
    } catch (error) {
      console.error('Failed to mark recommendation as viewed:', error)
      throw error
    }
  }

  async markRecommendationActioned(recommendationId: string, actionData?: Record<string, any>): Promise<void> {
    try {
      await api.patch(`/ai/recommendations/${recommendationId}/actioned`, { actionData })
    } catch (error) {
      console.error('Failed to mark recommendation as actioned:', error)
      throw error
    }
  }

  async dismissRecommendation(recommendationId: string, reason?: string): Promise<void> {
    try {
      await api.delete(`/ai/recommendations/${recommendationId}`, {
        data: { reason }
      })
    } catch (error) {
      console.error('Failed to dismiss recommendation:', error)
      throw error
    }
  }

  // Personalization
  async getPersonalizationProfile(userId: string): Promise<PersonalizationProfile> {
    try {
      const response = await api.get(`/ai/personalization/${userId}`)
      return response.data
    } catch (error) {
      console.error('Failed to get personalization profile:', error)
      throw error
    }
  }

  async updatePersonalizationProfile(request: UpdatePersonalizationRequest): Promise<PersonalizationProfile> {
    try {
      const response = await api.patch(`/ai/personalization/${request.userId}`, request)
      return response.data
    } catch (error) {
      console.error('Failed to update personalization profile:', error)
      throw error
    }
  }

  async trackUserBehavior(userId: string, behaviorData: Record<string, any>): Promise<void> {
    try {
      await api.post(`/ai/personalization/${userId}/behavior`, behaviorData)
    } catch (error) {
      console.error('Failed to track user behavior:', error)
      throw error
    }
  }

  async getPersonalizedContent(userId: string, contentType: string, limit = 20): Promise<any[]> {
    try {
      const response = await api.get(`/ai/personalization/${userId}/content/${contentType}?limit=${limit}`)
      return response.data.content
    } catch (error) {
      console.error('Failed to get personalized content:', error)
      return []
    }
  }

  // Predictive Analytics
  async getPredictions(request: GetPredictionsRequest): Promise<GetPredictionsResponse> {
    try {
      const params = new URLSearchParams()
      if (request.types?.length) params.append('types', request.types.join(','))
      if (request.targets?.length) params.append('targets', request.targets.join(','))
      if (request.timeframe) params.append('timeframe', request.timeframe)
      if (request.limit) params.append('limit', request.limit.toString())

      const response = await api.get(`/ai/predictions?${params}`)
      return response.data
    } catch (error) {
      console.error('Failed to get predictions:', error)
      throw error
    }
  }

  async getPricePrediction(nftId: string, timeframe = '7d'): Promise<PredictiveAnalytics | null> {
    try {
      const response = await this.getPredictions({
        types: [PredictionType.PRICE_MOVEMENT],
        targets: [nftId],
        timeframe,
        limit: 1
      })
      return response.predictions[0] || null
    } catch (error) {
      console.error('Failed to get price prediction:', error)
      return null
    }
  }

  async getMarketTrendPrediction(timeframe = '30d'): Promise<PredictiveAnalytics | null> {
    try {
      const response = await this.getPredictions({
        types: [PredictionType.MARKET_TREND],
        timeframe,
        limit: 1
      })
      return response.predictions[0] || null
    } catch (error) {
      console.error('Failed to get market trend prediction:', error)
      return null
    }
  }

  async getUserBehaviorPrediction(userId: string): Promise<PredictiveAnalytics | null> {
    try {
      const response = await this.getPredictions({
        types: [PredictionType.USER_BEHAVIOR],
        targets: [userId],
        limit: 1
      })
      return response.predictions[0] || null
    } catch (error) {
      console.error('Failed to get user behavior prediction:', error)
      return null
    }
  }

  // Automation
  async getAutomationRules(userId: string, type?: AutomationType): Promise<AutomationRule[]> {
    try {
      const params = new URLSearchParams()
      if (type) params.append('type', type)

      const response = await api.get(`/ai/automation/${userId}/rules?${params}`)
      return response.data.rules
    } catch (error) {
      console.error('Failed to get automation rules:', error)
      return []
    }
  }

  async createAutomationRule(userId: string, request: CreateAutomationRequest): Promise<AutomationRule> {
    try {
      const response = await api.post(`/ai/automation/${userId}/rules`, request)
      return response.data
    } catch (error) {
      console.error('Failed to create automation rule:', error)
      throw error
    }
  }

  async updateAutomationRule(userId: string, request: UpdateAutomationRequest): Promise<AutomationRule> {
    try {
      const response = await api.patch(`/ai/automation/${userId}/rules/${request.id}`, request)
      return response.data
    } catch (error) {
      console.error('Failed to update automation rule:', error)
      throw error
    }
  }

  async deleteAutomationRule(userId: string, ruleId: string): Promise<void> {
    try {
      await api.delete(`/ai/automation/${userId}/rules/${ruleId}`)
    } catch (error) {
      console.error('Failed to delete automation rule:', error)
      throw error
    }
  }

  async toggleAutomationRule(userId: string, ruleId: string, isActive: boolean): Promise<AutomationRule> {
    try {
      const response = await api.patch(`/ai/automation/${userId}/rules/${ruleId}/toggle`, { isActive })
      return response.data
    } catch (error) {
      console.error('Failed to toggle automation rule:', error)
      throw error
    }
  }

  async getAutomationExecutionHistory(userId: string, ruleId: string, limit = 50): Promise<any[]> {
    try {
      const response = await api.get(`/ai/automation/${userId}/rules/${ruleId}/history?limit=${limit}`)
      return response.data.executions
    } catch (error) {
      console.error('Failed to get automation execution history:', error)
      return []
    }
  }

  // ML Models
  async getMLModels(): Promise<MLModel[]> {
    try {
      const response = await api.get('/ai/models')
      return response.data.models
    } catch (error) {
      console.error('Failed to get ML models:', error)
      return []
    }
  }

  async getMLModel(modelId: string): Promise<MLModel> {
    try {
      const response = await api.get(`/ai/models/${modelId}`)
      return response.data
    } catch (error) {
      console.error('Failed to get ML model:', error)
      throw error
    }
  }

  async triggerModelTraining(modelId: string): Promise<void> {
    try {
      await api.post(`/ai/models/${modelId}/train`)
    } catch (error) {
      console.error('Failed to trigger model training:', error)
      throw error
    }
  }

  // Analytics & Insights
  async getAIInsights(userId: string, timeframe = '30d'): Promise<any> {
    try {
      const response = await api.get(`/ai/insights/${userId}?timeframe=${timeframe}`)
      return response.data
    } catch (error) {
      console.error('Failed to get AI insights:', error)
      throw error
    }
  }

  async getPortfolioOptimization(userId: string): Promise<any> {
    try {
      const response = await api.get(`/ai/portfolio/${userId}/optimization`)
      return response.data
    } catch (error) {
      console.error('Failed to get portfolio optimization:', error)
      throw error
    }
  }

  async getRiskAssessment(userId: string): Promise<any> {
    try {
      const response = await api.get(`/ai/risk/${userId}/assessment`)
      return response.data
    } catch (error) {
      console.error('Failed to get risk assessment:', error)
      throw error
    }
  }

  async getMarketSentiment(): Promise<any> {
    try {
      const response = await api.get('/ai/market/sentiment')
      return response.data
    } catch (error) {
      console.error('Failed to get market sentiment:', error)
      throw error
    }
  }

  async getTrendingTopics(limit = 10): Promise<any[]> {
    try {
      const response = await api.get(`/ai/trends/topics?limit=${limit}`)
      return response.data.topics
    } catch (error) {
      console.error('Failed to get trending topics:', error)
      return []
    }
  }

  async getInfluencerRecommendations(userId: string, limit = 5): Promise<any[]> {
    try {
      const response = await api.get(`/ai/recommendations/${userId}/influencers?limit=${limit}`)
      return response.data.influencers
    } catch (error) {
      console.error('Failed to get influencer recommendations:', error)
      return []
    }
  }

  async getCommunityRecommendations(userId: string, limit = 5): Promise<any[]> {
    try {
      const response = await api.get(`/ai/recommendations/${userId}/communities?limit=${limit}`)
      return response.data.communities
    } catch (error) {
      console.error('Failed to get community recommendations:', error)
      return []
    }
  }

  async getContentRecommendations(userId: string, contentType: string, limit = 10): Promise<any[]> {
    try {
      const response = await api.get(`/ai/recommendations/${userId}/content/${contentType}?limit=${limit}`)
      return response.data.content
    } catch (error) {
      console.error('Failed to get content recommendations:', error)
      return []
    }
  }

  // Smart Notifications
  async getSmartNotifications(userId: string): Promise<any[]> {
    try {
      const response = await api.get(`/ai/notifications/${userId}/smart`)
      return response.data.notifications
    } catch (error) {
      console.error('Failed to get smart notifications:', error)
      return []
    }
  }

  async optimizeNotificationTiming(userId: string): Promise<any> {
    try {
      const response = await api.get(`/ai/notifications/${userId}/timing`)
      return response.data
    } catch (error) {
      console.error('Failed to optimize notification timing:', error)
      throw error
    }
  }
}

export const aiService = new AIService()
