'use client'

import React, { useState } from 'react'
import {
  WalletIcon,
  PlusIcon,
  XMarkIcon,
  ArrowsRightLeftIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import {
  useConnectWallet,
  useDisconnectWallet,
  useSwitchNetwork,
  useWalletBalance,
  useActiveNetworks
} from '@/hooks/useBlockchainIntegration'
import { WalletConnection, WalletType, BlockchainNetwork } from '@/types/blockchain-integration.types'

interface WalletManagerProps {
  connectedWallets?: WalletConnection[]
  isLoading: boolean
  onWalletConnected?: (walletId: string) => void
  className?: string
}

export default function WalletManager({
  connectedWallets = [],
  isLoading,
  onWalletConnected,
  className = ''
}: WalletManagerProps) {
  const [showConnectModal, setShowConnectModal] = useState(false)
  const [selectedWallet, setSelectedWallet] = useState<WalletConnection | null>(null)

  const { data: activeNetworks } = useActiveNetworks()
  const connectWalletMutation = useConnectWallet()
  const disconnectWalletMutation = useDisconnectWallet()
  const switchNetworkMutation = useSwitchNetwork()

  const walletTypes = [
    { type: WalletType.METAMASK, name: 'MetaMask', icon: '🦊', description: 'Most popular Ethereum wallet' },
    { type: WalletType.WALLET_CONNECT, name: 'WalletConnect', icon: '🔗', description: 'Connect any wallet' },
    { type: WalletType.COINBASE_WALLET, name: 'Coinbase Wallet', icon: '🔵', description: 'Coinbase\'s self-custody wallet' },
    { type: WalletType.LEDGER, name: 'Ledger', icon: '🔒', description: 'Hardware wallet security' },
    { type: WalletType.TREZOR, name: 'Trezor', icon: '🛡️', description: 'Hardware wallet protection' },
    { type: WalletType.RAINBOW, name: 'Rainbow', icon: '🌈', description: 'Fun and simple wallet' }
  ]

  const handleConnectWallet = async (walletType: WalletType) => {
    try {
      const wallet = await connectWalletMutation.mutateAsync({
        walletType,
        permissions: ['read', 'sign', 'send']
      })
      
      onWalletConnected?.(wallet.id)
      setShowConnectModal(false)
    } catch (error) {
      console.error('Failed to connect wallet:', error)
    }
  }

  const handleDisconnectWallet = async (walletId: string) => {
    if (confirm('Are you sure you want to disconnect this wallet?')) {
      try {
        await disconnectWalletMutation.mutateAsync(walletId)
      } catch (error) {
        console.error('Failed to disconnect wallet:', error)
      }
    }
  }

  const handleSwitchNetwork = async (walletId: string, network: BlockchainNetwork) => {
    const wallet = connectedWallets.find(w => w.id === walletId)
    if (!wallet) return

    const networkConfig = activeNetworks?.find(n => n.network === network)
    if (!networkConfig) return

    try {
      await switchNetworkMutation.mutateAsync({
        chainId: networkConfig.chainId,
        network
      })
    } catch (error) {
      console.error('Failed to switch network:', error)
    }
  }

  const getWalletStatusIcon = (wallet: WalletConnection) => {
    if (!wallet.isConnected) {
      return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
    }
    if (wallet.isHardwareWallet) {
      return <ShieldCheckIcon className="h-5 w-5 text-green-500" />
    }
    return <CheckCircleIcon className="h-5 w-5 text-green-500" />
  }

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`
  }

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance)
    if (num === 0) return '0'
    if (num < 0.001) return '<0.001'
    return num.toFixed(3)
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(2)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Wallet Management</h2>
          <p className="text-sm text-gray-600">Connect and manage your blockchain wallets</p>
        </div>
        
        <button
          onClick={() => setShowConnectModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Connect Wallet
        </button>
      </div>

      {/* Connected Wallets */}
      {connectedWallets.length > 0 ? (
        <div className="space-y-4">
          {connectedWallets.map((wallet) => (
            <WalletCard
              key={wallet.id}
              wallet={wallet}
              activeNetworks={activeNetworks}
              onDisconnect={() => handleDisconnectWallet(wallet.id)}
              onSwitchNetwork={(network) => handleSwitchNetwork(wallet.id, network)}
              onViewDetails={() => setSelectedWallet(wallet)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <WalletIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No wallets connected</h3>
          <p className="mt-1 text-sm text-gray-500">
            Connect a wallet to start interacting with the blockchain.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowConnectModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Connect Your First Wallet
            </button>
          </div>
        </div>
      )}

      {/* Connect Wallet Modal */}
      {showConnectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Connect Wallet</h3>
              <button
                onClick={() => setShowConnectModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-3">
              {walletTypes.map((walletType) => (
                <button
                  key={walletType.type}
                  onClick={() => handleConnectWallet(walletType.type)}
                  disabled={connectWalletMutation.isPending}
                  className="w-full flex items-center p-3 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors disabled:opacity-50"
                >
                  <span className="text-2xl mr-3">{walletType.icon}</span>
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900">{walletType.name}</div>
                    <div className="text-xs text-gray-600">{walletType.description}</div>
                  </div>
                </button>
              ))}
            </div>

            {connectWalletMutation.isPending && (
              <div className="mt-4 text-center">
                <div className="inline-flex items-center text-sm text-gray-600">
                  <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                  Connecting wallet...
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Wallet Details Modal */}
      {selectedWallet && (
        <WalletDetailsModal
          wallet={selectedWallet}
          onClose={() => setSelectedWallet(null)}
        />
      )}
    </div>
  )
}

interface WalletCardProps {
  wallet: WalletConnection
  activeNetworks?: any[]
  onDisconnect: () => void
  onSwitchNetwork: (network: BlockchainNetwork) => void
  onViewDetails: () => void
}

function WalletCard({ wallet, activeNetworks, onDisconnect, onSwitchNetwork, onViewDetails }: WalletCardProps) {
  const { data: balance } = useWalletBalance(wallet.address, wallet.network)

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <WalletIcon className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900">{wallet.walletName}</h3>
            <p className="text-sm text-gray-600">{wallet.address.slice(0, 6)}...{wallet.address.slice(-4)}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {wallet.isConnected ? (
            <CheckCircleIcon className="h-5 w-5 text-green-500" />
          ) : (
            <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
          )}
          
          <button
            onClick={onDisconnect}
            className="text-sm text-red-600 hover:text-red-700"
          >
            Disconnect
          </button>
        </div>
      </div>

      <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Network</div>
          <div className="text-sm text-gray-600 capitalize">{wallet.network}</div>
        </div>

        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Balance</div>
          <div className="text-sm text-gray-600">
            {balance ? `${formatBalance(balance.nativeBalance)} ETH` : 'Loading...'}
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Type</div>
          <div className="text-sm text-gray-600 capitalize">
            {wallet.type.replace('_', ' ')}
            {wallet.isHardwareWallet && ' (Hardware)'}
          </div>
        </div>
      </div>

      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <select
            value={wallet.network}
            onChange={(e) => onSwitchNetwork(e.target.value as BlockchainNetwork)}
            className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-blue-500 focus:border-blue-500"
          >
            {activeNetworks?.map((network) => (
              <option key={network.network} value={network.network}>
                {network.name}
              </option>
            ))}
          </select>
        </div>

        <button
          onClick={onViewDetails}
          className="text-sm text-blue-600 hover:text-blue-700"
        >
          View Details
        </button>
      </div>
    </div>
  )
}

interface WalletDetailsModalProps {
  wallet: WalletConnection
  onClose: () => void
}

function WalletDetailsModal({ wallet, onClose }: WalletDetailsModalProps) {
  const { data: balance } = useWalletBalance(wallet.address, wallet.network)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4 max-h-96 overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Wallet Details</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Address</label>
            <div className="mt-1 text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded">
              {wallet.address}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Wallet Type</label>
              <div className="mt-1 text-sm text-gray-900 capitalize">
                {wallet.type.replace('_', ' ')}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Network</label>
              <div className="mt-1 text-sm text-gray-900 capitalize">
                {wallet.network}
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Native Balance</label>
            <div className="mt-1 text-sm text-gray-900">
              {balance ? `${formatBalance(balance.nativeBalance)} ETH` : 'Loading...'}
            </div>
          </div>

          {balance && balance.tokenBalances.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Token Balances</label>
              <div className="mt-1 space-y-2">
                {balance.tokenBalances.slice(0, 5).map((token, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span>{token.symbol}</span>
                    <span>{formatBalance(token.balanceFormatted)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <label className="block text-sm font-medium text-gray-700">Connected At</label>
              <div className="mt-1 text-gray-900">
                {new Date(wallet.connectedAt).toLocaleString()}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Last Activity</label>
              <div className="mt-1 text-gray-900">
                {new Date(wallet.lastActivity).toLocaleString()}
              </div>
            </div>
          </div>

          {wallet.permissions && wallet.permissions.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Permissions</label>
              <div className="mt-1 flex flex-wrap gap-2">
                {wallet.permissions.map((permission, index) => (
                  <span
                    key={index}
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      permission.granted
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {permission.permission}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

function formatBalance(balance: string) {
  const num = parseFloat(balance)
  if (num === 0) return '0'
  if (num < 0.001) return '<0.001'
  return num.toFixed(3)
}
