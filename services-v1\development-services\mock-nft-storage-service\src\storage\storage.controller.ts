import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { StorageService } from './storage.service';

@ApiTags('storage')
@Controller('storage')
export class StorageController {
  constructor(private readonly storageService: StorageService) {}

  @Get('health')
  @ApiOperation({ summary: 'Storage service health check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck() {
    return this.storageService.healthCheck();
  }

  @Get('status')
  @ApiOperation({ summary: 'Get storage system status' })
  @ApiResponse({ status: 200, description: 'Storage status retrieved successfully' })
  async getStorageStatus() {
    return {
      status: 'success',
      data: {
        provider: 'mock-ipfs',
        connected: true,
        totalFiles: 150,
        totalSize: '2.5 GB',
        availableSpace: '97.5 GB',
        timestamp: new Date().toISOString()
      }
    };
  }
}
