'use client'

import React from 'react'
import {
  CurrencyDollarIcon,
  TrendingUpIcon,
  ChartBarIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline'
import { useRevenueAnalytics, useROIAnalysis, useCostAnalysis } from '@/hooks/useAnalytics'
import { AnalyticsTimeframe } from '@/types/analytics.types'

interface RevenueAnalyticsProps {
  campaignId: string
  timeframe: AnalyticsTimeframe
  className?: string
}

export default function RevenueAnalytics({
  campaignId,
  timeframe,
  className = ''
}: RevenueAnalyticsProps) {
  const { data: revenue, isLoading: revenueLoading } = useRevenueAnalytics(campaignId, timeframe)
  const { data: roi, isLoading: roiLoading } = useROIAnalysis(campaignId)
  const { data: costs, isLoading: costsLoading } = useCostAnalysis(campaignId, timeframe)

  if (revenueLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h2 className="text-lg font-medium text-gray-900">Revenue & ROI Analytics</h2>
        <p className="text-sm text-gray-600">Financial performance and return on investment</p>
      </div>

      {/* Revenue Metrics */}
      {revenue && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-green-600 bg-green-100 mb-4">
              <CurrencyDollarIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                ${revenue.totalRevenue.toLocaleString()}
              </h3>
              <p className="text-sm font-medium text-gray-700">Total Revenue</p>
            </div>
            <p className="text-sm text-gray-500">Campaign generated</p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-blue-600 bg-blue-100 mb-4">
              <BanknotesIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                ${revenue.averageRevenuePerUser.toFixed(2)}
              </h3>
              <p className="text-sm font-medium text-gray-700">ARPU</p>
            </div>
            <p className="text-sm text-gray-500">Average revenue per user</p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-purple-600 bg-purple-100 mb-4">
              <TrendingUpIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                {revenue.revenueGrowthRate > 0 ? '+' : ''}{(revenue.revenueGrowthRate * 100).toFixed(1)}%
              </h3>
              <p className="text-sm font-medium text-gray-700">Growth Rate</p>
            </div>
            <p className="text-sm text-gray-500">Revenue growth</p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-orange-600 bg-orange-100 mb-4">
              <ChartBarIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                {revenue.monthOverMonthGrowth > 0 ? '+' : ''}{(revenue.monthOverMonthGrowth * 100).toFixed(1)}%
              </h3>
              <p className="text-sm font-medium text-gray-700">MoM Growth</p>
            </div>
            <p className="text-sm text-gray-500">Month over month</p>
          </div>
        </div>
      )}

      {/* ROI Analysis */}
      {roi && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">ROI Analysis</h3>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <TrendingUpIcon className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-600">ROI breakdown and analysis</p>
              <p className="text-xs text-gray-500">ROI visualization would be implemented here</p>
            </div>
          </div>
        </div>
      )}

      {/* Cost Analysis */}
      {costs && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Cost Breakdown</h3>
            <div className="space-y-3">
              {costs.costsByCategory.map((category, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{category.category}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${category.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      ${category.amount.toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Cost Efficiency</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Cost per Participant</span>
                <span className="text-sm font-medium text-gray-900">
                  ${costs.costPerParticipant.toFixed(2)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Cost per Conversion</span>
                <span className="text-sm font-medium text-gray-900">
                  ${costs.costPerConversion.toFixed(2)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Cost per NFT</span>
                <span className="text-sm font-medium text-gray-900">
                  ${costs.costPerNFT.toFixed(2)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Efficiency Ratio</span>
                <span className="text-sm font-medium text-gray-900">
                  {costs.costEfficiencyRatio.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Revenue Projections */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Projections</h3>
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">Revenue forecast and projections</p>
            <p className="text-xs text-gray-500">Time series projection chart would be implemented here</p>
          </div>
        </div>
      </div>
    </div>
  )
}
