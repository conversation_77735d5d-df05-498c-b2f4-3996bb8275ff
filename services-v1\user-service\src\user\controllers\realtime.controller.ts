import { Controller, Get, Post, Put, Body, Param, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { Public } from '../decorators/public.decorator';
import { RealtimeService } from '../services/realtime.service';
import { RealtimeGateway } from '../gateways/realtime.gateway';
import { 
  CreateNotificationDto,
  NotificationDto,
  ActivityFeedItemDto,
  RealTimeMetricsDto,
  ActivityType 
} from '../dto/realtime.dto';
import { ErrorResponseDto } from '../../common/dto/error-response.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RBACGuard } from '../guards/rbac.guard';
import { 
  RequireAnalyticsRead,
  RequireAnalyticsWrite,
  Authenticated 
} from '../decorators/permissions.decorator';

@ApiTags('Real-time Features')
@Controller('realtime')
@UseGuards(JwtAuthGuard, RBACGuard)
@ApiBearerAuth()
export class RealtimeController {
  constructor(
    private readonly realtimeService: RealtimeService,
    private readonly realtimeGateway: RealtimeGateway,
  ) {}

  @Post('notifications')
  @RequireAnalyticsWrite()
  @ApiOperation({ summary: 'Create notification for user' })
  @ApiBody({ type: CreateNotificationDto })
  @ApiResponse({ status: 201, description: 'Notification created successfully', type: NotificationDto })
  @ApiResponse({ status: 400, description: 'Invalid notification data', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async createNotification(@Body() createNotificationDto: CreateNotificationDto, @Req() request: any) {
    try {
      // Create notification
      const notification = await this.realtimeService.createNotification(createNotificationDto);

      // Send real-time notification if user is connected
      if (this.realtimeGateway.isUserConnected(createNotificationDto.userId)) {
        await this.realtimeGateway.sendNotificationToUser(createNotificationDto.userId, notification);
      }

      return {
        success: true,
        data: notification,
        message: 'Notification created successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('notifications')
  @Authenticated()
  @ApiOperation({ summary: 'Get user notifications' })
  @ApiQuery({ name: 'unreadOnly', type: Boolean, required: false, description: 'Get only unread notifications' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async getNotifications(
    @Query('unreadOnly') unreadOnly: boolean | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Req() request: any
  ) {
    const userId = request.user.sub;

    try {
      if (unreadOnly) {
        const notifications = await this.realtimeService.getPendingNotifications(userId);
        return {
          success: true,
          data: {
            notifications,
            unreadCount: notifications.length,
          },
        };
      }

      // TODO: Implement paginated notifications retrieval
      return {
        success: true,
        message: 'Paginated notifications not yet implemented',
        data: {
          notifications: [],
          pagination: {
            page: page || 1,
            limit: limit || 20,
            totalCount: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Put('notifications/:id/read')
  @Authenticated()
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({ status: 200, description: 'Notification marked as read successfully' })
  @ApiResponse({ status: 404, description: 'Notification not found', type: ErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async markNotificationAsRead(@Param('id') id: string, @Req() request: any) {
    const userId = request.user.sub;

    try {
      await this.realtimeService.markNotificationAsRead(id, userId);

      return {
        success: true,
        message: 'Notification marked as read',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('activity-feed')
  @Authenticated()
  @ApiOperation({ summary: 'Get platform activity feed' })
  @ApiQuery({ name: 'type', enum: ActivityType, required: false, description: 'Filter by activity type' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiResponse({ status: 200, description: 'Activity feed retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async getActivityFeed(
    @Query('type') type: ActivityType | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Req() request: any
  ) {
    try {
      // TODO: Implement activity feed retrieval
      return {
        success: true,
        message: 'Activity feed not yet implemented',
        data: {
          activities: [],
          pagination: {
            page: page || 1,
            limit: limit || 20,
            totalCount: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('metrics')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get real-time platform metrics' })
  @ApiResponse({ status: 200, description: 'Real-time metrics retrieved successfully', type: RealTimeMetricsDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getRealTimeMetrics(@Req() request: any) {
    try {
      const metrics = await this.realtimeService.getRealTimeMetrics();

      return {
        success: true,
        data: metrics,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('status')
  @Authenticated()
  @ApiOperation({ summary: 'Get real-time connection status' })
  @ApiResponse({ status: 200, description: 'Connection status retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async getConnectionStatus(@Req() request: any) {
    const userId = request.user.sub;

    try {
      const isConnected = this.realtimeGateway.isUserConnected(userId);
      const connectedUsersCount = this.realtimeGateway.getConnectedUsersCount();

      return {
        success: true,
        data: {
          userId,
          isConnected,
          connectedUsersCount,
          serverTime: new Date().toISOString(),
          websocketUrl: '/realtime',
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('connected-users')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get connected users information' })
  @ApiResponse({ status: 200, description: 'Connected users information retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getConnectedUsers(@Req() request: any) {
    try {
      const connectedUserIds = this.realtimeGateway.getConnectedUserIds();
      const connectedUsersCount = this.realtimeGateway.getConnectedUsersCount();

      return {
        success: true,
        data: {
          connectedUsersCount,
          connectedUserIds: connectedUserIds.slice(0, 100), // Limit to first 100 for privacy
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Post('broadcast/activity')
  @RequireAnalyticsWrite()
  @ApiOperation({ summary: 'Broadcast activity update to all users' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        type: { type: 'string', enum: Object.values(ActivityType) },
        title: { type: 'string' },
        description: { type: 'string' },
        userId: { type: 'string' },
        entityType: { type: 'string' },
        entityId: { type: 'string' },
        metadata: { type: 'object' },
      },
      required: ['type', 'title', 'description', 'userId'],
    },
  })
  @ApiResponse({ status: 200, description: 'Activity broadcasted successfully' })
  @ApiResponse({ status: 400, description: 'Invalid activity data', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async broadcastActivity(@Body() activityData: any, @Req() request: any) {
    try {
      // Create activity feed item
      const activity = await this.realtimeService.createActivityFeedItem(
        activityData.type,
        activityData.title,
        activityData.description,
        activityData.userId,
        activityData.entityType,
        activityData.entityId,
        activityData.metadata
      );

      // Broadcast to all connected users
      await this.realtimeGateway.broadcastActivityUpdate(activity);

      return {
        success: true,
        data: activity,
        message: 'Activity broadcasted successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Post('broadcast/campaign-update')
  @RequireAnalyticsWrite()
  @ApiOperation({ summary: 'Broadcast campaign update to participants' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        campaignId: { type: 'string' },
        updateData: { type: 'object' },
      },
      required: ['campaignId', 'updateData'],
    },
  })
  @ApiResponse({ status: 200, description: 'Campaign update broadcasted successfully' })
  @ApiResponse({ status: 400, description: 'Invalid update data', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async broadcastCampaignUpdate(@Body() data: { campaignId: string; updateData: any }, @Req() request: any) {
    try {
      await this.realtimeGateway.sendCampaignUpdate(data.campaignId, data.updateData);

      return {
        success: true,
        message: 'Campaign update broadcasted successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Post('broadcast/marketplace-update')
  @RequireAnalyticsWrite()
  @ApiOperation({ summary: 'Broadcast marketplace update to all users' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        updateData: { type: 'object' },
      },
      required: ['updateData'],
    },
  })
  @ApiResponse({ status: 200, description: 'Marketplace update broadcasted successfully' })
  @ApiResponse({ status: 400, description: 'Invalid update data', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async broadcastMarketplaceUpdate(@Body() data: { updateData: any }, @Req() request: any) {
    try {
      await this.realtimeGateway.sendMarketplaceUpdate(data.updateData);

      return {
        success: true,
        message: 'Marketplace update broadcasted successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('health')
  @Public()
  @ApiOperation({ summary: 'Get real-time service health status' })
  @ApiResponse({ status: 200, description: 'Real-time service health status' })
  async getRealtimeHealth() {
    try {
      const connectedUsersCount = this.realtimeGateway.getConnectedUsersCount();
      const metrics = await this.realtimeService.getRealTimeMetrics();

      return {
        success: true,
        data: {
          status: 'healthy',
          connectedUsers: connectedUsersCount,
          websocketStatus: 'active',
          metricsLastUpdated: metrics.timestamp,
          uptime: process.uptime(),
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        data: {
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }
}
