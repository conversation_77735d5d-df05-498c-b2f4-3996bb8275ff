import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('ProfileAnalysisService');
  
  try {
    // Create NestJS application
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // Global validation pipe
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }));

    // CORS configuration
    app.enableCors({
      origin: process.env.CORS_ORIGIN || '*',
      methods: process.env.CORS_METHODS || 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      credentials: process.env.CORS_CREDENTIALS === 'true',
    });

    // API prefix
    const apiPrefix = process.env.API_PREFIX || 'api';
    app.setGlobalPrefix(apiPrefix);

    // Swagger documentation
    if (process.env.SWAGGER_ENABLED !== 'false') {
      const config = new DocumentBuilder()
        .setTitle('Profile Analysis Service API')
        .setDescription('Twitter profile analysis and social media scoring service for Social NFT Platform')
        .setVersion('1.0')
        .addBearerAuth()
        .addTag('profile-analysis', 'Profile analysis operations')
        .addTag('twitter', 'Twitter integration operations')
        .addTag('health', 'Health check operations')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup(`${apiPrefix}/docs`, app, document, {
        swaggerOptions: {
          persistAuthorization: true,
        },
      });
    }

    // Start server
    const port = process.env.SERVICE_PORT || 3002;
    await app.listen(port);

    logger.log(`🚀 Profile Analysis Service started successfully`);
    logger.log(`📍 Server running on: http://localhost:${port}`);
    logger.log(`📚 API Documentation: http://localhost:${port}/${apiPrefix}/docs`);
    logger.log(`🏥 Health Check: http://localhost:${port}/${apiPrefix}/health`);
    logger.log(`🎯 Environment: ${process.env.NODE_ENV || 'development'}`);
    
  } catch (error) {
    logger.error('❌ Failed to start Profile Analysis Service', error);
    process.exit(1);
  }
}

bootstrap();
