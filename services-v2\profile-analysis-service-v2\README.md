# 🧠 **Profile Analysis Service V2 - AI-Powered Analysis**

## **📋 OVERVIEW**

Profile Analysis Service V2 is an AI-powered microservice that analyzes social media profiles and provides insights, metrics, and recommendations. Built with clean architecture and following the **Database Per Service** pattern.

### **🎯 Key Features**
- ✅ **AI-Powered Analysis** - Advanced profile analysis using machine learning
- ✅ **Multi-Platform Support** - Twitter, Instagram, LinkedIn, TikTok, YouTube
- ✅ **Database Per Service** - Own PostgreSQL database (`profile_analysis_service_v2`)
- ✅ **Queue Management** - Asynchronous analysis processing
- ✅ **Insights Generation** - Automated insights and recommendations
- ✅ **Metrics Tracking** - Comprehensive performance metrics
- ✅ **Health Monitoring** - Kubernetes-ready health endpoints
- ✅ **API Documentation** - Swagger/OpenAPI integration

---

## 🏗️ **ARCHITECTURE**

### **Database Per Service Pattern**
```
Profile Analysis Service V2 (Port 4002) ──► profile_analysis_service_v2 Database
├── profile_analyses table
├── profile_metrics table
├── profile_insights table
├── profile_recommendations table
├── analysis_templates table
├── analysis_queue table
├── ai_models table
└── analysis_cache table
```

### **AI Analysis Pipeline**
```
1. Request Analysis → 2. Queue Processing → 3. Data Collection
        ↓                      ↓                    ↓
8. Generate Report ← 7. Create Recommendations ← 4. AI Analysis
        ↓                      ↑                    ↓
9. Store Results ← 6. Generate Insights ← 5. Extract Metrics
```

---

## 🚀 **GETTING STARTED**

### **Prerequisites**
- Node.js 18+
- PostgreSQL 14+
- npm 8+

### **Installation**
```bash
# Navigate to service directory
cd services-v2/profile-analysis-service-v2

# Install dependencies
npm install

# Setup database
createdb profile_analysis_service_v2

# Run database migrations
npm run prisma:migrate

# Generate Prisma client
npm run prisma:generate

# Seed database (optional)
npm run db:seed
```

### **Environment Setup**
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

### **Running the Service**
```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod

# Debug mode
npm run start:debug
```

---

## 📊 **API ENDPOINTS**

### **Analysis Endpoints**
```bash
POST /analysis                    # Create new analysis
GET  /analysis                    # Get all analyses (paginated)
GET  /analysis/:id                # Get analysis by ID
GET  /analysis/user/:userId       # Get analyses by user
PUT  /analysis/:id                # Update analysis
DELETE /analysis/:id              # Delete analysis
POST /analysis/:id/process        # Trigger AI processing
GET  /analysis/:id/status         # Get analysis status
GET  /analysis/:id/results        # Get analysis results
```

### **Health Check Endpoints**
```bash
GET /health/simple                # Simple health check
GET /health/detailed              # Detailed health with dependencies
GET /health/database              # Database health check
GET /health/ready                 # Readiness probe (Kubernetes)
GET /health/live                  # Liveness probe (Kubernetes)
```

---

## 🔧 **CONFIGURATION**

### **Environment Variables**
```bash
# Service Configuration
SERVICE_NAME=profile-analysis-service-v2
SERVICE_PORT=4002
NODE_ENV=development

# Database (Own Database)
DATABASE_URL=postgresql://postgres:password@localhost:5432/profile_analysis_service_v2

# AI/ML Configuration
OPENAI_API_KEY=your-openai-api-key
ANALYSIS_TIMEOUT=300000
MAX_CONCURRENT_ANALYSES=5

# External Services (API Communication Only)
USER_SERVICE_URL=http://localhost:4001
API_GATEWAY_URL=http://localhost:4010
```

### **Database Schema**
The service uses its own PostgreSQL database with the following tables:
- `profile_analyses` - Analysis requests and results
- `profile_metrics` - Extracted metrics and KPIs
- `profile_insights` - AI-generated insights
- `profile_recommendations` - Actionable recommendations
- `analysis_templates` - Analysis configuration templates
- `analysis_queue` - Processing queue management
- `ai_models` - AI model configurations
- `analysis_cache` - Results caching

---

## 🧪 **TESTING**

### **Running Tests**
```bash
# Unit tests
npm test

# Watch mode
npm run test:watch

# Coverage report
npm run test:cov

# E2E tests
npm run test:e2e
```

### **API Testing**
```bash
# Test health check
curl http://localhost:4002/health/simple

# Create analysis
curl -X POST http://localhost:4002/analysis \
  -H "Content-Type: application/json" \
  -d '{"userId":"user123","analysisType":"twitter","sourceUrl":"https://twitter.com/username"}'

# Get analysis status
curl http://localhost:4002/analysis/{analysisId}/status

# Get analysis results
curl http://localhost:4002/analysis/{analysisId}/results
```

---

## 📚 **API DOCUMENTATION**

### **Swagger Documentation**
- **URL**: `http://localhost:4002/api/docs`
- **Interactive API Explorer**: Test endpoints directly
- **Schema Documentation**: Complete request/response schemas

### **Response Format**
All API responses follow the standardized format:
```json
{
  "success": true,
  "data": {...},
  "message": "Analysis completed successfully",
  "timestamp": "2024-06-14T10:30:00.000Z",
  "service": "profile-analysis-service-v2"
}
```

---

## 🤖 **AI ANALYSIS FEATURES**

### **Supported Platforms**
- **Twitter** - Tweets, engagement, follower analysis
- **Instagram** - Posts, stories, audience insights
- **LinkedIn** - Professional content, network analysis
- **TikTok** - Video content, viral potential
- **YouTube** - Channel performance, subscriber growth
- **General** - Cross-platform analysis

### **Analysis Metrics**
- **Engagement Rate** - Likes, comments, shares ratio
- **Reach Score** - Audience size and growth potential
- **Sentiment Analysis** - Audience response sentiment
- **Authenticity Score** - Content authenticity assessment
- **Influence Score** - Industry influence measurement

### **Generated Insights**
- **Strengths** - What's working well
- **Opportunities** - Areas for improvement
- **Trends** - Performance patterns
- **Audience** - Follower demographics and behavior

### **Recommendations**
- **Content Strategy** - Content optimization suggestions
- **Posting Schedule** - Optimal timing recommendations
- **Audience Targeting** - Growth strategies
- **Brand Partnerships** - Monetization opportunities

---

## 🔍 **MONITORING & LOGGING**

### **Analysis Tracking**
- Analysis request and completion tracking
- Queue processing monitoring
- AI model performance metrics
- Error rate and retry tracking

### **Performance Monitoring**
- Database query performance
- AI processing time tracking
- Memory and CPU usage metrics
- Queue processing throughput

### **Health Monitoring**
```bash
# Check service health
curl http://localhost:4002/health/detailed

# Check database health
curl http://localhost:4002/health/database

# Kubernetes probes
curl http://localhost:4002/health/ready   # Readiness
curl http://localhost:4002/health/live    # Liveness
```

---

## 🚀 **DEPLOYMENT**

### **Docker Deployment**
```bash
# Build Docker image
docker build -t profile-analysis-service-v2 .

# Run container
docker run -p 4002:4002 \
  -e DATABASE_URL=postgresql://postgres:<EMAIL>:5432/profile_analysis_service_v2 \
  profile-analysis-service-v2
```

### **Kubernetes Deployment**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: profile-analysis-service-v2
spec:
  replicas: 3
  selector:
    matchLabels:
      app: profile-analysis-service-v2
  template:
    metadata:
      labels:
        app: profile-analysis-service-v2
    spec:
      containers:
      - name: profile-analysis-service-v2
        image: profile-analysis-service-v2:latest
        ports:
        - containerPort: 4002
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: profile-analysis-secrets
              key: database-url
        livenessProbe:
          httpGet:
            path: /health/live
            port: 4002
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 4002
```

---

## 🎯 **COMPARISON WITH V1**

### **Improvements in V2**
- ✅ **AI-Powered Analysis** - Advanced machine learning capabilities
- ✅ **Queue Management** - Asynchronous processing with retry logic
- ✅ **Multi-Platform Support** - Support for multiple social media platforms
- ✅ **Insights Generation** - Automated insights and recommendations
- ✅ **Performance Optimization** - Better caching and processing efficiency
- ✅ **Health Monitoring** - Kubernetes-ready health endpoints
- ✅ **API Documentation** - Comprehensive Swagger documentation

### **Migration from V1**
- **Port Change**: V1 (3002) → V2 (4002)
- **Database**: Separate database (`profile_analysis_service_v2`)
- **Enhanced Features**: AI analysis, insights, recommendations
- **Zero Downtime**: Can run both versions simultaneously

---

**🧠 Profile Analysis Service V2 provides enterprise-grade AI-powered social media profile analysis!**
