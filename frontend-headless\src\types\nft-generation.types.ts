export enum NFTGenerationStatus {
  PENDING = 'pending',
  QUEUED = 'queued',
  GENERATING = 'generating',
  GENERATED = 'generated',
  REVIEWING = 'reviewing',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  MINTING = 'minting',
  MINTED = 'minted',
  DISTRIBUTED = 'distributed',
  FAILED = 'failed'
}

export enum NFTQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  PREMIUM = 'premium'
}

export enum NFTRarity {
  COMMON = 'common',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary',
  MYTHIC = 'mythic'
}

export enum GenerationProvider {
  OPENAI_DALLE = 'openai_dalle',
  MIDJOURNEY = 'midjourney',
  STABLE_DIFFUSION = 'stable_diffusion',
  CUSTOM_AI = 'custom_ai',
  MANUAL = 'manual'
}

export interface NFTGenerationRequest {
  id: string
  campaignId: string
  participantId: string
  userId: string
  
  // Generation Parameters
  prompt: string
  style: string
  theme: string
  quality: NFTQuality
  rarity: NFTRarity
  
  // Configuration
  provider: GenerationProvider
  settings: GenerationSettings
  metadata: NFTMetadata
  
  // Status and Tracking
  status: NFTGenerationStatus
  priority: number
  attempts: number
  maxAttempts: number
  
  // Timestamps
  requestedAt: string
  startedAt?: string
  completedAt?: string
  
  // Results
  generatedImages?: GeneratedImage[]
  selectedImageId?: string
  mintingData?: MintingData
  
  // Error Handling
  errors?: GenerationError[]
  
  // Review Data
  reviewData?: ReviewData
}

export interface GenerationSettings {
  width: number
  height: number
  steps: number
  guidance: number
  seed?: number
  negativePrompt?: string
  batchSize: number
  variations: number
  
  // Style Settings
  styleStrength: number
  colorPalette: string[]
  effects: string[]
  
  // Quality Settings
  upscale: boolean
  enhanceDetails: boolean
  removeBackground: boolean
  
  // Provider-specific
  providerSettings: Record<string, any>
}

export interface NFTMetadata {
  name: string
  description: string
  image: string
  external_url?: string
  
  // Standard Attributes
  attributes: NFTAttribute[]
  
  // Campaign Context
  campaignName: string
  campaignId: string
  participantId: string
  
  // Generation Context
  generatedAt: string
  generationProvider: GenerationProvider
  prompt: string
  rarity: NFTRarity
  
  // Custom Properties
  properties: Record<string, any>
  
  // Collection Info
  collection: {
    name: string
    family: string
  }
}

export interface NFTAttribute {
  trait_type: string
  value: string | number
  display_type?: 'boost_number' | 'boost_percentage' | 'number' | 'date'
  max_value?: number
}

export interface GeneratedImage {
  id: string
  url: string
  thumbnailUrl: string
  width: number
  height: number
  fileSize: number
  format: string
  
  // Quality Metrics
  qualityScore: number
  aestheticScore: number
  technicalScore: number
  
  // Analysis
  analysis: ImageAnalysis
  
  // Status
  status: 'generated' | 'selected' | 'rejected'
  rejectionReason?: string
}

export interface ImageAnalysis {
  // Content Analysis
  objects: DetectedObject[]
  colors: ColorAnalysis
  composition: CompositionAnalysis
  
  // Quality Metrics
  sharpness: number
  brightness: number
  contrast: number
  saturation: number
  
  // Style Analysis
  styleMatch: number
  themeMatch: number
  promptAdherence: number
  
  // Technical Analysis
  resolution: number
  artifactScore: number
  coherenceScore: number
}

export interface DetectedObject {
  name: string
  confidence: number
  boundingBox: {
    x: number
    y: number
    width: number
    height: number
  }
}

export interface ColorAnalysis {
  dominantColors: string[]
  colorHarmony: number
  paletteMatch: number
  vibrancy: number
}

export interface CompositionAnalysis {
  balance: number
  symmetry: number
  ruleOfThirds: number
  focusPoint: {
    x: number
    y: number
  }
}

export interface MintingData {
  blockchain: string
  contractAddress: string
  tokenId?: string
  transactionHash?: string
  gasUsed?: number
  gasCost?: number
  
  // Minting Status
  mintingStatus: 'pending' | 'minting' | 'minted' | 'failed'
  mintedAt?: string
  
  // IPFS Data
  ipfsHash?: string
  metadataUri?: string
  imageUri?: string
  
  // Error Handling
  mintingErrors?: string[]
}

export interface GenerationError {
  code: string
  message: string
  details?: any
  timestamp: string
  retryable: boolean
}

export interface ReviewData {
  reviewerId: string
  reviewedAt: string
  decision: 'approved' | 'rejected' | 'revision_required'
  score: number
  feedback: string
  
  // Detailed Review
  qualityRating: number
  styleRating: number
  appropriatenessRating: number
  
  // Suggestions
  suggestions?: string[]
  revisionNotes?: string
}

export interface GenerationQueue {
  id: string
  name: string
  campaignId: string
  
  // Queue Configuration
  maxConcurrent: number
  priority: number
  provider: GenerationProvider
  
  // Status
  status: 'active' | 'paused' | 'stopped'
  totalRequests: number
  completedRequests: number
  failedRequests: number
  
  // Performance
  averageGenerationTime: number
  successRate: number
  
  // Timestamps
  createdAt: string
  lastProcessedAt?: string
}

export interface GenerationBatch {
  id: string
  queueId: string
  campaignId: string
  
  // Batch Info
  size: number
  priority: number
  
  // Requests
  requests: NFTGenerationRequest[]
  
  // Status
  status: 'pending' | 'processing' | 'completed' | 'failed'
  startedAt?: string
  completedAt?: string
  
  // Progress
  totalRequests: number
  completedRequests: number
  failedRequests: number
  
  // Performance
  averageGenerationTime: number
  totalCost: number
}

export interface GenerationStats {
  // Overall Stats
  totalGenerated: number
  totalMinted: number
  totalDistributed: number
  
  // Quality Stats
  averageQualityScore: number
  approvalRate: number
  rejectionRate: number
  
  // Performance Stats
  averageGenerationTime: number
  averageMintingTime: number
  successRate: number
  
  // Cost Stats
  totalCost: number
  averageCostPerNFT: number
  costByProvider: Record<GenerationProvider, number>
  
  // Provider Stats
  providerUsage: Record<GenerationProvider, number>
  providerSuccessRates: Record<GenerationProvider, number>
  
  // Time Series
  generationOverTime: Array<{
    date: string
    generated: number
    minted: number
    cost: number
  }>
}

export interface CreateGenerationRequestData {
  campaignId: string
  participantId: string
  userId: string
  prompt: string
  style: string
  theme: string
  quality: NFTQuality
  rarity: NFTRarity
  provider?: GenerationProvider
  settings?: Partial<GenerationSettings>
  metadata?: Partial<NFTMetadata>
  priority?: number
}

export interface UpdateGenerationRequestData {
  status?: NFTGenerationStatus
  selectedImageId?: string
  reviewData?: ReviewData
  mintingData?: Partial<MintingData>
  errors?: GenerationError[]
}

export interface GenerationFilters {
  campaignId?: string
  userId?: string
  status?: NFTGenerationStatus[]
  provider?: GenerationProvider[]
  quality?: NFTQuality[]
  rarity?: NFTRarity[]
  dateRange?: {
    start: string
    end: string
  }
  sortBy?: 'created_at' | 'completed_at' | 'quality_score' | 'priority'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface QueueConfiguration {
  maxConcurrent: number
  retryAttempts: number
  retryDelay: number
  timeoutDuration: number
  priorityWeights: Record<string, number>
  providerLimits: Record<GenerationProvider, number>
  qualityThresholds: Record<NFTQuality, number>
  autoApprovalThreshold: number
  costLimits: {
    perRequest: number
    perBatch: number
    perCampaign: number
  }
}
