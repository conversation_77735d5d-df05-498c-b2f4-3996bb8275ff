# Social NFT Platform - Integration Testing Guide

## 🧪 COMPLETE PLATFORM INTEGRATION TESTING

### **Prerequisites**
- All 9 services running (see SERVICE_STARTUP_GUIDE.md)
- PostgreSQL databases created and accessible
- curl or Postman for API testing

## 🔄 END-TO-END WORKFLOW TESTING

### **Step 1: Health Check All Services**
```bash
# Verify all services are running
curl http://localhost:3001/health
curl http://localhost:3002/health
curl http://localhost:3004/api/health
curl http://localhost:3005/api/health
curl http://localhost:3006/health
curl http://localhost:3007/api/health
curl http://localhost:3008/api/health
curl http://localhost:3009/api/health
curl http://localhost:3010/api/health
```

### **Step 2: User Registration & Authentication**
```bash
# Register new user
curl -X POST http://localhost:3001/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "username": "testuser"
  }'

# Login user
curl -X POST http://localhost:3001/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### **Step 3: Profile Analysis**
```bash
# Analyze Twitter profile
curl -X POST http://localhost:3002/analysis/twitter-profile \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-id-from-step-2",
    "twitterHandle": "@testuser",
    "campaignId": "campaign-id"
  }'
```

### **Step 4: NFT Generation**
```bash
# Generate NFT based on profile analysis
curl -X POST http://localhost:3004/api/nft-generation/generate \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-id",
    "campaignId": "campaign-id",
    "analysisId": "analysis-id-from-step-3"
  }'
```

### **Step 5: Blockchain Minting**
```bash
# Mint NFT to blockchain
curl -X POST http://localhost:3005/api/blockchain/mint \
  -H "Content-Type: application/json" \
  -d '{
    "nftId": "nft-id-from-step-4",
    "userId": "user-id",
    "network": "ethereum"
  }'
```

### **Step 6: Marketplace Listing**
```bash
# List NFT on marketplace
curl -X POST http://localhost:3007/api/marketplace/listings \
  -H "Content-Type: application/json" \
  -d '{
    "nftId": "nft-id",
    "userId": "user-id",
    "price": 0.1,
    "currency": "ETH"
  }'
```

### **Step 7: Notification Testing**
```bash
# Send notification
curl -X POST http://localhost:3008/api/notifications \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-id",
    "type": "email",
    "category": "nft-generated",
    "title": "NFT Generated Successfully",
    "message": "Your NFT has been generated and is ready for minting!"
  }'
```

### **Step 8: Analytics Tracking**
```bash
# Track analytics event
curl -X POST http://localhost:3009/api/analytics/track \
  -H "Content-Type: application/json" \
  -d '{
    "eventType": "nft_generated",
    "category": "nft",
    "userId": "user-id",
    "properties": {
      "rarity": "rare",
      "campaign": "test-campaign"
    }
  }'
```

## 📊 VERIFICATION TESTS

### **Platform Overview**
```bash
# Get comprehensive platform statistics
curl http://localhost:3009/api/platform-overview
```

### **Service Status Checks**
```bash
# Check all service configurations
curl http://localhost:3004/api/nft-generation-status
curl http://localhost:3005/api/blockchain-status
curl http://localhost:3007/api/marketplace-status
curl http://localhost:3008/api/notification-status
curl http://localhost:3009/api/analytics-status
```

### **Data Retrieval Tests**
```bash
# Get user data across services
curl http://localhost:3001/users/profile/user-id
curl http://localhost:3002/analysis/user/user-id
curl http://localhost:3004/api/nft-generation/user/user-id
curl http://localhost:3005/api/blockchain/mints/user/user-id
curl http://localhost:3007/api/marketplace/listings/user/user-id
curl http://localhost:3008/api/notifications/user/user-id
```

## ✅ SUCCESS CRITERIA

### **All Services Healthy**
- All health endpoints return 200 status
- All services show "ok" status
- Database connections established

### **E2E Workflow Complete**
- User registration successful
- Profile analysis completed
- NFT generation successful
- Blockchain minting simulated
- Marketplace listing created
- Notifications sent
- Analytics events tracked

### **Data Consistency**
- User data consistent across services
- NFT metadata properly linked
- Transaction history accurate
- Analytics events recorded

## 🚨 TROUBLESHOOTING

### **Common Issues**
- **Service not responding:** Check if service is running on correct port
- **Database errors:** Verify PostgreSQL connection and database exists
- **Authentication errors:** Ensure JWT tokens are valid
- **CORS errors:** Check CORS configuration in services

### **Debug Commands**
```bash
# Check service logs
npm run start:dev (in service directory)

# Verify database connection
psql -h localhost -p 5432 -U postgres -l

# Check port usage
netstat -ano | findstr :3001
```

---

**Integration Testing Complete: Platform ready for production deployment** 🚀
