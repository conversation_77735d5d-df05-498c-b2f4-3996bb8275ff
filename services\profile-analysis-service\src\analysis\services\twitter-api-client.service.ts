import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class TwitterApiClientService {
  private readonly logger = new Logger(TwitterApiClientService.name);
  private readonly mockTwitterServiceUrl: string;
  private readonly useMockTwitter: boolean;

  constructor(private readonly httpService: HttpService) {
    this.mockTwitterServiceUrl = process.env.MOCK_TWITTER_SERVICE_URL || 'http://localhost:3020';
    this.useMockTwitter = process.env.USE_MOCK_TWITTER === 'true';
  }

  /**
   * Get Twitter profile data
   */
  async getProfile(username: string): Promise<any> {
    try {
      this.logger.log(`Getting Twitter profile for: ${username}`);

      if (this.useMockTwitter) {
        return await this.getMockProfile(username);
      } else {
        return await this.getRealProfile(username);
      }
    } catch (error) {
      this.logger.error(`Failed to get Twitter profile: ${error.message}`, { username });
      throw error;
    }
  }

  /**
   * Exchange OAuth code for access token
   */
  async exchangeCodeForToken(code: string, state: string): Promise<any> {
    try {
      this.logger.log('Exchanging OAuth code for token');

      if (this.useMockTwitter) {
        return await this.mockTokenExchange(code, state);
      } else {
        return await this.realTokenExchange(code, state);
      }
    } catch (error) {
      this.logger.error(`Token exchange failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate access token
   */
  async validateToken(token: string): Promise<any> {
    try {
      this.logger.log('Validating access token');

      if (this.useMockTwitter) {
        return {
          success: true,
          data: {
            valid: true,
            user: {
              id: 'mock_user_id',
              username: 'mock_user',
              email: '<EMAIL>',
            },
          },
        };
      } else {
        // TODO: Implement real token validation
        return {
          success: false,
          error: 'Real token validation not implemented',
        };
      }
    } catch (error) {
      this.logger.error(`Token validation failed: ${error.message}`);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get mock Twitter profile data
   */
  private async getMockProfile(username: string): Promise<any> {
    try {
      this.logger.log(`Getting mock Twitter profile for: ${username}`);

      // Try to call mock Twitter service if available
      try {
        const response = await firstValueFrom(
          this.httpService.get(`${this.mockTwitterServiceUrl}/twitter/users/${username}`, {
            timeout: 5000,
          })
        );

        if (response.data && response.data.success) {
          this.logger.log(`Mock Twitter service returned data for: ${username}`);
          return response.data.data;
        }
      } catch (serviceError) {
        this.logger.warn(`Mock Twitter service unavailable, using fallback data: ${serviceError.message}`);
      }

      // Fallback to generated mock data
      return this.generateMockProfileData(username);
    } catch (error) {
      this.logger.error(`Failed to get mock profile: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get real Twitter profile data
   */
  private async getRealProfile(username: string): Promise<any> {
    try {
      this.logger.log(`Getting real Twitter profile for: ${username}`);

      // TODO: Implement real Twitter API v2 integration
      throw new Error('Real Twitter API integration not implemented yet');
    } catch (error) {
      this.logger.error(`Failed to get real profile: ${error.message}`);
      throw error;
    }
  }

  /**
   * Mock token exchange
   */
  private async mockTokenExchange(code: string, state: string): Promise<any> {
    try {
      this.logger.log('Performing mock token exchange');

      // Try to call mock Twitter service if available
      try {
        const response = await firstValueFrom(
          this.httpService.post(`${this.mockTwitterServiceUrl}/oauth/token`, {
            code,
            state,
            grant_type: 'authorization_code',
          }, {
            timeout: 5000,
          })
        );

        if (response.data && response.data.success) {
          this.logger.log('Mock Twitter service token exchange successful');
          return response.data;
        }
      } catch (serviceError) {
        this.logger.warn(`Mock Twitter service unavailable, using fallback token: ${serviceError.message}`);
      }

      // Fallback to generated mock token
      return {
        success: true,
        data: {
          access_token: `mock_token_${Date.now()}`,
          refresh_token: `mock_refresh_${Date.now()}`,
          expires_in: 3600,
          user: {
            id: `mock_user_${Date.now()}`,
            username: 'mock_twitter_user',
            name: 'Mock Twitter User',
            email: '<EMAIL>',
            verified: false,
            profile_image_url: 'https://via.placeholder.com/150',
            public_metrics: {
              followers_count: 1000,
              following_count: 500,
              tweet_count: 2000,
              listed_count: 10,
            },
            description: 'Mock Twitter user for development',
            location: 'Mock City',
            url: 'https://example.com',
            created_at: '2020-01-01T00:00:00.000Z',
            protected: false,
          },
        },
      };
    } catch (error) {
      this.logger.error(`Mock token exchange failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Real token exchange
   */
  private async realTokenExchange(code: string, state: string): Promise<any> {
    try {
      this.logger.log('Performing real token exchange');

      // TODO: Implement real Twitter OAuth token exchange
      throw new Error('Real Twitter OAuth token exchange not implemented yet');
    } catch (error) {
      this.logger.error(`Real token exchange failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate mock profile data
   */
  private generateMockProfileData(username: string): any {
    const baseFollowers = Math.floor(Math.random() * 10000) + 100;
    const baseFollowing = Math.floor(Math.random() * 1000) + 50;
    const baseTweets = Math.floor(Math.random() * 5000) + 100;

    return {
      id: `mock_${username}_${Date.now()}`,
      username: username,
      displayName: `Mock ${username.charAt(0).toUpperCase() + username.slice(1)}`,
      description: `Mock Twitter profile for ${username}. This is a development profile with simulated data.`,
      followersCount: baseFollowers,
      followingCount: baseFollowing,
      tweetsCount: baseTweets,
      listedCount: Math.floor(Math.random() * 50),
      verified: Math.random() > 0.8, // 20% chance of being verified
      profileImageUrl: `https://via.placeholder.com/150?text=${username.charAt(0).toUpperCase()}`,
      location: 'Mock City, Mock Country',
      website: `https://${username}.example.com`,
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    };
  }
}
