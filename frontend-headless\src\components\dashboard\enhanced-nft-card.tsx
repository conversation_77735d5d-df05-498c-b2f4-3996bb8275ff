'use client'

import {
  SparklesIcon,
  CalendarIcon,
  HashtagIcon,
  ChartBarIcon,
  GlobeAltIcon,
  CubeIcon,
  EyeIcon,
  PencilIcon,
  ShareIcon,
  TagIcon,
  BanknotesIcon,
  CheckBadgeIcon
} from '@heroicons/react/24/outline'
import { NFT, NFTRarity, NFTStatus, BlockchainNetwork } from '@/types/nft.types'

interface EnhancedNFTCardProps {
  nft: NFT
  onClick?: () => void
  onView?: (nft: NFT) => void
  onEdit?: (nft: NFT) => void
  onShare?: (nft: NFT) => void
  onMint?: (nft: NFT) => void
  onList?: (nft: NFT) => void
  showActions?: boolean
  className?: string
}

export default function EnhancedNFTCard({
  nft,
  onClick,
  onView,
  onEdit,
  onShare,
  onMint,
  onList,
  showActions = true,
  className = ''
}: EnhancedNFTCardProps) {
  const getRarityColor = (rarity: NFTRarity) => {
    switch (rarity) {
      case NFTRarity.MYTHIC:
        return 'bg-gradient-to-br from-purple-600 to-pink-600 text-white'
      case NFTRarity.LEGENDARY:
        return 'bg-gradient-to-br from-yellow-400 to-orange-500 text-white'
      case NFTRarity.EPIC:
        return 'bg-gradient-to-br from-purple-500 to-pink-500 text-white'
      case NFTRarity.RARE:
        return 'bg-gradient-to-br from-blue-500 to-cyan-500 text-white'
      case NFTRarity.COMMON:
        return 'bg-gradient-to-br from-gray-400 to-gray-600 text-white'
      default:
        return 'bg-gradient-to-br from-green-500 to-emerald-500 text-white'
    }
  }

  const getRarityBadgeColor = (rarity: NFTRarity) => {
    switch (rarity) {
      case NFTRarity.MYTHIC:
        return 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-200'
      case NFTRarity.LEGENDARY:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case NFTRarity.EPIC:
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case NFTRarity.RARE:
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case NFTRarity.COMMON:
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-green-100 text-green-800 border-green-200'
    }
  }

  const getStatusColor = (status: NFTStatus) => {
    switch (status) {
      case NFTStatus.MINTED:
        return 'bg-green-100 text-green-800 border-green-200'
      case NFTStatus.LISTED:
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case NFTStatus.SOLD:
        return 'bg-red-100 text-red-800 border-red-200'
      case NFTStatus.GENERATING:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case NFTStatus.MINTING:
        return 'bg-indigo-100 text-indigo-800 border-indigo-200'
      case NFTStatus.GENERATED:
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Generate SVG image if none provided
  const generateFallbackSVG = (rarity: string, score: number, twitterHandle: string) => {
    const colors = getRarityColors(rarity)

    const svgContent = `
      <svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:${colors.primary};stop-opacity:0.8" />
            <stop offset="70%" style="stop-color:${colors.secondary};stop-opacity:0.4" />
            <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
          </radialGradient>
        </defs>

        <rect width="100%" height="100%" fill="url(#bgGradient)" />

        <circle cx="200" cy="200" r="150" fill="none" stroke="${colors.accent}" stroke-width="2" stroke-opacity="0.3" />
        <circle cx="200" cy="200" r="120" fill="none" stroke="${colors.primary}" stroke-width="2" stroke-opacity="0.5" />

        <circle cx="200" cy="200" r="60" fill="none" stroke="${colors.accent}" stroke-width="6" stroke-opacity="0.3" />
        <circle cx="200" cy="200" r="60" fill="none" stroke="${colors.primary}" stroke-width="6"
                stroke-linecap="round" stroke-dasharray="377"
                stroke-dashoffset="${377 - (score / 100) * 377}"
                transform="rotate(-90 200 200)" />

        <text x="200" y="200" text-anchor="middle" dominant-baseline="middle"
              fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
          ${score}
        </text>

        <text x="200" y="225" text-anchor="middle" dominant-baseline="middle"
              fill="white" font-family="Arial, sans-serif" font-size="12">
          SCORE
        </text>

        <rect x="150" y="50" width="100" height="30" fill="${colors.primary}"
              stroke="${colors.accent}" stroke-width="2" rx="5" />
        <text x="200" y="70" text-anchor="middle" dominant-baseline="middle"
              fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
          ${rarity.toUpperCase()}
        </text>

        <rect x="0" y="350" width="400" height="50" fill="rgba(0, 0, 0, 0.7)" />
        <text x="200" y="380" text-anchor="middle" dominant-baseline="middle"
              fill="${colors.accent}" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
          @${twitterHandle}
        </text>
      </svg>
    `.trim()

    return `data:image/svg+xml;base64,${btoa(svgContent)}`
  }

  const getRarityColors = (rarity: NFTRarity) => {
    const colorMap = {
      [NFTRarity.COMMON]: { primary: '#6B7280', secondary: '#9CA3AF', accent: '#D1D5DB' },
      [NFTRarity.RARE]: { primary: '#3B82F6', secondary: '#60A5FA', accent: '#93C5FD' },
      [NFTRarity.EPIC]: { primary: '#8B5CF6', secondary: '#A78BFA', accent: '#C4B5FD' },
      [NFTRarity.LEGENDARY]: { primary: '#F59E0B', secondary: '#FBBF24', accent: '#FCD34D' },
      [NFTRarity.MYTHIC]: { primary: '#DC2626', secondary: '#EF4444', accent: '#F87171' },
    }
    return colorMap[rarity] || colorMap[NFTRarity.COMMON]
  }

  // Get the image to display
  const getImageSrc = () => {
    if (nft.metadata?.image) {
      return nft.metadata.image
    }
    // Generate fallback SVG
    return generateFallbackSVG(nft.rarity, nft.score, nft.twitterHandle)
  }

  const getKeyAttributes = () => {
    if (!nft.metadata?.attributes) return []
    return nft.metadata.attributes.filter(attr => 
      ['Engagement Score', 'Rarity', 'Generation Date'].includes(attr.trait_type)
    ).slice(0, 3)
  }

  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation()
    action()
  }

  return (
    <div
      className={`bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-[1.02] ${className}`}
      onClick={onClick}
    >
      {/* NFT Image/Visual */}
      <div className="h-48 relative overflow-hidden group">
        <img
          src={getImageSrc()}
          alt={nft.name}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          onError={(e) => {
            // Fallback if image fails to load
            const target = e.target as HTMLImageElement
            target.src = generateFallbackSVG(nft.rarity, nft.currentScore, nft.twitterHandle)
          }}
        />

        {/* Rarity Badge */}
        <div className="absolute top-3 right-3">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRarityBadgeColor(nft.rarity)}`}>
            <SparklesIcon className="h-3 w-3 mr-1" />
            {nft.rarity.charAt(0).toUpperCase() + nft.rarity.slice(1)}
          </span>
        </div>

        {/* Score Badge */}
        <div className="absolute top-3 left-3">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-gray-800 border border-gray-200">
            <ChartBarIcon className="h-3 w-3 mr-1" />
            {nft.currentScore}
          </span>
        </div>

        {/* Status Badge */}
        {nft.status && (
          <div className="absolute bottom-3 left-3">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(nft.status)}`}>
              <CubeIcon className="h-3 w-3 mr-1" />
              {nft.status.charAt(0).toUpperCase() + nft.status.slice(1)}
            </span>
          </div>
        )}

        {/* Action Buttons Overlay */}
        {showActions && (
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex space-x-2">
              {onView && (
                <button
                  onClick={(e) => handleActionClick(e, () => onView(nft))}
                  className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  title="View Details"
                >
                  <EyeIcon className="h-4 w-4 text-gray-600" />
                </button>
              )}
              {onEdit && (
                <button
                  onClick={(e) => handleActionClick(e, () => onEdit(nft))}
                  className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  title="Edit NFT"
                >
                  <PencilIcon className="h-4 w-4 text-gray-600" />
                </button>
              )}
              {onShare && (
                <button
                  onClick={(e) => handleActionClick(e, () => onShare(nft))}
                  className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  title="Share NFT"
                >
                  <ShareIcon className="h-4 w-4 text-gray-600" />
                </button>
              )}
              {onMint && nft.status === NFTStatus.GENERATED && (
                <button
                  onClick={(e) => handleActionClick(e, () => onMint(nft))}
                  className="p-2 bg-blue-600 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                  title="Mint NFT"
                >
                  <TagIcon className="h-4 w-4 text-white" />
                </button>
              )}
              {onList && nft.status === NFTStatus.MINTED && (
                <button
                  onClick={(e) => handleActionClick(e, () => onList(nft))}
                  className="p-2 bg-green-600 rounded-full shadow-lg hover:bg-green-700 transition-colors"
                  title="List for Sale"
                >
                  <BanknotesIcon className="h-4 w-4 text-white" />
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* NFT Details */}
      <div className="p-4">
        {/* Header */}
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {nft.name}
          </h3>
          <p className="text-sm text-gray-600 flex items-center mt-1">
            <span className="mr-2">@{nft.twitterHandle}</span>
            {nft.tokenId && (
              <>
                <HashtagIcon className="h-3 w-3 mr-1" />
                <span className="text-xs">#{nft.tokenId}</span>
              </>
            )}
          </p>
        </div>

        {/* Description */}
        {nft.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {nft.description}
          </p>
        )}

        {/* Key Attributes */}
        {getKeyAttributes().length > 0 && (
          <div className="mb-3">
            <div className="grid grid-cols-1 gap-2">
              {getKeyAttributes().map((attr, index) => (
                <div key={index} className="flex justify-between items-center text-xs">
                  <span className="text-gray-500">{attr.trait_type}:</span>
                  <span className="font-medium text-gray-900">
                    {attr.display_type === 'number' ? attr.value : attr.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Evolution Indicator */}
        {nft.evolution && (
          <div className="mb-3 p-2 bg-purple-50 rounded-lg border border-purple-200">
            <div className="flex items-center text-xs text-purple-700">
              <SparklesIcon className="h-3 w-3 mr-1" />
              <span className="font-medium">Evolved NFT</span>
            </div>
            <div className="text-xs text-purple-600 mt-1">
              Score: {nft.evolution.previousScore} → {nft.evolution.newScore}
            </div>
          </div>
        )}

        {/* Marketplace Data */}
        {nft.marketplaceData?.isListed && (
          <div className="mb-3 p-2 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center justify-between text-xs">
              <span className="text-green-700 font-medium">Listed for Sale</span>
              <span className="text-green-800 font-bold">
                {nft.marketplaceData.price} {nft.marketplaceData.currency}
              </span>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center text-xs text-gray-500">
            <CalendarIcon className="h-3 w-3 mr-1" />
            {formatDate(nft.createdAt)}
          </div>

          <div className="flex items-center space-x-1">
            {nft.blockchain && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                <GlobeAltIcon className="h-3 w-3 mr-1" />
                {nft.blockchain.charAt(0).toUpperCase() + nft.blockchain.slice(1)}
              </span>
            )}

            {nft.isMinted && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                <CheckBadgeIcon className="h-3 w-3 mr-1" />
                Minted
              </span>
            )}
          </div>
        </div>

        {/* External Link */}
        {nft.metadata?.external_url && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <a
              href={nft.metadata.external_url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-600 hover:text-blue-500 flex items-center"
              onClick={(e) => e.stopPropagation()}
            >
              <GlobeAltIcon className="h-3 w-3 mr-1" />
              View Details
            </a>
          </div>
        )}
      </div>
    </div>
  )
}
