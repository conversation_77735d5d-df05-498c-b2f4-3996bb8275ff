'use client'

import React from 'react'
import { MarketplaceListing, ListingType, ListingStatus } from '@/types/marketplace.types'
import {
  ClockIcon,
  EyeIcon,
  HeartIcon,
  TagIcon,
  CurrencyDollarIcon,
  UserIcon,
  SparklesIcon,
  FireIcon
} from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

interface MarketplaceListingCardProps {
  listing: MarketplaceListing
  onView?: (listing: MarketplaceListing) => void
  onFavorite?: (listing: MarketplaceListing) => void
  onMakeOffer?: (listing: MarketplaceListing) => void
  onBuyNow?: (listing: MarketplaceListing) => void
  isFavorited?: boolean
  showActions?: boolean
  variant?: 'default' | 'compact' | 'featured'
  className?: string
}

export default function MarketplaceListingCard({
  listing,
  onView,
  onFavorite,
  onMakeOffer,
  onBuyNow,
  isFavorited = false,
  showActions = true,
  variant = 'default',
  className = ''
}: MarketplaceListingCardProps) {

  const getStatusColor = (status: ListingStatus) => {
    switch (status) {
      case ListingStatus.ACTIVE:
        return 'bg-green-100 text-green-800 border-green-200'
      case ListingStatus.SOLD:
        return 'bg-red-100 text-red-800 border-red-200'
      case ListingStatus.CANCELLED:
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case ListingStatus.EXPIRED:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  const getListingTypeIcon = (type: ListingType) => {
    switch (type) {
      case ListingType.AUCTION:
        return <ClockIcon className="h-3 w-3" />
      case ListingType.DUTCH_AUCTION:
        return <FireIcon className="h-3 w-3" />
      default:
        return <TagIcon className="h-3 w-3" />
    }
  }

  const formatPrice = (price: number, currency: string) => {
    return `${price.toFixed(3)} ${currency}`
  }

  const formatTimeRemaining = (endTime: string) => {
    const now = new Date()
    const end = new Date(endTime)
    const diff = end.getTime() - now.getTime()
    
    if (diff <= 0) return 'Ended'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (days > 0) return `${days}d ${hours}h`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation()
    action()
  }

  // Compact variant for list views
  if (variant === 'compact') {
    return (
      <div 
        className={`bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer ${className}`}
        onClick={() => onView?.(listing)}
      >
        <div className="flex items-center space-x-4">
          {/* NFT Image */}
          <div className="flex-shrink-0">
            <img
              src={listing.nft?.imageUrl || '/api/placeholder/60/60'}
              alt={listing.title}
              className="w-15 h-15 rounded-lg object-cover"
            />
          </div>

          {/* Listing Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-base font-semibold text-gray-900 truncate">
                  {listing.title}
                </h3>
                <p className="text-sm text-gray-600">by {listing.sellerUsername || 'Unknown'}</p>
              </div>
              
              <div className="text-right">
                <div className="text-lg font-bold text-gray-900">
                  {formatPrice(listing.price, listing.currency)}
                </div>
                {listing.listingType === ListingType.AUCTION && listing.auctionEndTime && (
                  <div className="text-xs text-gray-500">
                    {formatTimeRemaining(listing.auctionEndTime)}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Featured variant for hero sections
  if (variant === 'featured') {
    return (
      <div 
        className={`bg-white border-2 border-yellow-200 rounded-xl overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-[1.02] ${className}`}
        onClick={() => onView?.(listing)}
      >
        {/* Featured Badge */}
        <div className="absolute top-4 left-4 z-10">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
            <SparklesIcon className="h-3 w-3 mr-1" />
            Featured
          </span>
        </div>

        {/* NFT Image */}
        <div className="relative h-64 overflow-hidden">
          <img
            src={listing.nft?.imageUrl || '/api/placeholder/400/400'}
            alt={listing.title}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
          />
          
          {/* Action Buttons Overlay */}
          {showActions && (
            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 hover:opacity-100">
              <div className="flex space-x-2">
                <button
                  onClick={(e) => handleActionClick(e, () => onView?.(listing))}
                  className="p-3 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  title="View Details"
                >
                  <EyeIcon className="h-5 w-5 text-gray-600" />
                </button>
                <button
                  onClick={(e) => handleActionClick(e, () => onFavorite?.(listing))}
                  className="p-3 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  title="Add to Favorites"
                >
                  {isFavorited ? (
                    <HeartSolidIcon className="h-5 w-5 text-red-500" />
                  ) : (
                    <HeartIcon className="h-5 w-5 text-gray-600" />
                  )}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Listing Details */}
        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-1">{listing.title}</h3>
              <p className="text-sm text-gray-600">by {listing.sellerUsername || 'Unknown'}</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">
                {formatPrice(listing.price, listing.currency)}
              </div>
              {listing.marketData?.priceChange24h && (
                <div className={`text-sm ${listing.marketData.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {listing.marketData.priceChange24h >= 0 ? '+' : ''}{listing.marketData.priceChange24h.toFixed(1)}%
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          {showActions && listing.status === ListingStatus.ACTIVE && (
            <div className="flex space-x-3">
              {listing.listingType === ListingType.FIXED_PRICE && (
                <button
                  onClick={(e) => handleActionClick(e, () => onBuyNow?.(listing))}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
                >
                  Buy Now
                </button>
              )}
              {listing.acceptOffers && (
                <button
                  onClick={(e) => handleActionClick(e, () => onMakeOffer?.(listing))}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
                >
                  Make Offer
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    )
  }

  // Default card variant
  return (
    <div 
      className={`bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-[1.02] ${className}`}
      onClick={() => onView?.(listing)}
    >
      {/* NFT Image */}
      <div className="relative h-48 overflow-hidden group">
        <img
          src={listing.nft?.imageUrl || '/api/placeholder/300/300'}
          alt={listing.title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />

        {/* Status Badge */}
        <div className="absolute top-3 right-3">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(listing.status)}`}>
            {listing.status.charAt(0).toUpperCase() + listing.status.slice(1)}
          </span>
        </div>

        {/* Listing Type Badge */}
        <div className="absolute top-3 left-3">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-gray-800 border border-gray-200">
            {getListingTypeIcon(listing.listingType)}
            <span className="ml-1">
              {listing.listingType === ListingType.AUCTION ? 'Auction' : 
               listing.listingType === ListingType.DUTCH_AUCTION ? 'Dutch' : 'Fixed'}
            </span>
          </span>
        </div>

        {/* Auction Timer */}
        {listing.listingType === ListingType.AUCTION && listing.auctionEndTime && (
          <div className="absolute bottom-3 left-3">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
              <ClockIcon className="h-3 w-3 mr-1" />
              {formatTimeRemaining(listing.auctionEndTime)}
            </span>
          </div>
        )}

        {/* Action Buttons Overlay */}
        {showActions && (
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex space-x-2">
              <button
                onClick={(e) => handleActionClick(e, () => onView?.(listing))}
                className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                title="View Details"
              >
                <EyeIcon className="h-4 w-4 text-gray-600" />
              </button>
              <button
                onClick={(e) => handleActionClick(e, () => onFavorite?.(listing))}
                className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                title="Add to Favorites"
              >
                {isFavorited ? (
                  <HeartSolidIcon className="h-4 w-4 text-red-500" />
                ) : (
                  <HeartIcon className="h-4 w-4 text-gray-600" />
                )}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Listing Details */}
      <div className="p-4">
        {/* Header */}
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {listing.title}
          </h3>
          <div className="flex items-center justify-between mt-1">
            <p className="text-sm text-gray-600 flex items-center">
              <UserIcon className="h-3 w-3 mr-1" />
              {listing.sellerUsername || 'Unknown'}
            </p>
            {listing.nft && (
              <span className="text-xs text-gray-500">
                {listing.nft.rarity} • Score: {listing.nft.currentScore}
              </span>
            )}
          </div>
        </div>

        {/* Price */}
        <div className="mb-3">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-xl font-bold text-gray-900">
                {formatPrice(listing.price, listing.currency)}
              </div>
              {listing.marketData?.lastSalePrice && (
                <div className="text-xs text-gray-500">
                  Last: {formatPrice(listing.marketData.lastSalePrice, listing.currency)}
                </div>
              )}
            </div>
            {listing.marketData?.priceChange24h !== undefined && (
              <div className={`text-sm font-medium ${listing.marketData.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {listing.marketData.priceChange24h >= 0 ? '+' : ''}{listing.marketData.priceChange24h.toFixed(1)}%
              </div>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
          <div className="flex items-center space-x-3">
            <span className="flex items-center">
              <EyeIcon className="h-3 w-3 mr-1" />
              {listing.viewCount}
            </span>
            <span className="flex items-center">
              <HeartIcon className="h-3 w-3 mr-1" />
              {listing.favoriteCount}
            </span>
            {listing.offerCount > 0 && (
              <span className="flex items-center">
                <TagIcon className="h-3 w-3 mr-1" />
                {listing.offerCount} offers
              </span>
            )}
          </div>
          <span>
            {new Date(listing.createdAt).toLocaleDateString()}
          </span>
        </div>

        {/* Action Buttons */}
        {showActions && listing.status === ListingStatus.ACTIVE && (
          <div className="flex space-x-2">
            {listing.listingType === ListingType.FIXED_PRICE && (
              <button
                onClick={(e) => handleActionClick(e, () => onBuyNow?.(listing))}
                className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
              >
                Buy Now
              </button>
            )}
            {listing.acceptOffers && (
              <button
                onClick={(e) => handleActionClick(e, () => onMakeOffer?.(listing))}
                className="flex-1 px-3 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
              >
                Make Offer
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
