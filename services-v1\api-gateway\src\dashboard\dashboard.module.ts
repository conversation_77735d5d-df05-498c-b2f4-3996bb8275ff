import { Modu<PERSON> } from '@nestjs/common';
import { DashboardController } from './dashboard.controller';
import { ServiceDiscoveryModule } from '../service-discovery/service-discovery.module';
import { CircuitBreakerModule } from '../circuit-breaker/circuit-breaker.module';
import { LoadBalancerModule } from '../load-balancer/load-balancer.module';
import { CacheModule } from '../cache/cache.module';
import { RateLimitModule } from '../rate-limit/rate-limit.module';
import { ProxyV2Module } from '../proxy-v2/proxy-v2.module';

/**
 * Dashboard Module
 * 
 * Provides comprehensive monitoring and management dashboard
 * for all API Gateway enterprise features.
 */
@Module({
  imports: [
    ServiceDiscoveryModule,
    CircuitBreakerModule,
    LoadBalancerModule,
    CacheModule,
    RateLimitModule,
    ProxyV2Module,
  ],
  controllers: [DashboardController],
})
export class DashboardModule {}
