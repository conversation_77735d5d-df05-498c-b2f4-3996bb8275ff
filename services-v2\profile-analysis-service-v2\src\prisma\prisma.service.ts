/**
 * Prisma Service - Profile Analysis Service V2
 * 
 * Database service for profile_analysis_service_v2 database
 * Database Per Service Pattern - Only connects to its own database
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  constructor() {
    super({
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
      log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      console.log('✅ Connected to profile_analysis_service_v2 database');
    } catch (error) {
      console.error('❌ Failed to connect to profile_analysis_service_v2 database:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    await this.$disconnect();
    console.log('🔌 Disconnected from profile_analysis_service_v2 database');
  }

  /**
   * Health check for database connection
   */
  async healthCheck() {
    try {
      await this.$queryRaw`SELECT 1`;
      return {
        status: 'healthy',
        database: 'profile_analysis_service_v2',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        database: 'profile_analysis_service_v2',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats() {
    try {
      const [
        analysisCount,
        metricsCount,
        insightsCount,
        recommendationsCount,
        queueCount,
      ] = await Promise.all([
        this.profileAnalysis.count(),
        this.profileMetric.count(),
        this.profileInsight.count(),
        this.profileRecommendation.count(),
        this.analysisQueue.count(),
      ]);

      return {
        analyses: analysisCount,
        metrics: metricsCount,
        insights: insightsCount,
        recommendations: recommendationsCount,
        queuedAnalyses: queueCount,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to get database stats: ${error.message}`);
    }
  }
}
