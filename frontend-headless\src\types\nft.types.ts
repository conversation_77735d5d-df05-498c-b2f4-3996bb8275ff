// NFT Types for Frontend Integration
export interface NFT {
  id: string
  userId: string
  campaignId?: string
  twitterHandle: string
  name: string
  description: string
  imageUrl: string
  rarity: NFTRarity
  currentScore: number
  status: NFTStatus
  blockchain?: BlockchainNetwork
  contractAddress?: string
  tokenId?: string
  isMinted: boolean
  mintedAt?: string
  createdAt: string
  updatedAt: string
  metadata?: NFTMetadata
  evolution?: NFTEvolution
  marketplaceData?: MarketplaceData
}

export enum NFTRarity {
  COMMON = 'common',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary',
  MYTHIC = 'mythic'
}

export enum NFTStatus {
  GENERATING = 'generating',
  GENERATED = 'generated',
  MINTING = 'minting',
  MINTED = 'minted',
  LISTED = 'listed',
  SOLD = 'sold',
  TRANSFERRED = 'transferred',
  BURNED = 'burned'
}

export enum BlockchainNetwork {
  ETHEREUM = 'ethereum',
  POLYGON = 'polygon',
  BSC = 'bsc',
  BASE = 'base'
}

export interface NFTMetadata {
  name: string
  description: string
  image: string
  external_url?: string
  attributes: NFTAttribute[]
  animation_url?: string
  background_color?: string
}

export interface NFTAttribute {
  trait_type: string
  value: string | number
  display_type?: 'boost_number' | 'boost_percentage' | 'number' | 'date'
  max_value?: number
}

export interface NFTEvolution {
  id: string
  nftId: string
  previousScore: number
  newScore: number
  previousRarity: NFTRarity
  newRarity: NFTRarity
  trigger: EvolutionTrigger
  evolutionDate: string
  changes: EvolutionChange[]
}

export interface EvolutionTrigger {
  type: 'score_increase' | 'time_based' | 'engagement_boost' | 'manual'
  threshold?: number
  description: string
}

export interface EvolutionChange {
  attribute: string
  oldValue: string | number
  newValue: string | number
  changeType: 'visual' | 'metadata' | 'rarity'
}

export interface MarketplaceData {
  isListed: boolean
  listingId?: string
  price?: number
  currency?: string
  listedAt?: string
  lastSalePrice?: number
  lastSaleDate?: string
  viewCount?: number
  favoriteCount?: number
}

export interface NFTCollection {
  userId: string
  nfts: NFT[]
  stats: CollectionStats
  pagination: PaginationInfo
}

export interface CollectionStats {
  totalNFTs: number
  rarityBreakdown: Record<NFTRarity, number>
  statusBreakdown: Record<NFTStatus, number>
  blockchainBreakdown: Record<BlockchainNetwork, number>
  totalValue: number
  averageScore: number
  evolutionCount: number
  mintedCount: number
  listedCount: number
}

export interface PaginationInfo {
  page: number
  limit: number
  totalCount: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface NFTAnalytics {
  userId: string
  timeframe: string
  metrics: {
    totalGenerated: number
    totalMinted: number
    totalListed: number
    totalSold: number
    averageScore: number
    scoreDistribution: Record<string, number>
    rarityDistribution: Record<NFTRarity, number>
    evolutionEvents: number
    marketplaceActivity: {
      listings: number
      sales: number
      totalVolume: number
      averagePrice: number
    }
  }
  trends: {
    scoreChanges: Array<{
      date: string
      averageScore: number
      count: number
    }>
    generationActivity: Array<{
      date: string
      generated: number
      minted: number
    }>
    marketplaceActivity: Array<{
      date: string
      listings: number
      sales: number
      volume: number
    }>
  }
}

export interface NFTFilters {
  rarity?: NFTRarity[]
  status?: NFTStatus[]
  blockchain?: BlockchainNetwork[]
  scoreRange?: {
    min: number
    max: number
  }
  dateRange?: {
    start: string
    end: string
  }
  searchQuery?: string
  isMinted?: boolean
  isListed?: boolean
}

export interface NFTSortOptions {
  field: 'createdAt' | 'updatedAt' | 'currentScore' | 'rarity' | 'name'
  order: 'asc' | 'desc'
}

// API Request/Response Types
export interface GetNFTCollectionRequest {
  userId: string
  filters?: NFTFilters
  sort?: NFTSortOptions
  pagination?: {
    page: number
    limit: number
  }
}

export interface UpdateNFTRequest {
  name?: string
  description?: string
  metadata?: Partial<NFTMetadata>
  customization?: {
    style?: string
    theme?: string
    colors?: string[]
  }
}

export interface MintNFTRequest {
  nftId: string
  blockchain: BlockchainNetwork
  contractAddress?: string
  gasPrice?: string
  gasLimit?: number
}

export interface NFTGenerationRequest {
  userId: string
  analysisId: string
  campaignId?: string
  customization?: {
    style?: string
    theme?: string
    rarity?: NFTRarity
  }
}

// Component Props Types
export interface NFTCardProps {
  nft: NFT
  showActions?: boolean
  onSelect?: (nftId: string) => void
  onView?: (nft: NFT) => void
  onEdit?: (nft: NFT) => void
  onMint?: (nft: NFT) => void
  onList?: (nft: NFT) => void
  className?: string
}

export interface NFTCollectionProps {
  userId?: string
  enableFilters?: boolean
  enableSorting?: boolean
  enableBulkActions?: boolean
  viewMode?: 'grid' | 'list'
  pageSize?: number
  onNFTSelect?: (nft: NFT) => void
  onNFTAction?: (action: string, nft: NFT) => void
}

export interface NFTFiltersProps {
  filters: NFTFilters
  onFiltersChange: (filters: NFTFilters) => void
  availableRarities: NFTRarity[]
  availableStatuses: NFTStatus[]
  availableBlockchains: BlockchainNetwork[]
  showAdvanced?: boolean
}

// Error Types
export interface NFTError {
  code: string
  message: string
  details?: any
}

// Loading States
export interface NFTLoadingState {
  isLoading: boolean
  isRefreshing: boolean
  error: NFTError | null
  lastUpdated: string | null
}
