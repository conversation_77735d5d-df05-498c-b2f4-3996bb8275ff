import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../common/services/cache.service';
import { UserAnalyticsDto, UserEngagementMetrics, UserSocialMetrics, UserRankingInfo, UserActivityHistory } from '../dto/user-analytics.dto';
import { RequestContext } from '../interfaces/request-context.interface';

export interface UserAnalyticsResult {
  success: boolean;
  data?: UserAnalyticsDto;
  error?: string;
}

@Injectable()
export class UserAnalyticsService {
  private readonly logger = new Logger(UserAnalyticsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * Get comprehensive user analytics
   */
  async getUserAnalytics(userId: string, context: RequestContext): Promise<UserAnalyticsResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;
    const cacheKey = `user:analytics:${userId}`;
    
    try {
      this.logger.log(`Getting analytics for user: ${userId}`, { correlationId });
      
      // Try cache first (5 minutes TTL for analytics)
      const cachedAnalytics = await this.cacheService.get<UserAnalyticsDto>(cacheKey);
      if (cachedAnalytics) {
        this.logger.debug(`Cache hit for user analytics: ${userId}`, { correlationId });
        return { success: true, data: cachedAnalytics };
      }
      
      // Verify user exists
      const user = await this.prisma.userQuery.findUnique({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }
      
      // Generate analytics data
      const analytics: UserAnalyticsDto = {
        userId,
        engagement: await this.calculateEngagementMetrics(userId),
        social: await this.calculateSocialMetrics(userId),
        ranking: await this.calculateRankingInfo(userId),
        recentActivity: await this.getRecentActivity(userId),
        totalPoints: await this.calculateTotalPoints(userId),
        currentLevel: await this.calculateCurrentLevel(userId),
        progressToNextLevel: await this.calculateProgressToNextLevel(userId),
        achievements: await this.getUserAchievements(userId),
        generatedAt: new Date().toISOString(),
      };
      
      // Cache the result (5 minutes TTL)
      await this.cacheService.set(cacheKey, analytics, 300);
      
      this.logger.log(`Analytics generated successfully for user: ${userId}`, { 
        correlationId,
        duration: Date.now() - startTime 
      });
      
      return { success: true, data: analytics };
      
    } catch (error) {
      this.logger.error(`Failed to get analytics for user ${userId}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });
      
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Calculate user engagement metrics
   */
  private async calculateEngagementMetrics(userId: string): Promise<UserEngagementMetrics> {
    // Mock implementation - in production, these would query actual data
    // from campaign participation, NFT generation, and marketplace services
    
    return {
      totalCampaigns: 5,
      totalNftsGenerated: 12,
      totalNftsOwned: 8,
      totalTransactions: 3,
      totalPortfolioValue: 2.5,
      averageEngagementScore: 85.5,
      currentStreak: 7,
      longestStreak: 21,
    };
  }

  /**
   * Calculate social media metrics
   */
  private async calculateSocialMetrics(userId: string): Promise<UserSocialMetrics> {
    // Get user's Twitter data if available
    const user = await this.prisma.userQuery.findUnique({
      where: { id: userId },
      select: { twitterUsername: true },
    });
    
    // Mock implementation - in production, this would integrate with
    // Profile Analysis Service to get real social media metrics
    
    return {
      twitterFollowers: 1500,
      twitterFollowing: 800,
      twitterEngagementRate: 3.2,
      totalInteractions: 450,
      influenceScore: 72.8,
    };
  }

  /**
   * Calculate user ranking information
   */
  private async calculateRankingInfo(userId: string): Promise<UserRankingInfo> {
    // Mock implementation - in production, this would calculate
    // actual rankings based on engagement scores and activity
    
    return {
      globalRank: 142,
      rankChange7d: 5,
      rankChange30d: -12,
      percentile: 85.5,
    };
  }

  /**
   * Get recent user activity
   */
  private async getRecentActivity(userId: string): Promise<UserActivityHistory[]> {
    // Mock implementation - in production, this would query
    // activity logs from various services
    
    return [
      {
        date: '2025-06-03',
        activityType: 'campaign_participation',
        description: 'Participated in CryptoPunks campaign',
        pointsEarned: 50,
      },
      {
        date: '2025-06-02',
        activityType: 'nft_generation',
        description: 'Generated Rare NFT',
        pointsEarned: 100,
      },
      {
        date: '2025-06-01',
        activityType: 'marketplace_transaction',
        description: 'Purchased NFT for 0.5 ETH',
        pointsEarned: 25,
      },
    ];
  }

  /**
   * Calculate total points earned by user
   */
  private async calculateTotalPoints(userId: string): Promise<number> {
    // Mock implementation - in production, this would sum
    // points from all activities and achievements
    
    return 2450;
  }

  /**
   * Calculate current user level
   */
  private async calculateCurrentLevel(userId: string): Promise<string> {
    const totalPoints = await this.calculateTotalPoints(userId);
    
    if (totalPoints >= 5000) return 'Diamond';
    if (totalPoints >= 3000) return 'Platinum';
    if (totalPoints >= 2000) return 'Gold';
    if (totalPoints >= 1000) return 'Silver';
    return 'Bronze';
  }

  /**
   * Calculate progress to next level
   */
  private async calculateProgressToNextLevel(userId: string): Promise<number> {
    const totalPoints = await this.calculateTotalPoints(userId);
    const currentLevel = await this.calculateCurrentLevel(userId);
    
    let nextLevelThreshold: number;
    let currentLevelThreshold: number;
    
    switch (currentLevel) {
      case 'Bronze':
        currentLevelThreshold = 0;
        nextLevelThreshold = 1000;
        break;
      case 'Silver':
        currentLevelThreshold = 1000;
        nextLevelThreshold = 2000;
        break;
      case 'Gold':
        currentLevelThreshold = 2000;
        nextLevelThreshold = 3000;
        break;
      case 'Platinum':
        currentLevelThreshold = 3000;
        nextLevelThreshold = 5000;
        break;
      case 'Diamond':
        return 100; // Max level reached
      default:
        return 0;
    }
    
    const progress = ((totalPoints - currentLevelThreshold) / (nextLevelThreshold - currentLevelThreshold)) * 100;
    return Math.min(Math.max(progress, 0), 100);
  }

  /**
   * Get user achievements
   */
  private async getUserAchievements(userId: string): Promise<string[]> {
    // Mock implementation - in production, this would check
    // achievement conditions and return earned achievements
    
    return ['First NFT', 'Campaign Master', 'Social Butterfly'];
  }

  /**
   * Invalidate user analytics cache
   */
  async invalidateUserAnalyticsCache(userId: string): Promise<void> {
    const cacheKey = `user:analytics:${userId}`;
    await this.cacheService.del(cacheKey);
    this.logger.debug(`Invalidated analytics cache for user: ${userId}`);
  }
}
