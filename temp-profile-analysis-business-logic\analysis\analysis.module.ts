import { Module } from '@nestjs/common';

// Enhanced analysis services
import { EngagementCalculatorService } from './services/engagement-calculator.service';
import { InfluenceMetricsService } from './services/influence-metrics.service';
import { SentimentAnalyzerService } from './services/sentiment-analyzer.service';
import { HistoricalAnalyzerService } from './services/historical-analyzer.service';

@Module({
  providers: [
    EngagementCalculatorService,
    InfluenceMetricsService,
    SentimentAnalyzerService,
    HistoricalAnalyzerService,
  ],
  exports: [
    EngagementCalculatorService,
    InfluenceMetricsService,
    SentimentAnalyzerService,
    HistoricalAnalyzerService,
  ],
})
export class AnalysisModule {}
