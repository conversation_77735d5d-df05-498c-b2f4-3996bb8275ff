{"name": "analytics-service", "version": "1.0.0", "description": "Analytics Service - Platform Analytics & Insights", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@nestjs/axios": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^11.0.0", "@prisma/client": "^6.8.2", "axios": "^1.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "prisma": "^6.8.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@types/node": "^20.0.0", "rimraf": "^5.0.0", "typescript": "^5.1.3"}}