'use client'

import React, { useState } from 'react'
import {
  SparklesIcon,
  ChartBarIcon,
  ClockIcon,
  TrophyIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'
import { useEvolutionTimeline, useEvolutionEvents } from '@/hooks/useEvolution'
import { EvolutionEvent, EvolutionEventType, EvolutionImpact } from '@/types/evolution.types'
import { NFTRarity } from '@/types/nft.types'

interface NFTEvolutionTimelineProps {
  nftId: string
  showFilters?: boolean
  maxEvents?: number
  className?: string
}

export default function NFTEvolutionTimeline({
  nftId,
  showFilters = true,
  maxEvents = 20,
  className = ''
}: NFTEvolutionTimelineProps) {
  const [selectedEventType, setSelectedEventType] = useState<EvolutionEventType | 'all'>('all')
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('all')

  const { data: timeline, isLoading: timelineLoading } = useEvolutionTimeline(nftId)
  const { data: eventsData, isLoading: eventsLoading } = useEvolutionEvents(nftId, {
    eventTypes: selectedEventType !== 'all' ? [selectedEventType] : undefined,
    dateRange: timeRange !== 'all' ? {
      start: new Date(Date.now() - getTimeRangeMs(timeRange)).toISOString(),
      end: new Date().toISOString()
    } : undefined
  })

  const isLoading = timelineLoading || eventsLoading
  const events = eventsData?.events?.slice(0, maxEvents) || []

  function getTimeRangeMs(range: string): number {
    switch (range) {
      case '7d': return 7 * 24 * 60 * 60 * 1000
      case '30d': return 30 * 24 * 60 * 60 * 1000
      case '90d': return 90 * 24 * 60 * 60 * 1000
      default: return 0
    }
  }

  const getEventIcon = (eventType: EvolutionEventType) => {
    switch (eventType) {
      case EvolutionEventType.RARITY_EVOLUTION:
        return <SparklesIcon className="h-5 w-5" />
      case EvolutionEventType.SCORE_UPDATE:
        return <ChartBarIcon className="h-5 w-5" />
      case EvolutionEventType.MILESTONE_REACHED:
        return <TrophyIcon className="h-5 w-5" />
      case EvolutionEventType.ATTRIBUTE_CHANGE:
        return <ArrowUpIcon className="h-5 w-5" />
      default:
        return <ClockIcon className="h-5 w-5" />
    }
  }

  const getEventColor = (eventType: EvolutionEventType, impact: EvolutionImpact) => {
    const baseColors = {
      [EvolutionEventType.RARITY_EVOLUTION]: 'purple',
      [EvolutionEventType.SCORE_UPDATE]: 'blue',
      [EvolutionEventType.MILESTONE_REACHED]: 'yellow',
      [EvolutionEventType.ATTRIBUTE_CHANGE]: 'green',
      [EvolutionEventType.VISUAL_UPDATE]: 'pink',
      [EvolutionEventType.METADATA_UPDATE]: 'gray'
    }

    const color = baseColors[eventType] || 'gray'
    const intensity = impact === EvolutionImpact.LEGENDARY ? '600' : 
                     impact === EvolutionImpact.MAJOR ? '500' : 
                     impact === EvolutionImpact.MODERATE ? '400' : '300'

    return `bg-${color}-${intensity} text-white`
  }

  const getRarityColor = (rarity: NFTRarity) => {
    switch (rarity) {
      case NFTRarity.MYTHIC: return 'text-purple-600'
      case NFTRarity.LEGENDARY: return 'text-yellow-600'
      case NFTRarity.EPIC: return 'text-purple-500'
      case NFTRarity.RARE: return 'text-blue-500'
      case NFTRarity.COMMON: return 'text-gray-500'
      default: return 'text-gray-500'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffMinutes = Math.floor(diffMs / (1000 * 60))

    if (diffDays > 0) return `${diffDays}d ago`
    if (diffHours > 0) return `${diffHours}h ago`
    if (diffMinutes > 0) return `${diffMinutes}m ago`
    return 'Just now'
  }

  const getScoreChange = (event: EvolutionEvent) => {
    const scoreDiff = event.newState.score - event.previousState.score
    if (scoreDiff === 0) return null
    
    return (
      <span className={`inline-flex items-center text-sm font-medium ${
        scoreDiff > 0 ? 'text-green-600' : 'text-red-600'
      }`}>
        {scoreDiff > 0 ? <ArrowUpIcon className="h-3 w-3 mr-1" /> : <ArrowDownIcon className="h-3 w-3 mr-1" />}
        {Math.abs(scoreDiff)}
      </span>
    )
  }

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex space-x-4">
              <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <ClockIcon className="h-5 w-5 mr-2 text-gray-500" />
              Evolution Timeline
            </h3>
            {timeline && (
              <p className="text-sm text-gray-600 mt-1">
                {timeline.totalEvolutions} evolutions • {timeline.evolutionRate.toFixed(1)} per month
              </p>
            )}
          </div>
          
          {timeline?.nextPredictedEvolution && (
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">Next Evolution</p>
              <p className="text-xs text-gray-500">
                {new Date(timeline.nextPredictedEvolution.estimatedDate).toLocaleDateString()}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="px-6 py-3 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-4">
            <select
              value={selectedEventType}
              onChange={(e) => setSelectedEventType(e.target.value as EvolutionEventType | 'all')}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Events</option>
              <option value={EvolutionEventType.RARITY_EVOLUTION}>Rarity Evolution</option>
              <option value={EvolutionEventType.SCORE_UPDATE}>Score Updates</option>
              <option value={EvolutionEventType.MILESTONE_REACHED}>Milestones</option>
              <option value={EvolutionEventType.ATTRIBUTE_CHANGE}>Attribute Changes</option>
            </select>

            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d' | 'all')}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Time</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </div>
        </div>
      )}

      {/* Timeline */}
      <div className="p-6">
        {events.length === 0 ? (
          <div className="text-center py-8">
            <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No evolution events</h3>
            <p className="mt-1 text-sm text-gray-500">
              Evolution events will appear here as your NFT grows and changes.
            </p>
          </div>
        ) : (
          <div className="flow-root">
            <ul className="-mb-8">
              {events.map((event, eventIdx) => (
                <li key={event.id}>
                  <div className="relative pb-8">
                    {eventIdx !== events.length - 1 ? (
                      <span
                        className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                        aria-hidden="true"
                      />
                    ) : null}
                    <div className="relative flex space-x-3">
                      <div>
                        <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                          getEventColor(event.eventType, event.changes[0]?.impact || EvolutionImpact.MINOR)
                        }`}>
                          {getEventIcon(event.eventType)}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {event.eventType === EvolutionEventType.RARITY_EVOLUTION && 
                                `Evolved to ${event.newState.rarity}`}
                              {event.eventType === EvolutionEventType.SCORE_UPDATE && 
                                `Score updated to ${event.newState.score}`}
                              {event.eventType === EvolutionEventType.MILESTONE_REACHED && 
                                'Milestone reached'}
                              {event.eventType === EvolutionEventType.ATTRIBUTE_CHANGE && 
                                'Attributes updated'}
                              {event.eventType === EvolutionEventType.VISUAL_UPDATE && 
                                'Visual appearance updated'}
                              {event.eventType === EvolutionEventType.METADATA_UPDATE && 
                                'Metadata updated'}
                            </p>
                            <div className="flex items-center space-x-2 mt-1">
                              <p className="text-xs text-gray-500">
                                {event.trigger.description}
                              </p>
                              {getScoreChange(event)}
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-gray-500 flex items-center">
                              <CalendarIcon className="h-3 w-3 mr-1" />
                              {formatDate(event.timestamp)}
                            </p>
                          </div>
                        </div>

                        {/* Event Details */}
                        {event.eventType === EvolutionEventType.RARITY_EVOLUTION && (
                          <div className="mt-2 flex items-center space-x-4 text-sm">
                            <span className={`font-medium ${getRarityColor(event.previousState.rarity)}`}>
                              {event.previousState.rarity}
                            </span>
                            <ArrowUpIcon className="h-4 w-4 text-gray-400" />
                            <span className={`font-medium ${getRarityColor(event.newState.rarity)}`}>
                              {event.newState.rarity}
                            </span>
                          </div>
                        )}

                        {/* Changes Summary */}
                        {event.changes.length > 0 && (
                          <div className="mt-2">
                            <div className="flex flex-wrap gap-1">
                              {event.changes.slice(0, 3).map((change, idx) => (
                                <span
                                  key={idx}
                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                                >
                                  {change.attribute}: {String(change.oldValue)} → {String(change.newValue)}
                                </span>
                              ))}
                              {event.changes.length > 3 && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                  +{event.changes.length - 3} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Load More */}
        {eventsData && eventsData.total > events.length && (
          <div className="mt-6 text-center">
            <button className="text-sm text-blue-600 hover:text-blue-500 font-medium">
              <EyeIcon className="h-4 w-4 inline mr-1" />
              View {eventsData.total - events.length} more events
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
