// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String?
  status      String   @default("active")
  ownerId     String
  isPublic    Boolean  @default(true)
  blockchainNetwork String?
  marketValue Float?
  participants Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  campaigns   Campaign[]

  @@map("projects")
}

model Campaign {
  id          String   @id @default(cuid())
  name        String
  description String?
  status      String   @default("active")
  projectId   String
  startDate   DateTime?
  endDate     DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("campaigns")
}
