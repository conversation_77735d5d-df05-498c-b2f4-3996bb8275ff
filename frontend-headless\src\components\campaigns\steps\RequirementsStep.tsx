'use client'

import React, { useState } from 'react'
import {
  PlusIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { CreateCampaignRequest, RequirementType, CampaignRequirement } from '@/types/campaign.types'

interface RequirementsStepProps {
  data: Partial<CreateCampaignRequest>
  updateData: (updates: Partial<CreateCampaignRequest>) => void
  errors: string[]
}

export default function RequirementsStep({ data, updateData, errors }: RequirementsStepProps) {
  const [showAddRequirement, setShowAddRequirement] = useState(false)

  const requirementTypes = [
    {
      type: RequirementType.TWITTER_FOLLOW,
      label: 'Follow on Twitter',
      description: 'Participants must follow a Twitter account',
      icon: '🐦',
      config: ['twitterUsername']
    },
    {
      type: RequirementType.TWITTER_RETWEET,
      label: 'Retweet Post',
      description: 'Participants must retweet a specific post',
      icon: '🔄',
      config: ['tweetUrl']
    },
    {
      type: RequirementType.TWITTER_LIKE,
      label: 'Like Tweet',
      description: 'Participants must like a specific tweet',
      icon: '❤️',
      config: ['tweetUrl']
    },
    {
      type: RequirementType.TWITTER_COMMENT,
      label: 'Comment on Tweet',
      description: 'Participants must comment on a tweet',
      icon: '💬',
      config: ['tweetUrl', 'hashtags']
    },
    {
      type: RequirementType.DISCORD_JOIN,
      label: 'Join Discord Server',
      description: 'Participants must join a Discord server',
      icon: '💬',
      config: ['serverUrl']
    },
    {
      type: RequirementType.TELEGRAM_JOIN,
      label: 'Join Telegram Group',
      description: 'Participants must join a Telegram group',
      icon: '📱',
      config: ['serverUrl']
    },
    {
      type: RequirementType.WEBSITE_VISIT,
      label: 'Visit Website',
      description: 'Participants must visit a specific website',
      icon: '🌐',
      config: ['websiteUrl', 'timeOnSite']
    },
    {
      type: RequirementType.EMAIL_SIGNUP,
      label: 'Email Signup',
      description: 'Participants must sign up with email',
      icon: '📧',
      config: []
    },
    {
      type: RequirementType.WALLET_CONNECT,
      label: 'Connect Wallet',
      description: 'Participants must connect their crypto wallet',
      icon: '👛',
      config: []
    },
    {
      type: RequirementType.NFT_HOLD,
      label: 'Hold NFT',
      description: 'Participants must hold specific NFTs',
      icon: '🖼️',
      config: ['nftCollectionAddress']
    },
    {
      type: RequirementType.TOKEN_HOLD,
      label: 'Hold Tokens',
      description: 'Participants must hold specific tokens',
      icon: '🪙',
      config: ['contractAddress', 'tokenAmount']
    },
    {
      type: RequirementType.CONTENT_CREATION,
      label: 'Create Content',
      description: 'Participants must create and submit content',
      icon: '🎨',
      config: ['customInstructions', 'submissionType']
    },
    {
      type: RequirementType.REFERRAL,
      label: 'Refer Friends',
      description: 'Participants must refer other users',
      icon: '👥',
      config: []
    },
    {
      type: RequirementType.CUSTOM_TASK,
      label: 'Custom Task',
      description: 'Define a custom requirement',
      icon: '⚙️',
      config: ['customInstructions', 'submissionType']
    }
  ]

  const addRequirement = (type: RequirementType) => {
    const newRequirement: Omit<CampaignRequirement, 'id' | 'campaignId' | 'createdAt' | 'updatedAt'> = {
      type,
      title: requirementTypes.find(rt => rt.type === type)?.label || '',
      description: requirementTypes.find(rt => rt.type === type)?.description || '',
      isRequired: true,
      order: (data.requirements?.length || 0) + 1,
      configuration: {},
      validationRules: [],
      points: 10
    }

    updateData({
      requirements: [...(data.requirements || []), newRequirement]
    })
    setShowAddRequirement(false)
  }

  const updateRequirement = (index: number, updates: Partial<CampaignRequirement>) => {
    const requirements = [...(data.requirements || [])]
    requirements[index] = { ...requirements[index], ...updates }
    updateData({ requirements })
  }

  const removeRequirement = (index: number) => {
    const requirements = [...(data.requirements || [])]
    requirements.splice(index, 1)
    // Reorder remaining requirements
    requirements.forEach((req, i) => {
      req.order = i + 1
    })
    updateData({ requirements })
  }

  const moveRequirement = (index: number, direction: 'up' | 'down') => {
    const requirements = [...(data.requirements || [])]
    const newIndex = direction === 'up' ? index - 1 : index + 1
    
    if (newIndex >= 0 && newIndex < requirements.length) {
      [requirements[index], requirements[newIndex]] = [requirements[newIndex], requirements[index]]
      // Update order
      requirements.forEach((req, i) => {
        req.order = i + 1
      })
      updateData({ requirements })
    }
  }

  const getConfigurationFields = (requirement: any, index: number) => {
    const reqType = requirementTypes.find(rt => rt.type === requirement.type)
    if (!reqType) return null

    return (
      <div className="mt-4 space-y-3">
        {reqType.config.includes('twitterUsername') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Twitter Username
            </label>
            <input
              type="text"
              value={requirement.configuration.twitterUsername || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, twitterUsername: e.target.value }
              })}
              placeholder="@username"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {reqType.config.includes('tweetUrl') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tweet URL
            </label>
            <input
              type="url"
              value={requirement.configuration.tweetUrl || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, tweetUrl: e.target.value }
              })}
              placeholder="https://twitter.com/username/status/..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {reqType.config.includes('serverUrl') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {requirement.type === RequirementType.DISCORD_JOIN ? 'Discord Invite URL' : 'Telegram Group URL'}
            </label>
            <input
              type="url"
              value={requirement.configuration.serverUrl || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, serverUrl: e.target.value }
              })}
              placeholder={requirement.type === RequirementType.DISCORD_JOIN ? 'https://discord.gg/...' : 'https://t.me/...'}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {reqType.config.includes('websiteUrl') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Website URL
            </label>
            <input
              type="url"
              value={requirement.configuration.websiteUrl || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, websiteUrl: e.target.value }
              })}
              placeholder="https://example.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {reqType.config.includes('timeOnSite') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Minimum Time on Site (seconds)
            </label>
            <input
              type="number"
              value={requirement.configuration.timeOnSite || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, timeOnSite: parseInt(e.target.value) }
              })}
              placeholder="30"
              min="1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {reqType.config.includes('contractAddress') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Token Contract Address
            </label>
            <input
              type="text"
              value={requirement.configuration.contractAddress || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, contractAddress: e.target.value }
              })}
              placeholder="0x..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {reqType.config.includes('tokenAmount') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Minimum Token Amount
            </label>
            <input
              type="number"
              value={requirement.configuration.tokenAmount || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, tokenAmount: parseFloat(e.target.value) }
              })}
              placeholder="100"
              min="0"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {reqType.config.includes('nftCollectionAddress') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              NFT Collection Address
            </label>
            <input
              type="text"
              value={requirement.configuration.nftCollectionAddress || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, nftCollectionAddress: e.target.value }
              })}
              placeholder="0x..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {reqType.config.includes('customInstructions') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Instructions for Participants
            </label>
            <textarea
              rows={3}
              value={requirement.configuration.customInstructions || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, customInstructions: e.target.value }
              })}
              placeholder="Provide detailed instructions for what participants need to do..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {reqType.config.includes('submissionType') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Submission Type
            </label>
            <select
              value={requirement.configuration.submissionType || 'text'}
              onChange={(e) => updateRequirement(index, {
                configuration: { ...requirement.configuration, submissionType: e.target.value }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="text">Text</option>
              <option value="image">Image</option>
              <option value="video">Video</option>
              <option value="link">Link</option>
            </select>
          </div>
        )}

        {reqType.config.includes('hashtags') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Required Hashtags (comma-separated)
            </label>
            <input
              type="text"
              value={requirement.configuration.hashtags?.join(', ') || ''}
              onChange={(e) => updateRequirement(index, {
                configuration: { 
                  ...requirement.configuration, 
                  hashtags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                }
              })}
              placeholder="#hashtag1, #hashtag2"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Requirements List */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Campaign Requirements</h3>
          <button
            onClick={() => setShowAddRequirement(true)}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Requirement
          </button>
        </div>

        {data.requirements && data.requirements.length > 0 ? (
          <div className="space-y-4">
            {data.requirements.map((requirement, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <span className="text-2xl">
                        {requirementTypes.find(rt => rt.type === requirement.type)?.icon}
                      </span>
                      <div className="flex-1">
                        <input
                          type="text"
                          value={requirement.title}
                          onChange={(e) => updateRequirement(index, { title: e.target.value })}
                          className="text-lg font-medium text-gray-900 border-none p-0 focus:ring-0 w-full"
                          placeholder="Requirement title"
                        />
                        <textarea
                          value={requirement.description}
                          onChange={(e) => updateRequirement(index, { description: e.target.value })}
                          className="text-sm text-gray-600 border-none p-0 focus:ring-0 w-full mt-1"
                          placeholder="Requirement description"
                          rows={2}
                        />
                      </div>
                    </div>

                    {getConfigurationFields(requirement, index)}

                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Points Reward
                        </label>
                        <input
                          type="number"
                          value={requirement.points}
                          onChange={(e) => updateRequirement(index, { points: parseInt(e.target.value) })}
                          min="0"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                        />
                      </div>
                      <div className="flex items-end">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={requirement.isRequired}
                            onChange={(e) => updateRequirement(index, { isRequired: e.target.checked })}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-700">Required</span>
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-2 ml-4">
                    <button
                      onClick={() => moveRequirement(index, 'up')}
                      disabled={index === 0}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    >
                      <ArrowUpIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => moveRequirement(index, 'down')}
                      disabled={index === (data.requirements?.length || 0) - 1}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    >
                      <ArrowDownIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => removeRequirement(index)}
                      className="p-1 text-red-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
            <ExclamationCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No requirements added</h3>
            <p className="mt-1 text-sm text-gray-500">
              Add requirements to define what participants need to do to complete your campaign.
            </p>
          </div>
        )}
      </div>

      {/* Add Requirement Modal */}
      {showAddRequirement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Add Requirement</h3>
              <button
                onClick={() => setShowAddRequirement(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {requirementTypes.map((reqType) => (
                <button
                  key={reqType.type}
                  onClick={() => addRequirement(reqType.type)}
                  className="text-left p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                >
                  <div className="flex items-start space-x-3">
                    <span className="text-2xl">{reqType.icon}</span>
                    <div>
                      <h4 className="font-medium text-gray-900">{reqType.label}</h4>
                      <p className="text-sm text-gray-600 mt-1">{reqType.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-blue-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Requirements Best Practices</h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Start with simple requirements (follow, like) before complex ones</li>
                <li>Balance required vs optional tasks to maximize participation</li>
                <li>Assign appropriate point values based on task difficulty</li>
                <li>Provide clear instructions for custom tasks</li>
                <li>Test all social media links before launching</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
