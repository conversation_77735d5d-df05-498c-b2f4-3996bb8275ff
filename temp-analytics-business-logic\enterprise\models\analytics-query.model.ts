// Enterprise Analytics Query Model (Read Side) - Template
import { ApiProperty } from '@nestjs/swagger';

export class AnalyticsQueryDto {
  @ApiProperty({ description: 'Analytics ID' })
  id: string;

  @ApiProperty({ description: 'Display name' })
  displayName: string;

  @ApiProperty({ description: 'Display value' })
  displayValue: string;

  @ApiProperty({ description: 'Metric type' })
  metricType: string;

  @ApiProperty({ description: 'Calculated value' })
  calculatedValue: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;
}
