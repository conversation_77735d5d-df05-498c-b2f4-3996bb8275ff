/**
 * Standardized JWT Authentication Guard
 * Provides consistent JWT authentication across all services
 */

import { 
  Injectable, 
  CanActivate, 
  ExecutionContext, 
  UnauthorizedException,
  Logger,
  Inject
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { 
  AuthenticatedUser, 
  TokenValidationResult, 
  IAuthService,
  AuthEventType,
  SecurityEventType,
  SecuritySeverity
} from '../interfaces/auth.interface';

/**
 * Standardized JWT Authentication Guard
 */
@Injectable()
export class StandardizedJwtAuthGuard implements CanActivate {
  private readonly logger = new Logger(StandardizedJwtAuthGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
    @Inject('AUTH_SERVICE') private readonly authService: IAuthService,
  ) {}

  /**
   * Determine if the request can proceed
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      // Check if the route is marked as public
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        this.logger.debug('Route is public, allowing access');
        return true;
      }

      const request = context.switchToHttp().getRequest<Request>();
      
      // Extract token from request
      const token = this.extractTokenFromHeader(request);
      if (!token) {
        this.logger.warn('No authentication token provided');
        await this.logSecurityEvent(request, SecurityEventType.UNAUTHORIZED_ACCESS, 'No token provided');
        throw new UnauthorizedException('Authentication token required');
      }

      // Validate token and get user
      const validationResult = await this.validateToken(token, request);
      if (!validationResult.success || !validationResult.data) {
        this.logger.warn('Token validation failed', { error: validationResult.error });
        await this.logSecurityEvent(request, SecurityEventType.INVALID_TOKEN, validationResult.error);
        throw new UnauthorizedException(validationResult.error || 'Invalid or expired token');
      }

      // Add user and session to request context
      const { user, session, tokenInfo } = validationResult.data;
      request.user = user;
      request.session = session;
      request.tokenInfo = tokenInfo;

      // Add correlation tracking
      if (!request.correlationId) {
        request.correlationId = this.generateCorrelationId();
      }

      // Log successful authentication
      await this.logAuthEvent(request, AuthEventType.LOGIN, true, user.id);

      this.logger.debug('Authentication successful', { 
        userId: user.id, 
        sessionId: session?.sessionId 
      });

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error('Authentication guard error', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Extract JWT token from request header
   */
  private extractTokenFromHeader(request: Request): string | null {
    const authHeader = request.headers.authorization;
    
    if (!authHeader) {
      return null;
    }

    // Support both "Bearer token" and "token" formats
    if (authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Direct token (for backward compatibility)
    return authHeader;
  }

  /**
   * Validate JWT token
   */
  private async validateToken(token: string, request: Request): Promise<TokenValidationResult> {
    try {
      // First, verify JWT signature and structure
      const decoded = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      if (!decoded || !decoded.sub) {
        return {
          success: false,
          error: 'Invalid token structure',
        };
      }

      // Use auth service for comprehensive validation
      const context = {
        ipAddress: this.getClientIP(request),
        userAgent: request.headers['user-agent'] || '',
        requestId: request.correlationId || this.generateCorrelationId(),
        endpoint: request.path,
        method: request.method,
      };

      return await this.authService.validateToken(token, context);
    } catch (error) {
      this.logger.warn('Token validation error', { error: error.message });
      
      if (error.name === 'TokenExpiredError') {
        return {
          success: false,
          error: 'Token has expired',
        };
      }

      if (error.name === 'JsonWebTokenError') {
        return {
          success: false,
          error: 'Invalid token format',
        };
      }

      return {
        success: false,
        error: 'Token validation failed',
      };
    }
  }

  /**
   * Get client IP address
   */
  private getClientIP(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * Generate correlation ID
   */
  private generateCorrelationId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log authentication event
   */
  private async logAuthEvent(
    request: Request, 
    type: AuthEventType, 
    success: boolean, 
    userId?: string
  ): Promise<void> {
    try {
      // Implementation would depend on your audit service
      // This is a placeholder for the audit logging
      this.logger.log('Auth event', {
        type,
        success,
        userId,
        ipAddress: this.getClientIP(request),
        userAgent: request.headers['user-agent'],
        endpoint: request.path,
        method: request.method,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error('Failed to log auth event', error);
    }
  }

  /**
   * Log security event
   */
  private async logSecurityEvent(
    request: Request,
    type: SecurityEventType,
    description: string,
    severity: SecuritySeverity = SecuritySeverity.MEDIUM
  ): Promise<void> {
    try {
      // Implementation would depend on your audit service
      this.logger.warn('Security event', {
        type,
        severity,
        description,
        ipAddress: this.getClientIP(request),
        userAgent: request.headers['user-agent'],
        endpoint: request.path,
        method: request.method,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error('Failed to log security event', error);
    }
  }
}

/**
 * Optional JWT Authentication Guard (allows both authenticated and anonymous access)
 */
@Injectable()
export class OptionalJwtAuthGuard extends StandardizedJwtAuthGuard {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      return await super.canActivate(context);
    } catch (error) {
      // If authentication fails, allow anonymous access
      // The user object will be undefined in the request
      console.log('Optional authentication failed, allowing anonymous access');
      return true;
    }
  }
}

/**
 * API Key Authentication Guard (for service-to-service communication)
 */
@Injectable()
export class ApiKeyAuthGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyAuthGuard.name);

  constructor(
    private readonly configService: ConfigService,
    @Inject('AUTH_SERVICE') private readonly authService: IAuthService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    
    // Extract API key from header
    const apiKey = request.headers['x-api-key'] as string;
    if (!apiKey) {
      throw new UnauthorizedException('API key required');
    }

    // Validate API key
    const validationResult = await this.authService.validateToken(apiKey, {
      type: 'api_key',
      ipAddress: this.getClientIP(request),
      userAgent: request.headers['user-agent'] || '',
    });

    if (!validationResult.success) {
      throw new UnauthorizedException('Invalid API key');
    }

    // Add service context to request
    request.serviceContext = validationResult.data;
    
    return true;
  }

  private getClientIP(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      request.connection.remoteAddress ||
      'unknown'
    );
  }
}
