# 🤖 AI Agent Configuration Guide

**Comprehensive Configuration for AI Assistants Working on Social NFT Platform**

## 🎯 Overview

This document provides specific instructions for AI agents (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, etc.) to ensure they generate code that complies with our enterprise standardization patterns.

---

## 🚨 CRITICAL AI AGENT RULES

### **BEFORE GENERATING ANY CODE:**

1. ✅ **ALWAYS** validate against our 6-phase standardization
2. ✅ **ALWAYS** use our mandatory templates
3. ✅ **ALWAYS** include proper imports from shared modules
4. ✅ **NEVER** generate code that bypasses established patterns
5. ✅ **NEVER** use deprecated or non-standard approaches

---

## 📋 AI AGENT SYSTEM PROMPT

### **Use This System Prompt for All AI Agents:**

```
You are an expert TypeScript/NestJS developer working on the Social NFT Platform. You MUST follow these enterprise standardization rules:

MANDATORY PATTERNS:
1. Environment: Use ConfigService, never process.env directly
2. Authentication: Always use JwtAuthGuard, RolesGuard, PermissionsGuard
3. Responses: Always use ResponseService for API responses
4. Logging: Always use ServiceLoggerService, never console.log
5. Data: Always use repository pattern, never direct Prisma access
6. Validation: Always include proper TypeScript types and validation

FORBIDDEN PATTERNS:
- Direct process.env access
- console.log statements
- Raw API responses
- Custom authentication
- Direct database access
- Hardcoded values

REQUIRED IMPORTS:
- ConfigService from @nestjs/config
- Guards from shared/auth/guards/
- ResponseService from responses/services/
- ServiceLoggerService from logging/services/
- BaseRepository from shared/data/repositories/

Always generate code that follows these patterns exactly.
```

---

## 🔧 CODE GENERATION TEMPLATES

### **1. Service Generation Template**

```typescript
// Template for generating services
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServiceLoggerService } from '../logging/services/service-logger.service';
import { {{Entity}}Repository } from '../data/repositories/{{entity}}.repository';
import { BusinessOutcome } from '../../../shared/logging/interfaces/logger.interface';
import { Create{{Entity}}Dto, Update{{Entity}}Dto } from './dto/{{entity}}.dto';
import { {{Entity}} } from './interfaces/{{entity}}.interface';

@Injectable()
export class {{Entity}}Service {
  constructor(
    private readonly {{entity}}Repository: {{Entity}}Repository,
    private readonly serviceLogger: ServiceLoggerService,
    private readonly configService: ConfigService,
  ) {}

  async create(createDto: Create{{Entity}}Dto): Promise<{{Entity}}> {
    try {
      this.serviceLogger.logOperationStart('{{entity}}_creation', {
        metadata: { ...createDto }
      });

      const result = await this.{{entity}}Repository.create(createDto);

      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'creation',
        BusinessOutcome.SUCCESS,
        { {{entity}}Id: result.id }
      );

      return result;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'creation',
        BusinessOutcome.FAILURE,
        { error: error.message }
      );
      throw error;
    }
  }

  async findAll(pagination: any, filters?: any, sorting?: any): Promise<any> {
    try {
      this.serviceLogger.logOperationStart('{{entity}}_list_retrieval');

      const result = await this.{{entity}}Repository.paginate(
        { where: filters },
        pagination
      );

      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'list_retrieval',
        BusinessOutcome.SUCCESS,
        { count: result.data.length }
      );

      return result;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'list_retrieval',
        BusinessOutcome.FAILURE,
        { error: error.message }
      );
      throw error;
    }
  }

  async findById(id: string): Promise<{{Entity}} | null> {
    try {
      this.serviceLogger.logOperationStart('{{entity}}_retrieval', {
        metadata: { id }
      });

      const result = await this.{{entity}}Repository.findById(id);

      if (result) {
        this.serviceLogger.logBusinessEvent(
          '{{entity}}',
          'retrieval',
          BusinessOutcome.SUCCESS,
          { {{entity}}Id: id }
        );
      }

      return result;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'retrieval',
        BusinessOutcome.FAILURE,
        { error: error.message, {{entity}}Id: id }
      );
      throw error;
    }
  }

  async update(id: string, updateDto: Update{{Entity}}Dto): Promise<{{Entity}}> {
    try {
      this.serviceLogger.logOperationStart('{{entity}}_update', {
        metadata: { id, ...updateDto }
      });

      const result = await this.{{entity}}Repository.update(id, updateDto);

      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'update',
        BusinessOutcome.SUCCESS,
        { {{entity}}Id: id }
      );

      return result;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'update',
        BusinessOutcome.FAILURE,
        { error: error.message, {{entity}}Id: id }
      );
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      this.serviceLogger.logOperationStart('{{entity}}_deletion', {
        metadata: { id }
      });

      const result = await this.{{entity}}Repository.delete(id);

      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'deletion',
        BusinessOutcome.SUCCESS,
        { {{entity}}Id: id }
      );

      return result;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'deletion',
        BusinessOutcome.FAILURE,
        { error: error.message, {{entity}}Id: id }
      );
      throw error;
    }
  }
}
```

### **2. Controller Generation Template**

```typescript
// Template for generating controllers
import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards 
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../shared/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { PermissionsGuard } from '../../../shared/auth/guards/permissions.guard';
import { RequirePermissions } from '../../../shared/auth/decorators/auth.decorator';
import { Permission } from '../../../shared/auth/interfaces/permission.interface';
import { 
  ApiCreateResponse,
  ApiReadResponse,
  ApiUpdateResponse,
  ApiDeleteResponse,
  ApiListResponse 
} from '../../../shared/responses/decorators/api-response.decorator';
import { ResponseService } from '../responses/services/response.service';
import { PaginationService } from '../responses/services/pagination.service';
import { {{Entity}}Service } from './{{entity}}.service';
import { Create{{Entity}}Dto, Update{{Entity}}Dto } from './dto/{{entity}}.dto';
import { {{Entity}}Dto } from './dto/{{entity}}.dto';

@Controller('{{entities}}')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@ApiTags('{{Entities}}')
export class {{Entity}}Controller {
  constructor(
    private readonly {{entity}}Service: {{Entity}}Service,
    private readonly responseService: ResponseService,
    private readonly paginationService: PaginationService,
  ) {}

  @Get()
  @RequirePermissions(Permission.{{ENTITY}}_READ)
  @ApiOperation({ summary: 'Get {{entities}} list with pagination' })
  @ApiListResponse({{Entity}}Dto, '{{Entities}} retrieved successfully')
  async findAll(@Query() query: any) {
    const { pagination, filters, sorting } = this.paginationService.parseQuery(query);
    const result = await this.{{entity}}Service.findAll(pagination, filters, sorting);
    
    return this.responseService.paginated(
      result.data,
      result.pagination,
      { 
        filters, 
        sorting, 
        message: '{{Entities}} retrieved successfully' 
      }
    );
  }

  @Get(':id')
  @RequirePermissions(Permission.{{ENTITY}}_READ)
  @ApiOperation({ summary: 'Get {{entity}} by ID' })
  @ApiReadResponse({{Entity}}Dto, '{{Entity}} retrieved successfully')
  async findOne(@Param('id') id: string) {
    const result = await this.{{entity}}Service.findById(id);
    
    if (!result) {
      return this.responseService.notFound('{{Entity}}', id);
    }
    
    return this.responseService.success(result, '{{Entity}} retrieved successfully');
  }

  @Post()
  @RequirePermissions(Permission.{{ENTITY}}_WRITE)
  @ApiOperation({ summary: 'Create new {{entity}}' })
  @ApiCreateResponse({{Entity}}Dto, '{{Entity}} created successfully')
  async create(@Body() createDto: Create{{Entity}}Dto) {
    const result = await this.{{entity}}Service.create(createDto);
    return this.responseService.created(result, '{{Entity}} created successfully');
  }

  @Put(':id')
  @RequirePermissions(Permission.{{ENTITY}}_WRITE)
  @ApiOperation({ summary: 'Update {{entity}}' })
  @ApiUpdateResponse({{Entity}}Dto, '{{Entity}} updated successfully')
  async update(
    @Param('id') id: string,
    @Body() updateDto: Update{{Entity}}Dto
  ) {
    const result = await this.{{entity}}Service.update(id, updateDto);
    return this.responseService.updated(result, '{{Entity}} updated successfully');
  }

  @Delete(':id')
  @RequirePermissions(Permission.{{ENTITY}}_DELETE)
  @ApiOperation({ summary: 'Delete {{entity}}' })
  @ApiDeleteResponse('{{Entity}} deleted successfully')
  async remove(@Param('id') id: string) {
    const result = await this.{{entity}}Service.delete(id);
    
    if (!result) {
      return this.responseService.notFound('{{Entity}}', id);
    }
    
    return this.responseService.deleted('{{Entity}} deleted successfully');
  }
}
```

### **3. Repository Generation Template**

```typescript
// Template for generating repositories
import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../shared/data/repositories/base.repository';
import { StandardizedPrismaService } from '../../../shared/data/services/prisma.service';
import { ServiceLoggerService } from '../logging/services/service-logger.service';
import { MetricsService } from '../../../shared/logging/services/metrics.service';
import { ConfigService } from '@nestjs/config';
import { {{Entity}} } from './interfaces/{{entity}}.interface';
import { Create{{Entity}}Dto, Update{{Entity}}Dto } from './dto/{{entity}}.dto';

@Injectable()
export class {{Entity}}Repository extends BaseRepository<{{Entity}}, Create{{Entity}}Dto, Update{{Entity}}Dto> {
  constructor(
    prisma: StandardizedPrismaService,
    serviceLogger: ServiceLoggerService,
    metricsService: MetricsService,
    configService: ConfigService,
  ) {
    super(prisma, serviceLogger, metricsService, configService, '{{Entity}}');
  }

  /**
   * Get the Prisma model delegate
   */
  protected getModel() {
    return this.prisma.{{entity}};
  }

  /**
   * Transform entity to DTO
   */
  protected toDto(entity: any): {{Entity}} {
    return {
      id: entity.id,
      // Add other properties based on your entity
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  /**
   * Transform create DTO to entity data
   */
  protected toCreateData(dto: Create{{Entity}}Dto): any {
    return {
      // Map DTO properties to entity data
      // Example: name: dto.name,
    };
  }

  /**
   * Transform update DTO to entity data
   */
  protected toUpdateData(dto: Update{{Entity}}Dto): any {
    const data: any = {};
    
    // Only include defined properties
    // Example:
    // if (dto.name !== undefined) data.name = dto.name;
    
    return data;
  }

  /**
   * Custom repository methods
   */
  async findByName(name: string): Promise<{{Entity}} | null> {
    return this.findOne({
      where: { name } as any,
    });
  }

  async findActive(): Promise<{{Entity}}[]> {
    return this.findMany({
      where: { 
        // Add your active condition
        // active: true 
      } as any,
    });
  }
}
```

---

## 🔍 AI VALIDATION CHECKLIST

### **Before Generating Code, Verify:**

#### **✅ Required Imports**
```typescript
// Configuration
import { ConfigService } from '@nestjs/config';

// Authentication
import { JwtAuthGuard } from '../../../shared/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { PermissionsGuard } from '../../../shared/auth/guards/permissions.guard';
import { RequirePermissions } from '../../../shared/auth/decorators/auth.decorator';

// Responses
import { ResponseService } from '../responses/services/response.service';
import { PaginationService } from '../responses/services/pagination.service';

// Logging
import { ServiceLoggerService } from '../logging/services/service-logger.service';

// Data
import { BaseRepository } from '../../../shared/data/repositories/base.repository';
import { StandardizedPrismaService } from '../../../shared/data/services/prisma.service';
```

#### **✅ Required Patterns**
1. **Configuration Access**: `this.configService.get<string>('KEY')`
2. **Authentication**: `@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)`
3. **Authorization**: `@RequirePermissions(Permission.ENTITY_READ)`
4. **Response Format**: `this.responseService.success(data, message)`
5. **Logging**: `this.serviceLogger.logBusinessEvent(...)`
6. **Data Access**: `this.entityRepository.findById(id)`

#### **❌ Forbidden Patterns**
1. **Direct Environment**: `process.env.KEY`
2. **Console Logging**: `console.log()`, `console.error()`
3. **Raw Responses**: `return data;`
4. **Direct Database**: `this.prisma.entity.findMany()`
5. **Hardcoded Values**: `const API_URL = 'https://...'`

---

## 🎯 AI AGENT SPECIFIC CONFIGURATIONS

### **For ChatGPT/GPT-4**
```
Custom Instructions:
- Always follow Social NFT Platform enterprise standards
- Use TypeScript with strict typing
- Implement NestJS patterns with dependency injection
- Include proper error handling and logging
- Use repository pattern for data access
- Never use console.log or direct process.env access
```

### **For Claude**
```
System Message:
You are a senior TypeScript developer working on an enterprise NestJS platform. 
Follow these mandatory patterns:
1. ConfigService for configuration
2. ResponseService for API responses  
3. ServiceLoggerService for logging
4. Repository pattern for data access
5. JWT authentication with RBAC
Never deviate from these patterns.
```

### **For GitHub Copilot**
```javascript
// .vscode/settings.json
{
  "github.copilot.enable": {
    "*": true,
    "yaml": false,
    "plaintext": false
  },
  "github.copilot.advanced": {
    "debug.overrideEngine": "codex",
    "debug.testOverrideProxyUrl": "",
    "debug.overrideProxyUrl": "",
    "length": 500,
    "temperature": 0.1,
    "top_p": 1,
    "stops": {
      "*": ["\n\n\n"]
    }
  }
}
```

---

## 📝 EXAMPLE PROMPTS FOR AI AGENTS

### **Service Generation Prompt**
```
Generate a UserService for the Social NFT Platform following our enterprise standards:
- Use ConfigService for configuration
- Use ServiceLoggerService for logging
- Use UserRepository for data access
- Include proper error handling
- Log business events for all operations
- Include CRUD operations: create, findAll, findById, update, delete
- Follow our standardized patterns exactly
```

### **Controller Generation Prompt**
```
Generate a UserController for the Social NFT Platform following our enterprise standards:
- Use JwtAuthGuard, RolesGuard, PermissionsGuard
- Use ResponseService for all responses
- Use PaginationService for list endpoints
- Include proper API documentation decorators
- Implement CRUD endpoints with proper permissions
- Follow our standardized response format
```

### **Repository Generation Prompt**
```
Generate a UserRepository for the Social NFT Platform following our enterprise standards:
- Extend BaseRepository
- Use StandardizedPrismaService
- Include proper logging and metrics
- Implement toDto, toCreateData, toUpdateData methods
- Add custom methods like findByEmail
- Follow our repository pattern exactly
```

---

## 🚨 AI AGENT ERROR PREVENTION

### **Common AI Mistakes to Prevent:**

1. **❌ Generating Direct Database Access**
   ```typescript
   // AI might generate this - REJECT
   return this.prisma.user.findMany();
   
   // Should be this - ACCEPT
   return this.userRepository.findMany();
   ```

2. **❌ Generating Console Logging**
   ```typescript
   // AI might generate this - REJECT
   console.log('User created');
   
   // Should be this - ACCEPT
   this.serviceLogger.logBusinessEvent('user', 'creation', BusinessOutcome.SUCCESS);
   ```

3. **❌ Generating Raw Responses**
   ```typescript
   // AI might generate this - REJECT
   return users;
   
   // Should be this - ACCEPT
   return this.responseService.success(users, 'Users retrieved');
   ```

### **AI Validation Script**
```typescript
// Use this to validate AI-generated code
function validateAICode(code: string): boolean {
  const violations = [];
  
  if (code.includes('process.env')) violations.push('Direct process.env access');
  if (code.includes('console.log')) violations.push('Console logging');
  if (code.includes('this.prisma.') && !code.includes('StandardizedPrismaService')) violations.push('Direct Prisma access');
  if (!code.includes('ResponseService') && code.includes('@Controller')) violations.push('Missing ResponseService');
  if (!code.includes('ServiceLoggerService') && code.includes('@Injectable')) violations.push('Missing ServiceLoggerService');
  
  if (violations.length > 0) {
    console.error('AI Code Violations:', violations);
    return false;
  }
  
  return true;
}
```

---

## 📚 AI AGENT TRAINING RESOURCES

### **Required Reading for AI Configuration:**
1. [Enterprise Standardization Guide](./ENTERPRISE_STANDARDIZATION_GUIDE.md)
2. [Development Rules](./DEVELOPMENT_RULES_AND_ENFORCEMENT.md)
3. [Code Examples](../examples/)

### **AI Agent Testing:**
- Test AI agents with sample prompts
- Validate generated code against standards
- Iterate on prompts until compliance is achieved
- Document successful prompt patterns

### **Continuous Improvement:**
- Monitor AI-generated code quality
- Update prompts based on common mistakes
- Share successful configurations across team
- Regular AI agent training updates

---

## 🎯 SUCCESS METRICS

### **AI Agent Compliance Targets:**
- **95%** first-time code compliance
- **0%** critical pattern violations
- **100%** proper import usage
- **90%** complete feature implementation

### **Monitoring:**
- Track AI-generated code review feedback
- Measure time to compliance
- Monitor pattern adoption rates
- Analyze common AI mistakes

---

**Remember: AI agents are tools that must be properly configured to follow our enterprise standards. Proper configuration ensures consistent, secure, and maintainable code generation.**
