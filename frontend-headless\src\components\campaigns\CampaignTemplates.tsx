'use client'

import React, { useState } from 'react'
import {
  DocumentDuplicateIcon,
  StarIcon,
  ClockIcon,
  UsersIcon,
  TagIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'
import { useCampaignTemplates, useCreateCampaignFromTemplate } from '@/hooks/useCampaigns'
import { CampaignTemplate, CampaignType } from '@/types/campaign.types'

interface CampaignTemplatesProps {
  projectId: string
  onTemplateSelect?: (campaign: any) => void
  onCreateFromScratch?: () => void
  className?: string
}

export default function CampaignTemplates({
  projectId,
  onTemplateSelect,
  onCreateFromScratch,
  className = ''
}: CampaignTemplatesProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState<CampaignType | 'all'>('all')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all')

  const { data: templates, isLoading } = useCampaignTemplates({
    search: searchQuery,
    type: selectedType !== 'all' ? selectedType : undefined,
    category: selectedCategory !== 'all' ? selectedCategory : undefined,
    difficulty: selectedDifficulty !== 'all' ? selectedDifficulty : undefined
  })

  const createFromTemplateMutation = useCreateCampaignFromTemplate()

  const campaignTypes = [
    { value: 'all', label: 'All Types' },
    { value: CampaignType.SOCIAL_ENGAGEMENT, label: 'Social Engagement' },
    { value: CampaignType.CONTENT_CREATION, label: 'Content Creation' },
    { value: CampaignType.COMMUNITY_BUILDING, label: 'Community Building' },
    { value: CampaignType.TRADING_ACTIVITY, label: 'Trading Activity' },
    { value: CampaignType.REFERRAL_PROGRAM, label: 'Referral Program' },
    { value: CampaignType.SEASONAL_EVENT, label: 'Seasonal Event' }
  ]

  const categories = [
    'All Categories',
    'Marketing',
    'Community',
    'Growth',
    'Engagement',
    'Acquisition',
    'Retention',
    'Brand Awareness'
  ]

  const difficulties = [
    'All Difficulties',
    'Easy',
    'Medium',
    'Hard'
  ]

  const handleUseTemplate = async (template: CampaignTemplate) => {
    try {
      const campaign = await createFromTemplateMutation.mutateAsync({
        templateId: template.id,
        data: {
          projectId,
          name: `${template.name} - ${new Date().toLocaleDateString()}`,
          description: template.description,
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        }
      })
      onTemplateSelect?.(campaign)
    } catch (error) {
      console.error('Failed to create campaign from template:', error)
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'hard': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeIcon = (type: CampaignType) => {
    switch (type) {
      case CampaignType.SOCIAL_ENGAGEMENT: return '📱'
      case CampaignType.CONTENT_CREATION: return '🎨'
      case CampaignType.COMMUNITY_BUILDING: return '👥'
      case CampaignType.TRADING_ACTIVITY: return '💰'
      case CampaignType.REFERRAL_PROGRAM: return '🔗'
      case CampaignType.SEASONAL_EVENT: return '🎉'
      default: return '📋'
    }
  }

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Campaign Templates</h2>
            <p className="text-sm text-gray-600 mt-1">
              Choose from pre-built templates or start from scratch
            </p>
          </div>
          
          <button
            onClick={onCreateFromScratch}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
            Create from Scratch
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search templates..."
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Type Filter */}
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as CampaignType | 'all')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {campaignTypes.map((type) => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {categories.map((category) => (
              <option key={category} value={category === 'All Categories' ? 'all' : category.toLowerCase()}>
                {category}
              </option>
            ))}
          </select>

          {/* Difficulty Filter */}
          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {difficulties.map((difficulty) => (
              <option key={difficulty} value={difficulty === 'All Difficulties' ? 'all' : difficulty.toLowerCase()}>
                {difficulty}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="p-6">
        {templates && templates.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.map((template) => (
              <div
                key={template.id}
                className="border border-gray-200 rounded-lg p-6 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer"
                onClick={() => handleUseTemplate(template)}
              >
                {/* Template Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getTypeIcon(template.type)}</span>
                    <div>
                      <h3 className="font-medium text-gray-900">{template.name}</h3>
                      <p className="text-sm text-gray-600">{template.category}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <StarIcon
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(template.popularity / 20)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                {/* Template Description */}
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                  {template.description}
                </p>

                {/* Template Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <ClockIcon className="h-4 w-4" />
                    <span>{template.estimatedDuration} days</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <UsersIcon className="h-4 w-4" />
                    <span>{template.requirements.length} tasks</span>
                  </div>
                </div>

                {/* Template Tags */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {template.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700"
                    >
                      {tag}
                    </span>
                  ))}
                  {template.tags.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                      +{template.tags.length - 3}
                    </span>
                  )}
                </div>

                {/* Template Footer */}
                <div className="flex items-center justify-between">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(template.difficulty)}`}>
                    {template.difficulty}
                  </span>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleUseTemplate(template)
                    }}
                    disabled={createFromTemplateMutation.isPending}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50"
                  >
                    {createFromTemplateMutation.isPending ? 'Creating...' : 'Use Template'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <DocumentDuplicateIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your filters or create a campaign from scratch.
            </p>
            <div className="mt-6">
              <button
                onClick={onCreateFromScratch}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Create from Scratch
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
