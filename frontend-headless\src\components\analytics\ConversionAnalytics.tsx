'use client'

import React from 'react'
import {
  PresentationChartLineIcon,
  ChartBarIcon,
  ClockIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'
import { useConversionAnalytics, useCohortAnalysis, useRetentionAnalysis } from '@/hooks/useAnalytics'
import { AnalyticsTimeframe } from '@/types/analytics.types'

interface ConversionAnalyticsProps {
  campaignId: string
  timeframe: AnalyticsTimeframe
  className?: string
}

export default function ConversionAnalytics({
  campaignId,
  timeframe,
  className = ''
}: ConversionAnalyticsProps) {
  const { data: conversion, isLoading: conversionLoading } = useConversionAnalytics(campaignId, timeframe)
  const { data: cohort, isLoading: cohortLoading } = useCohortAnalysis(campaignId)
  const { data: retention, isLoading: retentionLoading } = useRetentionAnalysis(campaignId)

  if (conversionLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h2 className="text-lg font-medium text-gray-900">Conversion Analytics</h2>
        <p className="text-sm text-gray-600">Conversion funnel and retention analysis</p>
      </div>

      {/* Conversion Metrics */}
      {conversion && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-blue-600 bg-blue-100 mb-4">
              <PresentationChartLineIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                {(conversion.overallConversionRate * 100).toFixed(1)}%
              </h3>
              <p className="text-sm font-medium text-gray-700">Overall Conversion Rate</p>
            </div>
            <p className="text-sm text-gray-500">Across all funnel steps</p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-green-600 bg-green-100 mb-4">
              <ClockIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                {Math.round(conversion.averageTimeToConvert / 3600)}h
              </h3>
              <p className="text-sm font-medium text-gray-700">Avg Time to Convert</p>
            </div>
            <p className="text-sm text-gray-500">From first interaction</p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg text-purple-600 bg-purple-100 mb-4">
              <UserGroupIcon className="h-6 w-6" />
            </div>
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">
                {(conversion.conversionQualityScore * 100).toFixed(0)}
              </h3>
              <p className="text-sm font-medium text-gray-700">Quality Score</p>
            </div>
            <p className="text-sm text-gray-500">Conversion quality rating</p>
          </div>
        </div>
      )}

      {/* Funnel Visualization */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Conversion Funnel</h3>
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <PresentationChartLineIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">Funnel visualization</p>
            <p className="text-xs text-gray-500">Interactive funnel chart would be implemented here</p>
          </div>
        </div>
      </div>

      {/* Cohort Analysis */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Cohort Analysis</h3>
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">Cohort retention heatmap</p>
            <p className="text-xs text-gray-500">Cohort analysis visualization would be implemented here</p>
          </div>
        </div>
      </div>

      {/* Retention Metrics */}
      {retention && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Day 1 Retention</h4>
            <div className="text-2xl font-bold text-gray-900">
              {(retention.dayOneRetention * 100).toFixed(1)}%
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Day 7 Retention</h4>
            <div className="text-2xl font-bold text-gray-900">
              {(retention.daySevenRetention * 100).toFixed(1)}%
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Day 30 Retention</h4>
            <div className="text-2xl font-bold text-gray-900">
              {(retention.dayThirtyRetention * 100).toFixed(1)}%
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
