'use client'

import React, { useState, useRef } from 'react'
import {
  XMarkIcon,
  PhotoIcon,
  GlobeAltIcon,
  UserGroupIcon,
  EyeIcon,
  LockClosedIcon,
  HashtagIcon,
  AtSymbolIcon,
  CubeIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import { useCreatePost } from '@/hooks/useSocial'
import { CreatePostRequest, PostType, PostVisibility } from '@/types/social.types'

interface CreatePostModalProps {
  onClose: () => void
  onPostCreated: () => void
  className?: string
}

export default function CreatePostModal({
  onClose,
  onPostCreated,
  className = ''
}: CreatePostModalProps) {
  const [postData, setPostData] = useState<CreatePostRequest>({
    content: '',
    type: PostType.TEXT,
    visibility: PostVisibility.PUBLIC,
    images: [],
    nftReferences: [],
    tags: [],
    mentions: []
  })
  
  const [selectedImages, setSelectedImages] = useState<File[]>([])
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([])
  const [tagInput, setTagInput] = useState('')
  const [mentionInput, setMentionInput] = useState('')
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const createPostMutation = useCreatePost()

  const postTypes = [
    { value: PostType.TEXT, label: 'Text Post', icon: '📝' },
    { value: PostType.IMAGE, label: 'Image Post', icon: '🖼️' },
    { value: PostType.NFT_SHOWCASE, label: 'NFT Showcase', icon: '🎨' },
    { value: PostType.MARKETPLACE_LISTING, label: 'Marketplace', icon: '🏪' },
    { value: PostType.ACHIEVEMENT, label: 'Achievement', icon: '🏆' },
    { value: PostType.POLL, label: 'Poll', icon: '📊' },
    { value: PostType.EVENT, label: 'Event', icon: '📅' }
  ]

  const visibilityOptions = [
    { 
      value: PostVisibility.PUBLIC, 
      label: 'Public', 
      description: 'Anyone can see this post',
      icon: <GlobeAltIcon className="h-4 w-4" />
    },
    { 
      value: PostVisibility.FRIENDS, 
      label: 'Friends', 
      description: 'Only your friends can see this',
      icon: <UserGroupIcon className="h-4 w-4" />
    },
    { 
      value: PostVisibility.FOLLOWERS, 
      label: 'Followers', 
      description: 'Only your followers can see this',
      icon: <EyeIcon className="h-4 w-4" />
    },
    { 
      value: PostVisibility.PRIVATE, 
      label: 'Private', 
      description: 'Only you can see this',
      icon: <LockClosedIcon className="h-4 w-4" />
    }
  ]

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length === 0) return

    // Limit to 4 images
    const newFiles = files.slice(0, 4 - selectedImages.length)
    setSelectedImages(prev => [...prev, ...newFiles])

    // Create preview URLs
    const newPreviewUrls = newFiles.map(file => URL.createObjectURL(file))
    setImagePreviewUrls(prev => [...prev, ...newPreviewUrls])

    // Update post type to image if images are added
    if (postData.type === PostType.TEXT) {
      setPostData(prev => ({ ...prev, type: PostType.IMAGE }))
    }
  }

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index))
    setImagePreviewUrls(prev => {
      const newUrls = prev.filter((_, i) => i !== index)
      // Revoke the removed URL to free memory
      URL.revokeObjectURL(prev[index])
      return newUrls
    })

    // Change back to text post if no images remain
    if (selectedImages.length === 1 && postData.type === PostType.IMAGE) {
      setPostData(prev => ({ ...prev, type: PostType.TEXT }))
    }
  }

  const addTag = () => {
    if (tagInput.trim() && !postData.tags.includes(tagInput.trim())) {
      setPostData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const removeTag = (tag: string) => {
    setPostData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }))
  }

  const addMention = () => {
    if (mentionInput.trim() && !postData.mentions.includes(mentionInput.trim())) {
      setPostData(prev => ({
        ...prev,
        mentions: [...prev.mentions, mentionInput.trim()]
      }))
      setMentionInput('')
    }
  }

  const removeMention = (mention: string) => {
    setPostData(prev => ({
      ...prev,
      mentions: prev.mentions.filter(m => m !== mention)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!postData.content.trim()) {
      alert('Please enter some content for your post.')
      return
    }

    try {
      // In a real implementation, you would upload images first
      // and get their URLs to include in the post data
      const imageUrls = selectedImages.map((_, index) => 
        `https://example.com/uploads/image-${Date.now()}-${index}.jpg`
      )

      await createPostMutation.mutateAsync({
        ...postData,
        images: imageUrls
      })

      onPostCreated()
    } catch (error) {
      console.error('Failed to create post:', error)
      alert('Failed to create post. Please try again.')
    }
  }

  const isSubmitting = createPostMutation.isPending
  const canSubmit = postData.content.trim().length > 0 && !isSubmitting

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Create Post</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Post Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Post Type</label>
            <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
              {postTypes.map((type) => (
                <button
                  key={type.value}
                  type="button"
                  onClick={() => setPostData(prev => ({ ...prev, type: type.value }))}
                  className={`p-3 text-center border rounded-lg text-sm font-medium transition-colors ${
                    postData.type === type.value
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 text-gray-700 hover:border-gray-400'
                  }`}
                >
                  <div className="text-lg mb-1">{type.icon}</div>
                  <div>{type.label}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
            <textarea
              value={postData.content}
              onChange={(e) => setPostData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="What's on your mind?"
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              required
            />
            <div className="mt-1 text-xs text-gray-500">
              {postData.content.length}/2000 characters
            </div>
          </div>

          {/* Images */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">Images</label>
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
              >
                <PhotoIcon className="h-4 w-4 mr-1" />
                Add Images
              </button>
            </div>
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
              className="hidden"
            />

            {imagePreviewUrls.length > 0 && (
              <div className="grid grid-cols-2 gap-2">
                {imagePreviewUrls.map((url, index) => (
                  <div key={index} className="relative">
                    <img
                      src={url}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Visibility */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Visibility</label>
            <div className="space-y-2">
              {visibilityOptions.map((option) => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="radio"
                    name="visibility"
                    value={option.value}
                    checked={postData.visibility === option.value}
                    onChange={(e) => setPostData(prev => ({ ...prev, visibility: e.target.value as PostVisibility }))}
                    className="mr-3 text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex items-center">
                    {option.icon}
                    <div className="ml-2">
                      <div className="text-sm font-medium text-gray-900">{option.label}</div>
                      <div className="text-xs text-gray-600">{option.description}</div>
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Advanced Options */}
          <div>
            <button
              type="button"
              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              className="flex items-center text-sm text-blue-600 hover:text-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Advanced Options
            </button>

            {showAdvancedOptions && (
              <div className="mt-4 space-y-4">
                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="relative flex-1">
                      <HashtagIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                        placeholder="Add a tag"
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 w-full"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={addTag}
                      className="px-3 py-2 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      Add
                    </button>
                  </div>
                  
                  {postData.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {postData.tags.map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          #{tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-1 text-blue-600 hover:text-blue-800"
                          >
                            <XMarkIcon className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                {/* Mentions */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Mentions</label>
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="relative flex-1">
                      <AtSymbolIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        value={mentionInput}
                        onChange={(e) => setMentionInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addMention())}
                        placeholder="Mention a user"
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 w-full"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={addMention}
                      className="px-3 py-2 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      Add
                    </button>
                  </div>
                  
                  {postData.mentions.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {postData.mentions.map((mention) => (
                        <span
                          key={mention}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                        >
                          @{mention}
                          <button
                            type="button"
                            onClick={() => removeMention(mention)}
                            className="ml-1 text-green-600 hover:text-green-800"
                          >
                            <XMarkIcon className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!canSubmit}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Creating...' : 'Create Post'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
