'use client'

import React, { useState } from 'react'
import {
  SparklesIcon,
  ShoppingCartIcon,
  CurrencyDollarIcon,
  UserPlusIcon,
  UserGroupIcon,
  DocumentTextIcon,
  TrophyIcon,
  ChartBarIcon,
  ShareIcon,
  XMarkIcon,
  CheckIcon,
  EyeIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import {
  AIRecommendation,
  RecommendationType,
  RecommendationPriority,
  RecommendationCategory
} from '@/types/ai.types'
import {
  useMarkRecommendationViewed,
  useMarkRecommendationActioned,
  useDismissRecommendation
} from '@/hooks/useAI'

interface AIRecommendationsProps {
  userId: string
  recommendations?: AIRecommendation[]
  isLoading?: boolean
  className?: string
}

export default function AIRecommendations({
  userId,
  recommendations = [],
  isLoading = false,
  className = ''
}: AIRecommendationsProps) {
  const [selectedCategory, setSelectedCategory] = useState<RecommendationCategory | 'all'>('all')
  const [selectedPriority, setSelectedPriority] = useState<RecommendationPriority | 'all'>('all')

  const markViewedMutation = useMarkRecommendationViewed()
  const markActionedMutation = useMarkRecommendationActioned()
  const dismissMutation = useDismissRecommendation()

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: RecommendationCategory.TRADING, label: 'Trading' },
    { value: RecommendationCategory.SOCIAL, label: 'Social' },
    { value: RecommendationCategory.CONTENT, label: 'Content' },
    { value: RecommendationCategory.PORTFOLIO, label: 'Portfolio' },
    { value: RecommendationCategory.COMMUNITY, label: 'Community' },
    { value: RecommendationCategory.PERSONALIZATION, label: 'Personalization' }
  ]

  const priorities = [
    { value: 'all', label: 'All Priorities' },
    { value: RecommendationPriority.URGENT, label: 'Urgent' },
    { value: RecommendationPriority.HIGH, label: 'High' },
    { value: RecommendationPriority.MEDIUM, label: 'Medium' },
    { value: RecommendationPriority.LOW, label: 'Low' }
  ]

  const filteredRecommendations = recommendations.filter(rec => {
    const categoryMatch = selectedCategory === 'all' || rec.category === selectedCategory
    const priorityMatch = selectedPriority === 'all' || rec.priority === selectedPriority
    return categoryMatch && priorityMatch
  })

  const handleMarkViewed = (recommendationId: string) => {
    markViewedMutation.mutate(recommendationId)
  }

  const handleTakeAction = (recommendation: AIRecommendation) => {
    markActionedMutation.mutate({
      recommendationId: recommendation.id,
      actionData: { timestamp: new Date().toISOString() }
    })

    // Navigate to action URL if provided
    if (recommendation.actionUrl) {
      window.open(recommendation.actionUrl, '_blank')
    }
  }

  const handleDismiss = (recommendationId: string, reason = 'Not interested') => {
    dismissMutation.mutate({ recommendationId, reason })
  }

  const getRecommendationIcon = (type: RecommendationType) => {
    switch (type) {
      case RecommendationType.NFT_PURCHASE:
        return <ShoppingCartIcon className="h-5 w-5 text-blue-600" />
      case RecommendationType.NFT_SELL:
        return <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
      case RecommendationType.NFT_MINT:
        return <SparklesIcon className="h-5 w-5 text-purple-600" />
      case RecommendationType.USER_FOLLOW:
        return <UserPlusIcon className="h-5 w-5 text-indigo-600" />
      case RecommendationType.COMMUNITY_JOIN:
        return <UserGroupIcon className="h-5 w-5 text-orange-600" />
      case RecommendationType.CONTENT_CREATE:
        return <DocumentTextIcon className="h-5 w-5 text-teal-600" />
      case RecommendationType.CAMPAIGN_PARTICIPATE:
        return <TrophyIcon className="h-5 w-5 text-yellow-600" />
      case RecommendationType.PORTFOLIO_REBALANCE:
        return <ChartBarIcon className="h-5 w-5 text-red-600" />
      case RecommendationType.SOCIAL_ENGAGEMENT:
        return <ShareIcon className="h-5 w-5 text-pink-600" />
      default:
        return <SparklesIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: RecommendationPriority) => {
    switch (priority) {
      case RecommendationPriority.URGENT:
        return 'bg-red-100 text-red-800 border-red-200'
      case RecommendationPriority.HIGH:
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case RecommendationPriority.MEDIUM:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case RecommendationPriority.LOW:
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    return `${Math.floor(diffInHours / 24)}d ago`
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg mb-4"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <SparklesIcon className="h-6 w-6 mr-2 text-blue-600" />
            AI Recommendations
          </h2>
          <p className="text-sm text-gray-600">
            Personalized suggestions based on your activity and preferences
          </p>
        </div>
        
        <div className="text-sm text-gray-500">
          {filteredRecommendations.length} recommendations
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {categories.map((category) => (
            <option key={category.value} value={category.value}>
              {category.label}
            </option>
          ))}
        </select>

        <select
          value={selectedPriority}
          onChange={(e) => setSelectedPriority(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {priorities.map((priority) => (
            <option key={priority.value} value={priority.value}>
              {priority.label}
            </option>
          ))}
        </select>
      </div>

      {/* Recommendations List */}
      {filteredRecommendations.length > 0 ? (
        <div className="space-y-4">
          {filteredRecommendations.map((recommendation) => (
            <RecommendationCard
              key={recommendation.id}
              recommendation={recommendation}
              onMarkViewed={() => handleMarkViewed(recommendation.id)}
              onTakeAction={() => handleTakeAction(recommendation)}
              onDismiss={() => handleDismiss(recommendation.id)}
              getIcon={getRecommendationIcon}
              getPriorityColor={getPriorityColor}
              formatTimeAgo={formatTimeAgo}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No recommendations</h3>
          <p className="mt-1 text-sm text-gray-500">
            {selectedCategory !== 'all' || selectedPriority !== 'all'
              ? 'Try adjusting your filters to see more recommendations.'
              : 'Check back later for personalized suggestions based on your activity.'}
          </p>
        </div>
      )}
    </div>
  )
}

interface RecommendationCardProps {
  recommendation: AIRecommendation
  onMarkViewed: () => void
  onTakeAction: () => void
  onDismiss: () => void
  getIcon: (type: RecommendationType) => React.ReactNode
  getPriorityColor: (priority: RecommendationPriority) => string
  formatTimeAgo: (date: string) => string
}

function RecommendationCard({
  recommendation,
  onMarkViewed,
  onTakeAction,
  onDismiss,
  getIcon,
  getPriorityColor,
  formatTimeAgo
}: RecommendationCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const handleCardClick = () => {
    if (!recommendation.isViewed) {
      onMarkViewed()
    }
    setIsExpanded(!isExpanded)
  }

  return (
    <div className={`border rounded-lg p-4 transition-all hover:shadow-md ${
      recommendation.isViewed ? 'border-gray-200 bg-white' : 'border-blue-200 bg-blue-50'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex-shrink-0">
            {getIcon(recommendation.type)}
          </div>
          
          <div className="flex-1 cursor-pointer" onClick={handleCardClick}>
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-sm font-medium text-gray-900">{recommendation.title}</h3>
              
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${
                getPriorityColor(recommendation.priority)
              }`}>
                {recommendation.priority}
              </span>
              
              <div className="flex items-center text-xs text-gray-500">
                <div className={`w-2 h-2 rounded-full mr-1 ${
                  recommendation.confidence >= 80 ? 'bg-green-500' :
                  recommendation.confidence >= 60 ? 'bg-yellow-500' :
                  'bg-red-500'
                }`}></div>
                {recommendation.confidence}% confidence
              </div>
            </div>
            
            <p className="text-sm text-gray-700 mb-2">{recommendation.description}</p>
            
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <div className="flex items-center">
                <ClockIcon className="h-3 w-3 mr-1" />
                {formatTimeAgo(recommendation.createdAt)}
              </div>
              
              <span className="capitalize">{recommendation.category}</span>
              
              {recommendation.expiresAt && (
                <div className="flex items-center text-orange-600">
                  <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                  Expires {formatTimeAgo(recommendation.expiresAt)}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 ml-4">
          {!recommendation.isViewed && (
            <button
              onClick={onMarkViewed}
              className="text-blue-600 hover:text-blue-700"
              title="Mark as viewed"
            >
              <EyeIcon className="h-4 w-4" />
            </button>
          )}
          
          {recommendation.actionUrl && (
            <button
              onClick={onTakeAction}
              className="inline-flex items-center px-3 py-1 border border-blue-600 text-xs font-medium rounded text-blue-600 bg-white hover:bg-blue-50"
            >
              <CheckIcon className="h-3 w-3 mr-1" />
              {recommendation.actionText || 'Take Action'}
            </button>
          )}
          
          <button
            onClick={onDismiss}
            className="text-gray-400 hover:text-red-600"
            title="Dismiss"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Expanded Details */}
      {isExpanded && recommendation.metadata && Object.keys(recommendation.metadata).length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <h4 className="text-xs font-medium text-gray-900 mb-2">Additional Details</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {Object.entries(recommendation.metadata).map(([key, value]) => (
              <div key={key}>
                <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1')}:</span>
                <span className="ml-1 text-gray-900">{String(value)}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
