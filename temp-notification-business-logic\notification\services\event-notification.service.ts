import { Injectable, Logger } from '@nestjs/common';
import { NotificationManagementService, CreateNotificationRequest } from './notification-management.service';

export interface NotificationEvent {
  type: 'user.registered' | 'user.verified' | 'nft.generated' | 'nft.minted' | 'listing.created' | 'trade.completed' | 'bid.placed' | 'bid.outbid' | 'auction.ended' | 'achievement.unlocked';
  userId: string;
  data: Record<string, any>;
  timestamp: Date;
  source: string;
  correlationId?: string;
}

export interface NotificationTemplate {
  id: string;
  eventType: string;
  channels: ('email' | 'sms' | 'push' | 'in_app')[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  templates: {
    email?: {
      subject: string;
      body: string;
      htmlBody?: string;
    };
    sms?: {
      message: string;
    };
    push?: {
      title: string;
      body: string;
    };
    inApp?: {
      title: string;
      message: string;
      actionUrl?: string;
    };
  };
  conditions?: {
    userPreferences?: string[];
    timeRestrictions?: {
      startHour: number;
      endHour: number;
      timezone: string;
    };
  };
}

@Injectable()
export class EventNotificationService {
  private readonly logger = new Logger(EventNotificationService.name);
  private templates: Map<string, NotificationTemplate> = new Map();
  private eventQueue: NotificationEvent[] = [];

  constructor(private readonly notificationManagement: NotificationManagementService) {
    this.initializeTemplates();
    this.startEventProcessor();
  }

  /**
   * Handle incoming notification event
   */
  async handleEvent(event: NotificationEvent): Promise<void> {
    try {
      this.logger.log(`Handling notification event: ${event.type}`, { userId: event.userId, source: event.source });

      // Add to processing queue
      this.eventQueue.push(event);

      this.logger.log(`Event queued for processing: ${event.type}`);
    } catch (error) {
      this.logger.error(`Failed to handle event: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process notification event
   */
  private async processEvent(event: NotificationEvent): Promise<void> {
    try {
      this.logger.log(`Processing event: ${event.type} for user: ${event.userId}`);

      // Find matching templates
      const matchingTemplates = Array.from(this.templates.values())
        .filter(template => template.eventType === event.type);

      if (matchingTemplates.length === 0) {
        this.logger.warn(`No templates found for event type: ${event.type}`);
        return;
      }

      // Process each template
      for (const template of matchingTemplates) {
        await this.createNotificationFromTemplate(event, template);
      }

      this.logger.log(`Event processed successfully: ${event.type}`);
    } catch (error) {
      this.logger.error(`Failed to process event: ${error.message}`, error.stack);
    }
  }

  /**
   * Create notification from template
   */
  private async createNotificationFromTemplate(event: NotificationEvent, template: NotificationTemplate): Promise<void> {
    try {
      // Check conditions
      if (template.conditions) {
        if (!this.checkConditions(event, template.conditions)) {
          this.logger.log(`Conditions not met for template: ${template.id}`);
          return;
        }
      }

      // Build notification channels
      const channels: any = {};

      // Email channel
      if (template.channels.includes('email') && template.templates.email) {
        channels.email = {
          to: this.getUserEmail(event.userId),
          subject: this.interpolateTemplate(template.templates.email.subject, event.data),
          body: this.interpolateTemplate(template.templates.email.body, event.data),
          htmlBody: template.templates.email.htmlBody 
            ? this.interpolateTemplate(template.templates.email.htmlBody, event.data)
            : undefined,
        };
      }

      // SMS channel
      if (template.channels.includes('sms') && template.templates.sms) {
        channels.sms = {
          to: this.getUserPhone(event.userId),
          message: this.interpolateTemplate(template.templates.sms.message, event.data),
        };
      }

      // Push channel
      if (template.channels.includes('push') && template.templates.push) {
        channels.push = {
          deviceTokens: this.getUserDeviceTokens(event.userId),
          title: this.interpolateTemplate(template.templates.push.title, event.data),
          body: this.interpolateTemplate(template.templates.push.body, event.data),
          data: event.data,
        };
      }

      // In-app channel
      if (template.channels.includes('in_app') && template.templates.inApp) {
        channels.inApp = {
          title: this.interpolateTemplate(template.templates.inApp.title, event.data),
          message: this.interpolateTemplate(template.templates.inApp.message, event.data),
          actionUrl: template.templates.inApp.actionUrl 
            ? this.interpolateTemplate(template.templates.inApp.actionUrl, event.data)
            : undefined,
        };
      }

      // Create notification request
      const notificationRequest: CreateNotificationRequest = {
        userId: event.userId,
        type: template.channels[0], // Primary channel
        category: template.category,
        priority: template.priority,
        title: channels.email?.subject || channels.push?.title || channels.inApp?.title || 'Notification',
        message: channels.email?.body || channels.push?.body || channels.inApp?.message || 'You have a new notification',
        data: event.data,
        channels,
        source: event.source,
        correlationId: event.correlationId,
      };

      // Create notification
      await this.notificationManagement.createNotification(notificationRequest);

      this.logger.log(`Notification created from template: ${template.id}`);
    } catch (error) {
      this.logger.error(`Failed to create notification from template: ${error.message}`, error.stack);
    }
  }

  /**
   * Check template conditions
   */
  private checkConditions(event: NotificationEvent, conditions: NotificationTemplate['conditions']): boolean {
    // Check time restrictions
    if (conditions?.timeRestrictions) {
      const now = new Date();
      const hour = now.getHours();
      const { startHour, endHour } = conditions.timeRestrictions;

      if (hour < startHour || hour > endHour) {
        return false;
      }
    }

    // Add more condition checks as needed
    return true;
  }

  /**
   * Interpolate template with data
   */
  private interpolateTemplate(template: string, data: Record<string, any>): string {
    let result = template;

    // Replace placeholders like {{key}} with data values
    Object.keys(data).forEach(key => {
      const placeholder = `{{${key}}}`;
      const value = data[key];
      result = result.replace(new RegExp(placeholder, 'g'), String(value));
    });

    return result;
  }

  /**
   * Get user email (mock implementation)
   */
  private getUserEmail(userId: string): string {
    // In a real implementation, this would fetch from user service
    return `user_${userId}@example.com`;
  }

  /**
   * Get user phone (mock implementation)
   */
  private getUserPhone(userId: string): string {
    // In a real implementation, this would fetch from user service
    return `+1234567890`;
  }

  /**
   * Get user device tokens (mock implementation)
   */
  private getUserDeviceTokens(userId: string): string[] {
    // In a real implementation, this would fetch from user service
    return [`device_token_${userId}`];
  }

  /**
   * Start event processor
   */
  private startEventProcessor(): void {
    setInterval(async () => {
      if (this.eventQueue.length > 0) {
        const event = this.eventQueue.shift();
        if (event) {
          await this.processEvent(event);
        }
      }
    }, 500); // Process every 500ms
  }

  /**
   * Initialize notification templates
   */
  private initializeTemplates(): void {
    const templates: NotificationTemplate[] = [
      {
        id: 'user_registered',
        eventType: 'user.registered',
        channels: ['email', 'in_app'],
        priority: 'medium',
        category: 'account',
        templates: {
          email: {
            subject: 'Welcome to Social NFT Platform!',
            body: 'Hi {{displayName}}, welcome to our platform! Please verify your email to get started.',
            htmlBody: '<h1>Welcome {{displayName}}!</h1><p>Please verify your email to get started.</p>',
          },
          inApp: {
            title: 'Welcome!',
            message: 'Welcome to Social NFT Platform! Please verify your email.',
            actionUrl: '/verify-email',
          },
        },
      },
      {
        id: 'user_verified',
        eventType: 'user.verified',
        channels: ['email', 'push'],
        priority: 'medium',
        category: 'account',
        templates: {
          email: {
            subject: 'Email Verified Successfully',
            body: 'Hi {{displayName}}, your email has been verified! You can now access all platform features.',
          },
          push: {
            title: 'Email Verified',
            body: 'Your email has been verified successfully!',
          },
        },
      },
      {
        id: 'nft_generated',
        eventType: 'nft.generated',
        channels: ['push', 'in_app'],
        priority: 'medium',
        category: 'trade',
        templates: {
          push: {
            title: 'NFT Generated!',
            body: 'Your NFT "{{title}}" has been generated with a score of {{score}}!',
          },
          inApp: {
            title: 'NFT Generated',
            message: 'Your NFT "{{title}}" is ready! Score: {{score}}',
            actionUrl: '/nfts/{{nftId}}',
          },
        },
      },
      {
        id: 'nft_minted',
        eventType: 'nft.minted',
        channels: ['email', 'push'],
        priority: 'high',
        category: 'trade',
        templates: {
          email: {
            subject: 'NFT Minted Successfully',
            body: 'Your NFT "{{title}}" has been minted to {{blockchain}}! Transaction: {{transactionHash}}',
          },
          push: {
            title: 'NFT Minted!',
            body: 'Your NFT "{{title}}" is now on the blockchain!',
          },
        },
      },
      {
        id: 'listing_created',
        eventType: 'listing.created',
        channels: ['push', 'in_app'],
        priority: 'medium',
        category: 'listing',
        templates: {
          push: {
            title: 'Listing Created',
            body: 'Your NFT "{{title}}" is now listed for {{price}} {{currency}}!',
          },
          inApp: {
            title: 'Listing Active',
            message: 'Your NFT "{{title}}" is now available for purchase.',
            actionUrl: '/marketplace/{{listingId}}',
          },
        },
      },
      {
        id: 'trade_completed',
        eventType: 'trade.completed',
        channels: ['email', 'push'],
        priority: 'high',
        category: 'trade',
        templates: {
          email: {
            subject: 'NFT Sale Completed',
            body: 'Congratulations! Your NFT "{{title}}" has been sold for {{price}} {{currency}}.',
          },
          push: {
            title: 'NFT Sold!',
            body: 'Your NFT "{{title}}" has been sold for {{price}} {{currency}}!',
          },
        },
      },
      {
        id: 'bid_placed',
        eventType: 'bid.placed',
        channels: ['push', 'in_app'],
        priority: 'medium',
        category: 'bid',
        templates: {
          push: {
            title: 'New Bid Received',
            body: 'Someone bid {{bidAmount}} {{currency}} on your NFT "{{title}}"!',
          },
          inApp: {
            title: 'New Bid',
            message: 'New bid of {{bidAmount}} {{currency}} on "{{title}}"',
            actionUrl: '/marketplace/{{listingId}}',
          },
        },
      },
      {
        id: 'bid_outbid',
        eventType: 'bid.outbid',
        channels: ['push', 'in_app'],
        priority: 'medium',
        category: 'bid',
        templates: {
          push: {
            title: 'You\'ve Been Outbid',
            body: 'Your bid on "{{title}}" has been outbid. Current bid: {{currentBid}} {{currency}}',
          },
          inApp: {
            title: 'Outbid',
            message: 'Your bid on "{{title}}" has been outbid.',
            actionUrl: '/marketplace/{{listingId}}',
          },
        },
      },
      {
        id: 'auction_ended',
        eventType: 'auction.ended',
        channels: ['email', 'push'],
        priority: 'high',
        category: 'bid',
        templates: {
          email: {
            subject: 'Auction Ended - {{title}}',
            body: 'The auction for "{{title}}" has ended. {{#isWinner}}Congratulations, you won!{{/isWinner}}{{^isWinner}}Better luck next time!{{/isWinner}}',
          },
          push: {
            title: 'Auction Ended',
            body: 'Auction for "{{title}}" has ended. {{#isWinner}}You won!{{/isWinner}}',
          },
        },
      },
      {
        id: 'achievement_unlocked',
        eventType: 'achievement.unlocked',
        channels: ['push', 'in_app'],
        priority: 'medium',
        category: 'system',
        templates: {
          push: {
            title: 'Achievement Unlocked!',
            body: 'You unlocked "{{achievementName}}" - {{achievementDescription}}',
          },
          inApp: {
            title: 'Achievement Unlocked!',
            message: 'You unlocked "{{achievementName}}"',
            actionUrl: '/profile/achievements',
          },
        },
      },
    ];

    templates.forEach(template => {
      this.templates.set(template.id, template);
    });

    this.logger.log(`Initialized ${templates.length} notification templates`);
  }

  /**
   * Get all templates
   */
  getTemplates(): NotificationTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Get template by ID
   */
  getTemplate(templateId: string): NotificationTemplate | undefined {
    return this.templates.get(templateId);
  }
}
