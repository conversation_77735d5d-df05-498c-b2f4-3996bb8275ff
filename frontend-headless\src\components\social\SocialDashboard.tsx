'use client'

import React, { useState } from 'react'
import {
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  BellIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  HomeIcon,
  UserIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  TrophyIcon,
  HeartIcon,
  ShareIcon,
  ChatBubbleOvalLeftIcon
} from '@heroicons/react/24/outline'
import {
  useCurrentUserProfile,
  useSocialFeed,
  useSocialNotifications,
  useSuggestedUsers,
  useSuggestedCommunities,
  useLeaderboard
} from '@/hooks/useSocial'
import { FeedFilters, LeaderboardType } from '@/types/social.types'
import SocialFeed from './SocialFeed'
import UserProfile from './UserProfile'
import Communities from './Communities'
import DirectMessages from './DirectMessages'
import SocialNotifications from './SocialNotifications'
import SocialSearch from './SocialSearch'
import SocialAnalytics from './SocialAnalytics'
import CreatePostModal from './CreatePostModal'

interface SocialDashboardProps {
  className?: string
}

export default function SocialDashboard({
  className = ''
}: SocialDashboardProps) {
  const [activeTab, setActiveTab] = useState<'feed' | 'profile' | 'communities' | 'messages' | 'notifications' | 'search' | 'analytics'>('feed')
  const [showCreatePost, setShowCreatePost] = useState(false)

  const { data: currentUser, isLoading: userLoading } = useCurrentUserProfile()
  const { data: notifications } = useSocialNotifications(5, 0)
  const { data: suggestedUsers } = useSuggestedUsers(5)
  const { data: suggestedCommunities } = useSuggestedCommunities(5)
  const { data: leaderboard } = useLeaderboard(LeaderboardType.MOST_ENGAGING, 'weekly')

  const tabs = [
    { id: 'feed', name: 'Feed', icon: HomeIcon },
    { id: 'profile', name: 'Profile', icon: UserIcon },
    { id: 'communities', name: 'Communities', icon: UserGroupIcon },
    { id: 'messages', name: 'Messages', icon: ChatBubbleLeftRightIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon, count: notifications?.unreadCount },
    { id: 'search', name: 'Search', icon: MagnifyingGlassIcon },
    { id: 'analytics', name: 'Analytics', icon: ChartBarIcon }
  ]

  if (userLoading && !currentUser) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <UserGroupIcon className="h-8 w-8 mr-3 text-blue-600" />
              Social Hub
            </h1>
            <p className="text-gray-600 mt-1">
              Connect, share, and engage with the NFT community
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Quick Stats */}
            {currentUser && (
              <div className="flex items-center space-x-6 text-sm text-gray-600">
                <div className="text-center">
                  <div className="font-semibold text-gray-900">{currentUser.socialStats.followersCount}</div>
                  <div>Followers</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-gray-900">{currentUser.socialStats.followingCount}</div>
                  <div>Following</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-gray-900">{currentUser.socialStats.postsCount}</div>
                  <div>Posts</div>
                </div>
              </div>
            )}
            
            <button
              onClick={() => setShowCreatePost(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Post
            </button>
          </div>
        </div>
      </div>

      {/* Quick Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <HeartIcon className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {currentUser?.socialStats.likesReceived || 0}
              </div>
              <div className="text-sm text-gray-600">Likes Received</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChatBubbleOvalLeftIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {currentUser?.socialStats.commentsReceived || 0}
              </div>
              <div className="text-sm text-gray-600">Comments</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ShareIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {currentUser?.socialStats.sharesReceived || 0}
              </div>
              <div className="text-sm text-gray-600">Shares</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrophyIcon className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {currentUser?.socialStats.influenceScore || 0}
              </div>
              <div className="text-sm text-gray-600">Influence Score</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg border border-gray-200">
            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <tab.icon className="h-4 w-4 mr-2" />
                    {tab.name}
                    {tab.count !== undefined && tab.count > 0 && (
                      <span className="ml-2 bg-red-100 text-red-800 py-0.5 px-2 rounded-full text-xs">
                        {tab.count}
                      </span>
                    )}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {activeTab === 'feed' && (
                <SocialFeed />
              )}

              {activeTab === 'profile' && currentUser && (
                <UserProfile user={currentUser} isOwnProfile={true} />
              )}

              {activeTab === 'communities' && (
                <Communities />
              )}

              {activeTab === 'messages' && (
                <DirectMessages />
              )}

              {activeTab === 'notifications' && (
                <SocialNotifications />
              )}

              {activeTab === 'search' && (
                <SocialSearch />
              )}

              {activeTab === 'analytics' && (
                <SocialAnalytics />
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Suggested Users */}
          {suggestedUsers && suggestedUsers.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Suggested Users</h3>
              
              <div className="space-y-4">
                {suggestedUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <img
                        src={user.avatar || '/default-avatar.png'}
                        alt={user.displayName}
                        className="w-10 h-10 rounded-full"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.displayName}</div>
                        <div className="text-xs text-gray-600">@{user.username}</div>
                      </div>
                    </div>
                    
                    <button className="text-sm text-blue-600 hover:text-blue-700">
                      Follow
                    </button>
                  </div>
                ))}
              </div>
              
              <button className="w-full mt-4 text-sm text-blue-600 hover:text-blue-700">
                View All Suggestions
              </button>
            </div>
          )}

          {/* Suggested Communities */}
          {suggestedCommunities && suggestedCommunities.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Suggested Communities</h3>
              
              <div className="space-y-4">
                {suggestedCommunities.map((community) => (
                  <div key={community.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <img
                        src={community.avatar || '/default-community.png'}
                        alt={community.name}
                        className="w-10 h-10 rounded-lg"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{community.name}</div>
                        <div className="text-xs text-gray-600">{community.memberCount} members</div>
                      </div>
                    </div>
                    
                    <button className="text-sm text-blue-600 hover:text-blue-700">
                      Join
                    </button>
                  </div>
                ))}
              </div>
              
              <button className="w-full mt-4 text-sm text-blue-600 hover:text-blue-700">
                Explore Communities
              </button>
            </div>
          )}

          {/* Weekly Leaderboard */}
          {leaderboard && leaderboard.entries.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <TrophyIcon className="h-5 w-5 mr-2 text-yellow-600" />
                Weekly Leaders
              </h3>
              
              <div className="space-y-3">
                {leaderboard.entries.slice(0, 5).map((entry) => (
                  <div key={entry.user.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                        entry.rank === 1 ? 'bg-yellow-100 text-yellow-800' :
                        entry.rank === 2 ? 'bg-gray-100 text-gray-800' :
                        entry.rank === 3 ? 'bg-orange-100 text-orange-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {entry.rank}
                      </div>
                      <img
                        src={entry.user.avatar || '/default-avatar.png'}
                        alt={entry.user.displayName}
                        className="w-8 h-8 rounded-full"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{entry.user.displayName}</div>
                      </div>
                    </div>
                    
                    <div className="text-sm font-medium text-gray-600">
                      {entry.score.toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
              
              <button className="w-full mt-4 text-sm text-blue-600 hover:text-blue-700">
                View Full Leaderboard
              </button>
            </div>
          )}

          {/* Recent Notifications */}
          {notifications && notifications.notifications.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <BellIcon className="h-5 w-5 mr-2 text-blue-600" />
                Recent Activity
              </h3>
              
              <div className="space-y-3">
                {notifications.notifications.slice(0, 3).map((notification) => (
                  <div key={notification.id} className={`p-3 rounded-lg ${
                    notification.isRead ? 'bg-gray-50' : 'bg-blue-50'
                  }`}>
                    <div className="text-sm font-medium text-gray-900">{notification.title}</div>
                    <div className="text-xs text-gray-600 mt-1">{notification.message}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(notification.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
              
              <button 
                onClick={() => setActiveTab('notifications')}
                className="w-full mt-4 text-sm text-blue-600 hover:text-blue-700"
              >
                View All Notifications
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Create Post Modal */}
      {showCreatePost && (
        <CreatePostModal
          onClose={() => setShowCreatePost(false)}
          onPostCreated={() => {
            setShowCreatePost(false)
            // Refresh feed
          }}
        />
      )}
    </div>
  )
}
