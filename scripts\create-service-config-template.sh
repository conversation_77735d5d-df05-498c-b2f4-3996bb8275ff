#!/bin/bash

# Create Service Configuration Template
# This script creates a standardized configuration class for a specific service

set -e

SERVICE_NAME="$1"
SERVICE_PORT="$2"
SERVICE_DB_NAME="$3"

if [ -z "$SERVICE_NAME" ] || [ -z "$SERVICE_PORT" ] || [ -z "$SERVICE_DB_NAME" ]; then
    echo "Usage: $0 <service-name> <service-port> <db-name>"
    exit 1
fi

SERVICE_DIR="services/$SERVICE_NAME"
CONFIG_DIR="$SERVICE_DIR/src/config"

echo "Creating configuration for $SERVICE_NAME..."

# Create config directory
mkdir -p "$CONFIG_DIR"

# Determine ServiceType enum value
SERVICE_TYPE_ENUM=$(echo "$SERVICE_NAME" | tr '[:lower:]' '[:upper:]' | tr '-' '_')

# Create app.config.ts
cat > "$CONFIG_DIR/app.config.ts" << EOF
import { IsS<PERSON>, <PERSON><PERSON><PERSON>ber, IsBoolean, IsEnum, IsArray, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { 
  Environment, 
  ServiceEnvironment, 
  LogLevel, 
  LogFormat,
  ServiceType,
  DataClassification 
} from '../../../../shared/config/environment.enum';

/**
 * Application Configuration for $SERVICE_NAME
 * Implements standardized configuration with validation
 */
export class AppConfig {
  // ===== SERVICE IDENTIFICATION =====
  @IsString()
  serviceName: string = process.env.SERVICE_NAME || '$SERVICE_NAME';

  @IsString()
  serviceVersion: string = process.env.SERVICE_VERSION || '1.0.0';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  servicePort: number = parseInt(process.env.SERVICE_PORT || '$SERVICE_PORT');

  @IsEnum(ServiceType)
  serviceType: ServiceType = ServiceType.$SERVICE_TYPE_ENUM;

  // ===== ENVIRONMENT CONFIGURATION =====
  @IsEnum(Environment)
  nodeEnv: Environment = (process.env.NODE_ENV as Environment) || Environment.DEVELOPMENT;

  @IsEnum(ServiceEnvironment)
  serviceEnvironment: ServiceEnvironment = (process.env.SERVICE_ENVIRONMENT as ServiceEnvironment) || ServiceEnvironment.MOCK;

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  useMockServices: boolean = process.env.USE_MOCK_SERVICES === 'true';

  // ===== SECURITY CONFIGURATION =====
  @IsString()
  jwtSecret: string = process.env.JWT_SECRET || 'dev-jwt-secret-change-in-production';

  @IsString()
  jwtExpiresIn: string = process.env.JWT_EXPIRES_IN || '24h';

  @IsString()
  jwtRefreshExpiresIn: string = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

  @IsString()
  gatewaySecret: string = process.env.GATEWAY_SECRET || 'dev-gateway-secret-change-in-production';

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  allowDirectAccess: boolean = process.env.ALLOW_DIRECT_ACCESS === 'true';

  @IsArray()
  @Transform(({ value }) => value.split(','))
  trustedIPs: string[] = (process.env.TRUSTED_IPS || '127.0.0.1,::1').split(',');

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableGatewayAuth: boolean = process.env.ENABLE_GATEWAY_AUTH === 'true';

  // ===== DATABASE CONFIGURATION =====
  @IsString()
  databaseUrl: string = process.env.DATABASE_URL || '';

  @IsString()
  dbHost: string = process.env.DB_HOST || 'localhost';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  dbPort: number = parseInt(process.env.DB_PORT || '5432');

  @IsString()
  dbUsername: string = process.env.DB_USERNAME || 'postgres';

  @IsString()
  dbPassword: string = process.env.DB_PASSWORD || '1111';

  @IsString()
  dbDatabase: string = process.env.DB_DATABASE || '$SERVICE_DB_NAME';

  // ===== CACHING CONFIGURATION =====
  @IsString()
  redisHost: string = process.env.REDIS_HOST || 'localhost';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  redisPort: number = parseInt(process.env.REDIS_PORT || '6379');

  @IsOptional()
  @IsString()
  redisPassword?: string = process.env.REDIS_PASSWORD;

  @IsString()
  redisUrl: string = process.env.REDIS_URL || 'redis://localhost:6379';

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableRedisCache: boolean = process.env.ENABLE_REDIS_CACHE === 'true';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  cacheTtl: number = parseInt(process.env.CACHE_TTL || '300');

  // ===== LOGGING CONFIGURATION =====
  @IsEnum(LogLevel)
  logLevel: LogLevel = (process.env.LOG_LEVEL as LogLevel) || LogLevel.DEBUG;

  @IsEnum(LogFormat)
  logFormat: LogFormat = (process.env.LOG_FORMAT as LogFormat) || LogFormat.COMBINED;

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableDebugLogging: boolean = process.env.ENABLE_DEBUG_LOGGING === 'true';

  // ===== DEVELOPMENT FLAGS =====
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableCors: boolean = process.env.ENABLE_CORS === 'true';

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableSwagger: boolean = process.env.ENABLE_SWAGGER === 'true';

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableHotReload: boolean = process.env.ENABLE_HOT_RELOAD === 'true';

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enablePerformanceMonitoring: boolean = process.env.ENABLE_PERFORMANCE_MONITORING === 'true';

  // ===== MONITORING & OBSERVABILITY =====
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableMetrics: boolean = process.env.ENABLE_METRICS === 'true';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  metricsPort: number = parseInt(process.env.METRICS_PORT || '9090');

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableHealthChecks: boolean = process.env.ENABLE_HEALTH_CHECKS === 'true';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  healthCheckInterval: number = parseInt(process.env.HEALTH_CHECK_INTERVAL || '30');

  // ===== ENTERPRISE FEATURES =====
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableAuditLogging: boolean = process.env.ENABLE_AUDIT_LOGGING === 'true';

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableEventSourcing: boolean = process.env.ENABLE_EVENT_SOURCING === 'true';

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableCorrelationTracking: boolean = process.env.ENABLE_CORRELATION_TRACKING === 'true';

  @IsEnum(DataClassification)
  dataClassification: DataClassification = (process.env.DATA_CLASSIFICATION as DataClassification) || DataClassification.INTERNAL;

  @IsString()
  retentionPolicy: string = process.env.RETENTION_POLICY || '7years';

  // ===== FEATURE FLAGS =====
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableRateLimiting: boolean = process.env.ENABLE_RATE_LIMITING === 'true';

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enableApiVersioning: boolean = process.env.ENABLE_API_VERSIONING === 'true';
}
EOF

# Create index.ts
cat > "$CONFIG_DIR/index.ts" << 'EOF'
export * from './app.config';
EOF

echo "✅ Configuration created for $SERVICE_NAME"
