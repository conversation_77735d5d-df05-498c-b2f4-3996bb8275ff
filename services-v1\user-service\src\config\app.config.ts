import { IsString, IsN<PERSON>ber, IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

// Service-local enums (Industry Standard - No shared dependencies)
export enum Environment {
  DEVELOPMENT = 'development',
  PRODUCTION = 'production',
  TEST = 'test',
}

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

export enum LogFormat {
  JSON = 'json',
  COMBINED = 'combined',
  SIMPLE = 'simple',
}

export class AppConfig {
  @IsString()
  SERVICE_NAME: string = 'user-service';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  SERVICE_PORT: number = 3001;

  @IsEnum(Environment)
  NODE_ENV: Environment = Environment.DEVELOPMENT;

  @IsString()
  DATABASE_URL: string;

  @IsString()
  JWT_SECRET: string;

  @IsString()
  JWT_EXPIRES_IN: string = '24h';

  @IsString()
  API_PREFIX: string = 'api';

  @IsString()
  API_VERSION: string = 'v1';

  @IsEnum(LogLevel)
  LOG_LEVEL: LogLevel = LogLevel.INFO;

  @IsEnum(LogFormat)
  LOG_FORMAT: LogFormat = LogFormat.JSON;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  HEALTH_CHECK_TIMEOUT: number = 5000;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  HEALTH_CHECK_INTERVAL: number = 30000;

  @IsString()
  CORS_ORIGIN: string = '*';

  // External service URLs (Industry Standard - Service Discovery)
  @IsString()
  @IsOptional()
  PROFILE_ANALYSIS_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  NFT_GENERATION_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  BLOCKCHAIN_SERVICE_URL?: string;

  // Twitter API configuration (for business logic)
  @IsString()
  @IsOptional()
  TWITTER_API_KEY?: string;

  @IsString()
  @IsOptional()
  TWITTER_API_SECRET?: string;

  @IsString()
  @IsOptional()
  TWITTER_ACCESS_TOKEN?: string;

  @IsString()
  @IsOptional()
  TWITTER_ACCESS_TOKEN_SECRET?: string;

  // Validation method
  static validate(config: Record<string, unknown>): AppConfig {
    const validatedConfig = new AppConfig();
    Object.assign(validatedConfig, config);
    return validatedConfig;
  }
}
