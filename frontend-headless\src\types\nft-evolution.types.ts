export enum EvolutionTriggerType {
  TIME_BASED = 'time_based',
  INTERACTION_BASED = 'interaction_based',
  ACHIEVEMENT_BASED = 'achievement_based',
  COMMUNITY_BASED = 'community_based',
  MARKET_BASED = 'market_based',
  SEASONAL = 'seasonal',
  MANUAL = 'manual'
}

export enum EvolutionStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum TraitType {
  VISUAL = 'visual',
  ATTRIBUTE = 'attribute',
  UTILITY = 'utility',
  RARITY = 'rarity',
  METADATA = 'metadata'
}

export enum EvolutionDirection {
  UPGRADE = 'upgrade',
  DOWNGRADE = 'downgrade',
  LATERAL = 'lateral',
  RANDOM = 'random'
}

export interface EvolutionTrigger {
  id: string
  type: EvolutionTriggerType
  name: string
  description: string
  
  // Trigger Conditions
  conditions: EvolutionCondition[]
  
  // Timing
  activationTime?: string
  expirationTime?: string
  cooldownPeriod?: number
  
  // Requirements
  requiredInteractions?: number
  requiredAchievements?: string[]
  requiredCommunityActions?: CommunityAction[]
  
  // Market Conditions
  minMarketValue?: number
  maxMarketValue?: number
  volumeThreshold?: number
  
  // Configuration
  isActive: boolean
  priority: number
  maxExecutions?: number
  currentExecutions: number
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface EvolutionCondition {
  id: string
  type: 'time' | 'interaction' | 'achievement' | 'market' | 'community' | 'custom'
  operator: 'equals' | 'greater_than' | 'less_than' | 'between' | 'contains' | 'exists'
  value: any
  secondaryValue?: any
  weight: number
  isRequired: boolean
}

export interface CommunityAction {
  action: 'like' | 'share' | 'comment' | 'vote' | 'collaborate' | 'endorse'
  threshold: number
  timeframe?: number
  communitySize?: number
}

export interface NFTEvolution {
  id: string
  nftId: string
  nftTokenId: string
  
  // Evolution Details
  evolutionStage: number
  currentVersion: string
  targetVersion: string
  
  // Trigger Information
  triggerId: string
  triggerType: EvolutionTriggerType
  triggerConditions: EvolutionCondition[]
  
  // Evolution Changes
  traitChanges: TraitChange[]
  metadataChanges: MetadataChange[]
  visualChanges: VisualChange[]
  
  // Status and Progress
  status: EvolutionStatus
  progress: number
  estimatedCompletion?: string
  
  // History
  evolutionHistory: EvolutionHistoryEntry[]
  
  // Validation
  isReversible: boolean
  requiresApproval: boolean
  approvedBy?: string
  approvedAt?: string
  
  // Metadata
  createdAt: string
  updatedAt: string
  completedAt?: string
  
  // Analytics
  evolutionScore: number
  rarityImpact: number
  valueImpact: number
  communityReaction: CommunityReaction
}

export interface TraitChange {
  traitId: string
  traitName: string
  traitType: TraitType
  
  // Change Details
  oldValue: any
  newValue: any
  changeType: 'add' | 'remove' | 'modify' | 'enhance'
  
  // Impact
  rarityChange: number
  valueChange: number
  utilityChange: number
  
  // Validation
  isValidated: boolean
  validationScore: number
  
  // Metadata
  changeReason: string
  changeTimestamp: string
}

export interface MetadataChange {
  field: string
  oldValue: any
  newValue: any
  changeType: 'add' | 'remove' | 'modify'
  
  // Versioning
  version: string
  previousVersion: string
  
  // Validation
  isValidated: boolean
  schema: string
  
  // IPFS
  ipfsHash?: string
  previousIpfsHash?: string
  
  // Metadata
  changeTimestamp: string
  changeReason: string
}

export interface VisualChange {
  elementId: string
  elementType: 'background' | 'character' | 'accessory' | 'effect' | 'overlay'
  
  // Visual Details
  oldImageUrl?: string
  newImageUrl: string
  animationUrl?: string
  
  // Transformation
  transformationType: 'replace' | 'overlay' | 'modify' | 'animate'
  transformationData: any
  
  // Quality
  imageQuality: number
  resolution: string
  format: string
  
  // Generation
  generationMethod: 'ai' | 'template' | 'manual' | 'procedural'
  generationParams: any
  
  // Metadata
  changeTimestamp: string
}

export interface EvolutionHistoryEntry {
  id: string
  evolutionId: string
  
  // Event Details
  eventType: 'trigger_activated' | 'evolution_started' | 'trait_changed' | 'evolution_completed' | 'evolution_failed'
  eventDescription: string
  eventData: any
  
  // Context
  triggeredBy: string
  triggerSource: string
  
  // State
  beforeState: NFTState
  afterState: NFTState
  
  // Metadata
  timestamp: string
  blockNumber?: number
  transactionHash?: string
}

export interface NFTState {
  version: string
  traits: Record<string, any>
  metadata: Record<string, any>
  visualElements: VisualElement[]
  rarityScore: number
  evolutionStage: number
}

export interface VisualElement {
  id: string
  type: string
  imageUrl: string
  layer: number
  opacity: number
  position: { x: number; y: number }
  scale: { x: number; y: number }
  rotation: number
  isVisible: boolean
}

export interface CommunityReaction {
  likes: number
  dislikes: number
  shares: number
  comments: number
  
  // Sentiment
  sentimentScore: number
  sentimentDistribution: {
    positive: number
    neutral: number
    negative: number
  }
  
  // Engagement
  engagementRate: number
  viralityScore: number
  
  // Community Feedback
  feedback: CommunityFeedback[]
}

export interface CommunityFeedback {
  userId: string
  username: string
  feedbackType: 'like' | 'dislike' | 'comment' | 'share' | 'report'
  content?: string
  timestamp: string
  isVerified: boolean
}

export interface EvolutionTemplate {
  id: string
  name: string
  description: string
  category: string
  
  // Template Configuration
  stages: EvolutionStage[]
  triggers: EvolutionTrigger[]
  rules: EvolutionRule[]
  
  // Constraints
  maxStages: number
  minTimeBetweenEvolutions: number
  maxEvolutionsPerDay: number
  
  // Requirements
  requiredTraits: string[]
  requiredRarity: number
  requiredValue: number
  
  // Customization
  isCustomizable: boolean
  customizationOptions: CustomizationOption[]
  
  // Usage
  usageCount: number
  successRate: number
  averageValueIncrease: number
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
  isPublic: boolean
  tags: string[]
}

export interface EvolutionStage {
  stageNumber: number
  stageName: string
  description: string
  
  // Requirements
  requirements: EvolutionCondition[]
  
  // Changes
  traitChanges: TraitChange[]
  visualChanges: VisualChange[]
  metadataChanges: MetadataChange[]
  
  // Timing
  minDuration: number
  maxDuration: number
  
  // Validation
  validationRules: ValidationRule[]
  
  // Rewards
  rewards: EvolutionReward[]
}

export interface EvolutionRule {
  id: string
  name: string
  description: string
  
  // Rule Logic
  condition: string
  action: string
  priority: number
  
  // Constraints
  isActive: boolean
  applicableStages: number[]
  
  // Execution
  executionCount: number
  maxExecutions?: number
  
  // Metadata
  createdAt: string
  updatedAt: string
}

export interface CustomizationOption {
  optionId: string
  optionName: string
  optionType: 'trait' | 'visual' | 'timing' | 'trigger'
  
  // Configuration
  availableValues: any[]
  defaultValue: any
  isRequired: boolean
  
  // Constraints
  dependencies: string[]
  conflicts: string[]
  
  // Cost
  cost: number
  currency: string
}

export interface ValidationRule {
  ruleId: string
  ruleName: string
  ruleType: 'trait_validation' | 'rarity_validation' | 'value_validation' | 'visual_validation'
  
  // Validation Logic
  validationFunction: string
  parameters: Record<string, any>
  
  // Thresholds
  minScore: number
  maxScore: number
  
  // Behavior
  isBlocking: boolean
  errorMessage: string
  warningMessage: string
}

export interface EvolutionReward {
  rewardId: string
  rewardType: 'token' | 'nft' | 'utility' | 'access' | 'badge'
  
  // Reward Details
  amount: number
  currency?: string
  description: string
  
  // Distribution
  distributionMethod: 'immediate' | 'delayed' | 'conditional'
  distributionConditions: EvolutionCondition[]
  
  // Metadata
  isClaimable: boolean
  expirationDate?: string
}

export interface EvolutionAnalytics {
  nftId: string
  timeframe: string
  
  // Evolution Metrics
  totalEvolutions: number
  successfulEvolutions: number
  failedEvolutions: number
  averageEvolutionTime: number
  
  // Value Impact
  valueIncrease: number
  rarityIncrease: number
  utilityIncrease: number
  
  // Community Metrics
  communityEngagement: number
  socialShares: number
  positiveReactions: number
  
  // Performance
  evolutionSuccessRate: number
  averageValueGain: number
  communityApprovalRate: number
  
  // Trends
  evolutionTrends: EvolutionTrend[]
  popularTriggers: TriggerPopularity[]
  
  // Predictions
  nextEvolutionPrediction: EvolutionPrediction
  valueProjection: ValueProjection
}

export interface EvolutionTrend {
  period: string
  evolutionCount: number
  averageValue: number
  popularTraits: string[]
  trendDirection: 'up' | 'down' | 'stable'
}

export interface TriggerPopularity {
  triggerId: string
  triggerName: string
  usageCount: number
  successRate: number
  averageValueIncrease: number
}

export interface EvolutionPrediction {
  predictedTrigger: string
  probability: number
  estimatedTime: string
  expectedValueIncrease: number
  confidence: number
}

export interface ValueProjection {
  currentValue: number
  projectedValue: number
  timeframe: string
  confidence: number
  factors: ProjectionFactor[]
}

export interface ProjectionFactor {
  factor: string
  impact: number
  confidence: number
  description: string
}

// API Request/Response Types
export interface CreateEvolutionRequest {
  nftId: string
  triggerId: string
  customizations?: Record<string, any>
  approvalRequired?: boolean
}

export interface UpdateEvolutionRequest {
  status?: EvolutionStatus
  progress?: number
  traitChanges?: TraitChange[]
  metadataChanges?: MetadataChange[]
  visualChanges?: VisualChange[]
}

export interface EvolutionSearchRequest {
  nftIds?: string[]
  triggerTypes?: EvolutionTriggerType[]
  statuses?: EvolutionStatus[]
  dateRange?: {
    start: string
    end: string
  }
  sortBy?: 'created_at' | 'updated_at' | 'evolution_score' | 'rarity_impact'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface EvolutionSearchResponse {
  evolutions: NFTEvolution[]
  total: number
  page: number
  limit: number
  hasMore: boolean
  aggregations: {
    byStatus: Record<EvolutionStatus, number>
    byTriggerType: Record<EvolutionTriggerType, number>
    averageEvolutionScore: number
    totalValueIncrease: number
  }
}
