'use client'

import React, { useState } from 'react'
import {
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  ClockIcon,
  SparklesIcon,
  ExclamationTriangleIcon,
  PhotoIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'
import {
  useStartGeneration,
  useCancelGeneration,
  useRetryGeneration,
  useApproveGeneration,
  useRejectGeneration,
  useSelectImage
} from '@/hooks/useNFTGeneration'
import { NFTGenerationRequest, NFTGenerationStatus, NFTRarity, GenerationProvider } from '@/types/nft-generation.types'

interface GenerationRequestCardProps {
  request: NFTGenerationRequest
  viewMode: 'grid' | 'list'
  onView?: () => void
  className?: string
}

export default function GenerationRequestCard({
  request,
  viewMode,
  onView,
  className = ''
}: GenerationRequestCardProps) {
  const [showImages, setShowImages] = useState(false)

  const startGenerationMutation = useStartGeneration()
  const cancelGenerationMutation = useCancelGeneration()
  const retryGenerationMutation = useRetryGeneration()
  const approveGenerationMutation = useApproveGeneration()
  const rejectGenerationMutation = useRejectGeneration()
  const selectImageMutation = useSelectImage()

  const getStatusColor = (status: NFTGenerationStatus) => {
    switch (status) {
      case NFTGenerationStatus.PENDING: return 'text-gray-600 bg-gray-100 border-gray-200'
      case NFTGenerationStatus.QUEUED: return 'text-blue-600 bg-blue-100 border-blue-200'
      case NFTGenerationStatus.GENERATING: return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      case NFTGenerationStatus.GENERATED: return 'text-green-600 bg-green-100 border-green-200'
      case NFTGenerationStatus.REVIEWING: return 'text-purple-600 bg-purple-100 border-purple-200'
      case NFTGenerationStatus.APPROVED: return 'text-green-600 bg-green-100 border-green-200'
      case NFTGenerationStatus.REJECTED: return 'text-red-600 bg-red-100 border-red-200'
      case NFTGenerationStatus.MINTING: return 'text-indigo-600 bg-indigo-100 border-indigo-200'
      case NFTGenerationStatus.MINTED: return 'text-green-600 bg-green-100 border-green-200'
      case NFTGenerationStatus.DISTRIBUTED: return 'text-blue-600 bg-blue-100 border-blue-200'
      case NFTGenerationStatus.FAILED: return 'text-red-600 bg-red-100 border-red-200'
      default: return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getRarityColor = (rarity: NFTRarity) => {
    switch (rarity) {
      case NFTRarity.COMMON: return 'text-gray-600 bg-gray-100'
      case NFTRarity.RARE: return 'text-blue-600 bg-blue-100'
      case NFTRarity.EPIC: return 'text-purple-600 bg-purple-100'
      case NFTRarity.LEGENDARY: return 'text-yellow-600 bg-yellow-100'
      case NFTRarity.MYTHIC: return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getProviderIcon = (provider: GenerationProvider) => {
    switch (provider) {
      case GenerationProvider.OPENAI_DALLE: return '🤖'
      case GenerationProvider.MIDJOURNEY: return '🎨'
      case GenerationProvider.STABLE_DIFFUSION: return '🔬'
      case GenerationProvider.CUSTOM_AI: return '⚙️'
      case GenerationProvider.MANUAL: return '✋'
      default: return '🖼️'
    }
  }

  const getProgress = () => {
    const statusOrder = [
      NFTGenerationStatus.PENDING,
      NFTGenerationStatus.QUEUED,
      NFTGenerationStatus.GENERATING,
      NFTGenerationStatus.GENERATED,
      NFTGenerationStatus.REVIEWING,
      NFTGenerationStatus.APPROVED,
      NFTGenerationStatus.MINTING,
      NFTGenerationStatus.MINTED,
      NFTGenerationStatus.DISTRIBUTED
    ]
    
    const currentIndex = statusOrder.indexOf(request.status)
    return currentIndex >= 0 ? Math.round((currentIndex / (statusOrder.length - 1)) * 100) : 0
  }

  const canStart = request.status === NFTGenerationStatus.PENDING
  const canCancel = [NFTGenerationStatus.QUEUED, NFTGenerationStatus.GENERATING].includes(request.status)
  const canRetry = request.status === NFTGenerationStatus.FAILED
  const canReview = request.status === NFTGenerationStatus.REVIEWING
  const hasImages = request.generatedImages && request.generatedImages.length > 0

  const handleAction = async (action: string) => {
    try {
      switch (action) {
        case 'start':
          await startGenerationMutation.mutateAsync(request.id)
          break
        case 'cancel':
          await cancelGenerationMutation.mutateAsync(request.id)
          break
        case 'retry':
          await retryGenerationMutation.mutateAsync(request.id)
          break
        case 'approve':
          await approveGenerationMutation.mutateAsync({ id: request.id })
          break
        case 'reject':
          await rejectGenerationMutation.mutateAsync({ id: request.id, reason: 'Quality issues' })
          break
      }
    } catch (error) {
      console.error(`Failed to ${action} generation:`, error)
    }
  }

  const handleSelectImage = async (imageId: string) => {
    try {
      await selectImageMutation.mutateAsync({ requestId: request.id, imageId })
      setShowImages(false)
    } catch (error) {
      console.error('Failed to select image:', error)
    }
  }

  if (viewMode === 'list') {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 flex-1 min-w-0">
            <div className="flex-shrink-0">
              <span className="text-2xl">{getProviderIcon(request.provider)}</span>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="text-sm font-medium text-gray-900 truncate">
                  {request.metadata.name || `Generation #${request.id.slice(-6)}`}
                </h3>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
                  {request.status.replace('_', ' ')}
                </span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRarityColor(request.rarity)}`}>
                  {request.rarity}
                </span>
              </div>
              <p className="text-sm text-gray-600 truncate">{request.prompt}</p>
              <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                <span>Priority: {request.priority}</span>
                <span>Attempts: {request.attempts}/{request.maxAttempts}</span>
                <span>{new Date(request.requestedAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {hasImages && (
              <button
                onClick={() => setShowImages(true)}
                className="p-2 text-gray-400 hover:text-gray-600"
                title="View Generated Images"
              >
                <PhotoIcon className="h-4 w-4" />
              </button>
            )}

            <button
              onClick={onView}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="View Details"
            >
              <EyeIcon className="h-4 w-4" />
            </button>

            {canStart && (
              <button
                onClick={() => handleAction('start')}
                disabled={startGenerationMutation.isPending}
                className="p-2 text-green-600 hover:text-green-700 disabled:opacity-50"
                title="Start Generation"
              >
                <PlayIcon className="h-4 w-4" />
              </button>
            )}

            {canCancel && (
              <button
                onClick={() => handleAction('cancel')}
                disabled={cancelGenerationMutation.isPending}
                className="p-2 text-red-600 hover:text-red-700 disabled:opacity-50"
                title="Cancel Generation"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            )}

            {canRetry && (
              <button
                onClick={() => handleAction('retry')}
                disabled={retryGenerationMutation.isPending}
                className="p-2 text-blue-600 hover:text-blue-700 disabled:opacity-50"
                title="Retry Generation"
              >
                <ArrowPathIcon className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        {![NFTGenerationStatus.PENDING, NFTGenerationStatus.FAILED, NFTGenerationStatus.REJECTED].includes(request.status) && (
          <div className="mt-3">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getProgress()}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all ${className}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-3xl">{getProviderIcon(request.provider)}</span>
          <div>
            <h3 className="font-semibold text-gray-900">
              {request.metadata.name || `Generation #${request.id.slice(-6)}`}
            </h3>
            <p className="text-sm text-gray-600">{request.provider.replace('_', ' ')}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
            {request.status.replace('_', ' ')}
          </span>
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRarityColor(request.rarity)}`}>
            {request.rarity}
          </span>
        </div>
      </div>

      {/* Prompt */}
      <div className="mb-4">
        <p className="text-sm text-gray-700 line-clamp-2">{request.prompt}</p>
      </div>

      {/* Generated Images Preview */}
      {hasImages && (
        <div className="mb-4">
          <div className="grid grid-cols-2 gap-2">
            {request.generatedImages!.slice(0, 4).map((image, index) => (
              <div key={image.id} className="relative">
                <img
                  src={image.thumbnailUrl}
                  alt={`Generated ${index + 1}`}
                  className="w-full h-20 object-cover rounded-md"
                />
                {image.id === request.selectedImageId && (
                  <div className="absolute top-1 right-1">
                    <CheckIcon className="h-4 w-4 text-green-600 bg-white rounded-full p-0.5" />
                  </div>
                )}
              </div>
            ))}
          </div>
          {request.generatedImages!.length > 4 && (
            <button
              onClick={() => setShowImages(true)}
              className="text-sm text-blue-600 hover:text-blue-700 mt-2"
            >
              View all {request.generatedImages!.length} images
            </button>
          )}
        </div>
      )}

      {/* Progress Bar */}
      {![NFTGenerationStatus.PENDING, NFTGenerationStatus.FAILED, NFTGenerationStatus.REJECTED].includes(request.status) && (
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Progress</span>
            <span>{getProgress()}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgress()}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div>
          <span className="text-gray-600">Priority:</span>
          <span className="font-medium ml-1">{request.priority}</span>
        </div>
        <div>
          <span className="text-gray-600">Quality:</span>
          <span className="font-medium ml-1">{request.quality}</span>
        </div>
        <div>
          <span className="text-gray-600">Attempts:</span>
          <span className="font-medium ml-1">{request.attempts}/{request.maxAttempts}</span>
        </div>
        <div>
          <span className="text-gray-600">Created:</span>
          <span className="font-medium ml-1">{new Date(request.requestedAt).toLocaleDateString()}</span>
        </div>
      </div>

      {/* Error Display */}
      {request.errors && request.errors.length > 0 && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-4 w-4 text-red-600" />
            <span className="text-sm font-medium text-red-800">Generation Error</span>
          </div>
          <p className="text-sm text-red-700 mt-1">{request.errors[0].message}</p>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {hasImages && (
            <button
              onClick={() => setShowImages(true)}
              className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <PhotoIcon className="h-3 w-3 mr-1" />
              Images
            </button>
          )}
          
          <button
            onClick={onView}
            className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <EyeIcon className="h-3 w-3 mr-1" />
            View
          </button>
        </div>

        <div className="flex items-center space-x-1">
          {canStart && (
            <button
              onClick={() => handleAction('start')}
              disabled={startGenerationMutation.isPending}
              className="p-2 text-green-600 hover:text-green-700 disabled:opacity-50"
              title="Start Generation"
            >
              <PlayIcon className="h-4 w-4" />
            </button>
          )}

          {canCancel && (
            <button
              onClick={() => handleAction('cancel')}
              disabled={cancelGenerationMutation.isPending}
              className="p-2 text-red-600 hover:text-red-700 disabled:opacity-50"
              title="Cancel Generation"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          )}

          {canRetry && (
            <button
              onClick={() => handleAction('retry')}
              disabled={retryGenerationMutation.isPending}
              className="p-2 text-blue-600 hover:text-blue-700 disabled:opacity-50"
              title="Retry Generation"
            >
              <ArrowPathIcon className="h-4 w-4" />
            </button>
          )}

          {canReview && (
            <div className="flex items-center space-x-1">
              <button
                onClick={() => handleAction('approve')}
                disabled={approveGenerationMutation.isPending}
                className="p-2 text-green-600 hover:text-green-700 disabled:opacity-50"
                title="Approve"
              >
                <CheckIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleAction('reject')}
                disabled={rejectGenerationMutation.isPending}
                className="p-2 text-red-600 hover:text-red-700 disabled:opacity-50"
                title="Reject"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Images Modal */}
      {showImages && hasImages && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Generated Images</h3>
              <button
                onClick={() => setShowImages(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {request.generatedImages!.map((image) => (
                <div key={image.id} className="relative">
                  <img
                    src={image.url}
                    alt="Generated"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <div className="absolute top-2 right-2 flex items-center space-x-1">
                    {image.id === request.selectedImageId ? (
                      <span className="bg-green-600 text-white px-2 py-1 rounded text-xs">Selected</span>
                    ) : (
                      <button
                        onClick={() => handleSelectImage(image.id)}
                        disabled={selectImageMutation.isPending}
                        className="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 disabled:opacity-50"
                      >
                        Select
                      </button>
                    )}
                  </div>
                  <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                    Score: {image.qualityScore.toFixed(1)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
