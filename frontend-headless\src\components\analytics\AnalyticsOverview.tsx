'use client'

import React from 'react'
import {
  UsersIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  SparklesIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { CampaignAnalytics } from '@/types/analytics.types'

interface AnalyticsOverviewProps {
  analytics: CampaignAnalytics
  realTimeMetrics?: any
  activityFeed?: any[]
  className?: string
}

export default function AnalyticsOverview({
  analytics,
  realTimeMetrics,
  activityFeed,
  className = ''
}: AnalyticsOverviewProps) {
  const { overview } = analytics

  const keyMetrics = [
    {
      title: 'Total Participants',
      value: overview.totalParticipants.toLocaleString(),
      icon: UsersIcon,
      color: 'text-blue-600 bg-blue-100',
      change: '+12%',
      trend: 'up' as const,
      description: `${overview.activeParticipants} currently active`
    },
    {
      title: 'Engagement Rate',
      value: `${(overview.engagementRate * 100).toFixed(1)}%`,
      icon: ChartBarIcon,
      color: 'text-green-600 bg-green-100',
      change: '+5.2%',
      trend: 'up' as const,
      description: `${Math.round(overview.averageEngagementTime / 60)}m avg time`
    },
    {
      title: 'Completion Rate',
      value: `${(overview.completionRate * 100).toFixed(1)}%`,
      icon: CheckCircleIcon,
      color: 'text-purple-600 bg-purple-100',
      change: '-2.1%',
      trend: 'down' as const,
      description: `${overview.totalCompletions} completed`
    },
    {
      title: 'ROI',
      value: `${(overview.roi * 100).toFixed(1)}%`,
      icon: CurrencyDollarIcon,
      color: 'text-orange-600 bg-orange-100',
      change: '+8.7%',
      trend: 'up' as const,
      description: `$${overview.totalCost.toLocaleString()} total cost`
    },
    {
      title: 'NFTs Generated',
      value: overview.nftsGenerated.toLocaleString(),
      icon: SparklesIcon,
      color: 'text-indigo-600 bg-indigo-100',
      change: '+15%',
      trend: 'up' as const,
      description: `${overview.nftsDistributed} distributed`
    },
    {
      title: 'Quality Score',
      value: overview.averageQualityScore.toFixed(1),
      icon: CheckCircleIcon,
      color: 'text-green-600 bg-green-100',
      change: '+0.3',
      trend: 'up' as const,
      description: 'Out of 10'
    }
  ]

  const getTrendIcon = (trend: 'up' | 'down') => {
    return trend === 'up' ? (
      <TrendingUpIcon className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDownIcon className="h-4 w-4 text-red-600" />
    )
  }

  const getHealthStatus = () => {
    const engagementHealth = overview.engagementRate > 0.7 ? 'good' : overview.engagementRate > 0.5 ? 'warning' : 'poor'
    const completionHealth = overview.completionRate > 0.8 ? 'good' : overview.completionRate > 0.6 ? 'warning' : 'poor'
    const roiHealth = overview.roi > 0.2 ? 'good' : overview.roi > 0 ? 'warning' : 'poor'
    
    const healthScores = [engagementHealth, completionHealth, roiHealth]
    const goodCount = healthScores.filter(h => h === 'good').length
    const warningCount = healthScores.filter(h => h === 'warning').length
    
    if (goodCount >= 2) return { status: 'Excellent', color: 'text-green-600 bg-green-100', icon: CheckCircleIcon }
    if (warningCount >= 2) return { status: 'Good', color: 'text-yellow-600 bg-yellow-100', icon: ExclamationTriangleIcon }
    return { status: 'Needs Attention', color: 'text-red-600 bg-red-100', icon: ExclamationTriangleIcon }
  }

  const healthStatus = getHealthStatus()

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {keyMetrics.map((metric, index) => (
          <div
            key={index}
            className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all"
          >
            {/* Icon and Trend */}
            <div className="flex items-center justify-between mb-4">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${metric.color}`}>
                <metric.icon className="h-6 w-6" />
              </div>
              <div className="flex items-center space-x-1">
                {getTrendIcon(metric.trend)}
                <span className={`text-sm font-medium ${
                  metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {metric.change}
                </span>
              </div>
            </div>

            {/* Value and Title */}
            <div className="mb-2">
              <h3 className="text-2xl font-bold text-gray-900">{metric.value}</h3>
              <p className="text-sm font-medium text-gray-700">{metric.title}</p>
            </div>

            {/* Description */}
            <p className="text-sm text-gray-500">{metric.description}</p>
          </div>
        ))}
      </div>

      {/* Campaign Health and Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Campaign Health */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Campaign Health</h3>
            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${healthStatus.color}`}>
              <healthStatus.icon className="h-4 w-4 mr-2" />
              {healthStatus.status}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Engagement Rate</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      overview.engagementRate > 0.7 ? 'bg-green-500' : 
                      overview.engagementRate > 0.5 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${overview.engagementRate * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {(overview.engagementRate * 100).toFixed(1)}%
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Completion Rate</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      overview.completionRate > 0.8 ? 'bg-green-500' : 
                      overview.completionRate > 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${overview.completionRate * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {(overview.completionRate * 100).toFixed(1)}%
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">ROI Performance</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      overview.roi > 0.2 ? 'bg-green-500' : 
                      overview.roi > 0 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${Math.min(100, Math.max(0, overview.roi * 100))}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {(overview.roi * 100).toFixed(1)}%
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Quality Score</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      overview.averageQualityScore > 8 ? 'bg-green-500' : 
                      overview.averageQualityScore > 6 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${(overview.averageQualityScore / 10) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {overview.averageQualityScore.toFixed(1)}/10
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Summary */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Summary</h3>
          
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <UsersIcon className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900">Participant Insights</span>
              </div>
              <p className="text-sm text-blue-800">
                {overview.totalParticipants > 1000 
                  ? `Strong participation with ${overview.totalParticipants.toLocaleString()} participants`
                  : overview.totalParticipants > 100
                  ? `Growing participation with ${overview.totalParticipants.toLocaleString()} participants`
                  : `Building momentum with ${overview.totalParticipants.toLocaleString()} participants`
                }
              </p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <ChartBarIcon className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-900">Engagement Status</span>
              </div>
              <p className="text-sm text-green-800">
                {overview.engagementRate > 0.7
                  ? `Excellent engagement rate of ${(overview.engagementRate * 100).toFixed(1)}%`
                  : overview.engagementRate > 0.5
                  ? `Good engagement rate of ${(overview.engagementRate * 100).toFixed(1)}% with room for improvement`
                  : `Engagement rate of ${(overview.engagementRate * 100).toFixed(1)}% needs attention`
                }
              </p>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <SparklesIcon className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-purple-900">NFT Generation</span>
              </div>
              <p className="text-sm text-purple-800">
                Generated {overview.nftsGenerated.toLocaleString()} NFTs with {overview.nftsDistributed.toLocaleString()} distributed
                ({((overview.nftsDistributed / overview.nftsGenerated) * 100).toFixed(1)}% distribution rate)
              </p>
            </div>

            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <CurrencyDollarIcon className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-900">Financial Performance</span>
              </div>
              <p className="text-sm text-orange-800">
                {overview.roi > 0.2
                  ? `Strong ROI of ${(overview.roi * 100).toFixed(1)}% with $${overview.totalCost.toLocaleString()} invested`
                  : overview.roi > 0
                  ? `Positive ROI of ${(overview.roi * 100).toFixed(1)}% with optimization opportunities`
                  : `ROI of ${(overview.roi * 100).toFixed(1)}% requires cost optimization`
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Time Series Chart Placeholder */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Performance Trends</h3>
          <div className="text-sm text-gray-500">Last 30 days</div>
        </div>
        
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">Time series chart visualization</p>
            <p className="text-xs text-gray-500">Integration with charting library (Chart.js, Recharts, etc.)</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors">
            <div className="text-center">
              <ChartBarIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">View Detailed Analytics</span>
            </div>
          </button>
          
          <button className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors">
            <div className="text-center">
              <CurrencyDollarIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Generate ROI Report</span>
            </div>
          </button>
          
          <button className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors">
            <div className="text-center">
              <UsersIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Analyze Participants</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
