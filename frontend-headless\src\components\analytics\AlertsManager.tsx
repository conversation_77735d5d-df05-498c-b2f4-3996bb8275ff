'use client'

import React, { useState } from 'react'
import {
  BellIcon,
  PlusIcon,
  TrashIcon,
  PencilIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import {
  useAlerts,
  useCreateAlert,
  useUpdateAlert,
  useDeleteAlert
} from '@/hooks/useAnalytics'

interface AlertsManagerProps {
  campaignId: string
  className?: string
}

export default function AlertsManager({
  campaignId,
  className = ''
}: AlertsManagerProps) {
  const [showCreateAlert, setShowCreateAlert] = useState(false)
  const [editingAlert, setEditingAlert] = useState<string | null>(null)

  const { data: alerts, isLoading } = useAlerts(campaignId)
  const createAlertMutation = useCreateAlert()
  const updateAlertMutation = useUpdateAlert()
  const deleteAlertMutation = useDeleteAlert()

  const [newAlert, setNewAlert] = useState({
    name: '',
    metric: 'engagement_rate',
    condition: 'less_than' as const,
    threshold: 0,
    frequency: 'immediate' as const,
    recipients: ['']
  })

  const handleCreateAlert = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await createAlertMutation.mutateAsync({
        campaignId,
        alert: {
          ...newAlert,
          recipients: newAlert.recipients.filter(email => email.trim() !== '')
        }
      })
      setShowCreateAlert(false)
      setNewAlert({
        name: '',
        metric: 'engagement_rate',
        condition: 'less_than',
        threshold: 0,
        frequency: 'immediate',
        recipients: ['']
      })
    } catch (error) {
      console.error('Failed to create alert:', error)
    }
  }

  const handleDeleteAlert = async (alertId: string) => {
    if (confirm('Are you sure you want to delete this alert?')) {
      try {
        await deleteAlertMutation.mutateAsync({ campaignId, alertId })
      } catch (error) {
        console.error('Failed to delete alert:', error)
      }
    }
  }

  const handleToggleAlert = async (alertId: string, currentStatus: string) => {
    try {
      await updateAlertMutation.mutateAsync({
        campaignId,
        alertId,
        updates: {
          status: currentStatus === 'active' ? 'disabled' : 'active'
        }
      })
    } catch (error) {
      console.error('Failed to toggle alert:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100'
      case 'triggered': return 'text-red-600 bg-red-100'
      case 'disabled': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircleIcon className="h-4 w-4" />
      case 'triggered': return <ExclamationTriangleIcon className="h-4 w-4" />
      case 'disabled': return <XCircleIcon className="h-4 w-4" />
      default: return <BellIcon className="h-4 w-4" />
    }
  }

  const metricOptions = [
    { value: 'engagement_rate', label: 'Engagement Rate' },
    { value: 'conversion_rate', label: 'Conversion Rate' },
    { value: 'participant_count', label: 'Participant Count' },
    { value: 'completion_rate', label: 'Completion Rate' },
    { value: 'bounce_rate', label: 'Bounce Rate' },
    { value: 'roi', label: 'ROI' },
    { value: 'cost_per_participant', label: 'Cost per Participant' },
    { value: 'quality_score', label: 'Quality Score' }
  ]

  const conditionOptions = [
    { value: 'greater_than', label: 'Greater than' },
    { value: 'less_than', label: 'Less than' },
    { value: 'equals', label: 'Equals' },
    { value: 'change_by', label: 'Changes by' }
  ]

  const frequencyOptions = [
    { value: 'immediate', label: 'Immediate' },
    { value: 'hourly', label: 'Hourly' },
    { value: 'daily', label: 'Daily' }
  ]

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Alert Management</h2>
          <p className="text-sm text-gray-600">Set up automated alerts for key metrics</p>
        </div>

        <button
          onClick={() => setShowCreateAlert(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Alert
        </button>
      </div>

      {/* Alerts List */}
      {alerts && alerts.length > 0 ? (
        <div className="space-y-4">
          {alerts.map((alert) => (
            <div key={alert.id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-medium text-gray-900">{alert.name}</h3>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(alert.status)}`}>
                      {getStatusIcon(alert.status)}
                      <span className="ml-1">{alert.status}</span>
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-2">
                    Alert when <strong>{alert.metric.replace('_', ' ')}</strong> is{' '}
                    <strong>{alert.condition.replace('_', ' ')}</strong> <strong>{alert.threshold}</strong>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    {alert.lastTriggered && (
                      <span>Last triggered: {new Date(alert.lastTriggered).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleToggleAlert(alert.id, alert.status)}
                    disabled={updateAlertMutation.isPending}
                    className={`px-3 py-1 rounded-md text-sm font-medium ${
                      alert.status === 'active'
                        ? 'bg-red-100 text-red-700 hover:bg-red-200'
                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                    } disabled:opacity-50`}
                  >
                    {alert.status === 'active' ? 'Disable' : 'Enable'}
                  </button>

                  <button
                    onClick={() => setEditingAlert(alert.id)}
                    className="p-2 text-gray-400 hover:text-gray-600"
                    title="Edit Alert"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>

                  <button
                    onClick={() => handleDeleteAlert(alert.id)}
                    disabled={deleteAlertMutation.isPending}
                    className="p-2 text-gray-400 hover:text-red-600 disabled:opacity-50"
                    title="Delete Alert"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No alerts configured</h3>
          <p className="mt-1 text-sm text-gray-500">
            Set up alerts to monitor important metrics and get notified when thresholds are reached.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowCreateAlert(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Alert
            </button>
          </div>
        </div>
      )}

      {/* Create Alert Modal */}
      {showCreateAlert && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Create New Alert</h3>
              <button
                onClick={() => setShowCreateAlert(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleCreateAlert} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Alert Name
                </label>
                <input
                  type="text"
                  value={newAlert.name}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, name: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Low Engagement Alert"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Metric to Monitor
                </label>
                <select
                  value={newAlert.metric}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, metric: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {metricOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Condition
                  </label>
                  <select
                    value={newAlert.condition}
                    onChange={(e) => setNewAlert(prev => ({ ...prev, condition: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {conditionOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Threshold
                  </label>
                  <input
                    type="number"
                    value={newAlert.threshold}
                    onChange={(e) => setNewAlert(prev => ({ ...prev, threshold: parseFloat(e.target.value) }))}
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Frequency
                </label>
                <select
                  value={newAlert.frequency}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, frequency: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {frequencyOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Recipients
                </label>
                {newAlert.recipients.map((email, index) => (
                  <div key={index} className="flex items-center space-x-2 mb-2">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => {
                        const newRecipients = [...newAlert.recipients]
                        newRecipients[index] = e.target.value
                        setNewAlert(prev => ({ ...prev, recipients: newRecipients }))
                      }}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="<EMAIL>"
                    />
                    {newAlert.recipients.length > 1 && (
                      <button
                        type="button"
                        onClick={() => {
                          const newRecipients = newAlert.recipients.filter((_, i) => i !== index)
                          setNewAlert(prev => ({ ...prev, recipients: newRecipients }))
                        }}
                        className="p-2 text-red-600 hover:text-red-700"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => setNewAlert(prev => ({ ...prev, recipients: [...prev.recipients, ''] }))}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  + Add recipient
                </button>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateAlert(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createAlertMutation.isPending}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {createAlertMutation.isPending ? 'Creating...' : 'Create Alert'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
