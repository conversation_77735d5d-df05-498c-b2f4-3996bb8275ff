# 🌍 Environment Variables Standardization

**Complete standardization of environment variables across all services with mock/real data switching support**

## 📋 Current Issues Identified

1. **Inconsistent .env structures** across services
2. **Duplicate configurations** (JWT_SECRET in multiple files)
3. **Missing environment files** (.env.development.mock, .env.development.real)
4. **Inconsistent naming conventions** (DB_DATABASE vs DB_NAME)
5. **Service-level overrides** causing configuration conflicts
6. **Security variables** scattered across different files

## 🎯 Standardization Objectives

1. **Unified Environment Structure**: Consistent .env files across all services
2. **Mock/Real Data Switching**: Seamless switching between mock and real services
3. **Centralized Configuration**: Platform-level configuration with service-specific overrides
4. **Security Standardization**: Consistent security variables across all services
5. **Development Flexibility**: Easy environment switching for different development scenarios

## 📁 Proposed Environment File Structure

```
# Platform Level (Root)
.env                           # Current active environment
.env.development.mock          # Mock services configuration
.env.development.real          # Real services configuration  
.env.staging                   # Staging environment
.env.production                # Production environment
.env.test                      # Testing environment
.env.local                     # Local overrides (git-ignored)

# Service Level (services/[service-name]/)
.env                           # Service-specific configuration
.env.local                     # Service-specific local overrides (git-ignored)
```

## 🔧 Standardized Environment Variables

### **Core Platform Variables**

```bash
# ===== ENVIRONMENT CONFIGURATION =====
NODE_ENV=development                    # development|staging|production|test
SERVICE_ENVIRONMENT=mock               # mock|real|hybrid
USE_MOCK_SERVICES=true                 # true|false

# ===== SERVICE DISCOVERY =====
API_GATEWAY_PORT=3010
API_GATEWAY_URL=http://localhost:3010

# Service Ports (Standardized)
USER_SERVICE_PORT=3011
PROFILE_ANALYSIS_SERVICE_PORT=3002
NFT_GENERATION_SERVICE_PORT=3003
BLOCKCHAIN_SERVICE_PORT=3004
PROJECT_SERVICE_PORT=3005
MARKETPLACE_SERVICE_PORT=3006
NOTIFICATION_SERVICE_PORT=3008
ANALYTICS_SERVICE_PORT=3009

# Mock Service Ports
MOCK_TWITTER_SERVICE_PORT=3020
MOCK_BLOCKCHAIN_SERVICE_PORT=3021
MOCK_NFT_STORAGE_SERVICE_PORT=3022

# ===== DATABASE CONFIGURATION =====
# Primary Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111

# Service-Specific Databases
USER_DB_NAME=user_service
PROFILE_ANALYSIS_DB_NAME=profile_analysis_service
NFT_GENERATION_DB_NAME=nft_generation_service
BLOCKCHAIN_DB_NAME=blockchain_service
PROJECT_DB_NAME=project_service
MARKETPLACE_DB_NAME=marketplace_service
NOTIFICATION_DB_NAME=notification_service
ANALYTICS_DB_NAME=analytics_service

# ===== SECURITY CONFIGURATION =====
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Gateway Security
GATEWAY_SECRET=dev-gateway-secret-change-in-production
ALLOW_DIRECT_ACCESS=true               # false in production
TRUSTED_IPS=127.0.0.1,::1,localhost
ENABLE_GATEWAY_AUTH=true

# ===== CACHING CONFIGURATION =====
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379
ENABLE_REDIS_CACHE=false              # true in production
CACHE_TTL=300

# ===== LOGGING CONFIGURATION =====
LOG_LEVEL=debug                        # debug|info|warn|error
LOG_FORMAT=combined                    # combined|json|simple
ENABLE_DEBUG_LOGGING=true              # false in production

# ===== DEVELOPMENT FLAGS =====
ENABLE_CORS=true
ENABLE_SWAGGER=true                    # false in production
ENABLE_HOT_RELOAD=true                 # false in production
ENABLE_PERFORMANCE_MONITORING=true

# ===== EXTERNAL SERVICES (Real Environment) =====
# Twitter API
TWITTER_API_KEY=
TWITTER_API_SECRET=
TWITTER_BEARER_TOKEN=

# Blockchain Configuration
BLOCKCHAIN_RPC_URL=
BLOCKCHAIN_PRIVATE_KEY=
BLOCKCHAIN_NETWORK=testnet             # testnet|mainnet

# NFT Storage
NFT_STORAGE_API_KEY=
NFT_STORAGE_ENDPOINT=

# Email Service
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_SECURE=false

# ===== MONITORING & OBSERVABILITY =====
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=30

# ===== FEATURE FLAGS =====
ENABLE_AUDIT_LOGGING=true
ENABLE_EVENT_SOURCING=true
ENABLE_RATE_LIMITING=true
ENABLE_API_VERSIONING=true
```

### **Service-Specific Variables Template**

```bash
# ===== SERVICE IDENTIFICATION =====
SERVICE_NAME=user-service              # Unique service identifier
SERVICE_VERSION=1.0.0
SERVICE_PORT=3011                      # Service-specific port

# ===== DATABASE CONNECTION =====
DATABASE_URL=postgresql://postgres:1111@localhost:5432/user_service

# ===== SERVICE-SPECIFIC FEATURES =====
# (Each service can have its own specific configurations)
ENABLE_USER_ANALYTICS=true
ENABLE_PROFILE_CACHING=true
MAX_UPLOAD_SIZE=10MB

# ===== ENTERPRISE FEATURES =====
DATA_CLASSIFICATION=internal           # public|internal|confidential|restricted
RETENTION_POLICY=7years
ENABLE_CORRELATION_TRACKING=true
```

## 🔄 Mock/Real Environment Switching

### **Mock Environment (.env.development.mock)**
- Uses mock services for external dependencies
- Fast development without API keys
- Predictable test data
- No external network dependencies

### **Real Environment (.env.development.real)**
- Uses actual external APIs
- Requires API keys and credentials
- Real data for integration testing
- Network dependencies required

### **Hybrid Environment (.env.development.hybrid)**
- Mix of mock and real services
- Allows selective testing
- Gradual migration from mock to real

## 🛠️ Implementation Plan

### **Phase 1: Create Standardized Environment Files**
1. Create platform-level environment files
2. Standardize variable naming conventions
3. Implement environment validation

### **Phase 2: Update Service Configurations**
1. Standardize service-level .env files
2. Remove duplicate configurations
3. Implement configuration inheritance

### **Phase 3: Update Environment Switching**
1. Fix switch-environment.sh script
2. Create missing environment files
3. Test switching functionality

### **Phase 4: Configuration Validation**
1. Add environment variable validation
2. Create configuration schemas
3. Implement startup checks

## 📊 Benefits of Standardization

1. **Consistency**: All services use the same configuration patterns
2. **Maintainability**: Easy to update configurations across all services
3. **Security**: Centralized security configuration management
4. **Development Speed**: Quick environment switching for different scenarios
5. **Production Readiness**: Clear separation between development and production configs
6. **Debugging**: Easier to troubleshoot configuration issues

## 🔍 Next Steps

1. **Audit Current Configurations**: Review all existing .env files
2. **Create Master Templates**: Develop standardized environment templates
3. **Implement Validation**: Add configuration validation to all services
4. **Update Documentation**: Document all environment variables and their purposes
5. **Test Environment Switching**: Verify mock/real switching works correctly
