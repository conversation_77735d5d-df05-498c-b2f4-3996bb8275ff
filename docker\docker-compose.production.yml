version: '3.8'

# Production Docker Compose Configuration
# Implements network-level security with isolated backend services

networks:
  frontend:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  backend:
    driver: bridge
    internal: true  # No external access
    ipam:
      config:
        - subnet: **********/16

services:
  # Nginx Reverse Proxy (Internet-facing)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - frontend
    depends_on:
      - api-gateway
    restart: unless-stopped

  # API Gateway (Frontend + Backend networks)
  api-gateway:
    build: 
      context: ../services/api-gateway
      dockerfile: Dockerfile.production
    expose:
      - "3010"
    networks:
      - frontend
      - backend
    environment:
      - NODE_ENV=production
      - PORT=3010
      - GATEWAY_SECRET=${GATEWAY_SECRET}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    volumes:
      - ../logs/api-gateway:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3010/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # User Service (Backend network only)
  user-service:
    build:
      context: ../services/user-service
      dockerfile: Dockerfile.production
    expose:
      - "3011"
    networks:
      - backend
    environment:
      - NODE_ENV=production
      - PORT=3011
      - ALLOW_DIRECT_ACCESS=false
      - GATEWAY_SECRET=${GATEWAY_SECRET}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    volumes:
      - ../logs/user-service:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3011/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Profile Analysis Service (Backend network only)
  profile-analysis-service:
    build:
      context: ../services/profile-analysis-service
      dockerfile: Dockerfile.production
    expose:
      - "3002"
    networks:
      - backend
    environment:
      - NODE_ENV=production
      - PORT=3002
      - ALLOW_DIRECT_ACCESS=false
      - GATEWAY_SECRET=${GATEWAY_SECRET}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ../logs/profile-analysis-service:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NFT Generation Service (Backend network only)
  nft-generation-service:
    build:
      context: ../services/nft-generation-service
      dockerfile: Dockerfile.production
    expose:
      - "3003"
    networks:
      - backend
    environment:
      - NODE_ENV=production
      - PORT=3003
      - ALLOW_DIRECT_ACCESS=false
      - GATEWAY_SECRET=${GATEWAY_SECRET}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ../logs/nft-generation-service:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Blockchain Service (Backend network only)
  blockchain-service:
    build:
      context: ../services/blockchain-service
      dockerfile: Dockerfile.production
    expose:
      - "3004"
    networks:
      - backend
    environment:
      - NODE_ENV=production
      - PORT=3004
      - ALLOW_DIRECT_ACCESS=false
      - GATEWAY_SECRET=${GATEWAY_SECRET}
      - DATABASE_URL=${DATABASE_URL}
      - BLOCKCHAIN_RPC_URL=${BLOCKCHAIN_RPC_URL}
    volumes:
      - ../logs/blockchain-service:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3004/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Project Service (Backend network only)
  project-service:
    build:
      context: ../services/project-service
      dockerfile: Dockerfile.production
    expose:
      - "3005"
    networks:
      - backend
    environment:
      - NODE_ENV=production
      - PORT=3005
      - ALLOW_DIRECT_ACCESS=false
      - GATEWAY_SECRET=${GATEWAY_SECRET}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ../logs/project-service:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Marketplace Service (Backend network only)
  marketplace-service:
    build:
      context: ../services/marketplace-service
      dockerfile: Dockerfile.production
    expose:
      - "3006"
    networks:
      - backend
    environment:
      - NODE_ENV=production
      - PORT=3006
      - ALLOW_DIRECT_ACCESS=false
      - GATEWAY_SECRET=${GATEWAY_SECRET}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ../logs/marketplace-service:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3006/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Notification Service (Backend network only)
  notification-service:
    build:
      context: ../services/notification-service
      dockerfile: Dockerfile.production
    expose:
      - "3008"
    networks:
      - backend
    environment:
      - NODE_ENV=production
      - PORT=3008
      - ALLOW_DIRECT_ACCESS=false
      - GATEWAY_SECRET=${GATEWAY_SECRET}
      - DATABASE_URL=${DATABASE_URL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    volumes:
      - ../logs/notification-service:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3008/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Analytics Service (Backend network only)
  analytics-service:
    build:
      context: ../services/analytics-service
      dockerfile: Dockerfile.production
    expose:
      - "3009"
    networks:
      - backend
    environment:
      - NODE_ENV=production
      - PORT=3009
      - ALLOW_DIRECT_ACCESS=false
      - GATEWAY_SECRET=${GATEWAY_SECRET}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ../logs/analytics-service:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3009/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database (Backend network only)
  postgres:
    image: postgres:15-alpine
    expose:
      - "5432"
    networks:
      - backend
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache (Backend network only)
  redis:
    image: redis:7-alpine
    expose:
      - "6379"
    networks:
      - backend
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Health Monitor (Backend network only)
  health-monitor:
    build:
      context: ../monitoring/health-monitor
      dockerfile: Dockerfile
    networks:
      - backend
    environment:
      - NODE_ENV=production
      - SERVICES=user-service:3011,profile-analysis-service:3002,nft-generation-service:3003,blockchain-service:3004,project-service:3005,marketplace-service:3006,notification-service:3008,analytics-service:3009
      - CHECK_INTERVAL=30
      - GATEWAY_SECRET=${GATEWAY_SECRET}
    volumes:
      - ../logs/health-monitor:/app/logs
    restart: unless-stopped
    depends_on:
      - user-service
      - profile-analysis-service
      - nft-generation-service
      - blockchain-service
      - project-service
      - marketplace-service
      - notification-service
      - analytics-service

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
