/**
 * User Service V2 - Main Entry Point
 * 
 * Implements shared infrastructure following SHARED-INFRASTRUCTURE-IMPLEMENTATION-PLAN.md
 * Database Per Service Pattern - Uses user_service_v2 database only
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Global validation pipe (enhanced by shared infrastructure)
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
  });

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('User Service V2 API')
    .setDescription('User Service V2 - Shared Infrastructure Implementation')
    .setVersion('2.0.0')
    .addBearerAuth()
    .addTag('users', 'User management operations')
    .addTag('auth', 'Authentication operations')
    .addTag('health', 'Health check operations')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Start server
  const port = process.env.SERVICE_PORT || 4001;
  await app.listen(port);

  console.log(`🚀 User Service V2 is running on: http://localhost:${port}`);
  console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  console.log(`🏥 Health Check: http://localhost:${port}/health/simple`);
  console.log(`🔧 Shared Infrastructure: Authentication, Logging, Responses, Config`);
  console.log(`🗄️ Database: user_service_v2 (Database Per Service Pattern)`);
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start User Service V2:', error);
  process.exit(1);
});
