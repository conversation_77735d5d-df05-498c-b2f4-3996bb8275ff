/**
 * Standardized Configuration Module
 * Provides consistent configuration setup for all services
 */

import { Module, DynamicModule, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { ConfigValidationUtil } from './config-validation.util';
import { Environment, EnvironmentValidator } from './environment.enum';

/**
 * Configuration options for the standardized config module
 */
export interface StandardizedConfigOptions {
  /**
   * Configuration class to validate against
   */
  configClass?: new () => any;
  
  /**
   * Required environment variables
   */
  requiredEnvVars?: string[];
  
  /**
   * Environment file paths (relative to service root)
   */
  envFilePaths?: string[];
  
  /**
   * Whether to validate configuration on startup
   */
  validateOnStartup?: boolean;
  
  /**
   * Whether to fail fast on validation errors
   */
  failFast?: boolean;
  
  /**
   * Custom validation function
   */
  customValidation?: (config: any) => Promise<void>;
}

/**
 * Default configuration options
 */
const DEFAULT_OPTIONS: StandardizedConfigOptions = {
  envFilePaths: ['.env.local', '.env'],
  validateOnStartup: true,
  failFast: true,
  requiredEnvVars: [
    'SERVICE_NAME',
    'SERVICE_PORT',
    'NODE_ENV',
    'DATABASE_URL',
    'JWT_SECRET',
    'GATEWAY_SECRET',
  ],
};

/**
 * Standardized Configuration Module
 * Provides consistent configuration setup across all services
 */
@Global()
@Module({})
export class StandardizedConfigModule {
  /**
   * Create a standardized configuration module
   */
  static forRoot(options: StandardizedConfigOptions = {}): DynamicModule {
    const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
    
    return {
      module: StandardizedConfigModule,
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: mergedOptions.envFilePaths,
          validate: mergedOptions.validateOnStartup 
            ? (config) => this.validateConfiguration(config, mergedOptions)
            : undefined,
          validationOptions: {
            allowUnknown: true,
            abortEarly: mergedOptions.failFast,
          },
        }),
      ],
      providers: [
        {
          provide: 'CONFIG_OPTIONS',
          useValue: mergedOptions,
        },
        {
          provide: 'CONFIG_VALIDATOR',
          useFactory: () => new ConfigurationValidator(mergedOptions),
        },
      ],
      exports: [ConfigService, 'CONFIG_OPTIONS', 'CONFIG_VALIDATOR'],
    };
  }

  /**
   * Create configuration module for specific service
   */
  static forService(serviceName: string, options: StandardizedConfigOptions = {}): DynamicModule {
    const serviceOptions: StandardizedConfigOptions = {
      ...options,
      requiredEnvVars: [
        ...(DEFAULT_OPTIONS.requiredEnvVars || []),
        ...(options.requiredEnvVars || []),
      ],
      customValidation: async (config) => {
        // Service-specific validation
        await this.validateServiceSpecificConfig(serviceName, config);
        
        // Custom validation if provided
        if (options.customValidation) {
          await options.customValidation(config);
        }
      },
    };

    return this.forRoot(serviceOptions);
  }

  /**
   * Validate configuration
   */
  private static async validateConfiguration(
    config: Record<string, any>,
    options: StandardizedConfigOptions
  ): Promise<Record<string, any>> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 1. Validate required environment variables
      if (options.requiredEnvVars) {
        const envValidation = ConfigValidationUtil.validateRequiredEnvVars(options.requiredEnvVars);
        errors.push(...envValidation.errors);
        warnings.push(...envValidation.warnings);
      }

      // 2. Validate environment consistency
      const envConsistency = ConfigValidationUtil.validateEnvironmentConsistency();
      errors.push(...envConsistency.errors);
      warnings.push(...envConsistency.warnings);

      // 3. Validate JWT secret
      const jwtValidation = ConfigValidationUtil.validateJwtSecret(config.JWT_SECRET);
      errors.push(...jwtValidation.errors);
      warnings.push(...jwtValidation.warnings);

      // 4. Validate database URL
      if (config.DATABASE_URL && !ConfigValidationUtil.validateDatabaseUrl(config.DATABASE_URL)) {
        errors.push('DATABASE_URL is not a valid database URL');
      }

      // 5. Validate Redis URL if provided
      if (config.REDIS_URL && !ConfigValidationUtil.validateRedisUrl(config.REDIS_URL)) {
        errors.push('REDIS_URL is not a valid Redis URL');
      }

      // 6. Validate port number
      const port = parseInt(config.SERVICE_PORT || config.PORT);
      if (!ConfigValidationUtil.validatePort(port)) {
        errors.push('SERVICE_PORT must be a valid port number (1-65535)');
      }

      // 7. Validate configuration class if provided
      if (options.configClass) {
        const classValidation = await ConfigValidationUtil.validateConfig(options.configClass, config);
        errors.push(...classValidation.errors);
        warnings.push(...classValidation.warnings);
      }

      // 8. Custom validation
      if (options.customValidation) {
        await options.customValidation(config);
      }

      // Log warnings
      if (warnings.length > 0) {
        console.warn('⚠️  Configuration Warnings:');
        warnings.forEach(warning => console.warn(`   - ${warning}`));
      }

      // Handle errors
      if (errors.length > 0) {
        console.error('❌ Configuration Validation Errors:');
        errors.forEach(error => console.error(`   - ${error}`));
        
        if (options.failFast) {
          throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
        }
      }

      // Log successful validation
      if (errors.length === 0) {
        const serviceName = config.SERVICE_NAME || 'Unknown Service';
        const environment = config.NODE_ENV || 'development';
        const serviceEnv = config.SERVICE_ENVIRONMENT || 'unknown';
        
        console.log(`✅ Configuration validated successfully for ${serviceName}`);
        console.log(`   Environment: ${environment} (${serviceEnv})`);
        console.log(`   Port: ${config.SERVICE_PORT || config.PORT}`);
        
        if (warnings.length > 0) {
          console.log(`   Warnings: ${warnings.length}`);
        }
      }

      return config;
    } catch (error) {
      console.error('❌ Configuration validation failed:', error.message);
      if (options.failFast) {
        throw error;
      }
      return config;
    }
  }

  /**
   * Validate service-specific configuration
   */
  private static async validateServiceSpecificConfig(
    serviceName: string,
    config: Record<string, any>
  ): Promise<void> {
    switch (serviceName) {
      case 'api-gateway':
        await this.validateApiGatewayConfig(config);
        break;
      case 'user-service':
        await this.validateUserServiceConfig(config);
        break;
      case 'blockchain-service':
        await this.validateBlockchainServiceConfig(config);
        break;
      // Add more service-specific validations as needed
    }
  }

  /**
   * Validate API Gateway specific configuration
   */
  private static async validateApiGatewayConfig(config: Record<string, any>): Promise<void> {
    const requiredServices = [
      'USER_SERVICE_URL',
      'PROFILE_ANALYSIS_SERVICE_URL',
      'NFT_GENERATION_SERVICE_URL',
      'BLOCKCHAIN_SERVICE_URL',
      'PROJECT_SERVICE_URL',
      'MARKETPLACE_SERVICE_URL',
      'NOTIFICATION_SERVICE_URL',
      'ANALYTICS_SERVICE_URL',
    ];

    const serviceUrls: Record<string, string> = {};
    for (const service of requiredServices) {
      serviceUrls[service] = config[service];
    }

    const validation = ConfigValidationUtil.validateServiceDependencies(serviceUrls);
    if (!validation.isValid) {
      throw new Error(`API Gateway configuration invalid: ${validation.errors.join(', ')}`);
    }
  }

  /**
   * Validate User Service specific configuration
   */
  private static async validateUserServiceConfig(config: Record<string, any>): Promise<void> {
    // User service specific validations
    if (config.PASSWORD_RESET_EXPIRY) {
      const expiry = parseInt(config.PASSWORD_RESET_EXPIRY);
      if (!ConfigValidationUtil.validateNumericRange(expiry, 300, 86400)) { // 5 minutes to 24 hours
        throw new Error('PASSWORD_RESET_EXPIRY must be between 300 and 86400 seconds');
      }
    }
  }

  /**
   * Validate Blockchain Service specific configuration
   */
  private static async validateBlockchainServiceConfig(config: Record<string, any>): Promise<void> {
    // Blockchain service specific validations
    if (config.BLOCKCHAIN_NETWORK) {
      const validNetworks = ['mainnet', 'testnet', 'localhost'];
      if (!validNetworks.includes(config.BLOCKCHAIN_NETWORK)) {
        throw new Error(`BLOCKCHAIN_NETWORK must be one of: ${validNetworks.join(', ')}`);
      }
    }
  }
}

/**
 * Configuration validator service
 */
export class ConfigurationValidator {
  constructor(private readonly options: StandardizedConfigOptions) {}

  /**
   * Validate configuration at runtime
   */
  async validateRuntime(config: any): Promise<void> {
    if (this.options.customValidation) {
      await this.options.customValidation(config);
    }
  }

  /**
   * Get configuration health status
   */
  getHealthStatus(): { status: string; details: any } {
    return {
      status: 'healthy',
      details: {
        environment: EnvironmentValidator.getDefaultEnvironment(),
        serviceEnvironment: EnvironmentValidator.getDefaultServiceEnvironment(),
        validationEnabled: this.options.validateOnStartup,
        requiredVarsCount: this.options.requiredEnvVars?.length || 0,
      },
    };
  }
}
