import { api } from '@/lib/api'
import {
  EvolutionTimeline,
  EvolutionEvent,
  EvolutionAnalytics,
  EvolutionFilters,
  EvolutionStats,
  CreateEvolutionEventRequest,
  UpdateEvolutionRequest,
  EvolutionNotification,
  EvolutionInsights,
  PredictedEvolution,
  EvolutionEventType,
  EvolutionTriggerType
} from '@/types/evolution.types'

export class EvolutionService {
  // ===== EVOLUTION TIMELINE =====
  
  async getEvolutionTimeline(nftId: string, filters?: EvolutionFilters): Promise<EvolutionTimeline> {
    try {
      const response = await api.get(`/evolution/nft/${nftId}/timeline`, { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution timeline:', error)
      throw new Error('Failed to load evolution timeline')
    }
  }

  async getEvolutionEvents(nftId: string, filters?: EvolutionFilters): Promise<{
    events: EvolutionEvent[]
    total: number
    page: number
    limit: number
  }> {
    try {
      const response = await api.get(`/evolution/nft/${nftId}/events`, { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution events:', error)
      throw new Error('Failed to load evolution events')
    }
  }

  async getEvolutionEvent(eventId: string): Promise<EvolutionEvent> {
    try {
      const response = await api.get(`/evolution/events/${eventId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution event:', error)
      throw new Error('Failed to load evolution event details')
    }
  }

  async createEvolutionEvent(data: CreateEvolutionEventRequest): Promise<EvolutionEvent> {
    try {
      const response = await api.post('/evolution/events', data)
      return response.data
    } catch (error) {
      console.error('Failed to create evolution event:', error)
      throw new Error('Failed to create evolution event')
    }
  }

  // ===== EVOLUTION ANALYTICS =====

  async getEvolutionAnalytics(nftId: string, timeframe?: '7d' | '30d' | '90d' | '1y'): Promise<EvolutionAnalytics> {
    try {
      const response = await api.get(`/evolution/nft/${nftId}/analytics`, { 
        params: { timeframe } 
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution analytics:', error)
      throw new Error('Failed to load evolution analytics')
    }
  }

  async getEvolutionStats(nftId: string): Promise<EvolutionStats> {
    try {
      const response = await api.get(`/evolution/nft/${nftId}/stats`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution stats:', error)
      throw new Error('Failed to load evolution statistics')
    }
  }

  async getEvolutionInsights(nftId: string): Promise<EvolutionInsights> {
    try {
      const response = await api.get(`/evolution/nft/${nftId}/insights`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution insights:', error)
      throw new Error('Failed to load evolution insights')
    }
  }

  // ===== EVOLUTION PREDICTIONS =====

  async getPredictedEvolution(nftId: string): Promise<PredictedEvolution> {
    try {
      const response = await api.get(`/evolution/nft/${nftId}/prediction`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution prediction:', error)
      throw new Error('Failed to load evolution prediction')
    }
  }

  async updateEvolutionPrediction(nftId: string): Promise<PredictedEvolution> {
    try {
      const response = await api.post(`/evolution/nft/${nftId}/prediction/update`)
      return response.data
    } catch (error) {
      console.error('Failed to update evolution prediction:', error)
      throw new Error('Failed to update evolution prediction')
    }
  }

  // ===== EVOLUTION TRIGGERS =====

  async triggerEvolution(nftId: string, data: UpdateEvolutionRequest): Promise<EvolutionEvent> {
    try {
      const response = await api.post(`/evolution/nft/${nftId}/trigger`, data)
      return response.data
    } catch (error) {
      console.error('Failed to trigger evolution:', error)
      throw new Error('Failed to trigger NFT evolution')
    }
  }

  async updateNFTScore(nftId: string, newScore: number, trigger: any): Promise<EvolutionEvent> {
    try {
      const response = await api.patch(`/evolution/nft/${nftId}/score`, { 
        score: newScore, 
        trigger 
      })
      return response.data
    } catch (error) {
      console.error('Failed to update NFT score:', error)
      throw new Error('Failed to update NFT score')
    }
  }

  // ===== COMPARATIVE ANALYTICS =====

  async getEvolutionComparison(nftIds: string[]): Promise<{
    nfts: Array<{
      nftId: string
      analytics: EvolutionAnalytics
      ranking: number
    }>
    averages: {
      evolutionVelocity: number
      averageScoreGain: number
      totalEvolutions: number
    }
  }> {
    try {
      const response = await api.post('/evolution/compare', { nftIds })
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution comparison:', error)
      throw new Error('Failed to load evolution comparison')
    }
  }

  async getGlobalEvolutionStats(): Promise<{
    totalEvolutions: number
    averageEvolutionTime: number
    topPerformers: Array<{
      nftId: string
      name: string
      evolutionCount: number
      currentScore: number
    }>
    evolutionTrends: Array<{
      date: string
      evolutionCount: number
      averageScore: number
    }>
  }> {
    try {
      const response = await api.get('/evolution/global/stats')
      return response.data
    } catch (error) {
      console.error('Failed to fetch global evolution stats:', error)
      throw new Error('Failed to load global evolution statistics')
    }
  }

  // ===== EVOLUTION NOTIFICATIONS =====

  async getEvolutionNotifications(userId?: string): Promise<EvolutionNotification[]> {
    try {
      const endpoint = userId ? `/evolution/users/${userId}/notifications` : '/evolution/notifications'
      const response = await api.get(endpoint)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution notifications:', error)
      throw new Error('Failed to load evolution notifications')
    }
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      await api.patch(`/evolution/notifications/${notificationId}/read`)
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
      throw new Error('Failed to update notification')
    }
  }

  // ===== EVOLUTION HISTORY =====

  async getEvolutionHistory(filters?: {
    userId?: string
    campaignId?: string
    eventType?: EvolutionEventType
    triggerType?: EvolutionTriggerType
    dateRange?: { start: string; end: string }
    page?: number
    limit?: number
  }): Promise<{
    events: EvolutionEvent[]
    total: number
    page: number
    limit: number
  }> {
    try {
      const response = await api.get('/evolution/history', { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution history:', error)
      throw new Error('Failed to load evolution history')
    }
  }

  async exportEvolutionData(nftId: string, format: 'json' | 'csv' | 'pdf'): Promise<Blob> {
    try {
      const response = await api.get(`/evolution/nft/${nftId}/export`, {
        params: { format },
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to export evolution data:', error)
      throw new Error('Failed to export evolution data')
    }
  }

  // ===== EVOLUTION MILESTONES =====

  async getEvolutionMilestones(nftId: string): Promise<Array<{
    id: string
    name: string
    description: string
    achieved: boolean
    achievedAt?: string
    progress: number
    requirement: string
    reward?: string
  }>> {
    try {
      const response = await api.get(`/evolution/nft/${nftId}/milestones`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution milestones:', error)
      throw new Error('Failed to load evolution milestones')
    }
  }

  async claimMilestoneReward(nftId: string, milestoneId: string): Promise<{
    success: boolean
    reward: any
    message: string
  }> {
    try {
      const response = await api.post(`/evolution/nft/${nftId}/milestones/${milestoneId}/claim`)
      return response.data
    } catch (error) {
      console.error('Failed to claim milestone reward:', error)
      throw new Error('Failed to claim milestone reward')
    }
  }

  // ===== UTILITY METHODS =====

  async searchEvolutionEvents(query: string, filters?: EvolutionFilters): Promise<EvolutionEvent[]> {
    try {
      const response = await api.get('/evolution/search', { 
        params: { q: query, ...filters } 
      })
      return response.data
    } catch (error) {
      console.error('Failed to search evolution events:', error)
      throw new Error('Failed to search evolution events')
    }
  }

  async getEvolutionLeaderboard(timeframe = '30d', limit = 50): Promise<Array<{
    nftId: string
    name: string
    currentScore: number
    evolutionCount: number
    evolutionVelocity: number
    rank: number
  }>> {
    try {
      const response = await api.get('/evolution/leaderboard', { 
        params: { timeframe, limit } 
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch evolution leaderboard:', error)
      throw new Error('Failed to load evolution leaderboard')
    }
  }
}

export const evolutionService = new EvolutionService()
