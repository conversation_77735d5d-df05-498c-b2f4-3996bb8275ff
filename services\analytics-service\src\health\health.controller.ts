import { <PERSON>, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthCheck, HealthCheckService, HealthCheckResult } from '@nestjs/terminus';
import { PrismaService } from '../prisma/prisma.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    private health: HealthCheckService,
    private prisma: PrismaService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Health check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  @HealthCheck()
  check(): Promise<HealthCheckResult> {
    return this.health.check([
      () => this.databaseHealthCheck(),
    ]);
  }

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check' })
  @ApiResponse({ status: 200, description: 'Service is running' })
  simpleCheck() {
    this.logger.log('Simple health check requested');
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'analytics-service',
      version: '1.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }

  @Get('detailed')
  @ApiOperation({ summary: 'Detailed health check' })
  @ApiResponse({ status: 200, description: 'Detailed service health information' })
  async detailedCheck() {
    try {
      this.logger.log('Detailed health check requested');
      
      const databaseHealthy = await this.prisma.isHealthy();
      
      return {
        status: databaseHealthy ? 'ok' : 'degraded',
        timestamp: new Date().toISOString(),
        service: {
          name: 'analytics-service',
          version: '1.0.0',
          uptime: process.uptime(),
          environment: process.env.NODE_ENV || 'development',
        },
        database: {
          status: databaseHealthy ? 'connected' : 'disconnected',
          type: 'postgresql',
        },
        system: {
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          platform: process.platform,
          nodeVersion: process.version,
        },
      };
    } catch (error) {
      this.logger.error('Detailed health check failed', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  @Get('database')
  @ApiOperation({ summary: 'Database health check' })
  @ApiResponse({ status: 200, description: 'Database connection status' })
  async databaseCheck() {
    try {
      this.logger.log('Database health check requested');
      const isHealthy = await this.prisma.isHealthy();
      
      return {
        status: isHealthy ? 'ok' : 'error',
        database: isHealthy ? 'connected' : 'disconnected',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Database health check failed', error);
      return {
        status: 'error',
        database: 'disconnected',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  private async databaseHealthCheck(): Promise<any> {
    const isHealthy = await this.prisma.isHealthy();
    if (!isHealthy) {
      throw new Error('Database connection failed');
    }
    return { database: { status: 'up' } };
  }
}
