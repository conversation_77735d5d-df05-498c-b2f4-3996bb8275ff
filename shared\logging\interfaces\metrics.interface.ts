/**
 * Metrics Collection Interfaces
 * Defines the standard metrics structure for all services
 */

/**
 * Metrics service interface
 */
export interface IMetricsService {
  // Basic metric operations
  counter(name: string, value?: number, tags?: MetricTags): void;
  gauge(name: string, value: number, tags?: MetricTags): void;
  histogram(name: string, value: number, tags?: MetricTags): void;
  timer(name: string, tags?: MetricTags): MetricTimer;
  
  // Convenience methods
  increment(name: string, tags?: MetricTags): void;
  decrement(name: string, tags?: MetricTags): void;
  timing(name: string, duration: number, tags?: MetricTags): void;
  
  // Business metrics
  recordBusinessMetric(metric: BusinessMetric): void;
  recordPerformanceMetric(metric: PerformanceMetric): void;
  recordSecurityMetric(metric: SecurityMetric): void;
  recordHealthMetric(metric: HealthMetric): void;
  
  // Metric retrieval
  getMetrics(): Promise<MetricSnapshot[]>;
  getMetric(name: string): Promise<MetricSnapshot | null>;
  
  // Registry management
  register(metric: CustomMetric): void;
  unregister(name: string): void;
  clear(): void;
}

/**
 * Metric tags interface
 */
export interface MetricTags {
  service: string;
  environment: string;
  version: string;
  [key: string]: string | number | boolean;
}

/**
 * Metric timer interface
 */
export interface MetricTimer {
  stop(tags?: MetricTags): number;
  elapsed(): number;
}

/**
 * Metric types enumeration
 */
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  SUMMARY = 'summary',
  TIMER = 'timer',
}

/**
 * Business metric interface
 */
export interface BusinessMetric {
  name: string;
  value: number;
  type: MetricType;
  category: BusinessMetricCategory;
  description?: string;
  unit?: string;
  timestamp?: Date;
  tags?: MetricTags;
  metadata?: Record<string, any>;
}

/**
 * Performance metric interface
 */
export interface PerformanceMetric {
  name: string;
  value: number;
  type: MetricType;
  category: PerformanceMetricCategory;
  description?: string;
  unit?: string;
  timestamp?: Date;
  tags?: MetricTags;
  threshold?: {
    warning: number;
    critical: number;
  };
}

/**
 * Security metric interface
 */
export interface SecurityMetric {
  name: string;
  value: number;
  type: MetricType;
  category: SecurityMetricCategory;
  severity: SecurityMetricSeverity;
  description?: string;
  timestamp?: Date;
  tags?: MetricTags;
  details?: Record<string, any>;
}

/**
 * Health metric interface
 */
export interface HealthMetric {
  name: string;
  value: number;
  type: MetricType;
  category: HealthMetricCategory;
  status: HealthStatus;
  description?: string;
  timestamp?: Date;
  tags?: MetricTags;
  details?: Record<string, any>;
}

/**
 * Custom metric interface
 */
export interface CustomMetric {
  name: string;
  type: MetricType;
  description: string;
  unit?: string;
  labelNames?: string[];
  buckets?: number[]; // For histograms
  percentiles?: number[]; // For summaries
}

/**
 * Metric snapshot interface
 */
export interface MetricSnapshot {
  name: string;
  type: MetricType;
  value: number | MetricValue[];
  timestamp: Date;
  tags: MetricTags;
  description?: string;
  unit?: string;
}

/**
 * Metric value interface (for histograms/summaries)
 */
export interface MetricValue {
  value: number;
  timestamp: Date;
  labels?: Record<string, string>;
}

/**
 * Business metric categories
 */
export enum BusinessMetricCategory {
  USER_ENGAGEMENT = 'user_engagement',
  TRANSACTION = 'transaction',
  REVENUE = 'revenue',
  CONVERSION = 'conversion',
  RETENTION = 'retention',
  GROWTH = 'growth',
  FEATURE_USAGE = 'feature_usage',
  CONTENT = 'content',
  MARKETPLACE = 'marketplace',
  NFT = 'nft',
  BLOCKCHAIN = 'blockchain',
}

/**
 * Performance metric categories
 */
export enum PerformanceMetricCategory {
  RESPONSE_TIME = 'response_time',
  THROUGHPUT = 'throughput',
  ERROR_RATE = 'error_rate',
  AVAILABILITY = 'availability',
  RESOURCE_USAGE = 'resource_usage',
  DATABASE = 'database',
  CACHE = 'cache',
  EXTERNAL_API = 'external_api',
  QUEUE = 'queue',
  NETWORK = 'network',
}

/**
 * Security metric categories
 */
export enum SecurityMetricCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  THREAT_DETECTION = 'threat_detection',
  VULNERABILITY = 'vulnerability',
  COMPLIANCE = 'compliance',
  AUDIT = 'audit',
  INCIDENT = 'incident',
}

/**
 * Health metric categories
 */
export enum HealthMetricCategory {
  SERVICE_HEALTH = 'service_health',
  DATABASE_HEALTH = 'database_health',
  CACHE_HEALTH = 'cache_health',
  EXTERNAL_SERVICE_HEALTH = 'external_service_health',
  INFRASTRUCTURE_HEALTH = 'infrastructure_health',
  APPLICATION_HEALTH = 'application_health',
}

/**
 * Security metric severity
 */
export enum SecurityMetricSeverity {
  INFO = 'info',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Health status enumeration
 */
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  UNKNOWN = 'unknown',
}

/**
 * Metric aggregation interface
 */
export interface MetricAggregation {
  name: string;
  type: MetricType;
  aggregationType: AggregationType;
  value: number;
  count: number;
  min?: number;
  max?: number;
  avg?: number;
  sum?: number;
  percentiles?: Record<string, number>;
  timestamp: Date;
  timeRange: {
    start: Date;
    end: Date;
  };
  tags: MetricTags;
}

/**
 * Aggregation types
 */
export enum AggregationType {
  SUM = 'sum',
  AVG = 'avg',
  MIN = 'min',
  MAX = 'max',
  COUNT = 'count',
  RATE = 'rate',
  PERCENTILE = 'percentile',
}

/**
 * Metric alert interface
 */
export interface MetricAlert {
  id: string;
  name: string;
  description: string;
  metricName: string;
  condition: AlertCondition;
  threshold: number;
  severity: AlertSeverity;
  enabled: boolean;
  tags?: MetricTags;
  notifications: AlertNotification[];
  cooldown?: number; // Minutes
  evaluationWindow?: number; // Minutes
}

/**
 * Alert condition interface
 */
export interface AlertCondition {
  operator: AlertOperator;
  value: number;
  duration?: number; // Minutes
  aggregation?: AggregationType;
}

/**
 * Alert operators
 */
export enum AlertOperator {
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  EQUAL = 'eq',
  NOT_EQUAL = 'ne',
  INCREASE = 'increase',
  DECREASE = 'decrease',
}

/**
 * Alert severity
 */
export enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  CRITICAL = 'critical',
  EMERGENCY = 'emergency',
}

/**
 * Alert notification interface
 */
export interface AlertNotification {
  type: NotificationType;
  target: string;
  template?: string;
  enabled: boolean;
}

/**
 * Notification types
 */
export enum NotificationType {
  EMAIL = 'email',
  SLACK = 'slack',
  WEBHOOK = 'webhook',
  SMS = 'sms',
  PAGERDUTY = 'pagerduty',
}

/**
 * Metric dashboard interface
 */
export interface MetricDashboard {
  id: string;
  name: string;
  description: string;
  widgets: DashboardWidget[];
  tags?: string[];
  refreshInterval?: number; // Seconds
  timeRange?: TimeRange;
}

/**
 * Dashboard widget interface
 */
export interface DashboardWidget {
  id: string;
  type: WidgetType;
  title: string;
  metrics: string[];
  visualization: VisualizationType;
  options?: Record<string, any>;
  position: WidgetPosition;
}

/**
 * Widget types
 */
export enum WidgetType {
  METRIC = 'metric',
  CHART = 'chart',
  TABLE = 'table',
  GAUGE = 'gauge',
  COUNTER = 'counter',
  HEATMAP = 'heatmap',
}

/**
 * Visualization types
 */
export enum VisualizationType {
  LINE = 'line',
  BAR = 'bar',
  PIE = 'pie',
  AREA = 'area',
  SCATTER = 'scatter',
  GAUGE = 'gauge',
  NUMBER = 'number',
  TABLE = 'table',
}

/**
 * Widget position interface
 */
export interface WidgetPosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Time range interface
 */
export interface TimeRange {
  start: Date;
  end: Date;
  relative?: string; // e.g., "1h", "24h", "7d"
}
