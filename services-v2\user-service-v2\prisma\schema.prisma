// User Service V2 Database Schema
// Database Per Service Pattern - This is the USER SERVICE database only

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management Tables (User Service Domain)
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  
  // Profile Information
  firstName String?
  lastName  String?
  avatar    String?
  bio       String?
  
  // Account Status
  isActive     Boolean @default(true)
  isVerified   Boolean @default(false)
  emailVerified Boolean @default(false)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLogin DateTime?
  
  // Relationships within User Service
  sessions UserSession[]
  preferences UserPreference[]
  
  @@map("users")
}

model UserSession {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  
  // Session Details
  ipAddress String?
  userAgent String?
  device    String?
  location  String?
  
  // Session Status
  isActive  Boolean @default(true)
  expiresAt DateTime
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_sessions")
}

model UserPreference {
  id     String @id @default(cuid())
  userId String
  
  // Notification Preferences
  emailNotifications    Boolean @default(true)
  pushNotifications     Boolean @default(true)
  marketingEmails       Boolean @default(false)
  
  // Privacy Preferences
  profileVisibility     String @default("public") // public, private, friends
  showEmail            Boolean @default(false)
  showLastLogin        Boolean @default(false)
  
  // Platform Preferences
  theme                String @default("light") // light, dark, auto
  language             String @default("en")
  timezone             String @default("UTC")
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([userId])
  @@map("user_preferences")
}

model UserRole {
  id          String @id @default(cuid())
  userId      String
  role        String // admin, user, moderator, etc.
  permissions Json?  // Flexible permissions structure
  
  // Role Status
  isActive Boolean @default(true)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  expiresAt DateTime?
  
  @@unique([userId, role])
  @@map("user_roles")
}

model UserActivity {
  id       String @id @default(cuid())
  userId   String
  
  // Activity Details
  action      String // login, logout, profile_update, etc.
  resource    String? // What was acted upon
  metadata    Json?   // Additional activity data
  
  // Context
  ipAddress String?
  userAgent String?
  
  // Timestamps
  createdAt DateTime @default(now())
  
  @@map("user_activities")
}

// Password Reset and Email Verification
model UserVerification {
  id     String @id @default(cuid())
  userId String
  
  // Verification Details
  type      String // email_verification, password_reset, etc.
  token     String @unique
  code      String? // Optional verification code
  
  // Status
  isUsed    Boolean @default(false)
  expiresAt DateTime
  
  // Timestamps
  createdAt DateTime @default(now())
  usedAt    DateTime?
  
  @@map("user_verifications")
}

// OAuth and Social Login Support
model UserOAuth {
  id       String @id @default(cuid())
  userId   String
  
  // OAuth Details
  provider    String // google, twitter, github, etc.
  providerId  String // ID from the OAuth provider
  email       String?
  
  // OAuth Data
  accessToken  String?
  refreshToken String?
  expiresAt    DateTime?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([provider, providerId])
  @@unique([userId, provider])
  @@map("user_oauth")
}
