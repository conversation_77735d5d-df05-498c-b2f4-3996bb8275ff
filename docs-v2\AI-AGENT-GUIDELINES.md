# 🤖 **AI AGENT GUIDELINES FOR SOCIAL NFT PLATFORM**

## **📋 MANDATORY AI AGENT PRACTICES**

**Purpose**: Establish comprehensive guidelines for AI agents working on the Social NFT Platform  
**Scope**: All AI agents, development tasks, and platform interactions  
**Authority**: Single source of truth for AI agent behavior and standards

---

## 🎯 **CORE AI AGENT PRINCIPLES**

### **1. Documentation-First Approach**
- **ALWAYS read platform documentation** before making any changes
- **Understand architecture** from `/docs` and `/docs-v2` directories
- **Follow established patterns** from existing implementations
- **Document all decisions** and rationale for future reference

### **2. Platform Architecture Understanding**
- **Microservices Architecture**: Each service is independent with its own database
- **API Gateway Pattern**: All external communication goes through API Gateway (port 3010)
- **Database Per Service**: Each service has its own PostgreSQL database
- **Shared Infrastructure**: Common patterns for auth, logging, configuration
- **Frontend Integration**: Frontend-headless communicates only through API Gateway

### **3. Service Standardization Priority**
- **User Service**: Template service - fully standardized and working
- **Profile Analysis Service**: Core business service - must be fully implemented
- **Other Services**: Follow User Service template exactly
- **No Direct Communication**: Services communicate only through API Gateway

---

## 🔧 **DEVELOPMENT WORKFLOW RULES**

### **Phase 1: Information Gathering**
```bash
# MANDATORY: Read documentation first
1. Read /docs-v2/ comprehensive guidelines
2. Read /docs/ existing platform documentation  
3. Use codebase-retrieval for specific implementation details
4. Understand service dependencies and architecture
```

### **Phase 2: Planning**
```bash
# MANDATORY: Create detailed plan
1. Analyze current state vs desired state
2. Identify all files that need changes
3. Plan implementation sequence
4. Consider impact on other services
5. Get user approval before proceeding
```

### **Phase 3: Implementation**
```bash
# MANDATORY: Follow standardized patterns
1. Use User Service as template for all services
2. Preserve business logic during standardization
3. Apply shared infrastructure patterns
4. Test each change incrementally
5. Commit frequently with descriptive messages
```

---

## 📊 **SERVICE STANDARDIZATION RULES**

### **Template Service Pattern (User Service)**
```typescript
// Standard service structure
src/
├── auth/           # Authentication guards and decorators
├── config/         # Configuration management
├── health/         # Health check endpoints
├── prisma/         # Database service and schema
├── shared/         # Shared utilities and interfaces
├── users/          # Business logic modules
├── app.module.ts   # Main application module
└── main.ts         # Application bootstrap
```

### **Mandatory Service Features**
- ✅ **Health Check Endpoints**: `/api/health/simple` and `/api/health`
- ✅ **Authentication Integration**: JWT guards and decorators
- ✅ **Database Integration**: Prisma service with proper connection handling
- ✅ **Error Handling**: Standardized error responses
- ✅ **Logging**: Structured logging with correlation IDs
- ✅ **Configuration**: Environment-based configuration management

### **Database Configuration Standards**
```typescript
// Standard database naming: {service_name}_service
DATABASE_URL="postgresql://postgres:1111@localhost:5432/user_service?schema=public"
DATABASE_URL="postgresql://postgres:1111@localhost:5432/profile_analysis_service?schema=public"
DATABASE_URL="postgresql://postgres:1111@localhost:5432/nft_generation_service?schema=public"
```

---

## 🚨 **CRITICAL RESTRICTIONS**

### **What AI Agents MUST NOT Do**
- ❌ **Never bypass API Gateway** - No direct service-to-service communication
- ❌ **Never modify shared infrastructure** without understanding impact
- ❌ **Never remove business logic** during standardization
- ❌ **Never commit broken code** - Always test before committing
- ❌ **Never ignore existing patterns** - Follow established conventions

### **What AI Agents MUST Ask Permission For**
- 🔒 **Installing dependencies** - Use package managers, get approval first
- 🔒 **Changing database schemas** - Coordinate with other services
- 🔒 **Modifying API Gateway** - Critical infrastructure changes
- 🔒 **Deploying code** - Production deployments require approval
- 🔒 **Changing security configurations** - Security-sensitive changes

---

## 🎯 **QUALITY STANDARDS**

### **Code Quality Requirements**
- **TypeScript Strict Mode**: All services must use strict TypeScript
- **Error Handling**: Comprehensive try-catch with proper error responses
- **Validation**: Input validation using class-validator decorators
- **Documentation**: JSDoc comments for all public methods
- **Testing**: Unit tests for critical business logic

### **Performance Standards**
- **Response Time**: < 500ms for 95% of requests
- **Error Rate**: < 1% error rate
- **Database Connections**: Proper connection pooling and cleanup
- **Memory Usage**: Monitor and optimize memory consumption

### **Security Standards**
- **Authentication**: JWT-based authentication for all protected endpoints
- **Authorization**: Role-based access control where applicable
- **Input Validation**: Sanitize and validate all inputs
- **Error Messages**: No sensitive information in error responses

---

## 📝 **COMMUNICATION PATTERNS**

### **AI Agent Reporting Format**
```markdown
## 🔍 ANALYSIS SUMMARY
- **Current State**: [Brief description]
- **Issues Found**: [List of problems]
- **Proposed Solution**: [Detailed plan]
- **Impact Assessment**: [Affected services/components]

## 📋 IMPLEMENTATION PLAN
1. [Step 1 with file changes]
2. [Step 2 with file changes]
3. [Step 3 with file changes]

## ✅ SUCCESS CRITERIA
- [Measurable outcome 1]
- [Measurable outcome 2]
- [Measurable outcome 3]
```

### **Progress Updates**
- **Regular Updates**: Provide status updates every 30 minutes for long tasks
- **Issue Reporting**: Immediately report any blockers or unexpected issues
- **Completion Confirmation**: Confirm successful completion with test results

---

## 🚀 **CONTINUOUS IMPROVEMENT**

### **Learning from Implementation**
- **Document Lessons Learned**: Update guidelines based on experience
- **Share Best Practices**: Contribute improvements to documentation
- **Feedback Integration**: Incorporate user feedback into future implementations

### **Platform Evolution**
- **Stay Updated**: Monitor platform changes and updates
- **Adapt Guidelines**: Update practices as platform evolves
- **Maintain Standards**: Ensure consistency across all implementations

---

**🎯 Following these guidelines ensures consistent, high-quality AI agent contributions to the Social NFT Platform!**
