import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Shared data components
import { StandardizedPrismaService } from '../../../../shared/data/services/prisma.service';

// Service-specific data services
import { DatabaseService } from './services/database.service';
import { RepositoryFactory } from './services/repository-factory.service';
import { DataHealthService } from './services/data-health.service';

// Import service-specific repositories
// Note: These would be imported based on the service's domain models

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    // Core data services
    StandardizedPrismaService,
    DatabaseService,
    RepositoryFactory,
    DataHealthService,
    
    // Provide services for injection
    {
      provide: 'DATABASE_SERVICE',
      useClass: DatabaseService,
    },
    {
      provide: 'REPOSITORY_FACTORY',
      useClass: RepositoryFactory,
    },
    {
      provide: 'DATA_HEALTH_SERVICE',
      useClass: DataHealthService,
    },
    {
      provide: 'PRISMA_SERVICE',
      useClass: StandardizedPrismaService,
    },
  ],
  exports: [
    StandardizedPrismaService,
    DatabaseService,
    RepositoryFactory,
    DataHealthService,
    'DATABASE_SERVICE',
    'REPOSITORY_FACTORY',
    'DATA_HEALTH_SERVICE',
    'PRISMA_SERVICE',
  ],
})
export class ServiceDataModule {}
