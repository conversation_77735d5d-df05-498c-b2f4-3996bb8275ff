'use client'

import React from 'react'
import { NFT } from '@/types/nft.types'
import EnhancedNFTCard from '@/components/dashboard/enhanced-nft-card'
import { 
  Squares2X2Icon,
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'

interface NFTGridProps {
  nfts: NFT[]
  isLoading?: boolean
  isError?: boolean
  error?: any
  isEmpty?: boolean
  viewMode?: 'grid' | 'list'
  onNFTClick?: (nft: NFT) => void
  onNFTView?: (nft: NFT) => void
  onNFTEdit?: (nft: NFT) => void
  onNFTShare?: (nft: NFT) => void
  onNFTMint?: (nft: NFT) => void
  onNFTList?: (nft: NFT) => void
  onRetry?: () => void
  showActions?: boolean
  className?: string
  emptyMessage?: string
  emptyDescription?: string
}

export default function NFTGrid({
  nfts,
  isLoading = false,
  isError = false,
  error,
  isEmpty = false,
  viewMode = 'grid',
  onNFTClick,
  onNFTView,
  onNFTEdit,
  onNFTShare,
  onNFTMint,
  onNFTList,
  onRetry,
  showActions = true,
  className = '',
  emptyMessage = 'No NFTs found',
  emptyDescription = 'Start by analyzing Twitter profiles to generate your first NFT!'
}: NFTGridProps) {

  // Loading state
  if (isLoading) {
    return (
      <div className={`${className}`}>
        <div className="animate-pulse">
          <div className={`grid gap-4 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}>
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-gray-200 rounded-lg h-80"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (isError) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading NFTs</h3>
          <p className="mt-1 text-sm text-gray-500">
            {error?.message || 'Failed to load NFTs. Please try again.'}
          </p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              Try Again
            </button>
          )}
        </div>
      </div>
    )
  }

  // Empty state
  if (isEmpty) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <Squares2X2Icon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">{emptyMessage}</h3>
          <p className="mt-1 text-sm text-gray-500">{emptyDescription}</p>
        </div>
      </div>
    )
  }

  // Grid view
  if (viewMode === 'grid') {
    return (
      <div className={`${className}`}>
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {nfts.map((nft) => (
            <EnhancedNFTCard
              key={nft.id}
              nft={nft}
              onClick={() => onNFTClick?.(nft)}
              onView={onNFTView}
              onEdit={onNFTEdit}
              onShare={onNFTShare}
              onMint={onNFTMint}
              onList={onNFTList}
              showActions={showActions}
            />
          ))}
        </div>
      </div>
    )
  }

  // List view
  return (
    <div className={`${className}`}>
      <div className="space-y-4">
        {nfts.map((nft) => (
          <div key={nft.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center space-x-4">
              {/* NFT Image */}
              <div className="flex-shrink-0">
                <img
                  src={nft.imageUrl || '/api/placeholder/80/80'}
                  alt={nft.name}
                  className="w-20 h-20 rounded-lg object-cover"
                />
              </div>

              {/* NFT Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {nft.name}
                    </h3>
                    <p className="text-sm text-gray-600">@{nft.twitterHandle}</p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {/* Rarity Badge */}
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      nft.rarity === 'legendary' ? 'bg-yellow-100 text-yellow-800' :
                      nft.rarity === 'epic' ? 'bg-purple-100 text-purple-800' :
                      nft.rarity === 'rare' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {nft.rarity.charAt(0).toUpperCase() + nft.rarity.slice(1)}
                    </span>

                    {/* Score */}
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Score: {nft.currentScore}
                    </span>
                  </div>
                </div>

                {/* Description */}
                {nft.description && (
                  <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                    {nft.description}
                  </p>
                )}

                {/* Footer */}
                <div className="mt-3 flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>Created: {new Date(nft.createdAt).toLocaleDateString()}</span>
                    {nft.blockchain && (
                      <span>Blockchain: {nft.blockchain}</span>
                    )}
                    {nft.status && (
                      <span>Status: {nft.status}</span>
                    )}
                  </div>

                  {/* Action Buttons */}
                  {showActions && (
                    <div className="flex items-center space-x-2">
                      {onNFTView && (
                        <button
                          onClick={() => onNFTView(nft)}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          View
                        </button>
                      )}
                      {onNFTEdit && (
                        <button
                          onClick={() => onNFTEdit(nft)}
                          className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                        >
                          Edit
                        </button>
                      )}
                      {onNFTShare && (
                        <button
                          onClick={() => onNFTShare(nft)}
                          className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                        >
                          Share
                        </button>
                      )}
                      {onNFTMint && nft.status === 'generated' && (
                        <button
                          onClick={() => onNFTMint(nft)}
                          className="text-green-600 hover:text-green-800 text-sm font-medium"
                        >
                          Mint
                        </button>
                      )}
                      {onNFTList && nft.status === 'minted' && (
                        <button
                          onClick={() => onNFTList(nft)}
                          className="text-orange-600 hover:text-orange-800 text-sm font-medium"
                        >
                          List
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
