# 🔍 Basic Logging Interceptor Foundation

## Overview
Simple request/response logging interceptor for monitoring API calls across all services.

## Basic Logging Interceptor Implementation

### 1. Simple Request Logging Interceptor
```typescript
// shared/interceptors/logging.interceptor.ts
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { LoggerService } from '../services/logger.service';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private readonly logger: LoggerService) {
    this.logger.setContext(LoggingInterceptor.name);
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    
    const { method, url, body, query, params } = request;
    const userAgent = request.get('User-Agent') || '';
    const ip = request.ip || request.connection.remoteAddress;
    
    const startTime = Date.now();
    
    // Log incoming request
    this.logger.log('Incoming Request', {
      method,
      url,
      userAgent,
      ip,
      body: this.sanitizeBody(body),
      query,
      params,
    });

    return next.handle().pipe(
      tap({
        next: (data) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          
          // Log successful response
          this.logger.log('Request Completed', {
            method,
            url,
            statusCode: response.statusCode,
            duration: `${duration}ms`,
            responseSize: JSON.stringify(data).length,
          });
        },
        error: (error) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          
          // Log error response
          this.logger.error('Request Failed', error.stack, {
            method,
            url,
            duration: `${duration}ms`,
            errorMessage: error.message,
            statusCode: error.status || 500,
          });
        },
      }),
    );
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;
    
    const sanitized = { ...body };
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });
    
    return sanitized;
  }
}
```

### 2. Request ID Generation Interceptor
```typescript
// shared/interceptors/request-id.interceptor.ts
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class RequestIdInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    
    // Generate or use existing request ID
    const requestId = request.headers['x-request-id'] || uuidv4();
    
    // Add to request for use in services
    request.requestId = requestId;
    
    // Add to response headers
    response.setHeader('x-request-id', requestId);
    
    return next.handle();
  }
}
```

### 3. Response Transform Interceptor
```typescript
// shared/interceptors/transform.interceptor.ts
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
  requestId?: string;
}

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
    const request = context.switchToHttp().getRequest();
    
    return next.handle().pipe(
      map(data => ({
        success: true,
        data,
        timestamp: new Date().toISOString(),
        requestId: request.requestId,
      })),
    );
  }
}
```

## Interceptor Registration

### 1. Global Interceptor Setup
```typescript
// app.module.ts
import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { LoggingInterceptor } from './shared/interceptors/logging.interceptor';
import { RequestIdInterceptor } from './shared/interceptors/request-id.interceptor';
import { TransformInterceptor } from './shared/interceptors/transform.interceptor';

@Module({
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: RequestIdInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
  ],
})
export class AppModule {}
```

### 2. Controller-Level Interceptor
```typescript
// feature/controllers/feature.controller.ts
import { Controller, Get, UseInterceptors } from '@nestjs/common';
import { LoggingInterceptor } from '../../shared/interceptors/logging.interceptor';

@Controller('feature')
@UseInterceptors(LoggingInterceptor)
export class FeatureController {
  @Get()
  getData() {
    return { message: 'Hello World' };
  }
}
```

### 3. Method-Level Interceptor
```typescript
// feature/controllers/feature.controller.ts
import { Controller, Get, UseInterceptors } from '@nestjs/common';
import { TransformInterceptor } from '../../shared/interceptors/transform.interceptor';

@Controller('feature')
export class FeatureController {
  @Get()
  @UseInterceptors(TransformInterceptor)
  getData() {
    return { message: 'Hello World' };
  }
}
```

## Usage Examples

### Request/Response Flow
```
1. Request comes in
2. RequestIdInterceptor adds request ID
3. LoggingInterceptor logs request details
4. Controller/Service processes request
5. TransformInterceptor formats response
6. LoggingInterceptor logs response details
7. Response sent to client
```

### Sample Log Output
```
[LoggingInterceptor] Incoming Request {
  method: 'GET',
  url: '/api/users/123',
  userAgent: 'Mozilla/5.0...',
  ip: '***********',
  query: { include: 'profile' },
  params: { id: '123' }
}

[LoggingInterceptor] Request Completed {
  method: 'GET',
  url: '/api/users/123',
  statusCode: 200,
  duration: '45ms',
  responseSize: 256
}
```

## AI Agent Implementation Rules

### Interceptor Creation Steps
1. Implement NestInterceptor interface
2. Inject required dependencies (LoggerService)
3. Extract request/response from ExecutionContext
4. Use RxJS operators for async processing
5. Handle both success and error cases
6. Sanitize sensitive data in logs

### Implementation Checklist
- [ ] Implements NestInterceptor interface
- [ ] Proper dependency injection
- [ ] Request/response extraction
- [ ] RxJS operators usage (tap, map)
- [ ] Error handling in tap operator
- [ ] Sensitive data sanitization
- [ ] Performance measurement (duration)

### Best Practices
- Always sanitize sensitive data (passwords, tokens)
- Measure and log request duration
- Include request ID for tracing
- Log both success and error cases
- Keep interceptor logic lightweight
- Use appropriate log levels

### Testing Considerations
- Mock LoggerService for unit tests
- Test both success and error scenarios
- Verify sensitive data is sanitized
- Check request ID generation/propagation
- Validate response transformation

This basic interceptor structure provides essential cross-cutting concerns like logging, request tracking, and response formatting across all services.
