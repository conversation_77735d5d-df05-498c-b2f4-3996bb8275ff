# 🔧 Basic Module Structure Pattern

## Overview
Standardized NestJS module structure for consistent dependency injection and organization.

## Standard Module Template

### Feature Module Structure
```typescript
// feature-name.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FeatureNameController } from './controllers/feature-name.controller';
import { FeatureNameService } from './services/feature-name.service';
import { FeatureNameRepository } from './repositories/feature-name.repository';
import { FeatureNameEntity } from './entities/feature-name.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([FeatureNameEntity]),
  ],
  controllers: [FeatureNameController],
  providers: [
    FeatureNameService,
    FeatureNameRepository,
  ],
  exports: [
    FeatureNameService,
    FeatureNameRepository,
  ],
})
export class FeatureNameModule {}
```

### Root App Module Structure
```typescript
// app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { Feature1Module } from './feature-1/feature-1.module';
import { Feature2Module } from './feature-2/feature-2.module';
import { SharedModule } from './shared/shared.module';
import { DatabaseConfig } from './shared/config/database.config';

@Module({
  imports: [
    // Configuration (always first)
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // Database (second)
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfig,
    }),
    
    // Shared modules (third)
    SharedModule,
    
    // Feature modules (last)
    Feature1Module,
    Feature2Module,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

## Module Organization Rules

### 1. Import Order
```typescript
@Module({
  imports: [
    // 1. External dependencies (TypeOrmModule, etc.)
    TypeOrmModule.forFeature([Entity]),
    
    // 2. Internal shared modules
    SharedModule,
    
    // 3. Other feature modules (if needed)
    OtherFeatureModule,
  ],
  // ... rest of module
})
```

### 2. Provider Organization
```typescript
@Module({
  providers: [
    // 1. Services (business logic)
    FeatureNameService,
    
    // 2. Repositories (data access)
    FeatureNameRepository,
    
    // 3. Guards (authentication/authorization)
    FeatureNameGuard,
    
    // 4. Interceptors (cross-cutting concerns)
    FeatureNameInterceptor,
    
    // 5. Custom providers with tokens
    {
      provide: 'FEATURE_CONFIG',
      useValue: featureConfig,
    },
  ],
})
```

### 3. Export Strategy
```typescript
@Module({
  exports: [
    // Export services that other modules need
    FeatureNameService,
    
    // Export repositories if needed by other features
    FeatureNameRepository,
    
    // Export custom providers
    'FEATURE_CONFIG',
  ],
})
```

## Dependency Injection Patterns

### Service Dependencies
```typescript
@Injectable()
export class FeatureNameService {
  constructor(
    // 1. Repository dependencies
    private readonly featureRepository: FeatureNameRepository,
    
    // 2. External service dependencies
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    
    // 3. Other feature services (if needed)
    private readonly otherFeatureService: OtherFeatureService,
    
    // 4. Custom injected tokens
    @Inject('FEATURE_CONFIG')
    private readonly config: FeatureConfig,
  ) {}
}
```

### Repository Dependencies
```typescript
@Injectable()
export class FeatureNameRepository {
  constructor(
    @InjectRepository(FeatureNameEntity)
    private readonly repository: Repository<FeatureNameEntity>,
    
    private readonly logger: LoggerService,
  ) {}
}
```

## AI Agent Implementation Rules

### Module Creation Checklist
- [ ] Import order follows standard pattern
- [ ] All controllers are listed in controllers array
- [ ] All services and repositories are in providers array
- [ ] Necessary services are exported for other modules
- [ ] TypeORM entities are properly imported
- [ ] Dependencies are injected in correct order

### Naming Conventions
- Module files: `feature-name.module.ts`
- Module classes: `FeatureNameModule`
- Exported services: Use descriptive names
- Custom providers: Use SCREAMING_SNAKE_CASE tokens

### Dependency Rules
- Services depend on repositories, not entities directly
- Controllers depend on services, not repositories
- Repositories depend on TypeORM, not business logic
- Shared services are injected through proper modules

## Validation Steps

Before completing module structure:
1. Verify all imports are present
2. Check dependency injection order
3. Ensure proper exports for inter-module communication
4. Validate naming conventions
5. Test module can be imported without circular dependencies
