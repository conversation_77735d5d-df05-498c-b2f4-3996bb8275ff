import { Injectable } from '@nestjs/common';
import { TwitterUser, TwitterCredentials, AuthResult } from '../interfaces/twitter.interfaces';

@Injectable()
export class TwitterService {
  private mockUsers: TwitterUser[] = [];

  constructor() {
    console.log('🐦 Mock Twitter Service initialized');
    this.initializeMockData();
  }

  private initializeMockData() {
    this.mockUsers = [
      {
        id: 'mock_user_1',
        username: 'nft_creator',
        displayName: 'NFT Creator',
        profileImageUrl: 'https://via.placeholder.com/150',
        followersCount: 1250,
        followingCount: 340,
        tweetsCount: 89,
        verified: false,
        description: 'Digital artist creating unique NFTs',
        location: 'San Francisco, CA',
        website: 'https://nftcreator.com',
        createdAt: '2020-01-15T10:30:00Z'
      },
      {
        id: 'mock_user_2',
        username: 'crypto_enthusiast',
        displayName: 'Crypto Enthusiast',
        profileImageUrl: 'https://via.placeholder.com/150',
        followersCount: 5670,
        followingCount: 890,
        tweetsCount: 234,
        verified: true,
        description: 'Blockchain advocate and NFT collector',
        location: 'New York, NY',
        website: 'https://cryptoenthusiast.com',
        createdAt: '2019-06-20T14:45:00Z'
      }
    ];
  }

  // Health check method
  async healthCheck() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString()
    };
  }

  // Get user by ID
  async getUserById(userId: string): Promise<TwitterUser | null> {
    return this.mockUsers.find(user => user.id === userId) || null;
  }

  // Get user by username
  async getUserByUsername(username: string): Promise<TwitterUser | null> {
    return this.mockUsers.find(user => user.username === username) || null;
  }

  // Get all users
  async getAllUsers(): Promise<TwitterUser[]> {
    return this.mockUsers;
  }

  // Authenticate user (mock implementation)
  async authenticateUser(credentials: any) {
    return {
      success: true,
      user: this.mockUsers[0],
      accessToken: 'mock_token_' + Date.now()
    };
  }
}
