import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsEnum, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum UserRole {
  USER = 'user',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

export enum Permission {
  // User permissions
  USER_READ = 'user:read',
  USER_WRITE = 'user:write',
  USER_DELETE = 'user:delete',
  USER_ADMIN = 'user:admin',

  // Profile permissions
  PROFILE_READ = 'profile:read',
  PROFILE_WRITE = 'profile:write',
  PROFILE_ADMIN = 'profile:admin',

  // Campaign permissions
  CAMPAIGN_READ = 'campaign:read',
  CAMPAIGN_WRITE = 'campaign:write',
  CAMPAIGN_DELETE = 'campaign:delete',
  CAMPAIGN_ADMIN = 'campaign:admin',

  // NFT permissions
  NFT_READ = 'nft:read',
  NFT_WRITE = 'nft:write',
  NFT_DELETE = 'nft:delete',
  NFT_ADMIN = 'nft:admin',

  // Marketplace permissions
  MARKETPLACE_READ = 'marketplace:read',
  MARKETPLACE_WRITE = 'marketplace:write',
  MARKETPLACE_DELETE = 'marketplace:delete',
  MARKETPLACE_ADMIN = 'marketplace:admin',

  // Analytics permissions
  ANALYTICS_READ = 'analytics:read',
  ANALYTICS_WRITE = 'analytics:write',
  ANALYTICS_ADMIN = 'analytics:admin',

  // System permissions
  SYSTEM_READ = 'system:read',
  SYSTEM_WRITE = 'system:write',
  SYSTEM_ADMIN = 'system:admin',

  // Audit permissions
  AUDIT_READ = 'audit:read',
  AUDIT_WRITE = 'audit:write',
  AUDIT_ADMIN = 'audit:admin',
}

// Define base permissions first
const USER_PERMISSIONS: Permission[] = [
  Permission.USER_READ,
  Permission.PROFILE_READ,
  Permission.PROFILE_WRITE,
  Permission.CAMPAIGN_READ,
  Permission.NFT_READ,
  Permission.NFT_WRITE,
  Permission.MARKETPLACE_READ,
  Permission.MARKETPLACE_WRITE,
  Permission.ANALYTICS_READ,
];

const MODERATOR_PERMISSIONS: Permission[] = [
  ...USER_PERMISSIONS,
  Permission.USER_WRITE,
  Permission.CAMPAIGN_WRITE,
  Permission.CAMPAIGN_DELETE,
  Permission.NFT_DELETE,
  Permission.MARKETPLACE_DELETE,
  Permission.ANALYTICS_WRITE,
  Permission.AUDIT_READ,
];

const ADMIN_PERMISSIONS: Permission[] = [
  ...MODERATOR_PERMISSIONS,
  Permission.USER_DELETE,
  Permission.USER_ADMIN,
  Permission.PROFILE_ADMIN,
  Permission.CAMPAIGN_ADMIN,
  Permission.NFT_ADMIN,
  Permission.MARKETPLACE_ADMIN,
  Permission.ANALYTICS_ADMIN,
  Permission.SYSTEM_READ,
  Permission.AUDIT_WRITE,
];

export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.USER]: USER_PERMISSIONS,
  [UserRole.MODERATOR]: MODERATOR_PERMISSIONS,
  [UserRole.ADMIN]: ADMIN_PERMISSIONS,
  [UserRole.SUPER_ADMIN]: Object.values(Permission),
};

export class CreateRoleDto {
  @ApiProperty({
    description: 'Role name',
    example: 'content_moderator',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Role display name',
    example: 'Content Moderator',
  })
  @IsString()
  displayName: string;

  @ApiPropertyOptional({
    description: 'Role description',
    example: 'Can moderate user-generated content',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Permissions assigned to this role',
    enum: Permission,
    isArray: true,
  })
  @IsArray()
  @IsEnum(Permission, { each: true })
  permissions: Permission[];

  @ApiPropertyOptional({
    description: 'Whether this role is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;
}

export class UpdateRoleDto {
  @ApiPropertyOptional({
    description: 'Role display name',
    example: 'Senior Content Moderator',
  })
  @IsOptional()
  @IsString()
  displayName?: string;

  @ApiPropertyOptional({
    description: 'Role description',
    example: 'Can moderate content and manage junior moderators',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Permissions assigned to this role',
    enum: Permission,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(Permission, { each: true })
  permissions?: Permission[];

  @ApiPropertyOptional({
    description: 'Whether this role is active',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class AssignRoleDto {
  @ApiProperty({
    description: 'User ID to assign role to',
    example: 'user_123',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Role to assign',
    enum: UserRole,
    example: UserRole.MODERATOR,
  })
  @IsEnum(UserRole)
  role: UserRole;

  @ApiPropertyOptional({
    description: 'Reason for role assignment',
    example: 'Promoted to moderator for excellent community contributions',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

export class RevokeRoleDto {
  @ApiProperty({
    description: 'User ID to revoke role from',
    example: 'user_123',
  })
  @IsString()
  userId: string;

  @ApiPropertyOptional({
    description: 'Reason for role revocation',
    example: 'Role no longer needed',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

export class PermissionCheckDto {
  @ApiProperty({
    description: 'Permission to check',
    enum: Permission,
    example: Permission.CAMPAIGN_WRITE,
  })
  @IsEnum(Permission)
  permission: Permission;

  @ApiPropertyOptional({
    description: 'Resource ID for resource-specific permissions',
    example: 'campaign_123',
  })
  @IsOptional()
  @IsString()
  resourceId?: string;
}

export class RoleResponseDto {
  @ApiProperty({
    description: 'Role ID',
    example: 'role_123',
  })
  id: string;

  @ApiProperty({
    description: 'Role name',
    example: 'content_moderator',
  })
  name: string;

  @ApiProperty({
    description: 'Role display name',
    example: 'Content Moderator',
  })
  displayName: string;

  @ApiPropertyOptional({
    description: 'Role description',
    example: 'Can moderate user-generated content',
  })
  description?: string;

  @ApiProperty({
    description: 'Permissions assigned to this role',
    enum: Permission,
    isArray: true,
  })
  permissions: Permission[];

  @ApiProperty({
    description: 'Whether this role is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Role creation timestamp',
    example: '2025-06-03T20:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Role last update timestamp',
    example: '2025-06-03T20:00:00Z',
  })
  updatedAt: string;
}

export class UserRoleResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: 'user_123',
  })
  userId: string;

  @ApiProperty({
    description: 'User role',
    enum: UserRole,
    example: UserRole.MODERATOR,
  })
  role: UserRole;

  @ApiProperty({
    description: 'User permissions',
    enum: Permission,
    isArray: true,
  })
  permissions: Permission[];

  @ApiProperty({
    description: 'Role assignment timestamp',
    example: '2025-06-03T20:00:00Z',
  })
  assignedAt: string;

  @ApiPropertyOptional({
    description: 'Reason for role assignment',
    example: 'Promoted to moderator for excellent community contributions',
  })
  reason?: string;
}
