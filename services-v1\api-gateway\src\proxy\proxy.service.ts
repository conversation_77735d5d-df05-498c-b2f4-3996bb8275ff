import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom, timeout, catchError } from 'rxjs';
import { of } from 'rxjs';

export interface ProxyRequest {
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  query?: Record<string, string>;
}

export interface ProxyResponse {
  success: boolean;
  data?: any;
  status: number;
  headers?: Record<string, string>;
  error?: string;
}

@Injectable()
export class ProxyService {
  private readonly logger = new Logger(ProxyService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  /**
   * Proxy request to User Service
   */
  async proxyToUserService(request: ProxyRequest): Promise<ProxyResponse> {
    const serviceUrl = this.configService.get<string>('USER_SERVICE_URL', 'http://localhost:3001');
    return this.proxyRequest(serviceUrl, request, 'user-service');
  }

  /**
   * Proxy request to Project Service
   */
  async proxyToProjectService(request: ProxyRequest): Promise<ProxyResponse> {
    const serviceUrl = this.configService.get<string>('PROJECT_SERVICE_URL', 'http://localhost:3002');
    return this.proxyRequest(serviceUrl, request, 'project-service');
  }

  /**
   * Proxy request to NFT Generator Service
   */
  async proxyToNFTService(request: ProxyRequest): Promise<ProxyResponse> {
    const serviceUrl = this.configService.get<string>('NFT_GENERATOR_SERVICE_URL', 'http://localhost:3003');
    return this.proxyRequest(serviceUrl, request, 'nft-generator-service');
  }

  /**
   * Proxy request to Blockchain Service
   */
  async proxyToBlockchainService(request: ProxyRequest): Promise<ProxyResponse> {
    const serviceUrl = this.configService.get<string>('BLOCKCHAIN_SERVICE_URL', 'http://localhost:3004');
    return this.proxyRequest(serviceUrl, request, 'blockchain-service');
  }

  /**
   * Proxy request to Marketplace Service
   */
  async proxyToMarketplaceService(request: ProxyRequest): Promise<ProxyResponse> {
    const serviceUrl = this.configService.get<string>('MARKETPLACE_SERVICE_URL', 'http://localhost:3005');
    return this.proxyRequest(serviceUrl, request, 'marketplace-service');
  }

  /**
   * Proxy request to Analytics Service
   */
  async proxyToAnalyticsService(request: ProxyRequest): Promise<ProxyResponse> {
    const serviceUrl = this.configService.get<string>('ANALYTICS_SERVICE_URL', 'http://localhost:3006');
    return this.proxyRequest(serviceUrl, request, 'analytics-service');
  }

  /**
   * Proxy request to Profile Analysis Service
   */
  async proxyToProfileService(request: ProxyRequest): Promise<ProxyResponse> {
    const serviceUrl = this.configService.get<string>('PROFILE_ANALYSIS_SERVICE_URL', 'http://localhost:3002');
    return this.proxyRequest(serviceUrl, request, 'profile-analysis-service');
  }

  /**
   * Proxy request to Notification Service
   */
  async proxyToNotificationService(request: ProxyRequest): Promise<ProxyResponse> {
    const serviceUrl = this.configService.get<string>('NOTIFICATION_SERVICE_URL', 'http://localhost:3008');
    return this.proxyRequest(serviceUrl, request, 'notification-service');
  }

  /**
   * Generic proxy request method
   */
  private async proxyRequest(
    serviceUrl: string, 
    request: ProxyRequest, 
    serviceName: string
  ): Promise<ProxyResponse> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Proxying ${request.method} request to ${serviceName}: ${request.url}`);

      // Build full URL
      const fullUrl = `${serviceUrl}${request.url}`;

      // Prepare headers (exclude host and content-length)
      const headers = { ...request.headers };
      delete headers['host'];
      delete headers['content-length'];
      delete headers['connection'];

      // Make the request
      const response$ = this.httpService.request({
        method: request.method.toLowerCase() as any,
        url: fullUrl,
        headers,
        data: request.body,
        params: request.query,
        timeout: 30000, // 30 second timeout
        validateStatus: () => true, // Accept all status codes
      }).pipe(
        timeout(30000),
        catchError((error) => {
          this.logger.error(`Proxy request failed to ${serviceName}: ${error.message}`);
          return of({
            data: { error: 'Service unavailable', message: error.message },
            status: 503,
            statusText: 'Service Unavailable',
            headers: {},
            config: {},
          });
        })
      );

      const response = await firstValueFrom(response$);

      const duration = Date.now() - startTime;

      this.logger.log(`Proxy response from ${serviceName}: ${response.status} (${duration}ms)`);

      return {
        success: response.status >= 200 && response.status < 400,
        data: response.data,
        status: response.status,
        headers: response.headers as Record<string, string>,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.error(`Proxy error for ${serviceName}: ${error.message} (${duration}ms)`, error.stack);

      return {
        success: false,
        status: 503,
        error: `Service ${serviceName} is unavailable: ${error.message}`,
        data: {
          error: 'Service unavailable',
          message: error.message,
          service: serviceName,
        },
      };
    }
  }

  /**
   * Check if service is available
   */
  async checkServiceHealth(serviceUrl: string, serviceName: string): Promise<boolean> {
    try {
      const healthUrl = `${serviceUrl}/api/health/simple`;
      
      const response$ = this.httpService.get(healthUrl).pipe(
        timeout(5000),
        catchError(() => of({ status: 0 }))
      );

      const response = await firstValueFrom(response$);
      const isHealthy = response.status === 200;
      
      this.logger.debug(`Health check for ${serviceName}: ${isHealthy ? 'healthy' : 'unhealthy'}`);
      
      return isHealthy;
    } catch (error) {
      this.logger.debug(`Health check error for ${serviceName}: ${error.message}`);
      return false;
    }
  }
}
