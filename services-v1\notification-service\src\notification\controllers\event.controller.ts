import { <PERSON>, Post, Body, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { EventNotificationService } from '../services/event-notification.service';

@ApiTags('events')
@Controller('events')
export class EventController {
  private readonly logger = new Logger(EventController.name);

  constructor(private readonly eventNotificationService: EventNotificationService) {}

  @Post('process')
  @ApiOperation({ summary: 'Process notification event' })
  @ApiResponse({ status: 201, description: 'Event processed successfully' })
  async processEvent(@Body() eventData: {
    eventType: string;
    userId: string;
    data: Record<string, any>;
    source?: string;
    correlationId?: string;
  }) {
    try {
      this.logger.log('Processing notification event', { 
        eventType: eventData.eventType,
        userId: eventData.userId 
      });

      const result = await this.eventNotificationService.processEventFromAPI(
        eventData.eventType,
        eventData.userId,
        eventData.data,
        eventData.source,
        eventData.correlationId
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to process event: ${error.message}`);
      return {
        success: false,
        error: 'Failed to process event',
        details: error.message,
      };
    }
  }

  @Post('user-registered')
  @ApiOperation({ summary: 'Process user registration event' })
  @ApiResponse({ status: 201, description: 'User registration event processed successfully' })
  async processUserRegistered(@Body() eventData: {
    userId: string;
    userEmail: string;
    userName: string;
  }) {
    try {
      this.logger.log('Processing user registration event', { userId: eventData.userId });

      const result = await this.eventNotificationService.processEventFromAPI(
        'user_registered',
        eventData.userId,
        {
          userEmail: eventData.userEmail,
          userName: eventData.userName,
        },
        'user-service'
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to process user registration event: ${error.message}`);
      return {
        success: false,
        error: 'Failed to process user registration event',
        details: error.message,
      };
    }
  }

  @Post('nft-generated')
  @ApiOperation({ summary: 'Process NFT generation event' })
  @ApiResponse({ status: 201, description: 'NFT generation event processed successfully' })
  async processNFTGenerated(@Body() eventData: {
    userId: string;
    nftId: string;
    userEmail?: string;
    userName?: string;
  }) {
    try {
      this.logger.log('Processing NFT generation event', { 
        userId: eventData.userId,
        nftId: eventData.nftId 
      });

      const result = await this.eventNotificationService.processEventFromAPI(
        'nft_generated',
        eventData.userId,
        {
          nftId: eventData.nftId,
          userEmail: eventData.userEmail,
          userName: eventData.userName,
        },
        'nft-generation-service'
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to process NFT generation event: ${error.message}`);
      return {
        success: false,
        error: 'Failed to process NFT generation event',
        details: error.message,
      };
    }
  }

  @Post('bid-received')
  @ApiOperation({ summary: 'Process bid received event' })
  @ApiResponse({ status: 201, description: 'Bid received event processed successfully' })
  async processBidReceived(@Body() eventData: {
    userId: string;
    nftId: string;
    bidAmount: string;
    bidderName?: string;
    nftTitle?: string;
  }) {
    try {
      this.logger.log('Processing bid received event', { 
        userId: eventData.userId,
        nftId: eventData.nftId,
        bidAmount: eventData.bidAmount
      });

      const result = await this.eventNotificationService.processEventFromAPI(
        'bid_received',
        eventData.userId,
        {
          nftId: eventData.nftId,
          bidAmount: eventData.bidAmount,
          bidderName: eventData.bidderName,
          nftTitle: eventData.nftTitle,
        },
        'marketplace-service'
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to process bid received event: ${error.message}`);
      return {
        success: false,
        error: 'Failed to process bid received event',
        details: error.message,
      };
    }
  }

  @Post('analysis-complete')
  @ApiOperation({ summary: 'Process analysis completion event' })
  @ApiResponse({ status: 201, description: 'Analysis completion event processed successfully' })
  async processAnalysisComplete(@Body() eventData: {
    userId: string;
    analysisId: string;
    platform: string;
    score?: number;
  }) {
    try {
      this.logger.log('Processing analysis completion event', { 
        userId: eventData.userId,
        analysisId: eventData.analysisId,
        platform: eventData.platform
      });

      const result = await this.eventNotificationService.processEventFromAPI(
        'analysis_complete',
        eventData.userId,
        {
          analysisId: eventData.analysisId,
          platform: eventData.platform,
          score: eventData.score,
        },
        'profile-analysis-service'
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to process analysis completion event: ${error.message}`);
      return {
        success: false,
        error: 'Failed to process analysis completion event',
        details: error.message,
      };
    }
  }
}
