// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Listing {
  id          String   @id @default(cuid())
  nftId       String
  title       String
  description String?
  price       String
  currency    String   @default("ETH")
  seller      String
  status      String   @default("active")
  category    String?
  imageUrl    String?
  views       Int      @default(0)
  favorites   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  expiresAt   DateTime?

  // Relations
  transactions Transaction[]
  offers       Offer[]

  @@map("listings")
}

model Auction {
  id             String   @id @default(cuid())
  nftId          String
  title          String
  description    String?
  startingPrice  String
  reservePrice   String?
  currentBid     String
  currency       String   @default("ETH")
  seller         String
  highestBidder  String?
  status         String   @default("active")
  bidsCount      Int      @default(0)
  imageUrl       String?
  startTime      DateTime
  endTime        DateTime
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  bids         Bid[]
  transactions Transaction[]

  @@map("auctions")
}

model Bid {
  id        String   @id @default(cuid())
  auctionId String
  bidder    String
  amount    String
  currency  String   @default("ETH")
  status    String   @default("active")
  createdAt DateTime @default(now())

  // Relations
  auction   Auction @relation(fields: [auctionId], references: [id])

  @@map("bids")
}

model Offer {
  id        String   @id @default(cuid())
  nftId     String?
  listingId String?
  amount    String
  currency  String   @default("ETH")
  offeror   String
  status    String   @default("pending")
  createdAt DateTime @default(now())
  expiresAt DateTime?

  // Relations
  listing   Listing? @relation(fields: [listingId], references: [id])

  @@map("offers")
}

model Transaction {
  id               String   @id @default(cuid())
  type             String
  nftId            String
  nftTitle         String?
  buyer            String?
  seller           String?
  price            String
  currency         String   @default("ETH")
  marketplaceFee   String?
  totalAmount      String
  status           String   @default("pending")
  paymentMethod    String?
  blockchainTxHash String?
  confirmations    Int      @default(0)
  listingId        String?
  auctionId        String?
  createdAt        DateTime @default(now())
  completedAt      DateTime?

  // Relations
  listing          Listing? @relation(fields: [listingId], references: [id])
  auction          Auction? @relation(fields: [auctionId], references: [id])

  @@map("transactions")
}

model Collection {
  id           String   @id @default(cuid())
  name         String
  description  String?
  totalItems   Int      @default(0)
  listedItems  Int      @default(0)
  floorPrice   String?
  totalVolume  String   @default("0")
  owners       Int      @default(0)
  averagePrice String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("collections")
}

model MarketplaceStats {
  id                String   @id @default(cuid())
  date              DateTime @unique
  totalVolume       String   @default("0")
  totalTransactions Int      @default(0)
  activeListings    Int      @default(0)
  activeAuctions    Int      @default(0)
  averagePrice      String   @default("0")
  marketplaceFees   String   @default("0")
  createdAt         DateTime @default(now())

  @@map("marketplace_stats")
}
