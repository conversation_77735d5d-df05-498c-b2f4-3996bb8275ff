#!/bin/bash

# Implement API Response Format Standardization
# This script standardizes API response formats across all services

set -e

echo "🔄 Implementing API Response Format Standardization"
echo "=================================================="

# Services to standardize
SERVICES=(
    "api-gateway"
    "user-service"
    "profile-analysis-service"
    "nft-generation-service"
    "blockchain-service"
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Function to create standardized response module for service
create_service_response_module() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    echo "🔧 Creating standardized response module for $service_name..."
    
    # Check if service directory exists
    if [ ! -d "$service_dir" ]; then
        echo "❌ Service directory not found: $service_dir"
        return 1
    fi
    
    # Create responses directory structure
    mkdir -p "$service_dir/src/responses"
    
    # Create service-specific response module
    cat > "$service_dir/src/responses/response.module.ts" << 'EOF'
import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR, APP_FILTER } from '@nestjs/core';

// Shared response components
import { ResponseTransformInterceptor } from '../../../../shared/responses/interceptors/response-transform.interceptor';
import { GlobalExceptionFilter } from './filters/global-exception.filter';

// Service-specific response services
import { ResponseService } from './services/response.service';
import { PaginationService } from './services/pagination.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    // Services
    ResponseService,
    PaginationService,
    
    // Global interceptor for response transformation
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor,
    },
    
    // Global exception filter
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
  ],
  exports: [
    ResponseService,
    PaginationService,
  ],
})
export class ServiceResponseModule {}
EOF

    # Create response service implementation
    mkdir -p "$service_dir/src/responses/services"
    
    cat > "$service_dir/src/responses/services/response.service.ts" << 'EOF'
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ResponseBuilder } from '../../../../../shared/responses/utils/response-builder.util';
import {
  SuccessResponse,
  ErrorResponse,
  ErrorCode,
} from '../../../../../shared/responses/interfaces/base-response.interface';
import {
  PaginatedResponse,
  PaginationMeta,
} from '../../../../../shared/responses/interfaces/pagination.interface';

@Injectable()
export class ResponseService {
  private readonly logger = new Logger(ResponseService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Build success response
   */
  success<T>(data: T, message?: string): SuccessResponse<T> {
    return ResponseBuilder.success(data, {
      message,
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });
  }

  /**
   * Build created response
   */
  created<T>(data: T, message?: string): SuccessResponse<T> {
    return ResponseBuilder.created(data, {
      message,
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });
  }

  /**
   * Build updated response
   */
  updated<T>(data: T, message?: string): SuccessResponse<T> {
    return ResponseBuilder.updated(data, {
      message,
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });
  }

  /**
   * Build deleted response
   */
  deleted(message?: string): SuccessResponse<{ deleted: boolean }> {
    return ResponseBuilder.deleted({
      message,
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });
  }

  /**
   * Build paginated response
   */
  paginated<T>(
    data: T[],
    pagination: PaginationMeta,
    options?: {
      filters?: any[];
      sorting?: any[];
      search?: any;
      message?: string;
    }
  ): PaginatedResponse<T> {
    return ResponseBuilder.paginated(data, pagination, {
      ...options,
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });
  }

  /**
   * Build error response
   */
  error(
    message: string,
    code: ErrorCode = ErrorCode.INTERNAL_SERVER_ERROR,
    details?: any
  ): ErrorResponse {
    return ResponseBuilder.error(message, code, {
      details,
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });
  }

  /**
   * Build not found response
   */
  notFound(resource: string, id?: string): ErrorResponse {
    return ResponseBuilder.notFound(resource, id, {
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });
  }

  /**
   * Build validation error response
   */
  validationError(validationErrors: any[]): ErrorResponse {
    return ResponseBuilder.validationError(validationErrors, {
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });
  }

  /**
   * Build business rule violation response
   */
  businessRuleViolation(rule: string, details?: any): ErrorResponse {
    return ResponseBuilder.businessRuleViolation(rule, details, {
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });
  }
}
EOF

    # Create pagination service implementation
    cat > "$service_dir/src/responses/services/pagination.service.ts" << 'EOF'
import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ResponseBuilder } from '../../../../../shared/responses/utils/response-builder.util';
import {
  PaginationMeta,
  PaginationQuery,
  FilterOptions,
  SortingOptions,
  SearchOptions,
} from '../../../../../shared/responses/interfaces/pagination.interface';

@Injectable()
export class PaginationService {
  private readonly defaultLimit: number;
  private readonly maxLimit: number;

  constructor(private readonly configService: ConfigService) {
    this.defaultLimit = 20;
    this.maxLimit = 100;
  }

  /**
   * Parse and validate pagination query parameters
   */
  parsePaginationQuery(query: any): PaginationQuery {
    const page = Math.max(1, parseInt(query.page) || 1);
    const limit = Math.min(
      this.maxLimit,
      Math.max(1, parseInt(query.limit) || this.defaultLimit)
    );

    return {
      page,
      limit,
      offset: (page - 1) * limit,
    };
  }

  /**
   * Parse filter parameters
   */
  parseFilters(query: any): FilterOptions[] {
    const filters: FilterOptions[] = [];

    // Parse filter parameters in format: filter[field][operator]=value
    for (const [key, value] of Object.entries(query)) {
      const filterMatch = key.match(/^filter\[([^\]]+)\]\[([^\]]+)\]$/);
      if (filterMatch) {
        const [, field, operator] = filterMatch;
        filters.push({
          field,
          operator: operator as any,
          value,
        });
      }
    }

    return filters;
  }

  /**
   * Parse sorting parameters
   */
  parseSorting(query: any): SortingOptions[] {
    const sorting: SortingOptions[] = [];

    if (query.sort) {
      const sortFields = Array.isArray(query.sort) ? query.sort : [query.sort];
      
      for (const sortField of sortFields) {
        if (typeof sortField === 'string') {
          const direction = sortField.startsWith('-') ? 'desc' : 'asc';
          const field = sortField.replace(/^[-+]/, '');
          
          sorting.push({
            field,
            direction: direction as 'asc' | 'desc',
          });
        }
      }
    }

    return sorting;
  }

  /**
   * Parse search parameters
   */
  parseSearch(query: any): SearchOptions | undefined {
    if (!query.search) {
      return undefined;
    }

    return {
      query: query.search,
      fields: query.searchFields ? query.searchFields.split(',') : undefined,
      fuzzy: query.fuzzy === 'true',
      caseSensitive: query.caseSensitive === 'true',
    };
  }

  /**
   * Build pagination metadata
   */
  buildPaginationMeta(
    page: number,
    limit: number,
    totalCount: number
  ): PaginationMeta {
    return ResponseBuilder.buildPaginationMeta(page, limit, totalCount);
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(page: number, limit: number): void {
    if (page < 1) {
      throw new BadRequestException('Page must be greater than 0');
    }

    if (limit < 1) {
      throw new BadRequestException('Limit must be greater than 0');
    }

    if (limit > this.maxLimit) {
      throw new BadRequestException(`Limit cannot exceed ${this.maxLimit}`);
    }
  }

  /**
   * Parse complete query parameters
   */
  parseQuery(query: any) {
    const pagination = this.parsePaginationQuery(query);
    const filters = this.parseFilters(query);
    const sorting = this.parseSorting(query);
    const search = this.parseSearch(query);

    this.validatePaginationParams(pagination.page, pagination.limit);

    return {
      pagination,
      filters,
      sorting,
      search,
    };
  }
}
EOF

    # Create global exception filter
    mkdir -p "$service_dir/src/responses/filters"
    
    cat > "$service_dir/src/responses/filters/global-exception.filter.ts" << 'EOF'
import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ResponseBuilder } from '../../../../../shared/responses/utils/response-builder.util';
import { ErrorCode } from '../../../../../shared/responses/interfaces/base-response.interface';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  constructor(private readonly configService: ConfigService) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorCode = ErrorCode.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let details: any = undefined;
    let validation: any[] = undefined;

    // Handle different exception types
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else if (typeof exceptionResponse === 'object') {
        const responseObj = exceptionResponse as any;
        message = responseObj.message || responseObj.error || message;
        details = responseObj.details;
        validation = responseObj.validation;
      }

      // Map HTTP status to error codes
      errorCode = this.mapStatusToErrorCode(status);
    } else if (exception instanceof Error) {
      message = exception.message;
      details = {
        name: exception.name,
        stack: this.shouldIncludeStack() ? exception.stack : undefined,
      };
    }

    // Build standardized error response
    const errorResponse = ResponseBuilder.error(message, errorCode, {
      statusCode: status,
      details,
      validation,
      stack: this.shouldIncludeStack() ? (exception as Error).stack : undefined,
      correlationId: request.correlationId,
      service: this.configService.get<string>('SERVICE_NAME'),
      version: this.configService.get<string>('SERVICE_VERSION'),
    });

    // Log the error
    this.logger.error('Exception caught by global filter', {
      error: message,
      status,
      path: request.path,
      method: request.method,
      correlationId: request.correlationId,
      userId: request.user?.id,
      stack: this.shouldIncludeStack() ? (exception as Error).stack : undefined,
    });

    // Send response
    response.status(status).json(errorResponse);
  }

  /**
   * Map HTTP status codes to error codes
   */
  private mapStatusToErrorCode(status: number): ErrorCode {
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        return ErrorCode.BAD_REQUEST;
      case HttpStatus.UNAUTHORIZED:
        return ErrorCode.UNAUTHORIZED;
      case HttpStatus.FORBIDDEN:
        return ErrorCode.FORBIDDEN;
      case HttpStatus.NOT_FOUND:
        return ErrorCode.NOT_FOUND;
      case HttpStatus.CONFLICT:
        return ErrorCode.CONFLICT;
      case HttpStatus.UNPROCESSABLE_ENTITY:
        return ErrorCode.VALIDATION_ERROR;
      case HttpStatus.TOO_MANY_REQUESTS:
        return ErrorCode.RATE_LIMIT_EXCEEDED;
      case HttpStatus.SERVICE_UNAVAILABLE:
        return ErrorCode.SERVICE_UNAVAILABLE;
      default:
        return ErrorCode.INTERNAL_SERVER_ERROR;
    }
  }

  /**
   * Check if stack trace should be included
   */
  private shouldIncludeStack(): boolean {
    const env = this.configService.get<string>('NODE_ENV') || 'development';
    return env === 'development' || env === 'test';
  }
}
EOF

    # Create response module index
    cat > "$service_dir/src/responses/index.ts" << 'EOF'
export * from './response.module';
export * from './services/response.service';
export * from './services/pagination.service';
export * from './filters/global-exception.filter';
EOF

    echo "✅ Response module created for $service_name"
}

# Function to update app.module.ts to use standardized responses
update_app_module_responses() {
    local service_name="$1"
    local service_dir="services/$service_name"
    local app_module="$service_dir/src/app.module.ts"
    
    echo "🔧 Updating app.module.ts response configuration for $service_name..."
    
    # Create backup
    cp "$app_module" "$app_module.response_backup.$(date +%Y%m%d_%H%M%S)"
    
    # Update imports section to include response module
    sed -i '/import.*ServiceAuthModule/a import { ServiceResponseModule } from '\''./responses/response.module'\'';' "$app_module"
    
    # Update imports array to include ServiceResponseModule
    sed -i '/ServiceAuthModule,/a \    ServiceResponseModule,' "$app_module"
    
    echo "✅ app.module.ts updated for $service_name"
}

# Function to create example controller with standardized responses
create_example_controller() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    echo "🔧 Creating example controller with standardized responses for $service_name..."
    
    # Create examples directory
    mkdir -p "$service_dir/src/examples"
    
    cat > "$service_dir/src/examples/standardized-response.controller.ts" << 'EOF'
import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import {
  ApiStandardResponse,
  ApiPaginatedResponse,
  ApiCreateResponse,
  ApiUpdateResponse,
  ApiDeleteResponse,
  ApiStandardErrorResponses,
} from '../../../../shared/responses/decorators/api-response.decorator';
import { ResponseService } from '../responses/services/response.service';
import { PaginationService } from '../responses/services/pagination.service';
import { RequirePermissions } from '../../../../shared/auth/decorators/auth.decorator';
import { Permission } from '../../../../shared/auth/interfaces/permission.interface';

// Example DTOs
class ExampleDto {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

class CreateExampleDto {
  name: string;
  description: string;
}

class UpdateExampleDto {
  name?: string;
  description?: string;
}

@ApiTags('Examples - Standardized Responses')
@Controller('examples')
@ApiStandardErrorResponses()
export class StandardizedResponseController {
  constructor(
    private readonly responseService: ResponseService,
    private readonly paginationService: PaginationService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get paginated list of examples' })
  @ApiPaginatedResponse(ExampleDto, 'Examples retrieved successfully')
  @RequirePermissions(Permission.USER_READ)
  async findAll(@Query() query: any) {
    // Parse query parameters
    const { pagination, filters, sorting, search } = this.paginationService.parseQuery(query);

    // Mock data for demonstration
    const mockData: ExampleDto[] = [
      {
        id: '1',
        name: 'Example 1',
        description: 'First example',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Example 2',
        description: 'Second example',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    // Build pagination metadata
    const paginationMeta = this.paginationService.buildPaginationMeta(
      pagination.page,
      pagination.limit,
      mockData.length
    );

    // Return paginated response
    return this.responseService.paginated(mockData, paginationMeta, {
      filters,
      sorting,
      search,
      message: 'Examples retrieved successfully',
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get example by ID' })
  @ApiStandardResponse(ExampleDto, 'Example retrieved successfully')
  @RequirePermissions(Permission.USER_READ)
  async findOne(@Param('id') id: string) {
    // Mock data for demonstration
    const mockExample: ExampleDto = {
      id,
      name: 'Example',
      description: 'Example description',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return this.responseService.success(mockExample, 'Example retrieved successfully');
  }

  @Post()
  @ApiOperation({ summary: 'Create new example' })
  @ApiCreateResponse(ExampleDto, 'Example created successfully')
  @RequirePermissions(Permission.USER_WRITE)
  async create(@Body() createDto: CreateExampleDto) {
    // Mock creation for demonstration
    const mockExample: ExampleDto = {
      id: Date.now().toString(),
      name: createDto.name,
      description: createDto.description,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return this.responseService.created(mockExample, 'Example created successfully');
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update example' })
  @ApiUpdateResponse(ExampleDto, 'Example updated successfully')
  @RequirePermissions(Permission.USER_WRITE)
  async update(@Param('id') id: string, @Body() updateDto: UpdateExampleDto) {
    // Mock update for demonstration
    const mockExample: ExampleDto = {
      id,
      name: updateDto.name || 'Updated Example',
      description: updateDto.description || 'Updated description',
      createdAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
      updatedAt: new Date().toISOString(),
    };

    return this.responseService.updated(mockExample, 'Example updated successfully');
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete example' })
  @ApiDeleteResponse('Example deleted successfully')
  @RequirePermissions(Permission.USER_DELETE)
  async remove(@Param('id') id: string) {
    // Mock deletion for demonstration
    return this.responseService.deleted('Example deleted successfully');
  }
}
EOF

    echo "✅ Example controller created for $service_name"
}

# Main execution
echo "🚀 Starting API response format standardization..."

for service_name in "${SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service_name..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service_name" ]; then
        echo "❌ Service directory not found: services/$service_name"
        continue
    fi
    
    # Create response module
    create_service_response_module "$service_name"
    
    # Update app.module.ts
    update_app_module_responses "$service_name"
    
    # Create example controller
    create_example_controller "$service_name"
    
    echo "✅ $service_name response standardization complete"
done

echo ""
echo "🎉 API Response Format Standardization Completed!"
echo "================================================"
echo ""
echo "📋 Summary:"
echo "- Created standardized response modules for ${#SERVICES[@]} services"
echo "- Implemented response transformation interceptors"
echo "- Added global exception filters for consistent error handling"
echo "- Created pagination and response services"
echo "- Added example controllers demonstrating standardized responses"
echo ""
echo "📁 Files Created:"
echo "services/[service]/src/responses/response.module.ts           # Response module"
echo "services/[service]/src/responses/services/response.service.ts # Response service"
echo "services/[service]/src/responses/services/pagination.service.ts # Pagination service"
echo "services/[service]/src/responses/filters/global-exception.filter.ts # Exception filter"
echo "services/[service]/src/examples/standardized-response.controller.ts # Example controller"
echo ""
echo "🔍 Next Steps:"
echo "1. Test standardized response endpoints"
echo "2. Update existing controllers to use ResponseService"
echo "3. Verify API documentation generation"
echo "4. Test error handling and pagination"
echo "5. Update frontend integration to use standardized responses"
echo ""
echo "⚠️  Important Notes:"
echo "• All API responses now follow standardized format"
echo "• Global exception filter provides consistent error responses"
echo "• Pagination is standardized across all list endpoints"
echo "• Response transformation is automatic via interceptors"
