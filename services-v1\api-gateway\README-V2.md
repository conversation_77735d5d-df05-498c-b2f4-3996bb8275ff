# 🚀 **Enterprise API Gateway V2**

## **Overview**

The Enhanced API Gateway V2 is an enterprise-grade service mesh entry point that provides comprehensive microservices management with advanced features including service discovery, circuit breaker protection, load balancing, caching, and rate limiting.

## **🎯 Enterprise Features**

### **✅ Service Discovery**
- **Dynamic Service Registration**: Automatic service registration and health monitoring
- **Health-Based Routing**: Routes traffic only to healthy service instances
- **Multi-Region Support**: Service discovery across different regions
- **Capability-Based Routing**: Route based on service capabilities

### **✅ Circuit Breaker Pattern**
- **Fault Tolerance**: Prevents cascading failures with intelligent failure detection
- **Bulkhead Isolation**: Limits concurrent requests per service
- **State Management**: Closed, Open, and Half-Open states with automatic recovery
- **Configurable Thresholds**: Customizable failure thresholds and reset timeouts

### **✅ Load Balancing**
- **Multiple Strategies**: Round-robin, weighted, least-connections, and random
- **Health-Aware Routing**: Only routes to healthy instances
- **Connection Tracking**: Monitors active connections per instance
- **Performance Metrics**: Comprehensive load balancing statistics

### **✅ Multi-Level Caching**
- **In-Memory Caching**: High-performance in-memory cache with TTL support
- **LRU Eviction**: Least Recently Used eviction policy
- **Compression Support**: Optional compression for large cache entries
- **Memory Management**: Configurable memory limits and automatic cleanup

### **✅ Advanced Rate Limiting**
- **Sliding Window Algorithm**: Accurate rate limiting with sliding windows
- **Multi-Key Support**: Per-user, per-service, and per-IP rate limiting
- **Configurable Limits**: Flexible rate limit configuration
- **Comprehensive Monitoring**: Detailed rate limiting statistics

### **✅ Comprehensive Monitoring**
- **Business Event Logging**: Structured logging with correlation IDs
- **Performance Metrics**: Prometheus-compatible metrics collection
- **Health Monitoring**: Real-time health status for all components
- **Dashboard Interface**: Web-based monitoring dashboard

## **🏗️ Architecture**

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway V2                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Proxy     │  │ Rate Limit  │  │    Cache    │         │
│  │   Service   │  │   Service   │  │   Service   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Service    │  │  Circuit    │  │    Load     │         │
│  │ Discovery   │  │  Breaker    │  │  Balancer   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│              Shared Infrastructure                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Logging   │  │   Metrics   │  │    Auth     │         │
│  │   Service   │  │   Service   │  │   Guards    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## **🚀 Quick Start**

### **1. Installation**

```bash
cd services-v1/api-gateway
npm install
```

### **2. Environment Configuration**

```bash
# Copy environment template
cp .env.example .env

# Configure service URLs
USER_SERVICE_URL=http://localhost:3001
PROFILE_SERVICE_URL=http://localhost:3002
NFT_SERVICE_URL=http://localhost:3003
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
MARKETPLACE_SERVICE_URL=http://localhost:3005

# Configure database
DATABASE_URL=postgresql://user:password@localhost:5432/api_gateway

# Configure JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h
```

### **3. Start the Enhanced API Gateway**

```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod
```

## **📊 Monitoring & Management**

### **Dashboard Endpoints**

- **Overview**: `GET /dashboard/overview` - Comprehensive system overview
- **Health**: `GET /dashboard/health` - System health status
- **Metrics**: `GET /dashboard/metrics` - Performance metrics
- **Alerts**: `GET /dashboard/alerts` - System alerts and warnings

### **Service Discovery**

- **Services**: `GET /discovery/services` - List all registered services
- **Healthy Instances**: `GET /discovery/services/{serviceName}/healthy`
- **Register Service**: `POST /discovery/services/register`
- **Health Check**: `GET /discovery/health`

### **Circuit Breaker**

- **Statistics**: `GET /circuit-breaker/stats` - All circuit breaker stats
- **Service Stats**: `GET /circuit-breaker/stats/{serviceKey}`
- **Reset Circuit**: `POST /circuit-breaker/reset/{serviceKey}`
- **Dashboard**: `GET /circuit-breaker/dashboard`

### **Load Balancer**

- **Connection Stats**: `GET /load-balancer/stats`
- **Instance Stats**: `GET /load-balancer/stats/{instanceId}`
- **Reset Stats**: `POST /load-balancer/reset`
- **Health**: `GET /load-balancer/health`

### **Cache Management**

- **Statistics**: `GET /cache/stats` - Cache performance statistics
- **Entries**: `GET /cache/entries` - List cache entries (admin only)
- **Clear Cache**: `POST /cache/clear` - Clear all cache entries
- **Health**: `GET /cache/health`

### **Rate Limiting**

- **Statistics**: `GET /rate-limit/stats` - Rate limiting statistics
- **All Limits**: `GET /rate-limit/limits` - List all rate limits
- **Reset Limit**: `POST /rate-limit/reset/{key}`
- **Clear All**: `POST /rate-limit/clear`

## **🔧 Configuration**

### **Service Discovery Configuration**

```typescript
const serviceConfig = {
  name: 'my-service',
  url: 'http://localhost:3001',
  metadata: {
    version: '1.0.0',
    capabilities: ['auth', 'users'],
    weight: 1,
  },
};
```

### **Circuit Breaker Configuration**

```typescript
const circuitConfig = {
  failureThreshold: 5,        // Open circuit after 5 failures
  resetTimeout: 60000,        // Try again after 1 minute
  bulkheadEnabled: true,      // Enable bulkhead isolation
  maxConcurrentRequests: 20,  // Max 20 concurrent requests
  successThreshold: 3,        // 3 successes to close from half-open
};
```

### **Rate Limiting Configuration**

```typescript
const rateLimitConfig = {
  windowMs: 60000,           // 1 minute window
  maxRequests: 100,          // 100 requests per window
  keyGenerator: (req) => req.user?.id || req.ip,
};
```

## **🔒 Security Features**

- **JWT Authentication**: Secure token-based authentication
- **RBAC Authorization**: Role-based access control
- **API Key Authentication**: Service-to-service authentication
- **Rate Limiting**: Protection against abuse and DDoS
- **Request Validation**: Input validation and sanitization

## **📈 Performance Features**

- **Connection Pooling**: Efficient connection management
- **Request Caching**: Intelligent response caching
- **Load Balancing**: Optimal traffic distribution
- **Circuit Breaker**: Fault tolerance and resilience
- **Metrics Collection**: Comprehensive performance monitoring

## **🔍 Observability**

### **Structured Logging**
- Correlation ID tracking across all requests
- Business event logging with context
- Performance metrics with timing information
- Error tracking with stack traces

### **Metrics Collection**
- Request/response metrics
- Circuit breaker statistics
- Cache hit/miss rates
- Rate limiting statistics
- Service health metrics

### **Health Monitoring**
- Service instance health checks
- Circuit breaker state monitoring
- Cache memory usage tracking
- Rate limiting status monitoring

## **🚀 Production Deployment**

### **Docker Deployment**

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "start:prod"]
```

### **Kubernetes Deployment**

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway-v2
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway-v2
  template:
    metadata:
      labels:
        app: api-gateway-v2
    spec:
      containers:
      - name: api-gateway-v2
        image: api-gateway-v2:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: api-gateway-secrets
              key: database-url
```

## **📚 API Documentation**

The API Gateway V2 provides comprehensive OpenAPI/Swagger documentation available at:
- **Development**: `http://localhost:3000/api/docs`
- **Production**: `https://your-domain.com/api/docs`

## **🤝 Contributing**

1. Follow the established coding standards
2. Write comprehensive tests for new features
3. Update documentation for any changes
4. Follow the git commit workflow strategy
5. Ensure all health checks pass before deployment

## **📞 Support**

For support and questions:
- Check the comprehensive dashboard at `/dashboard/overview`
- Review system alerts at `/dashboard/alerts`
- Monitor health status at `/dashboard/health`
- Check individual component health endpoints
