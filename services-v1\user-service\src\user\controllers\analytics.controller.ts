import { Controller, Get, Post, Body, Query, Req, UseGuards, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { AnalyticsService } from '../services/analytics.service';
import { 
  AnalyticsQueryDto,
  GenerateReportDto,
  PlatformAnalyticsDto,
  AnalyticsTimeframe,
  AnalyticsMetric,
  ReportType,
  ReportFormat 
} from '../dto/analytics.dto';
import { ErrorResponseDto } from '../../common/dto/error-response.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RBACGuard } from '../guards/rbac.guard';
import { 
  RequireAnalyticsRead,
  RequireAnalyticsWrite,
  RequireAnalyticsAdmin 
} from '../decorators/permissions.decorator';

@ApiTags('Analytics & Reporting')
@Controller('analytics')
@UseGuards(JwtAuthGuard, RBACGuard)
@ApiBearerAuth()
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('platform')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get comprehensive platform analytics' })
  @ApiQuery({ name: 'timeframe', enum: AnalyticsTimeframe, required: true, description: 'Analytics timeframe' })
  @ApiQuery({ name: 'startDate', type: String, required: false, description: 'Custom start date (ISO string)' })
  @ApiQuery({ name: 'endDate', type: String, required: false, description: 'Custom end date (ISO string)' })
  @ApiQuery({ name: 'metrics', enum: AnalyticsMetric, isArray: true, required: false, description: 'Specific metrics to include' })
  @ApiQuery({ name: 'userId', type: String, required: false, description: 'Filter by user ID' })
  @ApiQuery({ name: 'campaignId', type: String, required: false, description: 'Filter by campaign ID' })
  @ApiQuery({ name: 'groupBy', type: String, required: false, description: 'Group results by time interval', enum: ['hour', 'day', 'week', 'month'] })
  @ApiQuery({ name: 'includeComparison', type: Boolean, required: false, description: 'Include comparison with previous period' })
  @ApiResponse({ status: 200, description: 'Platform analytics retrieved successfully', type: PlatformAnalyticsDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getPlatformAnalytics(
    @Query('timeframe') timeframe: AnalyticsTimeframe,
    @Query('startDate') startDate: string | undefined,
    @Query('endDate') endDate: string | undefined,
    @Query('metrics') metrics: AnalyticsMetric | AnalyticsMetric[] | undefined,
    @Query('userId') userId: string | undefined,
    @Query('campaignId') campaignId: string | undefined,
    @Query('groupBy') groupBy: string | undefined,
    @Query('includeComparison') includeComparison: boolean | undefined,
    @Req() request: any
  ) {
    const query: AnalyticsQueryDto = {
      timeframe,
      startDate,
      endDate,
      metrics: Array.isArray(metrics) ? metrics : metrics ? [metrics] : undefined,
      userId,
      campaignId,
      groupBy,
      includeComparison,
    };

    const result = await this.analyticsService.getPlatformAnalytics(query, request.context);
    return result;
  }

  @Get('users')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get user analytics and metrics' })
  @ApiQuery({ name: 'timeframe', enum: AnalyticsTimeframe, required: true, description: 'Analytics timeframe' })
  @ApiQuery({ name: 'userId', type: String, required: false, description: 'Specific user ID' })
  @ApiQuery({ name: 'includeDetails', type: Boolean, required: false, description: 'Include detailed breakdowns' })
  @ApiResponse({ status: 200, description: 'User analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getUserAnalytics(
    @Query('timeframe') timeframe: AnalyticsTimeframe,
    @Query('userId') userId: string | undefined,
    @Query('includeDetails') includeDetails: boolean | undefined,
    @Req() request: any
  ) {
    const query: AnalyticsQueryDto = {
      timeframe,
      userId,
      metrics: [AnalyticsMetric.USERS],
    };

    const result = await this.analyticsService.getPlatformAnalytics(query, request.context);
    
    if (result.success && result.data) {
      return {
        success: true,
        data: {
          users: result.data.users,
          timeframe,
          generatedAt: result.data.generatedAt,
        },
      };
    }

    return result;
  }

  @Get('campaigns')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get campaign analytics and performance metrics' })
  @ApiQuery({ name: 'timeframe', enum: AnalyticsTimeframe, required: true, description: 'Analytics timeframe' })
  @ApiQuery({ name: 'campaignId', type: String, required: false, description: 'Specific campaign ID' })
  @ApiQuery({ name: 'includeDetails', type: Boolean, required: false, description: 'Include detailed breakdowns' })
  @ApiResponse({ status: 200, description: 'Campaign analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getCampaignAnalytics(
    @Query('timeframe') timeframe: AnalyticsTimeframe,
    @Query('campaignId') campaignId: string | undefined,
    @Query('includeDetails') includeDetails: boolean | undefined,
    @Req() request: any
  ) {
    const query: AnalyticsQueryDto = {
      timeframe,
      campaignId,
      metrics: [AnalyticsMetric.CAMPAIGNS],
    };

    const result = await this.analyticsService.getPlatformAnalytics(query, request.context);
    
    if (result.success && result.data) {
      return {
        success: true,
        data: {
          campaigns: result.data.campaigns,
          timeframe,
          generatedAt: result.data.generatedAt,
        },
      };
    }

    return result;
  }

  @Get('nfts')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get NFT analytics and market metrics' })
  @ApiQuery({ name: 'timeframe', enum: AnalyticsTimeframe, required: true, description: 'Analytics timeframe' })
  @ApiQuery({ name: 'includeDetails', type: Boolean, required: false, description: 'Include detailed breakdowns' })
  @ApiResponse({ status: 200, description: 'NFT analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getNFTAnalytics(
    @Query('timeframe') timeframe: AnalyticsTimeframe,
    @Query('includeDetails') includeDetails: boolean | undefined,
    @Req() request: any
  ) {
    const query: AnalyticsQueryDto = {
      timeframe,
      metrics: [AnalyticsMetric.NFTS],
    };

    const result = await this.analyticsService.getPlatformAnalytics(query, request.context);
    
    if (result.success && result.data) {
      return {
        success: true,
        data: {
          nfts: result.data.nfts,
          timeframe,
          generatedAt: result.data.generatedAt,
        },
      };
    }

    return result;
  }

  @Get('marketplace')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get marketplace analytics and trading metrics' })
  @ApiQuery({ name: 'timeframe', enum: AnalyticsTimeframe, required: true, description: 'Analytics timeframe' })
  @ApiQuery({ name: 'includeDetails', type: Boolean, required: false, description: 'Include detailed breakdowns' })
  @ApiResponse({ status: 200, description: 'Marketplace analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getMarketplaceAnalytics(
    @Query('timeframe') timeframe: AnalyticsTimeframe,
    @Query('includeDetails') includeDetails: boolean | undefined,
    @Req() request: any
  ) {
    const query: AnalyticsQueryDto = {
      timeframe,
      metrics: [AnalyticsMetric.MARKETPLACE],
    };

    const result = await this.analyticsService.getPlatformAnalytics(query, request.context);
    
    if (result.success && result.data) {
      return {
        success: true,
        data: {
          marketplace: result.data.marketplace,
          timeframe,
          generatedAt: result.data.generatedAt,
        },
      };
    }

    return result;
  }

  @Get('engagement')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get engagement analytics and user behavior metrics' })
  @ApiQuery({ name: 'timeframe', enum: AnalyticsTimeframe, required: true, description: 'Analytics timeframe' })
  @ApiQuery({ name: 'includeDetails', type: Boolean, required: false, description: 'Include detailed breakdowns' })
  @ApiResponse({ status: 200, description: 'Engagement analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getEngagementAnalytics(
    @Query('timeframe') timeframe: AnalyticsTimeframe,
    @Query('includeDetails') includeDetails: boolean | undefined,
    @Req() request: any
  ) {
    const query: AnalyticsQueryDto = {
      timeframe,
      metrics: [AnalyticsMetric.ENGAGEMENT],
    };

    const result = await this.analyticsService.getPlatformAnalytics(query, request.context);
    
    if (result.success && result.data) {
      return {
        success: true,
        data: {
          engagement: result.data.engagement,
          timeframe,
          generatedAt: result.data.generatedAt,
        },
      };
    }

    return result;
  }

  @Get('revenue')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get revenue analytics and financial metrics' })
  @ApiQuery({ name: 'timeframe', enum: AnalyticsTimeframe, required: true, description: 'Analytics timeframe' })
  @ApiQuery({ name: 'includeDetails', type: Boolean, required: false, description: 'Include detailed breakdowns' })
  @ApiResponse({ status: 200, description: 'Revenue analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getRevenueAnalytics(
    @Query('timeframe') timeframe: AnalyticsTimeframe,
    @Query('includeDetails') includeDetails: boolean | undefined,
    @Req() request: any
  ) {
    const query: AnalyticsQueryDto = {
      timeframe,
      metrics: [AnalyticsMetric.REVENUE],
    };

    const result = await this.analyticsService.getPlatformAnalytics(query, request.context);
    
    if (result.success && result.data) {
      return {
        success: true,
        data: {
          revenue: result.data.revenue,
          timeframe,
          generatedAt: result.data.generatedAt,
        },
      };
    }

    return result;
  }

  @Post('reports/generate')
  @RequireAnalyticsWrite()
  @ApiOperation({ summary: 'Generate analytics report' })
  @ApiBody({ type: GenerateReportDto })
  @ApiResponse({ status: 201, description: 'Report generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid report parameters', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async generateReport(@Body() generateReportDto: GenerateReportDto, @Req() request: any) {
    const result = await this.analyticsService.generateReport(generateReportDto, request.context);
    return result;
  }

  @Get('reports/types')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get available report types and formats' })
  @ApiResponse({ status: 200, description: 'Report types retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getReportTypes(@Req() request: any) {
    return {
      success: true,
      data: {
        reportTypes: Object.values(ReportType),
        formats: Object.values(ReportFormat),
        timeframes: Object.values(AnalyticsTimeframe),
        metrics: Object.values(AnalyticsMetric),
      },
    };
  }

  @Get('reports/:reportId/download')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Download generated report' })
  @ApiParam({ name: 'reportId', description: 'Report ID' })
  @ApiResponse({ status: 200, description: 'Report downloaded successfully' })
  @ApiResponse({ status: 404, description: 'Report not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async downloadReport(@Param('reportId') reportId: string, @Req() request: any) {
    // TODO: Implement report download functionality
    return {
      success: true,
      message: 'Report download not yet implemented',
      data: {
        reportId,
        downloadUrl: `/api/analytics/reports/download/${reportId}`,
      },
    };
  }

  @Get('dashboard')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get analytics dashboard data' })
  @ApiQuery({ name: 'timeframe', enum: AnalyticsTimeframe, required: false, description: 'Dashboard timeframe' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getDashboard(
    @Query('timeframe') timeframe: AnalyticsTimeframe = AnalyticsTimeframe.WEEK,
    @Req() request: any
  ) {
    const query: AnalyticsQueryDto = {
      timeframe,
      includeComparison: true,
    };

    const result = await this.analyticsService.getPlatformAnalytics(query, request.context);
    
    if (result.success && result.data) {
      return {
        success: true,
        data: {
          ...result.data,
          dashboardConfig: {
            refreshInterval: 300000, // 5 minutes
            autoRefresh: true,
            widgets: [
              'users',
              'campaigns',
              'nfts',
              'marketplace',
              'engagement',
              'revenue',
            ],
          },
        },
      };
    }

    return result;
  }

  @Get('trends')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get analytics trends and forecasts' })
  @ApiQuery({ name: 'metric', enum: AnalyticsMetric, required: true, description: 'Metric to analyze trends for' })
  @ApiQuery({ name: 'timeframe', enum: AnalyticsTimeframe, required: false, description: 'Trend analysis timeframe' })
  @ApiQuery({ name: 'forecast', type: Boolean, required: false, description: 'Include forecast data' })
  @ApiResponse({ status: 200, description: 'Trends retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getTrends(
    @Query('metric') metric: AnalyticsMetric,
    @Query('timeframe') timeframe: AnalyticsTimeframe = AnalyticsTimeframe.MONTH,
    @Query('forecast') forecast: boolean | undefined,
    @Req() request: any
  ) {
    // TODO: Implement trend analysis
    return {
      success: true,
      message: 'Trend analysis not yet implemented',
      data: {
        metric,
        timeframe,
        trends: [],
        forecast: forecast ? [] : undefined,
        generatedAt: new Date().toISOString(),
      },
    };
  }

  @Get('real-time')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get real-time analytics data' })
  @ApiResponse({ status: 200, description: 'Real-time analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getRealTimeAnalytics(@Req() request: any) {
    // TODO: Implement real-time analytics
    return {
      success: true,
      message: 'Real-time analytics not yet implemented',
      data: {
        activeUsers: 0,
        activeCampaigns: 0,
        recentTransactions: [],
        liveEngagement: 0,
        generatedAt: new Date().toISOString(),
      },
    };
  }
}
