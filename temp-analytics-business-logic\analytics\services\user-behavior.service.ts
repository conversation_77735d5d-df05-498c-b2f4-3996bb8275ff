import { Injectable, Logger } from '@nestjs/common';
import { EventTrackingService, AnalyticsEvent } from './event-tracking.service';

export interface UserBehaviorProfile {
  userId: string;
  totalSessions: number;
  totalEvents: number;
  averageSessionDuration: number; // minutes
  mostActiveHours: number[];
  topActions: Array<{ action: string; count: number }>;
  engagementScore: number; // 0-100
  userSegment: 'new' | 'casual' | 'active' | 'power' | 'churned';
  preferences: {
    preferredFeatures: string[];
    contentInterests: string[];
    deviceTypes: string[];
  };
  journey: {
    firstSeen: Date;
    lastSeen: Date;
    totalDays: number;
    conversionEvents: string[];
  };
}

export interface UserSegmentAnalysis {
  segment: string;
  userCount: number;
  characteristics: {
    avgSessionDuration: number;
    avgEventsPerSession: number;
    topActions: string[];
    retentionRate: number;
  };
}

export interface BehaviorInsights {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  userSegments: UserSegmentAnalysis[];
  topUserActions: Array<{ action: string; count: number; uniqueUsers: number }>;
  engagementTrends: Array<{ date: string; avgEngagement: number; activeUsers: number }>;
  conversionFunnel: Array<{ step: string; users: number; conversionRate: number }>;
}

@Injectable()
export class UserBehaviorService {
  private readonly logger = new Logger(UserBehaviorService.name);
  private userProfiles: Map<string, UserBehaviorProfile> = new Map();
  private userSessions: Map<string, Array<{ sessionId: string; startTime: Date; endTime?: Date; events: AnalyticsEvent[] }>> = new Map();

  constructor(private readonly eventTracking: EventTrackingService) {}

  /**
   * Analyze user behavior from events
   */
  async analyzeUserBehavior(userId: string): Promise<UserBehaviorProfile> {
    try {
      this.logger.log(`Analyzing behavior for user: ${userId}`);

      // Get user events
      const userEvents = this.eventTracking.getEventsByUser(userId);
      
      if (userEvents.length === 0) {
        return this.createEmptyProfile(userId);
      }

      // Calculate behavior metrics
      const profile = await this.calculateUserProfile(userId, userEvents);
      
      // Cache the profile
      this.userProfiles.set(userId, profile);

      this.logger.debug(`User behavior analysis completed for ${userId}`, {
        totalEvents: profile.totalEvents,
        engagementScore: profile.engagementScore,
        segment: profile.userSegment
      });

      return profile;
    } catch (error) {
      this.logger.error(`Failed to analyze user behavior: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get behavior insights for all users
   */
  async getBehaviorInsights(): Promise<BehaviorInsights> {
    try {
      this.logger.log('Generating behavior insights');

      const allMetrics = this.eventTracking.getEventMetrics();
      const userProfiles = Array.from(this.userProfiles.values());

      // Calculate user segments
      const userSegments = this.calculateUserSegments(userProfiles);

      // Calculate top actions
      const topUserActions = this.calculateTopUserActions();

      // Calculate engagement trends (mock data for now)
      const engagementTrends = this.calculateEngagementTrends();

      // Calculate conversion funnel
      const conversionFunnel = this.calculateConversionFunnel();

      const insights: BehaviorInsights = {
        totalUsers: allMetrics.uniqueUsers,
        activeUsers: userProfiles.filter(p => p.userSegment === 'active' || p.userSegment === 'power').length,
        newUsers: userProfiles.filter(p => p.userSegment === 'new').length,
        userSegments,
        topUserActions,
        engagementTrends,
        conversionFunnel,
      };

      this.logger.debug('Behavior insights generated', {
        totalUsers: insights.totalUsers,
        activeUsers: insights.activeUsers,
        segments: insights.userSegments.length
      });

      return insights;
    } catch (error) {
      this.logger.error(`Failed to generate behavior insights: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Track user session
   */
  async trackUserSession(userId: string, sessionId: string, action: 'start' | 'end'): Promise<void> {
    try {
      if (!this.userSessions.has(userId)) {
        this.userSessions.set(userId, []);
      }

      const sessions = this.userSessions.get(userId)!;

      if (action === 'start') {
        sessions.push({
          sessionId,
          startTime: new Date(),
          events: []
        });
        this.logger.debug(`Session started for user ${userId}: ${sessionId}`);
      } else if (action === 'end') {
        const session = sessions.find(s => s.sessionId === sessionId && !s.endTime);
        if (session) {
          session.endTime = new Date();
          this.logger.debug(`Session ended for user ${userId}: ${sessionId}`);
        }
      }

      // Track session event
      await this.eventTracking.trackEvent({
        eventType: `session_${action}`,
        userId,
        sessionId,
        properties: { action },
        metadata: { source: 'session_tracking' }
      });
    } catch (error) {
      this.logger.error(`Failed to track user session: ${error.message}`, error.stack);
    }
  }

  /**
   * Get user engagement score
   */
  calculateEngagementScore(userId: string): number {
    const profile = this.userProfiles.get(userId);
    if (!profile) return 0;

    return profile.engagementScore;
  }

  /**
   * Get user segment
   */
  getUserSegment(userId: string): string {
    const profile = this.userProfiles.get(userId);
    return profile?.userSegment || 'new';
  }

  /**
   * Get user behavior trends
   */
  getUserBehaviorTrends(userId: string, days: number = 30): Array<{ date: string; events: number; engagement: number }> {
    const userEvents = this.eventTracking.getEventsByUser(userId);
    const trends: Array<{ date: string; events: number; engagement: number }> = [];

    // Group events by date
    const eventsByDate = new Map<string, AnalyticsEvent[]>();
    
    userEvents.forEach(event => {
      const date = event.timestamp.toISOString().split('T')[0];
      if (!eventsByDate.has(date)) {
        eventsByDate.set(date, []);
      }
      eventsByDate.get(date)!.push(event);
    });

    // Calculate trends for each date
    eventsByDate.forEach((events, date) => {
      const engagement = this.calculateDailyEngagement(events);
      trends.push({
        date,
        events: events.length,
        engagement
      });
    });

    return trends.slice(-days);
  }

  /**
   * Calculate user profile from events
   */
  private async calculateUserProfile(userId: string, events: AnalyticsEvent[]): Promise<UserBehaviorProfile> {
    const sessions = this.getUserSessions(userId);
    const totalSessions = sessions.length;
    const totalEvents = events.length;

    // Calculate average session duration
    const avgSessionDuration = this.calculateAverageSessionDuration(sessions);

    // Calculate most active hours
    const mostActiveHours = this.calculateMostActiveHours(events);

    // Calculate top actions
    const topActions = this.calculateTopActions(events);

    // Calculate engagement score
    const engagementScore = this.calculateUserEngagementScore(events, sessions);

    // Determine user segment
    const userSegment = this.determineUserSegment(totalEvents, totalSessions, engagementScore, events);

    // Calculate preferences
    const preferences = this.calculateUserPreferences(events);

    // Calculate journey
    const journey = this.calculateUserJourney(events);

    return {
      userId,
      totalSessions,
      totalEvents,
      averageSessionDuration: avgSessionDuration,
      mostActiveHours,
      topActions,
      engagementScore,
      userSegment,
      preferences,
      journey,
    };
  }

  /**
   * Create empty profile for new users
   */
  private createEmptyProfile(userId: string): UserBehaviorProfile {
    return {
      userId,
      totalSessions: 0,
      totalEvents: 0,
      averageSessionDuration: 0,
      mostActiveHours: [],
      topActions: [],
      engagementScore: 0,
      userSegment: 'new',
      preferences: {
        preferredFeatures: [],
        contentInterests: [],
        deviceTypes: [],
      },
      journey: {
        firstSeen: new Date(),
        lastSeen: new Date(),
        totalDays: 0,
        conversionEvents: [],
      },
    };
  }

  /**
   * Get user sessions
   */
  private getUserSessions(userId: string) {
    return this.userSessions.get(userId) || [];
  }

  /**
   * Calculate average session duration
   */
  private calculateAverageSessionDuration(sessions: any[]): number {
    if (sessions.length === 0) return 0;

    const durations = sessions
      .filter(s => s.endTime)
      .map(s => (s.endTime.getTime() - s.startTime.getTime()) / (1000 * 60)); // minutes

    return durations.length > 0 ? durations.reduce((sum, d) => sum + d, 0) / durations.length : 0;
  }

  /**
   * Calculate most active hours
   */
  private calculateMostActiveHours(events: AnalyticsEvent[]): number[] {
    const hourCounts = new Map<number, number>();

    events.forEach(event => {
      const hour = event.timestamp.getHours();
      hourCounts.set(hour, (hourCounts.get(hour) || 0) + 1);
    });

    return Array.from(hourCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([hour]) => hour);
  }

  /**
   * Calculate top actions
   */
  private calculateTopActions(events: AnalyticsEvent[]): Array<{ action: string; count: number }> {
    const actionCounts = new Map<string, number>();

    events.forEach(event => {
      const action = event.properties.action || event.eventType;
      actionCounts.set(action, (actionCounts.get(action) || 0) + 1);
    });

    return Array.from(actionCounts.entries())
      .map(([action, count]) => ({ action, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Calculate user engagement score
   */
  private calculateUserEngagementScore(events: AnalyticsEvent[], sessions: any[]): number {
    if (events.length === 0) return 0;

    let score = 0;

    // Base score from event count
    score += Math.min(events.length * 2, 40);

    // Session quality bonus
    const avgSessionDuration = this.calculateAverageSessionDuration(sessions);
    score += Math.min(avgSessionDuration * 2, 20);

    // Diversity bonus (different event types)
    const uniqueEventTypes = new Set(events.map(e => e.eventType)).size;
    score += Math.min(uniqueEventTypes * 5, 25);

    // Recent activity bonus
    const recentEvents = events.filter(e => 
      Date.now() - e.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000 // 7 days
    );
    score += Math.min(recentEvents.length, 15);

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Determine user segment
   */
  private determineUserSegment(totalEvents: number, totalSessions: number, engagementScore: number, events: AnalyticsEvent[]): 'new' | 'casual' | 'active' | 'power' | 'churned' {
    const daysSinceLastEvent = events.length > 0 
      ? (Date.now() - Math.max(...events.map(e => e.timestamp.getTime()))) / (1000 * 60 * 60 * 24)
      : 0;

    if (daysSinceLastEvent > 30) return 'churned';
    if (totalEvents < 5) return 'new';
    if (engagementScore > 80 && totalEvents > 50) return 'power';
    if (engagementScore > 50 && totalEvents > 20) return 'active';
    return 'casual';
  }

  /**
   * Calculate user preferences
   */
  private calculateUserPreferences(events: AnalyticsEvent[]) {
    const features = new Map<string, number>();
    const interests = new Map<string, number>();
    const devices = new Map<string, number>();

    events.forEach(event => {
      // Extract features from event properties
      if (event.properties.feature) {
        features.set(event.properties.feature, (features.get(event.properties.feature) || 0) + 1);
      }

      // Extract interests from categories
      if (event.properties.category) {
        interests.set(event.properties.category, (interests.get(event.properties.category) || 0) + 1);
      }

      // Extract device types from metadata
      if (event.metadata.platform) {
        devices.set(event.metadata.platform, (devices.get(event.metadata.platform) || 0) + 1);
      }
    });

    return {
      preferredFeatures: Array.from(features.entries()).sort((a, b) => b[1] - a[1]).slice(0, 5).map(([feature]) => feature),
      contentInterests: Array.from(interests.entries()).sort((a, b) => b[1] - a[1]).slice(0, 5).map(([interest]) => interest),
      deviceTypes: Array.from(devices.entries()).sort((a, b) => b[1] - a[1]).slice(0, 3).map(([device]) => device),
    };
  }

  /**
   * Calculate user journey
   */
  private calculateUserJourney(events: AnalyticsEvent[]) {
    if (events.length === 0) {
      return {
        firstSeen: new Date(),
        lastSeen: new Date(),
        totalDays: 0,
        conversionEvents: [],
      };
    }

    const timestamps = events.map(e => e.timestamp.getTime());
    const firstSeen = new Date(Math.min(...timestamps));
    const lastSeen = new Date(Math.max(...timestamps));
    const totalDays = Math.ceil((lastSeen.getTime() - firstSeen.getTime()) / (1000 * 60 * 60 * 24));

    const conversionEvents = events
      .filter(e => ['nft_generated', 'nft_minted', 'analysis_completed'].includes(e.eventType))
      .map(e => e.eventType);

    return {
      firstSeen,
      lastSeen,
      totalDays,
      conversionEvents,
    };
  }

  /**
   * Calculate user segments
   */
  private calculateUserSegments(profiles: UserBehaviorProfile[]): UserSegmentAnalysis[] {
    const segments = new Map<string, UserBehaviorProfile[]>();

    profiles.forEach(profile => {
      if (!segments.has(profile.userSegment)) {
        segments.set(profile.userSegment, []);
      }
      segments.get(profile.userSegment)!.push(profile);
    });

    return Array.from(segments.entries()).map(([segment, users]) => ({
      segment,
      userCount: users.length,
      characteristics: {
        avgSessionDuration: users.reduce((sum, u) => sum + u.averageSessionDuration, 0) / users.length,
        avgEventsPerSession: users.reduce((sum, u) => sum + (u.totalSessions > 0 ? u.totalEvents / u.totalSessions : 0), 0) / users.length,
        topActions: this.getTopActionsForSegment(users),
        retentionRate: this.calculateRetentionRate(users),
      },
    }));
  }

  /**
   * Calculate top user actions
   */
  private calculateTopUserActions(): Array<{ action: string; count: number; uniqueUsers: number }> {
    // This would typically query the database
    // For now, return mock data
    return [
      { action: 'page_view', count: 1250, uniqueUsers: 85 },
      { action: 'nft_generated', count: 340, uniqueUsers: 45 },
      { action: 'analysis_started', count: 280, uniqueUsers: 52 },
      { action: 'nft_viewed', count: 220, uniqueUsers: 38 },
      { action: 'analysis_completed', count: 180, uniqueUsers: 35 },
    ];
  }

  /**
   * Calculate engagement trends
   */
  private calculateEngagementTrends(): Array<{ date: string; avgEngagement: number; activeUsers: number }> {
    // Mock data for demonstration
    const trends = [];
    for (let i = 7; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      trends.push({
        date: date.toISOString().split('T')[0],
        avgEngagement: 65 + Math.random() * 20,
        activeUsers: 25 + Math.floor(Math.random() * 15),
      });
    }
    return trends;
  }

  /**
   * Calculate conversion funnel
   */
  private calculateConversionFunnel(): Array<{ step: string; users: number; conversionRate: number }> {
    return [
      { step: 'Visited Platform', users: 100, conversionRate: 100 },
      { step: 'Started Analysis', users: 75, conversionRate: 75 },
      { step: 'Completed Analysis', users: 60, conversionRate: 60 },
      { step: 'Generated NFT', users: 45, conversionRate: 45 },
      { step: 'Minted NFT', users: 25, conversionRate: 25 },
    ];
  }

  /**
   * Calculate daily engagement
   */
  private calculateDailyEngagement(events: AnalyticsEvent[]): number {
    // Simple engagement calculation based on event types and frequency
    let engagement = 0;
    
    events.forEach(event => {
      switch (event.eventType) {
        case 'page_view':
          engagement += 1;
          break;
        case 'user_action':
          engagement += 3;
          break;
        case 'nft_generated':
          engagement += 10;
          break;
        case 'nft_minted':
          engagement += 15;
          break;
        default:
          engagement += 2;
      }
    });

    return Math.min(100, engagement);
  }

  /**
   * Get top actions for segment
   */
  private getTopActionsForSegment(users: UserBehaviorProfile[]): string[] {
    const actionCounts = new Map<string, number>();

    users.forEach(user => {
      user.topActions.forEach(action => {
        actionCounts.set(action.action, (actionCounts.get(action.action) || 0) + action.count);
      });
    });

    return Array.from(actionCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([action]) => action);
  }

  /**
   * Calculate retention rate
   */
  private calculateRetentionRate(users: UserBehaviorProfile[]): number {
    const activeUsers = users.filter(u => 
      Date.now() - u.journey.lastSeen.getTime() < 7 * 24 * 60 * 60 * 1000 // Active in last 7 days
    ).length;

    return users.length > 0 ? (activeUsers / users.length) * 100 : 0;
  }
}
