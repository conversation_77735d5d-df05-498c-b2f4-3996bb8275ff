import { Controller, Get, Post, Put, Delete, Body, Param, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { RBACService } from '../services/rbac.service';
import { 
  AssignRoleDto,
  RevokeRoleDto,
  PermissionCheckDto,
  UserRoleResponseDto,
  Permission 
} from '../dto/rbac.dto';
import { ErrorResponseDto } from '../../common/dto/error-response.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RBACGuard } from '../guards/rbac.guard';
import { 
  RequireUserAdmin,
  RequireAnyPermission,
  RequireSystemRead,
  RequireAuditRead 
} from '../decorators/permissions.decorator';

@ApiTags('RBAC - Role Based Access Control')
@Controller('rbac')
@UseGuards(JwtAuthGuard, RBACGuard)
@ApiBearerAuth()
export class RBACController {
  constructor(private readonly rbacService: RBACService) {}

  @Get('permissions')
  @RequireSystemRead()
  @ApiOperation({ summary: 'Get all available permissions' })
  @ApiResponse({ status: 200, description: 'Permissions retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getAllPermissions() {
    const permissions = Object.values(Permission).map(permission => ({
      name: permission,
      description: this.getPermissionDescription(permission),
    }));

    return {
      success: true,
      data: permissions,
    };
  }

  @Get('user/:userId/role')
  @RequireAnyPermission(Permission.USER_READ, Permission.USER_ADMIN)
  @ApiOperation({ summary: 'Get user role and permissions' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User role retrieved successfully', type: UserRoleResponseDto })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getUserRole(@Param('userId') userId: string) {
    const result = await this.rbacService.getUserRoleAndPermissions(userId);
    
    if (!result) {
      return { success: false, error: 'User not found or inactive' };
    }

    return { success: true, data: result };
  }

  @Get('user/:userId/permissions')
  @RequireAnyPermission(Permission.USER_READ, Permission.USER_ADMIN)
  @ApiOperation({ summary: 'Get user permissions' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User permissions retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getUserPermissions(@Param('userId') userId: string) {
    const result = await this.rbacService.getUserRoleAndPermissions(userId);
    
    if (!result) {
      return { success: false, error: 'User not found or inactive' };
    }

    return { 
      success: true, 
      data: {
        userId: result.userId,
        permissions: result.permissions,
      }
    };
  }

  @Post('user/:userId/check-permission')
  @RequireAnyPermission(Permission.USER_READ, Permission.USER_ADMIN)
  @ApiOperation({ summary: 'Check if user has specific permission' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiBody({ type: PermissionCheckDto })
  @ApiResponse({ status: 200, description: 'Permission check completed' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async checkUserPermission(
    @Param('userId') userId: string,
    @Body() permissionCheckDto: PermissionCheckDto
  ) {
    const hasPermission = await this.rbacService.hasPermission(
      userId,
      permissionCheckDto.permission,
      permissionCheckDto.resourceId
    );

    return {
      success: true,
      data: {
        userId,
        permission: permissionCheckDto.permission,
        resourceId: permissionCheckDto.resourceId,
        hasPermission,
      },
    };
  }

  @Post('assign-role')
  @RequireUserAdmin()
  @ApiOperation({ summary: 'Assign role to user' })
  @ApiBody({ type: AssignRoleDto })
  @ApiResponse({ status: 200, description: 'Role assigned successfully' })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async assignRole(@Body() assignRoleDto: AssignRoleDto, @Req() request: any) {
    const result = await this.rbacService.assignRole(assignRoleDto, request.context);
    return result;
  }

  @Post('revoke-role')
  @RequireUserAdmin()
  @ApiOperation({ summary: 'Revoke role from user (set to default USER role)' })
  @ApiBody({ type: RevokeRoleDto })
  @ApiResponse({ status: 200, description: 'Role revoked successfully' })
  @ApiResponse({ status: 404, description: 'User not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async revokeRole(@Body() revokeRoleDto: RevokeRoleDto, @Req() request: any) {
    const result = await this.rbacService.revokeRole(revokeRoleDto, request.context);
    return result;
  }

  @Get('my-permissions')
  @ApiOperation({ summary: 'Get current user permissions' })
  @ApiResponse({ status: 200, description: 'Current user permissions retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized', type: ErrorResponseDto })
  async getMyPermissions(@Req() request: any) {
    const userId = request.user.sub;
    const result = await this.rbacService.getUserRoleAndPermissions(userId);
    
    if (!result) {
      return { success: false, error: 'User not found or inactive' };
    }

    return { 
      success: true, 
      data: {
        userId: result.userId,
        role: result.role,
        permissions: result.permissions,
        assignedAt: result.assignedAt,
      }
    };
  }

  @Post('check-my-permission')
  @ApiOperation({ summary: 'Check if current user has specific permission' })
  @ApiBody({ type: PermissionCheckDto })
  @ApiResponse({ status: 200, description: 'Permission check completed' })
  @ApiResponse({ status: 401, description: 'Unauthorized', type: ErrorResponseDto })
  async checkMyPermission(
    @Body() permissionCheckDto: PermissionCheckDto,
    @Req() request: any
  ) {
    const userId = request.user.sub;
    const hasPermission = await this.rbacService.hasPermission(
      userId,
      permissionCheckDto.permission,
      permissionCheckDto.resourceId
    );

    return {
      success: true,
      data: {
        userId,
        permission: permissionCheckDto.permission,
        resourceId: permissionCheckDto.resourceId,
        hasPermission,
      },
    };
  }

  @Get('audit/role-changes')
  @RequireAuditRead()
  @ApiOperation({ summary: 'Get audit trail of role changes' })
  @ApiResponse({ status: 200, description: 'Role change audit trail retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getRoleChangeAudit() {
    // TODO: Implement audit trail retrieval for role changes
    return {
      success: true,
      data: [],
      message: 'Role change audit trail not yet implemented',
    };
  }

  /**
   * Get human-readable description for a permission
   */
  private getPermissionDescription(permission: Permission): string {
    const descriptions: Record<Permission, string> = {
      [Permission.USER_READ]: 'View user information',
      [Permission.USER_WRITE]: 'Create and update users',
      [Permission.USER_DELETE]: 'Delete users',
      [Permission.USER_ADMIN]: 'Full user management access',
      
      [Permission.PROFILE_READ]: 'View user profiles',
      [Permission.PROFILE_WRITE]: 'Update user profiles',
      [Permission.PROFILE_ADMIN]: 'Full profile management access',
      
      [Permission.CAMPAIGN_READ]: 'View campaigns',
      [Permission.CAMPAIGN_WRITE]: 'Create and update campaigns',
      [Permission.CAMPAIGN_DELETE]: 'Delete campaigns',
      [Permission.CAMPAIGN_ADMIN]: 'Full campaign management access',
      
      [Permission.NFT_READ]: 'View NFTs',
      [Permission.NFT_WRITE]: 'Create and update NFTs',
      [Permission.NFT_DELETE]: 'Delete NFTs',
      [Permission.NFT_ADMIN]: 'Full NFT management access',
      
      [Permission.MARKETPLACE_READ]: 'View marketplace listings',
      [Permission.MARKETPLACE_WRITE]: 'Create and update marketplace listings',
      [Permission.MARKETPLACE_DELETE]: 'Delete marketplace listings',
      [Permission.MARKETPLACE_ADMIN]: 'Full marketplace management access',
      
      [Permission.ANALYTICS_READ]: 'View analytics and reports',
      [Permission.ANALYTICS_WRITE]: 'Create and update analytics',
      [Permission.ANALYTICS_ADMIN]: 'Full analytics management access',
      
      [Permission.SYSTEM_READ]: 'View system information',
      [Permission.SYSTEM_WRITE]: 'Update system settings',
      [Permission.SYSTEM_ADMIN]: 'Full system administration access',
      
      [Permission.AUDIT_READ]: 'View audit logs',
      [Permission.AUDIT_WRITE]: 'Create audit entries',
      [Permission.AUDIT_ADMIN]: 'Full audit management access',
    };

    return descriptions[permission] || 'Unknown permission';
  }
}
