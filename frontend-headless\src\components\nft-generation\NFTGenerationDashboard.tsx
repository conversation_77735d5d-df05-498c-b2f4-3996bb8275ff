'use client'

import React, { useState } from 'react'
import {
  SparklesIcon,
  QueueListIcon,
  ChartBarIcon,
  CogIcon,
  PlusIcon,
  ArrowPathIcon,
  FunnelIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import { useGenerationRequests, useGenerationStats, useGenerationQueues } from '@/hooks/useNFTGeneration'
import { NFTGenerationStatus, GenerationProvider, GenerationFilters } from '@/types/nft-generation.types'
import GenerationRequestCard from './GenerationRequestCard'
import GenerationFilters from './GenerationFilters'
import GenerationStats from './GenerationStats'
import QueueManagement from './QueueManagement'

interface NFTGenerationDashboardProps {
  campaignId?: string
  onCreateRequest?: () => void
  onViewRequest?: (id: string) => void
  className?: string
}

export default function NFTGenerationDashboard({
  campaignId,
  onCreateRequest,
  onViewRequest,
  className = ''
}: NFTGenerationDashboardProps) {
  const [activeTab, setActiveTab] = useState<'requests' | 'queues' | 'analytics' | 'settings'>('requests')
  const [filters, setFilters] = useState<GenerationFilters>({
    campaignId,
    status: [],
    provider: [],
    sortBy: 'created_at',
    sortOrder: 'desc',
    page: 1,
    limit: 20
  })
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  const { data: requestsData, isLoading: requestsLoading, refetch: refetchRequests } = useGenerationRequests(filters)
  const { data: stats, isLoading: statsLoading } = useGenerationStats({ campaignId })
  const { data: queues, isLoading: queuesLoading } = useGenerationQueues()

  const requests = requestsData?.requests || []
  const totalRequests = requestsData?.total || 0

  const getStatusCounts = () => {
    return requests.reduce((counts, request) => {
      counts[request.status] = (counts[request.status] || 0) + 1
      return counts
    }, {} as Record<NFTGenerationStatus, number>)
  }

  const statusCounts = getStatusCounts()

  const getStatusColor = (status: NFTGenerationStatus) => {
    switch (status) {
      case NFTGenerationStatus.PENDING: return 'text-gray-600 bg-gray-100'
      case NFTGenerationStatus.QUEUED: return 'text-blue-600 bg-blue-100'
      case NFTGenerationStatus.GENERATING: return 'text-yellow-600 bg-yellow-100'
      case NFTGenerationStatus.GENERATED: return 'text-green-600 bg-green-100'
      case NFTGenerationStatus.REVIEWING: return 'text-purple-600 bg-purple-100'
      case NFTGenerationStatus.APPROVED: return 'text-green-600 bg-green-100'
      case NFTGenerationStatus.REJECTED: return 'text-red-600 bg-red-100'
      case NFTGenerationStatus.MINTING: return 'text-indigo-600 bg-indigo-100'
      case NFTGenerationStatus.MINTED: return 'text-green-600 bg-green-100'
      case NFTGenerationStatus.DISTRIBUTED: return 'text-blue-600 bg-blue-100'
      case NFTGenerationStatus.FAILED: return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const tabs = [
    { id: 'requests', name: 'Generation Requests', icon: SparklesIcon, count: totalRequests },
    { id: 'queues', name: 'Queue Management', icon: QueueListIcon, count: queues?.length },
    { id: 'analytics', name: 'Analytics', icon: ChartBarIcon },
    { id: 'settings', name: 'Settings', icon: CogIcon }
  ]

  if (requestsLoading && !requests.length) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <SparklesIcon className="h-8 w-8 mr-3 text-purple-600" />
              NFT Generation Pipeline
            </h1>
            <p className="text-gray-600 mt-1">
              Manage AI-powered NFT generation, review, and distribution
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => refetchRequests()}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              Refresh
            </button>
            
            {onCreateRequest && (
              <button
                onClick={onCreateRequest}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Generation
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      {!statsLoading && stats && (
        <GenerationStats stats={stats} />
      )}

      {/* Status Overview */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Generation Status Overview</h2>
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          {Object.values(NFTGenerationStatus).map((status) => (
            <div key={status} className="text-center">
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status)}`}>
                {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
              </div>
              <div className="text-2xl font-bold text-gray-900 mt-2">
                {statusCounts[status] || 0}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'requests' && (
            <div className="space-y-6">
              {/* Filters and Controls */}
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Generation Requests ({totalRequests})</h3>
                
                <div className="flex items-center space-x-3">
                  {/* View Mode Toggle */}
                  <div className="flex items-center border border-gray-300 rounded-md">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm font-medium ${
                        viewMode === 'grid'
                          ? 'bg-purple-600 text-white'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      Grid
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm font-medium ${
                        viewMode === 'list'
                          ? 'bg-purple-600 text-white'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      List
                    </button>
                  </div>

                  {/* Sort Options */}
                  <select
                    value={`${filters.sortBy}-${filters.sortOrder}`}
                    onChange={(e) => {
                      const [sortBy, sortOrder] = e.target.value.split('-')
                      setFilters(prev => ({ ...prev, sortBy, sortOrder: sortOrder as 'asc' | 'desc' }))
                    }}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="created_at-desc">Newest First</option>
                    <option value="created_at-asc">Oldest First</option>
                    <option value="priority-desc">Highest Priority</option>
                    <option value="quality_score-desc">Best Quality</option>
                    <option value="status-asc">Status</option>
                  </select>
                </div>
              </div>

              <GenerationFilters
                filters={filters}
                onFiltersChange={setFilters}
              />

              {/* Requests List/Grid */}
              {requests.length > 0 ? (
                <div className={viewMode === 'grid' 
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                  : 'space-y-4'
                }>
                  {requests.map((request) => (
                    <GenerationRequestCard
                      key={request.id}
                      request={request}
                      viewMode={viewMode}
                      onView={() => onViewRequest?.(request.id)}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No generation requests found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {Object.keys(filters).some(key => filters[key as keyof GenerationFilters] && 
                      (Array.isArray(filters[key as keyof GenerationFilters]) 
                        ? (filters[key as keyof GenerationFilters] as any[]).length > 0
                        : filters[key as keyof GenerationFilters]))
                      ? 'Try adjusting your filters to see more requests.'
                      : 'Get started by creating your first NFT generation request.'}
                  </p>
                  {onCreateRequest && (
                    <div className="mt-6">
                      <button
                        onClick={onCreateRequest}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
                      >
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Create Generation Request
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Pagination */}
              {totalRequests > filters.limit && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {((filters.page - 1) * filters.limit) + 1} to {Math.min(filters.page * filters.limit, totalRequests)} of {totalRequests} requests
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setFilters(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                      disabled={filters.page === 1}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <span className="px-3 py-2 text-sm text-gray-700">
                      Page {filters.page} of {Math.ceil(totalRequests / filters.limit)}
                    </span>
                    <button
                      onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                      disabled={filters.page >= Math.ceil(totalRequests / filters.limit)}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'queues' && (
            <QueueManagement />
          )}

          {activeTab === 'analytics' && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Generation Analytics</h3>
              <p className="text-gray-600">Detailed analytics and reporting will be implemented here.</p>
            </div>
          )}

          {activeTab === 'settings' && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Pipeline Settings</h3>
              <p className="text-gray-600">Configuration and settings will be implemented here.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
