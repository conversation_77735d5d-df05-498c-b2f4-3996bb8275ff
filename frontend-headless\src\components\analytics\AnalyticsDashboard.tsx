'use client'

import React, { useState } from 'react'
import {
  ChartBarIcon,
  PresentationChartLineIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  ClockIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  BellIcon,
  CogIcon
} from '@heroicons/react/24/outline'
import {
  useCampaignAnalytics,
  useRealTimeMetrics,
  useActivityFeed,
  useRefreshAnalyticsCache
} from '@/hooks/useAnalytics'
import { AnalyticsTimeframe } from '@/types/analytics.types'
import AnalyticsOverview from './AnalyticsOverview'
import EngagementAnalytics from './EngagementAnalytics'
import ConversionAnalytics from './ConversionAnalytics'
import RevenueAnalytics from './RevenueAnalytics'
import SocialAnalytics from './SocialAnalytics'
import GeographicAnalytics from './GeographicAnalytics'
import ReportBuilder from './ReportBuilder'
import AlertsManager from './AlertsManager'

interface AnalyticsDashboardProps {
  campaignId: string
  onExportData?: () => void
  onCreateReport?: () => void
  className?: string
}

export default function AnalyticsDashboard({
  campaignId,
  onExportData,
  onCreateReport,
  className = ''
}: AnalyticsDashboardProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'engagement' | 'conversion' | 'revenue' | 'social' | 'geographic' | 'reports' | 'alerts'>('overview')
  const [timeframe, setTimeframe] = useState<AnalyticsTimeframe>(AnalyticsTimeframe.MONTH)

  const { data: analytics, isLoading: analyticsLoading, refetch: refetchAnalytics } = useCampaignAnalytics(campaignId, timeframe)
  const { data: realTimeMetrics, isLoading: realTimeLoading } = useRealTimeMetrics(campaignId)
  const { data: activityFeed } = useActivityFeed(campaignId, 10)
  const refreshCacheMutation = useRefreshAnalyticsCache()

  const handleRefresh = async () => {
    try {
      await refreshCacheMutation.mutateAsync(campaignId)
      await refetchAnalytics()
    } catch (error) {
      console.error('Failed to refresh analytics:', error)
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'engagement', name: 'Engagement', icon: UserGroupIcon },
    { id: 'conversion', name: 'Conversion', icon: PresentationChartLineIcon },
    { id: 'revenue', name: 'Revenue & ROI', icon: CurrencyDollarIcon },
    { id: 'social', name: 'Social', icon: GlobeAltIcon },
    { id: 'geographic', name: 'Geographic', icon: GlobeAltIcon },
    { id: 'reports', name: 'Reports', icon: DocumentArrowDownIcon },
    { id: 'alerts', name: 'Alerts', icon: BellIcon }
  ]

  const timeframeOptions = [
    { value: AnalyticsTimeframe.DAY, label: 'Last 24 Hours' },
    { value: AnalyticsTimeframe.WEEK, label: 'Last 7 Days' },
    { value: AnalyticsTimeframe.MONTH, label: 'Last 30 Days' },
    { value: AnalyticsTimeframe.QUARTER, label: 'Last 90 Days' },
    { value: AnalyticsTimeframe.YEAR, label: 'Last Year' },
    { value: AnalyticsTimeframe.ALL_TIME, label: 'All Time' }
  ]

  if (analyticsLoading && !analytics) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <ChartBarIcon className="h-8 w-8 mr-3 text-blue-600" />
              Campaign Analytics
            </h1>
            <p className="text-gray-600 mt-1">
              Comprehensive insights and performance metrics
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Timeframe Selector */}
            <select
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value as AnalyticsTimeframe)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              {timeframeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* Refresh Button */}
            <button
              onClick={handleRefresh}
              disabled={refreshCacheMutation.isPending}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              <ArrowPathIcon className={`h-4 w-4 mr-2 ${refreshCacheMutation.isPending ? 'animate-spin' : ''}`} />
              {refreshCacheMutation.isPending ? 'Refreshing...' : 'Refresh'}
            </button>

            {/* Export Button */}
            {onExportData && (
              <button
                onClick={onExportData}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                Export
              </button>
            )}

            {/* Create Report Button */}
            {onCreateReport && (
              <button
                onClick={onCreateReport}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                Create Report
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Real-time Metrics Bar */}
      {realTimeMetrics && !realTimeLoading && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-sm font-medium text-gray-700 flex items-center">
              <ClockIcon className="h-4 w-4 mr-2" />
              Live Metrics
            </h2>
            <div className="text-xs text-gray-500">
              Updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{realTimeMetrics.activeParticipants}</div>
              <div className="text-xs text-gray-600">Active Now</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{realTimeMetrics.currentEngagements}</div>
              <div className="text-xs text-gray-600">Current Engagements</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{realTimeMetrics.recentConversions}</div>
              <div className="text-xs text-gray-600">Recent Conversions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {realTimeMetrics.liveActivity.reduce((sum, activity) => sum + activity.count, 0)}
              </div>
              <div className="text-xs text-gray-600">Live Activity</div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && analytics && (
            <AnalyticsOverview 
              analytics={analytics}
              realTimeMetrics={realTimeMetrics}
              activityFeed={activityFeed}
            />
          )}

          {activeTab === 'engagement' && (
            <EngagementAnalytics 
              campaignId={campaignId}
              timeframe={timeframe}
            />
          )}

          {activeTab === 'conversion' && (
            <ConversionAnalytics 
              campaignId={campaignId}
              timeframe={timeframe}
            />
          )}

          {activeTab === 'revenue' && (
            <RevenueAnalytics 
              campaignId={campaignId}
              timeframe={timeframe}
            />
          )}

          {activeTab === 'social' && (
            <SocialAnalytics 
              campaignId={campaignId}
              timeframe={timeframe}
            />
          )}

          {activeTab === 'geographic' && (
            <GeographicAnalytics 
              campaignId={campaignId}
            />
          )}

          {activeTab === 'reports' && (
            <ReportBuilder 
              campaignId={campaignId}
            />
          )}

          {activeTab === 'alerts' && (
            <AlertsManager 
              campaignId={campaignId}
            />
          )}
        </div>
      </div>

      {/* Recent Activity Feed */}
      {activityFeed && activityFeed.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {activityFeed.slice(0, 5).map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{activity.event}</div>
                    <div className="text-xs text-gray-600">by {activity.participant}</div>
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(activity.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
          
          {activityFeed.length > 5 && (
            <div className="mt-4 text-center">
              <button className="text-sm text-blue-600 hover:text-blue-700">
                View all activity
              </button>
            </div>
          )}
        </div>
      )}

      {/* Analytics Health Status */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Analytics System Status: Healthy</span>
          </div>
          <div className="text-xs text-gray-500">
            Data freshness: {analytics?.lastUpdated ? new Date(analytics.lastUpdated).toLocaleString() : 'Unknown'}
          </div>
        </div>
      </div>
    </div>
  )
}
