import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo() {
    return {
      name: 'Analytics Service',
      version: '1.0.0',
      description: 'Platform Analytics & Insights',
      status: 'running',
      timestamp: new Date().toISOString(),
      features: [
        'Event Tracking',
        'Performance Monitoring', 
        'User Behavior Analysis',
        'Real-time Analytics',
        'Custom Metrics',
        'Dashboard Data'
      ],
      endpoints: {
        health: '/api/health',
        docs: '/api/docs',
        analytics: '/api/analytics',
        events: '/api/events',
        performance: '/api/performance',
        behavior: '/api/behavior'
      }
    };
  }
}
