import { Injectable, Logger } from '@nestjs/common';

export interface NFTImageGenerationParams {
  rarity: string;
  score: number;
  twitterHandle: string;
  analysisData: {
    profile: {
      followerCount: number;
      followingCount: number;
      tweetCount: number;
      engagementRate: number;
      isVerified: boolean;
      hasProfileImage: boolean;
    };
    metrics: {
      contentQuality: number;
      activityLevel: number;
      influenceScore: number;
      authenticity: number;
      engagement: number;
    };
    breakdown: {
      followerScore: number;
      engagementScore: number;
      contentScore: number;
      activityScore: number;
      profileScore: number;
    };
  };
  customization?: {
    style?: 'modern' | 'classic' | 'cyberpunk' | 'minimalist';
    theme?: 'social' | 'tech' | 'art' | 'gaming';
    colorScheme?: 'auto' | 'blue' | 'purple' | 'gold' | 'red';
  };
}

export interface GeneratedNFTImage {
  imageBuffer: Buffer;
  filename: string;
  dimensions: { width: number; height: number };
  metadata: {
    rarity: string;
    style: string;
    theme: string;
    generatedAt: string;
  };
}

@Injectable()
export class NFTImageGeneratorService {
  private readonly logger = new Logger(NFTImageGeneratorService.name);

  // Canvas dimensions
  private readonly CANVAS_WIDTH = 800;
  private readonly CANVAS_HEIGHT = 800;

  /**
   * Generate NFT image based on analysis data and customization
   */
  async generateNFTImage(params: NFTImageGenerationParams): Promise<GeneratedNFTImage> {
    try {
      this.logger.log(`Generating NFT image for @${params.twitterHandle} with ${params.rarity} rarity`);

      // Get rarity configuration
      const rarityConfig = this.getRarityConfiguration(params.rarity);
      const style = params.customization?.style || 'modern';
      const theme = params.customization?.theme || 'social';

      // Generate SVG content
      const svgContent = this.generateSVGContent(params, rarityConfig, style, theme);

      // Convert SVG to PNG buffer (simplified approach)
      const imageBuffer = Buffer.from(svgContent, 'utf-8');

      const filename = `nft_${params.twitterHandle}_${params.rarity}_${Date.now()}.svg`;

      this.logger.log(`NFT image generated successfully: ${filename} (${imageBuffer.length} bytes)`);

      return {
        imageBuffer,
        filename,
        dimensions: { width: this.CANVAS_WIDTH, height: this.CANVAS_HEIGHT },
        metadata: {
          rarity: params.rarity,
          style,
          theme,
          generatedAt: new Date().toISOString(),
        },
      };

    } catch (error) {
      this.logger.error(`Failed to generate NFT image: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Generate SVG content for the NFT
   */
  private generateSVGContent(
    params: NFTImageGenerationParams,
    rarityConfig: any,
    style: string,
    theme: string
  ): string {
    const { rarity, score, twitterHandle, analysisData } = params;

    return `
      <svg width="${this.CANVAS_WIDTH}" height="${this.CANVAS_HEIGHT}" xmlns="http://www.w3.org/2000/svg">
        <!-- Background Gradient -->
        <defs>
          <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:${rarityConfig.primaryColor};stop-opacity:0.8" />
            <stop offset="70%" style="stop-color:${rarityConfig.secondaryColor};stop-opacity:0.4" />
            <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
          </radialGradient>

          <linearGradient id="scoreGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:${rarityConfig.primaryColor}" />
            <stop offset="100%" style="stop-color:${rarityConfig.accentColor}" />
          </linearGradient>
        </defs>

        <!-- Background -->
        <rect width="100%" height="100%" fill="url(#bgGradient)" />

        <!-- Geometric Patterns -->
        ${this.generateGeometricPatterns(analysisData.metrics, rarityConfig)}

        <!-- Score Circle -->
        ${this.generateScoreCircle(score, rarityConfig)}

        <!-- Rarity Badge -->
        ${this.generateRarityBadge(rarity, rarityConfig)}

        <!-- Twitter Handle -->
        ${this.generateTwitterHandle(twitterHandle, rarityConfig)}

        <!-- Metrics Bars -->
        ${this.generateMetricsBars(analysisData.metrics, rarityConfig)}

        <!-- Verification Badge -->
        ${analysisData.profile.isVerified ? this.generateVerificationBadge() : ''}

        <!-- Decorative Elements -->
        ${this.generateDecorativeElements(theme, rarityConfig)}
      </svg>
    `;
  }

  /**
   * Get rarity-specific configuration
   */
  private getRarityConfiguration(rarity: string) {
    const configs = {
      common: {
        primaryColor: '#6B7280',
        secondaryColor: '#9CA3AF',
        accentColor: '#D1D5DB',
        glowIntensity: 0.3,
        particleCount: 20,
        borderWidth: 4,
        gradientStops: ['#6B7280', '#4B5563'],
      },
      rare: {
        primaryColor: '#3B82F6',
        secondaryColor: '#60A5FA',
        accentColor: '#93C5FD',
        glowIntensity: 0.5,
        particleCount: 35,
        borderWidth: 6,
        gradientStops: ['#3B82F6', '#1D4ED8'],
      },
      epic: {
        primaryColor: '#8B5CF6',
        secondaryColor: '#A78BFA',
        accentColor: '#C4B5FD',
        glowIntensity: 0.7,
        particleCount: 50,
        borderWidth: 8,
        gradientStops: ['#8B5CF6', '#7C3AED'],
      },
      legendary: {
        primaryColor: '#F59E0B',
        secondaryColor: '#FBBF24',
        accentColor: '#FCD34D',
        glowIntensity: 0.9,
        particleCount: 75,
        borderWidth: 10,
        gradientStops: ['#F59E0B', '#D97706'],
      },
      mythic: {
        primaryColor: '#EF4444',
        secondaryColor: '#F87171',
        accentColor: '#FCA5A5',
        glowIntensity: 1.0,
        particleCount: 100,
        borderWidth: 12,
        gradientStops: ['#EF4444', '#DC2626', '#B91C1C'],
      },
    };

    return configs[rarity.toLowerCase()] || configs.common;
  }

  /**
   * Generate geometric patterns SVG
   */
  private generateGeometricPatterns(metrics: any, rarityConfig: any): string {
    const centerX = this.CANVAS_WIDTH / 2;
    const centerY = this.CANVAS_HEIGHT / 2;

    const metricsArray = [
      metrics.contentQuality,
      metrics.activityLevel,
      metrics.influenceScore,
      metrics.authenticity,
      metrics.engagement,
    ];

    let patterns = '';
    metricsArray.forEach((metric, index) => {
      const radius = 100 + (metric / 100) * 150 + index * 30;
      const opacity = (metric / 100) * 0.3;

      patterns += `
        <circle cx="${centerX}" cy="${centerY}" r="${radius}"
                fill="none" stroke="${rarityConfig.accentColor}"
                stroke-width="2" stroke-opacity="${opacity}" />
      `;
    });

    return patterns;
  }

  /**
   * Generate score circle SVG
   */
  private generateScoreCircle(score: number, rarityConfig: any): string {
    const centerX = this.CANVAS_WIDTH / 2;
    const centerY = this.CANVAS_HEIGHT / 2;
    const radius = 80;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (score / 100) * circumference;

    return `
      <!-- Score Background Circle -->
      <circle cx="${centerX}" cy="${centerY}" r="${radius}"
              fill="none" stroke="${rarityConfig.accentColor}"
              stroke-width="8" stroke-opacity="0.3" />

      <!-- Score Progress Circle -->
      <circle cx="${centerX}" cy="${centerY}" r="${radius}"
              fill="none" stroke="${rarityConfig.primaryColor}"
              stroke-width="8" stroke-linecap="round"
              stroke-dasharray="${strokeDasharray}"
              stroke-dashoffset="${strokeDashoffset}"
              transform="rotate(-90 ${centerX} ${centerY})" />

      <!-- Score Text -->
      <text x="${centerX}" y="${centerY}" text-anchor="middle"
            dominant-baseline="middle" fill="white"
            font-family="Arial, sans-serif" font-size="36" font-weight="bold">
        ${score}
      </text>

      <!-- Score Label -->
      <text x="${centerX}" y="${centerY + 25}" text-anchor="middle"
            dominant-baseline="middle" fill="white"
            font-family="Arial, sans-serif" font-size="14">
        SCORE
      </text>
    `;
  }

  /**
   * Generate rarity badge SVG
   */
  private generateRarityBadge(rarity: string, rarityConfig: any): string {
    const badgeY = 60;
    const badgeWidth = 200;
    const badgeHeight = 40;
    const badgeX = (this.CANVAS_WIDTH - badgeWidth) / 2;

    return `
      <!-- Rarity Badge Background -->
      <rect x="${badgeX}" y="${badgeY}" width="${badgeWidth}" height="${badgeHeight}"
            fill="${rarityConfig.primaryColor}" stroke="${rarityConfig.accentColor}"
            stroke-width="2" rx="5" />

      <!-- Rarity Text -->
      <text x="${this.CANVAS_WIDTH / 2}" y="${badgeY + badgeHeight / 2}"
            text-anchor="middle" dominant-baseline="middle"
            fill="white" font-family="Arial, sans-serif"
            font-size="18" font-weight="bold">
        ${rarity.toUpperCase()}
      </text>
    `;
  }

  /**
   * Generate Twitter handle SVG
   */
  private generateTwitterHandle(twitterHandle: string, rarityConfig: any): string {
    const handleY = this.CANVAS_HEIGHT - 80;

    return `
      <!-- Twitter Handle Background -->
      <rect x="0" y="${handleY - 20}" width="${this.CANVAS_WIDTH}" height="60"
            fill="rgba(0, 0, 0, 0.7)" />

      <!-- Twitter Handle Text -->
      <text x="${this.CANVAS_WIDTH / 2}" y="${handleY + 10}"
            text-anchor="middle" dominant-baseline="middle"
            fill="${rarityConfig.accentColor}" font-family="Arial, sans-serif"
            font-size="24" font-weight="bold">
        @${twitterHandle}
      </text>
    `;
  }

  /**
   * Generate metrics bars SVG
   */
  private generateMetricsBars(metrics: any, rarityConfig: any): string {
    const metricsArray = [
      { label: 'Content', value: metrics.contentQuality },
      { label: 'Activity', value: metrics.activityLevel },
      { label: 'Influence', value: metrics.influenceScore },
      { label: 'Authentic', value: metrics.authenticity },
      { label: 'Engage', value: metrics.engagement },
    ];

    const startX = 50;
    const startY = 200;
    const barWidth = 20;
    const barMaxHeight = 100;
    const spacing = 30;

    let barsHTML = '';
    metricsArray.forEach((metric, index) => {
      const x = startX + index * (barWidth + spacing);
      const barHeight = (metric.value / 100) * barMaxHeight;
      const y = startY + barMaxHeight - barHeight;

      barsHTML += `
        <!-- Metric Bar Background -->
        <rect x="${x}" y="${startY}" width="${barWidth}" height="${barMaxHeight}"
              fill="rgba(255, 255, 255, 0.1)" />

        <!-- Metric Bar Fill -->
        <rect x="${x}" y="${y}" width="${barWidth}" height="${barHeight}"
              fill="url(#scoreGradient)" />

        <!-- Metric Label -->
        <text x="${x + barWidth / 2}" y="${startY + barMaxHeight + 15}"
              text-anchor="middle" fill="white"
              font-family="Arial, sans-serif" font-size="10">
          ${metric.label}
        </text>

        <!-- Metric Value -->
        <text x="${x + barWidth / 2}" y="${startY + barMaxHeight + 30}"
              text-anchor="middle" fill="white"
              font-family="Arial, sans-serif" font-size="12" font-weight="bold">
          ${Math.round(metric.value)}
        </text>
      `;
    });

    return barsHTML;
  }

  /**
   * Generate verification badge SVG
   */
  private generateVerificationBadge(): string {
    const badgeSize = 40;
    const badgeX = this.CANVAS_WIDTH - badgeSize - 20;
    const badgeY = 20;

    return `
      <!-- Verification Badge Circle -->
      <circle cx="${badgeX + badgeSize / 2}" cy="${badgeY + badgeSize / 2}"
              r="${badgeSize / 2}" fill="#1DA1F2" />

      <!-- Checkmark -->
      <path d="M${badgeX + 12},${badgeY + 20} L${badgeX + 18},${badgeY + 26} L${badgeX + 28},${badgeY + 16}"
            stroke="white" stroke-width="3" stroke-linecap="round"
            stroke-linejoin="round" fill="none" />
    `;
  }

  /**
   * Generate decorative elements SVG
   */
  private generateDecorativeElements(theme: string, rarityConfig: any): string {
    if (theme === 'tech') {
      return this.generateTechElements(rarityConfig);
    } else if (theme === 'art') {
      return this.generateArtElements(rarityConfig);
    } else if (theme === 'gaming') {
      return this.generateGamingElements(rarityConfig);
    }
    return '';
  }

  /**
   * Generate tech-themed decorative elements
   */
  private generateTechElements(rarityConfig: any): string {
    const corners = [
      { x: 50, y: 50 },
      { x: this.CANVAS_WIDTH - 50, y: 50 },
      { x: 50, y: this.CANVAS_HEIGHT - 50 },
      { x: this.CANVAS_WIDTH - 50, y: this.CANVAS_HEIGHT - 50 },
    ];

    let techElements = '';
    corners.forEach(corner => {
      techElements += `
        <g stroke="${rarityConfig.primaryColor}" stroke-width="2" stroke-opacity="0.6">
          <line x1="${corner.x - 20}" y1="${corner.y}" x2="${corner.x + 20}" y2="${corner.y}" />
          <line x1="${corner.x}" y1="${corner.y - 20}" x2="${corner.x}" y2="${corner.y + 20}" />
        </g>
      `;
    });

    return techElements;
  }

  /**
   * Generate art-themed decorative elements
   */
  private generateArtElements(rarityConfig: any): string {
    return `
      <g stroke="${rarityConfig.accentColor}" stroke-width="3" stroke-opacity="0.4" fill="none">
        <path d="M100,100 Q200,50 300,100 T500,100" />
        <path d="M600,200 Q500,150 400,200 T200,200" />
        <path d="M150,600 Q250,550 350,600 T550,600" />
      </g>
    `;
  }

  /**
   * Generate gaming-themed decorative elements
   */
  private generateGamingElements(rarityConfig: any): string {
    let pixels = '';
    for (let i = 0; i < 20; i++) {
      const x = Math.floor(Math.random() * (this.CANVAS_WIDTH / 8)) * 8;
      const y = Math.floor(Math.random() * (this.CANVAS_HEIGHT / 8)) * 8;
      pixels += `
        <rect x="${x}" y="${y}" width="8" height="8"
              fill="${rarityConfig.primaryColor}" fill-opacity="0.4" />
      `;
    }
    return pixels;
  }
}
