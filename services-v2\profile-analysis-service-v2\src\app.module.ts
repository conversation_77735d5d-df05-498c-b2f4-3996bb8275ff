/**
 * Profile Analysis Service V2 - App Module
 * 
 * Clean implementation with Database Per Service Pattern
 * Connects to profile_analysis_service_v2 database only
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { HttpModule } from '@nestjs/axios';
import { TerminusModule } from '@nestjs/terminus';

// Core modules
import { PrismaModule } from './prisma/prisma.module';
import { AnalysisModule } from './analysis/analysis.module';
import { InsightsModule } from './insights/insights.module';
import { MetricsModule } from './metrics/metrics.module';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    // Configuration - Service-local
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env'],
    }),
    
    // JWT - Service-local configuration
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET || 'dev-jwt-secret-change-in-production',
      signOptions: { 
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      },
    }),

    // HTTP Client for external API calls
    HttpModule.register({
      timeout: parseInt(process.env.API_GATEWAY_TIMEOUT) || 30000,
      maxRedirects: 5,
    }),

    // Database - Service-local (profile_analysis_service_v2)
    PrismaModule,

    // Health Checks
    TerminusModule,
    HealthModule,
    
    // Business Logic Modules
    AnalysisModule,
    InsightsModule,
    MetricsModule,
  ],
})
export class AppModule {
  constructor() {
    console.log('🏗️ Profile Analysis Service V2 initialized');
    console.log('🗄️ Database: profile_analysis_service_v2 (Database Per Service Pattern)');
    console.log('🧠 AI-Powered Analysis Engine Ready');
    console.log('📊 Metrics and Insights Processing Enabled');
  }
}
