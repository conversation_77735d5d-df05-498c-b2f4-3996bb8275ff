# 🚨 Development Rules and Enforcement Guidelines

**Mandatory Standards for Social NFT Platform Development**

## 🎯 Overview

This document establishes **MANDATORY** rules that ALL developers and AI agents MUST follow when working on the Social NFT Platform. These rules are **NON-NEGOTIABLE** and violations will result in code rejection.

---

## 🚫 CRITICAL VIOLATIONS - IMMEDIATE REJECTION

### **❌ NEVER DO THESE - CODE WILL BE REJECTED**

#### **1. Environment & Configuration Violations**
```typescript
// ❌ FORBIDDEN - Direct process.env access
const port = process.env.PORT;
const dbUrl = process.env.DATABASE_URL;

// ❌ FORBIDDEN - Hardcoded values
const API_URL = 'https://api.example.com';
const MAX_RETRIES = 3;

// ❌ FORBIDDEN - Missing validation
@Injectable()
export class BadService {
  constructor() {
    this.apiKey = process.env.API_KEY; // No validation!
  }
}
```

#### **2. Authentication & Security Violations**
```typescript
// ❌ FORBIDDEN - No authentication
@Controller('users')
export class UsersController {
  @Get()
  async findAll() { } // Missing authentication!
}

// ❌ FORBIDDEN - Custom auth without approval
@Injectable()
export class CustomAuthService {
  validateToken(token: string) {
    // Custom implementation - FORBIDDEN!
  }
}

// ❌ FORBIDDEN - Missing authorization
@Get('admin')
async adminEndpoint() {
  // No permission check - FORBIDDEN!
}
```

#### **3. Response Format Violations**
```typescript
// ❌ FORBIDDEN - Raw response
@Get()
async getUsers() {
  return users; // Missing response wrapper!
}

// ❌ FORBIDDEN - Custom error format
throw new Error('Something went wrong'); // Use standardized errors!

// ❌ FORBIDDEN - Missing correlation ID
return { success: true, data: users }; // Missing correlation tracking!
```

#### **4. Logging Violations**
```typescript
// ❌ FORBIDDEN - Console logging
console.log('User created');
console.error('Error occurred');

// ❌ FORBIDDEN - Missing context
this.logger.log('Operation completed'); // No context!

// ❌ FORBIDDEN - Sensitive data in logs
this.logger.log('User password: ' + password); // Security violation!
```

#### **5. Data Access Violations**
```typescript
// ❌ FORBIDDEN - Direct Prisma access
@Injectable()
export class UserService {
  constructor(private prisma: PrismaService) {}
  
  async getUser(id: string) {
    return this.prisma.user.findUnique({ where: { id } }); // Use repository!
  }
}

// ❌ FORBIDDEN - No transaction for multi-operations
async transferFunds(from: string, to: string, amount: number) {
  await this.updateBalance(from, -amount);
  await this.updateBalance(to, amount); // Should be in transaction!
}
```

---

## ✅ MANDATORY PATTERNS - MUST USE

### **1. Environment & Configuration - REQUIRED**
```typescript
// ✅ REQUIRED - Configuration service usage
@Injectable()
export class UserService {
  constructor(private readonly configService: ConfigService) {}
  
  getApiUrl(): string {
    return this.configService.get<string>('API_URL')!;
  }
}

// ✅ REQUIRED - Configuration validation
export class AppConfig {
  @IsString()
  @IsNotEmpty()
  serviceName: string;
  
  @IsNumber()
  @Min(1000)
  @Max(65535)
  port: number;
}
```

### **2. Authentication & Authorization - REQUIRED**
```typescript
// ✅ REQUIRED - Authentication guards
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class UsersController {
  
  @Get()
  @RequirePermissions(Permission.USER_READ)
  @RequireRoles(Role.USER, Role.ADMIN)
  async findAll() {
    // Implementation
  }
}

// ✅ REQUIRED - Security event logging
this.serviceLogger.logSecurityEvent(
  SecurityEventType.AUTHENTICATION,
  SecuritySeverity.MEDIUM,
  SecurityOutcome.FAILURE,
  { ipAddress, reason: 'invalid_credentials' }
);
```

### **3. Response Format - REQUIRED**
```typescript
// ✅ REQUIRED - Response service usage
@Controller('users')
export class UsersController {
  constructor(private readonly responseService: ResponseService) {}
  
  @Get()
  async findAll(@Query() query: any) {
    const users = await this.userService.findAll(query);
    return this.responseService.paginated(
      users.data,
      users.pagination,
      { message: 'Users retrieved successfully' }
    );
  }
  
  @Post()
  async create(@Body() createUserDto: CreateUserDto) {
    const user = await this.userService.create(createUserDto);
    return this.responseService.created(user, 'User created successfully');
  }
}
```

### **4. Logging - REQUIRED**
```typescript
// ✅ REQUIRED - Structured logging
@Injectable()
export class UserService {
  constructor(private readonly serviceLogger: ServiceLoggerService) {}
  
  async createUser(createUserDto: CreateUserDto): Promise<User> {
    try {
      this.serviceLogger.logOperationStart('user_creation', {
        metadata: { email: createUserDto.email }
      });
      
      const user = await this.userRepository.create(createUserDto);
      
      this.serviceLogger.logBusinessEvent(
        'user',
        'registration',
        BusinessOutcome.SUCCESS,
        { userId: user.id, email: user.email }
      );
      
      return user;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        'user',
        'registration',
        BusinessOutcome.FAILURE,
        { error: error.message }
      );
      throw error;
    }
  }
}
```

### **5. Data Access - REQUIRED**
```typescript
// ✅ REQUIRED - Repository pattern
@Injectable()
export class UserRepository extends BaseRepository<User, CreateUserDto, UpdateUserDto> {
  constructor(
    prisma: StandardizedPrismaService,
    serviceLogger: ServiceLoggerService,
    metricsService: MetricsService,
    configService: ConfigService,
  ) {
    super(prisma, serviceLogger, metricsService, configService, 'User');
  }
  
  protected getModel() {
    return this.prisma.user;
  }
}

// ✅ REQUIRED - Transaction usage
async transferFunds(from: string, to: string, amount: number): Promise<void> {
  await this.userRepository.withTransaction(async (repo) => {
    await repo.updateBalance(from, -amount);
    await repo.updateBalance(to, amount);
  });
}
```

---

## 📋 CODE REVIEW ENFORCEMENT CHECKLIST

### **🔍 Pre-Merge Requirements - ALL MUST PASS**

#### **Phase 1: Environment Variables**
- [ ] ✅ No `process.env` direct access in business logic
- [ ] ✅ All configuration uses ConfigService
- [ ] ✅ Environment variables documented in `.env.example`
- [ ] ✅ Configuration validation implemented

#### **Phase 2: Application Configuration**
- [ ] ✅ Configuration classes with validation decorators
- [ ] ✅ Type-safe configuration access
- [ ] ✅ No hardcoded values in source code
- [ ] ✅ Configuration module properly imported

#### **Phase 3: Authentication & Authorization**
- [ ] ✅ JWT authentication guards applied
- [ ] ✅ RBAC permissions implemented
- [ ] ✅ Security events logged
- [ ] ✅ No custom auth without approval

#### **Phase 4: API Response Format**
- [ ] ✅ ResponseService used for all API responses
- [ ] ✅ Correlation IDs included
- [ ] ✅ Standardized error handling
- [ ] ✅ Proper HTTP status codes

#### **Phase 5: Logging & Monitoring**
- [ ] ✅ Structured logging implemented
- [ ] ✅ No console.log statements
- [ ] ✅ Business events logged
- [ ] ✅ Performance metrics recorded

#### **Phase 6: Data Layer**
- [ ] ✅ Repository pattern used
- [ ] ✅ No direct Prisma access
- [ ] ✅ Transactions for multi-operations
- [ ] ✅ Database health monitoring

---

## 🤖 AI AGENT ENFORCEMENT RULES

### **FOR ALL AI ASSISTANTS AND CODE GENERATION TOOLS**

#### **🚨 CRITICAL REQUIREMENTS**

1. **ALWAYS validate against standardization patterns before generating code**
2. **NEVER generate code that bypasses established patterns**
3. **ALWAYS include proper imports from shared modules**
4. **ALWAYS implement error handling and logging**
5. **ALWAYS use dependency injection**
6. **ALWAYS include TypeScript types**

#### **🔧 Code Generation Templates - MANDATORY**

When generating ANY code, use these templates:

##### **Service Template**
```typescript
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServiceLoggerService } from '../logging/services/service-logger.service';
import { BusinessOutcome } from '../../../shared/logging/interfaces/logger.interface';

@Injectable()
export class {{ServiceName}}Service {
  constructor(
    private readonly {{entity}}Repository: {{Entity}}Repository,
    private readonly serviceLogger: ServiceLoggerService,
    private readonly configService: ConfigService,
  ) {}

  async create(createDto: Create{{Entity}}Dto): Promise<{{Entity}}> {
    try {
      this.serviceLogger.logOperationStart('{{entity}}_creation');
      
      const result = await this.{{entity}}Repository.create(createDto);
      
      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'creation',
        BusinessOutcome.SUCCESS,
        { {{entity}}Id: result.id }
      );
      
      return result;
    } catch (error) {
      this.serviceLogger.logBusinessEvent(
        '{{entity}}',
        'creation',
        BusinessOutcome.FAILURE,
        { error: error.message }
      );
      throw error;
    }
  }
}
```

##### **Controller Template**
```typescript
import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../shared/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { PermissionsGuard } from '../../../shared/auth/guards/permissions.guard';
import { RequirePermissions } from '../../../shared/auth/decorators/auth.decorator';
import { Permission } from '../../../shared/auth/interfaces/permission.interface';
import { ResponseService } from '../responses/services/response.service';
import { PaginationService } from '../responses/services/pagination.service';

@Controller('{{entities}}')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@ApiTags('{{Entities}}')
export class {{Entity}}Controller {
  constructor(
    private readonly {{entity}}Service: {{Entity}}Service,
    private readonly responseService: ResponseService,
    private readonly paginationService: PaginationService,
  ) {}

  @Get()
  @RequirePermissions(Permission.{{ENTITY}}_READ)
  @ApiOperation({ summary: 'Get {{entities}} list' })
  async findAll(@Query() query: any) {
    const { pagination, filters, sorting } = this.paginationService.parseQuery(query);
    const result = await this.{{entity}}Service.findAll(pagination, filters, sorting);
    
    return this.responseService.paginated(
      result.data,
      result.pagination,
      { filters, sorting, message: '{{Entities}} retrieved successfully' }
    );
  }

  @Post()
  @RequirePermissions(Permission.{{ENTITY}}_WRITE)
  @ApiOperation({ summary: 'Create {{entity}}' })
  async create(@Body() createDto: Create{{Entity}}Dto) {
    const result = await this.{{entity}}Service.create(createDto);
    return this.responseService.created(result, '{{Entity}} created successfully');
  }
}
```

##### **Repository Template**
```typescript
import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../shared/data/repositories/base.repository';
import { StandardizedPrismaService } from '../../../shared/data/services/prisma.service';
import { ServiceLoggerService } from '../logging/services/service-logger.service';
import { MetricsService } from '../../../shared/logging/services/metrics.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class {{Entity}}Repository extends BaseRepository<{{Entity}}, Create{{Entity}}Dto, Update{{Entity}}Dto> {
  constructor(
    prisma: StandardizedPrismaService,
    serviceLogger: ServiceLoggerService,
    metricsService: MetricsService,
    configService: ConfigService,
  ) {
    super(prisma, serviceLogger, metricsService, configService, '{{Entity}}');
  }

  protected getModel() {
    return this.prisma.{{entity}};
  }

  protected toDto(entity: any): {{Entity}} {
    return {
      id: entity.id,
      // Map other properties
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  protected toCreateData(dto: Create{{Entity}}Dto): any {
    return {
      // Map DTO properties to entity data
    };
  }

  protected toUpdateData(dto: Update{{Entity}}Dto): any {
    return {
      // Map DTO properties to entity data
    };
  }
}
```

#### **🔍 AI Validation Checklist**

Before suggesting or generating ANY code, verify:

1. ✅ Uses ConfigService instead of process.env
2. ✅ Implements proper authentication guards
3. ✅ Uses ResponseService for API responses
4. ✅ Includes structured logging
5. ✅ Uses repository pattern for data access
6. ✅ Includes proper error handling
7. ✅ Follows naming conventions
8. ✅ Includes TypeScript types
9. ✅ Uses dependency injection
10. ✅ Includes API documentation

#### **🚫 AI Rejection Criteria**

Immediately reject and regenerate if code contains:

1. ❌ Direct `process.env` access
2. ❌ `console.log` statements
3. ❌ Raw response returns
4. ❌ Missing authentication guards
5. ❌ Direct Prisma access
6. ❌ Hardcoded values
7. ❌ Missing error handling
8. ❌ Missing correlation IDs
9. ❌ Custom auth implementations
10. ❌ Non-standard patterns

---

## 🔧 AUTOMATED ENFORCEMENT TOOLS

### **Pre-commit Hooks**
```bash
# Install pre-commit hooks
npm run install:hooks

# Hooks will automatically check:
# - ESLint rules for standardization
# - TypeScript compilation
# - Import validation
# - Pattern compliance
```

### **CI/CD Pipeline Checks**
```yaml
# .github/workflows/standardization-check.yml
name: Standardization Check
on: [push, pull_request]
jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - name: Check Environment Variables
        run: npm run validate:env
      - name: Check Configuration
        run: npm run validate:config
      - name: Check Authentication
        run: npm run validate:auth
      - name: Check Response Format
        run: npm run validate:responses
      - name: Check Logging
        run: npm run validate:logging
      - name: Check Data Layer
        run: npm run validate:data
```

### **ESLint Rules**
```json
{
  "rules": {
    "no-process-env": "error",
    "no-console": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "import/no-relative-parent-imports": "error"
  }
}
```

---

## 📊 COMPLIANCE MONITORING

### **Daily Compliance Reports**
- Automated scans for pattern violations
- Metrics on standardization compliance
- Alerts for critical violations
- Performance impact analysis

### **Weekly Reviews**
- Code review compliance rates
- Pattern adoption metrics
- Developer training needs
- Tool effectiveness analysis

### **Monthly Audits**
- Complete codebase standardization audit
- Security compliance verification
- Performance optimization opportunities
- Documentation updates

---

## 🎯 ENFORCEMENT CONSEQUENCES

### **Code Review Process**
1. **Automated Checks**: Pre-commit hooks prevent violations
2. **Peer Review**: Manual verification of patterns
3. **Lead Review**: Final approval for complex changes
4. **Rejection**: Non-compliant code is rejected immediately

### **Developer Training**
- Mandatory standardization training for new developers
- Regular refresher sessions for existing team
- AI agent configuration and validation training
- Best practices workshops

### **Escalation Process**
1. **First Violation**: Code rejection with explanation
2. **Repeated Violations**: Additional training required
3. **Persistent Issues**: Lead developer intervention
4. **Critical Violations**: Immediate escalation to management

---

## 📚 QUICK REFERENCE

### **Emergency Contacts**
- **Lead Developer**: For pattern clarifications
- **DevOps Team**: For CI/CD and tooling issues
- **Security Team**: For security-related violations

### **Documentation Links**
- [Complete Standardization Guide](./ENTERPRISE_STANDARDIZATION_GUIDE.md)
- [Code Examples](../examples/)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)

### **Tools and Scripts**
- `npm run validate:all` - Validate all patterns
- `npm run fix:patterns` - Auto-fix common violations
- `npm run generate:service` - Generate compliant service
- `npm run generate:controller` - Generate compliant controller

---

## 🚨 REMEMBER: THESE ARE NOT SUGGESTIONS

**These rules are MANDATORY and NON-NEGOTIABLE. All code MUST comply with these standards. Violations will result in immediate code rejection and may require additional training.**

**When in doubt, follow the patterns. When patterns are unclear, ask for clarification. Never implement custom solutions without approval.**
