import { Module } from '@nestjs/common';
import { setupAPIGateway } from '../../../../shared';
import { ProxyV2Module } from './proxy-v2/proxy-v2.module';
import { ServiceDiscoveryModule } from './service-discovery/service-discovery.module';
import { CircuitBreakerModule } from './circuit-breaker/circuit-breaker.module';
import { LoadBalancerModule } from './load-balancer/load-balancer.module';
import { CacheModule } from './cache/cache.module';
import { RateLimitModule } from './rate-limit/rate-limit.module';
import { DashboardModule } from './dashboard/dashboard.module';

/**
 * Enhanced API Gateway Module with Enterprise Features
 * 
 * Features:
 * - Shared infrastructure integration
 * - Service discovery with health checks
 * - Circuit breaker pattern
 * - Load balancing with health-based routing
 * - Multi-level caching
 * - Advanced rate limiting
 * - Comprehensive monitoring and metrics
 * 
 * @example
 * ```typescript
 * // This module provides enterprise-grade API Gateway functionality
 * // with automatic service discovery, circuit breaker protection,
 * // and comprehensive monitoring
 * ```
 */
@Module({
  imports: [
    // Shared infrastructure with API Gateway configuration
    setupAPIGateway('api-gateway', '2.0.0'),
    
    // Enhanced proxy with enterprise features
    ProxyV2Module,
    
    // Service discovery and health management
    ServiceDiscoveryModule,
    
    // Circuit breaker for fault tolerance
    CircuitBreakerModule,
    
    // Load balancing with health checks
    LoadBalancerModule,
    
    // Multi-level caching
    CacheModule,
    
    // Advanced rate limiting
    RateLimitModule,

    // Comprehensive dashboard
    DashboardModule,
  ],
})
export class AppV2Module {}
