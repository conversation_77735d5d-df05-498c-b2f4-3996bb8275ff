import { <PERSON>, Get, Post, Body, Query, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { PerformanceAnalyticsService } from '../services/performance-analytics.service';

@ApiTags('performance')
@Controller('performance')
export class PerformanceController {
  private readonly logger = new Logger(PerformanceController.name);

  constructor(private readonly performanceAnalytics: PerformanceAnalyticsService) {}

  @Get('system')
  @ApiOperation({ summary: 'Get current system performance' })
  @ApiResponse({ status: 200, description: 'System performance retrieved successfully' })
  async getSystemPerformance() {
    try {
      this.logger.log('Getting system performance');

      const performance = await this.performanceAnalytics.getSystemPerformance();

      return {
        success: true,
        data: performance,
      };
    } catch (error) {
      this.logger.error(`Failed to get system performance: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve system performance',
        details: error.message,
      };
    }
  }

  @Get('services')
  @ApiOperation({ summary: 'Get service metrics' })
  @ApiResponse({ status: 200, description: 'Service metrics retrieved successfully' })
  @ApiQuery({ name: 'serviceName', required: false, description: 'Specific service to get metrics for' })
  async getServiceMetrics(@Query('serviceName') serviceName?: string) {
    try {
      this.logger.log(`Getting service metrics${serviceName ? ` for ${serviceName}` : ''}`);

      const metrics = await this.performanceAnalytics.getServiceMetrics(serviceName);

      return {
        success: true,
        data: {
          services: metrics,
          count: metrics.length,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get service metrics: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve service metrics',
        details: error.message,
      };
    }
  }

  @Get('report')
  @ApiOperation({ summary: 'Generate performance report' })
  @ApiResponse({ status: 200, description: 'Performance report generated successfully' })
  async generatePerformanceReport() {
    try {
      this.logger.log('Generating performance report');

      const report = await this.performanceAnalytics.generatePerformanceReport();

      return {
        success: true,
        data: report,
      };
    } catch (error) {
      this.logger.error(`Failed to generate performance report: ${error.message}`);
      return {
        success: false,
        error: 'Failed to generate performance report',
        details: error.message,
      };
    }
  }

  @Get('trends')
  @ApiOperation({ summary: 'Get performance trends' })
  @ApiResponse({ status: 200, description: 'Performance trends retrieved successfully' })
  @ApiQuery({ name: 'hours', required: false, description: 'Number of hours to get trends for' })
  async getPerformanceTrends(@Query('hours') hours: string = '24') {
    try {
      const hoursNum = parseInt(hours);
      this.logger.log(`Getting performance trends for ${hoursNum} hours`);

      const trends = this.performanceAnalytics.getPerformanceTrends(hoursNum);

      return {
        success: true,
        data: {
          timeRange: `${hoursNum} hours`,
          trends,
          count: trends.length,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get performance trends: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve performance trends',
        details: error.message,
      };
    }
  }

  @Post('metric')
  @ApiOperation({ summary: 'Record a performance metric' })
  @ApiResponse({ status: 201, description: 'Metric recorded successfully' })
  async recordMetric(@Body() metricData: {
    metricType: string;
    value: number;
    unit: string;
    source: string;
    metadata?: Record<string, any>;
  }) {
    try {
      this.logger.log('Recording performance metric', { 
        metricType: metricData.metricType,
        source: metricData.source 
      });

      const metricId = await this.performanceAnalytics.recordMetric(
        metricData.metricType,
        metricData.value,
        metricData.unit,
        metricData.source,
        metricData.metadata
      );

      return {
        success: true,
        data: {
          metricId,
          message: 'Metric recorded successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to record metric: ${error.message}`);
      return {
        success: false,
        error: 'Failed to record metric',
        details: error.message,
      };
    }
  }

  @Post('response-time')
  @ApiOperation({ summary: 'Record response time metric' })
  @ApiResponse({ status: 201, description: 'Response time recorded successfully' })
  async recordResponseTime(@Body() responseData: {
    service: string;
    endpoint: string;
    responseTime: number;
    statusCode: number;
  }) {
    try {
      this.logger.log('Recording response time', { 
        service: responseData.service,
        endpoint: responseData.endpoint 
      });

      await this.performanceAnalytics.recordResponseTime(
        responseData.service,
        responseData.endpoint,
        responseData.responseTime,
        responseData.statusCode
      );

      return {
        success: true,
        data: {
          message: 'Response time recorded successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to record response time: ${error.message}`);
      return {
        success: false,
        error: 'Failed to record response time',
        details: error.message,
      };
    }
  }

  @Post('throughput')
  @ApiOperation({ summary: 'Record throughput metric' })
  @ApiResponse({ status: 201, description: 'Throughput recorded successfully' })
  async recordThroughput(@Body() throughputData: {
    service: string;
    requestCount: number;
    timeWindow: number;
  }) {
    try {
      this.logger.log('Recording throughput', { 
        service: throughputData.service,
        requestCount: throughputData.requestCount 
      });

      await this.performanceAnalytics.recordThroughput(
        throughputData.service,
        throughputData.requestCount,
        throughputData.timeWindow
      );

      return {
        success: true,
        data: {
          message: 'Throughput recorded successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to record throughput: ${error.message}`);
      return {
        success: false,
        error: 'Failed to record throughput',
        details: error.message,
      };
    }
  }

  @Post('error')
  @ApiOperation({ summary: 'Record error metric' })
  @ApiResponse({ status: 201, description: 'Error recorded successfully' })
  async recordError(@Body() errorData: {
    service: string;
    errorType: string;
    endpoint?: string;
    metadata?: Record<string, any>;
  }) {
    try {
      this.logger.log('Recording error', { 
        service: errorData.service,
        errorType: errorData.errorType 
      });

      await this.performanceAnalytics.recordError(
        errorData.service,
        errorData.errorType,
        errorData.endpoint,
        errorData.metadata
      );

      return {
        success: true,
        data: {
          message: 'Error recorded successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to record error: ${error.message}`);
      return {
        success: false,
        error: 'Failed to record error',
        details: error.message,
      };
    }
  }
}
