import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { campaignService } from '@/services/campaignService'
import {
  Campaign,
  CampaignAnalytics,
  CampaignParticipation,
  CampaignSubmission,
  CreateCampaignRequest,
  UpdateCampaignRequest,
  CampaignFilters,
  CampaignTemplate,
  CampaignStatus,
  ParticipationStatus,
  SubmissionStatus
} from '@/types/campaign.types'

// ===== CAMPAIGN CRUD HOOKS =====

export function useCampaigns(filters?: CampaignFilters) {
  return useQuery({
    queryKey: ['campaigns', filters],
    queryFn: () => campaignService.getCampaigns(filters),
    staleTime: 300000, // 5 minutes
    refetchOnWindowFocus: false,
  })
}

export function useCampaign(id: string) {
  return useQuery({
    queryKey: ['campaign', id],
    queryFn: () => campaignService.getCampaign(id),
    enabled: !!id,
    staleTime: 300000,
  })
}

export function useCreateCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateCampaignRequest) => campaignService.createCampaign(data),
    onSuccess: (newCampaign) => {
      // Invalidate campaigns list
      queryClient.invalidateQueries({ queryKey: ['campaigns'] })
      queryClient.invalidateQueries({ queryKey: ['projects', newCampaign.projectId, 'campaigns'] })
      
      // Add to cache
      queryClient.setQueryData(['campaign', newCampaign.id], newCampaign)
    },
    onError: (error: any) => {
      console.error('Failed to create campaign:', error)
    },
  })
}

export function useUpdateCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCampaignRequest }) => 
      campaignService.updateCampaign(id, data),
    onSuccess: (updatedCampaign, { id }) => {
      // Update campaign in cache
      queryClient.setQueryData(['campaign', id], updatedCampaign)
      queryClient.invalidateQueries({ queryKey: ['campaigns'] })
      queryClient.invalidateQueries({ queryKey: ['projects', updatedCampaign.projectId, 'campaigns'] })
    },
    onError: (error: any) => {
      console.error('Failed to update campaign:', error)
    },
  })
}

export function useDeleteCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => campaignService.deleteCampaign(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: ['campaign', id] })
      queryClient.invalidateQueries({ queryKey: ['campaigns'] })
    },
    onError: (error: any) => {
      console.error('Failed to delete campaign:', error)
    },
  })
}

// ===== CAMPAIGN STATUS MANAGEMENT HOOKS =====

export function useStartCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => campaignService.startCampaign(id),
    onSuccess: (updatedCampaign, id) => {
      queryClient.setQueryData(['campaign', id], updatedCampaign)
      queryClient.invalidateQueries({ queryKey: ['campaigns'] })
    },
    onError: (error: any) => {
      console.error('Failed to start campaign:', error)
    },
  })
}

export function usePauseCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => campaignService.pauseCampaign(id),
    onSuccess: (updatedCampaign, id) => {
      queryClient.setQueryData(['campaign', id], updatedCampaign)
      queryClient.invalidateQueries({ queryKey: ['campaigns'] })
    },
    onError: (error: any) => {
      console.error('Failed to pause campaign:', error)
    },
  })
}

export function useResumeCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => campaignService.resumeCampaign(id),
    onSuccess: (updatedCampaign, id) => {
      queryClient.setQueryData(['campaign', id], updatedCampaign)
      queryClient.invalidateQueries({ queryKey: ['campaigns'] })
    },
    onError: (error: any) => {
      console.error('Failed to resume campaign:', error)
    },
  })
}

export function useCompleteCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => campaignService.completeCampaign(id),
    onSuccess: (updatedCampaign, id) => {
      queryClient.setQueryData(['campaign', id], updatedCampaign)
      queryClient.invalidateQueries({ queryKey: ['campaigns'] })
    },
    onError: (error: any) => {
      console.error('Failed to complete campaign:', error)
    },
  })
}

export function useCancelCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) => 
      campaignService.cancelCampaign(id, reason),
    onSuccess: (updatedCampaign, { id }) => {
      queryClient.setQueryData(['campaign', id], updatedCampaign)
      queryClient.invalidateQueries({ queryKey: ['campaigns'] })
    },
    onError: (error: any) => {
      console.error('Failed to cancel campaign:', error)
    },
  })
}

// ===== CAMPAIGN PARTICIPATION HOOKS =====

export function useCampaignParticipants(campaignId: string, filters?: {
  status?: ParticipationStatus
  search?: string
  page?: number
  limit?: number
}) {
  return useQuery({
    queryKey: ['campaign', campaignId, 'participants', filters],
    queryFn: () => campaignService.getCampaignParticipants(campaignId, filters),
    enabled: !!campaignId,
    staleTime: 60000, // 1 minute
  })
}

export function useJoinCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ campaignId, data }: { 
      campaignId: string; 
      data: { twitterHandle?: string; walletAddress?: string; metadata?: Record<string, any> }
    }) => campaignService.joinCampaign(campaignId, data),
    onSuccess: (participation, { campaignId }) => {
      queryClient.invalidateQueries({ queryKey: ['campaign', campaignId, 'participants'] })
      queryClient.invalidateQueries({ queryKey: ['campaign', campaignId, 'participation'] })
      queryClient.invalidateQueries({ queryKey: ['campaign', campaignId] })
    },
    onError: (error: any) => {
      console.error('Failed to join campaign:', error)
    },
  })
}

export function useLeaveCampaign() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (campaignId: string) => campaignService.leaveCampaign(campaignId),
    onSuccess: (_, campaignId) => {
      queryClient.invalidateQueries({ queryKey: ['campaign', campaignId, 'participants'] })
      queryClient.invalidateQueries({ queryKey: ['campaign', campaignId, 'participation'] })
      queryClient.invalidateQueries({ queryKey: ['campaign', campaignId] })
    },
    onError: (error: any) => {
      console.error('Failed to leave campaign:', error)
    },
  })
}

export function useUserParticipation(campaignId: string, userId?: string) {
  return useQuery({
    queryKey: ['campaign', campaignId, 'participation', userId],
    queryFn: () => campaignService.getUserParticipation(campaignId, userId),
    enabled: !!campaignId,
    staleTime: 60000,
  })
}

// ===== CAMPAIGN SUBMISSIONS HOOKS =====

export function useSubmitRequirement() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ campaignId, requirementId, data }: {
      campaignId: string
      requirementId: string
      data: { content: any; metadata?: Record<string, any> }
    }) => campaignService.submitRequirement(campaignId, requirementId, data),
    onSuccess: (submission, { campaignId }) => {
      queryClient.invalidateQueries({ queryKey: ['campaign', campaignId, 'submissions'] })
      queryClient.invalidateQueries({ queryKey: ['campaign', campaignId, 'participation'] })
    },
    onError: (error: any) => {
      console.error('Failed to submit requirement:', error)
    },
  })
}

export function useCampaignSubmissions(campaignId: string, filters?: {
  requirementId?: string
  status?: SubmissionStatus
  userId?: string
  page?: number
  limit?: number
}) {
  return useQuery({
    queryKey: ['campaign', campaignId, 'submissions', filters],
    queryFn: () => campaignService.getSubmissions(campaignId, filters),
    enabled: !!campaignId,
    staleTime: 60000,
  })
}

export function useReviewSubmission() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ submissionId, data }: {
      submissionId: string
      data: { status: SubmissionStatus; notes?: string; points?: number }
    }) => campaignService.reviewSubmission(submissionId, data),
    onSuccess: (submission) => {
      queryClient.invalidateQueries({ queryKey: ['campaign', 'submissions'] })
      queryClient.invalidateQueries({ queryKey: ['submission', submission.id] })
    },
    onError: (error: any) => {
      console.error('Failed to review submission:', error)
    },
  })
}

// ===== CAMPAIGN ANALYTICS HOOKS =====

export function useCampaignAnalytics(campaignId: string, timeframe?: '24h' | '7d' | '30d' | '90d') {
  return useQuery({
    queryKey: ['campaign', campaignId, 'analytics', timeframe],
    queryFn: () => campaignService.getCampaignAnalytics(campaignId, timeframe),
    enabled: !!campaignId,
    staleTime: 300000, // 5 minutes
  })
}

export function useCampaignMetrics(campaignId: string) {
  return useQuery({
    queryKey: ['campaign', campaignId, 'metrics'],
    queryFn: () => campaignService.getCampaignMetrics(campaignId),
    enabled: !!campaignId,
    staleTime: 300000,
  })
}

export function useExportCampaignData() {
  return useMutation({
    mutationFn: ({ campaignId, format }: { campaignId: string; format: 'json' | 'csv' | 'pdf' }) => 
      campaignService.exportCampaignData(campaignId, format),
    onError: (error: any) => {
      console.error('Failed to export campaign data:', error)
    },
  })
}

// ===== CAMPAIGN TEMPLATES HOOKS =====

export function useCampaignTemplates(filters?: {
  type?: string
  category?: string
  difficulty?: string
  search?: string
}) {
  return useQuery({
    queryKey: ['campaign-templates', filters],
    queryFn: () => campaignService.getCampaignTemplates(filters),
    staleTime: 600000, // 10 minutes
  })
}

export function useCreateCampaignFromTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ templateId, data }: {
      templateId: string
      data: {
        projectId: string
        name: string
        description?: string
        startDate: string
        endDate: string
        customizations?: Record<string, any>
      }
    }) => campaignService.createCampaignFromTemplate(templateId, data),
    onSuccess: (newCampaign) => {
      queryClient.invalidateQueries({ queryKey: ['campaigns'] })
      queryClient.setQueryData(['campaign', newCampaign.id], newCampaign)
    },
    onError: (error: any) => {
      console.error('Failed to create campaign from template:', error)
    },
  })
}

// ===== UTILITY HOOKS =====

export function useSearchCampaigns(query: string, filters?: Partial<CampaignFilters>) {
  return useQuery({
    queryKey: ['campaigns', 'search', query, filters],
    queryFn: () => campaignService.searchCampaigns(query, filters),
    enabled: !!query && query.length > 2,
    staleTime: 300000,
  })
}

export function useFeaturedCampaigns(limit = 10) {
  return useQuery({
    queryKey: ['campaigns', 'featured', limit],
    queryFn: () => campaignService.getFeaturedCampaigns(limit),
    staleTime: 600000,
  })
}

export function useActiveCampaigns(limit = 20) {
  return useQuery({
    queryKey: ['campaigns', 'active', limit],
    queryFn: () => campaignService.getActiveCampaigns(limit),
    staleTime: 300000,
  })
}

export function useProjectCampaigns(projectId: string, filters?: Partial<CampaignFilters>) {
  return useQuery({
    queryKey: ['projects', projectId, 'campaigns', filters],
    queryFn: () => campaignService.getProjectCampaigns(projectId, filters),
    enabled: !!projectId,
    staleTime: 300000,
  })
}

export function useValidateCampaignData() {
  return useMutation({
    mutationFn: (data: CreateCampaignRequest) => campaignService.validateCampaignData(data),
    onError: (error: any) => {
      console.error('Failed to validate campaign data:', error)
    },
  })
}

export function usePreviewCampaign() {
  return useMutation({
    mutationFn: (data: CreateCampaignRequest) => campaignService.previewCampaign(data),
    onError: (error: any) => {
      console.error('Failed to preview campaign:', error)
    },
  })
}
