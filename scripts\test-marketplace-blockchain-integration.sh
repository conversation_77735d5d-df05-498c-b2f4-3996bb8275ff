#!/bin/bash

# =============================================================================
# MARKETPLACE-BLOCKCHAIN INTEGRATION TEST SCRIPT
# =============================================================================
# Tests end-to-end integration between marketplace and blockchain services
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# API endpoints
MARKETPLACE_API="http://localhost:3006/api/marketplace"
BLOCKCHAIN_API="http://localhost:3021"
API_GATEWAY="http://localhost:3010/api/marketplace"

echo ""
log_info "🔗 MARKETPLACE-BLOCKCHAIN INTEGRATION TESTING"
echo "=============================================="

# Test Data
TEST_USER="integration_user_$(date +%s)"
TEST_NFT_ID=$(uuidgen 2>/dev/null || echo "550e8400-e29b-41d4-a716-************")
TEST_TOKEN_ID="token_$(date +%s)"
TEST_WALLET="******************************************"

echo ""
log_info "📋 Test Data:"
echo "  User: $TEST_USER"
echo "  NFT ID: $TEST_NFT_ID"
echo "  Token ID: $TEST_TOKEN_ID"
echo "  Wallet: $TEST_WALLET"

# Function to test API endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    log_info "Testing: $description"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -H "x-user-id: $TEST_USER" \
            -d "$data" \
            "$endpoint")
    else
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X "$method" \
            "$endpoint")
    fi
    
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    echo "  Status: $http_status"
    echo "  Response: $(echo "$response_body" | head -c 200)..."
    
    if [[ "$http_status" =~ ^2[0-9][0-9]$ ]]; then
        log_success "$description - SUCCESS"
        return 0
    else
        log_error "$description - FAILED (Status: $http_status)"
        return 1
    fi
}

echo ""
log_info "🧪 PHASE 3: INTEGRATION TESTING"
echo "==============================="

# Test 1: Mock NFT Minting
echo ""
log_info "Test 1: Mock NFT Minting"
echo "----------------------"

nft_mint_data='{
    "to": "'$TEST_WALLET'",
    "metadata": {
        "name": "Integration Test NFT",
        "description": "Testing marketplace-blockchain integration",
        "image": "https://example.com/integration-nft.png",
        "attributes": [
            {"trait_type": "Test", "value": "Integration"},
            {"trait_type": "Rarity", "value": "Legendary"}
        ]
    },
    "campaignId": "test-campaign-integration",
    "userId": "'$TEST_USER'"
}'

test_endpoint "POST" "$BLOCKCHAIN_API/nft/mint" "$nft_mint_data" "Mock NFT Minting"

# Test 2: Create Marketplace Listing
echo ""
log_info "Test 2: Create Marketplace Listing"
echo "--------------------------------"

listing_data='{
    "nftId": "'$TEST_NFT_ID'",
    "tokenId": "'$TEST_TOKEN_ID'",
    "contractAddress": "0xMockNFTContract123",
    "price": "3.5",
    "currency": "ETH",
    "listingType": "fixed_price",
    "description": "Integration test NFT listing"
}'

test_endpoint "POST" "$MARKETPLACE_API/listings" "$listing_data" "Create Marketplace Listing"

# Test 3: Verify Listing via API Gateway
echo ""
log_info "Test 3: Verify Listing via API Gateway"
echo "------------------------------------"

test_endpoint "GET" "$API_GATEWAY/listings" "" "Get Listings via API Gateway"

# Test 4: Transaction Verification
echo ""
log_info "Test 4: Transaction Verification"
echo "------------------------------"

test_endpoint "GET" "$BLOCKCHAIN_API/transactions/0xintegrationtest123" "" "Verify Transaction"

echo ""
log_success "🎉 Marketplace-Blockchain Integration Testing Complete!"
echo ""
log_info "📊 Integration Summary:"
log_info "- Mock blockchain NFT minting: Tested"
log_info "- Marketplace listing creation: Tested"
log_info "- API Gateway routing: Tested"
log_info "- Transaction verification: Tested"
echo ""
