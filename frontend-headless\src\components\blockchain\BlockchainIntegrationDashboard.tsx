'use client'

import React, { useState } from 'react'
import {
  LinkIcon,
  CubeIcon,
  ArrowsRightLeftIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  WalletIcon,
  GlobeAltIcon,
  BoltIcon,
  ShieldCheckIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import {
  useConnectedWallets,
  useActiveNetworks,
  useNetworkStatus,
  useContracts,
  useBlockchainAnalytics
} from '@/hooks/useBlockchainIntegration'
import { BlockchainNetwork } from '@/types/blockchain-integration.types'
import WalletManager from './WalletManager'
import NetworkManager from './NetworkManager'
import ContractManager from './ContractManager'
import TransactionManager from './TransactionManager'
import CrossChainBridge from './CrossChainBridge'
import BlockchainAnalytics from './BlockchainAnalytics'

interface BlockchainIntegrationDashboardProps {
  onWalletConnected?: (walletId: string) => void
  onTransactionSent?: (transactionHash: string) => void
  className?: string
}

export default function BlockchainIntegrationDashboard({
  onWalletConnected,
  onTransactionSent,
  className = ''
}: BlockchainIntegrationDashboardProps) {
  const [activeTab, setActiveTab] = useState<'wallets' | 'networks' | 'contracts' | 'transactions' | 'bridge' | 'analytics'>('wallets')

  const { data: connectedWallets, isLoading: walletsLoading } = useConnectedWallets()
  const { data: activeNetworks, isLoading: networksLoading } = useActiveNetworks()
  const { data: networkStatus, isLoading: statusLoading } = useNetworkStatus()
  const { data: contracts, isLoading: contractsLoading } = useContracts()

  const tabs = [
    { id: 'wallets', name: 'Wallets', icon: WalletIcon, count: connectedWallets?.length },
    { id: 'networks', name: 'Networks', icon: GlobeAltIcon, count: activeNetworks?.length },
    { id: 'contracts', name: 'Contracts', icon: CubeIcon, count: contracts?.length },
    { id: 'transactions', name: 'Transactions', icon: ArrowsRightLeftIcon },
    { id: 'bridge', name: 'Cross-Chain', icon: LinkIcon },
    { id: 'analytics', name: 'Analytics', icon: ChartBarIcon }
  ]

  const getNetworkStatusColor = (status: any) => {
    if (!status) return 'bg-gray-400'
    return status.isOnline ? 'bg-green-400' : 'bg-red-400'
  }

  if (walletsLoading && !connectedWallets) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <LinkIcon className="h-8 w-8 mr-3 text-blue-600" />
              Blockchain Integration
            </h1>
            <p className="text-gray-600 mt-1">
              Manage multi-chain connections, contracts, and transactions
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Network Status Indicators */}
            {networkStatus && (
              <div className="flex items-center space-x-2">
                {Object.entries(networkStatus).slice(0, 4).map(([network, status]) => (
                  <div key={network} className="flex items-center space-x-1">
                    <div className={`w-2 h-2 rounded-full ${getNetworkStatusColor(status)}`}></div>
                    <span className="text-xs text-gray-600 capitalize">{network}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <WalletIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{connectedWallets?.length || 0}</div>
              <div className="text-sm text-gray-600">Connected Wallets</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <GlobeAltIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{activeNetworks?.length || 0}</div>
              <div className="text-sm text-gray-600">Active Networks</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CubeIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{contracts?.length || 0}</div>
              <div className="text-sm text-gray-600">Smart Contracts</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ShieldCheckIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {networkStatus ? Object.values(networkStatus).filter(s => s.isOnline).length : 0}
              </div>
              <div className="text-sm text-gray-600">Networks Online</div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Status */}
      {connectedWallets && connectedWallets.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <WalletIcon className="h-5 w-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-900">
                Active Wallet Connection
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {connectedWallets.slice(0, 3).map((wallet) => (
                    <div key={wallet.id} className="flex items-center justify-between">
                      <span>{wallet.type}: {wallet.address.slice(0, 6)}...{wallet.address.slice(-4)}</span>
                      <span className="font-medium capitalize">{wallet.network}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'wallets' && (
            <WalletManager 
              connectedWallets={connectedWallets}
              isLoading={walletsLoading}
              onWalletConnected={onWalletConnected}
            />
          )}

          {activeTab === 'networks' && (
            <NetworkManager 
              activeNetworks={activeNetworks}
              networkStatus={networkStatus}
              isLoading={networksLoading || statusLoading}
            />
          )}

          {activeTab === 'contracts' && (
            <ContractManager 
              contracts={contracts}
              isLoading={contractsLoading}
            />
          )}

          {activeTab === 'transactions' && (
            <TransactionManager 
              onTransactionSent={onTransactionSent}
            />
          )}

          {activeTab === 'bridge' && (
            <CrossChainBridge />
          )}

          {activeTab === 'analytics' && (
            <BlockchainAnalytics />
          )}
        </div>
      </div>

      {/* Network Health Status */}
      {networkStatus && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Network Health Status</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(networkStatus).map(([network, status]) => (
              <div key={network} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-900 capitalize">{network}</span>
                  <div className={`w-3 h-3 rounded-full ${getNetworkStatusColor(status)}`}></div>
                </div>
                
                <div className="space-y-1 text-xs text-gray-600">
                  <div className="flex justify-between">
                    <span>Block Height:</span>
                    <span className="font-medium">{status.blockHeight?.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Block Time:</span>
                    <span className="font-medium">{status.avgBlockTime}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Gas Price:</span>
                    <span className="font-medium">{status.gasPrice} gwei</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Congestion:</span>
                    <span className={`font-medium capitalize ${
                      status.congestion === 'low' ? 'text-green-600' :
                      status.congestion === 'medium' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {status.congestion}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button 
            onClick={() => setActiveTab('wallets')}
            className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <div className="text-center">
              <WalletIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Connect Wallet</span>
            </div>
          </button>
          
          <button 
            onClick={() => setActiveTab('contracts')}
            className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <div className="text-center">
              <CubeIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Deploy Contract</span>
            </div>
          </button>
          
          <button 
            onClick={() => setActiveTab('bridge')}
            className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <div className="text-center">
              <LinkIcon className="mx-auto h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Bridge Assets</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
