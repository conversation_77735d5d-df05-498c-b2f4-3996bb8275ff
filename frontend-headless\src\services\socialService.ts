import { api } from '@/lib/api'
import {
  UserProfile,
  SocialPost,
  Comment,
  Community,
  DirectMessage,
  Conversation,
  Follow,
  FollowRequest,
  SocialFeed,
  SocialAnalytics,
  SocialNotification,
  SocialLeaderboard,
  CreatePostRequest,
  UpdatePostRequest,
  CreateCommentRequest,
  UpdateProfileRequest,
  CreateCommunityRequest,
  SendMessageRequest,
  SocialSearchRequest,
  SocialSearchResponse,
  FeedFilters,
  PostType,
  CommunityCategory,
  LeaderboardType,
  SocialNotificationType
} from '@/types/social.types'

export class SocialService {
  // ===== USER PROFILE MANAGEMENT =====
  
  async getCurrentUserProfile(): Promise<UserProfile> {
    try {
      const response = await api.get('/social/profile/me')
      return response.data
    } catch (error) {
      console.error('Failed to fetch current user profile:', error)
      throw new Error('Failed to load user profile')
    }
  }

  async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      const response = await api.get(`/social/profile/${userId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
      throw new Error('Failed to load user profile')
    }
  }

  async updateProfile(updates: UpdateProfileRequest): Promise<UserProfile> {
    try {
      const response = await api.patch('/social/profile/me', updates)
      return response.data
    } catch (error) {
      console.error('Failed to update profile:', error)
      throw new Error('Failed to update profile')
    }
  }

  async uploadAvatar(file: File): Promise<{ avatarUrl: string }> {
    try {
      const formData = new FormData()
      formData.append('avatar', file)
      
      const response = await api.post('/social/profile/avatar', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      return response.data
    } catch (error) {
      console.error('Failed to upload avatar:', error)
      throw new Error('Failed to upload avatar')
    }
  }

  async uploadCoverImage(file: File): Promise<{ coverImageUrl: string }> {
    try {
      const formData = new FormData()
      formData.append('coverImage', file)
      
      const response = await api.post('/social/profile/cover', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      return response.data
    } catch (error) {
      console.error('Failed to upload cover image:', error)
      throw new Error('Failed to upload cover image')
    }
  }

  // ===== SOCIAL POSTS =====

  async createPost(postData: CreatePostRequest): Promise<SocialPost> {
    try {
      const response = await api.post('/social/posts', postData)
      return response.data
    } catch (error) {
      console.error('Failed to create post:', error)
      throw new Error('Failed to create post')
    }
  }

  async getPost(postId: string): Promise<SocialPost> {
    try {
      const response = await api.get(`/social/posts/${postId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch post:', error)
      throw new Error('Failed to load post')
    }
  }

  async updatePost(postId: string, updates: UpdatePostRequest): Promise<SocialPost> {
    try {
      const response = await api.patch(`/social/posts/${postId}`, updates)
      return response.data
    } catch (error) {
      console.error('Failed to update post:', error)
      throw new Error('Failed to update post')
    }
  }

  async deletePost(postId: string): Promise<void> {
    try {
      await api.delete(`/social/posts/${postId}`)
    } catch (error) {
      console.error('Failed to delete post:', error)
      throw new Error('Failed to delete post')
    }
  }

  async likePost(postId: string): Promise<void> {
    try {
      await api.post(`/social/posts/${postId}/like`)
    } catch (error) {
      console.error('Failed to like post:', error)
      throw new Error('Failed to like post')
    }
  }

  async unlikePost(postId: string): Promise<void> {
    try {
      await api.delete(`/social/posts/${postId}/like`)
    } catch (error) {
      console.error('Failed to unlike post:', error)
      throw new Error('Failed to unlike post')
    }
  }

  async sharePost(postId: string, comment?: string): Promise<void> {
    try {
      await api.post(`/social/posts/${postId}/share`, { comment })
    } catch (error) {
      console.error('Failed to share post:', error)
      throw new Error('Failed to share post')
    }
  }

  async bookmarkPost(postId: string): Promise<void> {
    try {
      await api.post(`/social/posts/${postId}/bookmark`)
    } catch (error) {
      console.error('Failed to bookmark post:', error)
      throw new Error('Failed to bookmark post')
    }
  }

  async unbookmarkPost(postId: string): Promise<void> {
    try {
      await api.delete(`/social/posts/${postId}/bookmark`)
    } catch (error) {
      console.error('Failed to unbookmark post:', error)
      throw new Error('Failed to unbookmark post')
    }
  }

  // ===== COMMENTS =====

  async createComment(postId: string, commentData: CreateCommentRequest): Promise<Comment> {
    try {
      const response = await api.post(`/social/posts/${postId}/comments`, commentData)
      return response.data
    } catch (error) {
      console.error('Failed to create comment:', error)
      throw new Error('Failed to create comment')
    }
  }

  async getComments(postId: string, limit = 20, offset = 0): Promise<{
    comments: Comment[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get(`/social/posts/${postId}/comments`, {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch comments:', error)
      throw new Error('Failed to load comments')
    }
  }

  async updateComment(commentId: string, content: string): Promise<Comment> {
    try {
      const response = await api.patch(`/social/comments/${commentId}`, { content })
      return response.data
    } catch (error) {
      console.error('Failed to update comment:', error)
      throw new Error('Failed to update comment')
    }
  }

  async deleteComment(commentId: string): Promise<void> {
    try {
      await api.delete(`/social/comments/${commentId}`)
    } catch (error) {
      console.error('Failed to delete comment:', error)
      throw new Error('Failed to delete comment')
    }
  }

  async likeComment(commentId: string): Promise<void> {
    try {
      await api.post(`/social/comments/${commentId}/like`)
    } catch (error) {
      console.error('Failed to like comment:', error)
      throw new Error('Failed to like comment')
    }
  }

  async unlikeComment(commentId: string): Promise<void> {
    try {
      await api.delete(`/social/comments/${commentId}/like`)
    } catch (error) {
      console.error('Failed to unlike comment:', error)
      throw new Error('Failed to unlike comment')
    }
  }

  // ===== SOCIAL FEED =====

  async getFeed(filters?: FeedFilters, limit = 20, cursor?: string): Promise<SocialFeed> {
    try {
      const response = await api.get('/social/feed', {
        params: { ...filters, limit, cursor }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch feed:', error)
      throw new Error('Failed to load social feed')
    }
  }

  async getUserPosts(userId: string, limit = 20, offset = 0): Promise<{
    posts: SocialPost[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get(`/social/users/${userId}/posts`, {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch user posts:', error)
      throw new Error('Failed to load user posts')
    }
  }

  async getTrendingPosts(timeframe = 'day', limit = 20): Promise<SocialPost[]> {
    try {
      const response = await api.get('/social/posts/trending', {
        params: { timeframe, limit }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch trending posts:', error)
      throw new Error('Failed to load trending posts')
    }
  }

  async getBookmarkedPosts(limit = 20, offset = 0): Promise<{
    posts: SocialPost[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get('/social/posts/bookmarked', {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch bookmarked posts:', error)
      throw new Error('Failed to load bookmarked posts')
    }
  }

  // ===== FOLLOW SYSTEM =====

  async followUser(userId: string): Promise<Follow> {
    try {
      const response = await api.post(`/social/users/${userId}/follow`)
      return response.data
    } catch (error) {
      console.error('Failed to follow user:', error)
      throw new Error('Failed to follow user')
    }
  }

  async unfollowUser(userId: string): Promise<void> {
    try {
      await api.delete(`/social/users/${userId}/follow`)
    } catch (error) {
      console.error('Failed to unfollow user:', error)
      throw new Error('Failed to unfollow user')
    }
  }

  async getFollowers(userId: string, limit = 20, offset = 0): Promise<{
    followers: UserProfile[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get(`/social/users/${userId}/followers`, {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch followers:', error)
      throw new Error('Failed to load followers')
    }
  }

  async getFollowing(userId: string, limit = 20, offset = 0): Promise<{
    following: UserProfile[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get(`/social/users/${userId}/following`, {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch following:', error)
      throw new Error('Failed to load following')
    }
  }

  async getFollowRequests(limit = 20, offset = 0): Promise<{
    requests: FollowRequest[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get('/social/follow-requests', {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch follow requests:', error)
      throw new Error('Failed to load follow requests')
    }
  }

  async acceptFollowRequest(requestId: string): Promise<void> {
    try {
      await api.post(`/social/follow-requests/${requestId}/accept`)
    } catch (error) {
      console.error('Failed to accept follow request:', error)
      throw new Error('Failed to accept follow request')
    }
  }

  async rejectFollowRequest(requestId: string): Promise<void> {
    try {
      await api.post(`/social/follow-requests/${requestId}/reject`)
    } catch (error) {
      console.error('Failed to reject follow request:', error)
      throw new Error('Failed to reject follow request')
    }
  }

  // ===== DIRECT MESSAGES =====

  async getConversations(limit = 20, offset = 0): Promise<{
    conversations: Conversation[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get('/social/conversations', {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch conversations:', error)
      throw new Error('Failed to load conversations')
    }
  }

  async getConversation(conversationId: string): Promise<Conversation> {
    try {
      const response = await api.get(`/social/conversations/${conversationId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch conversation:', error)
      throw new Error('Failed to load conversation')
    }
  }

  async getMessages(conversationId: string, limit = 50, offset = 0): Promise<{
    messages: DirectMessage[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get(`/social/conversations/${conversationId}/messages`, {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch messages:', error)
      throw new Error('Failed to load messages')
    }
  }

  async sendMessage(messageData: SendMessageRequest): Promise<DirectMessage> {
    try {
      const response = await api.post('/social/messages', messageData)
      return response.data
    } catch (error) {
      console.error('Failed to send message:', error)
      throw new Error('Failed to send message')
    }
  }

  async markMessageAsRead(messageId: string): Promise<void> {
    try {
      await api.patch(`/social/messages/${messageId}/read`)
    } catch (error) {
      console.error('Failed to mark message as read:', error)
      throw new Error('Failed to mark message as read')
    }
  }

  async markConversationAsRead(conversationId: string): Promise<void> {
    try {
      await api.patch(`/social/conversations/${conversationId}/read`)
    } catch (error) {
      console.error('Failed to mark conversation as read:', error)
      throw new Error('Failed to mark conversation as read')
    }
  }

  async deleteMessage(messageId: string): Promise<void> {
    try {
      await api.delete(`/social/messages/${messageId}`)
    } catch (error) {
      console.error('Failed to delete message:', error)
      throw new Error('Failed to delete message')
    }
  }

  // ===== COMMUNITIES =====

  async getCommunities(category?: CommunityCategory, limit = 20, offset = 0): Promise<{
    communities: Community[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get('/social/communities', {
        params: { category, limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch communities:', error)
      throw new Error('Failed to load communities')
    }
  }

  async getCommunity(communityId: string): Promise<Community> {
    try {
      const response = await api.get(`/social/communities/${communityId}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch community:', error)
      throw new Error('Failed to load community')
    }
  }

  async createCommunity(communityData: CreateCommunityRequest): Promise<Community> {
    try {
      const response = await api.post('/social/communities', communityData)
      return response.data
    } catch (error) {
      console.error('Failed to create community:', error)
      throw new Error('Failed to create community')
    }
  }

  async joinCommunity(communityId: string): Promise<void> {
    try {
      await api.post(`/social/communities/${communityId}/join`)
    } catch (error) {
      console.error('Failed to join community:', error)
      throw new Error('Failed to join community')
    }
  }

  async leaveCommunity(communityId: string): Promise<void> {
    try {
      await api.delete(`/social/communities/${communityId}/join`)
    } catch (error) {
      console.error('Failed to leave community:', error)
      throw new Error('Failed to leave community')
    }
  }

  async getCommunityPosts(communityId: string, limit = 20, offset = 0): Promise<{
    posts: SocialPost[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get(`/social/communities/${communityId}/posts`, {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch community posts:', error)
      throw new Error('Failed to load community posts')
    }
  }

  async getUserCommunities(userId?: string, limit = 20, offset = 0): Promise<{
    communities: Community[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const endpoint = userId ? `/social/users/${userId}/communities` : '/social/communities/my'
      const response = await api.get(endpoint, {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch user communities:', error)
      throw new Error('Failed to load user communities')
    }
  }

  // ===== SEARCH =====

  async search(searchRequest: SocialSearchRequest): Promise<SocialSearchResponse> {
    try {
      const response = await api.post('/social/search', searchRequest)
      return response.data
    } catch (error) {
      console.error('Failed to search:', error)
      throw new Error('Failed to perform search')
    }
  }

  async getSuggestedUsers(limit = 10): Promise<UserProfile[]> {
    try {
      const response = await api.get('/social/suggestions/users', {
        params: { limit }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch suggested users:', error)
      throw new Error('Failed to load suggested users')
    }
  }

  async getSuggestedCommunities(limit = 10): Promise<Community[]> {
    try {
      const response = await api.get('/social/suggestions/communities', {
        params: { limit }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch suggested communities:', error)
      throw new Error('Failed to load suggested communities')
    }
  }

  // ===== ANALYTICS =====

  async getSocialAnalytics(timeframe = '30d'): Promise<SocialAnalytics> {
    try {
      const response = await api.get('/social/analytics', {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch social analytics:', error)
      throw new Error('Failed to load social analytics')
    }
  }

  async getUserAnalytics(userId: string, timeframe = '30d'): Promise<SocialAnalytics> {
    try {
      const response = await api.get(`/social/users/${userId}/analytics`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch user analytics:', error)
      throw new Error('Failed to load user analytics')
    }
  }

  // ===== LEADERBOARDS =====

  async getLeaderboard(type: LeaderboardType, timeframe = 'weekly'): Promise<SocialLeaderboard> {
    try {
      const response = await api.get('/social/leaderboard', {
        params: { type, timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch leaderboard:', error)
      throw new Error('Failed to load leaderboard')
    }
  }

  // ===== NOTIFICATIONS =====

  async getNotifications(limit = 20, offset = 0): Promise<{
    notifications: SocialNotification[]
    totalCount: number
    unreadCount: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get('/social/notifications', {
        params: { limit, offset }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
      throw new Error('Failed to load notifications')
    }
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      await api.patch(`/social/notifications/${notificationId}/read`)
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
      throw new Error('Failed to mark notification as read')
    }
  }

  async markAllNotificationsAsRead(): Promise<void> {
    try {
      await api.patch('/social/notifications/read-all')
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
      throw new Error('Failed to mark all notifications as read')
    }
  }

  async deleteNotification(notificationId: string): Promise<void> {
    try {
      await api.delete(`/social/notifications/${notificationId}`)
    } catch (error) {
      console.error('Failed to delete notification:', error)
      throw new Error('Failed to delete notification')
    }
  }
}

export const socialService = new SocialService()
