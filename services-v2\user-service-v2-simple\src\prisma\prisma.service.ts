/**
 * Prisma Service - User Service V2 Simple
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  constructor() {
    super({
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
      log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      console.log('✅ Connected to user_service_v2 database');
    } catch (error) {
      console.error('❌ Failed to connect to user_service_v2 database:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    await this.$disconnect();
    console.log('🔌 Disconnected from user_service_v2 database');
  }

  async healthCheck() {
    try {
      await this.$queryRaw`SELECT 1`;
      return {
        status: 'healthy',
        database: 'user_service_v2',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        database: 'user_service_v2',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
