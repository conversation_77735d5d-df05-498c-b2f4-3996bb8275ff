#!/bin/bash

# Standardize Service-Level Environment Files
# This script creates consistent .env files for all services

set -e

echo "🌍 Standardizing Service-Level Environment Files"
echo "==============================================="

# Services to standardize
SERVICES=(
    "user-service:3011:user_service"
    "profile-analysis-service:3002:profile_analysis_service"
    "nft-generation-service:3003:nft_generation_service"
    "blockchain-service:3004:blockchain_service"
    "project-service:3005:project_service"
    "marketplace-service:3006:marketplace_service"
    "notification-service:3008:notification_service"
    "analytics-service:3009:analytics_service"
    "api-gateway:3010:api_gateway"
)

# Function to create standardized service .env file
create_service_env() {
    local service_info="$1"
    local service_name=$(echo "$service_info" | cut -d: -f1)
    local service_port=$(echo "$service_info" | cut -d: -f2)
    local db_name=$(echo "$service_info" | cut -d: -f3)
    local service_dir="services/$service_name"
    
    echo "🔧 Creating standardized .env for $service_name..."
    
    # Create service directory if it doesn't exist
    mkdir -p "$service_dir"
    
    # Create standardized .env file
    cat > "$service_dir/.env" << EOF
# ===== SERVICE IDENTIFICATION =====
SERVICE_NAME=$service_name
SERVICE_VERSION=1.0.0
SERVICE_PORT=$service_port

# ===== ENVIRONMENT CONFIGURATION =====
NODE_ENV=development
PORT=$service_port

# ===== DATABASE CONFIGURATION =====
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111
DB_DATABASE=$db_name

# Prisma Database URL
DATABASE_URL="postgresql://postgres:1111@localhost:5432/$db_name"

# ===== SECURITY CONFIGURATION =====
# JWT Configuration (inherited from platform)
JWT_SECRET=dev-jwt-secret-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Gateway Security (inherited from platform)
GATEWAY_SECRET=dev-gateway-secret-change-in-production
ALLOW_DIRECT_ACCESS=true
TRUSTED_IPS=127.0.0.1,::1,localhost
ENABLE_GATEWAY_AUTH=true

# ===== API GATEWAY CONFIGURATION =====
API_GATEWAY_URL=http://localhost:3010

# ===== CACHING CONFIGURATION =====
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379
ENABLE_REDIS_CACHE=false
CACHE_TTL=300

# ===== LOGGING CONFIGURATION =====
LOG_LEVEL=debug
LOG_FORMAT=combined
ENABLE_DEBUG_LOGGING=true

# ===== DEVELOPMENT FLAGS =====
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_HOT_RELOAD=true

# ===== ENTERPRISE FEATURES =====
ENABLE_AUDIT_LOGGING=true
ENABLE_EVENT_SOURCING=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_CORRELATION_TRACKING=true

# ===== ENTERPRISE COMPLIANCE =====
DATA_CLASSIFICATION=internal
RETENTION_POLICY=7years

# ===== FEATURE FLAGS =====
ENABLE_RATE_LIMITING=false
ENABLE_API_VERSIONING=true

# ===== SERVICE-SPECIFIC CONFIGURATION =====
# Add service-specific variables below this line
EOF

    # Add service-specific configurations
    case $service_name in
        "api-gateway")
            cat >> "$service_dir/.env" << 'EOF'

# ===== API GATEWAY SPECIFIC =====
ENABLE_REQUEST_LOGGING=true
ENABLE_RESPONSE_CACHING=false
REQUEST_TIMEOUT=30000
MAX_REQUEST_SIZE=10MB
ENABLE_COMPRESSION=true
EOF
            ;;
        "user-service")
            cat >> "$service_dir/.env" << 'EOF'

# ===== USER SERVICE SPECIFIC =====
ENABLE_USER_ANALYTICS=true
ENABLE_PROFILE_CACHING=true
MAX_UPLOAD_SIZE=10MB
ENABLE_EMAIL_VERIFICATION=true
PASSWORD_RESET_EXPIRY=1h
EOF
            ;;
        "blockchain-service")
            cat >> "$service_dir/.env" << 'EOF'

# ===== BLOCKCHAIN SERVICE SPECIFIC =====
BLOCKCHAIN_NETWORK=testnet
GAS_LIMIT=21000
GAS_PRICE=20
CONFIRMATION_BLOCKS=3
ENABLE_TRANSACTION_MONITORING=true
EOF
            ;;
        "marketplace-service")
            cat >> "$service_dir/.env" << 'EOF'

# ===== MARKETPLACE SERVICE SPECIFIC =====
ENABLE_ESCROW=true
COMMISSION_RATE=2.5
MIN_LISTING_PRICE=0.001
MAX_LISTING_PRICE=1000
ENABLE_AUCTION=true
AUCTION_EXTENSION_TIME=300
EOF
            ;;
        "notification-service")
            cat >> "$service_dir/.env" << 'EOF'

# ===== NOTIFICATION SERVICE SPECIFIC =====
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_PUSH_NOTIFICATIONS=false
ENABLE_SMS_NOTIFICATIONS=false
EMAIL_QUEUE_SIZE=1000
NOTIFICATION_RETRY_ATTEMPTS=3
EOF
            ;;
        *)
            cat >> "$service_dir/.env" << 'EOF'

# ===== SERVICE SPECIFIC CONFIGURATION =====
# Add your service-specific environment variables here
EOF
            ;;
    esac
    
    echo "✅ Created standardized .env for $service_name"
}

# Function to backup existing .env files
backup_existing_env() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    if [ -f "$service_dir/.env" ]; then
        echo "📋 Backing up existing .env for $service_name..."
        cp "$service_dir/.env" "$service_dir/.env.backup.$(date +%Y%m%d_%H%M%S)"
    fi
}

# Function to create .env.local template
create_env_local_template() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    cat > "$service_dir/.env.local.template" << 'EOF'
# ===== LOCAL OVERRIDES =====
# This file is for local development overrides
# Copy this to .env.local and customize as needed
# .env.local is git-ignored and won't be committed

# Example overrides:
# LOG_LEVEL=trace
# ENABLE_DEBUG_LOGGING=true
# DB_PASSWORD=your-local-password
# REDIS_URL=redis://your-local-redis:6379

# Service-specific local overrides:
# Add your local development overrides here
EOF
}

# Main execution
echo "🚀 Starting service environment standardization..."

for service_info in "${SERVICES[@]}"; do
    service_name=$(echo "$service_info" | cut -d: -f1)
    
    echo ""
    echo "🔄 Processing $service_name..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service_name" ]; then
        echo "❌ Service directory not found: services/$service_name"
        continue
    fi
    
    # Backup existing .env
    backup_existing_env "$service_name"
    
    # Create standardized .env
    create_service_env "$service_info"
    
    # Create .env.local template
    create_env_local_template "$service_name"
    
    echo "✅ $service_name environment standardization complete"
done

echo ""
echo "🎉 Service Environment Standardization Completed!"
echo "================================================"
echo ""
echo "📋 Summary:"
echo "- Standardized .env files for ${#SERVICES[@]} services"
echo "- Created .env.local templates for local overrides"
echo "- Backed up existing .env files"
echo "- Applied consistent naming conventions"
echo ""
echo "📁 File Structure Created:"
echo "services/[service-name]/.env              # Standardized configuration"
echo "services/[service-name]/.env.local.template # Template for local overrides"
echo "services/[service-name]/.env.backup.*     # Backup of original files"
echo ""
echo "🔍 Next Steps:"
echo "1. Review the generated .env files"
echo "2. Copy .env.local.template to .env.local for local overrides"
echo "3. Update service configurations to use standardized variables"
echo "4. Test environment switching functionality"
echo ""
echo "⚠️  Important Notes:"
echo "• .env.local files are git-ignored for security"
echo "• Service-level .env files inherit from platform-level configuration"
echo "• Use .env.local for sensitive local development overrides"
