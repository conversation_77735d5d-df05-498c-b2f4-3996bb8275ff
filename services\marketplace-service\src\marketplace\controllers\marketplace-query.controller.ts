import {
  Controller,
  Get,
  Param,
  Query,
  Res,
  HttpStatus,
  ParseIntPipe,
  DefaultValuePipe,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { MarketplaceQueryService } from '../services/marketplace-query.service';

@ApiTags('Marketplace')
@Controller('marketplace')
export class MarketplaceQueryController {
  private readonly logger = new Logger(MarketplaceQueryController.name);

  constructor(private readonly marketplaceQueryService: MarketplaceQueryService) {}

  @Get('listings')
  @ApiOperation({
    summary: 'Get marketplace listings',
    description: 'Retrieve paginated list of active NFT listings in the marketplace'
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by category' })
  @ApiQuery({ name: 'priceMin', required: false, description: 'Minimum price filter' })
  @ApiQuery({ name: 'priceMax', required: false, description: 'Maximum price filter' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort by field (price, date, popularity)' })
  @ApiResponse({ status: 200, description: 'Listings retrieved successfully' })
  async getListings(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('category') category: string,
    @Query('priceMin') priceMin: string,
    @Query('priceMax') priceMax: string,
    @Query('sortBy', new DefaultValuePipe('date')) sortBy: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log('Getting marketplace listings');
      
      const filters = { category, priceMin, priceMax, sortBy };
      const result = await this.marketplaceQueryService.getListings(page, limit, filters);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Listings retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('auctions')
  @ApiOperation({
    summary: 'Get active auctions',
    description: 'Retrieve list of active NFT auctions'
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by auction status' })
  @ApiResponse({ status: 200, description: 'Auctions retrieved successfully' })
  async getAuctions(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('status', new DefaultValuePipe('active')) status: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log('Getting active auctions');
      
      const filters = { status };
      const result = await this.marketplaceQueryService.getAuctions(page, limit, filters);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Auctions retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('stats')
  @ApiOperation({
    summary: 'Get marketplace statistics',
    description: 'Get comprehensive marketplace statistics and metrics'
  })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getMarketplaceStats(@Res() res: Response) {
    try {
      this.logger.log('Getting marketplace statistics');
      
      const result = await this.marketplaceQueryService.getMarketplaceStats();
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Statistics retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('trending')
  @ApiOperation({
    summary: 'Get trending NFTs',
    description: 'Get list of trending NFTs based on trading activity'
  })
  @ApiQuery({ name: 'period', required: false, description: 'Time period (24h, 7d, 30d)' })
  @ApiResponse({ status: 200, description: 'Trending NFTs retrieved successfully' })
  async getTrendingNFTs(
    @Query('period', new DefaultValuePipe('24h')) period: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting trending NFTs for period: ${period}`);
      
      const result = await this.marketplaceQueryService.getTrendingNFTs(period);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Trending NFTs retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('collection/:collectionId')
  @ApiOperation({
    summary: 'Get collection marketplace data',
    description: 'Get marketplace data for a specific NFT collection'
  })
  @ApiParam({ name: 'collectionId', description: 'Collection ID' })
  @ApiResponse({ status: 200, description: 'Collection data retrieved successfully' })
  async getCollectionData(@Param('collectionId') collectionId: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting collection data for: ${collectionId}`);
      
      const result = await this.marketplaceQueryService.getCollectionData(collectionId);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Collection data retrieval failed for ${collectionId}: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
