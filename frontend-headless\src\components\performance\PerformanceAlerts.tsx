'use client'

import React, { useState } from 'react'
import {
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  EyeIcon,
  EyeSlashIcon,
  BellIcon,
  BellSlashIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import {
  useAcknowledgeAlert,
  useResolveAlert,
  useSuppressAlert
} from '@/hooks/usePerformance'
import {
  PerformanceAlert,
  AlertSeverity,
  AlertStatus,
  AlertType
} from '@/types/performance.types'

interface PerformanceAlertsProps {
  alerts?: PerformanceAlert[]
  className?: string
}

export default function PerformanceAlerts({
  alerts = [],
  className = ''
}: PerformanceAlertsProps) {
  const [selectedSeverity, setSelectedSeverity] = useState<AlertSeverity | 'all'>('all')
  const [selectedStatus, setSelectedStatus] = useState<AlertStatus | 'all'>('all')
  const [showResolutionModal, setShowResolutionModal] = useState<string | null>(null)
  const [resolutionText, setResolutionText] = useState('')

  const acknowledgeAlertMutation = useAcknowledgeAlert()
  const resolveAlertMutation = useResolveAlert()
  const suppressAlertMutation = useSuppressAlert()

  // Mock alerts if none provided
  const mockAlerts: PerformanceAlert[] = [
    {
      id: '1',
      type: AlertType.THRESHOLD_EXCEEDED,
      severity: AlertSeverity.HIGH,
      title: 'High CPU Usage Detected',
      description: 'CPU usage has exceeded 85% for the past 10 minutes on server-01',
      metric: 'cpu_usage',
      currentValue: 87.5,
      threshold: 85,
      timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      status: AlertStatus.ACTIVE,
      actions: [
        { type: 'investigate', label: 'Investigate', url: '/performance/system' },
        { type: 'scale_up', label: 'Scale Up', automated: true }
      ],
      metadata: {
        server: 'server-01',
        region: 'us-east-1',
        service: 'api-gateway'
      }
    },
    {
      id: '2',
      type: AlertType.ANOMALY_DETECTED,
      severity: AlertSeverity.MEDIUM,
      title: 'Unusual Response Time Pattern',
      description: 'Response times showing anomalous pattern - 40% increase from baseline',
      metric: 'response_time',
      currentValue: 280,
      threshold: 200,
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      status: AlertStatus.ACKNOWLEDGED,
      actions: [
        { type: 'investigate', label: 'View Details', url: '/performance/metrics' },
        { type: 'optimize_query', label: 'Optimize Queries' }
      ],
      metadata: {
        endpoint: '/api/nfts',
        method: 'GET',
        percentile: 'p95'
      }
    },
    {
      id: '3',
      type: AlertType.RESOURCE_EXHAUSTION,
      severity: AlertSeverity.CRITICAL,
      title: 'Database Connection Pool Exhausted',
      description: 'All database connections are in use. New requests are being queued.',
      metric: 'db_connections',
      currentValue: 100,
      threshold: 95,
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      status: AlertStatus.ACTIVE,
      actions: [
        { type: 'scale_up', label: 'Increase Pool Size', automated: true },
        { type: 'restart_service', label: 'Restart Service' }
      ],
      metadata: {
        database: 'primary',
        pool_size: 100,
        queue_length: 25
      }
    },
    {
      id: '4',
      type: AlertType.TREND_DEGRADATION,
      severity: AlertSeverity.LOW,
      title: 'Cache Hit Rate Declining',
      description: 'Cache hit rate has been steadily declining over the past hour',
      metric: 'cache_hit_rate',
      currentValue: 82.5,
      threshold: 90,
      timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
      status: AlertStatus.RESOLVED,
      actions: [
        { type: 'clear_cache', label: 'Clear Cache' },
        { type: 'investigate', label: 'Analyze Patterns' }
      ],
      metadata: {
        cache_type: 'redis',
        trend_duration: '1h',
        decline_rate: '7.5%'
      }
    }
  ]

  const displayAlerts = alerts.length > 0 ? alerts : mockAlerts

  const severityOptions = [
    { value: 'all', label: 'All Severities' },
    { value: AlertSeverity.CRITICAL, label: 'Critical' },
    { value: AlertSeverity.HIGH, label: 'High' },
    { value: AlertSeverity.MEDIUM, label: 'Medium' },
    { value: AlertSeverity.LOW, label: 'Low' }
  ]

  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: AlertStatus.ACTIVE, label: 'Active' },
    { value: AlertStatus.ACKNOWLEDGED, label: 'Acknowledged' },
    { value: AlertStatus.RESOLVED, label: 'Resolved' },
    { value: AlertStatus.SUPPRESSED, label: 'Suppressed' }
  ]

  const filteredAlerts = displayAlerts.filter(alert => {
    const severityMatch = selectedSeverity === 'all' || alert.severity === selectedSeverity
    const statusMatch = selectedStatus === 'all' || alert.status === selectedStatus
    return severityMatch && statusMatch
  })

  const getSeverityIcon = (severity: AlertSeverity) => {
    switch (severity) {
      case AlertSeverity.CRITICAL:
        return <XCircleIcon className="h-5 w-5 text-red-600" />
      case AlertSeverity.HIGH:
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-600" />
      case AlertSeverity.MEDIUM:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
      case AlertSeverity.LOW:
        return <InformationCircleIcon className="h-5 w-5 text-blue-600" />
      default:
        return <InformationCircleIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getSeverityColor = (severity: AlertSeverity) => {
    switch (severity) {
      case AlertSeverity.CRITICAL:
        return 'border-red-200 bg-red-50'
      case AlertSeverity.HIGH:
        return 'border-orange-200 bg-orange-50'
      case AlertSeverity.MEDIUM:
        return 'border-yellow-200 bg-yellow-50'
      case AlertSeverity.LOW:
        return 'border-blue-200 bg-blue-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getStatusIcon = (status: AlertStatus) => {
    switch (status) {
      case AlertStatus.ACTIVE:
        return <BellIcon className="h-4 w-4 text-red-600" />
      case AlertStatus.ACKNOWLEDGED:
        return <EyeIcon className="h-4 w-4 text-yellow-600" />
      case AlertStatus.RESOLVED:
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />
      case AlertStatus.SUPPRESSED:
        return <BellSlashIcon className="h-4 w-4 text-gray-600" />
      default:
        return <InformationCircleIcon className="h-4 w-4 text-gray-600" />
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  const handleAcknowledge = (alertId: string) => {
    acknowledgeAlertMutation.mutate(alertId)
  }

  const handleResolve = (alertId: string) => {
    if (resolutionText.trim()) {
      resolveAlertMutation.mutate({ alertId, resolution: resolutionText })
      setShowResolutionModal(null)
      setResolutionText('')
    } else {
      resolveAlertMutation.mutate({ alertId })
    }
  }

  const handleSuppress = (alertId: string, duration: number) => {
    suppressAlertMutation.mutate({ alertId, duration })
  }

  const alertStats = {
    total: displayAlerts.length,
    active: displayAlerts.filter(a => a.status === AlertStatus.ACTIVE).length,
    critical: displayAlerts.filter(a => a.severity === AlertSeverity.CRITICAL).length,
    acknowledged: displayAlerts.filter(a => a.status === AlertStatus.ACKNOWLEDGED).length
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900">Performance Alerts</h2>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedSeverity}
            onChange={(e) => setSelectedSeverity(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {severityOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Alert Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <BellIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{alertStats.total}</div>
              <div className="text-sm text-gray-600">Total Alerts</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{alertStats.active}</div>
              <div className="text-sm text-gray-600">Active Alerts</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircleIcon className="h-8 w-8 text-red-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{alertStats.critical}</div>
              <div className="text-sm text-gray-600">Critical Alerts</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <EyeIcon className="h-8 w-8 text-yellow-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{alertStats.acknowledged}</div>
              <div className="text-sm text-gray-600">Acknowledged</div>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts List */}
      {filteredAlerts.length > 0 ? (
        <div className="space-y-4">
          {filteredAlerts.map((alert) => (
            <div key={alert.id} className={`border rounded-lg p-6 ${getSeverityColor(alert.severity)}`}>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className="flex-shrink-0">
                    {getSeverityIcon(alert.severity)}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-sm font-medium text-gray-900">{alert.title}</h3>
                      
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        alert.severity === AlertSeverity.CRITICAL ? 'bg-red-100 text-red-800' :
                        alert.severity === AlertSeverity.HIGH ? 'bg-orange-100 text-orange-800' :
                        alert.severity === AlertSeverity.MEDIUM ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {alert.severity}
                      </span>
                      
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {getStatusIcon(alert.status)}
                        <span className="ml-1">{alert.status}</span>
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-700 mb-2">{alert.description}</p>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500 mb-3">
                      <div className="flex items-center">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        {formatTimeAgo(alert.timestamp)}
                      </div>
                      
                      <span>Metric: {alert.metric}</span>
                      <span>Current: {alert.currentValue}</span>
                      <span>Threshold: {alert.threshold}</span>
                    </div>

                    {/* Metadata */}
                    {Object.keys(alert.metadata).length > 0 && (
                      <div className="text-xs text-gray-600 mb-3">
                        {Object.entries(alert.metadata).map(([key, value]) => (
                          <span key={key} className="mr-3">
                            {key}: {String(value)}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      {alert.actions.map((action, index) => (
                        <button
                          key={index}
                          onClick={() => action.url && window.open(action.url, '_blank')}
                          className="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        >
                          {action.label}
                          {action.automated && (
                            <span className="ml-1 text-blue-600">(Auto)</span>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Alert Actions */}
                <div className="flex items-center space-x-2 ml-4">
                  {alert.status === AlertStatus.ACTIVE && (
                    <>
                      <button
                        onClick={() => handleAcknowledge(alert.id)}
                        className="text-yellow-600 hover:text-yellow-700"
                        title="Acknowledge"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      
                      <button
                        onClick={() => setShowResolutionModal(alert.id)}
                        className="text-green-600 hover:text-green-700"
                        title="Resolve"
                      >
                        <CheckCircleIcon className="h-4 w-4" />
                      </button>
                      
                      <button
                        onClick={() => handleSuppress(alert.id, 3600)} // 1 hour
                        className="text-gray-600 hover:text-gray-700"
                        title="Suppress for 1 hour"
                      >
                        <EyeSlashIcon className="h-4 w-4" />
                      </button>
                    </>
                  )}
                  
                  {alert.status === AlertStatus.ACKNOWLEDGED && (
                    <button
                      onClick={() => setShowResolutionModal(alert.id)}
                      className="text-green-600 hover:text-green-700"
                      title="Resolve"
                    >
                      <CheckCircleIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <CheckCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No alerts found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {selectedSeverity !== 'all' || selectedStatus !== 'all'
              ? 'Try adjusting your filters to see more alerts.'
              : 'All systems are running smoothly. No performance alerts at this time.'}
          </p>
        </div>
      )}

      {/* Resolution Modal */}
      {showResolutionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Resolve Alert</h3>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Resolution Notes (Optional)
                </label>
                <textarea
                  value={resolutionText}
                  onChange={(e) => setResolutionText(e.target.value)}
                  placeholder="Describe how the issue was resolved..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div className="flex items-center justify-end space-x-3">
                <button
                  onClick={() => setShowResolutionModal(null)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleResolve(showResolutionModal)}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                >
                  Resolve Alert
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
