/**
 * Update Analysis DTO - Profile Analysis Service V2
 * 
 * Data Transfer Object for updating profile analysis
 */

import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsIn,
  IsObject,
  IsN<PERSON>ber,
  Min,
  Max,
} from 'class-validator';

export class UpdateAnalysisDto {
  @ApiProperty({
    description: 'Analysis status',
    example: 'processing',
    enum: ['pending', 'processing', 'completed', 'failed'],
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['pending', 'processing', 'completed', 'failed'])
  status?: string;

  @ApiProperty({
    description: 'Analysis progress (0-100)',
    example: 75,
    minimum: 0,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  progress?: number;

  @ApiProperty({
    description: 'Analysis results',
    example: {
      summary: 'High engagement profile with strong tech focus',
      scores: { engagement: 0.85, reach: 0.72, sentiment: 0.91 }
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  results?: Record<string, any>;

  @ApiProperty({
    description: 'Confidence score (0.0-1.0)',
    example: 0.87,
    minimum: 0,
    maximum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;

  @ApiProperty({
    description: 'Additional metadata',
    example: { processingTime: 45.2, model: 'v2.1' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
