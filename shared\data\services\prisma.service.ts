/**
 * Standardized Prisma Service
 * Provides enterprise-grade Prisma client with monitoring, health checks, and performance optimization
 */

import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaClient, Prisma } from '@prisma/client';
import { MetricsService } from '../../logging/services/metrics.service';
import { ServiceLoggerService } from '../../logging/services/service-logger.service';
import {
  PerformanceMetricCategory,
  HealthMetricCategory,
  HealthStatus,
} from '../../logging/interfaces/metrics.interface';

/**
 * Standardized Prisma Service with enterprise features
 */
@Injectable()
export class StandardizedPrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(StandardizedPrismaService.name);
  private readonly serviceName: string;
  private connectionRetries = 0;
  private readonly maxRetries = 5;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor(
    private readonly configService: ConfigService,
    private readonly metricsService: MetricsService,
    private readonly serviceLogger: ServiceLoggerService,
  ) {
    super({
      datasources: {
        db: {
          url: configService.get<string>('DATABASE_URL'),
        },
      },
      log: [
        { level: 'query', emit: 'event' },
        { level: 'error', emit: 'event' },
        { level: 'info', emit: 'event' },
        { level: 'warn', emit: 'event' },
      ],
      errorFormat: 'pretty',
    });

    this.serviceName = this.configService.get<string>('SERVICE_NAME') || 'unknown-service';
    this.setupEventListeners();
  }

  /**
   * Module initialization
   */
  async onModuleInit() {
    await this.connectWithRetry();
    this.setupHealthMonitoring();
    this.logger.log(`Prisma service initialized for ${this.serviceName}`);
  }

  /**
   * Module destruction
   */
  async onModuleDestroy() {
    await this.gracefulDisconnect();
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    this.logger.log(`Prisma service destroyed for ${this.serviceName}`);
  }

  /**
   * Connect with retry logic and exponential backoff
   */
  private async connectWithRetry(): Promise<void> {
    try {
      await this.$connect();
      this.logger.log(`✅ Connected to PostgreSQL database (${this.serviceName})`);
      this.connectionRetries = 0;
      
      // Record successful connection
      this.metricsService.gauge('database_connection_status', 1, {
        service: this.serviceName,
        database: 'postgresql',
        environment: process.env.NODE_ENV || 'development',
        version: process.env.SERVICE_VERSION || '1.0.0',
      } as any);
      
      this.serviceLogger.logOperationComplete('database_connection', 0, true);
    } catch (error) {
      this.connectionRetries++;
      this.logger.error(`❌ Failed to connect to database (attempt ${this.connectionRetries}/${this.maxRetries}):`, error);
      
      // Record failed connection
      this.metricsService.gauge('database_connection_status', 0, {
        service: this.serviceName,
        database: 'postgresql',
        environment: process.env.NODE_ENV || 'development',
        version: process.env.SERVICE_VERSION || '1.0.0',
      } as any);
      
      this.serviceLogger.logOperationComplete('database_connection', 0, false, {
        error: error.message,
        attempt: this.connectionRetries,
      });

      if (this.connectionRetries < this.maxRetries) {
        const delay = Math.pow(2, this.connectionRetries) * 1000; // Exponential backoff
        this.logger.log(`Retrying connection in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.connectWithRetry();
      } else {
        throw error;
      }
    }
  }

  /**
   * Graceful disconnection
   */
  private async gracefulDisconnect(): Promise<void> {
    try {
      await this.$disconnect();
      this.logger.log('🔌 Disconnected from PostgreSQL database');
      
      this.metricsService.gauge('database_connection_status', 0, {
        service: this.serviceName,
        database: 'postgresql',
        environment: process.env.NODE_ENV || 'development',
        version: process.env.SERVICE_VERSION || '1.0.0',
      } as any);
    } catch (error) {
      this.logger.error('Error during database disconnection:', error);
    }
  }

  /**
   * Setup event listeners for monitoring
   */
  private setupEventListeners(): void {
    // Query logging and metrics
    this.$on('query', (e) => {
      const duration = e.duration;
      const operation = this.extractOperationType(e.query);
      
      // Log slow queries
      if (duration > 1000) {
        this.serviceLogger.warn(
          `Slow query detected: ${e.query.substring(0, 100)}... (${duration}ms)`,
          {
            operation: 'database_query',
            performance: {
              startTime: Date.now() - duration,
              endTime: Date.now(),
              duration,
              dbQueryCount: 1,
              dbQueryTime: duration,
            },
            metadata: {
              query: e.query.substring(0, 500), // Truncate long queries
              params: e.params,
              target: e.target,
              slow: true,
              operation,
            },
          }
        );
      }

      // Record query metrics
      this.metricsService.recordPerformanceMetric({
        name: 'database_query_duration',
        value: duration,
        type: 'histogram' as any,
        category: PerformanceMetricCategory.DATABASE,
        unit: 'ms',
        timestamp: new Date(),
        tags: {
          service: this.serviceName,
          operation,
          target: e.target,
          environment: process.env.NODE_ENV || 'development',
          version: process.env.SERVICE_VERSION || '1.0.0',
        } as any,
        threshold: {
          warning: 1000,
          critical: 5000,
        },
      });

      this.metricsService.increment('database_queries_total', {
        service: this.serviceName,
        operation,
        target: e.target,
        environment: process.env.NODE_ENV || 'development',
        version: process.env.SERVICE_VERSION || '1.0.0',
      } as any);
    });

    // Error logging
    this.$on('error', (e) => {
      this.serviceLogger.error(
        `Database error: ${e.message}`,
        e,
        {
          operation: 'database_operation',
          error: {
            name: 'DatabaseError',
            message: e.message,
            code: 'DATABASE_ERROR',
          },
          metadata: {
            target: e.target,
            timestamp: new Date().toISOString(),
          },
        }
      );

      this.metricsService.increment('database_errors_total', {
        service: this.serviceName,
        error_type: 'database_error',
        target: e.target,
        environment: process.env.NODE_ENV || 'development',
        version: process.env.SERVICE_VERSION || '1.0.0',
      } as any);
    });

    // Info and warn logging
    this.$on('info', (e) => {
      this.serviceLogger.info(`Database info: ${e.message}`, {
        metadata: { target: e.target },
      });
    });

    this.$on('warn', (e) => {
      this.serviceLogger.getStructuredLogger().warn(`Database warning: ${e.message}`, {
        metadata: { target: e.target },
      });
    });
  }

  /**
   * Extract operation type from SQL query
   */
  private extractOperationType(query: string): string {
    const normalizedQuery = query.trim().toLowerCase();
    if (normalizedQuery.startsWith('select')) return 'select';
    if (normalizedQuery.startsWith('insert')) return 'insert';
    if (normalizedQuery.startsWith('update')) return 'update';
    if (normalizedQuery.startsWith('delete')) return 'delete';
    if (normalizedQuery.startsWith('create')) return 'create';
    if (normalizedQuery.startsWith('drop')) return 'drop';
    if (normalizedQuery.startsWith('alter')) return 'alter';
    if (normalizedQuery.startsWith('begin')) return 'transaction';
    if (normalizedQuery.startsWith('commit')) return 'transaction';
    if (normalizedQuery.startsWith('rollback')) return 'transaction';
    return 'other';
  }

  /**
   * Setup periodic health monitoring
   */
  private setupHealthMonitoring(): void {
    const healthCheckInterval = this.configService.get<number>('DATABASE_HEALTH_CHECK_INTERVAL') || 30000;
    
    this.healthCheckInterval = setInterval(async () => {
      try {
        const startTime = Date.now();
        await this.$queryRaw`SELECT 1 as health_check`;
        const responseTime = Date.now() - startTime;

        // Record health metrics
        this.metricsService.recordHealthMetric({
          name: 'database_health_check',
          value: responseTime,
          type: 'gauge' as any,
          category: HealthMetricCategory.DATABASE_HEALTH,
          status: HealthStatus.HEALTHY,
          timestamp: new Date(),
          tags: {
            service: this.serviceName,
            database: 'postgresql',
            environment: process.env.NODE_ENV || 'development',
            version: process.env.SERVICE_VERSION || '1.0.0',
          } as any,
          details: { responseTime },
        });

        this.metricsService.gauge('database_health_status', 1, {
          service: this.serviceName,
          database: 'postgresql',
          environment: process.env.NODE_ENV || 'development',
          version: process.env.SERVICE_VERSION || '1.0.0',
        } as any);
      } catch (error) {
        this.metricsService.recordHealthMetric({
          name: 'database_health_check',
          value: -1,
          type: 'gauge' as any,
          category: HealthMetricCategory.DATABASE_HEALTH,
          status: HealthStatus.UNHEALTHY,
          timestamp: new Date(),
          tags: {
            service: this.serviceName,
            database: 'postgresql',
            environment: process.env.NODE_ENV || 'development',
            version: process.env.SERVICE_VERSION || '1.0.0',
          } as any,
          details: { error: error.message },
        });

        this.metricsService.gauge('database_health_status', 0, {
          service: this.serviceName,
          database: 'postgresql',
          environment: process.env.NODE_ENV || 'development',
          version: process.env.SERVICE_VERSION || '1.0.0',
        } as any);

        this.logger.error('Database health check failed:', error);
      }
    }, healthCheckInterval);
  }

  /**
   * Enhanced query method with monitoring
   */
  async queryWithMetrics<T>(query: string, params?: any[]): Promise<T[]> {
    const startTime = Date.now();
    const operation = this.extractOperationType(query);
    
    try {
      const result = await (this as any).$queryRawUnsafe(query, ...params || []);
      const duration = Date.now() - startTime;

      this.serviceLogger.logDatabaseOperation('raw_query', 'custom', duration, true, {
        operation,
        query: query.substring(0, 100),
        params: params?.length || 0,
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.serviceLogger.logDatabaseOperation('raw_query', 'custom', duration, false, {
        operation,
        query: query.substring(0, 100),
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Enhanced transaction method with monitoring
   */
  async transactionWithMetrics<T>(
    fn: (prisma: Prisma.TransactionClient) => Promise<T>,
    options?: {
      maxWait?: number;
      timeout?: number;
      isolationLevel?: any;
    }
  ): Promise<T> {
    const startTime = Date.now();
    const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      this.serviceLogger.debug(`Transaction started: ${transactionId}`, {
        operation: 'database_transaction',
        metadata: {
          transactionId,
          options,
          phase: 'start',
        },
      });

      const result = await this.$transaction(fn, options);
      const duration = Date.now() - startTime;

      this.serviceLogger.logDatabaseOperation('transaction', 'multiple', duration, true, {
        transactionId,
        options,
      });

      this.metricsService.increment('database_transactions_total', {
        service: this.serviceName,
        status: 'success',
        environment: process.env.NODE_ENV || 'development',
        version: process.env.SERVICE_VERSION || '1.0.0',
      } as any);

      this.serviceLogger.info(`Transaction completed: ${transactionId} (${duration}ms)`, {
        operation: 'database_transaction',
        performance: {
          startTime,
          endTime: Date.now(),
          duration,
        },
        metadata: {
          transactionId,
          phase: 'complete',
          success: true,
        },
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.serviceLogger.logDatabaseOperation('transaction', 'multiple', duration, false, {
        transactionId,
        error: error.message,
      });

      this.metricsService.increment('database_transactions_total', {
        service: this.serviceName,
        status: 'error',
        environment: process.env.NODE_ENV || 'development',
        version: process.env.SERVICE_VERSION || '1.0.0',
      } as any);

      this.serviceLogger.error(`Transaction failed: ${transactionId} (${duration}ms)`, error, {
        operation: 'database_transaction',
        performance: {
          startTime,
          endTime: Date.now(),
          duration,
        },
        metadata: {
          transactionId,
          phase: 'error',
          success: false,
        },
      });

      throw error;
    }
  }

  /**
   * Comprehensive health check
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy' | 'degraded';
    responseTime: number;
    details: any;
  }> {
    try {
      const startTime = Date.now();
      
      // Basic connectivity test
      await this.$queryRaw`SELECT 1 as health_check`;
      const basicResponseTime = Date.now() - startTime;

      // Connection pool status
      const poolStatus = await this.getConnectionPoolStatus();
      
      // Database version and info
      const dbInfo = await this.getDatabaseInfo();

      const responseTime = Date.now() - startTime;
      const status = this.determineHealthStatus(basicResponseTime, poolStatus);

      return {
        status,
        responseTime,
        details: {
          database: 'postgresql',
          service: this.serviceName,
          basicResponseTime,
          connectionPool: poolStatus,
          databaseInfo: dbInfo,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: -1,
        details: {
          database: 'postgresql',
          service: this.serviceName,
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Get connection pool status
   */
  private async getConnectionPoolStatus(): Promise<any> {
    try {
      // This is a simplified version - in a real implementation,
      // you would query actual connection pool metrics
      return {
        active: 'unknown',
        idle: 'unknown',
        total: 'unknown',
        status: 'available',
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
      };
    }
  }

  /**
   * Get database information
   */
  private async getDatabaseInfo(): Promise<any> {
    try {
      const result = await this.$queryRaw`SELECT version() as version`;
      return {
        version: result[0]?.version || 'unknown',
        type: 'postgresql',
      };
    } catch (error) {
      return {
        version: 'unknown',
        type: 'postgresql',
        error: error.message,
      };
    }
  }

  /**
   * Determine health status based on metrics
   */
  private determineHealthStatus(responseTime: number, poolStatus: any): 'healthy' | 'unhealthy' | 'degraded' {
    if (responseTime > 5000) return 'unhealthy';
    if (responseTime > 1000 || poolStatus.status === 'error') return 'degraded';
    return 'healthy';
  }

  /**
   * Get service name
   */
  getServiceName(): string {
    return this.serviceName;
  }

  /**
   * Get connection status
   */
  isConnected(): boolean {
    // This is a simplified check - in a real implementation,
    // you would check the actual connection status
    return this.connectionRetries === 0;
  }
}
