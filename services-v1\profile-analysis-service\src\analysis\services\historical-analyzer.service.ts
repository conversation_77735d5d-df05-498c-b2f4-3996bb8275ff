import { Injectable, Logger } from '@nestjs/common';

export interface HistoricalAnalysis {
  growthTrend: 'exponential' | 'linear' | 'plateau' | 'declining';
  followerGrowthRate: number; // followers per day
  engagementTrend: 'increasing' | 'stable' | 'decreasing';
  activityPattern: 'consistent' | 'sporadic' | 'seasonal' | 'declining';
  peakPerformancePeriods: PeakPeriod[];
  contentEvolution: ContentEvolution;
  milestones: Milestone[];
  seasonalPatterns: SeasonalPattern[];
  predictiveMetrics: PredictiveMetrics;
  benchmarkComparison: BenchmarkComparison;
  riskFactors: string[];
  opportunities: string[];
}

export interface PeakPeriod {
  startDate: string;
  endDate: string;
  metric: 'followers' | 'engagement' | 'reach';
  peakValue: number;
  growthRate: number;
  triggerEvents: string[];
}

export interface ContentEvolution {
  topicShifts: TopicShift[];
  sentimentEvolution: SentimentEvolution;
  engagementEvolution: EngagementEvolution;
  formatPreferences: FormatPreference[];
}

export interface TopicShift {
  period: string;
  dominantTopics: string[];
  topicDistribution: { [topic: string]: number };
  emergingTopics: string[];
  decliningTopics: string[];
}

export interface SentimentEvolution {
  periods: SentimentPeriod[];
  overallTrend: 'improving' | 'stable' | 'declining';
  volatility: number;
}

export interface SentimentPeriod {
  period: string;
  averageSentiment: number;
  sentimentRange: { min: number; max: number };
  dominantEmotion: string;
}

export interface EngagementEvolution {
  periods: EngagementPeriod[];
  overallTrend: 'improving' | 'stable' | 'declining';
  bestPerformingContentTypes: string[];
}

export interface EngagementPeriod {
  period: string;
  averageEngagement: number;
  engagementRate: number;
  topPerformingTweets: string[];
}

export interface FormatPreference {
  format: 'text' | 'image' | 'video' | 'link' | 'thread';
  usage: number;
  engagement: number;
  trend: 'increasing' | 'stable' | 'decreasing';
}

export interface Milestone {
  date: string;
  type: 'follower_milestone' | 'viral_content' | 'verification' | 'engagement_spike';
  description: string;
  impact: number;
  metrics: { [key: string]: number };
}

export interface SeasonalPattern {
  pattern: 'weekly' | 'monthly' | 'yearly';
  description: string;
  peakTimes: string[];
  lowTimes: string[];
  strength: number; // 0-100
}

export interface PredictiveMetrics {
  projectedFollowerGrowth: ProjectedGrowth;
  projectedEngagement: ProjectedGrowth;
  riskOfDecline: number;
  growthPotential: number;
  optimalPostingTimes: string[];
  contentRecommendations: string[];
}

export interface ProjectedGrowth {
  nextMonth: number;
  nextQuarter: number;
  nextYear: number;
  confidence: number;
}

export interface BenchmarkComparison {
  percentileRank: number;
  similarProfiles: string[];
  performanceGaps: { [metric: string]: number };
  competitiveAdvantages: string[];
}

export interface HistoricalDataPoint {
  date: string;
  followers: number;
  following: number;
  tweets: number;
  engagement?: number;
  sentiment?: number;
  activity?: number;
}

@Injectable()
export class HistoricalAnalyzerService {
  private readonly logger = new Logger(HistoricalAnalyzerService.name);

  constructor() {}

  /**
   * Analyze historical data and trends
   */
  async analyzeHistoricalData(
    currentData: any,
    historicalData: HistoricalDataPoint[],
    tweets: any[]
  ): Promise<HistoricalAnalysis> {
    try {
      // Analyze growth trends
      const growthTrend = this.analyzeGrowthTrend(historicalData);
      const followerGrowthRate = this.calculateFollowerGrowthRate(historicalData);

      // Analyze engagement trends
      const engagementTrend = this.analyzeEngagementTrend(historicalData, tweets);

      // Analyze activity patterns
      const activityPattern = this.analyzeActivityPattern(historicalData, tweets);

      // Identify peak performance periods
      const peakPerformancePeriods = this.identifyPeakPeriods(historicalData);

      // Analyze content evolution
      const contentEvolution = this.analyzeContentEvolution(tweets);

      // Identify milestones
      const milestones = this.identifyMilestones(historicalData, currentData);

      // Detect seasonal patterns
      const seasonalPatterns = this.detectSeasonalPatterns(historicalData, tweets);

      // Generate predictive metrics
      const predictiveMetrics = this.generatePredictiveMetrics(historicalData, tweets);

      // Benchmark comparison
      const benchmarkComparison = this.performBenchmarkComparison(currentData, historicalData);

      // Identify risks and opportunities
      const riskFactors = this.identifyRiskFactors(historicalData, tweets);
      const opportunities = this.identifyOpportunities(historicalData, tweets);

      const analysis: HistoricalAnalysis = {
        growthTrend,
        followerGrowthRate,
        engagementTrend,
        activityPattern,
        peakPerformancePeriods,
        contentEvolution,
        milestones,
        seasonalPatterns,
        predictiveMetrics,
        benchmarkComparison,
        riskFactors,
        opportunities,
      };

      return analysis;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Analyze growth trend from historical data
   */
  private analyzeGrowthTrend(historicalData: HistoricalDataPoint[]): 'exponential' | 'linear' | 'plateau' | 'declining' {
    if (historicalData.length < 3) return 'linear';

    // Sort by date
    const sortedData = historicalData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    // Calculate growth rates between consecutive points
    const growthRates: number[] = [];
    for (let i = 1; i < sortedData.length; i++) {
      const prev = sortedData[i - 1];
      const curr = sortedData[i];
      const daysDiff = (new Date(curr.date).getTime() - new Date(prev.date).getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysDiff > 0 && prev.followers > 0) {
        const growthRate = ((curr.followers - prev.followers) / prev.followers) * (30 / daysDiff); // Monthly growth rate
        growthRates.push(growthRate);
      }
    }

    if (growthRates.length === 0) return 'linear';

    // Analyze trend
    const avgGrowthRate = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length;
    const recentGrowthRate = growthRates.slice(-3).reduce((sum, rate) => sum + rate, 0) / Math.min(3, growthRates.length);

    // Check if growth is accelerating (exponential)
    if (recentGrowthRate > avgGrowthRate * 1.5 && avgGrowthRate > 0.05) {
      return 'exponential';
    }

    // Check if growth has plateaued
    if (Math.abs(recentGrowthRate) < 0.01 && avgGrowthRate > 0) {
      return 'plateau';
    }

    // Check if declining
    if (recentGrowthRate < -0.02) {
      return 'declining';
    }

    return 'linear';
  }

  /**
   * Calculate follower growth rate
   */
  private calculateFollowerGrowthRate(historicalData: HistoricalDataPoint[]): number {
    if (historicalData.length < 2) return 0;

    const sortedData = historicalData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    const oldest = sortedData[0];
    const newest = sortedData[sortedData.length - 1];

    const daysDiff = (new Date(newest.date).getTime() - new Date(oldest.date).getTime()) / (1000 * 60 * 60 * 24);
    
    if (daysDiff <= 0) return 0;

    return (newest.followers - oldest.followers) / daysDiff;
  }

  /**
   * Analyze engagement trend
   */
  private analyzeEngagementTrend(historicalData: HistoricalDataPoint[], tweets: any[]): 'increasing' | 'stable' | 'decreasing' {
    // If we have engagement data in historical data, use it
    const engagementData = historicalData.filter(d => d.engagement !== undefined);
    
    if (engagementData.length >= 3) {
      const sortedData = engagementData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
      const firstHalf = sortedData.slice(0, Math.floor(sortedData.length / 2));
      const secondHalf = sortedData.slice(Math.floor(sortedData.length / 2));

      const firstHalfAvg = firstHalf.reduce((sum, d) => sum + d.engagement!, 0) / firstHalf.length;
      const secondHalfAvg = secondHalf.reduce((sum, d) => sum + d.engagement!, 0) / secondHalf.length;

      const changePercent = ((secondHalfAvg - firstHalfAvg) / Math.max(1, firstHalfAvg)) * 100;

      if (changePercent > 15) return 'increasing';
      if (changePercent < -15) return 'decreasing';
      return 'stable';
    }

    // Otherwise, analyze from tweets if available
    if (tweets.length >= 10) {
      const sortedTweets = tweets.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      const midpoint = Math.floor(sortedTweets.length / 2);
      const firstHalf = sortedTweets.slice(0, midpoint);
      const secondHalf = sortedTweets.slice(midpoint);

      const firstHalfEngagement = this.calculateAverageEngagement(firstHalf);
      const secondHalfEngagement = this.calculateAverageEngagement(secondHalf);

      const changePercent = ((secondHalfEngagement - firstHalfEngagement) / Math.max(1, firstHalfEngagement)) * 100;

      if (changePercent > 20) return 'increasing';
      if (changePercent < -20) return 'decreasing';
    }

    return 'stable';
  }

  /**
   * Analyze activity pattern
   */
  private analyzeActivityPattern(historicalData: HistoricalDataPoint[], tweets: any[]): 'consistent' | 'sporadic' | 'seasonal' | 'declining' {
    // Analyze tweet frequency over time
    if (tweets.length >= 20) {
      const tweetsByMonth = this.groupTweetsByMonth(tweets);
      const monthlyTweetCounts = Object.values(tweetsByMonth).map(monthTweets => monthTweets.length);
      
      // Calculate coefficient of variation
      const mean = monthlyTweetCounts.reduce((sum, count) => sum + count, 0) / monthlyTweetCounts.length;
      const variance = monthlyTweetCounts.reduce((sum, count) => sum + Math.pow(count - mean, 2), 0) / monthlyTweetCounts.length;
      const standardDeviation = Math.sqrt(variance);
      const coefficientOfVariation = mean > 0 ? standardDeviation / mean : 1;

      // Check for declining activity
      const recentMonths = monthlyTweetCounts.slice(-3);
      const earlierMonths = monthlyTweetCounts.slice(0, -3);
      
      if (earlierMonths.length > 0 && recentMonths.length > 0) {
        const recentAvg = recentMonths.reduce((sum, count) => sum + count, 0) / recentMonths.length;
        const earlierAvg = earlierMonths.reduce((sum, count) => sum + count, 0) / earlierMonths.length;
        
        if (recentAvg < earlierAvg * 0.5) {
          return 'declining';
        }
      }

      // Check for seasonal patterns
      if (this.hasSeasonalPattern(monthlyTweetCounts)) {
        return 'seasonal';
      }

      // Check consistency
      if (coefficientOfVariation < 0.3) {
        return 'consistent';
      } else {
        return 'sporadic';
      }
    }

    return 'consistent';
  }

  /**
   * Identify peak performance periods
   */
  private identifyPeakPeriods(historicalData: HistoricalDataPoint[]): PeakPeriod[] {
    const peaks: PeakPeriod[] = [];
    
    if (historicalData.length < 5) return peaks;

    const sortedData = historicalData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Find follower growth peaks
    const followerPeaks = this.findMetricPeaks(sortedData, 'followers');
    peaks.push(...followerPeaks);

    // Find engagement peaks if data available
    const engagementData = sortedData.filter(d => d.engagement !== undefined);
    if (engagementData.length >= 5) {
      const engagementPeaks = this.findMetricPeaks(engagementData, 'engagement');
      peaks.push(...engagementPeaks);
    }

    return peaks.slice(0, 5); // Return top 5 peaks
  }

  /**
   * Find peaks for a specific metric
   */
  private findMetricPeaks(data: HistoricalDataPoint[], metric: 'followers' | 'engagement'): PeakPeriod[] {
    const peaks: PeakPeriod[] = [];
    const windowSize = Math.max(3, Math.floor(data.length / 10));

    for (let i = windowSize; i < data.length - windowSize; i++) {
      const window = data.slice(i - windowSize, i + windowSize + 1);
      const centerValue = metric === 'followers' ? data[i].followers : data[i].engagement || 0;
      const windowMax = Math.max(...window.map(d => metric === 'followers' ? d.followers : d.engagement || 0));

      // If center point is the maximum in the window, it's a peak
      if (centerValue === windowMax && centerValue > 0) {
        const startDate = data[Math.max(0, i - windowSize)].date;
        const endDate = data[Math.min(data.length - 1, i + windowSize)].date;
        
        // Calculate growth rate during peak period
        const startValue = metric === 'followers' ? data[Math.max(0, i - windowSize)].followers : data[Math.max(0, i - windowSize)].engagement || 0;
        const growthRate = startValue > 0 ? ((centerValue - startValue) / startValue) * 100 : 0;

        peaks.push({
          startDate,
          endDate,
          metric,
          peakValue: centerValue,
          growthRate,
          triggerEvents: [], // Would need additional data to identify trigger events
        });
      }
    }

    return peaks.sort((a, b) => b.peakValue - a.peakValue).slice(0, 3);
  }

  /**
   * Analyze content evolution
   */
  private analyzeContentEvolution(tweets: any[]): ContentEvolution {
    // Group tweets by time periods
    const tweetsByPeriod = this.groupTweetsByQuarter(tweets);
    
    const topicShifts = this.analyzeTopicShifts(tweetsByPeriod);
    const sentimentEvolution = this.analyzeSentimentEvolution(tweetsByPeriod);
    const engagementEvolution = this.analyzeEngagementEvolution(tweetsByPeriod);
    const formatPreferences = this.analyzeFormatPreferences(tweets);

    return {
      topicShifts,
      sentimentEvolution,
      engagementEvolution,
      formatPreferences,
    };
  }

  /**
   * Group tweets by quarter
   */
  private groupTweetsByQuarter(tweets: any[]): { [quarter: string]: any[] } {
    const grouped: { [quarter: string]: any[] } = {};

    tweets.forEach(tweet => {
      const date = new Date(tweet.createdAt);
      const year = date.getFullYear();
      const quarter = Math.floor(date.getMonth() / 3) + 1;
      const key = `${year}-Q${quarter}`;

      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(tweet);
    });

    return grouped;
  }

  /**
   * Group tweets by month
   */
  private groupTweetsByMonth(tweets: any[]): { [month: string]: any[] } {
    const grouped: { [month: string]: any[] } = {};

    tweets.forEach(tweet => {
      const date = new Date(tweet.createdAt);
      const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(tweet);
    });

    return grouped;
  }

  /**
   * Check for seasonal patterns
   */
  private hasSeasonalPattern(monthlyData: number[]): boolean {
    if (monthlyData.length < 12) return false;

    // Simple seasonal pattern detection
    // Check if there's a recurring pattern every 3, 6, or 12 months
    const patterns = [3, 6, 12];
    
    for (const pattern of patterns) {
      if (monthlyData.length >= pattern * 2) {
        let correlation = 0;
        let comparisons = 0;

        for (let i = 0; i < monthlyData.length - pattern; i++) {
          const current = monthlyData[i];
          const seasonal = monthlyData[i + pattern];
          
          if (Math.abs(current - seasonal) < Math.max(current, seasonal) * 0.3) {
            correlation++;
          }
          comparisons++;
        }

        if (correlation / comparisons > 0.6) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Calculate average engagement for tweets
   */
  private calculateAverageEngagement(tweets: any[]): number {
    if (tweets.length === 0) return 0;

    const totalEngagement = tweets.reduce((sum, tweet) => 
      sum + (tweet.likes || 0) + (tweet.retweets || 0) + (tweet.replies || 0), 0
    );

    return totalEngagement / tweets.length;
  }

  /**
   * Analyze topic shifts (simplified implementation)
   */
  private analyzeTopicShifts(tweetsByPeriod: { [period: string]: any[] }): TopicShift[] {
    // This would need more sophisticated NLP for real implementation
    // For now, return a basic structure
    return Object.keys(tweetsByPeriod).map(period => ({
      period,
      dominantTopics: ['technology', 'business'], // Placeholder
      topicDistribution: { technology: 40, business: 30, personal: 30 },
      emergingTopics: ['ai', 'blockchain'],
      decliningTopics: ['legacy_tech'],
    }));
  }

  /**
   * Analyze sentiment evolution (simplified implementation)
   */
  private analyzeSentimentEvolution(tweetsByPeriod: { [period: string]: any[] }): SentimentEvolution {
    const periods = Object.keys(tweetsByPeriod).map(period => ({
      period,
      averageSentiment: 0.6, // Placeholder
      sentimentRange: { min: -0.2, max: 0.8 },
      dominantEmotion: 'positive',
    }));

    return {
      periods,
      overallTrend: 'stable',
      volatility: 0.3,
    };
  }

  /**
   * Analyze engagement evolution (simplified implementation)
   */
  private analyzeEngagementEvolution(tweetsByPeriod: { [period: string]: any[] }): EngagementEvolution {
    const periods = Object.keys(tweetsByPeriod).map(period => {
      const tweets = tweetsByPeriod[period];
      const avgEngagement = this.calculateAverageEngagement(tweets);
      
      return {
        period,
        averageEngagement: avgEngagement,
        engagementRate: avgEngagement * 0.02, // Simplified calculation
        topPerformingTweets: tweets.slice(0, 3).map(t => t.id),
      };
    });

    return {
      periods,
      overallTrend: 'stable',
      bestPerformingContentTypes: ['images', 'threads'],
    };
  }

  /**
   * Analyze format preferences (simplified implementation)
   */
  private analyzeFormatPreferences(tweets: any[]): FormatPreference[] {
    return [
      { format: 'text', usage: 60, engagement: 50, trend: 'stable' },
      { format: 'image', usage: 25, engagement: 70, trend: 'increasing' },
      { format: 'video', usage: 10, engagement: 80, trend: 'increasing' },
      { format: 'link', usage: 5, engagement: 40, trend: 'decreasing' },
    ];
  }

  /**
   * Identify milestones (simplified implementation)
   */
  private identifyMilestones(historicalData: HistoricalDataPoint[], currentData: any): Milestone[] {
    const milestones: Milestone[] = [];

    // Find follower milestones
    const followerMilestones = [1000, 5000, 10000, 50000, 100000, 500000, 1000000];
    
    for (const milestone of followerMilestones) {
      if (currentData.followersCount >= milestone) {
        milestones.push({
          date: new Date().toISOString(), // Would need actual date from historical data
          type: 'follower_milestone',
          description: `Reached ${milestone.toLocaleString()} followers`,
          impact: milestone / 1000,
          metrics: { followers: milestone },
        });
      }
    }

    return milestones.slice(0, 5);
  }

  /**
   * Detect seasonal patterns (simplified implementation)
   */
  private detectSeasonalPatterns(historicalData: HistoricalDataPoint[], tweets: any[]): SeasonalPattern[] {
    return [
      {
        pattern: 'weekly',
        description: 'Higher engagement on weekdays',
        peakTimes: ['Tuesday', 'Wednesday', 'Thursday'],
        lowTimes: ['Saturday', 'Sunday'],
        strength: 65,
      },
      {
        pattern: 'yearly',
        description: 'Increased activity during Q4',
        peakTimes: ['October', 'November', 'December'],
        lowTimes: ['July', 'August'],
        strength: 45,
      },
    ];
  }

  /**
   * Generate predictive metrics (simplified implementation)
   */
  private generatePredictiveMetrics(historicalData: HistoricalDataPoint[], tweets: any[]): PredictiveMetrics {
    const currentGrowthRate = this.calculateFollowerGrowthRate(historicalData);
    
    return {
      projectedFollowerGrowth: {
        nextMonth: currentGrowthRate * 30,
        nextQuarter: currentGrowthRate * 90,
        nextYear: currentGrowthRate * 365,
        confidence: 75,
      },
      projectedEngagement: {
        nextMonth: 1.05,
        nextQuarter: 1.15,
        nextYear: 1.3,
        confidence: 65,
      },
      riskOfDecline: 15,
      growthPotential: 80,
      optimalPostingTimes: ['9:00 AM', '1:00 PM', '7:00 PM'],
      contentRecommendations: ['More visual content', 'Engage with trending topics', 'Increase thread usage'],
    };
  }

  /**
   * Perform benchmark comparison (simplified implementation)
   */
  private performBenchmarkComparison(currentData: any, historicalData: HistoricalDataPoint[]): BenchmarkComparison {
    return {
      percentileRank: 75,
      similarProfiles: ['@similar_user1', '@similar_user2'],
      performanceGaps: {
        engagement_rate: -0.5,
        posting_frequency: 2.3,
        follower_growth: 1.2,
      },
      competitiveAdvantages: ['High engagement quality', 'Consistent posting schedule'],
    };
  }

  /**
   * Identify risk factors (simplified implementation)
   */
  private identifyRiskFactors(historicalData: HistoricalDataPoint[], tweets: any[]): string[] {
    const risks: string[] = [];

    // Check for declining growth
    const growthTrend = this.analyzeGrowthTrend(historicalData);
    if (growthTrend === 'declining') {
      risks.push('Declining follower growth');
    }

    // Check for low engagement
    const avgEngagement = this.calculateAverageEngagement(tweets);
    if (avgEngagement < 10) {
      risks.push('Low engagement rates');
    }

    // Check for infrequent posting
    if (tweets.length < 30) {
      risks.push('Infrequent posting activity');
    }

    return risks;
  }

  /**
   * Identify opportunities (simplified implementation)
   */
  private identifyOpportunities(historicalData: HistoricalDataPoint[], tweets: any[]): string[] {
    const opportunities: string[] = [];

    // Check for growth potential
    const growthTrend = this.analyzeGrowthTrend(historicalData);
    if (growthTrend === 'exponential' || growthTrend === 'linear') {
      opportunities.push('Strong growth momentum');
    }

    // Check for engagement potential
    const avgEngagement = this.calculateAverageEngagement(tweets);
    if (avgEngagement > 50) {
      opportunities.push('High engagement potential');
    }

    // Always include some generic opportunities
    opportunities.push('Expand content variety');
    opportunities.push('Increase posting frequency');
    opportunities.push('Engage with trending topics');

    return opportunities.slice(0, 5);
  }
}
