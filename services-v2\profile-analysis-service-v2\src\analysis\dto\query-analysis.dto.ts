/**
 * Query Analysis DTO - Profile Analysis Service V2
 * 
 * Data Transfer Object for querying analyses with pagination and filtering
 */

import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsInt,
  Min,
  Max,
  IsIn,
  IsUUID,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class QueryAnalysisDto {
  @ApiProperty({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit must not exceed 100' })
  limit?: number;

  @ApiProperty({
    description: 'Filter by user ID',
    example: 'clp123abc456def789',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  userId?: string;

  @ApiProperty({
    description: 'Filter by analysis type',
    example: 'twitter',
    enum: ['twitter', 'instagram', 'linkedin', 'tiktok', 'youtube', 'general'],
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['twitter', 'instagram', 'linkedin', 'tiktok', 'youtube', 'general'])
  analysisType?: string;

  @ApiProperty({
    description: 'Filter by status',
    example: 'completed',
    enum: ['pending', 'processing', 'completed', 'failed'],
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['pending', 'processing', 'completed', 'failed'])
  status?: string;

  @ApiProperty({
    description: 'Field to sort by',
    example: 'createdAt',
    enum: ['createdAt', 'updatedAt', 'completedAt', 'progress', 'confidence'],
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['createdAt', 'updatedAt', 'completedAt', 'progress', 'confidence'])
  sortBy?: string;

  @ApiProperty({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc'],
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc';

  @ApiProperty({
    description: 'Search term for request ID or source URL',
    example: 'twitter.com',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;
}
