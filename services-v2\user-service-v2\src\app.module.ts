/**
 * User Service V2 - App Module
 * 
 * Clean implementation using shared infrastructure
 * Database Per Service Pattern - Connects to user_service_v2 database only
 */

import { Module } from '@nestjs/common';
import { setupBusinessService } from '../../../shared';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    // Shared Infrastructure Setup for Business Service
    // This configures:
    // - Authentication & Authorization
    // - Response Standardization & Interceptors
    // - Structured Logging & Business Events
    // - Database Connection (to user_service_v2 database)
    // - Global Error Handling
    // - Configuration Management
    setupBusinessService('user-service-v2', '2.0.0'),

    // Business Domain Modules
    UsersModule,
    AuthModule,
    
    // Health Check Module
    HealthModule,
  ],
})
export class AppModule {
  constructor() {
    console.log('🏗️ User Service V2 initialized with shared infrastructure');
    console.log('🗄️ Database: user_service_v2 (Database Per Service Pattern)');
    console.log('🔧 Shared Infrastructure: Authentication, Logging, Responses, Config');
  }
}
