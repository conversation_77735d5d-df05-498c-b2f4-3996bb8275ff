import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Response<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
  path: string;
  service: string;
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, Response<T>> {
  private readonly logger = new Logger(ResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    const request = context.switchToHttp().getRequest();
    const path = request.url;
    
    return next.handle().pipe(
      map((data) => {
        const response: Response<T> = {
          success: true,
          data,
          timestamp: new Date().toISOString(),
          path,
          service: 'profile-analysis-service',
        };

        // Add message for specific operations
        if (path.includes('/analyze')) {
          response.message = 'Profile analysis completed successfully';
        } else if (path.includes('/health')) {
          response.message = 'Health check completed';
        }

        this.logger.debug(`Response formatted for ${path}`);
        return response;
      }),
    );
  }
}
