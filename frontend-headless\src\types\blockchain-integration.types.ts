export enum BlockchainNetwork {
  ETHEREUM = 'ethereum',
  POLYGON = 'polygon',
  BSC = 'bsc',
  BASE = 'base',
  ARBITRUM = 'arbitrum',
  OPTIMISM = 'optimism',
  AVALANCHE = 'avalanche',
  FANTOM = 'fantom'
}

export enum WalletType {
  METAMASK = 'metamask',
  WALLET_CONNECT = 'wallet_connect',
  COINBASE_WALLET = 'coinbase_wallet',
  LEDGER = 'ledger',
  TREZOR = 'trezor',
  GNOSIS_SAFE = 'gnosis_safe',
  RAINBOW = 'rainbow',
  TRUST_WALLET = 'trust_wallet'
}

export enum TransactionStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REPLACED = 'replaced'
}

export enum ContractType {
  ERC721 = 'erc721',
  ERC1155 = 'erc1155',
  MARKETPLACE = 'marketplace',
  EVOLUTION = 'evolution',
  UTILITY = 'utility',
  GOVERNANCE = 'governance',
  STAKING = 'staking',
  BRIDGE = 'bridge'
}

export enum GasStrategy {
  SLOW = 'slow',
  STANDARD = 'standard',
  FAST = 'fast',
  INSTANT = 'instant',
  CUSTOM = 'custom'
}

export interface NetworkConfig {
  id: string
  name: string
  network: BlockchainNetwork
  
  // Network Details
  chainId: number
  rpcUrls: string[]
  blockExplorerUrls: string[]
  nativeCurrency: {
    name: string
    symbol: string
    decimals: number
  }
  
  // Contract Addresses
  contracts: Record<ContractType, string>
  
  // Configuration
  isTestnet: boolean
  isActive: boolean
  priority: number
  
  // Gas Configuration
  gasLimits: Record<string, number>
  gasPriceMultiplier: number
  maxGasPrice: string
  
  // Features
  supportedFeatures: string[]
  bridgeSupport: BlockchainNetwork[]
  
  // Metadata
  iconUrl: string
  description: string
  websiteUrl: string
}

export interface WalletConnection {
  id: string
  type: WalletType
  address: string
  
  // Connection Details
  isConnected: boolean
  chainId: number
  network: BlockchainNetwork
  
  // Wallet Information
  walletName: string
  walletVersion?: string
  isHardwareWallet: boolean
  
  // Balances
  nativeBalance: string
  tokenBalances: TokenBalance[]
  
  // Permissions
  permissions: WalletPermission[]
  
  // Session
  connectedAt: string
  lastActivity: string
  sessionExpiry?: string
  
  // Metadata
  metadata: Record<string, any>
}

export interface TokenBalance {
  contractAddress: string
  symbol: string
  name: string
  decimals: number
  balance: string
  balanceFormatted: string
  usdValue?: number
  logoUrl?: string
}

export interface WalletPermission {
  permission: 'read' | 'sign' | 'send' | 'contract_interaction'
  granted: boolean
  grantedAt: string
  expiresAt?: string
}

export interface SmartContract {
  id: string
  name: string
  type: ContractType
  
  // Contract Details
  address: string
  network: BlockchainNetwork
  chainId: number
  
  // Deployment Information
  deployedAt: string
  deployedBy: string
  deploymentTxHash: string
  blockNumber: number
  
  // Contract Code
  bytecode: string
  abi: any[]
  sourceCode?: string
  
  // Verification
  isVerified: boolean
  verificationSource?: string
  verifiedAt?: string
  
  // Version Information
  version: string
  previousVersions: ContractVersion[]
  
  // Upgrade Information
  isUpgradeable: boolean
  proxyType?: 'transparent' | 'uups' | 'beacon'
  implementationAddress?: string
  adminAddress?: string
  
  // Security
  securityAudits: SecurityAudit[]
  riskLevel: 'low' | 'medium' | 'high'
  
  // Usage Statistics
  totalTransactions: number
  uniqueUsers: number
  totalValueLocked?: string
  
  // Metadata
  description: string
  documentation?: string
  tags: string[]
  isActive: boolean
}

export interface ContractVersion {
  version: string
  address: string
  deployedAt: string
  changes: string[]
  migrationRequired: boolean
}

export interface SecurityAudit {
  auditor: string
  auditDate: string
  reportUrl: string
  findings: AuditFinding[]
  overallRating: 'excellent' | 'good' | 'fair' | 'poor'
}

export interface AuditFinding {
  severity: 'critical' | 'high' | 'medium' | 'low' | 'informational'
  title: string
  description: string
  recommendation: string
  status: 'open' | 'acknowledged' | 'fixed'
}

export interface Transaction {
  id: string
  hash: string
  
  // Transaction Details
  from: string
  to: string
  value: string
  data?: string
  
  // Network Information
  network: BlockchainNetwork
  chainId: number
  blockNumber?: number
  blockHash?: string
  transactionIndex?: number
  
  // Gas Information
  gasLimit: string
  gasUsed?: string
  gasPrice: string
  maxFeePerGas?: string
  maxPriorityFeePerGas?: string
  
  // Status
  status: TransactionStatus
  confirmations: number
  requiredConfirmations: number
  
  // Timing
  submittedAt: string
  confirmedAt?: string
  
  // Transaction Type
  type: TransactionType
  contractAddress?: string
  methodName?: string
  methodParameters?: any[]
  
  // Error Information
  error?: string
  revertReason?: string
  
  // Metadata
  description: string
  tags: string[]
  internalId?: string
}

export enum TransactionType {
  TRANSFER = 'transfer',
  CONTRACT_CALL = 'contract_call',
  CONTRACT_DEPLOYMENT = 'contract_deployment',
  NFT_MINT = 'nft_mint',
  NFT_TRANSFER = 'nft_transfer',
  NFT_BURN = 'nft_burn',
  EVOLUTION = 'evolution',
  MARKETPLACE = 'marketplace',
  BRIDGE = 'bridge',
  STAKING = 'staking',
  GOVERNANCE = 'governance'
}

export interface GasEstimate {
  network: BlockchainNetwork
  
  // Gas Estimates by Strategy
  estimates: Record<GasStrategy, GasEstimateDetails>
  
  // Current Network Conditions
  baseFee?: string
  priorityFee?: string
  networkCongestion: 'low' | 'medium' | 'high'
  
  // Recommendations
  recommendedStrategy: GasStrategy
  estimatedConfirmationTime: Record<GasStrategy, number>
  
  // Historical Data
  averageGasPrice24h: string
  gasUsageHistory: GasUsagePoint[]
}

export interface GasEstimateDetails {
  gasLimit: string
  gasPrice?: string
  maxFeePerGas?: string
  maxPriorityFeePerGas?: string
  totalCost: string
  totalCostUSD?: number
  estimatedConfirmationTime: number
}

export interface GasUsagePoint {
  timestamp: string
  gasPrice: string
  blockNumber: number
  utilization: number
}

export interface CrossChainBridge {
  id: string
  name: string
  
  // Bridge Configuration
  sourceNetwork: BlockchainNetwork
  targetNetwork: BlockchainNetwork
  supportedTokens: BridgeToken[]
  
  // Bridge Contracts
  sourceContract: string
  targetContract: string
  
  // Fees and Limits
  bridgeFee: string
  minAmount: string
  maxAmount: string
  
  // Timing
  estimatedTime: number
  confirmationsRequired: number
  
  // Security
  securityLevel: 'high' | 'medium' | 'low'
  auditReports: string[]
  
  // Status
  isActive: boolean
  maintenanceMode: boolean
  
  // Statistics
  totalVolume: string
  totalTransactions: number
  successRate: number
}

export interface BridgeToken {
  sourceAddress: string
  targetAddress: string
  symbol: string
  name: string
  decimals: number
  isNative: boolean
}

export interface BridgeTransaction {
  id: string
  
  // Bridge Details
  bridgeId: string
  sourceNetwork: BlockchainNetwork
  targetNetwork: BlockchainNetwork
  
  // Transaction Information
  sourceHash: string
  targetHash?: string
  amount: string
  tokenAddress: string
  
  // Addresses
  fromAddress: string
  toAddress: string
  
  // Status
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  
  // Timing
  initiatedAt: string
  completedAt?: string
  estimatedCompletion: string
  
  // Fees
  bridgeFee: string
  gasFees: {
    source: string
    target?: string
  }
  
  // Error Handling
  error?: string
  retryCount: number
  maxRetries: number
}

export interface NFTContract {
  id: string
  address: string
  network: BlockchainNetwork
  
  // Contract Information
  name: string
  symbol: string
  type: 'ERC721' | 'ERC1155'
  
  // Collection Details
  totalSupply: number
  maxSupply?: number
  mintedCount: number
  burnedCount: number
  
  // Owner Information
  owner: string
  creator: string
  
  // Metadata
  baseTokenURI: string
  contractURI?: string
  
  // Features
  isMintable: boolean
  isBurnable: boolean
  isPausable: boolean
  isUpgradeable: boolean
  hasRoyalties: boolean
  
  // Royalty Information
  royaltyRecipient?: string
  royaltyPercentage?: number
  
  // Evolution Features
  isEvolvable: boolean
  evolutionContract?: string
  
  // Marketplace Integration
  isMarketplaceApproved: boolean
  approvedMarketplaces: string[]
  
  // Statistics
  floorPrice?: string
  totalVolume?: string
  uniqueHolders: number
  
  // Metadata
  description?: string
  imageUrl?: string
  externalUrl?: string
  createdAt: string
}

export interface MultiChainNFT {
  id: string
  originalNetwork: BlockchainNetwork
  
  // Cross-Chain Instances
  instances: NFTInstance[]
  
  // Synchronization
  syncStatus: 'synced' | 'syncing' | 'out_of_sync' | 'failed'
  lastSyncAt: string
  
  // Metadata Consistency
  metadataHash: string
  metadataConsistent: boolean
  
  // Bridge History
  bridgeHistory: BridgeTransaction[]
  
  // Ownership Tracking
  currentOwner: string
  ownershipHistory: OwnershipRecord[]
}

export interface NFTInstance {
  network: BlockchainNetwork
  contractAddress: string
  tokenId: string
  
  // Status
  isActive: boolean
  isBridged: boolean
  
  // Metadata
  tokenURI: string
  metadata: any
  
  // Ownership
  owner: string
  
  // Last Update
  lastUpdated: string
  blockNumber: number
}

export interface OwnershipRecord {
  network: BlockchainNetwork
  owner: string
  acquiredAt: string
  transactionHash: string
  transferType: 'mint' | 'transfer' | 'bridge'
}

export interface BlockchainAnalytics {
  network: BlockchainNetwork
  timeframe: string
  
  // Transaction Metrics
  totalTransactions: number
  successfulTransactions: number
  failedTransactions: number
  averageGasUsed: string
  
  // Gas Analytics
  averageGasPrice: string
  gasEfficiency: number
  gasSavings: string
  
  // Contract Metrics
  contractInteractions: number
  uniqueContracts: number
  newContracts: number
  
  // NFT Metrics
  nftMints: number
  nftTransfers: number
  nftEvolutions: number
  
  // Bridge Metrics
  bridgeTransactions: number
  bridgeVolume: string
  bridgeSuccessRate: number
  
  // Performance
  averageConfirmationTime: number
  networkUptime: number
  
  // Trends
  transactionTrends: MetricTrend[]
  gasTrends: MetricTrend[]
  activityTrends: MetricTrend[]
}

export interface MetricTrend {
  timestamp: string
  value: number
  change: number
  percentChange: number
}

// API Request/Response Types
export interface ConnectWalletRequest {
  walletType: WalletType
  chainId?: number
  permissions?: string[]
}

export interface SwitchNetworkRequest {
  chainId: number
  network: BlockchainNetwork
}

export interface SendTransactionRequest {
  to: string
  value?: string
  data?: string
  gasLimit?: string
  gasPrice?: string
  maxFeePerGas?: string
  maxPriorityFeePerGas?: string
  nonce?: number
}

export interface DeployContractRequest {
  contractType: ContractType
  network: BlockchainNetwork
  constructorArgs: any[]
  gasStrategy?: GasStrategy
  verifyContract?: boolean
}

export interface BridgeNFTRequest {
  nftContract: string
  tokenId: string
  sourceNetwork: BlockchainNetwork
  targetNetwork: BlockchainNetwork
  bridgeId: string
  recipient?: string
}

export interface EstimateGasRequest {
  network: BlockchainNetwork
  transaction: {
    to: string
    value?: string
    data?: string
  }
  strategy?: GasStrategy
}

export interface BlockchainSearchRequest {
  networks?: BlockchainNetwork[]
  transactionTypes?: TransactionType[]
  contractTypes?: ContractType[]
  addresses?: string[]
  dateRange?: {
    start: string
    end: string
  }
  sortBy?: 'timestamp' | 'gas_used' | 'value' | 'confirmations'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface BlockchainSearchResponse {
  transactions: Transaction[]
  contracts: SmartContract[]
  total: number
  page: number
  limit: number
  hasMore: boolean
  aggregations: {
    byNetwork: Record<BlockchainNetwork, number>
    byType: Record<TransactionType, number>
    byStatus: Record<TransactionStatus, number>
    totalGasUsed: string
    totalValue: string
  }
}
