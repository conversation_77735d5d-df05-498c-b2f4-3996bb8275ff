// Profile Analysis Service V2 Database Schema
// Database Per Service Pattern - This is the PROFILE ANALYSIS SERVICE database only

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Profile Analysis Tables (Profile Analysis Service Domain)
model ProfileAnalysis {
  id       String @id @default(cuid())
  userId   String // Reference to user in User Service (via API calls)
  
  // Analysis Request Details
  requestId     String  @unique
  analysisType  String  // twitter, instagram, linkedin, etc.
  sourceUrl     String? // Profile URL being analyzed
  
  // Analysis Status
  status        String @default("pending") // pending, processing, completed, failed
  progress      Int    @default(0) // 0-100
  
  // Analysis Results
  results       Json?  // Flexible JSON structure for analysis results
  confidence    Float? // Confidence score 0.0-1.0
  
  // Metadata
  metadata      Json?  // Additional analysis metadata
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  completedAt   DateTime?
  
  // Relationships within Profile Analysis Service
  metrics       ProfileMetric[]
  insights      ProfileInsight[]
  recommendations ProfileRecommendation[]
  
  @@map("profile_analyses")
}

model ProfileMetric {
  id        String @id @default(cuid())
  analysisId String
  
  // Metric Details
  category  String // engagement, reach, sentiment, etc.
  name      String // followers_count, engagement_rate, etc.
  value     Float
  unit      String? // percentage, count, score, etc.
  
  // Metric Context
  source    String? // twitter_api, instagram_scraper, etc.
  period    String? // daily, weekly, monthly, all_time
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  analysis ProfileAnalysis @relation(fields: [analysisId], references: [id], onDelete: Cascade)
  
  @@map("profile_metrics")
}

model ProfileInsight {
  id        String @id @default(cuid())
  analysisId String
  
  // Insight Details
  type      String // strength, weakness, opportunity, trend
  category  String // content, audience, engagement, growth
  title     String
  description String
  
  // Insight Scoring
  importance String @default("medium") // low, medium, high, critical
  confidence Float  @default(0.5) // 0.0-1.0
  
  // Supporting Data
  evidence  Json?  // Supporting metrics and data
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  analysis ProfileAnalysis @relation(fields: [analysisId], references: [id], onDelete: Cascade)
  
  @@map("profile_insights")
}

model ProfileRecommendation {
  id        String @id @default(cuid())
  analysisId String
  
  // Recommendation Details
  type      String // content, posting_schedule, audience_targeting, etc.
  category  String // growth, engagement, monetization, branding
  title     String
  description String
  
  // Implementation Details
  actionItems Json?  // Specific steps to implement
  priority    String @default("medium") // low, medium, high, urgent
  effort      String @default("medium") // low, medium, high
  impact      String @default("medium") // low, medium, high
  
  // Timeline
  timeframe   String? // immediate, short_term, long_term
  
  // Tracking
  isImplemented Boolean @default(false)
  implementedAt DateTime?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  analysis ProfileAnalysis @relation(fields: [analysisId], references: [id], onDelete: Cascade)
  
  @@map("profile_recommendations")
}

model AnalysisTemplate {
  id          String @id @default(cuid())
  
  // Template Details
  name        String
  description String?
  type        String // twitter, instagram, linkedin, general
  version     String @default("1.0")
  
  // Template Configuration
  config      Json   // Analysis configuration and parameters
  metrics     Json   // Metrics to collect
  insights    Json   // Insight generation rules
  recommendations Json // Recommendation generation rules
  
  // Template Status
  isActive    Boolean @default(true)
  isDefault   Boolean @default(false)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("analysis_templates")
}

model AnalysisQueue {
  id        String @id @default(cuid())
  
  // Queue Details
  userId    String
  requestId String @unique
  type      String
  priority  Int    @default(5) // 1-10, higher = more priority
  
  // Queue Status
  status    String @default("queued") // queued, processing, completed, failed
  attempts  Int    @default(0)
  maxAttempts Int  @default(3)
  
  // Processing Details
  assignedWorker String?
  startedAt      DateTime?
  completedAt    DateTime?
  
  // Error Handling
  lastError      String?
  errorCount     Int @default(0)
  
  // Queue Data
  payload   Json   // Analysis request data
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("analysis_queue")
}

model AIModel {
  id          String @id @default(cuid())
  
  // Model Details
  name        String
  description String?
  type        String // sentiment, engagement_prediction, content_classification
  version     String
  
  // Model Configuration
  config      Json   // Model parameters and settings
  endpoint    String? // API endpoint if external
  
  // Model Performance
  accuracy    Float?
  precision   Float?
  recall      Float?
  f1Score     Float?
  
  // Model Status
  isActive    Boolean @default(true)
  isDefault   Boolean @default(false)
  
  // Training Data
  trainedOn   DateTime?
  datasetSize Int?
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("ai_models")
}

model AnalysisCache {
  id        String @id @default(cuid())
  
  // Cache Key
  cacheKey  String @unique
  userId    String
  type      String
  
  // Cache Data
  data      Json
  
  // Cache Metadata
  size      Int?
  hits      Int @default(0)
  
  // Cache Expiry
  expiresAt DateTime
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastAccessed DateTime @default(now())
  
  @@map("analysis_cache")
}
