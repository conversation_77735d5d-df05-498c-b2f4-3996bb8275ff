import { Injectable, Logger, ConflictException, NotFoundException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

export interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  passwordHash: string;
  emailVerified: boolean;
  twoFactorEnabled: boolean;
  status: 'active' | 'inactive' | 'suspended' | 'pending_verification';
  profile: UserProfile;
  preferences: UserPreferences;
  security: UserSecurity;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    lastLoginAt?: Date;
    loginCount: number;
    ipAddress?: string;
    userAgent?: string;
  };
}

export interface UserProfile {
  firstName?: string;
  lastName?: string;
  bio?: string;
  avatar?: string;
  coverImage?: string;
  location?: string;
  website?: string;
  socialLinks: {
    twitter?: string;
    discord?: string;
    telegram?: string;
    instagram?: string;
  };
  stats: {
    nftsGenerated: number;
    nftsMinted: number;
    analysesCompleted: number;
    totalScore: number;
    averageScore: number;
  };
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
    marketing: boolean;
  };
  privacy: {
    profilePublic: boolean;
    showStats: boolean;
    showNFTs: boolean;
    allowAnalytics: boolean;
  };
  features: {
    autoMint: boolean;
    shareResults: boolean;
    betaFeatures: boolean;
  };
}

export interface UserSecurity {
  passwordChangedAt: Date;
  failedLoginAttempts: number;
  lockedUntil?: Date;
  twoFactorSecret?: string;
  backupCodes: string[];
  sessions: UserSession[];
  securityLog: SecurityEvent[];
}

export interface UserSession {
  id: string;
  deviceId: string;
  deviceName: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastActiveAt: Date;
  expiresAt: Date;
  isActive: boolean;
}

export interface SecurityEvent {
  id: string;
  type: 'login' | 'logout' | 'password_change' | 'email_change' | 'failed_login' | 'account_locked' | 'suspicious_activity';
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  details: Record<string, any>;
}

export interface CreateUserRequest {
  email: string;
  username: string;
  password: string;
  displayName?: string;
  firstName?: string;
  lastName?: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
}

export interface UpdateUserRequest {
  displayName?: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
  location?: string;
  website?: string;
  socialLinks?: Partial<UserProfile['socialLinks']>;
}

@Injectable()
export class UserManagementService {
  private readonly logger = new Logger(UserManagementService.name);
  private users: Map<string, User> = new Map();
  private usersByEmail: Map<string, string> = new Map(); // email -> userId
  private usersByUsername: Map<string, string> = new Map(); // username -> userId

  constructor() {
    this.initializeMockUsers();
  }

  /**
   * Create a new user account
   */
  async createUser(createUserRequest: CreateUserRequest): Promise<User> {
    try {
      this.logger.log(`Creating new user: ${createUserRequest.email}`);

      // Validate input
      await this.validateCreateUserRequest(createUserRequest);

      // Check if user already exists
      if (this.usersByEmail.has(createUserRequest.email.toLowerCase())) {
        throw new ConflictException('User with this email already exists');
      }

      if (this.usersByUsername.has(createUserRequest.username.toLowerCase())) {
        throw new ConflictException('Username is already taken');
      }

      // Hash password
      const passwordHash = await bcrypt.hash(createUserRequest.password, 12);

      // Generate user ID
      const userId = this.generateUserId();

      // Create user object
      const user: User = {
        id: userId,
        email: createUserRequest.email.toLowerCase(),
        username: createUserRequest.username.toLowerCase(),
        displayName: createUserRequest.displayName || createUserRequest.username,
        passwordHash,
        emailVerified: false,
        twoFactorEnabled: false,
        status: 'pending_verification',
        profile: {
          firstName: createUserRequest.firstName,
          lastName: createUserRequest.lastName,
          socialLinks: {},
          stats: {
            nftsGenerated: 0,
            nftsMinted: 0,
            analysesCompleted: 0,
            totalScore: 0,
            averageScore: 0,
          },
        },
        preferences: this.getDefaultPreferences(),
        security: {
          passwordChangedAt: new Date(),
          failedLoginAttempts: 0,
          backupCodes: [],
          sessions: [],
          securityLog: [],
        },
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          loginCount: 0,
        },
      };

      // Store user
      this.users.set(userId, user);
      this.usersByEmail.set(user.email, userId);
      this.usersByUsername.set(user.username, userId);

      // Log security event
      await this.logSecurityEvent(userId, 'login', {
        type: 'account_created',
        email: user.email,
        username: user.username,
      });

      this.logger.log(`User created successfully: ${userId}`);
      return this.sanitizeUser(user);
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<User | null> {
    try {
      const user = this.users.get(userId);
      return user ? this.sanitizeUser(user) : null;
    } catch (error) {
      this.logger.error(`Failed to get user by ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      const userId = this.usersByEmail.get(email.toLowerCase());
      return userId ? await this.getUserById(userId) : null;
    } catch (error) {
      this.logger.error(`Failed to get user by email: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get user by username
   */
  async getUserByUsername(username: string): Promise<User | null> {
    try {
      const userId = this.usersByUsername.get(username.toLowerCase());
      return userId ? await this.getUserById(userId) : null;
    } catch (error) {
      this.logger.error(`Failed to get user by username: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Update user profile
   */
  async updateUser(userId: string, updateRequest: UpdateUserRequest): Promise<User> {
    try {
      this.logger.log(`Updating user: ${userId}`);

      const user = this.users.get(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Update profile fields
      if (updateRequest.displayName !== undefined) {
        user.displayName = updateRequest.displayName;
      }

      if (updateRequest.firstName !== undefined) {
        user.profile.firstName = updateRequest.firstName;
      }

      if (updateRequest.lastName !== undefined) {
        user.profile.lastName = updateRequest.lastName;
      }

      if (updateRequest.bio !== undefined) {
        user.profile.bio = updateRequest.bio;
      }

      if (updateRequest.location !== undefined) {
        user.profile.location = updateRequest.location;
      }

      if (updateRequest.website !== undefined) {
        user.profile.website = updateRequest.website;
      }

      if (updateRequest.socialLinks) {
        user.profile.socialLinks = {
          ...user.profile.socialLinks,
          ...updateRequest.socialLinks,
        };
      }

      // Update metadata
      user.metadata.updatedAt = new Date();

      // Store updated user
      this.users.set(userId, user);

      this.logger.log(`User updated successfully: ${userId}`);
      return this.sanitizeUser(user);
    } catch (error) {
      this.logger.error(`Failed to update user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<User> {
    try {
      const user = this.users.get(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Update preferences
      user.preferences = {
        ...user.preferences,
        ...preferences,
      };

      user.metadata.updatedAt = new Date();
      this.users.set(userId, user);

      this.logger.log(`User preferences updated: ${userId}`);
      return this.sanitizeUser(user);
    } catch (error) {
      this.logger.error(`Failed to update user preferences: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update user stats
   */
  async updateUserStats(userId: string, stats: Partial<UserProfile['stats']>): Promise<void> {
    try {
      const user = this.users.get(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Update stats
      user.profile.stats = {
        ...user.profile.stats,
        ...stats,
      };

      // Recalculate average score
      if (user.profile.stats.analysesCompleted > 0) {
        user.profile.stats.averageScore = user.profile.stats.totalScore / user.profile.stats.analysesCompleted;
      }

      user.metadata.updatedAt = new Date();
      this.users.set(userId, user);

      this.logger.debug(`User stats updated: ${userId}`, stats);
    } catch (error) {
      this.logger.error(`Failed to update user stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Verify user password
   */
  async verifyPassword(userId: string, password: string): Promise<boolean> {
    try {
      const user = this.users.get(userId);
      if (!user) {
        return false;
      }

      return await bcrypt.compare(password, user.passwordHash);
    } catch (error) {
      this.logger.error(`Failed to verify password: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Change user password
   */
  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    try {
      const user = this.users.get(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await this.verifyPassword(userId, currentPassword);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const newPasswordHash = await bcrypt.hash(newPassword, 12);

      // Update user
      user.passwordHash = newPasswordHash;
      user.security.passwordChangedAt = new Date();
      user.metadata.updatedAt = new Date();

      this.users.set(userId, user);

      // Log security event
      await this.logSecurityEvent(userId, 'password_change', {
        timestamp: new Date(),
      });

      this.logger.log(`Password changed successfully: ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to change password: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all users (admin function)
   */
  async getAllUsers(limit: number = 100, offset: number = 0): Promise<{ users: User[]; total: number }> {
    try {
      const allUsers = Array.from(this.users.values());
      const total = allUsers.length;
      const users = allUsers
        .slice(offset, offset + limit)
        .map(user => this.sanitizeUser(user));

      return { users, total };
    } catch (error) {
      this.logger.error(`Failed to get all users: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Search users
   */
  async searchUsers(query: string, limit: number = 20): Promise<User[]> {
    try {
      const searchTerm = query.toLowerCase();
      const matchingUsers = Array.from(this.users.values())
        .filter(user => 
          user.username.includes(searchTerm) ||
          user.displayName.toLowerCase().includes(searchTerm) ||
          user.email.includes(searchTerm) ||
          (user.profile.firstName && user.profile.firstName.toLowerCase().includes(searchTerm)) ||
          (user.profile.lastName && user.profile.lastName.toLowerCase().includes(searchTerm))
        )
        .slice(0, limit)
        .map(user => this.sanitizeUser(user));

      return matchingUsers;
    } catch (error) {
      this.logger.error(`Failed to search users: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete user account
   */
  async deleteUser(userId: string): Promise<void> {
    try {
      const user = this.users.get(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Remove from all maps
      this.users.delete(userId);
      this.usersByEmail.delete(user.email);
      this.usersByUsername.delete(user.username);

      this.logger.log(`User deleted: ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to delete user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Log security event
   */
  private async logSecurityEvent(
    userId: string,
    type: SecurityEvent['type'],
    details: Record<string, any>,
    ipAddress: string = 'unknown',
    userAgent: string = 'unknown'
  ): Promise<void> {
    try {
      const user = this.users.get(userId);
      if (!user) return;

      const event: SecurityEvent = {
        id: this.generateEventId(),
        type,
        timestamp: new Date(),
        ipAddress,
        userAgent,
        details,
      };

      user.security.securityLog.push(event);

      // Keep only last 100 events
      if (user.security.securityLog.length > 100) {
        user.security.securityLog = user.security.securityLog.slice(-100);
      }

      this.users.set(userId, user);
    } catch (error) {
      this.logger.error(`Failed to log security event: ${error.message}`, error.stack);
    }
  }

  /**
   * Validate create user request
   */
  private async validateCreateUserRequest(request: CreateUserRequest): Promise<void> {
    const errors: string[] = [];

    // Email validation
    if (!request.email || !this.isValidEmail(request.email)) {
      errors.push('Valid email is required');
    }

    // Username validation
    if (!request.username || request.username.length < 3 || request.username.length > 30) {
      errors.push('Username must be between 3 and 30 characters');
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(request.username)) {
      errors.push('Username can only contain letters, numbers, underscores, and hyphens');
    }

    // Password validation
    if (!request.password || request.password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    // Terms acceptance
    if (!request.acceptTerms) {
      errors.push('Terms and conditions must be accepted');
    }

    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Get default user preferences
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      theme: 'auto',
      language: 'en',
      timezone: 'UTC',
      notifications: {
        email: true,
        push: true,
        sms: false,
        marketing: false,
      },
      privacy: {
        profilePublic: true,
        showStats: true,
        showNFTs: true,
        allowAnalytics: true,
      },
      features: {
        autoMint: false,
        shareResults: true,
        betaFeatures: false,
      },
    };
  }

  /**
   * Sanitize user object (remove sensitive data)
   */
  private sanitizeUser(user: User): User {
    const sanitized = { ...user };
    delete sanitized.passwordHash;
    delete sanitized.security.twoFactorSecret;
    delete sanitized.security.backupCodes;
    return sanitized;
  }

  /**
   * Initialize mock users for testing
   */
  private initializeMockUsers(): void {
    // This would typically be removed in production
    const mockUsers = [
      {
        email: '<EMAIL>',
        username: 'demo_user',
        password: 'password123',
        displayName: 'Demo User',
        firstName: 'Demo',
        lastName: 'User',
        acceptTerms: true,
      },
    ];

    mockUsers.forEach(async (userData) => {
      try {
        await this.createUser(userData);
      } catch (error) {
        // Ignore errors for mock data
      }
    });
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Generate unique user ID
   */
  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
