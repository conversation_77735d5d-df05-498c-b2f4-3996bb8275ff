/**
 * Error Response DTO
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, IsArray, IsObject } from 'class-validator';
import { Type } from 'class-transformer';

export class ValidationErrorDto {
  @ApiProperty({ description: 'Field name that failed validation' })
  @IsString()
  field: string;

  @ApiProperty({ description: 'Validation error message' })
  @IsString()
  message: string;

  @ApiProperty({ description: 'Value that failed validation', required: false })
  @IsOptional()
  value?: any;

  @ApiProperty({ description: 'Validation constraints', required: false })
  @IsOptional()
  @IsObject()
  constraints?: Record<string, string>;
}

export class ErrorDetailsDto {
  @ApiProperty({ description: 'Error code' })
  @IsString()
  code: string;

  @ApiProperty({ description: 'Error message' })
  @IsString()
  message: string;

  @ApiProperty({ description: 'Additional error details', required: false })
  @IsOptional()
  details?: any;

  @ApiProperty({ description: 'Error stack trace (development only)', required: false })
  @IsOptional()
  @IsString()
  stack?: string;

  @ApiProperty({ description: 'Validation errors', type: [ValidationErrorDto], required: false })
  @IsOptional()
  @IsArray()
  @Type(() => ValidationErrorDto)
  validation?: ValidationErrorDto[];

  @ApiProperty({ description: 'Error context', required: false })
  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}

export class ErrorResponseDto {
  @ApiProperty({ description: 'Success flag', example: false })
  success: false = false;

  @ApiProperty({ description: 'Error details', type: ErrorDetailsDto })
  @Type(() => ErrorDetailsDto)
  error: ErrorDetailsDto;

  @ApiProperty({ description: 'HTTP status code' })
  @IsNumber()
  statusCode: number;

  @ApiProperty({ description: 'Request correlation ID' })
  @IsString()
  correlationId: string;

  @ApiProperty({ description: 'Response timestamp' })
  @IsString()
  timestamp: string;

  @ApiProperty({ description: 'Service name' })
  @IsString()
  service: string;

  @ApiProperty({ description: 'Service version' })
  @IsString()
  version: string;
}
