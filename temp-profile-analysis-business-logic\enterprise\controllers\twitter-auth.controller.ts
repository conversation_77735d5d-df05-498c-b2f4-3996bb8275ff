import { <PERSON>, Get, Post, Query, Res, Req, HttpStatus, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { TwitterApiClientService } from '../services/twitter-api-client.service';
import { JwtService } from '../services/jwt.service';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@ApiTags('Twitter Authentication')
@Controller('auth/twitter')
export class TwitterAuthController {
  private readonly userServiceUrl: string;

  constructor(
    private readonly twitterApiClient: TwitterApiClientService,
    private readonly jwtService: JwtService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {
    this.userServiceUrl = this.configService.get('USER_SERVICE_URL', 'http://localhost:3011');
  }
  
  @Get('login')
  @ApiOperation({ summary: 'Initiate Twitter OAuth authentication' })
  @ApiResponse({ status: 302, description: 'Redirect to Twitter OAuth' })
  async initiateTwitterAuth(@Res() res: Response) {
    try {
      console.log('🐦 Profile Analysis Service: Twitter OAuth initiation requested');
      
      // Check if we should use mock or real Twitter OAuth
      const useMockTwitter = process.env.USE_MOCK_SERVICES === 'true';
      
      if (useMockTwitter) {
        console.log('🔧 Using Mock Twitter OAuth for development');

        // ✅ FIXED: Redirect to our own callback endpoint first (through API Gateway)
        const mockCallbackUrl = `http://localhost:3010/api/auth/twitter/callback?code=mock_auth_code_${Date.now()}&state=mock_state`;

        console.log('↩️ Redirecting to API Gateway callback:', mockCallbackUrl);
        return res.redirect(mockCallbackUrl);
        
      } else {
        console.log('🔗 Using Real Twitter OAuth API');
        
        // Real Twitter OAuth configuration
        const twitterClientId = process.env.TWITTER_CLIENT_ID;
        const redirectUri = encodeURIComponent('http://localhost:3010/api/auth/twitter/callback');
        const state = `twitter_oauth_${Date.now()}`;
        const scope = encodeURIComponent('tweet.read users.read');
        
        if (!twitterClientId) {
          console.error('❌ Twitter Client ID not configured');
          return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
            success: false,
            error: 'Twitter OAuth not configured. Please set TWITTER_CLIENT_ID environment variable.'
          });
        }
        
        const twitterAuthUrl = `https://twitter.com/i/oauth2/authorize?response_type=code&client_id=${twitterClientId}&redirect_uri=${redirectUri}&scope=${scope}&state=${state}&code_challenge=challenge&code_challenge_method=plain`;
        
        console.log('↩️ Redirecting to Twitter OAuth:', twitterAuthUrl);
        return res.redirect(twitterAuthUrl);
      }
      
    } catch (error) {
      console.error('❌ Twitter OAuth initiation error:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Failed to initiate Twitter authentication',
        details: error.message
      });
    }
  }

  @Get('callback')
  @ApiOperation({ summary: 'Handle Twitter OAuth callback' })
  @ApiResponse({ status: 302, description: 'Redirect to frontend with auth result' })
  async handleTwitterCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Query('error') error: string,
    @Res() res: Response
  ) {
    try {
      console.log('🐦 Profile Analysis Service: Twitter OAuth callback received');
      console.log('📝 Callback params:', { code: code ? 'present' : 'missing', state, error });
      
      if (error) {
        console.error('❌ Twitter OAuth error:', error);
        const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent(error)}`;
        return res.redirect(errorUrl);
      }
      
      if (!code || !state) {
        console.error('❌ Missing required OAuth parameters');
        const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Missing authorization code or state')}`;
        return res.redirect(errorUrl);
      }
      
      // Check if we're using mock services
      const useMockTwitter = process.env.USE_MOCK_SERVICES === 'true';
      
      if (useMockTwitter) {
        console.log('🔧 Processing Mock Twitter OAuth callback');

        try {
          // ✅ ENHANCED: Call enhanced Mock Twitter Service for token exchange
          console.log('🔄 Calling Enhanced Twitter API Client for token exchange');
          const tokenResponse = await this.twitterApiClient.exchangeCodeForToken(code, state);

          if (!tokenResponse.success) {
            console.error('❌ Enhanced token exchange failed:', tokenResponse.error);
            const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent(`Token exchange failed: ${tokenResponse.error}`)}`;
            return res.redirect(errorUrl);
          }

          // ✅ ENHANCED: Process enhanced service response with complete user data
          const { access_token, user } = tokenResponse.data;
          console.log('✅ Enhanced token exchange successful, user:', user.username);
          console.log(`👤 User details: ${user.name} (@${user.username})`);
          console.log(`✅ Verified: ${user.verified}, Followers: ${user.public_metrics.followers_count}`);

          // ✅ ENHANCED: Validate the received access token
          console.log('🔍 Validating received access token');
          const tokenValidation = await this.twitterApiClient.validateToken(access_token);

          if (!tokenValidation.success) {
            console.error('❌ Access token validation failed:', tokenValidation.error);
            const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Token validation failed')}`;
            return res.redirect(errorUrl);
          }

          console.log('✅ Access token validated successfully');

          // ✅ ENHANCED: Transform Twitter user data to our user format
          const transformedUser = {
            id: user.id,
            username: user.username,
            email: user.email,
            displayName: user.name,
            profileImage: user.profile_image_url,
            provider: 'twitter',
            providerId: user.id,
            isActive: true,
            role: 'user',
            isVerified: user.verified,
            followerCount: user.public_metrics.followers_count,
            followingCount: user.public_metrics.following_count,
            tweetCount: user.public_metrics.tweet_count,
            description: user.description,
            location: user.location,
            url: user.url,
            createdAt: user.created_at,
            protected: user.protected
          };

          // ✅ ENHANCEMENT: Store user in database via User Service
          console.log('💾 Storing enhanced user data in database via User Service');
          const savedUser = await this.storeUserInDatabase(transformedUser);

          if (!savedUser) {
            console.error('❌ Failed to store user in database');
            const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Failed to store user data')}`;
            return res.redirect(errorUrl);
          }

          console.log('✅ User stored successfully in database');

          // ✅ ENHANCEMENT: Generate backend JWT token with complete user data
          console.log('🔐 Generating JWT token with enhanced user data');
          const jwtToken = await this.jwtService.generateToken(savedUser);

          // ✅ ENHANCEMENT: Redirect with real JWT token
          const successUrl = `http://localhost:3000/auth/twitter/callback?token=${jwtToken}`;
          console.log('✅ Enhanced authentication successful, redirecting with JWT token');
          return res.redirect(successUrl);

        } catch (error) {
          console.error('❌ Enhanced Mock Twitter API call failed:', error.message);
          const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Enhanced service call failed: ' + error.message)}`;
          return res.redirect(errorUrl);
        }
        
      } else {
        console.log('🔗 Processing Real Twitter OAuth callback');
        
        // Real Twitter OAuth token exchange
        const twitterClientId = process.env.TWITTER_CLIENT_ID;
        const twitterClientSecret = process.env.TWITTER_CLIENT_SECRET;
        
        if (!twitterClientId || !twitterClientSecret) {
          console.error('❌ Twitter OAuth credentials not configured');
          const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Twitter OAuth not properly configured')}`;
          return res.redirect(errorUrl);
        }
        
        // TODO: Implement real Twitter OAuth token exchange
        // For now, redirect with an error indicating real OAuth needs implementation
        const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Real Twitter OAuth implementation pending')}`;
        console.log('⚠️ Real Twitter OAuth not yet implemented, redirecting to:', errorUrl);
        return res.redirect(errorUrl);
      }
      
    } catch (error) {
      console.error('❌ Twitter OAuth callback error:', error);
      const errorUrl = `http://localhost:3000/auth/twitter/callback?error=${encodeURIComponent('Authentication failed: ' + error.message)}`;
      return res.redirect(errorUrl);
    }
  }

  @Post('validate-token')
  @ApiOperation({ summary: 'Validate JWT token' })
  @ApiResponse({ status: 200, description: 'Token validation result' })
  async validateToken(@Body() body: { token: string }) {
    try {
      console.log('🔍 JWT token validation requested');

      if (!body.token) {
        console.log('❌ No token provided');
        return {
          success: false,
          error: 'No token provided'
        };
      }

      const validationResult = await this.jwtService.validateToken(body.token);

      if (validationResult.success) {
        console.log(`✅ Token validated for user: ${validationResult.data.user.username}`);
      } else {
        console.log(`❌ Token validation failed: ${validationResult.error}`);
      }

      return validationResult;

    } catch (error) {
      console.error('❌ Token validation error:', error);
      return {
        success: false,
        error: 'Token validation failed',
        details: error.message
      };
    }
  }

  @Post('exchange')
  @ApiOperation({ summary: 'Exchange Twitter OAuth code for access token' })
  @ApiResponse({ status: 200, description: 'Token exchange successful' })
  async exchangeCodeForToken(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response
  ) {
    try {
      console.log('🔄 Profile Analysis Service: Token exchange requested');
      
      // Check if we're using mock services
      const useMockTwitter = process.env.USE_MOCK_SERVICES === 'true';
      
      if (useMockTwitter) {
        // Return mock authentication data
        const mockAuthData = {
          success: true,
          data: {
            accessToken: 'mock_jwt_token_' + Date.now(),
            refreshToken: 'mock_refresh_token_' + Date.now(),
            user: {
              id: 'twitter_user_' + Date.now(),
              username: 'mock_twitter_user',
              email: '<EMAIL>',
              displayName: 'Mock Twitter User',
              profileImage: 'https://via.placeholder.com/150',
              provider: 'twitter',
              isVerified: true
            },
            expiresIn: 3600
          }
        };
        
        console.log('✅ Mock token exchange successful');
        return res.status(HttpStatus.OK).json(mockAuthData);
        
      } else {
        // TODO: Implement real Twitter OAuth token exchange
        console.log('⚠️ Real Twitter OAuth token exchange not yet implemented');
        return res.status(HttpStatus.NOT_IMPLEMENTED).json({
          success: false,
          error: 'Real Twitter OAuth token exchange not yet implemented'
        });
      }
      
    } catch (error) {
      console.error('❌ Token exchange error:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Token exchange failed',
        details: error.message
      });
    }
  }

  /**
   * Store user in database via User Service
   */
  private async storeUserInDatabase(twitterUser: any): Promise<any> {
    try {
      console.log('📡 Calling User Service to store user');
      console.log(`🔗 User Service URL: ${this.userServiceUrl}/api/twitter-users/find-or-create`);

      const response = await firstValueFrom(
        this.httpService.post(`${this.userServiceUrl}/api/twitter-users/find-or-create`, twitterUser, {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Profile-Analysis-Service/1.0'
          }
        })
      );

      if (response.data.success) {
        console.log('✅ User stored successfully in database');
        return response.data.data;
      } else {
        console.error('❌ User Service returned error:', response.data.error);
        return null;
      }

    } catch (error) {
      console.error('❌ Error calling User Service:', error.message);
      console.error('🔍 Error details:', error.response?.data || error.stack);
      return null;
    }
  }
}
