# API Gateway Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=api

# Service URLs
USER_SERVICE_URL=http://localhost:3001
PROJECT_SERVICE_URL=http://localhost:3002
NFT_GENERATOR_SERVICE_URL=http://localhost:3003
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
MARKETPLACE_SERVICE_URL=http://localhost:3005
ANALYTICS_SERVICE_URL=http://localhost:3006
PROFILE_ANALYSIS_SERVICE_URL=http://localhost:3007

# Mock Services Configuration
USE_MOCK_SERVICES=true
MOCK_TWITTER_SERVICE_URL=http://localhost:3010

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=combined

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Swagger Configuration
SWAGGER_ENABLED=true
SWAGGER_TITLE=Social NFT Platform API
SWAGGER_DESCRIPTION=API Gateway for Social NFT Platform
SWAGGER_VERSION=1.0.0
