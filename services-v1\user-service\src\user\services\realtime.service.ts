import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../common/services/cache.service';
import { AuditService } from '../../common/services/audit.service';
import { RBACService } from './rbac.service';
import { 
  NotificationDto,
  CreateNotificationDto,
  ActivityFeedItemDto,
  ChatMessageDto,
  SendMessageDto,
  RealTimeMetricsDto,
  NotificationType,
  NotificationPriority,
  ActivityType 
} from '../dto/realtime.dto';
import { Permission } from '../dto/rbac.dto';
import { RequestContext } from '../interfaces/request-context.interface';

export interface RealtimeResult {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

@Injectable()
export class RealtimeService {
  private readonly logger = new Logger(RealtimeService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
    private readonly auditService: AuditService,
    private readonly rbacService: RBACService,
  ) {}

  /**
   * Create notification for user
   */
  async createNotification(createNotificationDto: CreateNotificationDto): Promise<NotificationDto> {
    const startTime = Date.now();

    try {
      this.logger.log(`Creating notification for user: ${createNotificationDto.userId}`);

      // Calculate expiration date
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + (createNotificationDto.expiresInHours || 168));

      // Create notification in database
      const notification = await this.prisma.notification.create({
        data: {
          type: createNotificationDto.type,
          priority: createNotificationDto.priority,
          title: createNotificationDto.title,
          message: createNotificationDto.message,
          userId: createNotificationDto.userId,
          icon: createNotificationDto.icon,
          actionUrl: createNotificationDto.actionUrl,
          actionText: createNotificationDto.actionText,
          isRead: false,
          expiresAt: expiresAt,
          data: createNotificationDto.data || {},
        },
      });

      // Invalidate user notifications cache
      await this.invalidateUserNotificationsCache(createNotificationDto.userId);

      this.logger.log(`Notification created successfully: ${notification.id}`, {
        notificationId: notification.id,
        userId: createNotificationDto.userId,
        type: createNotificationDto.type,
        duration: Date.now() - startTime,
      });

      return this.mapToNotificationDto(notification);

    } catch (error) {
      this.logger.error(`Failed to create notification: ${error.message}`, {
        createNotificationDto,
        error: error.stack,
        duration: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Get pending notifications for user
   */
  async getPendingNotifications(userId: string): Promise<NotificationDto[]> {
    const startTime = Date.now();

    try {
      this.logger.log(`Getting pending notifications for user: ${userId}`);

      // Try cache first
      const cacheKey = `user_notifications:${userId}`;
      let notifications = await this.cacheService.get<any[]>(cacheKey);

      if (!notifications) {
        notifications = await this.prisma.notification.findMany({
          where: {
            userId: userId,
            isRead: false,
            expiresAt: {
              gt: new Date(),
            },
          },
          orderBy: [
            { priority: 'desc' },
            { createdAt: 'desc' },
          ],
          take: 50, // Limit to 50 most recent notifications
        });

        // Cache for 2 minutes
        await this.cacheService.set(cacheKey, notifications, 120);
      }

      this.logger.log(`Retrieved ${notifications.length} pending notifications for user: ${userId}`, {
        userId,
        count: notifications.length,
        duration: Date.now() - startTime,
      });

      return notifications.map(n => this.mapToNotificationDto(n));

    } catch (error) {
      this.logger.error(`Failed to get pending notifications for user ${userId}: ${error.message}`, {
        userId,
        error: error.stack,
        duration: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(notificationId: string, userId: string): Promise<void> {
    const startTime = Date.now();

    try {
      this.logger.log(`Marking notification as read: ${notificationId} for user: ${userId}`);

      // Update notification
      const notification = await this.prisma.notification.updateMany({
        where: {
          id: notificationId,
          userId: userId,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      if (notification.count === 0) {
        throw new NotFoundException(`Notification ${notificationId} not found for user ${userId}`);
      }

      // Invalidate user notifications cache
      await this.invalidateUserNotificationsCache(userId);

      this.logger.log(`Notification marked as read: ${notificationId}`, {
        notificationId,
        userId,
        duration: Date.now() - startTime,
      });

    } catch (error) {
      this.logger.error(`Failed to mark notification as read: ${error.message}`, {
        notificationId,
        userId,
        error: error.stack,
        duration: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Create activity feed item
   */
  async createActivityFeedItem(
    type: ActivityType,
    title: string,
    description: string,
    userId: string,
    entityType?: string,
    entityId?: string,
    metadata?: Record<string, any>
  ): Promise<ActivityFeedItemDto> {
    const startTime = Date.now();

    try {
      this.logger.log(`Creating activity feed item: ${type} for user: ${userId}`);

      // Get user details
      const user = await this.prisma.userQuery.findUnique({
        where: { id: userId },
        select: {
          id: true,
          username: true,
          displayName: true,
          profileImage: true,
        },
      });

      if (!user) {
        throw new NotFoundException(`User ${userId} not found`);
      }

      // Create activity item
      const activity = await this.prisma.activityFeed.create({
        data: {
          type: type,
          title: title,
          description: description,
          userId: userId,
          entityType: entityType,
          entityId: entityId,
          metadata: metadata || {},
        },
      });

      // Invalidate activity feed cache
      await this.invalidateActivityFeedCache();

      this.logger.log(`Activity feed item created: ${activity.id}`, {
        activityId: activity.id,
        type,
        userId,
        duration: Date.now() - startTime,
      });

      return {
        id: activity.id,
        type: type,
        title: title,
        description: description,
        user: {
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          avatar: user.profileImage,
        },
        entity: entityType && entityId ? {
          type: entityType,
          id: entityId,
          name: title,
        } : undefined,
        createdAt: activity.createdAt.toISOString(),
        metadata: metadata,
      };

    } catch (error) {
      this.logger.error(`Failed to create activity feed item: ${error.message}`, {
        type,
        userId,
        error: error.stack,
        duration: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Create chat message
   */
  async createChatMessage(sendMessageDto: SendMessageDto, senderId: string): Promise<ChatMessageDto> {
    const startTime = Date.now();

    try {
      this.logger.log(`Creating chat message in room: ${sendMessageDto.roomId} from user: ${senderId}`);

      // Get sender details
      const sender = await this.prisma.userQuery.findUnique({
        where: { id: senderId },
        select: {
          id: true,
          username: true,
          displayName: true,
          profileImage: true,
        },
      });

      if (!sender) {
        throw new NotFoundException(`Sender ${senderId} not found`);
      }

      // Create message
      const message = await this.prisma.chatMessage.create({
        data: {
          roomId: sendMessageDto.roomId,
          senderId: senderId,
          content: sendMessageDto.content,
          messageType: sendMessageDto.messageType || 'text',
          attachments: sendMessageDto.attachments || [],
          replyToId: sendMessageDto.replyToId,
          metadata: sendMessageDto.metadata || {},
          isEdited: false,
        },
      });

      this.logger.log(`Chat message created: ${message.id}`, {
        messageId: message.id,
        roomId: sendMessageDto.roomId,
        senderId,
        duration: Date.now() - startTime,
      });

      return {
        id: message.id,
        roomId: message.roomId,
        senderId: message.senderId,
        sender: {
          id: sender.id,
          username: sender.username,
          displayName: sender.displayName,
          avatar: sender.profileImage,
        },
        content: message.content,
        messageType: message.messageType,
        attachments: Array.isArray(message.attachments) ? message.attachments.map(String) : [],
        createdAt: message.createdAt.toISOString(),
        editedAt: message.editedAt?.toISOString(),
        isEdited: message.isEdited,
        replyToId: message.replyToId,
        metadata: message.metadata as Record<string, any>,
      };

    } catch (error) {
      this.logger.error(`Failed to create chat message: ${error.message}`, {
        sendMessageDto,
        senderId,
        error: error.stack,
        duration: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Update user online status
   */
  async updateUserOnlineStatus(userId: string, isOnline: boolean): Promise<void> {
    try {
      await this.prisma.userCommand.update({
        where: { id: userId },
        data: {
          isOnline: isOnline,
          lastSeenAt: new Date(),
        },
      });

      this.logger.log(`User online status updated: ${userId} - ${isOnline ? 'online' : 'offline'}`);
    } catch (error) {
      this.logger.error(`Failed to update user online status: ${error.message}`, { userId, isOnline });
    }
  }

  /**
   * Get real-time metrics
   */
  async getRealTimeMetrics(): Promise<RealTimeMetricsDto> {
    const startTime = Date.now();

    try {
      // Try cache first
      const cacheKey = 'real_time_metrics';
      let metrics = await this.cacheService.get<RealTimeMetricsDto>(cacheKey);

      if (!metrics) {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        const [
          activeUsers,
          activeCampaigns,
          recentTransactions,
          activeListings,
        ] = await Promise.all([
          this.prisma.userCommand.count({
            where: {
              isOnline: true,
              lastSeenAt: {
                gte: new Date(now.getTime() - 15 * 60 * 1000), // Last 15 minutes
              },
            },
          }),
          this.prisma.campaign.count({
            where: { status: 'active' },
          }),
          this.prisma.marketplaceTransaction.count({
            where: {
              createdAt: {
                gte: oneHourAgo,
              },
            },
          }),
          this.prisma.marketplaceListing.count({
            where: { status: 'active' },
          }),
        ]);

        metrics = {
          activeUsers,
          activeCampaigns,
          recentTransactions,
          liveEngagement: 78.5, // TODO: Calculate actual engagement
          currentVolume: '45.67', // TODO: Calculate from transactions
          activeListings,
          timestamp: new Date().toISOString(),
          metrics: {
            newUsersToday: 0, // TODO: Calculate
            nftsGeneratedToday: 0, // TODO: Calculate
            averageResponseTime: 250, // TODO: Calculate
          },
        };

        // Cache for 30 seconds
        await this.cacheService.set(cacheKey, metrics, 30);
      }

      this.logger.debug(`Real-time metrics retrieved`, {
        activeUsers: metrics.activeUsers,
        activeCampaigns: metrics.activeCampaigns,
        duration: Date.now() - startTime,
      });

      return metrics;

    } catch (error) {
      this.logger.error(`Failed to get real-time metrics: ${error.message}`, {
        error: error.stack,
        duration: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Validate campaign access for user
   */
  async validateCampaignAccess(userId: string, campaignId: string): Promise<boolean> {
    try {
      const participation = await this.prisma.campaignParticipation.findUnique({
        where: {
          userId_campaignId: {
            userId: userId,
            campaignId: campaignId,
          },
        },
      });

      return !!participation;
    } catch (error) {
      this.logger.error(`Failed to validate campaign access: ${error.message}`, { userId, campaignId });
      return false;
    }
  }

  /**
   * Validate chat room access for user
   */
  async validateChatRoomAccess(userId: string, chatRoomId: string): Promise<boolean> {
    try {
      // TODO: Implement chat room access validation
      // For now, allow access to all chat rooms
      return true;
    } catch (error) {
      this.logger.error(`Failed to validate chat room access: ${error.message}`, { userId, chatRoomId });
      return false;
    }
  }

  /**
   * Map database notification to DTO
   */
  private mapToNotificationDto(notification: any): NotificationDto {
    return {
      id: notification.id,
      type: notification.type as NotificationType,
      priority: notification.priority as NotificationPriority,
      title: notification.title,
      message: notification.message,
      icon: notification.icon,
      actionUrl: notification.actionUrl,
      actionText: notification.actionText,
      userId: notification.userId,
      isRead: notification.isRead,
      createdAt: notification.createdAt.toISOString(),
      expiresAt: notification.expiresAt?.toISOString(),
      data: notification.data as Record<string, any>,
    };
  }

  /**
   * Invalidate user notifications cache
   */
  private async invalidateUserNotificationsCache(userId: string): Promise<void> {
    const cacheKey = `user_notifications:${userId}`;
    await this.cacheService.del(cacheKey);
  }

  /**
   * Invalidate activity feed cache
   */
  private async invalidateActivityFeedCache(): Promise<void> {
    const cacheKey = 'activity_feed';
    await this.cacheService.del(cacheKey);
  }
}
