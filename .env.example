# Environment Configuration Example
# Generated from MASTER_REFERENCE.md

# Core Configuration
NODE_ENV=development
USE_MOCK_SERVICES=true
API_GATEWAY_PORT=3010

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password_here

# Service URLs - Real Services
USER_SERVICE_URL=http://localhost:3011
PROFILE_ANALYSIS_SERVICE_URL=http://localhost:3002
NFT_GENERATION_SERVICE_URL=http://localhost:3003
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
PROJECT_SERVICE_URL=http://localhost:3005
MARKETPLACE_SERVICE_URL=http://localhost:3006
NOTIFICATION_SERVICE_URL=http://localhost:3008
ANALYTICS_SERVICE_URL=http://localhost:3009

# Service URLs - Mock Services (Development)
MOCK_TWITTER_SERVICE_URL=http://localhost:3020
MOCK_BLOCKCHAIN_SERVICE_URL=http://localhost:3021
MOCK_NFT_STORAGE_SERVICE_URL=http://localhost:3022

# Database Names
USER_DB_NAME=user_service
PROFILE_ANALYSIS_DB_NAME=profile_analysis
NFT_GENERATION_DB_NAME=nft_generation
BLOCKCHAIN_DB_NAME=blockchain_service
PROJECT_DB_NAME=project_service
MARKETPLACE_DB_NAME=marketplace_service
ANALYTICS_DB_NAME=analytics_service
NOTIFICATION_DB_NAME=notification_service

# JWT Configuration
JWT_SECRET=your-secret-key-here
JWT_EXPIRES_IN=24h

# Development Flags
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGGING=true
