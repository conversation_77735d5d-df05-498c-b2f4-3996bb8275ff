import { <PERSON>, Get, Post, Body, Query, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { EventTrackingService } from '../services/event-tracking.service';
import { PerformanceAnalyticsService } from '../services/performance-analytics.service';
import { UserBehaviorService } from '../services/user-behavior.service';

@ApiTags('analytics')
@Controller('analytics')
export class AnalyticsController {
  private readonly logger = new Logger(AnalyticsController.name);

  constructor(
    private readonly eventTracking: EventTrackingService,
    private readonly performanceAnalytics: PerformanceAnalyticsService,
    private readonly userBehavior: UserBehaviorService,
  ) {}

  @Get('overview')
  @ApiOperation({ summary: 'Get analytics overview' })
  @ApiResponse({ status: 200, description: 'Analytics overview retrieved successfully' })
  async getAnalyticsOverview() {
    try {
      this.logger.log('Getting analytics overview');

      const [eventMetrics, behaviorInsights, performanceReport] = await Promise.all([
        this.eventTracking.getEventMetrics(),
        this.userBehavior.getBehaviorInsights(),
        this.performanceAnalytics.generatePerformanceReport(),
      ]);

      return {
        success: true,
        data: {
          events: eventMetrics,
          behavior: behaviorInsights,
          performance: {
            system: performanceReport.system,
            alerts: performanceReport.alerts.length,
            recommendations: performanceReport.recommendations.length,
          },
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get analytics overview: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve analytics overview',
        details: error.message,
      };
    }
  }

  @Get('dashboard')
  @ApiOperation({ summary: 'Get analytics dashboard data' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved successfully' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range for data (24h, 7d, 30d)' })
  async getDashboardData(@Query('timeRange') timeRange: string = '24h') {
    try {
      this.logger.log(`Getting dashboard data for time range: ${timeRange}`);

      const hours = this.parseTimeRange(timeRange);
      const trends = this.performanceAnalytics.getPerformanceTrends(hours);
      const behaviorInsights = await this.userBehavior.getBehaviorInsights();
      const eventMetrics = this.eventTracking.getEventMetrics();

      return {
        success: true,
        data: {
          timeRange,
          trends,
          userSegments: behaviorInsights.userSegments,
          topEvents: eventMetrics.topEvents,
          totalUsers: behaviorInsights.totalUsers,
          activeUsers: behaviorInsights.activeUsers,
          eventsPerSecond: eventMetrics.eventsPerSecond,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get dashboard data: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve dashboard data',
        details: error.message,
      };
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Get platform metrics' })
  @ApiResponse({ status: 200, description: 'Platform metrics retrieved successfully' })
  async getPlatformMetrics() {
    try {
      this.logger.log('Getting platform metrics');

      const eventMetrics = this.eventTracking.getEventMetrics();
      const behaviorInsights = await this.userBehavior.getBehaviorInsights();
      const systemPerformance = await this.performanceAnalytics.getSystemPerformance();

      return {
        success: true,
        data: {
          events: {
            total: eventMetrics.totalEvents,
            perSecond: eventMetrics.eventsPerSecond,
            topTypes: eventMetrics.topEvents,
          },
          users: {
            total: behaviorInsights.totalUsers,
            active: behaviorInsights.activeUsers,
            new: behaviorInsights.newUsers,
            segments: behaviorInsights.userSegments.map(s => ({
              name: s.segment,
              count: s.userCount,
            })),
          },
          system: {
            cpu: systemPerformance.cpu.usage,
            memory: systemPerformance.memory.percentage,
            network: systemPerformance.network.requestsPerSecond,
            database: systemPerformance.database.queryTime,
          },
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get platform metrics: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve platform metrics',
        details: error.message,
      };
    }
  }

  @Post('track')
  @ApiOperation({ summary: 'Track custom analytics event' })
  @ApiResponse({ status: 201, description: 'Event tracked successfully' })
  async trackCustomEvent(@Body() eventData: any) {
    try {
      this.logger.log('Tracking custom event', { eventType: eventData.eventType });

      const eventId = await this.eventTracking.trackEvent(eventData);

      return {
        success: true,
        data: {
          eventId,
          message: 'Event tracked successfully',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to track custom event: ${error.message}`);
      return {
        success: false,
        error: 'Failed to track event',
        details: error.message,
      };
    }
  }

  @Get('reports')
  @ApiOperation({ summary: 'Get analytics reports' })
  @ApiResponse({ status: 200, description: 'Reports retrieved successfully' })
  @ApiQuery({ name: 'type', required: false, description: 'Report type (performance, behavior, events)' })
  async getReports(@Query('type') type: string = 'all') {
    try {
      this.logger.log(`Getting analytics reports: ${type}`);

      const reports: any = {};

      if (type === 'all' || type === 'performance') {
        reports.performance = await this.performanceAnalytics.generatePerformanceReport();
      }

      if (type === 'all' || type === 'behavior') {
        reports.behavior = await this.userBehavior.getBehaviorInsights();
      }

      if (type === 'all' || type === 'events') {
        reports.events = this.eventTracking.getEventMetrics();
      }

      return {
        success: true,
        data: reports,
      };
    } catch (error) {
      this.logger.error(`Failed to get reports: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve reports',
        details: error.message,
      };
    }
  }

  private parseTimeRange(timeRange: string): number {
    switch (timeRange) {
      case '1h': return 1;
      case '6h': return 6;
      case '24h': return 24;
      case '7d': return 24 * 7;
      case '30d': return 24 * 30;
      default: return 24;
    }
  }
}
