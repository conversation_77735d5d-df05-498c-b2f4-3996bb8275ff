'use client'

import React, { useState } from 'react'
import {
  PlusIcon,
  TrashIcon,
  GiftIcon,
  CurrencyDollarIcon,
  SparklesIcon,
  TrophyIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { CreateCampaignRequest, RewardType, CampaignReward } from '@/types/campaign.types'

interface RewardsStepProps {
  data: Partial<CreateCampaignRequest>
  updateData: (updates: Partial<CreateCampaignRequest>) => void
  errors: string[]
}

export default function RewardsStep({ data, updateData, errors }: RewardsStepProps) {
  const [showAddReward, setShowAddReward] = useState(false)

  const rewardTypes = [
    {
      type: RewardType.NFT_GENERATION,
      label: 'NFT Generation',
      description: 'Generate unique NFTs for participants',
      icon: '🖼️',
      config: ['nftTemplate', 'rarityWeights', 'customAttributes']
    },
    {
      type: RewardType.TOKEN_REWARD,
      label: 'Token Reward',
      description: 'Distribute cryptocurrency tokens',
      icon: '🪙',
      config: ['tokenAddress', 'tokenSymbol']
    },
    {
      type: RewardType.POINTS,
      label: 'Points',
      description: 'Award platform points',
      icon: '⭐',
      config: []
    },
    {
      type: RewardType.BADGE,
      label: 'Badge',
      description: 'Award achievement badges',
      icon: '🏆',
      config: []
    },
    {
      type: RewardType.MARKETPLACE_CREDIT,
      label: 'Marketplace Credit',
      description: 'Provide marketplace spending credits',
      icon: '💳',
      config: ['creditAmount', 'expirationDate']
    },
    {
      type: RewardType.EXCLUSIVE_ACCESS,
      label: 'Exclusive Access',
      description: 'Grant access to exclusive content or features',
      icon: '🔑',
      config: ['accessLevel', 'accessDuration']
    },
    {
      type: RewardType.PHYSICAL_ITEM,
      label: 'Physical Item',
      description: 'Send physical merchandise',
      icon: '📦',
      config: ['shippingRequired', 'shippingRegions']
    }
  ]

  const currencies = ['ETH', 'MATIC', 'USDC', 'USDT', 'BNB', 'AVAX', 'SOL']

  const addReward = (type: RewardType) => {
    const newReward: Omit<CampaignReward, 'id' | 'campaignId' | 'distributedCount' | 'createdAt' | 'updatedAt'> = {
      type,
      title: rewardTypes.find(rt => rt.type === type)?.label || '',
      description: rewardTypes.find(rt => rt.type === type)?.description || '',
      value: 0,
      quantity: 100,
      configuration: {},
      eligibilityCriteria: {}
    }

    updateData({
      rewards: [...(data.rewards || []), newReward]
    })
    setShowAddReward(false)
  }

  const updateReward = (index: number, updates: Partial<CampaignReward>) => {
    const rewards = [...(data.rewards || [])]
    rewards[index] = { ...rewards[index], ...updates }
    updateData({ rewards })
  }

  const removeReward = (index: number) => {
    const rewards = [...(data.rewards || [])]
    rewards.splice(index, 1)
    updateData({ rewards })
  }

  const getConfigurationFields = (reward: any, index: number) => {
    const rewardType = rewardTypes.find(rt => rt.type === reward.type)
    if (!rewardType) return null

    return (
      <div className="mt-4 space-y-3">
        {rewardType.config.includes('nftTemplate') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              NFT Template/Style
            </label>
            <select
              value={reward.configuration.nftTemplate || 'default'}
              onChange={(e) => updateReward(index, {
                configuration: { ...reward.configuration, nftTemplate: e.target.value }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="default">Default Template</option>
              <option value="premium">Premium Template</option>
              <option value="animated">Animated Template</option>
              <option value="custom">Custom Template</option>
            </select>
          </div>
        )}

        {rewardType.config.includes('rarityWeights') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Rarity Distribution (%)
            </label>
            <div className="grid grid-cols-5 gap-2">
              {['common', 'rare', 'epic', 'legendary', 'mythic'].map((rarity) => (
                <div key={rarity}>
                  <label className="block text-xs text-gray-600 mb-1 capitalize">{rarity}</label>
                  <input
                    type="number"
                    value={reward.configuration.rarityWeights?.[rarity] || 0}
                    onChange={(e) => updateReward(index, {
                      configuration: {
                        ...reward.configuration,
                        rarityWeights: {
                          ...reward.configuration.rarityWeights,
                          [rarity]: parseInt(e.target.value)
                        }
                      }
                    })}
                    min="0"
                    max="100"
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {rewardType.config.includes('tokenAddress') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Token Contract Address
            </label>
            <input
              type="text"
              value={reward.configuration.tokenAddress || ''}
              onChange={(e) => updateReward(index, {
                configuration: { ...reward.configuration, tokenAddress: e.target.value }
              })}
              placeholder="0x..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {rewardType.config.includes('tokenSymbol') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Token Symbol
            </label>
            <input
              type="text"
              value={reward.configuration.tokenSymbol || ''}
              onChange={(e) => updateReward(index, {
                configuration: { ...reward.configuration, tokenSymbol: e.target.value }
              })}
              placeholder="TOKEN"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {rewardType.config.includes('creditAmount') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Credit Amount
            </label>
            <input
              type="number"
              value={reward.configuration.creditAmount || ''}
              onChange={(e) => updateReward(index, {
                configuration: { ...reward.configuration, creditAmount: parseFloat(e.target.value) }
              })}
              placeholder="10.00"
              min="0"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {rewardType.config.includes('expirationDate') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Credit Expiration Date
            </label>
            <input
              type="date"
              value={reward.configuration.expirationDate || ''}
              onChange={(e) => updateReward(index, {
                configuration: { ...reward.configuration, expirationDate: e.target.value }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {rewardType.config.includes('accessLevel') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Access Level
            </label>
            <select
              value={reward.configuration.accessLevel || 'basic'}
              onChange={(e) => updateReward(index, {
                configuration: { ...reward.configuration, accessLevel: e.target.value }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="basic">Basic Access</option>
              <option value="premium">Premium Access</option>
              <option value="vip">VIP Access</option>
              <option value="exclusive">Exclusive Access</option>
            </select>
          </div>
        )}

        {rewardType.config.includes('accessDuration') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Access Duration (days)
            </label>
            <input
              type="number"
              value={reward.configuration.accessDuration || ''}
              onChange={(e) => updateReward(index, {
                configuration: { ...reward.configuration, accessDuration: parseInt(e.target.value) }
              })}
              placeholder="30"
              min="1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}

        {rewardType.config.includes('shippingRequired') && (
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={reward.configuration.shippingRequired || false}
                onChange={(e) => updateReward(index, {
                  configuration: { ...reward.configuration, shippingRequired: e.target.checked }
                })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">Requires shipping address</span>
            </label>
          </div>
        )}

        {rewardType.config.includes('shippingRegions') && reward.configuration.shippingRequired && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Shipping Regions (comma-separated)
            </label>
            <input
              type="text"
              value={reward.configuration.shippingRegions?.join(', ') || ''}
              onChange={(e) => updateReward(index, {
                configuration: {
                  ...reward.configuration,
                  shippingRegions: e.target.value.split(',').map(region => region.trim()).filter(Boolean)
                }
              })}
              placeholder="US, CA, EU, UK"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        )}
      </div>
    )
  }

  const getTotalRewardValue = () => {
    return (data.rewards || []).reduce((total, reward) => {
      return total + (reward.value * reward.quantity)
    }, 0)
  }

  return (
    <div className="space-y-6">
      {/* Rewards List */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Campaign Rewards</h3>
          <button
            onClick={() => setShowAddReward(true)}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Reward
          </button>
        </div>

        {data.rewards && data.rewards.length > 0 ? (
          <div className="space-y-4">
            {data.rewards.map((reward, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <span className="text-2xl">
                        {rewardTypes.find(rt => rt.type === reward.type)?.icon}
                      </span>
                      <div className="flex-1">
                        <input
                          type="text"
                          value={reward.title}
                          onChange={(e) => updateReward(index, { title: e.target.value })}
                          className="text-lg font-medium text-gray-900 border-none p-0 focus:ring-0 w-full"
                          placeholder="Reward title"
                        />
                        <textarea
                          value={reward.description}
                          onChange={(e) => updateReward(index, { description: e.target.value })}
                          className="text-sm text-gray-600 border-none p-0 focus:ring-0 w-full mt-1"
                          placeholder="Reward description"
                          rows={2}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Value
                        </label>
                        <input
                          type="number"
                          value={reward.value}
                          onChange={(e) => updateReward(index, { value: parseFloat(e.target.value) })}
                          min="0"
                          step="0.01"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                        />
                      </div>
                      
                      {reward.type === RewardType.TOKEN_REWARD && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Currency
                          </label>
                          <select
                            value={reward.currency || 'ETH'}
                            onChange={(e) => updateReward(index, { currency: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                          >
                            {currencies.map(currency => (
                              <option key={currency} value={currency}>{currency}</option>
                            ))}
                          </select>
                        </div>
                      )}

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Quantity
                        </label>
                        <input
                          type="number"
                          value={reward.quantity}
                          onChange={(e) => updateReward(index, { quantity: parseInt(e.target.value) })}
                          min="1"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total Value
                        </label>
                        <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-gray-700">
                          {(reward.value * reward.quantity).toFixed(2)}
                        </div>
                      </div>
                    </div>

                    {getConfigurationFields(reward, index)}

                    {/* Eligibility Criteria */}
                    <div className="mt-4 p-3 bg-gray-50 rounded-md">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Eligibility Criteria</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div>
                          <label className="block text-xs text-gray-600 mb-1">
                            Minimum Points
                          </label>
                          <input
                            type="number"
                            value={reward.eligibilityCriteria.minPoints || ''}
                            onChange={(e) => updateReward(index, {
                              eligibilityCriteria: {
                                ...reward.eligibilityCriteria,
                                minPoints: e.target.value ? parseInt(e.target.value) : undefined
                              }
                            })}
                            placeholder="0"
                            min="0"
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-xs text-gray-600 mb-1">
                            Participation Days
                          </label>
                          <input
                            type="number"
                            value={reward.eligibilityCriteria.participationDuration || ''}
                            onChange={(e) => updateReward(index, {
                              eligibilityCriteria: {
                                ...reward.eligibilityCriteria,
                                participationDuration: e.target.value ? parseInt(e.target.value) : undefined
                              }
                            })}
                            placeholder="0"
                            min="0"
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </div>

                        <div>
                          <label className="block text-xs text-gray-600 mb-1">
                            Required Tasks
                          </label>
                          <input
                            type="text"
                            value={reward.eligibilityCriteria.requiredTasks?.join(', ') || ''}
                            onChange={(e) => updateReward(index, {
                              eligibilityCriteria: {
                                ...reward.eligibilityCriteria,
                                requiredTasks: e.target.value.split(',').map(task => task.trim()).filter(Boolean)
                              }
                            })}
                            placeholder="task1, task2"
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={() => removeReward(index)}
                    className="p-2 text-red-400 hover:text-red-600 ml-4"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}

            {/* Total Rewards Summary */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-800 mb-2">Rewards Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-blue-700">
                <div>
                  <span className="font-medium">Total Rewards:</span> {data.rewards.length}
                </div>
                <div>
                  <span className="font-medium">Total Value:</span> {getTotalRewardValue().toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Total Recipients:</span> {data.rewards.reduce((sum, r) => sum + r.quantity, 0)}
                </div>
                <div>
                  <span className="font-medium">Avg Value/Recipient:</span> {
                    data.rewards.length > 0 
                      ? (getTotalRewardValue() / data.rewards.reduce((sum, r) => sum + r.quantity, 0)).toFixed(2)
                      : '0.00'
                  }
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
            <GiftIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No rewards added</h3>
            <p className="mt-1 text-sm text-gray-500">
              Add rewards to incentivize participation in your campaign.
            </p>
          </div>
        )}
      </div>

      {/* Add Reward Modal */}
      {showAddReward && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Add Reward</h3>
              <button
                onClick={() => setShowAddReward(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {rewardTypes.map((rewardType) => (
                <button
                  key={rewardType.type}
                  onClick={() => addReward(rewardType.type)}
                  className="text-left p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                >
                  <div className="flex items-start space-x-3">
                    <span className="text-2xl">{rewardType.icon}</span>
                    <div>
                      <h4 className="font-medium text-gray-900">{rewardType.label}</h4>
                      <p className="text-sm text-gray-600 mt-1">{rewardType.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="bg-green-50 border border-green-200 rounded-md p-4">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-green-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">Rewards Strategy Tips</h3>
            <div className="mt-2 text-sm text-green-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Offer a mix of immediate and long-term rewards</li>
                <li>NFT rewards create lasting value and community</li>
                <li>Set appropriate eligibility criteria to ensure quality participation</li>
                <li>Consider the total campaign budget when setting reward values</li>
                <li>Limited quantity rewards create urgency and exclusivity</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
