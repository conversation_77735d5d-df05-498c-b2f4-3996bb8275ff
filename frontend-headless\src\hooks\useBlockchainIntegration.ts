import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { blockchainIntegrationService } from '@/services/blockchainIntegrationService'
import {
  NetworkConfig,
  WalletConnection,
  SmartContract,
  Transaction,
  GasEstimate,
  CrossChainBridge,
  BridgeTransaction,
  NFTContract,
  MultiChainNFT,
  BlockchainAnalytics,
  ConnectWalletRequest,
  SwitchNetworkRequest,
  SendTransactionRequest,
  DeployContractRequest,
  BridgeNFTRequest,
  EstimateGasRequest,
  BlockchainSearchRequest,
  BlockchainNetwork,
  WalletType
} from '@/types/blockchain-integration.types'

// ===== NETWORK HOOKS =====

export function useNetworks() {
  return useQuery({
    queryKey: ['blockchain-networks'],
    queryFn: () => blockchainIntegrationService.getNetworks(),
    staleTime: 600000, // 10 minutes
  })
}

export function useNetwork(networkId: string) {
  return useQuery({
    queryKey: ['blockchain-network', networkId],
    queryFn: () => blockchainIntegrationService.getNetwork(networkId),
    enabled: !!networkId,
    staleTime: 600000,
  })
}

export function useActiveNetworks() {
  return useQuery({
    queryKey: ['blockchain-active-networks'],
    queryFn: () => blockchainIntegrationService.getActiveNetworks(),
    staleTime: 300000, // 5 minutes
  })
}

export function useUpdateNetworkConfig() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ networkId, config }: { networkId: string; config: Partial<NetworkConfig> }) =>
      blockchainIntegrationService.updateNetworkConfig(networkId, config),
    onSuccess: (updatedNetwork, { networkId }) => {
      queryClient.setQueryData(['blockchain-network', networkId], updatedNetwork)
      queryClient.invalidateQueries({ queryKey: ['blockchain-networks'] })
      queryClient.invalidateQueries({ queryKey: ['blockchain-active-networks'] })
    },
    onError: (error: any) => {
      console.error('Failed to update network config:', error)
    },
  })
}

// ===== WALLET HOOKS =====

export function useConnectWallet() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: ConnectWalletRequest) =>
      blockchainIntegrationService.connectWallet(request),
    onSuccess: (walletConnection) => {
      queryClient.invalidateQueries({ queryKey: ['connected-wallets'] })
      queryClient.setQueryData(['wallet-connection', walletConnection.id], walletConnection)
    },
    onError: (error: any) => {
      console.error('Failed to connect wallet:', error)
    },
  })
}

export function useDisconnectWallet() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (walletId: string) => blockchainIntegrationService.disconnectWallet(walletId),
    onSuccess: (_, walletId) => {
      queryClient.removeQueries({ queryKey: ['wallet-connection', walletId] })
      queryClient.invalidateQueries({ queryKey: ['connected-wallets'] })
    },
    onError: (error: any) => {
      console.error('Failed to disconnect wallet:', error)
    },
  })
}

export function useConnectedWallets() {
  return useQuery({
    queryKey: ['connected-wallets'],
    queryFn: () => blockchainIntegrationService.getConnectedWallets(),
    staleTime: 60000, // 1 minute
    refetchInterval: 30000, // 30 seconds
  })
}

export function useWalletBalance(address: string, network: BlockchainNetwork) {
  return useQuery({
    queryKey: ['wallet-balance', address, network],
    queryFn: () => blockchainIntegrationService.getWalletBalance(address, network),
    enabled: !!(address && network),
    staleTime: 60000,
    refetchInterval: 30000,
  })
}

export function useSwitchNetwork() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: SwitchNetworkRequest) =>
      blockchainIntegrationService.switchNetwork(request),
    onSuccess: (updatedConnection) => {
      queryClient.invalidateQueries({ queryKey: ['connected-wallets'] })
      queryClient.setQueryData(['wallet-connection', updatedConnection.id], updatedConnection)
    },
    onError: (error: any) => {
      console.error('Failed to switch network:', error)
    },
  })
}

export function useAddNetwork() {
  return useMutation({
    mutationFn: (networkConfig: NetworkConfig) =>
      blockchainIntegrationService.addNetwork(networkConfig),
    onError: (error: any) => {
      console.error('Failed to add network:', error)
    },
  })
}

// ===== SMART CONTRACT HOOKS =====

export function useContracts(network?: BlockchainNetwork) {
  return useQuery({
    queryKey: ['blockchain-contracts', network],
    queryFn: () => blockchainIntegrationService.getContracts(network),
    staleTime: 300000,
  })
}

export function useContract(contractId: string) {
  return useQuery({
    queryKey: ['blockchain-contract', contractId],
    queryFn: () => blockchainIntegrationService.getContract(contractId),
    enabled: !!contractId,
    staleTime: 300000,
  })
}

export function useDeployContract() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: DeployContractRequest) =>
      blockchainIntegrationService.deployContract(request),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['blockchain-contracts'] })
      // Optionally set the new contract data if available
    },
    onError: (error: any) => {
      console.error('Failed to deploy contract:', error)
    },
  })
}

export function useUpgradeContract() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ contractId, newImplementation }: { contractId: string; newImplementation: string }) =>
      blockchainIntegrationService.upgradeContract(contractId, newImplementation),
    onSuccess: (transaction, { contractId }) => {
      queryClient.invalidateQueries({ queryKey: ['blockchain-contract', contractId] })
      queryClient.invalidateQueries({ queryKey: ['blockchain-contracts'] })
    },
    onError: (error: any) => {
      console.error('Failed to upgrade contract:', error)
    },
  })
}

export function useVerifyContract() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ contractId, sourceCode }: { contractId: string; sourceCode: string }) =>
      blockchainIntegrationService.verifyContract(contractId, sourceCode),
    onSuccess: (result, { contractId }) => {
      queryClient.invalidateQueries({ queryKey: ['blockchain-contract', contractId] })
    },
    onError: (error: any) => {
      console.error('Failed to verify contract:', error)
    },
  })
}

export function usePauseContract() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (contractId: string) => blockchainIntegrationService.pauseContract(contractId),
    onSuccess: (transaction, contractId) => {
      queryClient.invalidateQueries({ queryKey: ['blockchain-contract', contractId] })
    },
    onError: (error: any) => {
      console.error('Failed to pause contract:', error)
    },
  })
}

export function useUnpauseContract() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (contractId: string) => blockchainIntegrationService.unpauseContract(contractId),
    onSuccess: (transaction, contractId) => {
      queryClient.invalidateQueries({ queryKey: ['blockchain-contract', contractId] })
    },
    onError: (error: any) => {
      console.error('Failed to unpause contract:', error)
    },
  })
}

// ===== TRANSACTION HOOKS =====

export function useSendTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: SendTransactionRequest) =>
      blockchainIntegrationService.sendTransaction(request),
    onSuccess: (transaction) => {
      queryClient.invalidateQueries({ queryKey: ['transaction-history'] })
      queryClient.setQueryData(['transaction', transaction.hash], transaction)
    },
    onError: (error: any) => {
      console.error('Failed to send transaction:', error)
    },
  })
}

export function useTransaction(hash: string) {
  return useQuery({
    queryKey: ['transaction', hash],
    queryFn: () => blockchainIntegrationService.getTransaction(hash),
    enabled: !!hash,
    staleTime: 30000, // 30 seconds
    refetchInterval: (data) => {
      // Stop refetching if transaction is confirmed or failed
      return data?.status === 'confirmed' || data?.status === 'failed' ? false : 10000
    },
  })
}

export function useTransactionHistory(address: string, network?: BlockchainNetwork) {
  return useQuery({
    queryKey: ['transaction-history', address, network],
    queryFn: () => blockchainIntegrationService.getTransactionHistory(address, network),
    enabled: !!address,
    staleTime: 60000,
  })
}

export function useCancelTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ hash, gasPrice }: { hash: string; gasPrice: string }) =>
      blockchainIntegrationService.cancelTransaction(hash, gasPrice),
    onSuccess: (transaction, { hash }) => {
      queryClient.setQueryData(['transaction', hash], transaction)
      queryClient.invalidateQueries({ queryKey: ['transaction-history'] })
    },
    onError: (error: any) => {
      console.error('Failed to cancel transaction:', error)
    },
  })
}

export function useSpeedUpTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ hash, gasPrice }: { hash: string; gasPrice: string }) =>
      blockchainIntegrationService.speedUpTransaction(hash, gasPrice),
    onSuccess: (transaction, { hash }) => {
      queryClient.setQueryData(['transaction', hash], transaction)
      queryClient.invalidateQueries({ queryKey: ['transaction-history'] })
    },
    onError: (error: any) => {
      console.error('Failed to speed up transaction:', error)
    },
  })
}

export function useSearchTransactions(request: BlockchainSearchRequest) {
  return useQuery({
    queryKey: ['search-transactions', request],
    queryFn: () => blockchainIntegrationService.searchTransactions(request),
    enabled: !!(request.addresses?.length || request.networks?.length),
    staleTime: 300000,
  })
}

// ===== GAS HOOKS =====

export function useEstimateGas() {
  return useMutation({
    mutationFn: (request: EstimateGasRequest) =>
      blockchainIntegrationService.estimateGas(request),
    onError: (error: any) => {
      console.error('Failed to estimate gas:', error)
    },
  })
}

export function useGasPrice(network: BlockchainNetwork) {
  return useQuery({
    queryKey: ['gas-price', network],
    queryFn: () => blockchainIntegrationService.getGasPrice(network),
    enabled: !!network,
    staleTime: 30000, // 30 seconds
    refetchInterval: 30000,
  })
}

export function useGasHistory(network: BlockchainNetwork, timeframe: string = '24h') {
  return useQuery({
    queryKey: ['gas-history', network, timeframe],
    queryFn: () => blockchainIntegrationService.getGasHistory(network, timeframe),
    enabled: !!network,
    staleTime: 300000,
  })
}

export function useOptimizeGas() {
  return useMutation({
    mutationFn: (transactionData: any) =>
      blockchainIntegrationService.optimizeGas(transactionData),
    onError: (error: any) => {
      console.error('Failed to optimize gas:', error)
    },
  })
}

// ===== BRIDGE HOOKS =====

export function useBridges() {
  return useQuery({
    queryKey: ['cross-chain-bridges'],
    queryFn: () => blockchainIntegrationService.getBridges(),
    staleTime: 600000,
  })
}

export function useBridge(bridgeId: string) {
  return useQuery({
    queryKey: ['cross-chain-bridge', bridgeId],
    queryFn: () => blockchainIntegrationService.getBridge(bridgeId),
    enabled: !!bridgeId,
    staleTime: 600000,
  })
}

export function useBridgeNFT() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: BridgeNFTRequest) =>
      blockchainIntegrationService.bridgeNFT(request),
    onSuccess: (bridgeTransaction) => {
      queryClient.invalidateQueries({ queryKey: ['bridge-history'] })
      queryClient.setQueryData(['bridge-transaction', bridgeTransaction.id], bridgeTransaction)
    },
    onError: (error: any) => {
      console.error('Failed to bridge NFT:', error)
    },
  })
}

export function useBridgeTransaction(transactionId: string) {
  return useQuery({
    queryKey: ['bridge-transaction', transactionId],
    queryFn: () => blockchainIntegrationService.getBridgeTransaction(transactionId),
    enabled: !!transactionId,
    staleTime: 30000,
    refetchInterval: (data) => {
      return data?.status === 'completed' || data?.status === 'failed' ? false : 15000
    },
  })
}

export function useBridgeHistory(address: string) {
  return useQuery({
    queryKey: ['bridge-history', address],
    queryFn: () => blockchainIntegrationService.getBridgeHistory(address),
    enabled: !!address,
    staleTime: 300000,
  })
}

export function useEstimateBridgeFee() {
  return useMutation({
    mutationFn: (request: BridgeNFTRequest) =>
      blockchainIntegrationService.estimateBridgeFee(request),
    onError: (error: any) => {
      console.error('Failed to estimate bridge fee:', error)
    },
  })
}

// ===== NFT CONTRACT HOOKS =====

export function useNFTContracts(network?: BlockchainNetwork) {
  return useQuery({
    queryKey: ['nft-contracts', network],
    queryFn: () => blockchainIntegrationService.getNFTContracts(network),
    staleTime: 300000,
  })
}

export function useNFTContract(contractAddress: string, network: BlockchainNetwork) {
  return useQuery({
    queryKey: ['nft-contract', contractAddress, network],
    queryFn: () => blockchainIntegrationService.getNFTContract(contractAddress, network),
    enabled: !!(contractAddress && network),
    staleTime: 300000,
  })
}

export function useDeployNFTContract() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (contractData: any) =>
      blockchainIntegrationService.deployNFTContract(contractData),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['nft-contracts'] })
    },
    onError: (error: any) => {
      console.error('Failed to deploy NFT contract:', error)
    },
  })
}

export function useMintNFT() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ contractAddress, network, mintData }: {
      contractAddress: string
      network: BlockchainNetwork
      mintData: any
    }) => blockchainIntegrationService.mintNFT(contractAddress, network, mintData),
    onSuccess: (transaction, { contractAddress, network }) => {
      queryClient.invalidateQueries({ queryKey: ['nft-contract', contractAddress, network] })
      queryClient.invalidateQueries({ queryKey: ['transaction-history'] })
    },
    onError: (error: any) => {
      console.error('Failed to mint NFT:', error)
    },
  })
}

export function useTransferNFT() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ contractAddress, network, transferData }: {
      contractAddress: string
      network: BlockchainNetwork
      transferData: any
    }) => blockchainIntegrationService.transferNFT(contractAddress, network, transferData),
    onSuccess: (transaction, { contractAddress, network }) => {
      queryClient.invalidateQueries({ queryKey: ['nft-contract', contractAddress, network] })
      queryClient.invalidateQueries({ queryKey: ['transaction-history'] })
    },
    onError: (error: any) => {
      console.error('Failed to transfer NFT:', error)
    },
  })
}

export function useBurnNFT() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ contractAddress, network, burnData }: {
      contractAddress: string
      network: BlockchainNetwork
      burnData: any
    }) => blockchainIntegrationService.burnNFT(contractAddress, network, burnData),
    onSuccess: (transaction, { contractAddress, network }) => {
      queryClient.invalidateQueries({ queryKey: ['nft-contract', contractAddress, network] })
      queryClient.invalidateQueries({ queryKey: ['transaction-history'] })
    },
    onError: (error: any) => {
      console.error('Failed to burn NFT:', error)
    },
  })
}

// ===== MULTI-CHAIN NFT HOOKS =====

export function useMultiChainNFT(nftId: string) {
  return useQuery({
    queryKey: ['multi-chain-nft', nftId],
    queryFn: () => blockchainIntegrationService.getMultiChainNFT(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

export function useSyncMultiChainNFT() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (nftId: string) => blockchainIntegrationService.syncMultiChainNFT(nftId),
    onSuccess: (syncedNFT, nftId) => {
      queryClient.setQueryData(['multi-chain-nft', nftId], syncedNFT)
    },
    onError: (error: any) => {
      console.error('Failed to sync multi-chain NFT:', error)
    },
  })
}

export function useMultiChainNFTHistory(nftId: string) {
  return useQuery({
    queryKey: ['multi-chain-nft-history', nftId],
    queryFn: () => blockchainIntegrationService.getMultiChainNFTHistory(nftId),
    enabled: !!nftId,
    staleTime: 300000,
  })
}

// ===== ANALYTICS HOOKS =====

export function useBlockchainAnalytics(network: BlockchainNetwork, timeframe: string = '30d') {
  return useQuery({
    queryKey: ['blockchain-analytics', network, timeframe],
    queryFn: () => blockchainIntegrationService.getBlockchainAnalytics(network, timeframe),
    enabled: !!network,
    staleTime: 300000,
  })
}

export function useNetworkStatus() {
  return useQuery({
    queryKey: ['network-status'],
    queryFn: () => blockchainIntegrationService.getNetworkStatus(),
    staleTime: 60000,
    refetchInterval: 60000,
  })
}

export function usePortfolioAnalytics(address: string) {
  return useQuery({
    queryKey: ['portfolio-analytics', address],
    queryFn: () => blockchainIntegrationService.getPortfolioAnalytics(address),
    enabled: !!address,
    staleTime: 300000,
  })
}
