'use client'

import React, { useState, Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { 
  XMarkIcon, 
  CurrencyDollarIcon, 
  ShoppingCartIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { MarketplaceListing, PaymentMethod, PurchaseRequest } from '@/types/marketplace.types'
import { usePurchaseListing } from '@/hooks/useMarketplace'

interface PurchaseModalProps {
  listing: MarketplaceListing | null
  isOpen: boolean
  onClose: () => void
  onSuccess?: (transaction: any) => void
}

export default function PurchaseModal({
  listing,
  isOpen,
  onClose,
  onSuccess
}: PurchaseModalProps) {
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(PaymentMethod.ETH)
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const [step, setStep] = useState<'confirm' | 'processing' | 'success' | 'error'>('confirm')

  const purchaseMutation = usePurchaseListing()

  const handlePurchase = async () => {
    if (!listing || !agreedToTerms) return

    setStep('processing')

    try {
      const purchaseData: PurchaseRequest = {
        listingId: listing.id,
        paymentMethod: paymentMethod
      }

      const result = await purchaseMutation.mutateAsync(purchaseData)
      setStep('success')
      onSuccess?.(result)
      
      // Auto-close after success
      setTimeout(() => {
        handleClose()
      }, 3000)
    } catch (error) {
      console.error('Purchase failed:', error)
      setStep('error')
    }
  }

  const handleClose = () => {
    setStep('confirm')
    setAgreedToTerms(false)
    onClose()
  }

  const calculateFees = () => {
    if (!listing) return { platformFee: 0, royaltyFee: 0, total: 0 }
    
    const platformFeeRate = 0.025 // 2.5%
    const royaltyFeeRate = 0.05 // 5%
    
    const platformFee = listing.price * platformFeeRate
    const royaltyFee = listing.price * royaltyFeeRate
    const total = listing.price + platformFee + royaltyFee
    
    return { platformFee, royaltyFee, total }
  }

  if (!listing) return null

  const fees = calculateFees()

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                
                {/* Confirm Step */}
                {step === 'confirm' && (
                  <>
                    {/* Header */}
                    <div className="flex items-center justify-between p-6 border-b border-gray-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                          <ShoppingCartIcon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <Dialog.Title as="h3" className="text-lg font-semibold text-gray-900">
                            Complete Purchase
                          </Dialog.Title>
                          <p className="text-sm text-gray-600">Review and confirm your purchase</p>
                        </div>
                      </div>
                      <button
                        onClick={handleClose}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <XMarkIcon className="h-5 w-5" />
                      </button>
                    </div>

                    <div className="p-6 space-y-6">
                      {/* NFT Preview */}
                      <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                        <img
                          src={listing.nft?.imageUrl || '/api/placeholder/80/80'}
                          alt={listing.title}
                          className="w-20 h-20 rounded-lg object-cover"
                        />
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{listing.title}</h4>
                          <p className="text-sm text-gray-600">by {listing.sellerUsername || 'Unknown'}</p>
                          <p className="text-sm text-gray-500">
                            {listing.nft?.rarity} • Score: {listing.nft?.currentScore}
                          </p>
                        </div>
                      </div>

                      {/* Price Breakdown */}
                      <div className="space-y-3">
                        <h4 className="font-medium text-gray-900">Price Breakdown</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Item Price</span>
                            <span className="font-medium">{listing.price.toFixed(3)} {listing.currency}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Platform Fee (2.5%)</span>
                            <span className="font-medium">{fees.platformFee.toFixed(3)} {listing.currency}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Creator Royalty (5%)</span>
                            <span className="font-medium">{fees.royaltyFee.toFixed(3)} {listing.currency}</span>
                          </div>
                          <div className="border-t border-gray-200 pt-2">
                            <div className="flex justify-between font-semibold">
                              <span>Total</span>
                              <span>{fees.total.toFixed(3)} {listing.currency}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Payment Method */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Payment Method
                        </label>
                        <select
                          value={paymentMethod}
                          onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        >
                          {Object.values(PaymentMethod).map(method => (
                            <option key={method} value={method}>{method}</option>
                          ))}
                        </select>
                      </div>

                      {/* Terms Agreement */}
                      <div className="flex items-start space-x-3">
                        <input
                          type="checkbox"
                          id="terms"
                          checked={agreedToTerms}
                          onChange={(e) => setAgreedToTerms(e.target.checked)}
                          className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <label htmlFor="terms" className="text-sm text-gray-600">
                          I agree to the{' '}
                          <a href="#" className="text-blue-600 hover:text-blue-500">Terms of Service</a>
                          {' '}and understand that this purchase is final and non-refundable.
                        </label>
                      </div>

                      {/* Warning */}
                      <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                        <div className="text-sm text-yellow-800">
                          <p className="font-medium">Important Notice</p>
                          <p>This transaction will be processed on the blockchain and cannot be reversed. Please ensure you have sufficient funds in your wallet.</p>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button
                          onClick={handleClose}
                          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handlePurchase}
                          disabled={!agreedToTerms}
                          className="px-6 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          Purchase Now
                        </button>
                      </div>
                    </div>
                  </>
                )}

                {/* Processing Step */}
                {step === 'processing' && (
                  <div className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-100 flex items-center justify-center">
                      <ClockIcon className="h-8 w-8 text-blue-600 animate-spin" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Processing Purchase</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Your transaction is being processed on the blockchain. This may take a few moments.
                    </p>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                    </div>
                  </div>
                )}

                {/* Success Step */}
                {step === 'success' && (
                  <div className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center">
                      <CheckCircleIcon className="h-8 w-8 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Purchase Successful!</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Congratulations! You have successfully purchased <strong>{listing.title}</strong>.
                    </p>
                    <p className="text-xs text-gray-500">
                      The NFT will appear in your collection once the transaction is confirmed on the blockchain.
                    </p>
                  </div>
                )}

                {/* Error Step */}
                {step === 'error' && (
                  <div className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
                      <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Purchase Failed</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      There was an error processing your purchase. Please try again or contact support if the problem persists.
                    </p>
                    <div className="flex items-center justify-center space-x-3">
                      <button
                        onClick={handleClose}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                      >
                        Close
                      </button>
                      <button
                        onClick={() => setStep('confirm')}
                        className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                      >
                        Try Again
                      </button>
                    </div>
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
