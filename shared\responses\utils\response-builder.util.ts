/**
 * Response Builder Utility
 * Provides utilities for building standardized API responses
 */

import {
  BaseResponse,
  SuccessResponse,
  ErrorResponse,
  ErrorDetails,
  ErrorCode,
  ERROR_CODE_STATUS_MAP,
  ResponseBuilderOptions,
  ErrorResponseBuilderOptions,
  ValidationError,
} from '../interfaces/base-response.interface';
import {
  PaginatedResponse,
  PaginationMeta,
  FilterOptions,
  SortingOptions,
  SearchOptions,
  PaginationResponseBuilderOptions,
} from '../interfaces/pagination.interface';

/**
 * Response Builder Utility Class
 */
export class ResponseBuilder {
  private static defaultService = process.env.SERVICE_NAME || 'unknown-service';
  private static defaultVersion = process.env.SERVICE_VERSION || '1.0.0';

  /**
   * Build a success response
   */
  static success<T>(
    data: T,
    options: ResponseBuilderOptions = {}
  ): SuccessResponse<T> {
    return {
      success: true,
      data,
      message: options.message,
      correlationId: options.correlationId || this.generateCorrelationId(),
      timestamp: new Date().toISOString(),
      service: options.service || this.defaultService,
      version: options.version || this.defaultVersion,
    };
  }

  /**
   * Build an error response
   */
  static error(
    message: string,
    code: ErrorCode = ErrorCode.INTERNAL_SERVER_ERROR,
    options: Partial<ErrorResponseBuilderOptions> = {}
  ): ErrorResponse {
    const statusCode = options.statusCode || ERROR_CODE_STATUS_MAP[code] || 500;

    const errorDetails: ErrorDetails = {
      code,
      message,
      details: options.details,
      stack: this.shouldIncludeStack() ? options.stack : undefined,
      validation: options.validation,
      context: options.context,
    };

    return {
      success: false,
      error: errorDetails,
      statusCode,
      correlationId: options.correlationId || this.generateCorrelationId(),
      timestamp: new Date().toISOString(),
      service: options.service || this.defaultService,
      version: options.version || this.defaultVersion,
    };
  }

  /**
   * Build a validation error response
   */
  static validationError(
    validationErrors: ValidationError[],
    options: Partial<ErrorResponseBuilderOptions> = {}
  ): ErrorResponse {
    return this.error(
      'Validation failed',
      ErrorCode.VALIDATION_ERROR,
      {
        ...options,
        validation: validationErrors,
        statusCode: 422,
      }
    );
  }

  /**
   * Build a not found error response
   */
  static notFound(
    resource: string,
    id?: string,
    options: Partial<ErrorResponseBuilderOptions> = {}
  ): ErrorResponse {
    const message = id 
      ? `${resource} with ID '${id}' not found`
      : `${resource} not found`;

    return this.error(message, ErrorCode.NOT_FOUND, {
      ...options,
      statusCode: 404,
      context: { resource, id },
    });
  }

  /**
   * Build an unauthorized error response
   */
  static unauthorized(
    message: string = 'Authentication required',
    options: Partial<ErrorResponseBuilderOptions> = {}
  ): ErrorResponse {
    return this.error(message, ErrorCode.UNAUTHORIZED, {
      ...options,
      statusCode: 401,
    });
  }

  /**
   * Build a forbidden error response
   */
  static forbidden(
    message: string = 'Access denied',
    options: Partial<ErrorResponseBuilderOptions> = {}
  ): ErrorResponse {
    return this.error(message, ErrorCode.FORBIDDEN, {
      ...options,
      statusCode: 403,
    });
  }

  /**
   * Build a conflict error response
   */
  static conflict(
    message: string,
    options: Partial<ErrorResponseBuilderOptions> = {}
  ): ErrorResponse {
    return this.error(message, ErrorCode.CONFLICT, {
      ...options,
      statusCode: 409,
    });
  }

  /**
   * Build a paginated response
   */
  static paginated<T>(
    data: T[],
    pagination: PaginationMeta,
    options: Partial<PaginationResponseBuilderOptions & ResponseBuilderOptions> = {}
  ): PaginatedResponse<T> {
    return {
      success: true,
      data,
      pagination,
      filters: options.filters,
      sorting: options.sorting,
      search: options.search,
      message: options.message,
      correlationId: options.correlationId || this.generateCorrelationId(),
      timestamp: new Date().toISOString(),
      service: options.service || this.defaultService,
      version: options.version || this.defaultVersion,
    };
  }

  /**
   * Build pagination metadata
   */
  static buildPaginationMeta(
    page: number,
    limit: number,
    totalCount: number
  ): PaginationMeta {
    const totalPages = Math.ceil(totalCount / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      page,
      limit,
      totalCount,
      totalPages,
      hasNext,
      hasPrev,
      nextPage: hasNext ? page + 1 : undefined,
      prevPage: hasPrev ? page - 1 : undefined,
      offset: (page - 1) * limit,
    };
  }

  /**
   * Build a created response (201)
   */
  static created<T>(
    data: T,
    options: ResponseBuilderOptions = {}
  ): SuccessResponse<T> {
    return this.success(data, {
      ...options,
      message: options.message || 'Resource created successfully',
    });
  }

  /**
   * Build an updated response (200)
   */
  static updated<T>(
    data: T,
    options: ResponseBuilderOptions = {}
  ): SuccessResponse<T> {
    return this.success(data, {
      ...options,
      message: options.message || 'Resource updated successfully',
    });
  }

  /**
   * Build a deleted response (200)
   */
  static deleted(
    options: ResponseBuilderOptions = {}
  ): SuccessResponse<{ deleted: boolean }> {
    return this.success({ deleted: true }, {
      ...options,
      message: options.message || 'Resource deleted successfully',
    });
  }

  /**
   * Build a no content response (204)
   */
  static noContent(
    options: ResponseBuilderOptions = {}
  ): SuccessResponse<null> {
    return this.success(null, {
      ...options,
      message: options.message || 'No content',
    });
  }

  /**
   * Build a rate limit exceeded error response
   */
  static rateLimitExceeded(
    retryAfter?: number,
    options: Partial<ErrorResponseBuilderOptions> = {}
  ): ErrorResponse {
    return this.error(
      'Rate limit exceeded',
      ErrorCode.RATE_LIMIT_EXCEEDED,
      {
        ...options,
        statusCode: 429,
        context: { retryAfter },
      }
    );
  }

  /**
   * Build a service unavailable error response
   */
  static serviceUnavailable(
    service: string,
    options: Partial<ErrorResponseBuilderOptions> = {}
  ): ErrorResponse {
    return this.error(
      `Service ${service} is currently unavailable`,
      ErrorCode.SERVICE_UNAVAILABLE,
      {
        ...options,
        statusCode: 503,
        context: { service },
      }
    );
  }

  /**
   * Build a business rule violation error response
   */
  static businessRuleViolation(
    rule: string,
    details?: any,
    options: Partial<ErrorResponseBuilderOptions> = {}
  ): ErrorResponse {
    return this.error(
      `Business rule violation: ${rule}`,
      ErrorCode.BUSINESS_RULE_VIOLATION,
      {
        ...options,
        statusCode: 422,
        details,
        context: { rule },
      }
    );
  }

  /**
   * Generate a correlation ID
   */
  private static generateCorrelationId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `${this.defaultService}_${timestamp}_${random}`;
  }

  /**
   * Check if stack trace should be included
   */
  private static shouldIncludeStack(): boolean {
    const env = process.env.NODE_ENV || 'development';
    return env === 'development' || env === 'test';
  }

  /**
   * Sanitize sensitive data from response
   */
  static sanitizeResponse<T>(response: T, sensitiveFields: string[] = []): T {
    if (!response || typeof response !== 'object') {
      return response;
    }

    const defaultSensitiveFields = [
      'password',
      'secret',
      'token',
      'key',
      'apiKey',
      'privateKey',
      'accessToken',
      'refreshToken',
    ];

    const allSensitiveFields = [...defaultSensitiveFields, ...sensitiveFields];
    
    return this.deepSanitize(response, allSensitiveFields);
  }

  /**
   * Deep sanitize object recursively
   */
  private static deepSanitize(obj: any, sensitiveFields: string[]): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepSanitize(item, sensitiveFields));
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (sensitiveFields.some(field => 
        key.toLowerCase().includes(field.toLowerCase())
      )) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = this.deepSanitize(value, sensitiveFields);
      }
    }

    return sanitized;
  }

  /**
   * Add response headers for caching
   */
  static addCacheHeaders(
    response: any,
    maxAge: number = 300,
    isPrivate: boolean = false
  ): any {
    const cacheControl = isPrivate 
      ? `private, max-age=${maxAge}`
      : `public, max-age=${maxAge}`;

    return {
      ...response,
      headers: {
        'Cache-Control': cacheControl,
        'ETag': this.generateETag(response),
        'Last-Modified': new Date().toUTCString(),
      },
    };
  }

  /**
   * Generate ETag for response
   */
  private static generateETag(response: any): string {
    const content = JSON.stringify(response);
    // Simple hash function for ETag
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `"${Math.abs(hash).toString(16)}"`;
  }
}
