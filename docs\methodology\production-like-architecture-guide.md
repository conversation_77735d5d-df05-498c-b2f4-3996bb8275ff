# Production-like Architecture Guide

## Overview
Comprehensive guide for implementing production-like architecture patterns in the Social NFT Platform.

## Table of Contents
- [Architecture Principles](#architecture-principles)
- [Service Communication](#service-communication)
- [Authentication Flow](#authentication-flow)
- [API Gateway Patterns](#api-gateway-patterns)
- [Development Rules](#development-rules)
- [Implementation Examples](#implementation-examples)

## Architecture Principles

### Core Principles
1. **Production-like Patterns** - Use real architectural patterns without external dependencies
2. **Consistent Implementation** - No mixing of mock and real implementations
3. **Service-Oriented Architecture** - Proper microservices communication
4. **API Gateway First** - All frontend requests through API Gateway
5. **Real Data Flow** - Use actual databases and JWT tokens

### What "Production-like" Means
- ✅ **Real JWT tokens** from User Service
- ✅ **Real database storage** in PostgreSQL
- ✅ **Proper service routing** through API Gateway
- ✅ **Mock external APIs** (Twitter, blockchain) for development
- ✅ **Production authentication patterns** with real token validation

### What to Avoid
- ❌ **Mixed implementations** (real OAuth + mock backend)
- ❌ **Direct service calls** bypassing API Gateway
- ❌ **Mock tokens** with production authentication
- ❌ **localStorage fallbacks** instead of proper validation
- ❌ **Inconsistent data sources** (some mock, some real)

## Service Communication

### Service Architecture
```
Frontend (Next.js) → API Gateway (3010) → Backend Services
                                        ├── User Service (3011)
                                        ├── Profile Analysis (3002)
                                        ├── Project Service (3005)
                                        ├── NFT Service (3003)
                                        └── Other Services...
```

### Communication Rules
1. **Frontend → API Gateway ONLY** - No direct service calls
2. **API Gateway → Services** - Internal service communication
3. **Services → Database** - Each service has dedicated database
4. **Services → Services** - Through internal APIs when needed

### Port Configuration
- **Frontend:** `http://localhost:3000`
- **API Gateway:** `http://localhost:3010`
- **User Service:** `http://localhost:3011`
- **Profile Analysis:** `http://localhost:3002`
- **Project Service:** `http://localhost:3005`
- **NFT Service:** `http://localhost:3003`

### API Endpoint Patterns
- **Authentication:** `GET/POST /api/auth/*` → User Service
- **Profile Analysis:** `GET/POST /api/analysis/*` → Profile Analysis Service
- **Projects:** `GET/POST /api/projects/*` → Project Service
- **NFTs:** `GET/POST /api/nfts/*` → NFT Service

## Authentication Flow

### Production-like Twitter OAuth Flow
```
1. User clicks "Connect Twitter" → Frontend
2. Frontend → API Gateway → Profile Analysis Service (mock Twitter OAuth)
3. Profile Analysis returns mock Twitter data
4. Frontend → API Gateway → User Service (register/login with Twitter data)
5. User Service returns real JWT token
6. Frontend stores JWT token
7. All subsequent requests use JWT token through API Gateway
```

### Authentication Components
- **Frontend:** Twitter OAuth UI components
- **API Gateway:** Route authentication requests
- **Profile Analysis Service:** Mock Twitter OAuth provider
- **User Service:** Real JWT token generation and validation
- **Database:** Real user storage in PostgreSQL

### Token Management
- **Real JWT tokens** generated by User Service
- **Proper token validation** on protected routes
- **Token refresh** mechanism for long sessions
- **Secure token storage** in httpOnly cookies (production) or localStorage (development)

### Protected Routes
- All dashboard and user-specific pages require valid JWT
- API Gateway validates tokens before forwarding requests
- Frontend redirects to login if token invalid/expired

## API Gateway Patterns

### Frontend Configuration
```typescript
// ✅ CORRECT: All requests through API Gateway
export const API_CONFIG = {
  BASE_URL: 'http://localhost:3010', // API Gateway
  ENDPOINTS: {
    AUTH: {
      LOGIN: '/api/auth/login',
      REGISTER: '/api/auth/register',
      PROFILE: '/api/auth/profile'
    },
    NFTS: {
      USER_NFTS: '/api/nfts/user',
      GENERATE: '/api/nfts/generate'
    }
  }
};

// ❌ WRONG: Direct service calls
const WRONG_CONFIG = {
  BASE_URL: 'http://localhost:3011', // Direct to User Service
};
```

### Request Headers
```typescript
// All requests must include JWT token
const headers = {
  'Authorization': `Bearer ${jwtToken}`,
  'Content-Type': 'application/json'
};
```

### Error Handling
- **401 Unauthorized:** Redirect to login
- **403 Forbidden:** Show access denied
- **404 Not Found:** Show not found page
- **500 Server Error:** Show error message with retry option

## Development Rules

### MANDATORY Rules for All Developers and AI Agents

#### 1. API Gateway First Rule
- ✅ **ALL frontend requests** must go through API Gateway (`localhost:3010`)
- ❌ **NEVER** make direct calls to backend services
- ✅ **Configure BASE_URL** to API Gateway in all service files

#### 2. Consistent Implementation Rule
- ✅ **Choose ONE approach:** Either full mock OR full production-like
- ❌ **NEVER mix** real authentication with mock backend
- ✅ **Document the approach** clearly in each component

#### 3. Real Data Flow Rule
- ✅ **Use real JWT tokens** from User Service
- ✅ **Store real data** in PostgreSQL databases
- ✅ **Validate tokens properly** on protected routes
- ❌ **No localStorage fallbacks** for authentication

#### 4. Service Communication Rule
- ✅ **Frontend → API Gateway → Service** (correct flow)
- ❌ **Frontend → Service** (bypasses gateway)
- ✅ **Include JWT token** in all authenticated requests

#### 5. Error Handling Rule
- ✅ **Handle all HTTP status codes** appropriately
- ✅ **Provide user feedback** for all error states
- ✅ **Log errors** for debugging
- ❌ **No silent failures** or unhandled errors

#### 6. Documentation Rule
- ✅ **Document every violation** and solution immediately
- ✅ **Update architecture docs** when patterns change
- ✅ **Create implementation guides** for complex flows

## Implementation Examples

### ✅ CORRECT: Authentication Service
```typescript
// services/authService.ts
export class AuthService {
  private baseURL = 'http://localhost:3010'; // API Gateway

  async login(credentials: LoginData) {
    const response = await axios.post(
      `${this.baseURL}/api/auth/login`, // Through API Gateway
      credentials,
      { headers: { 'Content-Type': 'application/json' } }
    );
    return response.data; // Real JWT token from User Service
  }

  async getProfile() {
    const token = this.getToken();
    const response = await axios.get(
      `${this.baseURL}/api/auth/profile`, // Through API Gateway
      { headers: { 'Authorization': `Bearer ${token}` } }
    );
    return response.data;
  }
}
```

### ❌ WRONG: Direct Service Calls
```typescript
// DON'T DO THIS
export class WrongAuthService {
  private baseURL = 'http://localhost:3011'; // Direct to User Service

  async login(credentials: LoginData) {
    // This bypasses API Gateway - WRONG!
    const response = await axios.post(`${this.baseURL}/auth/login`, credentials);

    // Mock token logic - WRONG!
    if (response.status === 409) {
      localStorage.setItem('mock_token', 'fake_token');
      return { user: mockUser };
    }
  }
}
```
