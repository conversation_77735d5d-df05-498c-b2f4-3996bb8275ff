export enum AnalyticsTimeframe {
  HOUR = '1h',
  DAY = '1d',
  WEEK = '7d',
  MONTH = '30d',
  QUARTER = '90d',
  YEAR = '365d',
  ALL_TIME = 'all'
}

export enum MetricType {
  ENGAGEMENT = 'engagement',
  CONVERSION = 'conversion',
  RETENTION = 'retention',
  REVENUE = 'revenue',
  COST = 'cost',
  PERFORMANCE = 'performance',
  SOCIAL = 'social',
  GEOGRAPHIC = 'geographic'
}

export enum ReportFormat {
  PDF = 'pdf',
  CSV = 'csv',
  EXCEL = 'excel',
  JSON = 'json',
  PNG = 'png',
  SVG = 'svg'
}

export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  PIE = 'pie',
  DOUGHNUT = 'doughnut',
  AREA = 'area',
  SCATTER = 'scatter',
  HEATMAP = 'heatmap',
  FUNNEL = 'funnel',
  GAUGE = 'gauge',
  TABLE = 'table'
}

export interface CampaignAnalytics {
  campaignId: string
  campaignName: string
  timeframe: AnalyticsTimeframe
  
  // Core Metrics
  overview: CampaignOverview
  engagement: EngagementMetrics
  conversion: ConversionMetrics
  retention: RetentionMetrics
  revenue: RevenueMetrics
  costs: CostMetrics
  performance: PerformanceMetrics
  social: SocialMetrics
  geographic: GeographicMetrics
  
  // Time Series Data
  timeSeries: TimeSeriesData[]
  
  // Comparative Data
  benchmarks: BenchmarkData
  
  // Generated At
  generatedAt: string
  lastUpdated: string
}

export interface CampaignOverview {
  // Basic Stats
  totalParticipants: number
  activeParticipants: number
  completedParticipants: number
  dropoffRate: number
  
  // Engagement
  totalEngagements: number
  averageEngagementTime: number
  engagementRate: number
  
  // Completion
  totalCompletions: number
  completionRate: number
  averageCompletionTime: number
  
  // NFTs
  nftsGenerated: number
  nftsMinted: number
  nftsDistributed: number
  
  // Financial
  totalRewardsDistributed: number
  totalCost: number
  roi: number
  
  // Quality
  averageQualityScore: number
  satisfactionScore: number
}

export interface EngagementMetrics {
  // Overall Engagement
  totalSessions: number
  averageSessionDuration: number
  bounceRate: number
  returnVisitorRate: number
  
  // Activity Patterns
  dailyActiveUsers: number
  weeklyActiveUsers: number
  monthlyActiveUsers: number
  
  // Interaction Metrics
  totalClicks: number
  totalShares: number
  totalComments: number
  totalLikes: number
  
  // Content Engagement
  mostEngagedContent: ContentEngagement[]
  leastEngagedContent: ContentEngagement[]
  
  // Time-based Patterns
  peakEngagementHours: number[]
  peakEngagementDays: string[]
  
  // User Journey
  averageStepsToCompletion: number
  commonDropoffPoints: DropoffPoint[]
}

export interface ConversionMetrics {
  // Funnel Analysis
  funnelSteps: FunnelStep[]
  overallConversionRate: number
  
  // Step-by-Step Conversion
  stepConversions: StepConversion[]
  
  // Time to Convert
  averageTimeToConvert: number
  conversionTimeDistribution: TimeDistribution[]
  
  // Conversion Sources
  conversionBySource: SourceConversion[]
  conversionByDevice: DeviceConversion[]
  conversionByLocation: LocationConversion[]
  
  // Quality of Conversions
  highValueConversions: number
  lowValueConversions: number
  conversionQualityScore: number
}

export interface RetentionMetrics {
  // Cohort Analysis
  cohortData: CohortData[]
  
  // Retention Rates
  dayOneRetention: number
  daySevenRetention: number
  dayThirtyRetention: number
  
  // Churn Analysis
  churnRate: number
  churnReasons: ChurnReason[]
  
  // Reactivation
  reactivationRate: number
  reactivationCampaigns: number
  
  // Lifetime Value
  averageLifetimeValue: number
  lifetimeValueDistribution: ValueDistribution[]
}

export interface RevenueMetrics {
  // Revenue Overview
  totalRevenue: number
  averageRevenuePerUser: number
  averageRevenuePerParticipant: number
  
  // Revenue Streams
  revenueBySource: RevenueSource[]
  revenueByProduct: ProductRevenue[]
  revenueByRegion: RegionRevenue[]
  
  // Growth Metrics
  revenueGrowthRate: number
  monthOverMonthGrowth: number
  yearOverYearGrowth: number
  
  // Projections
  projectedRevenue: ProjectedRevenue[]
  revenueForecasts: RevenueForecast[]
}

export interface CostMetrics {
  // Cost Overview
  totalCosts: number
  costPerParticipant: number
  costPerConversion: number
  costPerNFT: number
  
  // Cost Breakdown
  costsByCategory: CostCategory[]
  costsByProvider: ProviderCost[]
  costsByRegion: RegionCost[]
  
  // Efficiency Metrics
  costEfficiencyRatio: number
  budgetUtilization: number
  costOptimizationOpportunities: OptimizationOpportunity[]
  
  // Trends
  costTrends: CostTrend[]
  budgetVariance: BudgetVariance[]
}

export interface PerformanceMetrics {
  // System Performance
  averageResponseTime: number
  uptime: number
  errorRate: number
  
  // Campaign Performance
  campaignEffectiveness: number
  goalAchievementRate: number
  kpiPerformance: KPIPerformance[]
  
  // Quality Metrics
  qualityScore: number
  userSatisfaction: number
  npsScore: number
  
  // Competitive Analysis
  marketPosition: number
  competitorComparison: CompetitorMetric[]
}

export interface SocialMetrics {
  // Social Engagement
  totalSocialShares: number
  socialReach: number
  socialImpressions: number
  socialEngagementRate: number
  
  // Platform Breakdown
  platformMetrics: PlatformMetric[]
  
  // Viral Metrics
  viralityCoefficient: number
  shareRate: number
  viralLoops: number
  
  // Sentiment Analysis
  sentimentScore: number
  positiveReactions: number
  negativeReactions: number
  neutralReactions: number
  
  // Influencer Impact
  influencerReach: number
  influencerEngagement: number
  topInfluencers: InfluencerMetric[]
}

export interface GeographicMetrics {
  // Geographic Distribution
  participantsByCountry: CountryMetric[]
  participantsByRegion: RegionMetric[]
  participantsByCity: CityMetric[]
  
  // Performance by Location
  conversionByCountry: CountryConversion[]
  engagementByRegion: RegionEngagement[]
  
  // Market Penetration
  marketPenetration: MarketPenetration[]
  growthOpportunities: GrowthOpportunity[]
}

export interface TimeSeriesData {
  timestamp: string
  participants: number
  engagements: number
  conversions: number
  revenue: number
  costs: number
  nftsGenerated: number
  socialShares: number
  qualityScore: number
}

export interface BenchmarkData {
  industryAverages: IndustryBenchmark[]
  competitorData: CompetitorBenchmark[]
  historicalComparison: HistoricalBenchmark[]
  goalComparison: GoalBenchmark[]
}

// Supporting Interfaces
export interface ContentEngagement {
  contentId: string
  contentType: string
  title: string
  engagementScore: number
  views: number
  interactions: number
  shareRate: number
}

export interface DropoffPoint {
  step: string
  dropoffRate: number
  participantsLost: number
  commonReasons: string[]
}

export interface FunnelStep {
  step: string
  participants: number
  conversionRate: number
  dropoffRate: number
  averageTime: number
}

export interface StepConversion {
  fromStep: string
  toStep: string
  conversionRate: number
  averageTime: number
  participants: number
}

export interface TimeDistribution {
  timeRange: string
  percentage: number
  count: number
}

export interface SourceConversion {
  source: string
  conversions: number
  conversionRate: number
  quality: number
}

export interface DeviceConversion {
  device: string
  conversions: number
  conversionRate: number
}

export interface LocationConversion {
  location: string
  conversions: number
  conversionRate: number
}

export interface CohortData {
  cohortDate: string
  cohortSize: number
  retentionRates: number[]
  lifetimeValue: number
}

export interface ChurnReason {
  reason: string
  percentage: number
  count: number
  impact: 'high' | 'medium' | 'low'
}

export interface ValueDistribution {
  valueRange: string
  percentage: number
  count: number
}

export interface RevenueSource {
  source: string
  revenue: number
  percentage: number
  growth: number
}

export interface ProductRevenue {
  product: string
  revenue: number
  units: number
  averagePrice: number
}

export interface RegionRevenue {
  region: string
  revenue: number
  participants: number
  averageRevenue: number
}

export interface ProjectedRevenue {
  period: string
  projected: number
  confidence: number
}

export interface RevenueForecast {
  date: string
  forecast: number
  actual?: number
  variance?: number
}

export interface CostCategory {
  category: string
  amount: number
  percentage: number
  trend: number
}

export interface ProviderCost {
  provider: string
  cost: number
  usage: number
  efficiency: number
}

export interface RegionCost {
  region: string
  cost: number
  participants: number
  costPerParticipant: number
}

export interface OptimizationOpportunity {
  area: string
  potentialSavings: number
  effort: 'low' | 'medium' | 'high'
  impact: 'low' | 'medium' | 'high'
  description: string
}

export interface CostTrend {
  period: string
  cost: number
  change: number
  efficiency: number
}

export interface BudgetVariance {
  category: string
  budgeted: number
  actual: number
  variance: number
  variancePercentage: number
}

export interface KPIPerformance {
  kpi: string
  target: number
  actual: number
  achievement: number
  trend: 'up' | 'down' | 'stable'
}

export interface CompetitorMetric {
  competitor: string
  metric: string
  theirValue: number
  ourValue: number
  difference: number
}

export interface PlatformMetric {
  platform: string
  shares: number
  reach: number
  engagement: number
  conversionRate: number
}

export interface InfluencerMetric {
  influencer: string
  reach: number
  engagement: number
  conversions: number
  roi: number
}

export interface CountryMetric {
  country: string
  participants: number
  percentage: number
  growth: number
}

export interface RegionMetric {
  region: string
  participants: number
  percentage: number
  engagement: number
}

export interface CityMetric {
  city: string
  participants: number
  density: number
  engagement: number
}

export interface CountryConversion {
  country: string
  conversionRate: number
  participants: number
  conversions: number
}

export interface RegionEngagement {
  region: string
  engagementRate: number
  averageTime: number
  interactions: number
}

export interface MarketPenetration {
  market: string
  penetration: number
  potential: number
  opportunity: number
}

export interface GrowthOpportunity {
  market: string
  opportunity: number
  effort: 'low' | 'medium' | 'high'
  timeline: string
  description: string
}

export interface IndustryBenchmark {
  metric: string
  industryAverage: number
  ourValue: number
  percentile: number
}

export interface CompetitorBenchmark {
  competitor: string
  metric: string
  theirValue: number
  ourValue: number
  gap: number
}

export interface HistoricalBenchmark {
  metric: string
  previousValue: number
  currentValue: number
  change: number
  trend: 'improving' | 'declining' | 'stable'
}

export interface GoalBenchmark {
  goal: string
  target: number
  actual: number
  achievement: number
  timeRemaining: number
}

// Report Builder Types
export interface ReportTemplate {
  id: string
  name: string
  description: string
  category: string
  metrics: string[]
  charts: ChartConfig[]
  filters: ReportFilter[]
  schedule?: ReportSchedule
  createdAt: string
  updatedAt: string
}

export interface ChartConfig {
  id: string
  type: ChartType
  title: string
  metric: string
  dimensions: string[]
  filters: ChartFilter[]
  styling: ChartStyling
  position: ChartPosition
}

export interface ReportFilter {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'between'
  value: any
  label: string
}

export interface ChartFilter {
  field: string
  value: any
  operator: string
}

export interface ChartStyling {
  colors: string[]
  theme: 'light' | 'dark'
  showLegend: boolean
  showGrid: boolean
  animation: boolean
}

export interface ChartPosition {
  x: number
  y: number
  width: number
  height: number
}

export interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  time: string
  timezone: string
  recipients: string[]
  format: ReportFormat
  enabled: boolean
}

export interface GeneratedReport {
  id: string
  templateId: string
  name: string
  format: ReportFormat
  data: any
  charts: GeneratedChart[]
  metadata: ReportMetadata
  generatedAt: string
  downloadUrl?: string
}

export interface GeneratedChart {
  id: string
  type: ChartType
  title: string
  data: any
  config: ChartConfig
  imageUrl?: string
}

export interface ReportMetadata {
  campaignId: string
  timeframe: AnalyticsTimeframe
  filters: ReportFilter[]
  totalRecords: number
  generationTime: number
  fileSize?: number
}

// Analytics Query Types
export interface AnalyticsQuery {
  campaignIds?: string[]
  timeframe: AnalyticsTimeframe
  startDate?: string
  endDate?: string
  metrics: MetricType[]
  dimensions?: string[]
  filters?: QueryFilter[]
  groupBy?: string[]
  orderBy?: OrderBy[]
  limit?: number
  offset?: number
}

export interface QueryFilter {
  field: string
  operator: string
  value: any
}

export interface OrderBy {
  field: string
  direction: 'asc' | 'desc'
}

export interface AnalyticsResponse {
  data: CampaignAnalytics[]
  total: number
  aggregations?: Record<string, any>
  metadata: ResponseMetadata
}

export interface ResponseMetadata {
  query: AnalyticsQuery
  executionTime: number
  cached: boolean
  cacheExpiry?: string
  dataFreshness: string
}
