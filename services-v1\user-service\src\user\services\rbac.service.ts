import { Injectable, Logger, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../common/services/cache.service';
import { AuditService } from '../../common/services/audit.service';
import { 
  UserRole, 
  Permission, 
  ROLE_PERMISSIONS,
  AssignRoleDto,
  RevokeRoleDto,
  PermissionCheckDto,
  UserRoleResponseDto 
} from '../dto/rbac.dto';
import { RequestContext } from '../interfaces/request-context.interface';

export interface RBACResult {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

@Injectable()
export class RBACService {
  private readonly logger = new Logger(RBACService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
    private readonly auditService: AuditService,
  ) {}

  /**
   * Check if user has specific permission
   */
  async hasPermission(userId: string, permission: Permission, resourceId?: string): Promise<boolean> {
    try {
      this.logger.debug(`Checking permission ${permission} for user ${userId}`, { resourceId });

      // Get user permissions from cache first
      const cacheKey = `user:permissions:${userId}`;
      let userPermissions = await this.cacheService.get<Permission[]>(cacheKey);

      if (!userPermissions) {
        // Get user role and calculate permissions
        const user = await this.prisma.userQuery.findUnique({
          where: { id: userId },
          select: { role: true, isActive: true },
        });

        if (!user || !user.isActive) {
          return false;
        }

        userPermissions = this.getRolePermissions(user.role as UserRole);
        
        // Cache permissions for 5 minutes
        await this.cacheService.set(cacheKey, userPermissions, 300);
      }

      // Check if user has the required permission
      const hasPermission = userPermissions.includes(permission);

      // TODO: Add resource-specific permission checks if resourceId is provided
      // This would involve checking ownership, group membership, etc.

      this.logger.debug(`Permission check result: ${hasPermission}`, {
        userId,
        permission,
        resourceId,
      });

      return hasPermission;

    } catch (error) {
      this.logger.error(`Permission check failed: ${error.message}`, {
        userId,
        permission,
        resourceId,
        error: error.stack,
      });
      return false;
    }
  }

  /**
   * Check if user has any of the specified permissions
   */
  async hasAnyPermission(userId: string, permissions: Permission[], resourceId?: string): Promise<boolean> {
    for (const permission of permissions) {
      if (await this.hasPermission(userId, permission, resourceId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if user has all of the specified permissions
   */
  async hasAllPermissions(userId: string, permissions: Permission[], resourceId?: string): Promise<boolean> {
    for (const permission of permissions) {
      if (!(await this.hasPermission(userId, permission, resourceId))) {
        return false;
      }
    }
    return true;
  }

  /**
   * Get user role and permissions
   */
  async getUserRoleAndPermissions(userId: string): Promise<UserRoleResponseDto | null> {
    try {
      const user = await this.prisma.userQuery.findUnique({
        where: { id: userId },
        select: { 
          role: true, 
          isActive: true,
          createdAt: true,
        },
      });

      if (!user || !user.isActive) {
        return null;
      }

      const role = user.role as UserRole;
      const permissions = this.getRolePermissions(role);

      return {
        userId,
        role,
        permissions,
        assignedAt: user.createdAt.toISOString(),
      };

    } catch (error) {
      this.logger.error(`Failed to get user role and permissions: ${error.message}`, {
        userId,
        error: error.stack,
      });
      return null;
    }
  }

  /**
   * Assign role to user
   */
  async assignRole(assignRoleDto: AssignRoleDto, context: RequestContext): Promise<RBACResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Assigning role ${assignRoleDto.role} to user ${assignRoleDto.userId}`, { correlationId });

      // Check if requesting user has permission to assign roles
      const canAssignRoles = await this.hasPermission(context.userId, Permission.USER_ADMIN);
      if (!canAssignRoles) {
        throw new ForbiddenException('Insufficient permissions to assign roles');
      }

      // Get target user
      const user = await this.prisma.userCommand.findUnique({ 
        where: { id: assignRoleDto.userId } 
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${assignRoleDto.userId} not found`);
      }

      const oldRole = user.role;

      // Update user role
      const updatedUser = await this.prisma.$transaction(async (tx) => {
        // Update command model
        const user = await tx.userCommand.update({
          where: { id: assignRoleDto.userId },
          data: {
            role: assignRoleDto.role,
            updatedBy: context.userId,
            version: { increment: 1 },
          },
        });

        // Update query model
        await tx.userQuery.update({
          where: { id: assignRoleDto.userId },
          data: {
            role: assignRoleDto.role,
            lastUpdated: new Date(),
          },
        });

        return user;
      });

      // Invalidate user permissions cache
      await this.invalidateUserPermissionsCache(assignRoleDto.userId);

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: assignRoleDto.userId,
        action: 'UPDATE',
        resource: 'users',
        oldValues: { role: oldRole },
        newValues: { role: assignRoleDto.role },
        userId: context.userId,
        correlationId: context.correlationId,
        metadata: { 
          action: 'role_assignment',
          reason: assignRoleDto.reason 
        },
      });

      this.logger.log(`Role assigned successfully: ${assignRoleDto.role} to user ${assignRoleDto.userId}`, {
        correlationId,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: {
          userId: assignRoleDto.userId,
          oldRole,
          newRole: assignRoleDto.role,
          permissions: this.getRolePermissions(assignRoleDto.role),
        },
        message: `Role ${assignRoleDto.role} assigned successfully`,
      };

    } catch (error) {
      this.logger.error(`Role assignment failed: ${error.message}`, {
        correlationId,
        assignRoleDto,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }

      return { success: false, error: 'Role assignment failed' };
    }
  }

  /**
   * Revoke role from user (set to default USER role)
   */
  async revokeRole(revokeRoleDto: RevokeRoleDto, context: RequestContext): Promise<RBACResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Revoking role from user ${revokeRoleDto.userId}`, { correlationId });

      // Check if requesting user has permission to revoke roles
      const canRevokeRoles = await this.hasPermission(context.userId, Permission.USER_ADMIN);
      if (!canRevokeRoles) {
        throw new ForbiddenException('Insufficient permissions to revoke roles');
      }

      // Get target user
      const user = await this.prisma.userCommand.findUnique({ 
        where: { id: revokeRoleDto.userId } 
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${revokeRoleDto.userId} not found`);
      }

      const oldRole = user.role;

      // Update user role to default USER role
      const updatedUser = await this.prisma.$transaction(async (tx) => {
        // Update command model
        const user = await tx.userCommand.update({
          where: { id: revokeRoleDto.userId },
          data: {
            role: UserRole.USER,
            updatedBy: context.userId,
            version: { increment: 1 },
          },
        });

        // Update query model
        await tx.userQuery.update({
          where: { id: revokeRoleDto.userId },
          data: {
            role: UserRole.USER,
            lastUpdated: new Date(),
          },
        });

        return user;
      });

      // Invalidate user permissions cache
      await this.invalidateUserPermissionsCache(revokeRoleDto.userId);

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: revokeRoleDto.userId,
        action: 'UPDATE',
        resource: 'users',
        oldValues: { role: oldRole },
        newValues: { role: UserRole.USER },
        userId: context.userId,
        correlationId: context.correlationId,
        metadata: { 
          action: 'role_revocation',
          reason: revokeRoleDto.reason 
        },
      });

      this.logger.log(`Role revoked successfully from user ${revokeRoleDto.userId}`, {
        correlationId,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: {
          userId: revokeRoleDto.userId,
          oldRole,
          newRole: UserRole.USER,
          permissions: this.getRolePermissions(UserRole.USER),
        },
        message: 'Role revoked successfully',
      };

    } catch (error) {
      this.logger.error(`Role revocation failed: ${error.message}`, {
        correlationId,
        revokeRoleDto,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }

      return { success: false, error: 'Role revocation failed' };
    }
  }

  /**
   * Get permissions for a specific role
   */
  getRolePermissions(role: UserRole): Permission[] {
    return ROLE_PERMISSIONS[role] || [];
  }

  /**
   * Invalidate user permissions cache
   */
  async invalidateUserPermissionsCache(userId: string): Promise<void> {
    const cacheKey = `user:permissions:${userId}`;
    await this.cacheService.del(cacheKey);
    this.logger.debug(`Invalidated permissions cache for user: ${userId}`);
  }

  /**
   * Check if user can perform action on resource
   */
  async canPerformAction(
    userId: string, 
    action: string, 
    resource: string, 
    resourceId?: string
  ): Promise<boolean> {
    // Map action and resource to permission
    const permission = this.mapActionToPermission(action, resource);
    if (!permission) {
      return false;
    }

    return this.hasPermission(userId, permission, resourceId);
  }

  /**
   * Map action and resource to permission
   */
  private mapActionToPermission(action: string, resource: string): Permission | null {
    const permissionKey = `${resource.toUpperCase()}:${action.toUpperCase()}`;
    return Object.values(Permission).find(p => p === permissionKey.toLowerCase()) || null;
  }
}
