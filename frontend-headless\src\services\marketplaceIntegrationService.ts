import { api } from '@/lib/api'
import {
  CampaignMarketplaceListing,
  NFTCollectionListing,
  MarketplaceFilters,
  BulkOperation,
  BulkOperationRequest,
  MarketplaceDiscovery,
  CampaignIntegrationSettings,
  MarketplaceTransaction,
  CampaignMarketplaceAnalytics,
  CreateMarketplaceListingRequest,
  UpdateMarketplaceListingRequest,
  MarketplaceSearchRequest,
  MarketplaceSearchResponse,
  MarketplaceStatus,
  SortOption
} from '@/types/marketplace-integration.types'

export class MarketplaceIntegrationService {
  // ===== CAMPAIGN MARKETPLACE LISTINGS =====
  
  async getCampaignListings(filters?: MarketplaceFilters): Promise<{
    listings: CampaignMarketplaceListing[]
    total: number
    page: number
    limit: number
    hasMore: boolean
  }> {
    try {
      const response = await api.get('/marketplace/campaigns', { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign listings:', error)
      throw new Error('Failed to load campaign listings')
    }
  }

  async getCampaignListing(id: string): Promise<CampaignMarketplaceListing> {
    try {
      const response = await api.get(`/marketplace/campaigns/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign listing:', error)
      throw new Error('Failed to load campaign listing details')
    }
  }

  async createCampaignListing(data: CreateMarketplaceListingRequest): Promise<CampaignMarketplaceListing> {
    try {
      const response = await api.post('/marketplace/campaigns', data)
      return response.data
    } catch (error) {
      console.error('Failed to create campaign listing:', error)
      throw new Error('Failed to create campaign listing')
    }
  }

  async updateCampaignListing(id: string, data: UpdateMarketplaceListingRequest): Promise<CampaignMarketplaceListing> {
    try {
      const response = await api.patch(`/marketplace/campaigns/${id}`, data)
      return response.data
    } catch (error) {
      console.error('Failed to update campaign listing:', error)
      throw new Error('Failed to update campaign listing')
    }
  }

  async deleteCampaignListing(id: string): Promise<void> {
    try {
      await api.delete(`/marketplace/campaigns/${id}`)
    } catch (error) {
      console.error('Failed to delete campaign listing:', error)
      throw new Error('Failed to delete campaign listing')
    }
  }

  // ===== LISTING MANAGEMENT =====

  async publishListing(id: string): Promise<CampaignMarketplaceListing> {
    try {
      const response = await api.post(`/marketplace/campaigns/${id}/publish`)
      return response.data
    } catch (error) {
      console.error('Failed to publish listing:', error)
      throw new Error('Failed to publish listing')
    }
  }

  async unpublishListing(id: string): Promise<CampaignMarketplaceListing> {
    try {
      const response = await api.post(`/marketplace/campaigns/${id}/unpublish`)
      return response.data
    } catch (error) {
      console.error('Failed to unpublish listing:', error)
      throw new Error('Failed to unpublish listing')
    }
  }

  async featureListing(id: string, duration: number): Promise<CampaignMarketplaceListing> {
    try {
      const response = await api.post(`/marketplace/campaigns/${id}/feature`, { duration })
      return response.data
    } catch (error) {
      console.error('Failed to feature listing:', error)
      throw new Error('Failed to feature listing')
    }
  }

  async updateListingStatus(id: string, status: MarketplaceStatus): Promise<CampaignMarketplaceListing> {
    try {
      const response = await api.patch(`/marketplace/campaigns/${id}/status`, { status })
      return response.data
    } catch (error) {
      console.error('Failed to update listing status:', error)
      throw new Error('Failed to update listing status')
    }
  }

  // ===== NFT COLLECTIONS =====

  async getCollectionListings(campaignId?: string): Promise<NFTCollectionListing[]> {
    try {
      const response = await api.get('/marketplace/collections', {
        params: campaignId ? { campaignId } : undefined
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch collection listings:', error)
      throw new Error('Failed to load collection listings')
    }
  }

  async getCollectionListing(id: string): Promise<NFTCollectionListing> {
    try {
      const response = await api.get(`/marketplace/collections/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch collection listing:', error)
      throw new Error('Failed to load collection listing details')
    }
  }

  async createCollectionListing(data: {
    campaignId: string
    collectionName: string
    description: string
    basePrice: number
    royaltyPercentage: number
  }): Promise<NFTCollectionListing> {
    try {
      const response = await api.post('/marketplace/collections', data)
      return response.data
    } catch (error) {
      console.error('Failed to create collection listing:', error)
      throw new Error('Failed to create collection listing')
    }
  }

  async updateCollectionListing(id: string, data: Partial<NFTCollectionListing>): Promise<NFTCollectionListing> {
    try {
      const response = await api.patch(`/marketplace/collections/${id}`, data)
      return response.data
    } catch (error) {
      console.error('Failed to update collection listing:', error)
      throw new Error('Failed to update collection listing')
    }
  }

  // ===== MARKETPLACE SEARCH & DISCOVERY =====

  async searchMarketplace(request: MarketplaceSearchRequest): Promise<MarketplaceSearchResponse> {
    try {
      const response = await api.post('/marketplace/search', request)
      return response.data
    } catch (error) {
      console.error('Failed to search marketplace:', error)
      throw new Error('Failed to search marketplace')
    }
  }

  async getMarketplaceDiscovery(): Promise<MarketplaceDiscovery> {
    try {
      const response = await api.get('/marketplace/discovery')
      return response.data
    } catch (error) {
      console.error('Failed to fetch marketplace discovery:', error)
      throw new Error('Failed to load marketplace discovery')
    }
  }

  async getFeaturedListings(): Promise<CampaignMarketplaceListing[]> {
    try {
      const response = await api.get('/marketplace/featured')
      return response.data
    } catch (error) {
      console.error('Failed to fetch featured listings:', error)
      throw new Error('Failed to load featured listings')
    }
  }

  async getTrendingCollections(): Promise<NFTCollectionListing[]> {
    try {
      const response = await api.get('/marketplace/trending')
      return response.data
    } catch (error) {
      console.error('Failed to fetch trending collections:', error)
      throw new Error('Failed to load trending collections')
    }
  }

  async getRecommendations(userId?: string): Promise<CampaignMarketplaceListing[]> {
    try {
      const response = await api.get('/marketplace/recommendations', {
        params: userId ? { userId } : undefined
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch recommendations:', error)
      throw new Error('Failed to load recommendations')
    }
  }

  // ===== BULK OPERATIONS =====

  async getBulkOperations(): Promise<BulkOperation[]> {
    try {
      const response = await api.get('/marketplace/bulk-operations')
      return response.data
    } catch (error) {
      console.error('Failed to fetch bulk operations:', error)
      throw new Error('Failed to load bulk operations')
    }
  }

  async getBulkOperation(id: string): Promise<BulkOperation> {
    try {
      const response = await api.get(`/marketplace/bulk-operations/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch bulk operation:', error)
      throw new Error('Failed to load bulk operation details')
    }
  }

  async createBulkOperation(request: BulkOperationRequest): Promise<BulkOperation> {
    try {
      const response = await api.post('/marketplace/bulk-operations', request)
      return response.data
    } catch (error) {
      console.error('Failed to create bulk operation:', error)
      throw new Error('Failed to create bulk operation')
    }
  }

  async executeBulkOperation(id: string): Promise<BulkOperation> {
    try {
      const response = await api.post(`/marketplace/bulk-operations/${id}/execute`)
      return response.data
    } catch (error) {
      console.error('Failed to execute bulk operation:', error)
      throw new Error('Failed to execute bulk operation')
    }
  }

  async cancelBulkOperation(id: string): Promise<BulkOperation> {
    try {
      const response = await api.post(`/marketplace/bulk-operations/${id}/cancel`)
      return response.data
    } catch (error) {
      console.error('Failed to cancel bulk operation:', error)
      throw new Error('Failed to cancel bulk operation')
    }
  }

  async deleteBulkOperation(id: string): Promise<void> {
    try {
      await api.delete(`/marketplace/bulk-operations/${id}`)
    } catch (error) {
      console.error('Failed to delete bulk operation:', error)
      throw new Error('Failed to delete bulk operation')
    }
  }

  // ===== INTEGRATION SETTINGS =====

  async getCampaignIntegrationSettings(campaignId: string): Promise<CampaignIntegrationSettings> {
    try {
      const response = await api.get(`/marketplace/campaigns/${campaignId}/integration-settings`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch integration settings:', error)
      throw new Error('Failed to load integration settings')
    }
  }

  async updateCampaignIntegrationSettings(
    campaignId: string, 
    settings: Partial<CampaignIntegrationSettings>
  ): Promise<CampaignIntegrationSettings> {
    try {
      const response = await api.patch(`/marketplace/campaigns/${campaignId}/integration-settings`, settings)
      return response.data
    } catch (error) {
      console.error('Failed to update integration settings:', error)
      throw new Error('Failed to update integration settings')
    }
  }

  async enableAutoListing(campaignId: string): Promise<CampaignIntegrationSettings> {
    try {
      const response = await api.post(`/marketplace/campaigns/${campaignId}/enable-auto-listing`)
      return response.data
    } catch (error) {
      console.error('Failed to enable auto listing:', error)
      throw new Error('Failed to enable auto listing')
    }
  }

  async disableAutoListing(campaignId: string): Promise<CampaignIntegrationSettings> {
    try {
      const response = await api.post(`/marketplace/campaigns/${campaignId}/disable-auto-listing`)
      return response.data
    } catch (error) {
      console.error('Failed to disable auto listing:', error)
      throw new Error('Failed to disable auto listing')
    }
  }

  // ===== TRANSACTIONS =====

  async getMarketplaceTransactions(filters?: {
    campaignId?: string
    itemType?: string
    status?: string
    startDate?: string
    endDate?: string
    page?: number
    limit?: number
  }): Promise<{
    transactions: MarketplaceTransaction[]
    total: number
    page: number
    limit: number
  }> {
    try {
      const response = await api.get('/marketplace/transactions', { params: filters })
      return response.data
    } catch (error) {
      console.error('Failed to fetch marketplace transactions:', error)
      throw new Error('Failed to load marketplace transactions')
    }
  }

  async getTransaction(id: string): Promise<MarketplaceTransaction> {
    try {
      const response = await api.get(`/marketplace/transactions/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch transaction:', error)
      throw new Error('Failed to load transaction details')
    }
  }

  // ===== ANALYTICS =====

  async getCampaignMarketplaceAnalytics(
    campaignId: string, 
    timeframe: string = '30d'
  ): Promise<CampaignMarketplaceAnalytics> {
    try {
      const response = await api.get(`/marketplace/campaigns/${campaignId}/analytics`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch campaign marketplace analytics:', error)
      throw new Error('Failed to load campaign marketplace analytics')
    }
  }

  async getMarketplaceOverview(): Promise<{
    totalListings: number
    totalVolume: number
    activeUsers: number
    averagePrice: number
    topCategories: Array<{ category: string; count: number; volume: number }>
    recentSales: MarketplaceTransaction[]
  }> {
    try {
      const response = await api.get('/marketplace/overview')
      return response.data
    } catch (error) {
      console.error('Failed to fetch marketplace overview:', error)
      throw new Error('Failed to load marketplace overview')
    }
  }

  // ===== FAVORITES & WATCHLIST =====

  async addToFavorites(listingId: string): Promise<void> {
    try {
      await api.post(`/marketplace/campaigns/${listingId}/favorite`)
    } catch (error) {
      console.error('Failed to add to favorites:', error)
      throw new Error('Failed to add to favorites')
    }
  }

  async removeFromFavorites(listingId: string): Promise<void> {
    try {
      await api.delete(`/marketplace/campaigns/${listingId}/favorite`)
    } catch (error) {
      console.error('Failed to remove from favorites:', error)
      throw new Error('Failed to remove from favorites')
    }
  }

  async getFavorites(): Promise<CampaignMarketplaceListing[]> {
    try {
      const response = await api.get('/marketplace/favorites')
      return response.data
    } catch (error) {
      console.error('Failed to fetch favorites:', error)
      throw new Error('Failed to load favorites')
    }
  }

  // ===== SOCIAL FEATURES =====

  async shareListing(listingId: string, platform: string): Promise<{ shareUrl: string }> {
    try {
      const response = await api.post(`/marketplace/campaigns/${listingId}/share`, { platform })
      return response.data
    } catch (error) {
      console.error('Failed to share listing:', error)
      throw new Error('Failed to share listing')
    }
  }

  async reportListing(listingId: string, reason: string, details?: string): Promise<void> {
    try {
      await api.post(`/marketplace/campaigns/${listingId}/report`, { reason, details })
    } catch (error) {
      console.error('Failed to report listing:', error)
      throw new Error('Failed to report listing')
    }
  }

  // ===== UTILITY METHODS =====

  async validateListingData(data: CreateMarketplaceListingRequest): Promise<{
    valid: boolean
    errors: string[]
    warnings: string[]
    suggestions: string[]
  }> {
    try {
      const response = await api.post('/marketplace/validate-listing', data)
      return response.data
    } catch (error) {
      console.error('Failed to validate listing data:', error)
      throw new Error('Failed to validate listing data')
    }
  }

  async estimateListingFees(data: {
    basePrice: number
    royaltyPercentage: number
    listingType: string
    duration?: number
  }): Promise<{
    platformFee: number
    royaltyAmount: number
    totalFees: number
    netAmount: number
    breakdown: Record<string, number>
  }> {
    try {
      const response = await api.post('/marketplace/estimate-fees', data)
      return response.data
    } catch (error) {
      console.error('Failed to estimate listing fees:', error)
      throw new Error('Failed to estimate listing fees')
    }
  }

  async getMarketplaceTrends(): Promise<{
    priceHistory: Array<{ date: string; averagePrice: number }>
    volumeHistory: Array<{ date: string; volume: number }>
    categoryTrends: Array<{ category: string; growth: number }>
    popularTags: Array<{ tag: string; count: number }>
  }> {
    try {
      const response = await api.get('/marketplace/trends')
      return response.data
    } catch (error) {
      console.error('Failed to fetch marketplace trends:', error)
      throw new Error('Failed to load marketplace trends')
    }
  }

  async exportMarketplaceData(filters?: MarketplaceFilters, format: 'csv' | 'json' = 'csv'): Promise<Blob> {
    try {
      const response = await api.post('/marketplace/export', {
        filters,
        format
      }, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to export marketplace data:', error)
      throw new Error('Failed to export marketplace data')
    }
  }
}

export const marketplaceIntegrationService = new MarketplaceIntegrationService()
