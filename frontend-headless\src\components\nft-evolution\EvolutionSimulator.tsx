'use client'

import React, { useState } from 'react'
import {
  EyeIcon,
  PlayIcon,
  CogIcon,
  SparklesIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import {
  useSimulateEvolution,
  useEstimateEvolutionCosts,
  useCreateEvolution,
  usePreviewTraitChanges,
  useGenerateVisualPreview
} from '@/hooks/useNFTEvolution'
import { EvolutionTrigger, TraitChange, VisualChange } from '@/types/nft-evolution.types'

interface EvolutionSimulatorProps {
  nftId: string
  availableTriggers?: EvolutionTrigger[]
  onSimulationComplete?: (evolutionId: string) => void
  className?: string
}

export default function EvolutionSimulator({
  nftId,
  availableTriggers = [],
  onSimulationComplete,
  className = ''
}: EvolutionSimulatorProps) {
  const [selectedTrigger, setSelectedTrigger] = useState<EvolutionTrigger | null>(null)
  const [customizations, setCustomizations] = useState<Record<string, any>>({})
  const [simulationStep, setSimulationStep] = useState<'select' | 'customize' | 'preview' | 'confirm'>('select')
  const [previewData, setPreviewData] = useState<any>(null)

  const simulateEvolutionMutation = useSimulateEvolution()
  const estimateCostsMutation = useEstimateEvolutionCosts()
  const createEvolutionMutation = useCreateEvolution()
  const previewTraitsMutation = usePreviewTraitChanges()
  const generateVisualPreviewMutation = useGenerateVisualPreview()

  const handleTriggerSelect = async (trigger: EvolutionTrigger) => {
    setSelectedTrigger(trigger)
    setSimulationStep('customize')
    
    // Estimate costs immediately
    try {
      await estimateCostsMutation.mutateAsync({
        nftId,
        triggerId: trigger.id
      })
    } catch (error) {
      console.error('Failed to estimate costs:', error)
    }
  }

  const handleRunSimulation = async () => {
    if (!selectedTrigger) return

    try {
      const simulation = await simulateEvolutionMutation.mutateAsync({
        nftId,
        triggerId: selectedTrigger.id,
        customizations
      })
      
      setPreviewData(simulation)
      setSimulationStep('preview')
    } catch (error) {
      console.error('Failed to run simulation:', error)
    }
  }

  const handleConfirmEvolution = async () => {
    if (!selectedTrigger) return

    try {
      const evolution = await createEvolutionMutation.mutateAsync({
        nftId,
        triggerId: selectedTrigger.id,
        customizations
      })
      
      onSimulationComplete?.(evolution.id)
      resetSimulator()
    } catch (error) {
      console.error('Failed to create evolution:', error)
    }
  }

  const resetSimulator = () => {
    setSelectedTrigger(null)
    setCustomizations({})
    setSimulationStep('select')
    setPreviewData(null)
  }

  const renderTriggerSelection = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Select Evolution Trigger</h3>
        <p className="text-sm text-gray-600">Choose a trigger to simulate its effects</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {availableTriggers.map((trigger) => (
          <div
            key={trigger.id}
            onClick={() => handleTriggerSelect(trigger)}
            className="border border-gray-200 rounded-lg p-4 hover:border-purple-300 hover:bg-purple-50 cursor-pointer transition-all"
          >
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-900">{trigger.name}</h4>
              <span className="text-xs text-gray-500">Priority: {trigger.priority}</span>
            </div>
            
            <p className="text-xs text-gray-600 mb-3">{trigger.description}</p>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-purple-600">{trigger.type.replace('_', ' ')}</span>
              <button className="text-xs text-purple-600 hover:text-purple-700">
                Select →
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderCustomization = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Customize Evolution</h3>
          <p className="text-sm text-gray-600">Configure parameters for {selectedTrigger?.name}</p>
        </div>
        <button
          onClick={() => setSimulationStep('select')}
          className="text-sm text-gray-600 hover:text-gray-700"
        >
          ← Back to Selection
        </button>
      </div>

      {/* Trigger Details */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-purple-900 mb-2">Selected Trigger</h4>
        <div className="space-y-1 text-sm text-purple-700">
          <div><strong>Name:</strong> {selectedTrigger?.name}</div>
          <div><strong>Type:</strong> {selectedTrigger?.type.replace('_', ' ')}</div>
          <div><strong>Description:</strong> {selectedTrigger?.description}</div>
        </div>
      </div>

      {/* Cost Estimation */}
      {estimateCostsMutation.data && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Estimated Costs</h4>
          <div className="grid grid-cols-2 gap-4 text-sm text-blue-700">
            <div>
              <span className="font-medium">Gas Fee:</span>
              <span className="ml-1">${estimateCostsMutation.data.gasFee.toFixed(4)}</span>
            </div>
            <div>
              <span className="font-medium">Service Fee:</span>
              <span className="ml-1">${estimateCostsMutation.data.serviceFee.toFixed(4)}</span>
            </div>
            <div className="col-span-2">
              <span className="font-medium">Total Cost:</span>
              <span className="ml-1 text-lg">${estimateCostsMutation.data.totalCost.toFixed(4)} {estimateCostsMutation.data.currency}</span>
            </div>
          </div>
        </div>
      )}

      {/* Customization Options */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-900">Customization Options</h4>
        
        {/* Example customization fields - would be dynamic based on trigger */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Evolution Intensity
            </label>
            <select
              value={customizations.intensity || 'medium'}
              onChange={(e) => setCustomizations(prev => ({ ...prev, intensity: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Focus Area
            </label>
            <select
              value={customizations.focusArea || 'balanced'}
              onChange={(e) => setCustomizations(prev => ({ ...prev, focusArea: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="balanced">Balanced</option>
              <option value="visual">Visual Changes</option>
              <option value="traits">Trait Enhancement</option>
              <option value="rarity">Rarity Boost</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Risk Level
            </label>
            <select
              value={customizations.riskLevel || 'medium'}
              onChange={(e) => setCustomizations(prev => ({ ...prev, riskLevel: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="conservative">Conservative</option>
              <option value="medium">Medium</option>
              <option value="aggressive">Aggressive</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Style Preference
            </label>
            <select
              value={customizations.stylePreference || 'auto'}
              onChange={(e) => setCustomizations(prev => ({ ...prev, stylePreference: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="auto">Auto-Select</option>
              <option value="futuristic">Futuristic</option>
              <option value="classic">Classic</option>
              <option value="artistic">Artistic</option>
              <option value="minimal">Minimal</option>
            </select>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-3">
        <button
          onClick={resetSimulator}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleRunSimulation}
          disabled={simulateEvolutionMutation.isPending}
          className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
        >
          {simulateEvolutionMutation.isPending ? (
            <>
              <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              Simulating...
            </>
          ) : (
            <>
              <EyeIcon className="h-4 w-4 mr-2" />
              Run Simulation
            </>
          )}
        </button>
      </div>
    </div>
  )

  const renderPreview = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Evolution Preview</h3>
          <p className="text-sm text-gray-600">Review the simulated evolution results</p>
        </div>
        <button
          onClick={() => setSimulationStep('customize')}
          className="text-sm text-gray-600 hover:text-gray-700"
        >
          ← Back to Customize
        </button>
      </div>

      {previewData && (
        <>
          {/* Visual Preview */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-sm font-medium text-gray-900 mb-4">Visual Preview</h4>
            <div className="flex items-center justify-center">
              <img
                src={previewData.estimatedOutcome.visualPreview}
                alt="Evolution Preview"
                className="max-w-sm w-full h-64 object-cover rounded-lg border border-gray-200"
              />
            </div>
          </div>

          {/* Impact Summary */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
              <SparklesIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-lg font-bold text-purple-900">
                +{previewData.estimatedOutcome.rarityChange.toFixed(1)}
              </div>
              <div className="text-sm text-purple-700">Rarity Increase</div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
              <CurrencyDollarIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-lg font-bold text-green-900">
                +{previewData.estimatedOutcome.valueChange.toFixed(1)}%
              </div>
              <div className="text-sm text-green-700">Value Increase</div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
              <ChartBarIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-lg font-bold text-blue-900">
                {(previewData.estimatedOutcome.successProbability * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-blue-700">Success Rate</div>
            </div>

            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
              <CurrencyDollarIcon className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-lg font-bold text-orange-900">
                ${previewData.costs.totalCost.toFixed(4)}
              </div>
              <div className="text-sm text-orange-700">Total Cost</div>
            </div>
          </div>

          {/* Risk Assessment */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2" />
              <h4 className="text-sm font-medium text-yellow-900">Risk Assessment</h4>
            </div>
            <div className="text-sm text-yellow-700">
              <p>
                This evolution has a {(previewData.estimatedOutcome.successProbability * 100).toFixed(1)}% 
                chance of success. The estimated value increase is {previewData.estimatedOutcome.valueChange.toFixed(1)}%, 
                but results may vary based on market conditions and community reception.
              </p>
            </div>
          </div>

          {/* Detailed Breakdown */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-sm font-medium text-gray-900 mb-4">Cost Breakdown</h4>
            <div className="space-y-2">
              {Object.entries(previewData.costs.breakdown || {}).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 capitalize">{key.replace('_', ' ')}:</span>
                  <span className="font-medium text-gray-900">${(value as number).toFixed(4)}</span>
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-3">
        <button
          onClick={resetSimulator}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={() => setSimulationStep('confirm')}
          className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
        >
          <CheckCircleIcon className="h-4 w-4 mr-2" />
          Proceed with Evolution
        </button>
      </div>
    </div>
  )

  const renderConfirmation = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Confirm Evolution</h3>
        <p className="text-sm text-gray-600">Final confirmation before executing the evolution</p>
      </div>

      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center mb-2">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-2" />
          <h4 className="text-sm font-medium text-red-900">Important Notice</h4>
        </div>
        <div className="text-sm text-red-700">
          <p>
            This evolution will permanently modify your NFT. While some evolutions may be reversible, 
            this action cannot be undone immediately. Please ensure you are satisfied with the preview 
            before proceeding.
          </p>
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h4 className="text-sm font-medium text-gray-900 mb-4">Evolution Summary</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Trigger:</span>
            <span className="font-medium">{selectedTrigger?.name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Expected Value Change:</span>
            <span className="font-medium text-green-600">
              +{previewData?.estimatedOutcome.valueChange.toFixed(1)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Success Probability:</span>
            <span className="font-medium">
              {(previewData?.estimatedOutcome.successProbability * 100).toFixed(1)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Total Cost:</span>
            <span className="font-medium">${previewData?.costs.totalCost.toFixed(4)}</span>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-end space-x-3">
        <button
          onClick={() => setSimulationStep('preview')}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          ← Back to Preview
        </button>
        <button
          onClick={handleConfirmEvolution}
          disabled={createEvolutionMutation.isPending}
          className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
        >
          {createEvolutionMutation.isPending ? (
            <>
              <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              Creating Evolution...
            </>
          ) : (
            <>
              <PlayIcon className="h-4 w-4 mr-2" />
              Confirm & Execute
            </>
          )}
        </button>
      </div>
    </div>
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {simulationStep === 'select' && renderTriggerSelection()}
      {simulationStep === 'customize' && renderCustomization()}
      {simulationStep === 'preview' && renderPreview()}
      {simulationStep === 'confirm' && renderConfirmation()}
    </div>
  )
}
