import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

export interface NotificationTemplate {
  id?: string;
  name: string;
  eventType: string;
  channels: string[];
  priority: string;
  category: string;
  templates: {
    email?: {
      subject: string;
      html: string;
      text: string;
    };
    sms?: {
      message: string;
    };
    push?: {
      title: string;
      body: string;
    };
    inApp?: {
      title: string;
      message: string;
    };
  };
  conditions?: Record<string, any>;
  isActive: boolean;
}

@Injectable()
export class TemplateService {
  private readonly logger = new Logger(TemplateService.name);

  constructor(private readonly prisma: PrismaService) {}

  async createTemplate(template: NotificationTemplate): Promise<NotificationTemplate> {
    try {
      this.logger.log(`Creating notification template: ${template.name}`);

      // For now, return mock data since database might not be available
      const mockTemplate: NotificationTemplate = {
        id: `template_${Date.now()}`,
        ...template,
      };

      this.logger.log(`Template created successfully: ${mockTemplate.id}`);
      return mockTemplate;

      // Real implementation would be:
      // const created = await this.prisma.notificationTemplate.create({
      //   data: {
      //     name: template.name,
      //     eventType: template.eventType,
      //     channels: template.channels,
      //     priority: template.priority,
      //     category: template.category,
      //     templates: template.templates,
      //     conditions: template.conditions,
      //     isActive: template.isActive,
      //   },
      // });
      // return created;
    } catch (error) {
      this.logger.error(`Failed to create template: ${error.message}`, error);
      throw error;
    }
  }

  async getTemplate(id: string): Promise<NotificationTemplate | null> {
    try {
      this.logger.log(`Getting template: ${id}`);

      // Mock implementation
      const mockTemplate: NotificationTemplate = {
        id: id,
        name: 'Welcome Email Template',
        eventType: 'user_registered',
        channels: ['email'],
        priority: 'medium',
        category: 'account',
        templates: {
          email: {
            subject: 'Welcome to Social NFT Platform!',
            html: '<h1>Welcome!</h1><p>Thank you for joining our platform.</p>',
            text: 'Welcome! Thank you for joining our platform.',
          },
        },
        isActive: true,
      };

      return mockTemplate;

      // Real implementation would be:
      // return await this.prisma.notificationTemplate.findUnique({
      //   where: { id },
      // });
    } catch (error) {
      this.logger.error(`Failed to get template: ${error.message}`, error);
      return null;
    }
  }

  async getTemplateByEventType(eventType: string): Promise<NotificationTemplate | null> {
    try {
      this.logger.log(`Getting template for event type: ${eventType}`);

      // Mock implementation with different templates based on event type
      const mockTemplates: Record<string, NotificationTemplate> = {
        user_registered: {
          id: 'template_welcome',
          name: 'Welcome Email Template',
          eventType: 'user_registered',
          channels: ['email', 'push'],
          priority: 'medium',
          category: 'account',
          templates: {
            email: {
              subject: 'Welcome to Social NFT Platform!',
              html: '<h1>Welcome {{userName}}!</h1><p>Thank you for joining our platform.</p>',
              text: 'Welcome {{userName}}! Thank you for joining our platform.',
            },
            push: {
              title: 'Welcome!',
              body: 'Welcome to Social NFT Platform, {{userName}}!',
            },
          },
          isActive: true,
        },
        nft_generated: {
          id: 'template_nft_generated',
          name: 'NFT Generated Template',
          eventType: 'nft_generated',
          channels: ['email', 'push', 'sms'],
          priority: 'high',
          category: 'nft',
          templates: {
            email: {
              subject: 'Your NFT is Ready!',
              html: '<h1>NFT Generated!</h1><p>Your unique NFT ({{nftId}}) has been created.</p>',
              text: 'NFT Generated! Your unique NFT ({{nftId}}) has been created.',
            },
            push: {
              title: 'NFT Generated!',
              body: 'Your unique NFT has been successfully created.',
            },
            sms: {
              message: 'Your Social NFT Platform NFT ({{nftId}}) is ready!',
            },
          },
          isActive: true,
        },
        bid_received: {
          id: 'template_bid_received',
          name: 'Bid Received Template',
          eventType: 'bid_received',
          channels: ['email', 'push'],
          priority: 'high',
          category: 'marketplace',
          templates: {
            email: {
              subject: 'New Bid on Your NFT!',
              html: '<h1>New Bid!</h1><p>Someone bid {{bidAmount}} on your NFT "{{nftTitle}}".</p>',
              text: 'New Bid! Someone bid {{bidAmount}} on your NFT "{{nftTitle}}".',
            },
            push: {
              title: 'New Bid Received!',
              body: 'Someone placed a bid of {{bidAmount}} on your NFT.',
            },
          },
          isActive: true,
        },
      };

      return mockTemplates[eventType] || null;

      // Real implementation would be:
      // return await this.prisma.notificationTemplate.findFirst({
      //   where: { 
      //     eventType,
      //     isActive: true,
      //   },
      // });
    } catch (error) {
      this.logger.error(`Failed to get template by event type: ${error.message}`, error);
      return null;
    }
  }

  async updateTemplate(id: string, updates: Partial<NotificationTemplate>): Promise<NotificationTemplate> {
    try {
      this.logger.log(`Updating template: ${id}`);

      // Mock implementation
      const existingTemplate = await this.getTemplate(id);
      if (!existingTemplate) {
        throw new Error('Template not found');
      }

      const updatedTemplate: NotificationTemplate = {
        ...existingTemplate,
        ...updates,
        id: existingTemplate.id,
      };

      this.logger.log(`Template updated successfully: ${id}`);
      return updatedTemplate;

      // Real implementation would be:
      // return await this.prisma.notificationTemplate.update({
      //   where: { id },
      //   data: updates,
      // });
    } catch (error) {
      this.logger.error(`Failed to update template: ${error.message}`, error);
      throw error;
    }
  }

  async deleteTemplate(id: string): Promise<boolean> {
    try {
      this.logger.log(`Deleting template: ${id}`);

      // Mock implementation
      this.logger.log(`Template deleted successfully: ${id}`);
      return true;

      // Real implementation would be:
      // await this.prisma.notificationTemplate.delete({
      //   where: { id },
      // });
      // return true;
    } catch (error) {
      this.logger.error(`Failed to delete template: ${error.message}`, error);
      return false;
    }
  }

  async listTemplates(category?: string, isActive?: boolean): Promise<NotificationTemplate[]> {
    try {
      this.logger.log('Listing notification templates');

      // Mock implementation
      const mockTemplates: NotificationTemplate[] = [
        {
          id: 'template_welcome',
          name: 'Welcome Email Template',
          eventType: 'user_registered',
          channels: ['email', 'push'],
          priority: 'medium',
          category: 'account',
          templates: {
            email: {
              subject: 'Welcome to Social NFT Platform!',
              html: '<h1>Welcome!</h1>',
              text: 'Welcome!',
            },
          },
          isActive: true,
        },
        {
          id: 'template_nft_generated',
          name: 'NFT Generated Template',
          eventType: 'nft_generated',
          channels: ['email', 'push'],
          priority: 'high',
          category: 'nft',
          templates: {
            email: {
              subject: 'Your NFT is Ready!',
              html: '<h1>NFT Generated!</h1>',
              text: 'NFT Generated!',
            },
          },
          isActive: true,
        },
      ];

      let filteredTemplates = mockTemplates;

      if (category) {
        filteredTemplates = filteredTemplates.filter(t => t.category === category);
      }

      if (isActive !== undefined) {
        filteredTemplates = filteredTemplates.filter(t => t.isActive === isActive);
      }

      return filteredTemplates;

      // Real implementation would be:
      // const where: any = {};
      // if (category) where.category = category;
      // if (isActive !== undefined) where.isActive = isActive;
      // 
      // return await this.prisma.notificationTemplate.findMany({
      //   where,
      //   orderBy: { createdAt: 'desc' },
      // });
    } catch (error) {
      this.logger.error(`Failed to list templates: ${error.message}`, error);
      return [];
    }
  }

  renderTemplate(template: any, data: Record<string, any>): any {
    try {
      // Simple template rendering - replace {{variable}} with actual values
      const rendered = JSON.parse(JSON.stringify(template));

      const replaceVariables = (obj: any): any => {
        if (typeof obj === 'string') {
          return obj.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return data[key] || match;
          });
        } else if (Array.isArray(obj)) {
          return obj.map(replaceVariables);
        } else if (obj && typeof obj === 'object') {
          const result: any = {};
          for (const [key, value] of Object.entries(obj)) {
            result[key] = replaceVariables(value);
          }
          return result;
        }
        return obj;
      };

      return replaceVariables(rendered);
    } catch (error) {
      this.logger.error(`Failed to render template: ${error.message}`, error);
      return template;
    }
  }
}
