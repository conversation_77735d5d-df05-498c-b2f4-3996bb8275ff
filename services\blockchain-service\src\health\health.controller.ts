import { <PERSON>, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  HealthCheckResult
} from '@nestjs/terminus';
import { PrismaService } from '../prisma/prisma.service';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    private readonly health: HealthCheckService,
    private readonly prisma: PrismaService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Comprehensive health check' })
  @ApiResponse({ 
    status: 200, 
    description: 'Health check completed successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        info: { type: 'object' },
        error: { type: 'object' },
        details: { type: 'object' }
      }
    }
  })
  @HealthCheck()
  check(): Promise<HealthCheckResult> {
    this.logger.log('Comprehensive health check requested');
    
    return this.health.check([
      async () => {
        const isHealthy = await this.prisma.healthCheck();
        return {
          database: {
            status: isHealthy ? 'up' : 'down',
            timestamp: new Date().toISOString(),
          },
        };
      },
    ]);
  }

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check' })
  @ApiResponse({ 
    status: 200, 
    description: 'Simple health status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        uptime: { type: 'number', example: 12345 }
      }
    }
  })
  simpleCheck() {
    this.logger.log('Simple health check requested');
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      service: 'project-service',
      version: '1.0.0',
    };
  }

  @Get('database')
  @ApiOperation({ summary: 'Database health check' })
  @ApiResponse({ 
    status: 200, 
    description: 'Database health check completed',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        database: { type: 'object' }
      }
    }
  })
  @HealthCheck()
  checkDatabase(): Promise<HealthCheckResult> {
    this.logger.log('Database health check requested');
    
    return this.health.check([
      async () => {
        const isHealthy = await this.prisma.healthCheck();
        return {
          database: {
            status: isHealthy ? 'up' : 'down',
            timestamp: new Date().toISOString(),
          },
        };
      },
    ]);
  }
}
