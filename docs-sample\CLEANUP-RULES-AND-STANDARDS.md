# 🧹 **CLEANUP RULES AND STANDARDS**

## **MANDATORY FOR ALL DEVELOPERS & AI AGENTS**

### **📋 CORE PRINCIPLE**
**After ANY fix, implementation, or change, developers and AI agents MUST clean up wrong structures, files, and duplicate code to maintain a clean, professional codebase.**

---

## **🎯 CLEANUP RESPONSIBILITIES**

### **✅ MANDATORY CLEANUP AFTER:**
- ✅ **Bug Fixes** - Remove temporary files, debug code, unused imports
- ✅ **Feature Implementation** - Clean up experimental code, unused dependencies
- ✅ **Refactoring** - Remove old code, unused functions, deprecated patterns
- ✅ **Dependency Updates** - Remove deprecated packages, unused configurations
- ✅ **Architecture Changes** - Clean up old patterns, obsolete structures
- ✅ **Testing Implementation** - Remove test artifacts, temporary test files

### **🚨 ZERO TOLERANCE FOR:**
- ❌ **Duplicate Code** - Same logic in multiple places
- ❌ **Dead Code** - Unused functions, variables, imports
- ❌ **Temporary Files** - Debug files, test artifacts, backup files
- ❌ **Wrong Structure** - Files in incorrect directories
- ❌ **Obsolete Dependencies** - Unused packages in package.json
- ❌ **Commented Code** - Old code left as comments

---

## **📁 FILE STRUCTURE CLEANUP**

### **🗑️ FILES TO REMOVE**
```bash
# Temporary and debug files
*.tmp
*.temp
*.debug
*.backup
*.old
*_backup.*
*_old.*
*_temp.*

# IDE and editor files
.vscode/settings.json (if not project-specific)
.idea/
*.swp
*.swo
*~

# Build artifacts in wrong places
dist/ (in root when should be in services)
build/ (in root when should be in services)
node_modules/ (in root for microservices)

# Duplicate configuration files
package.json (in root for microservices)
tsconfig.json (duplicates without purpose)
.env.* (unused environment files)

# Test artifacts
coverage/ (old coverage reports)
*.test.js.snap (unused snapshots)
test-results/
```

### **📂 DIRECTORY STRUCTURE CLEANUP**
```bash
# Remove empty directories
find . -type d -empty -delete

# Remove incorrectly placed directories
services/shared/ (should be in root)
shared/services/ (wrong nesting)
src/shared/ (should use imports)

# Remove duplicate directories
services/api-gateway/ AND services/api-gateway-v2/
shared/utils/ AND shared/utilities/
docs/old/ AND docs/archive/
```

---

## **💻 CODE CLEANUP STANDARDS**

### **🔧 IMPORT CLEANUP**
```typescript
// ❌ BAD - Unused imports
import { Component, OnInit, OnDestroy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
// Only using Component

// ✅ GOOD - Only needed imports
import { Component } from '@angular/core';

// ❌ BAD - Duplicate imports
import { Logger } from '@shared/logging';
import { ServiceLogger } from '@shared/logging/services/service-logger.service';

// ✅ GOOD - Consolidated imports
import { ServiceLogger } from '@shared/logging/services/service-logger.service';
```

### **🗑️ DEAD CODE REMOVAL**
```typescript
// ❌ BAD - Commented out code
export class UserService {
  // constructor(private http: HttpClient) {}
  
  getUsers() {
    // return this.http.get('/users');
    return this.newUserService.getUsers();
  }
  
  // deleteUser(id: string) {
  //   return this.http.delete(`/users/${id}`);
  // }
}

// ✅ GOOD - Clean implementation
export class UserService {
  constructor(private newUserService: NewUserService) {}
  
  getUsers() {
    return this.newUserService.getUsers();
  }
}
```

### **🔄 DUPLICATE CODE ELIMINATION**
```typescript
// ❌ BAD - Duplicate validation logic
// In user.service.ts
validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// In auth.service.ts
validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// ✅ GOOD - Shared utility
// In shared/utils/validation.ts
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// In services
import { validateEmail } from '@shared/utils/validation';
```

---

## **📦 DEPENDENCY CLEANUP**

### **🗑️ PACKAGE.JSON CLEANUP**
```json
// ❌ BAD - Unused dependencies
{
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "lodash": "^4.17.21",          // Not used anywhere
    "moment": "^2.29.4",           // Replaced with date-fns
    "axios": "^1.4.0",             // Using @nestjs/axios
    "express": "^4.18.2"           // Using @nestjs/platform-express
  }
}

// ✅ GOOD - Only used dependencies
{
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "@nestjs/axios": "^3.0.0",
    "@nestjs/platform-express": "^10.0.0",
    "date-fns": "^2.30.0"
  }
}
```

### **🔧 DEPENDENCY AUDIT COMMANDS**
```bash
# Check for unused dependencies
npx depcheck

# Remove unused dependencies
npm uninstall package-name

# Update outdated dependencies
npm outdated
npm update

# Security audit
npm audit
npm audit fix
```

---

## **🧪 TEST CLEANUP**

### **🗑️ TEST FILE CLEANUP**
```typescript
// ❌ BAD - Obsolete test files
user.service.spec.ts.backup
user.service.test.ts.old
user.service.spec.ts.temp

// ❌ BAD - Duplicate test logic
describe('UserService - Test 1', () => {
  it('should validate email', () => {
    // Same test logic
  });
});

describe('UserService - Test 2', () => {
  it('should validate email format', () => {
    // Same test logic with different name
  });
});

// ✅ GOOD - Consolidated tests
describe('UserService', () => {
  describe('email validation', () => {
    it('should validate email format correctly', () => {
      // Single, comprehensive test
    });
  });
});
```

---

## **📋 CLEANUP CHECKLIST**

### **🔍 BEFORE COMMITTING - MANDATORY CHECKS**

#### **📁 File Structure**
- [ ] No files in wrong directories
- [ ] No duplicate directories or files
- [ ] No temporary or backup files
- [ ] No empty directories
- [ ] No IDE-specific files (unless project-wide)

#### **💻 Code Quality**
- [ ] No unused imports
- [ ] No commented-out code
- [ ] No duplicate functions or logic
- [ ] No debug console.log statements
- [ ] No TODO comments without tickets

#### **📦 Dependencies**
- [ ] No unused dependencies in package.json
- [ ] No deprecated packages
- [ ] No security vulnerabilities
- [ ] No duplicate dependencies across services

#### **🧪 Testing**
- [ ] No obsolete test files
- [ ] No duplicate test logic
- [ ] No test artifacts or temporary files
- [ ] Coverage reports in correct location

#### **📚 Documentation**
- [ ] No outdated documentation
- [ ] No duplicate documentation files
- [ ] No broken links or references
- [ ] README files updated and accurate

---

## **🛠️ CLEANUP AUTOMATION**

### **📜 CLEANUP SCRIPTS**
```bash
#!/bin/bash
# cleanup.sh - Run before every commit

echo "🧹 Starting cleanup process..."

# Remove temporary files
find . -name "*.tmp" -delete
find . -name "*.temp" -delete
find . -name "*.backup" -delete
find . -name "*_old.*" -delete

# Remove empty directories
find . -type d -empty -delete

# Check for unused dependencies
echo "📦 Checking dependencies..."
npx depcheck

# Run linting
echo "🔧 Running linter..."
npm run lint:fix

# Format code
echo "✨ Formatting code..."
npm run format

echo "✅ Cleanup complete!"
```

### **🔧 PRE-COMMIT HOOKS**
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "./scripts/cleanup.sh && lint-staged"
    }
  },
  "lint-staged": {
    "*.{ts,js}": [
      "eslint --fix",
      "prettier --write"
    ]
  }
}
```

---

## **⚠️ ENFORCEMENT**

### **🚨 MANDATORY COMPLIANCE**
- **ALL commits** must pass cleanup checklist
- **ALL pull requests** must include cleanup verification
- **ALL AI agents** must run cleanup after any changes
- **ALL developers** must follow cleanup standards

### **🔍 REVIEW REQUIREMENTS**
- Code reviews MUST verify cleanup compliance
- Automated checks MUST pass before merge
- Documentation MUST be updated for structural changes
- Dependencies MUST be justified and documented

---

## **📊 CLEANUP METRICS**

### **🎯 SUCCESS INDICATORS**
- Zero unused dependencies across all services
- Zero duplicate code patterns
- Zero temporary or backup files
- Zero empty directories
- Zero commented-out code blocks

### **📈 MONITORING**
- Weekly dependency audits
- Monthly code duplication analysis
- Quarterly structure reviews
- Continuous automated cleanup checks

---

**🎯 REMEMBER: A clean codebase is a maintainable codebase. Every cleanup action contributes to long-term project success and developer productivity.**
