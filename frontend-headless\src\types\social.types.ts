// Social Features Types for Task 3.3: Social Features
// Comprehensive social interaction and community types

export interface UserProfile {
  id: string
  username: string
  displayName: string
  email: string
  avatar?: string
  coverImage?: string
  bio?: string
  location?: string
  website?: string
  twitterHandle?: string
  discordHandle?: string
  telegramHandle?: string
  joinedAt: string
  lastActiveAt: string
  isVerified: boolean
  isOnline: boolean
  privacySettings: PrivacySettings
  socialStats: SocialStats
  badges: UserBadge[]
  achievements: Achievement[]
  preferences: UserPreferences
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private'
  showEmail: boolean
  showLocation: boolean
  showOnlineStatus: boolean
  allowDirectMessages: 'everyone' | 'friends' | 'none'
  allowFollowRequests: boolean
  showActivity: boolean
  showCollections: boolean
  showTransactions: boolean
}

export interface SocialStats {
  followersCount: number
  followingCount: number
  postsCount: number
  likesReceived: number
  commentsReceived: number
  sharesReceived: number
  nftCount: number
  totalEngagement: number
  influenceScore: number
  reputationScore: number
}

export interface UserBadge {
  id: string
  name: string
  description: string
  icon: string
  color: string
  rarity: BadgeRarity
  earnedAt: string
  category: BadgeCategory
}

export enum BadgeRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

export enum BadgeCategory {
  COLLECTOR = 'collector',
  TRADER = 'trader',
  CREATOR = 'creator',
  COMMUNITY = 'community',
  ACHIEVEMENT = 'achievement',
  SPECIAL = 'special'
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: AchievementCategory
  progress: number
  maxProgress: number
  isCompleted: boolean
  completedAt?: string
  reward?: AchievementReward
}

export enum AchievementCategory {
  SOCIAL = 'social',
  TRADING = 'trading',
  COLLECTING = 'collecting',
  ENGAGEMENT = 'engagement',
  MILESTONE = 'milestone',
  SPECIAL_EVENT = 'special_event'
}

export interface AchievementReward {
  type: 'badge' | 'points' | 'nft' | 'title' | 'access'
  value: string | number
  description: string
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  emailNotifications: NotificationPreferences
  pushNotifications: NotificationPreferences
  socialFeatures: SocialFeaturePreferences
}

export interface NotificationPreferences {
  newFollower: boolean
  newMessage: boolean
  newComment: boolean
  newLike: boolean
  newShare: boolean
  marketplaceActivity: boolean
  nftUpdates: boolean
  systemUpdates: boolean
}

export interface SocialFeaturePreferences {
  autoFollowBack: boolean
  showActivityFeed: boolean
  allowTagging: boolean
  allowMentions: boolean
  showRecommendations: boolean
}

// Social Interactions
export interface SocialPost {
  id: string
  authorId: string
  author: UserProfile
  content: string
  images?: string[]
  nftReferences?: string[]
  type: PostType
  visibility: PostVisibility
  createdAt: string
  updatedAt?: string
  isEdited: boolean
  isPinned: boolean
  tags: string[]
  mentions: string[]
  engagement: PostEngagement
  comments: Comment[]
  shares: Share[]
}

export enum PostType {
  TEXT = 'text',
  IMAGE = 'image',
  NFT_SHOWCASE = 'nft_showcase',
  MARKETPLACE_LISTING = 'marketplace_listing',
  ACHIEVEMENT = 'achievement',
  POLL = 'poll',
  EVENT = 'event'
}

export enum PostVisibility {
  PUBLIC = 'public',
  FRIENDS = 'friends',
  FOLLOWERS = 'followers',
  PRIVATE = 'private'
}

export interface PostEngagement {
  likesCount: number
  commentsCount: number
  sharesCount: number
  viewsCount: number
  isLikedByUser: boolean
  isSharedByUser: boolean
  isBookmarkedByUser: boolean
}

export interface Comment {
  id: string
  postId: string
  authorId: string
  author: UserProfile
  content: string
  parentCommentId?: string
  replies?: Comment[]
  createdAt: string
  updatedAt?: string
  isEdited: boolean
  likesCount: number
  isLikedByUser: boolean
  mentions: string[]
}

export interface Share {
  id: string
  postId: string
  userId: string
  user: UserProfile
  comment?: string
  createdAt: string
  visibility: PostVisibility
}

export interface Like {
  id: string
  userId: string
  user: UserProfile
  targetId: string
  targetType: 'post' | 'comment' | 'nft'
  createdAt: string
}

// Social Relationships
export interface Follow {
  id: string
  followerId: string
  follower: UserProfile
  followingId: string
  following: UserProfile
  createdAt: string
  isAccepted: boolean
  isMutual: boolean
}

export interface FollowRequest {
  id: string
  fromUserId: string
  fromUser: UserProfile
  toUserId: string
  toUser: UserProfile
  createdAt: string
  status: FollowRequestStatus
  message?: string
}

export enum FollowRequestStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

// Direct Messages
export interface DirectMessage {
  id: string
  conversationId: string
  senderId: string
  sender: UserProfile
  receiverId: string
  receiver: UserProfile
  content: string
  messageType: MessageType
  attachments?: MessageAttachment[]
  createdAt: string
  readAt?: string
  isRead: boolean
  isEdited: boolean
  replyToId?: string
  replyTo?: DirectMessage
}

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  NFT = 'nft',
  LINK = 'link',
  SYSTEM = 'system'
}

export interface MessageAttachment {
  id: string
  type: 'image' | 'nft' | 'link'
  url: string
  metadata?: Record<string, any>
}

export interface Conversation {
  id: string
  participants: UserProfile[]
  lastMessage?: DirectMessage
  lastMessageAt: string
  unreadCount: number
  isArchived: boolean
  isMuted: boolean
  createdAt: string
}

// Community Features
export interface Community {
  id: string
  name: string
  description: string
  avatar?: string
  coverImage?: string
  creatorId: string
  creator: UserProfile
  category: CommunityCategory
  type: CommunityType
  visibility: CommunityVisibility
  memberCount: number
  postCount: number
  isVerified: boolean
  rules: string[]
  tags: string[]
  createdAt: string
  settings: CommunitySettings
  stats: CommunityStats
}

export enum CommunityCategory {
  GENERAL = 'general',
  TRADING = 'trading',
  COLLECTING = 'collecting',
  ART = 'art',
  GAMING = 'gaming',
  MUSIC = 'music',
  SPORTS = 'sports',
  TECHNOLOGY = 'technology',
  EDUCATION = 'education',
  ENTERTAINMENT = 'entertainment'
}

export enum CommunityType {
  PUBLIC = 'public',
  PRIVATE = 'private',
  INVITE_ONLY = 'invite_only'
}

export enum CommunityVisibility {
  PUBLIC = 'public',
  UNLISTED = 'unlisted',
  PRIVATE = 'private'
}

export interface CommunitySettings {
  allowPosts: boolean
  allowImages: boolean
  allowNFTSharing: boolean
  requireApproval: boolean
  allowInvites: boolean
  moderationLevel: 'low' | 'medium' | 'high'
}

export interface CommunityStats {
  activeMembers: number
  dailyPosts: number
  weeklyEngagement: number
  growthRate: number
  engagementRate: number
}

export interface CommunityMembership {
  id: string
  communityId: string
  community: Community
  userId: string
  user: UserProfile
  role: CommunityRole
  joinedAt: string
  isActive: boolean
  permissions: CommunityPermission[]
}

export enum CommunityRole {
  MEMBER = 'member',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  OWNER = 'owner'
}

export enum CommunityPermission {
  POST = 'post',
  COMMENT = 'comment',
  MODERATE = 'moderate',
  INVITE = 'invite',
  MANAGE_MEMBERS = 'manage_members',
  MANAGE_SETTINGS = 'manage_settings'
}

// Social Feed
export interface SocialFeed {
  posts: SocialPost[]
  hasMore: boolean
  nextCursor?: string
  totalCount: number
  lastUpdated: string
}

export interface FeedFilters {
  type?: PostType[]
  timeRange?: 'hour' | 'day' | 'week' | 'month' | 'all'
  sortBy?: 'recent' | 'popular' | 'trending'
  includeFollowing?: boolean
  includeCommunities?: boolean
  tags?: string[]
}

// Social Analytics
export interface SocialAnalytics {
  userId: string
  timeframe: string
  metrics: SocialMetrics
  engagement: EngagementMetrics
  growth: GrowthMetrics
  topPosts: SocialPost[]
  topCommunities: Community[]
  insights: SocialInsight[]
}

export interface SocialMetrics {
  totalPosts: number
  totalLikes: number
  totalComments: number
  totalShares: number
  totalViews: number
  averageEngagement: number
  reachCount: number
  impressionCount: number
}

export interface EngagementMetrics {
  engagementRate: number
  likesPerPost: number
  commentsPerPost: number
  sharesPerPost: number
  bestPostingTime: string
  topEngagingContent: string[]
}

export interface GrowthMetrics {
  followersGrowth: number
  followingGrowth: number
  engagementGrowth: number
  reachGrowth: number
  periodComparison: number
}

export interface SocialInsight {
  type: 'tip' | 'warning' | 'opportunity' | 'achievement'
  title: string
  description: string
  actionable: boolean
  action?: string
  priority: 'low' | 'medium' | 'high'
}

// Social Notifications
export interface SocialNotification {
  id: string
  userId: string
  type: SocialNotificationType
  title: string
  message: string
  data: Record<string, any>
  isRead: boolean
  createdAt: string
  actionUrl?: string
  actionText?: string
}

export enum SocialNotificationType {
  NEW_FOLLOWER = 'new_follower',
  FOLLOW_REQUEST = 'follow_request',
  POST_LIKE = 'post_like',
  POST_COMMENT = 'post_comment',
  POST_SHARE = 'post_share',
  COMMENT_REPLY = 'comment_reply',
  MENTION = 'mention',
  DIRECT_MESSAGE = 'direct_message',
  COMMUNITY_INVITE = 'community_invite',
  ACHIEVEMENT_EARNED = 'achievement_earned',
  BADGE_EARNED = 'badge_earned'
}

// API Request/Response Types
export interface CreatePostRequest {
  content: string
  type: PostType
  visibility: PostVisibility
  images?: string[]
  nftReferences?: string[]
  tags?: string[]
  mentions?: string[]
}

export interface UpdatePostRequest {
  content?: string
  visibility?: PostVisibility
  tags?: string[]
  isPinned?: boolean
}

export interface CreateCommentRequest {
  content: string
  parentCommentId?: string
  mentions?: string[]
}

export interface UpdateProfileRequest {
  displayName?: string
  bio?: string
  location?: string
  website?: string
  twitterHandle?: string
  discordHandle?: string
  telegramHandle?: string
  avatar?: string
  coverImage?: string
  privacySettings?: Partial<PrivacySettings>
  preferences?: Partial<UserPreferences>
}

export interface CreateCommunityRequest {
  name: string
  description: string
  category: CommunityCategory
  type: CommunityType
  visibility: CommunityVisibility
  avatar?: string
  coverImage?: string
  rules?: string[]
  tags?: string[]
  settings?: Partial<CommunitySettings>
}

export interface SendMessageRequest {
  receiverId: string
  content: string
  messageType: MessageType
  attachments?: MessageAttachment[]
  replyToId?: string
}

export interface SocialSearchRequest {
  query: string
  type: 'users' | 'posts' | 'communities' | 'all'
  filters?: {
    verified?: boolean
    hasNFTs?: boolean
    location?: string
    tags?: string[]
  }
  sortBy?: 'relevance' | 'recent' | 'popular'
  limit?: number
  offset?: number
}

export interface SocialSearchResponse {
  users: UserProfile[]
  posts: SocialPost[]
  communities: Community[]
  totalCount: number
  hasMore: boolean
}

// Leaderboards
export interface SocialLeaderboard {
  type: LeaderboardType
  timeframe: 'daily' | 'weekly' | 'monthly' | 'all_time'
  entries: LeaderboardEntry[]
  userRank?: number
  totalParticipants: number
}

export enum LeaderboardType {
  MOST_FOLLOWED = 'most_followed',
  MOST_ENGAGING = 'most_engaging',
  TOP_COLLECTORS = 'top_collectors',
  TOP_TRADERS = 'top_traders',
  MOST_ACTIVE = 'most_active',
  INFLUENCE_SCORE = 'influence_score'
}

export interface LeaderboardEntry {
  rank: number
  user: UserProfile
  score: number
  change: number
  badge?: string
}
