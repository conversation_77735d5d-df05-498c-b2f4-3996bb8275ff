import { IsString, <PERSON>N<PERSON>ber, IsEnum, IsBoolean, validateSync } from 'class-validator';
import { Transform, plainToClass } from 'class-transformer';

export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test',
}

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose',
}

export enum LogFormat {
  JSON = 'json',
  SIMPLE = 'simple',
}

export class AppConfig {
  @IsString()
  SERVICE_NAME: string = 'profile-analysis-service';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  SERVICE_PORT: number = 3002;

  @IsEnum(Environment)
  NODE_ENV: Environment = Environment.DEVELOPMENT;

  @IsString()
  DATABASE_URL: string;

  @IsString()
  JWT_SECRET: string;

  @IsString()
  JWT_EXPIRES_IN: string = '24h';

  @IsString()
  API_PREFIX: string = 'api';

  @IsEnum(LogLevel)
  LOG_LEVEL: LogLevel = LogLevel.INFO;

  @IsEnum(LogFormat)
  LOG_FORMAT: LogFormat = LogFormat.JSON;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  HEALTH_CHECK_TIMEOUT: number = 5000;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  HEALTH_CHECK_INTERVAL: number = 30000;

  @IsString()
  CORS_ORIGIN: string = '*';

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  SWAGGER_ENABLED: boolean = true;

  // Twitter API Configuration
  @IsString()
  TWITTER_API_KEY: string;

  @IsString()
  TWITTER_API_SECRET: string;

  @IsString()
  TWITTER_BEARER_TOKEN: string;

  @IsString()
  TWITTER_ACCESS_TOKEN: string;

  @IsString()
  TWITTER_ACCESS_TOKEN_SECRET: string;

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  USE_MOCK_TWITTER: boolean = true;

  @IsString()
  MOCK_TWITTER_SERVICE_URL: string = 'http://localhost:3020';

  @IsString()
  USER_SERVICE_URL: string = 'http://localhost:3001';

  @IsString()
  API_GATEWAY_URL: string = 'http://localhost:3010';

  static validate(config: Record<string, unknown>): AppConfig {
    const validatedConfig = plainToClass(AppConfig, config, {
      enableImplicitConversion: true,
    });

    const errors = validateSync(validatedConfig, {
      skipMissingProperties: false,
    });

    if (errors.length > 0) {
      throw new Error(`Configuration validation error: ${errors.toString()}`);
    }

    return validatedConfig;
  }
}
