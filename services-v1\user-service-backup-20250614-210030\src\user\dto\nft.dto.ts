import { IsString, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>, Is<PERSON><PERSON>ber, IsBoolean, IsArray, ValidateNested, <PERSON>, <PERSON>, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum NFTRarity {
  COMMON = 'common',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary',
  MYTHIC = 'mythic',
}

export enum NFTStatus {
  PENDING = 'pending',
  GENERATING = 'generating',
  COMPLETED = 'completed',
  FAILED = 'failed',
  MINTED = 'minted',
  TRANSFERRED = 'transferred',
}

export enum BlockchainNetwork {
  ETHEREUM = 'ethereum',
  POLYGON = 'polygon',
  BSC = 'bsc',
  BASE = 'base',
  ARBITRUM = 'arbitrum',
}

export class NFTAttributeDto {
  @ApiProperty({
    description: 'Attribute name',
    example: 'Background',
  })
  @IsString()
  trait_type: string;

  @ApiProperty({
    description: 'Attribute value',
    example: 'Cosmic Blue',
  })
  value: string | number;

  @ApiPropertyOptional({
    description: 'Display type for numeric attributes',
    example: 'boost_percentage',
    enum: ['boost_number', 'boost_percentage', 'number', 'date'],
  })
  @IsOptional()
  @IsString()
  display_type?: string;

  @ApiPropertyOptional({
    description: 'Maximum value for boost attributes',
    example: 100,
  })
  @IsOptional()
  @IsNumber()
  max_value?: number;
}

export class NFTMetadataDto {
  @ApiProperty({
    description: 'NFT name',
    example: 'Cosmic Warrior #1234',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'NFT description',
    example: 'A legendary cosmic warrior with extraordinary powers',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'NFT image URL',
    example: 'https://nft-storage.com/image/cosmic-warrior-1234.png',
  })
  @IsUrl()
  image: string;

  @ApiPropertyOptional({
    description: 'External URL for more information',
    example: 'https://platform.com/nft/cosmic-warrior-1234',
  })
  @IsOptional()
  @IsUrl()
  external_url?: string;

  @ApiPropertyOptional({
    description: 'Animation URL for animated NFTs',
    example: 'https://nft-storage.com/animation/cosmic-warrior-1234.mp4',
  })
  @IsOptional()
  @IsUrl()
  animation_url?: string;

  @ApiProperty({
    description: 'NFT attributes',
    type: [NFTAttributeDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NFTAttributeDto)
  attributes: NFTAttributeDto[];

  @ApiPropertyOptional({
    description: 'Background color in hex',
    example: '#1a1a2e',
  })
  @IsOptional()
  @IsString()
  background_color?: string;

  @ApiPropertyOptional({
    description: 'YouTube video URL',
    example: 'https://youtube.com/watch?v=abc123',
  })
  @IsOptional()
  @IsUrl()
  youtube_url?: string;
}

export class GenerateNFTDto {
  @ApiProperty({
    description: 'User ID for NFT generation',
    example: 'user_123',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Campaign ID that triggered NFT generation',
    example: 'campaign_456',
  })
  @IsString()
  campaignId: string;

  @ApiProperty({
    description: 'User engagement score for NFT generation',
    example: 85.5,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  engagementScore: number;

  @ApiProperty({
    description: 'Desired NFT rarity',
    enum: NFTRarity,
    example: NFTRarity.RARE,
  })
  @IsEnum(NFTRarity)
  rarity: NFTRarity;

  @ApiPropertyOptional({
    description: 'Preferred blockchain network',
    enum: BlockchainNetwork,
    example: BlockchainNetwork.POLYGON,
    default: BlockchainNetwork.POLYGON,
  })
  @IsOptional()
  @IsEnum(BlockchainNetwork)
  blockchain?: BlockchainNetwork = BlockchainNetwork.POLYGON;

  @ApiPropertyOptional({
    description: 'Custom NFT template or theme',
    example: 'cosmic_warrior',
  })
  @IsOptional()
  @IsString()
  template?: string;

  @ApiPropertyOptional({
    description: 'Additional generation parameters',
    example: {
      style: 'cyberpunk',
      colorScheme: 'neon',
      specialEffects: true,
    },
  })
  @IsOptional()
  generationParams?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Whether to auto-mint the NFT',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  autoMint?: boolean = false;

  @ApiPropertyOptional({
    description: 'Recipient wallet address for auto-transfer',
    example: '******************************************',
  })
  @IsOptional()
  @IsString()
  recipientAddress?: string;
}

export class UpdateNFTDto {
  @ApiPropertyOptional({
    description: 'Updated NFT metadata',
    type: NFTMetadataDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => NFTMetadataDto)
  metadata?: NFTMetadataDto;

  @ApiPropertyOptional({
    description: 'Updated NFT status',
    enum: NFTStatus,
    example: NFTStatus.COMPLETED,
  })
  @IsOptional()
  @IsEnum(NFTStatus)
  status?: NFTStatus;

  @ApiPropertyOptional({
    description: 'Updated blockchain network',
    enum: BlockchainNetwork,
    example: BlockchainNetwork.ETHEREUM,
  })
  @IsOptional()
  @IsEnum(BlockchainNetwork)
  blockchain?: BlockchainNetwork;

  @ApiPropertyOptional({
    description: 'Token ID after minting',
    example: '12345',
  })
  @IsOptional()
  @IsString()
  tokenId?: string;

  @ApiPropertyOptional({
    description: 'Contract address after minting',
    example: '******************************************',
  })
  @IsOptional()
  @IsString()
  contractAddress?: string;

  @ApiPropertyOptional({
    description: 'Transaction hash for minting',
    example: '******************************************90abcdef1234567890abcdef12',
  })
  @IsOptional()
  @IsString()
  transactionHash?: string;

  @ApiPropertyOptional({
    description: 'Current owner wallet address',
    example: '******************************************',
  })
  @IsOptional()
  @IsString()
  ownerAddress?: string;
}

export class MintNFTDto {
  @ApiProperty({
    description: 'NFT ID to mint',
    example: 'nft_789',
  })
  @IsString()
  nftId: string;

  @ApiProperty({
    description: 'Recipient wallet address',
    example: '******************************************',
  })
  @IsString()
  recipientAddress: string;

  @ApiPropertyOptional({
    description: 'Blockchain network for minting',
    enum: BlockchainNetwork,
    example: BlockchainNetwork.POLYGON,
  })
  @IsOptional()
  @IsEnum(BlockchainNetwork)
  blockchain?: BlockchainNetwork;

  @ApiPropertyOptional({
    description: 'Gas price in gwei',
    example: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  gasPrice?: number;

  @ApiPropertyOptional({
    description: 'Whether to prioritize transaction',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  priorityMinting?: boolean = false;
}

export class TransferNFTDto {
  @ApiProperty({
    description: 'NFT ID to transfer',
    example: 'nft_789',
  })
  @IsString()
  nftId: string;

  @ApiProperty({
    description: 'Current owner wallet address',
    example: '******************************************',
  })
  @IsString()
  fromAddress: string;

  @ApiProperty({
    description: 'Recipient wallet address',
    example: '******************************************',
  })
  @IsString()
  toAddress: string;

  @ApiPropertyOptional({
    description: 'Transfer reason or note',
    example: 'Campaign reward distribution',
  })
  @IsOptional()
  @IsString()
  transferReason?: string;
}

export class NFTResponseDto {
  @ApiProperty({
    description: 'NFT ID',
    example: 'nft_789',
  })
  id: string;

  @ApiProperty({
    description: 'User ID who owns/generated the NFT',
    example: 'user_123',
  })
  userId: string;

  @ApiProperty({
    description: 'Campaign ID that triggered generation',
    example: 'campaign_456',
  })
  campaignId: string;

  @ApiProperty({
    description: 'NFT metadata',
    type: NFTMetadataDto,
  })
  metadata: NFTMetadataDto;

  @ApiProperty({
    description: 'NFT rarity',
    enum: NFTRarity,
    example: NFTRarity.RARE,
  })
  rarity: NFTRarity;

  @ApiProperty({
    description: 'NFT status',
    enum: NFTStatus,
    example: NFTStatus.COMPLETED,
  })
  status: NFTStatus;

  @ApiProperty({
    description: 'Blockchain network',
    enum: BlockchainNetwork,
    example: BlockchainNetwork.POLYGON,
  })
  blockchain: BlockchainNetwork;

  @ApiProperty({
    description: 'Engagement score used for generation',
    example: 85.5,
  })
  engagementScore: number;

  @ApiPropertyOptional({
    description: 'Token ID after minting',
    example: '12345',
  })
  tokenId?: string;

  @ApiPropertyOptional({
    description: 'Contract address',
    example: '******************************************',
  })
  contractAddress?: string;

  @ApiPropertyOptional({
    description: 'Transaction hash',
    example: '******************************************90abcdef1234567890abcdef12',
  })
  transactionHash?: string;

  @ApiPropertyOptional({
    description: 'Current owner wallet address',
    example: '******************************************',
  })
  ownerAddress?: string;

  @ApiProperty({
    description: 'NFT creation timestamp',
    example: '2025-06-03T21:30:00Z',
  })
  createdAt: string;

  @ApiPropertyOptional({
    description: 'NFT minting timestamp',
    example: '2025-06-03T22:00:00Z',
  })
  mintedAt?: string;

  @ApiPropertyOptional({
    description: 'Last transfer timestamp',
    example: '2025-06-04T10:15:00Z',
  })
  lastTransferredAt?: string;

  @ApiPropertyOptional({
    description: 'Generation parameters used',
    example: {
      template: 'cosmic_warrior',
      style: 'cyberpunk',
      colorScheme: 'neon',
    },
  })
  generationParams?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Additional NFT metadata',
    example: {
      ipfsHash: 'QmX1234567890abcdef',
      fileSize: 2048576,
      dimensions: '1024x1024',
    },
  })
  additionalMetadata?: Record<string, any>;
}
