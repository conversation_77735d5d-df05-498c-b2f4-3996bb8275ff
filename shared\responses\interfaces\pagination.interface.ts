/**
 * Pagination Interfaces
 * Defines the standard pagination structure for all services
 */

import { BaseResponse } from './base-response.interface';

/**
 * Pagination metadata interface
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  nextPage?: number;
  prevPage?: number;
  offset?: number;
}

/**
 * Paginated response interface
 */
export interface PaginatedResponse<T = any> extends BaseResponse<T[]> {
  success: true;
  data: T[];
  pagination: PaginationMeta;
  filters?: FilterOptions[];
  sorting?: SortingOptions[];
  search?: SearchOptions;
}

/**
 * Pagination query parameters
 */
export interface PaginationQuery {
  page?: number;
  limit?: number;
  offset?: number;
  cursor?: string; // For cursor-based pagination
}

/**
 * Sorting options interface
 */
export interface SortingOptions {
  field: string;
  direction: SortDirection;
  nullsFirst?: boolean;
}

/**
 * Sort direction enumeration
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Filter options interface
 */
export interface FilterOptions {
  field: string;
  operator: FilterOperator;
  value: any;
  values?: any[]; // For IN/NOT_IN operators
}

/**
 * Filter operator enumeration
 */
export enum FilterOperator {
  // Equality operators
  EQUALS = 'eq',
  NOT_EQUALS = 'ne',
  
  // Comparison operators
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  
  // Array operators
  IN = 'in',
  NOT_IN = 'nin',
  
  // String operators
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  STARTS_WITH = 'starts_with',
  ENDS_WITH = 'ends_with',
  REGEX = 'regex',
  
  // Null operators
  IS_NULL = 'is_null',
  IS_NOT_NULL = 'is_not_null',
  
  // Date operators
  DATE_EQUALS = 'date_eq',
  DATE_BEFORE = 'date_before',
  DATE_AFTER = 'date_after',
  DATE_BETWEEN = 'date_between',
  
  // Boolean operators
  IS_TRUE = 'is_true',
  IS_FALSE = 'is_false',
}

/**
 * Search options interface
 */
export interface SearchOptions {
  query: string;
  fields?: string[];
  fuzzy?: boolean;
  caseSensitive?: boolean;
  wholeWord?: boolean;
  highlight?: boolean;
}

/**
 * Cursor-based pagination interface (for large datasets)
 */
export interface CursorPagination {
  cursor?: string;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
  nextCursor?: string;
  prevCursor?: string;
}

/**
 * Cursor-based paginated response
 */
export interface CursorPaginatedResponse<T = any> extends BaseResponse<T[]> {
  success: true;
  data: T[];
  cursor: CursorPagination;
  filters?: FilterOptions[];
  sorting?: SortingOptions[];
  search?: SearchOptions;
}

/**
 * Aggregation options interface
 */
export interface AggregationOptions {
  field: string;
  operation: AggregationOperation;
  alias?: string;
}

/**
 * Aggregation operation enumeration
 */
export enum AggregationOperation {
  COUNT = 'count',
  SUM = 'sum',
  AVG = 'avg',
  MIN = 'min',
  MAX = 'max',
  GROUP_BY = 'group_by',
}

/**
 * Aggregated response interface
 */
export interface AggregatedResponse<T = any> extends BaseResponse<T[]> {
  success: true;
  data: T[];
  aggregations: Record<string, any>;
  filters?: FilterOptions[];
  groupBy?: string[];
}

/**
 * Pagination configuration interface
 */
export interface PaginationConfig {
  defaultLimit: number;
  maxLimit: number;
  allowCursor: boolean;
  allowOffset: boolean;
  defaultSort?: SortingOptions;
}

/**
 * Query builder interface
 */
export interface QueryBuilder {
  pagination: PaginationQuery;
  filters: FilterOptions[];
  sorting: SortingOptions[];
  search?: SearchOptions;
  aggregations?: AggregationOptions[];
}

/**
 * Pagination utilities interface
 */
export interface PaginationUtils {
  /**
   * Calculate pagination metadata
   */
  calculateMeta(page: number, limit: number, totalCount: number): PaginationMeta;
  
  /**
   * Build pagination query
   */
  buildQuery(query: any): QueryBuilder;
  
  /**
   * Validate pagination parameters
   */
  validateParams(page: number, limit: number, config: PaginationConfig): void;
  
  /**
   * Generate cursor for cursor-based pagination
   */
  generateCursor(item: any, sortField: string): string;
  
  /**
   * Parse cursor for cursor-based pagination
   */
  parseCursor(cursor: string): any;
}

/**
 * Filter validation rules
 */
export interface FilterValidationRule {
  field: string;
  allowedOperators: FilterOperator[];
  dataType: 'string' | 'number' | 'boolean' | 'date' | 'array';
  required?: boolean;
  defaultValue?: any;
}

/**
 * Sorting validation rules
 */
export interface SortValidationRule {
  field: string;
  allowedDirections: SortDirection[];
  defaultDirection?: SortDirection;
}

/**
 * Query validation configuration
 */
export interface QueryValidationConfig {
  filters: FilterValidationRule[];
  sorting: SortValidationRule[];
  pagination: PaginationConfig;
  search?: {
    allowedFields: string[];
    minQueryLength: number;
    maxQueryLength: number;
  };
}

/**
 * Pagination response builder options
 */
export interface PaginationResponseBuilderOptions {
  data: any[];
  pagination: PaginationMeta;
  filters?: FilterOptions[];
  sorting?: SortingOptions[];
  search?: SearchOptions;
  aggregations?: Record<string, any>;
}

/**
 * List endpoint configuration
 */
export interface ListEndpointConfig {
  defaultLimit: number;
  maxLimit: number;
  allowedFilters: string[];
  allowedSortFields: string[];
  searchableFields: string[];
  defaultSort: SortingOptions;
  enableAggregation: boolean;
  enableCursor: boolean;
}

/**
 * Pagination performance metrics
 */
export interface PaginationMetrics {
  queryTime: number;
  totalRows: number;
  returnedRows: number;
  cacheHit: boolean;
  indexUsed: boolean;
}

/**
 * Enhanced paginated response with metrics
 */
export interface EnhancedPaginatedResponse<T = any> extends PaginatedResponse<T> {
  metrics?: PaginationMetrics;
  warnings?: string[];
  recommendations?: string[];
}
