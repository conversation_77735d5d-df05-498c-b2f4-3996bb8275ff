/**
 * Service Logger Service
 * 
 * Extends structured logging with business event tracking and performance metrics
 * Provides standardized logging patterns for microservices
 */

import { Injectable } from '@nestjs/common';
import { StandardizedConfigService } from '../../config/services/standardized-config.service';
import { StructuredLoggerService } from './structured-logger.service';
import { 
  LogLevel, 
  LogContext, 
  BusinessContext, 
  SecurityContext, 
  PerformanceMetrics 
} from '../interfaces/logger.interface';

/**
 * Business Event Outcome
 */
export enum BusinessOutcome {
  SUCCESS = 'success',
  FAILURE = 'failure',
  PARTIAL = 'partial',
  PENDING = 'pending',
}

/**
 * Security Event Type
 */
export enum SecurityEventType {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  CONFIGURATION_CHANGE = 'configuration_change',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  SECURITY_VIOLATION = 'security_violation',
}

/**
 * Security Severity
 */
export enum SecuritySeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Service Logger Service
 * Provides business-focused logging capabilities
 */
@Injectable()
export class ServiceLoggerService extends StructuredLoggerService {
  private readonly serviceNameLocal: string;

  constructor(private readonly configService: StandardizedConfigService) {
    super(configService as any);
    const serviceConfig = this.configService.getServiceConfig();
    this.serviceNameLocal = serviceConfig.serviceName;
  }

  /**
   * Log business event
   */
  logBusinessEvent(
    domain: string,
    action: string,
    outcome: BusinessOutcome,
    context: Partial<LogContext> = {}
  ): void {
    const businessContext: BusinessContext = {
      domain,
      entity: context.business?.entity || domain,
      entityId: context.business?.entityId,
      action,
      outcome,
      metrics: context.business?.metrics,
      attributes: context.business?.attributes,
      tags: context.business?.tags,
    };

    const logContext: LogContext = {
      ...context,
      business: businessContext,
      operation: `${domain}:${action}`,
    };

    const message = `Business event: ${domain}.${action} - ${outcome}`;
    this.info(message, logContext);
  }

  /**
   * Log security event
   */
  logSecurityEvent(
    eventType: SecurityEventType,
    severity: SecuritySeverity,
    description: string,
    context: Partial<LogContext> = {}
  ): void {
    const securityContext: SecurityContext = {
      eventType,
      severity,
      actor: typeof context.security?.actor === 'string'
        ? { userId: context.security.actor }
        : context.security?.actor || { userId: context.userId },
      resource: context.security?.resource,
      action: context.security?.action,
      outcome: context.security?.outcome,
      riskScore: context.security?.riskScore,
      details: context.security?.details || { description },
    };

    const logContext: LogContext = {
      ...context,
      security: securityContext,
      operation: `security:${eventType}`,
    };

    const message = `Security event: ${eventType} - ${severity} - ${description}`;
    
    // Log at appropriate level based on severity
    switch (severity) {
      case SecuritySeverity.CRITICAL:
        this.fatal(message, undefined, logContext);
        break;
      case SecuritySeverity.HIGH:
        this.error(message, undefined, logContext);
        break;
      case SecuritySeverity.MEDIUM:
        this.warn(message, logContext);
        break;
      default:
        this.info(message, logContext);
    }
  }

  /**
   * Log performance metrics
   */
  logPerformanceMetric(
    operation: string,
    duration: number,
    success: boolean,
    context: Partial<LogContext> = {}
  ): void {
    const performanceMetrics: PerformanceMetrics = {
      startTime: context.performance?.startTime || Date.now() - duration,
      endTime: context.performance?.endTime || Date.now(),
      duration,
      dbQueryCount: context.performance?.dbQueryCount,
      dbQueryTime: context.performance?.dbQueryTime,
      cacheHits: context.performance?.cacheHits,
      cacheMisses: context.performance?.cacheMisses,
      externalApiCalls: context.performance?.externalApiCalls,
      externalApiTime: context.performance?.externalApiTime,
      memoryUsage: context.performance?.memoryUsage,
      cpuUsage: context.performance?.cpuUsage,
    };

    const logContext: LogContext = {
      ...context,
      performance: performanceMetrics,
      operation,
    };

    const message = `Performance: ${operation} completed in ${duration}ms - ${success ? 'success' : 'failure'}`;
    
    // Log slow operations as warnings
    if (duration > 1000) {
      this.warn(message, logContext);
    } else {
      this.info(message, logContext);
    }
  }

  /**
   * Log operation start
   */
  logOperationStart(operation: string, context: Partial<LogContext> = {}): void {
    const logContext: LogContext = {
      ...context,
      operation,
      performance: {
        startTime: Date.now(),
        ...context.performance,
      },
    };

    this.debug(`Operation started: ${operation}`, logContext);
  }

  /**
   * Log operation end
   */
  logOperationEnd(
    operation: string, 
    success: boolean, 
    duration?: number,
    context: Partial<LogContext> = {}
  ): void {
    const endTime = Date.now();
    const calculatedDuration = duration || (endTime - (context.performance?.startTime || endTime));

    const logContext: LogContext = {
      ...context,
      operation,
      performance: {
        ...context.performance,
        endTime,
        duration: calculatedDuration,
      },
    };

    const message = `Operation completed: ${operation} - ${success ? 'success' : 'failure'} (${calculatedDuration}ms)`;
    
    if (success) {
      this.info(message, logContext);
    } else {
      this.error(message, undefined, logContext);
    }
  }

  /**
   * Log API request
   */
  logApiRequest(
    method: string,
    path: string,
    statusCode: number,
    duration: number,
    context: Partial<LogContext> = {}
  ): void {
    const logContext: LogContext = {
      ...context,
      operation: `api:${method.toLowerCase()}:${path}`,
      request: {
        method,
        path,
        ...context.request,
      },
      response: {
        statusCode,
        ...context.response,
      },
      performance: {
        duration,
        ...context.performance,
      },
    };

    const message = `API ${method} ${path} - ${statusCode} (${duration}ms)`;
    
    if (statusCode >= 500) {
      this.error(message, undefined, logContext);
    } else if (statusCode >= 400) {
      this.warn(message, logContext);
    } else {
      this.info(message, logContext);
    }
  }

  /**
   * Log database operation
   */
  logDatabaseOperation(
    operation: string,
    table: string,
    duration: number,
    success: boolean,
    context: Partial<LogContext> = {}
  ): void {
    const logContext: LogContext = {
      ...context,
      operation: `db:${operation}:${table}`,
      performance: {
        duration,
        dbQueryTime: duration,
        dbQueryCount: 1,
        ...context.performance,
      },
    };

    const message = `Database ${operation} on ${table} - ${success ? 'success' : 'failure'} (${duration}ms)`;
    
    if (success) {
      this.debug(message, logContext);
    } else {
      this.error(message, undefined, logContext);
    }
  }

  /**
   * Log external API call
   */
  logExternalApiCall(
    service: string,
    endpoint: string,
    method: string,
    statusCode: number,
    duration: number,
    context: Partial<LogContext> = {}
  ): void {
    const logContext: LogContext = {
      ...context,
      operation: `external:${service}:${method.toLowerCase()}`,
      performance: {
        duration,
        externalApiTime: duration,
        externalApiCalls: 1,
        ...context.performance,
      },
    };

    const message = `External API call to ${service} ${method} ${endpoint} - ${statusCode} (${duration}ms)`;
    
    if (statusCode >= 500) {
      this.error(message, undefined, logContext);
    } else if (statusCode >= 400) {
      this.warn(message, logContext);
    } else {
      this.info(message, logContext);
    }
  }

  /**
   * Log cache operation
   */
  logCacheOperation(
    operation: 'hit' | 'miss' | 'set' | 'delete',
    key: string,
    duration?: number,
    context: Partial<LogContext> = {}
  ): void {
    const logContext: LogContext = {
      ...context,
      operation: `cache:${operation}`,
      performance: {
        duration: duration || 0,
        cacheHits: operation === 'hit' ? 1 : 0,
        cacheMisses: operation === 'miss' ? 1 : 0,
        ...context.performance,
      },
      metadata: {
        cacheKey: key,
        ...context.metadata,
      },
    };

    const message = `Cache ${operation}: ${key}${duration ? ` (${duration}ms)` : ''}`;
    this.debug(message, logContext);
  }

  /**
   * Log user action
   */
  logUserAction(
    userId: string,
    action: string,
    resource: string,
    outcome: BusinessOutcome,
    context: Partial<LogContext> = {}
  ): void {
    const logContext: LogContext = {
      ...context,
      userId,
      operation: `user:${action}`,
      business: {
        domain: 'user',
        entity: resource,
        action,
        outcome,
        ...context.business,
      },
    };

    const message = `User action: ${userId} ${action} on ${resource} - ${outcome}`;
    this.info(message, logContext);
  }

  /**
   * Create child logger with correlation ID
   */
  withCorrelationId(correlationId: string): ServiceLoggerService {
    const childLogger = new ServiceLoggerService(this.configService);
    childLogger.setContext({ correlationId });
    return childLogger;
  }

  /**
   * Create child logger with user context
   */
  withUser(userId: string, sessionId?: string): ServiceLoggerService {
    const childLogger = new ServiceLoggerService(this.configService);
    childLogger.setContext({ userId, sessionId });
    return childLogger;
  }

  /**
   * Create child logger with operation context
   */
  withOperation(operation: string): ServiceLoggerService {
    const childLogger = new ServiceLoggerService(this.configService);
    childLogger.setContext({ operation });
    return childLogger;
  }

  /**
   * Log operation completion with performance metrics
   */
  logOperationComplete(
    operation: string,
    duration: number,
    success: boolean,
    context: Partial<LogContext> = {},
  ): void {
    const logContext: LogContext = {
      ...this.getContext(),
      ...context,
      operation,
      performance: {
        duration,
        startTime: Date.now() - duration,
        endTime: Date.now(),
        ...context.performance,
      },
    };

    const message = `Operation ${operation} ${success ? 'completed successfully' : 'failed'} in ${duration}ms`;
    if (success) {
      this.info(message, logContext);
    } else {
      this.error(message, undefined, logContext);
    }
  }

  /**
   * Get structured logger instance
   */
  getStructuredLogger() {
    return this;
  }
}
