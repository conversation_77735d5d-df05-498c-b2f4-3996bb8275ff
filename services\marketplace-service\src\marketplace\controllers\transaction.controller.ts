import { <PERSON>, <PERSON>, Param, Query, <PERSON><PERSON>, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { TransactionProcessingService } from '../services/transaction-processing.service';

@ApiTags('Transactions')
@Controller('transactions')
export class TransactionController {
  private readonly logger = new Logger(TransactionController.name);

  constructor(private readonly transactionProcessingService: TransactionProcessingService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get transaction details', description: 'Get details of a specific transaction' })
  @ApiParam({ name: 'id', description: 'Transaction ID' })
  @ApiResponse({ status: 200, description: 'Transaction details retrieved successfully' })
  async getTransaction(@Param('id') id: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting transaction details for: ${id}`);
      const result = await this.transactionProcessingService.getTransaction(id);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Transaction retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('user/:userAddress')
  @ApiOperation({ summary: 'Get user transactions', description: 'Get transaction history for a user' })
  @ApiParam({ name: 'userAddress', description: 'User wallet address' })
  @ApiQuery({ name: 'type', required: false, description: 'Transaction type filter' })
  @ApiResponse({ status: 200, description: 'User transactions retrieved successfully' })
  async getUserTransactions(
    @Param('userAddress') userAddress: string,
    @Query('type') type: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Getting transactions for user: ${userAddress}`);
      const result = await this.transactionProcessingService.getUserTransactions(userAddress, type);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`User transactions retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
