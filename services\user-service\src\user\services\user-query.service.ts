import { Injectable, Logger, NotFoundException, ServiceUnavailableException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../common/services/cache.service';
import { RequestContext } from '../interfaces/request-context.interface';

export interface UserQueryResult {
  success: boolean;
  data?: any;
  error?: string;
  fallback?: boolean;
  cached?: boolean;
}

export interface UserQueryOptions {
  includeInactive?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

@Injectable()
export class UserQueryService {
  private readonly logger = new Logger(UserQueryService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
  ) {}

  async getUserById(id: string, context: RequestContext): Promise<UserQueryResult> {
    const startTime = Date.now();
    const cacheKey = `user:${id}`;
    
    try {
      this.logger.debug(`Getting user by ID: ${id}`, { correlationId: context.correlationId });
      
      // Try cache first (enterprise caching strategy)
      const cachedUser = await this.cacheService.get(cacheKey);
      if (cachedUser) {
        this.logger.debug(`Cache hit for user: ${id}`, { correlationId: context.correlationId });
        return { 
          success: true, 
          data: cachedUser, 
          cached: true 
        };
      }
      
      // Query from read model (CQRS Query side)
      const user = await this.prisma.userQuery.findUnique({
        where: { id },
      });
      
      if (!user) {
        // Graceful fallback to command model
        return this.fallbackToCommandModel(id, context);
      }
      
      // Cache the result (5 minutes TTL)
      await this.cacheService.set(cacheKey, user, 300);
      
      this.logger.debug(`User retrieved successfully: ${id}`, { 
        correlationId: context.correlationId,
        duration: Date.now() - startTime 
      });
      
      return { success: true, data: user };
      
    } catch (error) {
      this.logger.error(`Failed to get user by ID: ${error.message}`, {
        userId: id,
        correlationId: context.correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });
      
      // Graceful fallback to command model
      return this.fallbackToCommandModel(id, context);
    }
  }

  async getUserByEmail(email: string, context: RequestContext): Promise<UserQueryResult> {
    const startTime = Date.now();
    const cacheKey = `user:email:${email}`;
    
    try {
      this.logger.debug(`Getting user by email: ${email}`, { correlationId: context.correlationId });
      
      // Try cache first
      const cachedUser = await this.cacheService.get(cacheKey);
      if (cachedUser) {
        return { 
          success: true, 
          data: cachedUser, 
          cached: true 
        };
      }
      
      // Query from read model
      const user = await this.prisma.userQuery.findUnique({
        where: { email },
      });
      
      if (!user) {
        // Graceful fallback to command model
        return this.fallbackToCommandModelByEmail(email, context);
      }
      
      // Cache the result
      await this.cacheService.set(cacheKey, user, 300);
      
      this.logger.debug(`User retrieved by email successfully: ${email}`, { 
        correlationId: context.correlationId,
        duration: Date.now() - startTime 
      });
      
      return { success: true, data: user };
      
    } catch (error) {
      this.logger.error(`Failed to get user by email: ${error.message}`, {
        email,
        correlationId: context.correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });
      
      // Graceful fallback
      return this.fallbackToCommandModelByEmail(email, context);
    }
  }

  async getUsers(options: UserQueryOptions = {}, context: RequestContext): Promise<UserQueryResult> {
    const startTime = Date.now();
    
    try {
      this.logger.debug(`Getting users with options`, { 
        options, 
        correlationId: context.correlationId 
      });
      
      const {
        includeInactive = false,
        limit = 50,
        offset = 0,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;
      
      // Build query conditions
      const where: any = {};
      if (!includeInactive) {
        where.isActive = true;
      }
      
      // Query from read model with pagination
      const [users, total] = await Promise.all([
        this.prisma.userQuery.findMany({
          where,
          take: limit,
          skip: offset,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.userQuery.count({ where }),
      ]);
      
      this.logger.debug(`Users retrieved successfully`, { 
        count: users.length,
        total,
        correlationId: context.correlationId,
        duration: Date.now() - startTime 
      });
      
      return { 
        success: true, 
        data: {
          users,
          pagination: {
            total,
            limit,
            offset,
            hasMore: offset + limit < total
          }
        }
      };
      
    } catch (error) {
      this.logger.error(`Failed to get users: ${error.message}`, {
        options,
        correlationId: context.correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });
      
      throw error;
    }
  }

  // Graceful fallback methods
  private async fallbackToCommandModel(id: string, context: RequestContext): Promise<UserQueryResult> {
    try {
      this.logger.warn(`Falling back to command model for user: ${id}`, { 
        correlationId: context.correlationId 
      });
      
      const user = await this.prisma.userCommand.findUnique({ 
        where: { id },
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          isActive: true,
          isEmailVerified: true,
          createdAt: true,
          updatedAt: true,
        }
      });
      
      if (user) {
        this.logger.warn(`Fallback to command model successful for user ${id}`);
        return { success: true, data: user, fallback: true };
      }
      
      throw new NotFoundException(`User with ID ${id} not found`);
      
    } catch (fallbackError) {
      this.logger.error(`Fallback also failed: ${fallbackError.message}`, {
        userId: id,
        correlationId: context.correlationId
      });
      
      throw new ServiceUnavailableException('User service temporarily unavailable');
    }
  }

  private async fallbackToCommandModelByEmail(email: string, context: RequestContext): Promise<UserQueryResult> {
    try {
      this.logger.warn(`Falling back to command model for email: ${email}`, { 
        correlationId: context.correlationId 
      });
      
      const user = await this.prisma.userCommand.findUnique({ 
        where: { email },
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          isActive: true,
          isEmailVerified: true,
          createdAt: true,
          updatedAt: true,
        }
      });
      
      if (user) {
        this.logger.warn(`Fallback to command model successful for email ${email}`);
        return { success: true, data: user, fallback: true };
      }
      
      throw new NotFoundException(`User with email ${email} not found`);
      
    } catch (fallbackError) {
      this.logger.error(`Fallback also failed: ${fallbackError.message}`, {
        email,
        correlationId: context.correlationId
      });
      
      throw new ServiceUnavailableException('User service temporarily unavailable');
    }
  }
}
