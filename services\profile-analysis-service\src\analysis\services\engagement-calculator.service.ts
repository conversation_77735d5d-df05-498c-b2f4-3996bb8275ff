import { Injectable, Logger } from '@nestjs/common';

export interface EngagementMetrics {
  overallEngagementRate: number;
  likesEngagementRate: number;
  retweetsEngagementRate: number;
  repliesEngagementRate: number;
  engagementVelocity: number;
  engagementConsistency: number;
  peakEngagementTimes: string[];
  engagementTrend: 'increasing' | 'decreasing' | 'stable';
  engagementQuality: number;
  viralPotential: number;
}

export interface TwitterProfileData {
  id: string;
  username: string;
  displayName: string;
  description?: string;
  followersCount: number;
  followingCount: number;
  tweetsCount: number;
  listedCount: number;
  verified: boolean;
  profileImageUrl?: string;
  location?: string;
  website?: string;
  createdAt: string;
  // Enhanced data for advanced analysis
  recentTweets?: any[];
  engagementHistory?: any[];
  followerGrowth?: any[];
}

@Injectable()
export class EngagementCalculatorService {
  private readonly logger = new Logger(EngagementCalculatorService.name);

  constructor() {}

  /**
   * Calculate comprehensive engagement metrics
   */
  async calculateEngagementMetrics(profileData: TwitterProfileData): Promise<EngagementMetrics> {
    try {
      // Calculate basic engagement rate
      const basicEngagementRate = this.calculateBasicEngagementRate(profileData);

      // Calculate advanced engagement metrics
      const advancedMetrics = await this.calculateAdvancedEngagementMetrics(profileData);

      // Calculate engagement quality and viral potential
      const engagementQuality = this.calculateEngagementQuality(profileData, advancedMetrics);
      const viralPotential = this.calculateViralPotential(profileData, advancedMetrics);

      const metrics: EngagementMetrics = {
        overallEngagementRate: basicEngagementRate,
        likesEngagementRate: advancedMetrics.likesRate,
        retweetsEngagementRate: advancedMetrics.retweetsRate,
        repliesEngagementRate: advancedMetrics.repliesRate,
        engagementVelocity: advancedMetrics.velocity,
        engagementConsistency: advancedMetrics.consistency,
        peakEngagementTimes: advancedMetrics.peakTimes,
        engagementTrend: advancedMetrics.trend,
        engagementQuality,
        viralPotential,
      };

      return metrics;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Calculate basic engagement rate
   */
  private calculateBasicEngagementRate(profileData: TwitterProfileData): number {
    if (profileData.followersCount === 0) return 0;

    // Basic formula: (average interactions per tweet / followers) * 100
    const avgInteractionsPerTweet = this.estimateAverageInteractions(profileData);
    const engagementRate = (avgInteractionsPerTweet / profileData.followersCount) * 100;

    return Math.min(100, Math.max(0, engagementRate));
  }

  /**
   * Estimate average interactions per tweet
   */
  private estimateAverageInteractions(profileData: TwitterProfileData): number {
    // If we have recent tweets data, use actual data
    if (profileData.recentTweets && profileData.recentTweets.length > 0) {
      const totalInteractions = profileData.recentTweets.reduce((sum, tweet) => {
        return sum + (tweet.likes || 0) + (tweet.retweets || 0) + (tweet.replies || 0);
      }, 0);
      return totalInteractions / profileData.recentTweets.length;
    }

    // Otherwise, estimate based on follower count and account characteristics
    return this.estimateInteractionsFromProfile(profileData);
  }

  /**
   * Estimate interactions from profile characteristics
   */
  private estimateInteractionsFromProfile(profileData: TwitterProfileData): number {
    let baseInteractions = profileData.followersCount * 0.02; // 2% base engagement

    // Adjust based on account characteristics
    if (profileData.verified) {
      baseInteractions *= 1.5; // Verified accounts typically get more engagement
    }

    if (profileData.followersCount > 100000) {
      baseInteractions *= 0.8; // Large accounts often have lower engagement rates
    }

    if (profileData.followersCount < 1000) {
      baseInteractions *= 1.2; // Smaller accounts often have higher engagement rates
    }

    // Account age factor
    const accountAge = this.calculateAccountAge(profileData.createdAt);
    if (accountAge < 365) {
      baseInteractions *= 0.9; // Newer accounts might have lower engagement
    }

    return Math.max(1, baseInteractions);
  }

  /**
   * Calculate advanced engagement metrics
   */
  private async calculateAdvancedEngagementMetrics(profileData: TwitterProfileData): Promise<{
    likesRate: number;
    retweetsRate: number;
    repliesRate: number;
    velocity: number;
    consistency: number;
    peakTimes: string[];
    trend: 'increasing' | 'decreasing' | 'stable';
  }> {
    // If we have recent tweets, calculate from actual data
    if (profileData.recentTweets && profileData.recentTweets.length > 0) {
      return this.calculateFromRecentTweets(profileData.recentTweets, profileData.followersCount);
    }

    // Otherwise, estimate based on profile characteristics
    return this.estimateAdvancedMetrics(profileData);
  }

  /**
   * Calculate metrics from recent tweets data
   */
  private calculateFromRecentTweets(recentTweets: any[], followersCount: number): {
    likesRate: number;
    retweetsRate: number;
    repliesRate: number;
    velocity: number;
    consistency: number;
    peakTimes: string[];
    trend: 'increasing' | 'decreasing' | 'stable';
  } {
    const totalLikes = recentTweets.reduce((sum, tweet) => sum + (tweet.likes || 0), 0);
    const totalRetweets = recentTweets.reduce((sum, tweet) => sum + (tweet.retweets || 0), 0);
    const totalReplies = recentTweets.reduce((sum, tweet) => sum + (tweet.replies || 0), 0);

    const likesRate = followersCount > 0 ? (totalLikes / recentTweets.length / followersCount) * 100 : 0;
    const retweetsRate = followersCount > 0 ? (totalRetweets / recentTweets.length / followersCount) * 100 : 0;
    const repliesRate = followersCount > 0 ? (totalReplies / recentTweets.length / followersCount) * 100 : 0;

    // Calculate engagement velocity (how quickly tweets get engagement)
    const velocity = this.calculateEngagementVelocity(recentTweets);

    // Calculate consistency (how consistent engagement is across tweets)
    const consistency = this.calculateEngagementConsistency(recentTweets);

    // Analyze peak engagement times
    const peakTimes = this.analyzePeakEngagementTimes(recentTweets);

    // Determine engagement trend
    const trend = this.determineEngagementTrend(recentTweets);

    return {
      likesRate: Math.min(100, Math.max(0, likesRate)),
      retweetsRate: Math.min(100, Math.max(0, retweetsRate)),
      repliesRate: Math.min(100, Math.max(0, repliesRate)),
      velocity,
      consistency,
      peakTimes,
      trend,
    };
  }

  /**
   * Estimate advanced metrics from profile data
   */
  private estimateAdvancedMetrics(profileData: TwitterProfileData): {
    likesRate: number;
    retweetsRate: number;
    repliesRate: number;
    velocity: number;
    consistency: number;
    peakTimes: string[];
    trend: 'increasing' | 'decreasing' | 'stable';
  } {
    const baseEngagement = this.calculateBasicEngagementRate(profileData);

    // Estimate distribution of engagement types
    const likesRate = baseEngagement * 0.7; // Likes typically make up 70% of engagement
    const retweetsRate = baseEngagement * 0.2; // Retweets about 20%
    const repliesRate = baseEngagement * 0.1; // Replies about 10%

    // Estimate other metrics based on account characteristics
    const velocity = this.estimateEngagementVelocity(profileData);
    const consistency = this.estimateEngagementConsistency(profileData);
    const peakTimes = this.estimatePeakTimes(profileData);
    const trend = this.estimateEngagementTrend(profileData);

    return {
      likesRate,
      retweetsRate,
      repliesRate,
      velocity,
      consistency,
      peakTimes,
      trend,
    };
  }

  /**
   * Calculate engagement velocity
   */
  private calculateEngagementVelocity(recentTweets: any[]): number {
    // Analyze how quickly tweets get engagement after posting
    // Higher velocity = faster engagement
    let totalVelocity = 0;
    let validTweets = 0;

    for (const tweet of recentTweets) {
      if (tweet.createdAt && tweet.engagementTimeline) {
        const velocity = this.analyzeTweetVelocity(tweet);
        totalVelocity += velocity;
        validTweets++;
      }
    }

    return validTweets > 0 ? totalVelocity / validTweets : 50; // Default to medium velocity
  }

  /**
   * Analyze individual tweet velocity
   */
  private analyzeTweetVelocity(tweet: any): number {
    // If engagement timeline is available, calculate actual velocity
    if (tweet.engagementTimeline && tweet.engagementTimeline.length > 0) {
      const firstHourEngagement = tweet.engagementTimeline
        .filter(point => point.timestamp <= tweet.createdAt + 3600000) // First hour
        .reduce((sum, point) => sum + point.engagement, 0);

      const totalEngagement = tweet.likes + tweet.retweets + tweet.replies;
      return totalEngagement > 0 ? (firstHourEngagement / totalEngagement) * 100 : 0;
    }

    // Estimate based on tweet characteristics
    return this.estimateTweetVelocity(tweet);
  }

  /**
   * Estimate tweet velocity
   */
  private estimateTweetVelocity(tweet: any): number {
    let velocity = 50; // Base velocity

    // Tweets with media typically get faster engagement
    if (tweet.hasMedia) velocity += 15;

    // Tweets with hashtags might get discovered faster
    if (tweet.hashtags && tweet.hashtags.length > 0) velocity += 10;

    // Tweets with mentions might get faster responses
    if (tweet.mentions && tweet.mentions.length > 0) velocity += 5;

    return Math.min(100, Math.max(0, velocity));
  }

  /**
   * Calculate engagement consistency
   */
  private calculateEngagementConsistency(recentTweets: any[]): number {
    if (recentTweets.length < 2) return 50;

    const engagementRates = recentTweets.map(tweet => {
      return (tweet.likes || 0) + (tweet.retweets || 0) + (tweet.replies || 0);
    });

    const mean = engagementRates.reduce((sum, rate) => sum + rate, 0) / engagementRates.length;
    const variance = engagementRates.reduce((sum, rate) => sum + Math.pow(rate - mean, 2), 0) / engagementRates.length;
    const standardDeviation = Math.sqrt(variance);

    // Lower standard deviation relative to mean = higher consistency
    const coefficientOfVariation = mean > 0 ? standardDeviation / mean : 1;
    const consistency = Math.max(0, 100 - (coefficientOfVariation * 50));

    return Math.min(100, consistency);
  }

  /**
   * Estimate engagement consistency
   */
  private estimateEngagementConsistency(profileData: TwitterProfileData): number {
    let consistency = 50; // Base consistency

    // Verified accounts tend to have more consistent engagement
    if (profileData.verified) consistency += 20;

    // Accounts with regular posting tend to be more consistent
    const accountAge = this.calculateAccountAge(profileData.createdAt);
    const tweetsPerDay = profileData.tweetsCount / Math.max(1, accountAge);

    if (tweetsPerDay >= 0.5 && tweetsPerDay <= 5) {
      consistency += 15; // Regular posting schedule
    }

    // Complete profiles tend to have more consistent engagement
    if (profileData.description && profileData.profileImageUrl) {
      consistency += 10;
    }

    return Math.min(100, Math.max(0, consistency));
  }

  /**
   * Analyze peak engagement times
   */
  private analyzePeakEngagementTimes(recentTweets: any[]): string[] {
    const hourlyEngagement = new Array(24).fill(0);
    const hourlyCounts = new Array(24).fill(0);

    for (const tweet of recentTweets) {
      if (tweet.createdAt) {
        const hour = new Date(tweet.createdAt).getHours();
        const engagement = (tweet.likes || 0) + (tweet.retweets || 0) + (tweet.replies || 0);
        hourlyEngagement[hour] += engagement;
        hourlyCounts[hour]++;
      }
    }

    // Calculate average engagement per hour
    const avgEngagementPerHour = hourlyEngagement.map((total, hour) => 
      hourlyCounts[hour] > 0 ? total / hourlyCounts[hour] : 0
    );

    // Find top 3 peak hours
    const peakHours = avgEngagementPerHour
      .map((avg, hour) => ({ hour, avg }))
      .sort((a, b) => b.avg - a.avg)
      .slice(0, 3)
      .map(item => `${item.hour}:00-${item.hour + 1}:00`);

    return peakHours;
  }

  /**
   * Estimate peak engagement times
   */
  private estimatePeakTimes(profileData: TwitterProfileData): string[] {
    // Default peak times based on general Twitter usage patterns
    return ['9:00-10:00', '12:00-13:00', '19:00-20:00'];
  }

  /**
   * Determine engagement trend
   */
  private determineEngagementTrend(recentTweets: any[]): 'increasing' | 'decreasing' | 'stable' {
    if (recentTweets.length < 5) return 'stable';

    // Sort tweets by date
    const sortedTweets = recentTweets.sort((a, b) => 
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    // Calculate engagement for first and second half
    const midpoint = Math.floor(sortedTweets.length / 2);
    const firstHalf = sortedTweets.slice(0, midpoint);
    const secondHalf = sortedTweets.slice(midpoint);

    const firstHalfAvg = this.calculateAverageEngagement(firstHalf);
    const secondHalfAvg = this.calculateAverageEngagement(secondHalf);

    const changePercent = ((secondHalfAvg - firstHalfAvg) / Math.max(1, firstHalfAvg)) * 100;

    if (changePercent > 10) return 'increasing';
    if (changePercent < -10) return 'decreasing';
    return 'stable';
  }

  /**
   * Estimate engagement trend
   */
  private estimateEngagementTrend(profileData: TwitterProfileData): 'increasing' | 'decreasing' | 'stable' {
    // Estimate based on account characteristics
    const accountAge = this.calculateAccountAge(profileData.createdAt);
    
    if (accountAge < 180) return 'increasing'; // New accounts typically growing
    if (profileData.verified) return 'stable'; // Verified accounts tend to be stable
    
    return 'stable'; // Default assumption
  }

  /**
   * Calculate average engagement for tweets
   */
  private calculateAverageEngagement(tweets: any[]): number {
    if (tweets.length === 0) return 0;

    const totalEngagement = tweets.reduce((sum, tweet) => 
      sum + (tweet.likes || 0) + (tweet.retweets || 0) + (tweet.replies || 0), 0
    );

    return totalEngagement / tweets.length;
  }

  /**
   * Calculate engagement quality
   */
  private calculateEngagementQuality(profileData: TwitterProfileData, metrics: any): number {
    let quality = 50; // Base quality

    // Higher reply rate indicates better quality engagement
    if (metrics.repliesRate > 0.5) quality += 20;
    else if (metrics.repliesRate > 0.2) quality += 10;

    // Consistent engagement indicates quality
    if (metrics.consistency > 70) quality += 15;
    else if (metrics.consistency > 50) quality += 10;

    // Verified accounts typically have higher quality engagement
    if (profileData.verified) quality += 10;

    // Reasonable follower/following ratio
    const ratio = profileData.followingCount > 0 ? profileData.followersCount / profileData.followingCount : 0;
    if (ratio > 1 && ratio < 100) quality += 5;

    return Math.min(100, Math.max(0, quality));
  }

  /**
   * Calculate viral potential
   */
  private calculateViralPotential(profileData: TwitterProfileData, metrics: any): number {
    let potential = 30; // Base potential

    // High retweet rate indicates viral potential
    if (metrics.retweetsRate > 1) potential += 25;
    else if (metrics.retweetsRate > 0.5) potential += 15;

    // High engagement velocity indicates viral potential
    if (metrics.velocity > 70) potential += 20;
    else if (metrics.velocity > 50) potential += 10;

    // Large follower base increases viral potential
    if (profileData.followersCount > 50000) potential += 15;
    else if (profileData.followersCount > 10000) potential += 10;

    // Verified accounts have higher viral potential
    if (profileData.verified) potential += 10;

    return Math.min(100, Math.max(0, potential));
  }

  /**
   * Estimate engagement velocity
   */
  private estimateEngagementVelocity(profileData: TwitterProfileData): number {
    let velocity = 50; // Base velocity

    if (profileData.verified) velocity += 20;
    if (profileData.followersCount > 10000) velocity += 15;
    if (profileData.description && profileData.profileImageUrl) velocity += 10;

    return Math.min(100, Math.max(0, velocity));
  }

  /**
   * Calculate account age in days
   */
  private calculateAccountAge(createdAt: string): number {
    return Math.floor((Date.now() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24));
  }
}
