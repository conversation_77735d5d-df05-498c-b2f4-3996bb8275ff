import { <PERSON>, Get, Put, Body, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { PreferenceService } from '../services/preference.service';

@ApiTags('preferences')
@Controller('preferences')
export class PreferenceController {
  private readonly logger = new Logger(PreferenceController.name);

  constructor(private readonly preferenceService: PreferenceService) {}

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get user notification preferences' })
  @ApiResponse({ status: 200, description: 'User preferences retrieved successfully' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  async getUserPreferences(@Param('userId') userId: string) {
    try {
      this.logger.log(`Getting preferences for user: ${userId}`);

      const preferences = await this.preferenceService.getUserPreferences(userId);

      return {
        success: true,
        data: preferences,
      };
    } catch (error) {
      this.logger.error(`Failed to get user preferences: ${error.message}`);
      return {
        success: false,
        error: 'Failed to retrieve user preferences',
        details: error.message,
      };
    }
  }

  @Put('user/:userId/category/:category')
  @ApiOperation({ summary: 'Update user preference for specific category' })
  @ApiResponse({ status: 200, description: 'Preference updated successfully' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiParam({ name: 'category', description: 'Notification category' })
  async updateUserPreference(
    @Param('userId') userId: string,
    @Param('category') category: string,
    @Body() preferenceData: any
  ) {
    try {
      this.logger.log(`Updating preference for user ${userId}, category: ${category}`);

      const preference = await this.preferenceService.updateUserPreference(userId, category, preferenceData);

      return {
        success: true,
        data: preference,
      };
    } catch (error) {
      this.logger.error(`Failed to update user preference: ${error.message}`);
      return {
        success: false,
        error: 'Failed to update preference',
        details: error.message,
      };
    }
  }

  @Put('user/:userId/global/:setting')
  @ApiOperation({ summary: 'Update global notification setting' })
  @ApiResponse({ status: 200, description: 'Global setting updated successfully' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiParam({ name: 'setting', description: 'Global setting name' })
  async updateGlobalSetting(
    @Param('userId') userId: string,
    @Param('setting') setting: string,
    @Body() settingData: { enabled: boolean }
  ) {
    try {
      this.logger.log(`Updating global setting for user ${userId}: ${setting} = ${settingData.enabled}`);

      const success = await this.preferenceService.setGlobalPreference(userId, setting, settingData.enabled);

      return {
        success,
        message: success ? 'Global setting updated successfully' : 'Failed to update global setting',
      };
    } catch (error) {
      this.logger.error(`Failed to update global setting: ${error.message}`);
      return {
        success: false,
        error: 'Failed to update global setting',
        details: error.message,
      };
    }
  }
}
