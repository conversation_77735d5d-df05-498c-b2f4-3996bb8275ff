'use client'

import React, { useState } from 'react'
import {
  DocumentArrowDownIcon,
  PlusIcon,
  EyeIcon,
  TrashIcon,
  ClockIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import {
  useReportTemplates,
  useCreateReportTemplate,
  useGenerateReport,
  useGeneratedReports,
  useDownloadReport
} from '@/hooks/useAnalytics'
import { ReportFormat, ChartType } from '@/types/analytics.types'

interface ReportBuilderProps {
  campaignId: string
  className?: string
}

export default function ReportBuilder({
  campaignId,
  className = ''
}: ReportBuilderProps) {
  const [activeTab, setActiveTab] = useState<'templates' | 'generated'>('templates')
  const [showCreateTemplate, setShowCreateTemplate] = useState(false)

  const { data: templates, isLoading: templatesLoading } = useReportTemplates()
  const { data: generatedReports, isLoading: reportsLoading } = useGeneratedReports(campaignId)
  const createTemplateMutation = useCreateReportTemplate()
  const generateReportMutation = useGenerateReport()
  const downloadReportMutation = useDownloadReport()

  const handleGenerateReport = async (templateId: string, format: ReportFormat = ReportFormat.PDF) => {
    try {
      await generateReportMutation.mutateAsync({
        templateId,
        campaignId,
        format
      })
    } catch (error) {
      console.error('Failed to generate report:', error)
    }
  }

  const handleDownloadReport = async (reportId: string, filename: string) => {
    try {
      const blob = await downloadReportMutation.mutateAsync(reportId)
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to download report:', error)
    }
  }

  const tabs = [
    { id: 'templates', name: 'Report Templates', count: templates?.length },
    { id: 'generated', name: 'Generated Reports', count: generatedReports?.length }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Report Builder</h2>
          <p className="text-sm text-gray-600">Create and manage custom analytics reports</p>
        </div>

        <button
          onClick={() => setShowCreateTemplate(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Template
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
              {tab.count !== undefined && (
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'templates' && (
        <div className="space-y-4">
          {templatesLoading ? (
            <div className="animate-pulse space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
          ) : templates && templates.length > 0 ? (
            <div className="space-y-4">
              {templates.map((template) => (
                <div key={template.id} className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">{template.name}</h3>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700">
                          {template.category}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{template.metrics.length} metrics</span>
                        <span>{template.charts.length} charts</span>
                        <span>Updated {new Date(template.updatedAt).toLocaleDateString()}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        className="p-2 text-gray-400 hover:text-gray-600"
                        title="Preview Template"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>

                      <div className="relative">
                        <select
                          onChange={(e) => {
                            if (e.target.value) {
                              handleGenerateReport(template.id, e.target.value as ReportFormat)
                              e.target.value = ''
                            }
                          }}
                          className="appearance-none bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          defaultValue=""
                        >
                          <option value="" disabled>Generate Report</option>
                          <option value={ReportFormat.PDF}>PDF Report</option>
                          <option value={ReportFormat.CSV}>CSV Data</option>
                          <option value={ReportFormat.EXCEL}>Excel File</option>
                          <option value={ReportFormat.JSON}>JSON Data</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No report templates</h3>
              <p className="mt-1 text-sm text-gray-500">
                Create your first report template to get started.
              </p>
              <div className="mt-6">
                <button
                  onClick={() => setShowCreateTemplate(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create Template
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === 'generated' && (
        <div className="space-y-4">
          {reportsLoading ? (
            <div className="animate-pulse space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded"></div>
              ))}
            </div>
          ) : generatedReports && generatedReports.length > 0 ? (
            <div className="space-y-4">
              {generatedReports.map((report) => (
                <div key={report.id} className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">{report.name}</h3>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                          {report.format.toUpperCase()}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <ClockIcon className="h-4 w-4" />
                          <span>Generated {new Date(report.generatedAt).toLocaleDateString()}</span>
                        </div>
                        <span>{report.charts.length} charts</span>
                        {report.metadata.fileSize && (
                          <span>{(report.metadata.fileSize / 1024 / 1024).toFixed(1)} MB</span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        className="p-2 text-gray-400 hover:text-gray-600"
                        title="Preview Report"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => handleDownloadReport(report.id, `${report.name}.${report.format}`)}
                        disabled={downloadReportMutation.isPending}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                      >
                        <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                        {downloadReportMutation.isPending ? 'Downloading...' : 'Download'}
                      </button>

                      <button
                        className="p-2 text-gray-400 hover:text-red-600"
                        title="Delete Report"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
              <DocumentArrowDownIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No generated reports</h3>
              <p className="mt-1 text-sm text-gray-500">
                Generate reports from templates to see them here.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Create Template Modal */}
      {showCreateTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Create Report Template</h3>
              <button
                onClick={() => setShowCreateTemplate(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Monthly Performance Report"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe what this report template includes..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500">
                  <option value="performance">Performance</option>
                  <option value="engagement">Engagement</option>
                  <option value="financial">Financial</option>
                  <option value="social">Social</option>
                  <option value="custom">Custom</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Include Metrics
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    'Total Participants',
                    'Engagement Rate',
                    'Conversion Rate',
                    'Revenue',
                    'ROI',
                    'Social Shares',
                    'Geographic Distribution',
                    'Quality Score'
                  ].map((metric) => (
                    <label key={metric} className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{metric}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Chart Types
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { value: ChartType.LINE, label: 'Line Chart' },
                    { value: ChartType.BAR, label: 'Bar Chart' },
                    { value: ChartType.PIE, label: 'Pie Chart' },
                    { value: ChartType.AREA, label: 'Area Chart' }
                  ].map((chart) => (
                    <label key={chart.value} className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{chart.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => setShowCreateTemplate(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  disabled={createTemplateMutation.isPending}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {createTemplateMutation.isPending ? 'Creating...' : 'Create Template'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
