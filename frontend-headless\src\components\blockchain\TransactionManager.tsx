'use client'

import React, { useState } from 'react'
import {
  ArrowsRightLeftIcon,
  PlusIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  BoltIcon,
  CurrencyDollarIcon,
  ArrowPathIcon,
  StopIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import {
  useSendTransaction,
  useTransactionHistory,
  useConnectedWallets,
  useCancelTransaction,
  useSpeedUpTransaction,
  useEstimateGas,
  useGasPrice
} from '@/hooks/useBlockchainIntegration'
import { Transaction, TransactionStatus, TransactionType, BlockchainNetwork } from '@/types/blockchain-integration.types'

interface TransactionManagerProps {
  onTransactionSent?: (transactionHash: string) => void
  className?: string
}

export default function TransactionManager({
  onTransactionSent,
  className = ''
}: TransactionManagerProps) {
  const [showSendModal, setShowSendModal] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null)
  const [filterStatus, setFilterStatus] = useState<TransactionStatus | 'all'>('all')
  const [filterType, setFilterType] = useState<TransactionType | 'all'>('all')

  const { data: connectedWallets } = useConnectedWallets()
  const activeWallet = connectedWallets?.[0]
  
  const { data: transactions, isLoading } = useTransactionHistory(
    activeWallet?.address || '',
    activeWallet?.network
  )
  
  const sendTransactionMutation = useSendTransaction()
  const cancelTransactionMutation = useCancelTransaction()
  const speedUpTransactionMutation = useSpeedUpTransaction()

  const getStatusIcon = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.CONFIRMED:
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />
      case TransactionStatus.PENDING:
        return <ClockIcon className="h-5 w-5 text-yellow-600" />
      case TransactionStatus.FAILED:
        return <XCircleIcon className="h-5 w-5 text-red-600" />
      case TransactionStatus.CANCELLED:
        return <StopIcon className="h-5 w-5 text-orange-600" />
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusColor = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.CONFIRMED:
        return 'text-green-600 bg-green-100'
      case TransactionStatus.PENDING:
        return 'text-yellow-600 bg-yellow-100'
      case TransactionStatus.FAILED:
        return 'text-red-600 bg-red-100'
      case TransactionStatus.CANCELLED:
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeColor = (type: TransactionType) => {
    switch (type) {
      case TransactionType.NFT_MINT:
      case TransactionType.NFT_TRANSFER:
        return 'text-blue-600 bg-blue-100'
      case TransactionType.EVOLUTION:
        return 'text-purple-600 bg-purple-100'
      case TransactionType.MARKETPLACE:
        return 'text-green-600 bg-green-100'
      case TransactionType.BRIDGE:
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const filteredTransactions = transactions?.filter(tx => {
    const statusMatch = filterStatus === 'all' || tx.status === filterStatus
    const typeMatch = filterType === 'all' || tx.type === filterType
    return statusMatch && typeMatch
  }) || []

  const handleCancelTransaction = async (hash: string, gasPrice: string) => {
    try {
      await cancelTransactionMutation.mutateAsync({ hash, gasPrice })
    } catch (error) {
      console.error('Failed to cancel transaction:', error)
    }
  }

  const handleSpeedUpTransaction = async (hash: string, gasPrice: string) => {
    try {
      await speedUpTransactionMutation.mutateAsync({ hash, gasPrice })
    } catch (error) {
      console.error('Failed to speed up transaction:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Transaction Management</h2>
          <p className="text-sm text-gray-600">Monitor and manage blockchain transactions</p>
        </div>
        
        <button
          onClick={() => setShowSendModal(true)}
          disabled={!activeWallet}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Send Transaction
        </button>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Status</option>
          <option value={TransactionStatus.PENDING}>Pending</option>
          <option value={TransactionStatus.CONFIRMED}>Confirmed</option>
          <option value={TransactionStatus.FAILED}>Failed</option>
          <option value={TransactionStatus.CANCELLED}>Cancelled</option>
        </select>

        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Types</option>
          <option value={TransactionType.TRANSFER}>Transfer</option>
          <option value={TransactionType.NFT_MINT}>NFT Mint</option>
          <option value={TransactionType.NFT_TRANSFER}>NFT Transfer</option>
          <option value={TransactionType.EVOLUTION}>Evolution</option>
          <option value={TransactionType.MARKETPLACE}>Marketplace</option>
        </select>
      </div>

      {/* Transaction List */}
      {filteredTransactions.length > 0 ? (
        <div className="space-y-4">
          {filteredTransactions.map((transaction) => (
            <TransactionCard
              key={transaction.id}
              transaction={transaction}
              onCancel={(gasPrice) => handleCancelTransaction(transaction.hash, gasPrice)}
              onSpeedUp={(gasPrice) => handleSpeedUpTransaction(transaction.hash, gasPrice)}
              onViewDetails={() => setSelectedTransaction(transaction)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <ArrowsRightLeftIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {activeWallet ? 'Send your first transaction to get started.' : 'Connect a wallet to view transactions.'}
          </p>
        </div>
      )}

      {/* Transaction Statistics */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Transaction Statistics</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{transactions?.length || 0}</div>
            <div className="text-sm text-gray-600">Total Transactions</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {transactions?.filter(tx => tx.status === TransactionStatus.PENDING).length || 0}
            </div>
            <div className="text-sm text-gray-600">Pending</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {transactions?.filter(tx => tx.status === TransactionStatus.CONFIRMED).length || 0}
            </div>
            <div className="text-sm text-gray-600">Confirmed</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {transactions?.filter(tx => tx.status === TransactionStatus.FAILED).length || 0}
            </div>
            <div className="text-sm text-gray-600">Failed</div>
          </div>
        </div>
      </div>

      {/* Send Transaction Modal */}
      {showSendModal && activeWallet && (
        <SendTransactionModal
          wallet={activeWallet}
          onClose={() => setShowSendModal(false)}
          onSend={(txData) => {
            sendTransactionMutation.mutate(txData, {
              onSuccess: (transaction) => {
                onTransactionSent?.(transaction.hash)
                setShowSendModal(false)
              }
            })
          }}
        />
      )}

      {/* Transaction Details Modal */}
      {selectedTransaction && (
        <TransactionDetailsModal
          transaction={selectedTransaction}
          onClose={() => setSelectedTransaction(null)}
        />
      )}
    </div>
  )
}

interface TransactionCardProps {
  transaction: Transaction
  onCancel: (gasPrice: string) => void
  onSpeedUp: (gasPrice: string) => void
  onViewDetails: () => void
}

function TransactionCard({ transaction, onCancel, onSpeedUp, onViewDetails }: TransactionCardProps) {
  const getStatusColor = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.CONFIRMED:
        return 'text-green-600 bg-green-100'
      case TransactionStatus.PENDING:
        return 'text-yellow-600 bg-yellow-100'
      case TransactionStatus.FAILED:
        return 'text-red-600 bg-red-100'
      case TransactionStatus.CANCELLED:
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeColor = (type: TransactionType) => {
    switch (type) {
      case TransactionType.NFT_MINT:
      case TransactionType.NFT_TRANSFER:
        return 'text-blue-600 bg-blue-100'
      case TransactionType.EVOLUTION:
        return 'text-purple-600 bg-purple-100'
      case TransactionType.MARKETPLACE:
        return 'text-green-600 bg-green-100'
      case TransactionType.BRIDGE:
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">{transaction.description}</h3>
          <p className="text-sm text-gray-600">{transaction.hash.slice(0, 10)}...{transaction.hash.slice(-8)}</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
            {transaction.status}
          </span>
          
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(transaction.type)}`}>
            {transaction.type.replace('_', ' ')}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Value</div>
          <div className="text-sm text-gray-600">{parseFloat(transaction.value).toFixed(4)} ETH</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Gas Used</div>
          <div className="text-sm text-gray-600">
            {transaction.gasUsed ? parseInt(transaction.gasUsed).toLocaleString() : 'Pending'}
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Confirmations</div>
          <div className="text-sm text-gray-600">{transaction.confirmations}/{transaction.requiredConfirmations}</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm font-medium text-gray-900">Network</div>
          <div className="text-sm text-gray-600 capitalize">{transaction.network}</div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          {new Date(transaction.submittedAt).toLocaleString()}
        </div>
        
        <div className="flex items-center space-x-2">
          {transaction.status === TransactionStatus.PENDING && (
            <>
              <button
                onClick={() => onSpeedUp('50')}
                className="text-sm text-blue-600 hover:text-blue-700"
                title="Speed up transaction"
              >
                <BoltIcon className="h-4 w-4" />
              </button>
              
              <button
                onClick={() => onCancel('60')}
                className="text-sm text-red-600 hover:text-red-700"
                title="Cancel transaction"
              >
                <StopIcon className="h-4 w-4" />
              </button>
            </>
          )}
          
          <button
            onClick={onViewDetails}
            className="text-sm text-gray-600 hover:text-gray-700"
            title="View details"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

interface SendTransactionModalProps {
  wallet: any
  onClose: () => void
  onSend: (txData: any) => void
}

function SendTransactionModal({ wallet, onClose, onSend }: SendTransactionModalProps) {
  const [txData, setTxData] = useState({
    to: '',
    value: '',
    data: '',
    gasLimit: '',
    gasPrice: ''
  })

  const { data: gasPrice } = useGasPrice(wallet.network)
  const estimateGasMutation = useEstimateGas()

  const handleEstimateGas = async () => {
    if (txData.to && txData.value) {
      try {
        const estimate = await estimateGasMutation.mutateAsync({
          network: wallet.network,
          transaction: {
            to: txData.to,
            value: txData.value,
            data: txData.data
          }
        })
        
        setTxData(prev => ({
          ...prev,
          gasLimit: estimate.estimates.standard.gasLimit,
          gasPrice: estimate.estimates.standard.gasPrice || ''
        }))
      } catch (error) {
        console.error('Failed to estimate gas:', error)
      }
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSend(txData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Send Transaction</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">×</button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">To Address</label>
            <input
              type="text"
              required
              value={txData.to}
              onChange={(e) => setTxData(prev => ({ ...prev, to: e.target.value }))}
              placeholder="0x..."
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Value (ETH)</label>
            <input
              type="number"
              step="0.000001"
              value={txData.value}
              onChange={(e) => setTxData(prev => ({ ...prev, value: e.target.value }))}
              placeholder="0.0"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Data (Optional)</label>
            <textarea
              rows={3}
              value={txData.data}
              onChange={(e) => setTxData(prev => ({ ...prev, data: e.target.value }))}
              placeholder="0x..."
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Gas Limit</label>
              <input
                type="number"
                value={txData.gasLimit}
                onChange={(e) => setTxData(prev => ({ ...prev, gasLimit: e.target.value }))}
                placeholder="21000"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Gas Price (gwei)</label>
              <input
                type="number"
                value={txData.gasPrice}
                onChange={(e) => setTxData(prev => ({ ...prev, gasPrice: e.target.value }))}
                placeholder={gasPrice?.standard || "20"}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <button
            type="button"
            onClick={handleEstimateGas}
            className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Estimate Gas
          </button>

          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Send Transaction
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

interface TransactionDetailsModalProps {
  transaction: Transaction
  onClose: () => void
}

function TransactionDetailsModal({ transaction, onClose }: TransactionDetailsModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4 max-h-96 overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Transaction Details</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">×</button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Hash</label>
            <div className="mt-1 text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded break-all">
              {transaction.hash}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <div className="mt-1 text-sm text-gray-900 capitalize">{transaction.status}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Type</label>
              <div className="mt-1 text-sm text-gray-900">{transaction.type.replace('_', ' ')}</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">From</label>
              <div className="mt-1 text-sm text-gray-900 font-mono">{transaction.from}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">To</label>
              <div className="mt-1 text-sm text-gray-900 font-mono">{transaction.to}</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Value</label>
              <div className="mt-1 text-sm text-gray-900">{parseFloat(transaction.value).toFixed(6)} ETH</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Network</label>
              <div className="mt-1 text-sm text-gray-900 capitalize">{transaction.network}</div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Gas Limit</label>
              <div className="mt-1 text-sm text-gray-900">{parseInt(transaction.gasLimit).toLocaleString()}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Gas Used</label>
              <div className="mt-1 text-sm text-gray-900">
                {transaction.gasUsed ? parseInt(transaction.gasUsed).toLocaleString() : 'Pending'}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Gas Price</label>
              <div className="mt-1 text-sm text-gray-900">{parseInt(transaction.gasPrice)} gwei</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Submitted</label>
              <div className="mt-1 text-sm text-gray-900">
                {new Date(transaction.submittedAt).toLocaleString()}
              </div>
            </div>
            {transaction.confirmedAt && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Confirmed</label>
                <div className="mt-1 text-sm text-gray-900">
                  {new Date(transaction.confirmedAt).toLocaleString()}
                </div>
              </div>
            )}
          </div>

          {transaction.blockNumber && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Block Number</label>
              <div className="mt-1 text-sm text-gray-900">{transaction.blockNumber.toLocaleString()}</div>
            </div>
          )}

          {transaction.error && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Error</label>
              <div className="mt-1 text-sm text-red-600 bg-red-50 p-2 rounded">{transaction.error}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
