import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { performanceService } from '@/services/performanceService'
import {
  PerformanceMetric,
  SystemPerformance,
  PerformanceAlert,
  OptimizationRecommendation,
  LoadTestResult,
  CacheStrategy,
  GetPerformanceMetricsRequest,
  CreateOptimizationRequest,
  UpdateOptimizationRequest,
  RunLoadTestRequest,
  PerformanceCategory,
  OptimizationType,
  CacheType,
  AlertSeverity
} from '@/types/performance.types'

// Performance Metrics Hooks
export function usePerformanceMetrics(request: GetPerformanceMetricsRequest) {
  return useQuery({
    queryKey: ['performance', 'metrics', request],
    queryFn: () => performanceService.getPerformanceMetrics(request),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute
    refetchOnWindowFocus: true
  })
}

export function useSystemPerformance() {
  return useQuery({
    queryKey: ['performance', 'system'],
    queryFn: performanceService.getSystemPerformance,
    staleTime: 15 * 1000, // 15 seconds
    refetchInterval: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: true
  })
}

export function useRealtimeMetrics() {
  return useQuery({
    queryKey: ['performance', 'realtime'],
    queryFn: performanceService.getRealtimeMetrics,
    staleTime: 5 * 1000, // 5 seconds
    refetchInterval: 10 * 1000, // 10 seconds
    refetchOnWindowFocus: true
  })
}

export function useRecordCustomMetric() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.recordCustomMetric,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'metrics'] })
      queryClient.invalidateQueries({ queryKey: ['performance', 'realtime'] })
    }
  })
}

// Performance Alerts Hooks
export function usePerformanceAlerts(severity?: AlertSeverity) {
  return useQuery({
    queryKey: ['performance', 'alerts', severity],
    queryFn: () => performanceService.getPerformanceAlerts(severity),
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000,
    refetchOnWindowFocus: true
  })
}

export function useAcknowledgeAlert() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.acknowledgeAlert,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'alerts'] })
    }
  })
}

export function useResolveAlert() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ alertId, resolution }: { alertId: string; resolution?: string }) =>
      performanceService.resolveAlert(alertId, resolution),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'alerts'] })
    }
  })
}

export function useSuppressAlert() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ alertId, duration }: { alertId: string; duration: number }) =>
      performanceService.suppressAlert(alertId, duration),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'alerts'] })
    }
  })
}

// Optimization Hooks
export function useOptimizationRecommendations(type?: OptimizationType) {
  return useQuery({
    queryKey: ['performance', 'optimizations', type],
    queryFn: () => performanceService.getOptimizationRecommendations(type),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  })
}

export function useCreateOptimizationRecommendation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.createOptimizationRecommendation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'optimizations'] })
    }
  })
}

export function useUpdateOptimizationRecommendation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.updateOptimizationRecommendation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'optimizations'] })
    }
  })
}

export function useImplementOptimization() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.implementOptimization,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'optimizations'] })
      queryClient.invalidateQueries({ queryKey: ['performance', 'metrics'] })
    }
  })
}

export function useOptimizationReport() {
  return useQuery({
    queryKey: ['performance', 'optimization-report'],
    queryFn: performanceService.generateOptimizationReport,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false
  })
}

// Cache Hooks
export function useCacheStrategies() {
  return useQuery({
    queryKey: ['performance', 'cache', 'strategies'],
    queryFn: performanceService.getCacheStrategies,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useCreateCacheStrategy() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.createCacheStrategy,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'cache', 'strategies'] })
    }
  })
}

export function useUpdateCacheStrategy() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ strategyId, updates }: { strategyId: string; updates: Partial<CacheStrategy> }) =>
      performanceService.updateCacheStrategy(strategyId, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'cache', 'strategies'] })
    }
  })
}

export function useCacheMetrics(type?: CacheType) {
  return useQuery({
    queryKey: ['performance', 'cache', 'metrics', type],
    queryFn: () => performanceService.getCacheMetrics(type),
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000,
    refetchOnWindowFocus: true
  })
}

export function useClearCache() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ type, pattern }: { type?: CacheType; pattern?: string }) =>
      performanceService.clearCache(type, pattern),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'cache', 'metrics'] })
    }
  })
}

export function useWarmupCache() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.warmupCache,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'cache', 'metrics'] })
    }
  })
}

// Load Testing Hooks
export function useLoadTests() {
  return useQuery({
    queryKey: ['performance', 'load-tests'],
    queryFn: performanceService.getLoadTests,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false
  })
}

export function useRunLoadTest() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.runLoadTest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'load-tests'] })
    }
  })
}

export function useLoadTestResult(testId: string) {
  return useQuery({
    queryKey: ['performance', 'load-test', testId],
    queryFn: () => performanceService.getLoadTestResult(testId),
    enabled: !!testId,
    staleTime: 30 * 1000,
    refetchInterval: (data) => {
      // Refetch more frequently if test is running
      return data?.status === 'running' ? 5 * 1000 : 60 * 1000
    },
    refetchOnWindowFocus: true
  })
}

export function useStopLoadTest() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.stopLoadTest,
    onSuccess: (_, testId) => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'load-test', testId] })
      queryClient.invalidateQueries({ queryKey: ['performance', 'load-tests'] })
    }
  })
}

export function useScheduleLoadTest() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ testId, scheduleTime }: { testId: string; scheduleTime: string }) =>
      performanceService.scheduleLoadTest(testId, scheduleTime),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'load-tests'] })
    }
  })
}

// Database Optimization Hooks
export function useDatabaseMetrics() {
  return useQuery({
    queryKey: ['performance', 'database', 'metrics'],
    queryFn: performanceService.getDatabaseMetrics,
    staleTime: 60 * 1000,
    refetchInterval: 2 * 60 * 1000,
    refetchOnWindowFocus: true
  })
}

export function useSlowQueries(limit = 10) {
  return useQuery({
    queryKey: ['performance', 'database', 'slow-queries', limit],
    queryFn: () => performanceService.getSlowQueries(limit),
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useOptimizeQuery() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.optimizeQuery,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'database'] })
    }
  })
}

export function useIndexAnalysis() {
  return useQuery({
    queryKey: ['performance', 'database', 'index-analysis'],
    queryFn: performanceService.analyzeIndexUsage,
    staleTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useCreateIndex() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ table, columns, options }: { table: string; columns: string[]; options?: any }) =>
      performanceService.createIndex(table, columns, options),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'database'] })
    }
  })
}

// Frontend Performance Hooks
export function useFrontendMetrics() {
  return useQuery({
    queryKey: ['performance', 'frontend', 'metrics'],
    queryFn: performanceService.getFrontendMetrics,
    staleTime: 60 * 1000,
    refetchInterval: 2 * 60 * 1000,
    refetchOnWindowFocus: true
  })
}

export function useWebVitals() {
  return useQuery({
    queryKey: ['performance', 'frontend', 'web-vitals'],
    queryFn: performanceService.getWebVitals,
    staleTime: 60 * 1000,
    refetchInterval: 2 * 60 * 1000,
    refetchOnWindowFocus: true
  })
}

export function useRecordWebVital() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ metric, value, metadata }: { metric: string; value: number; metadata?: any }) =>
      performanceService.recordWebVital(metric, value, metadata),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'frontend', 'web-vitals'] })
    }
  })
}

export function useBundleAnalysis() {
  return useQuery({
    queryKey: ['performance', 'frontend', 'bundle-analysis'],
    queryFn: performanceService.getBundleAnalysis,
    staleTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false
  })
}

export function useOptimizeAssets() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.optimizeAssets,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'frontend'] })
    }
  })
}

// Network Optimization Hooks
export function useNetworkMetrics() {
  return useQuery({
    queryKey: ['performance', 'network', 'metrics'],
    queryFn: performanceService.getNetworkMetrics,
    staleTime: 60 * 1000,
    refetchInterval: 2 * 60 * 1000,
    refetchOnWindowFocus: true
  })
}

export function useOptimizeNetworkRequests() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.optimizeNetworkRequests,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'network'] })
    }
  })
}

export function useEnableCompression() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.enableCompression,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'network'] })
    }
  })
}

export function useConfigureCDN() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: performanceService.configureCDN,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['performance', 'network'] })
    }
  })
}

// Dashboard Hook
export function usePerformanceDashboard() {
  return useQuery({
    queryKey: ['performance', 'dashboard'],
    queryFn: performanceService.getPerformanceDashboard,
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000,
    refetchOnWindowFocus: true
  })
}
