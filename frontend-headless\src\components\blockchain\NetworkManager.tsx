'use client'

import React, { useState } from 'react'
import {
  GlobeAltIcon,
  PlusIcon,
  Cog6ToothIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  BoltIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import {
  useUpdateNetworkConfig,
  useGasPrice,
  useGasHistory
} from '@/hooks/useBlockchainIntegration'
import { NetworkConfig, BlockchainNetwork } from '@/types/blockchain-integration.types'

interface NetworkManagerProps {
  activeNetworks?: NetworkConfig[]
  networkStatus?: Record<string, any>
  isLoading: boolean
  className?: string
}

export default function NetworkManager({
  activeNetworks = [],
  networkStatus = {},
  isLoading,
  className = ''
}: NetworkManagerProps) {
  const [selectedNetwork, setSelectedNetwork] = useState<NetworkConfig | null>(null)
  const [showAddNetwork, setShowAddNetwork] = useState(false)

  const updateNetworkMutation = useUpdateNetworkConfig()

  const getNetworkStatusIcon = (network: string) => {
    const status = networkStatus[network]
    if (!status) return <XCircleIcon className="h-5 w-5 text-gray-400" />
    
    if (status.isOnline) {
      return status.congestion === 'high' 
        ? <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
        : <CheckCircleIcon className="h-5 w-5 text-green-500" />
    }
    return <XCircleIcon className="h-5 w-5 text-red-500" />
  }

  const getNetworkStatusText = (network: string) => {
    const status = networkStatus[network]
    if (!status) return 'Unknown'
    
    if (!status.isOnline) return 'Offline'
    return `Online (${status.congestion} congestion)`
  }

  const getCongestionColor = (congestion: string) => {
    switch (congestion) {
      case 'low': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'high': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const handleToggleNetwork = async (network: NetworkConfig) => {
    try {
      await updateNetworkMutation.mutateAsync({
        networkId: network.id,
        config: { isActive: !network.isActive }
      })
    } catch (error) {
      console.error('Failed to toggle network:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Network Management</h2>
          <p className="text-sm text-gray-600">Manage blockchain networks and monitor their status</p>
        </div>
        
        <button
          onClick={() => setShowAddNetwork(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Network
        </button>
      </div>

      {/* Network Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {activeNetworks.map((network) => (
          <NetworkCard
            key={network.id}
            network={network}
            status={networkStatus[network.network]}
            onToggle={() => handleToggleNetwork(network)}
            onConfigure={() => setSelectedNetwork(network)}
          />
        ))}
      </div>

      {/* Network Status Overview */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Network Status Overview</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Object.values(networkStatus).filter((s: any) => s?.isOnline).length}
            </div>
            <div className="text-sm text-gray-600">Networks Online</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {Object.values(networkStatus).filter((s: any) => !s?.isOnline).length}
            </div>
            <div className="text-sm text-gray-600">Networks Offline</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {Object.values(networkStatus).filter((s: any) => s?.congestion === 'high').length}
            </div>
            <div className="text-sm text-gray-600">High Congestion</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {activeNetworks.filter(n => n.isActive).length}
            </div>
            <div className="text-sm text-gray-600">Active Networks</div>
          </div>
        </div>
      </div>

      {/* Network Configuration Modal */}
      {selectedNetwork && (
        <NetworkConfigModal
          network={selectedNetwork}
          onClose={() => setSelectedNetwork(null)}
          onUpdate={(config) => {
            updateNetworkMutation.mutate({
              networkId: selectedNetwork.id,
              config
            })
            setSelectedNetwork(null)
          }}
        />
      )}

      {/* Add Network Modal */}
      {showAddNetwork && (
        <AddNetworkModal
          onClose={() => setShowAddNetwork(false)}
          onAdd={(networkConfig) => {
            // Handle adding new network
            setShowAddNetwork(false)
          }}
        />
      )}
    </div>
  )
}

interface NetworkCardProps {
  network: NetworkConfig
  status?: any
  onToggle: () => void
  onConfigure: () => void
}

function NetworkCard({ network, status, onToggle, onConfigure }: NetworkCardProps) {
  const { data: gasPrice } = useGasPrice(network.network)

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <img
            src={network.iconUrl}
            alt={network.name}
            className="w-8 h-8 rounded-full"
            onError={(e) => {
              e.currentTarget.src = '/placeholder-network.png'
            }}
          />
          <div>
            <h3 className="text-lg font-medium text-gray-900">{network.name}</h3>
            <p className="text-sm text-gray-600">Chain ID: {network.chainId}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {status && (
            <div className="flex items-center space-x-1">
              {status.isOnline ? (
                <CheckCircleIcon className="h-4 w-4 text-green-500" />
              ) : (
                <XCircleIcon className="h-4 w-4 text-red-500" />
              )}
              <span className="text-xs text-gray-600">
                {status.isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
          )}
          
          <button
            onClick={onToggle}
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
              network.isActive ? 'bg-blue-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                network.isActive ? 'translate-x-5' : 'translate-x-0'
              }`}
            />
          </button>
        </div>
      </div>

      {/* Network Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        {status && (
          <>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm font-medium text-gray-900">Block Height</div>
              <div className="text-sm text-gray-600">{status.blockHeight?.toLocaleString()}</div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm font-medium text-gray-900">Block Time</div>
              <div className="text-sm text-gray-600">{status.avgBlockTime}s</div>
            </div>
          </>
        )}
        
        {gasPrice && (
          <>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm font-medium text-gray-900">Gas Price</div>
              <div className="text-sm text-gray-600">{gasPrice.standard} gwei</div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm font-medium text-gray-900">Congestion</div>
              <div className={`text-sm font-medium capitalize ${
                status?.congestion === 'low' ? 'text-green-600' :
                status?.congestion === 'medium' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {status?.congestion || 'Unknown'}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Network Features */}
      <div className="mb-4">
        <div className="text-sm font-medium text-gray-900 mb-2">Features</div>
        <div className="flex flex-wrap gap-1">
          {network.supportedFeatures.slice(0, 3).map((feature) => (
            <span
              key={feature}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {feature}
            </span>
          ))}
          {network.supportedFeatures.length > 3 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              +{network.supportedFeatures.length - 3} more
            </span>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="text-xs text-gray-500">
          {network.isTestnet ? 'Testnet' : 'Mainnet'}
        </div>
        
        <button
          onClick={onConfigure}
          className="text-sm text-blue-600 hover:text-blue-700"
        >
          Configure
        </button>
      </div>
    </div>
  )
}

interface NetworkConfigModalProps {
  network: NetworkConfig
  onClose: () => void
  onUpdate: (config: Partial<NetworkConfig>) => void
}

function NetworkConfigModal({ network, onClose, onUpdate }: NetworkConfigModalProps) {
  const [config, setConfig] = useState({
    priority: network.priority,
    gasPriceMultiplier: network.gasPriceMultiplier,
    maxGasPrice: network.maxGasPrice,
    isActive: network.isActive
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onUpdate(config)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Configure {network.name}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Priority</label>
            <input
              type="number"
              min="1"
              max="10"
              value={config.priority}
              onChange={(e) => setConfig(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Gas Price Multiplier</label>
            <input
              type="number"
              min="0.1"
              max="5"
              step="0.1"
              value={config.gasPriceMultiplier}
              onChange={(e) => setConfig(prev => ({ ...prev, gasPriceMultiplier: parseFloat(e.target.value) }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Max Gas Price (gwei)</label>
            <input
              type="text"
              value={config.maxGasPrice}
              onChange={(e) => setConfig(prev => ({ ...prev, maxGasPrice: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              checked={config.isActive}
              onChange={(e) => setConfig(prev => ({ ...prev, isActive: e.target.checked }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
              Network Active
            </label>
          </div>

          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Update
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

interface AddNetworkModalProps {
  onClose: () => void
  onAdd: (network: Partial<NetworkConfig>) => void
}

function AddNetworkModal({ onClose, onAdd }: AddNetworkModalProps) {
  const [networkData, setNetworkData] = useState({
    name: '',
    chainId: '',
    rpcUrl: '',
    blockExplorerUrl: '',
    nativeCurrency: {
      name: '',
      symbol: '',
      decimals: 18
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onAdd({
      name: networkData.name,
      chainId: parseInt(networkData.chainId),
      rpcUrls: [networkData.rpcUrl],
      blockExplorerUrls: [networkData.blockExplorerUrl],
      nativeCurrency: networkData.nativeCurrency,
      isActive: true,
      priority: 5
    } as Partial<NetworkConfig>)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-96 overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Add Custom Network</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Network Name</label>
            <input
              type="text"
              required
              value={networkData.name}
              onChange={(e) => setNetworkData(prev => ({ ...prev, name: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Chain ID</label>
            <input
              type="number"
              required
              value={networkData.chainId}
              onChange={(e) => setNetworkData(prev => ({ ...prev, chainId: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">RPC URL</label>
            <input
              type="url"
              required
              value={networkData.rpcUrl}
              onChange={(e) => setNetworkData(prev => ({ ...prev, rpcUrl: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Block Explorer URL</label>
            <input
              type="url"
              value={networkData.blockExplorerUrl}
              onChange={(e) => setNetworkData(prev => ({ ...prev, blockExplorerUrl: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Currency Name</label>
              <input
                type="text"
                required
                value={networkData.nativeCurrency.name}
                onChange={(e) => setNetworkData(prev => ({
                  ...prev,
                  nativeCurrency: { ...prev.nativeCurrency, name: e.target.value }
                }))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Currency Symbol</label>
              <input
                type="text"
                required
                value={networkData.nativeCurrency.symbol}
                onChange={(e) => setNetworkData(prev => ({
                  ...prev,
                  nativeCurrency: { ...prev.nativeCurrency, symbol: e.target.value }
                }))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Add Network
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
