'use client'

import React, { useState } from 'react'
import {
  ClipboardDocumentListIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  PhotoIcon,
  VideoCameraIcon,
  LinkIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import { useCampaignSubmissions, useReviewSubmission } from '@/hooks/useCampaigns'
import { SubmissionStatus } from '@/types/campaign.types'

interface CampaignSubmissionsProps {
  campaignId: string
  className?: string
}

export default function CampaignSubmissions({ campaignId, className = '' }: CampaignSubmissionsProps) {
  const [filters, setFilters] = useState({
    status: undefined as SubmissionStatus | undefined,
    requirementId: undefined as string | undefined,
    page: 1,
    limit: 20
  })

  const { data: submissionsData, isLoading } = useCampaignSubmissions(campaignId, filters)
  const reviewSubmissionMutation = useReviewSubmission()

  const submissions = submissionsData?.submissions || []
  const total = submissionsData?.total || 0

  const handleReview = async (submissionId: string, status: SubmissionStatus, notes?: string, points?: number) => {
    try {
      await reviewSubmissionMutation.mutateAsync({
        submissionId,
        data: { status, notes, points }
      })
    } catch (error) {
      console.error('Failed to review submission:', error)
    }
  }

  const getStatusColor = (status: SubmissionStatus) => {
    switch (status) {
      case SubmissionStatus.APPROVED: return 'text-green-600 bg-green-100'
      case SubmissionStatus.REJECTED: return 'text-red-600 bg-red-100'
      case SubmissionStatus.UNDER_REVIEW: return 'text-blue-600 bg-blue-100'
      case SubmissionStatus.PENDING: return 'text-yellow-600 bg-yellow-100'
      case SubmissionStatus.REQUIRES_REVISION: return 'text-orange-600 bg-orange-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: SubmissionStatus) => {
    switch (status) {
      case SubmissionStatus.APPROVED: return <CheckIcon className="h-4 w-4" />
      case SubmissionStatus.REJECTED: return <XMarkIcon className="h-4 w-4" />
      case SubmissionStatus.UNDER_REVIEW: return <ClockIcon className="h-4 w-4" />
      case SubmissionStatus.PENDING: return <ClockIcon className="h-4 w-4" />
      case SubmissionStatus.REQUIRES_REVISION: return <ExclamationTriangleIcon className="h-4 w-4" />
      default: return <ClipboardDocumentListIcon className="h-4 w-4" />
    }
  }

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'image': return <PhotoIcon className="h-4 w-4" />
      case 'video': return <VideoCameraIcon className="h-4 w-4" />
      case 'link': return <LinkIcon className="h-4 w-4" />
      case 'text': return <DocumentTextIcon className="h-4 w-4" />
      default: return <DocumentTextIcon className="h-4 w-4" />
    }
  }

  const renderSubmissionContent = (submission: any) => {
    const { content } = submission

    switch (content.type) {
      case 'image':
        return (
          <div className="mt-2">
            <img
              src={content.value}
              alt="Submission"
              className="w-32 h-32 object-cover rounded-lg"
            />
          </div>
        )
      case 'video':
        return (
          <div className="mt-2">
            <video
              src={content.value}
              className="w-32 h-24 object-cover rounded-lg"
              controls
            />
          </div>
        )
      case 'link':
        return (
          <div className="mt-2">
            <a
              href={content.value}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 text-sm break-all"
            >
              {content.value}
            </a>
          </div>
        )
      case 'text':
      default:
        return (
          <div className="mt-2">
            <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
              {content.value}
            </p>
          </div>
        )
    }
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Campaign Submissions</h3>
          <p className="text-sm text-gray-600">{total} total submissions</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <select
          value={filters.status || ''}
          onChange={(e) => setFilters(prev => ({ 
            ...prev, 
            status: e.target.value ? e.target.value as SubmissionStatus : undefined 
          }))}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">All Statuses</option>
          <option value={SubmissionStatus.PENDING}>Pending</option>
          <option value={SubmissionStatus.UNDER_REVIEW}>Under Review</option>
          <option value={SubmissionStatus.APPROVED}>Approved</option>
          <option value={SubmissionStatus.REJECTED}>Rejected</option>
          <option value={SubmissionStatus.REQUIRES_REVISION}>Requires Revision</option>
        </select>

        <select
          value={filters.requirementId || ''}
          onChange={(e) => setFilters(prev => ({ 
            ...prev, 
            requirementId: e.target.value || undefined 
          }))}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">All Requirements</option>
          {/* This would be populated with actual requirements */}
          <option value="req1">Twitter Follow</option>
          <option value="req2">Content Creation</option>
          <option value="req3">Discord Join</option>
        </select>
      </div>

      {/* Submissions List */}
      {submissions.length > 0 ? (
        <div className="space-y-4">
          {submissions.map((submission) => (
            <div
              key={submission.id}
              className="border border-gray-200 rounded-lg p-6 hover:border-gray-300 transition-colors"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {getContentIcon(submission.content.type)}
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      Requirement Submission
                    </h4>
                    <p className="text-sm text-gray-600">
                      Submitted {new Date(submission.submittedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(submission.status)}`}>
                    {getStatusIcon(submission.status)}
                    <span className="ml-1">{submission.status.replace('_', ' ')}</span>
                  </span>

                  <div className="text-sm text-gray-600">
                    {submission.points} points
                  </div>
                </div>
              </div>

              {/* Submission Content */}
              <div className="mb-4">
                {renderSubmissionContent(submission)}
              </div>

              {/* Review Notes */}
              {submission.reviewNotes && (
                <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h5 className="text-sm font-medium text-yellow-800 mb-1">Review Notes</h5>
                  <p className="text-sm text-yellow-700">{submission.reviewNotes}</p>
                  {submission.reviewedAt && (
                    <p className="text-xs text-yellow-600 mt-1">
                      Reviewed on {new Date(submission.reviewedAt).toLocaleDateString()}
                    </p>
                  )}
                </div>
              )}

              {/* Review Actions */}
              {submission.status === SubmissionStatus.PENDING && (
                <div className="flex items-center space-x-3 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => handleReview(submission.id, SubmissionStatus.APPROVED, undefined, submission.points)}
                    disabled={reviewSubmissionMutation.isPending}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                  >
                    <CheckIcon className="h-4 w-4 mr-1" />
                    Approve
                  </button>

                  <button
                    onClick={() => handleReview(submission.id, SubmissionStatus.REJECTED, 'Does not meet requirements')}
                    disabled={reviewSubmissionMutation.isPending}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
                  >
                    <XMarkIcon className="h-4 w-4 mr-1" />
                    Reject
                  </button>

                  <button
                    onClick={() => handleReview(submission.id, SubmissionStatus.REQUIRES_REVISION, 'Please revise and resubmit')}
                    disabled={reviewSubmissionMutation.isPending}
                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                    Request Revision
                  </button>

                  <button
                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <EyeIcon className="h-4 w-4 mr-1" />
                    View Details
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No submissions found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {filters.status || filters.requirementId
              ? 'Try adjusting your filters to see more submissions.'
              : 'No submissions have been made for this campaign yet.'}
          </p>
        </div>
      )}

      {/* Pagination */}
      {total > filters.limit && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((filters.page - 1) * filters.limit) + 1} to {Math.min(filters.page * filters.limit, total)} of {total} submissions
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setFilters(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
              disabled={filters.page === 1}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Previous
            </button>
            <span className="px-3 py-2 text-sm text-gray-700">
              Page {filters.page} of {Math.ceil(total / filters.limit)}
            </span>
            <button
              onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={filters.page >= Math.ceil(total / filters.limit)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
