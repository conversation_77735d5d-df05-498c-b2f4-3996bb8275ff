'use client'

import React, { useState } from 'react'
import {
  ArrowLeftIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  PencilIcon,
  ChartBarIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  GiftIcon,
  SparklesIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'
import { useCampaign, useCampaignAnalytics, useCampaignParticipants } from '@/hooks/useCampaigns'
import { Campaign, CampaignStatus } from '@/types/campaign.types'
import CampaignParticipants from './CampaignParticipants'
import CampaignAnalytics from './CampaignAnalytics'
import CampaignSubmissions from './CampaignSubmissions'

interface CampaignDetailViewProps {
  campaignId: string
  onBack?: () => void
  onEdit?: (campaign: Campaign) => void
  onStatusChange?: (campaign: Campaign, status: CampaignStatus) => void
  className?: string
}

export default function CampaignDetailView({
  campaignId,
  onBack,
  onEdit,
  onStatusChange,
  className = ''
}: CampaignDetailViewProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'participants' | 'analytics' | 'submissions'>('overview')

  const { data: campaign, isLoading } = useCampaign(campaignId)
  const { data: analytics } = useCampaignAnalytics(campaignId)
  const { data: participantsData } = useCampaignParticipants(campaignId)

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!campaign) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 text-center ${className}`}>
        <h3 className="text-lg font-medium text-gray-900">Campaign not found</h3>
        <p className="text-gray-600 mt-2">The campaign you're looking for doesn't exist or has been removed.</p>
        {onBack && (
          <button
            onClick={onBack}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Go Back
          </button>
        )}
      </div>
    )
  }

  const getStatusColor = (status: CampaignStatus) => {
    switch (status) {
      case CampaignStatus.ACTIVE: return 'text-green-600 bg-green-100 border-green-200'
      case CampaignStatus.PAUSED: return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      case CampaignStatus.COMPLETED: return 'text-blue-600 bg-blue-100 border-blue-200'
      case CampaignStatus.CANCELLED: return 'text-red-600 bg-red-100 border-red-200'
      case CampaignStatus.DRAFT: return 'text-gray-600 bg-gray-100 border-gray-200'
      case CampaignStatus.SCHEDULED: return 'text-purple-600 bg-purple-100 border-purple-200'
      default: return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const calculateProgress = () => {
    const now = new Date()
    const start = new Date(campaign.startDate)
    const end = new Date(campaign.endDate)
    
    if (now < start) return 0
    if (now > end) return 100
    
    const total = end.getTime() - start.getTime()
    const elapsed = now.getTime() - start.getTime()
    return Math.round((elapsed / total) * 100)
  }

  const getDaysRemaining = () => {
    const now = new Date()
    const end = new Date(campaign.endDate)
    const diffTime = end.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 0) return 'Ended'
    if (diffDays === 0) return 'Ends today'
    if (diffDays === 1) return '1 day left'
    return `${diffDays} days left`
  }

  const progress = calculateProgress()
  const daysRemaining = getDaysRemaining()

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ClipboardDocumentListIcon },
    { id: 'participants', name: 'Participants', icon: UsersIcon, count: campaign.participantCount },
    { id: 'analytics', name: 'Analytics', icon: ChartBarIcon },
    { id: 'submissions', name: 'Submissions', icon: GiftIcon }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {onBack && (
              <button
                onClick={onBack}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
            )}
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">{campaign.name}</h1>
                {campaign.featured && (
                  <SparklesIcon className="h-6 w-6 text-yellow-500" title="Featured Campaign" />
                )}
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(campaign.status)}`}>
                  {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                </span>
              </div>
              <p className="text-gray-600">{campaign.description}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {onEdit && (
              <button
                onClick={() => onEdit(campaign)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                Edit
              </button>
            )}

            {campaign.status === CampaignStatus.DRAFT && onStatusChange && (
              <button
                onClick={() => onStatusChange(campaign, CampaignStatus.ACTIVE)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                <PlayIcon className="h-4 w-4 mr-2" />
                Start Campaign
              </button>
            )}

            {campaign.status === CampaignStatus.ACTIVE && onStatusChange && (
              <button
                onClick={() => onStatusChange(campaign, CampaignStatus.PAUSED)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
              >
                <PauseIcon className="h-4 w-4 mr-2" />
                Pause Campaign
              </button>
            )}

            {(campaign.status === CampaignStatus.ACTIVE || campaign.status === CampaignStatus.PAUSED) && onStatusChange && (
              <button
                onClick={() => onStatusChange(campaign, CampaignStatus.COMPLETED)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <StopIcon className="h-4 w-4 mr-2" />
                Complete
              </button>
            )}
          </div>
        </div>

        {/* Campaign Banner */}
        {campaign.bannerImage && (
          <div className="mb-6">
            <img
              src={campaign.bannerImage}
              alt={campaign.name}
              className="w-full h-48 object-cover rounded-lg"
            />
          </div>
        )}

        {/* Progress Bar */}
        {campaign.status === CampaignStatus.ACTIVE && (
          <div className="mb-6">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Campaign Progress</span>
              <span>{progress}% • {daysRemaining}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{campaign.participantCount.toLocaleString()}</div>
            <div className="text-sm text-gray-600">Participants</div>
            {campaign.maxParticipants && (
              <div className="text-xs text-gray-500">of {campaign.maxParticipants.toLocaleString()}</div>
            )}
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{Math.round((campaign.completionRate || 0) * 100)}%</div>
            <div className="text-sm text-gray-600">Completion Rate</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{campaign.requirements?.length || 0}</div>
            <div className="text-sm text-gray-600">Requirements</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{campaign.rewards?.length || 0}</div>
            <div className="text-sm text-gray-600">Reward Types</div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Campaign Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Information</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center space-x-2">
                      <CalendarIcon className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600">Start Date:</span>
                      <span className="font-medium">{new Date(campaign.startDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <ClockIcon className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600">End Date:</span>
                      <span className="font-medium">{new Date(campaign.endDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <TagIcon className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600">Type:</span>
                      <span className="font-medium">{campaign.type.replace('_', ' ')}</span>
                    </div>
                    {campaign.geoRestrictions && campaign.geoRestrictions.length > 0 && (
                      <div className="flex items-start space-x-2">
                        <GlobeAltIcon className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-gray-600">Regions:</span>
                        <span className="font-medium">{campaign.geoRestrictions.join(', ')}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                  {campaign.tags && campaign.tags.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {campaign.tags.map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-700"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No tags assigned</p>
                  )}
                </div>
              </div>

              {/* Requirements */}
              {campaign.requirements && campaign.requirements.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Requirements</h3>
                  <div className="space-y-3">
                    {campaign.requirements.map((req, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <span className="font-medium text-gray-900">{req.title}</span>
                          <p className="text-sm text-gray-600">{req.description}</p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className={`w-2 h-2 rounded-full ${req.isRequired ? 'bg-red-500' : 'bg-yellow-500'}`}></span>
                          <span className="text-sm font-medium text-purple-600">{req.points} pts</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Rewards */}
              {campaign.rewards && campaign.rewards.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Rewards</h3>
                  <div className="space-y-3">
                    {campaign.rewards.map((reward, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <span className="font-medium text-gray-900">{reward.title}</span>
                          <p className="text-sm text-gray-600">{reward.description}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">{reward.quantity} recipients</div>
                          <div className="text-xs text-gray-600">{reward.value} each</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'participants' && (
            <CampaignParticipants campaignId={campaignId} />
          )}

          {activeTab === 'analytics' && (
            <CampaignAnalytics campaignId={campaignId} />
          )}

          {activeTab === 'submissions' && (
            <CampaignSubmissions campaignId={campaignId} />
          )}
        </div>
      </div>
    </div>
  )
}
