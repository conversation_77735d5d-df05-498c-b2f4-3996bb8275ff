# User Service V2 Configuration
# Database Per Service Pattern - This service has its OWN database

# Service Configuration
SERVICE_NAME=user-service-v2
SERVICE_PORT=4001
NODE_ENV=development
LOG_LEVEL=info

# Database Configuration (User Service Database ONLY)
DATABASE_URL=postgresql://postgres:password@localhost:5432/user_service_v2

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=user-service-v2
JWT_AUDIENCE=social-nft-platform

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:4010
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100
ENABLE_HELMET=true
ENABLE_CSRF=false

# Monitoring Configuration
ENABLE_METRICS=true
ENABLE_TRACING=true
ENABLE_HEALTH_CHECKS=true
METRICS_PORT=9091

# Logging Configuration
LOG_DIRECTORY=logs
ENABLE_CONSOLE_LOGGING=true
ENABLE_FILE_LOGGING=false
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false

# Database Configuration Details
DB_SSL=false
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=30000

# API Gateway Configuration
API_GATEWAY_URL=http://localhost:4010
API_GATEWAY_TIMEOUT=30000
API_GATEWAY_RETRIES=3

# Redis Configuration (if needed for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# External Services (if needed)
# EMAIL_API_KEY=your-email-api-key
# EMAIL_FROM_ADDRESS=<EMAIL>
