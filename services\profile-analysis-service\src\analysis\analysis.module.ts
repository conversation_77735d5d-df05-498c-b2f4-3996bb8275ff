import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { PrismaModule } from '../prisma/prisma.module';

// Controllers
import { ProfileAnalysisQueryController } from './controllers/profile-analysis-query.controller';
import { ProfileAnalysisCommandController } from './controllers/profile-analysis-command.controller';
import { TwitterAuthController } from './controllers/twitter-auth.controller';

// Services
import { TwitterAnalysisService } from './services/twitter-analysis.service';
import { TwitterApiClientService } from './services/twitter-api-client.service';
import { ProfileAnalysisService } from './services/profile-analysis.service';

@Module({
  imports: [
    PrismaModule,
    HttpModule.register({
      timeout: 10000,
      maxRedirects: 3,
    }),
  ],
  controllers: [
    ProfileAnalysisQueryController,
    ProfileAnalysisCommandController,
    TwitterAuthController,
  ],
  providers: [
    TwitterAnalysisService,
    TwitterApiClientService,
    ProfileAnalysisService,
  ],
  exports: [
    TwitterAnalysisService,
    TwitterApiClientService,
    ProfileAnalysisService,
  ],
})
export class AnalysisModule {}
