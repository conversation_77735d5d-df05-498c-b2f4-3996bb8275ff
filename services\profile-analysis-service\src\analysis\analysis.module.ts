import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { PrismaModule } from '../prisma/prisma.module';

// Controllers
import { ProfileAnalysisQueryController } from './controllers/profile-analysis-query.controller';
import { ProfileAnalysisCommandController } from './controllers/profile-analysis-command.controller';
import { TwitterAuthController } from './controllers/twitter-auth.controller';

// Services
import { TwitterAnalysisService } from './services/twitter-analysis.service';
import { TwitterApiClientService } from './services/twitter-api-client.service';

@Module({
  imports: [
    PrismaModule,
    HttpModule.register({
      timeout: 10000,
      maxRedirects: 3,
    }),
  ],
  controllers: [
    ProfileAnalysisQueryController,
    ProfileAnalysisCommandController,
    TwitterAuthController,
  ],
  providers: [
    TwitterAnalysisService,
    TwitterApiClientService,
  ],
  exports: [
    TwitterAnalysisService,
    TwitterApiClientService,
  ],
})
export class AnalysisModule {}
