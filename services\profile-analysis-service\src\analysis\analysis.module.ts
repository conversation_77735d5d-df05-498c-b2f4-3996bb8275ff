import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { PrismaModule } from '../prisma/prisma.module';

// Controllers
import { ProfileAnalysisQueryController } from './controllers/profile-analysis-query.controller';
import { ProfileAnalysisCommandController } from './controllers/profile-analysis-command.controller';
import { TwitterAuthController } from './controllers/twitter-auth.controller';

// Services
import { TwitterAnalysisService } from './services/twitter-analysis.service';
import { TwitterApiClientService } from './services/twitter-api-client.service';
import { EngagementCalculatorService } from './services/engagement-calculator.service';
import { InfluenceMetricsService } from './services/influence-metrics.service';
import { SentimentAnalyzerService } from './services/sentiment-analyzer.service';
import { HistoricalAnalyzerService } from './services/historical-analyzer.service';

@Module({
  imports: [
    PrismaModule,
    HttpModule.register({
      timeout: 10000,
      maxRedirects: 3,
    }),
  ],
  controllers: [
    ProfileAnalysisQueryController,
    ProfileAnalysisCommandController,
    TwitterAuthController,
  ],
  providers: [
    TwitterAnalysisService,
    TwitterApiClientService,
    EngagementCalculatorService,
    InfluenceMetricsService,
    SentimentAnalyzerService,
    HistoricalAnalyzerService,
  ],
  exports: [
    TwitterAnalysisService,
    TwitterApiClientService,
    EngagementCalculatorService,
    InfluenceMetricsService,
    SentimentAnalyzerService,
    HistoricalAnalyzerService,
  ],
})
export class AnalysisModule {}
