import { Injectable, Logger } from '@nestjs/common';

export interface SentimentAnalysis {
  overallSentiment: 'very_positive' | 'positive' | 'neutral' | 'negative' | 'very_negative';
  sentimentScore: number; // -100 to +100
  emotionalTone: string[];
  contentCategories: string[];
  topicSentiments: { [topic: string]: number };
  sentimentTrend: 'improving' | 'stable' | 'declining';
  positivityRatio: number;
  engagementSentiment: number;
  brandSafety: 'safe' | 'moderate' | 'risky';
  toxicityScore: number;
  professionalismScore: number;
}

export interface TweetData {
  id: string;
  text: string;
  createdAt: string;
  likes?: number;
  retweets?: number;
  replies?: number;
  hashtags?: string[];
  mentions?: string[];
  urls?: string[];
}

@Injectable()
export class SentimentAnalyzerService {
  private readonly logger = new Logger(SentimentAnalyzerService.name);

  // Sentiment lexicons
  private readonly positiveWords = new Set([
    'amazing', 'awesome', 'excellent', 'fantastic', 'great', 'love', 'wonderful',
    'brilliant', 'outstanding', 'perfect', 'incredible', 'superb', 'magnificent',
    'delighted', 'thrilled', 'excited', 'happy', 'joy', 'pleased', 'satisfied',
    'grateful', 'thankful', 'blessed', 'lucky', 'proud', 'confident', 'optimistic',
    'success', 'achievement', 'victory', 'win', 'breakthrough', 'innovation',
    'beautiful', 'stunning', 'gorgeous', 'elegant', 'impressive', 'remarkable'
  ]);

  private readonly negativeWords = new Set([
    'terrible', 'awful', 'horrible', 'disgusting', 'hate', 'worst', 'pathetic',
    'disappointing', 'frustrated', 'angry', 'furious', 'annoyed', 'upset', 'sad',
    'depressed', 'worried', 'concerned', 'anxious', 'stressed', 'overwhelmed',
    'failed', 'failure', 'disaster', 'crisis', 'problem', 'issue', 'trouble',
    'broken', 'damaged', 'wrong', 'bad', 'poor', 'weak', 'useless', 'worthless'
  ]);

  private readonly toxicWords = new Set([
    'stupid', 'idiot', 'moron', 'dumb', 'loser', 'pathetic', 'worthless',
    'disgusting', 'sick', 'twisted', 'crazy', 'insane', 'ridiculous'
  ]);

  private readonly professionalWords = new Set([
    'professional', 'business', 'corporate', 'industry', 'market', 'strategy',
    'development', 'innovation', 'leadership', 'management', 'expertise',
    'experience', 'knowledge', 'skills', 'qualifications', 'achievements'
  ]);

  constructor() {}

  /**
   * Analyze sentiment of profile content
   */
  async analyzeSentiment(tweets: TweetData[], profileBio?: string): Promise<SentimentAnalysis> {
    try {
      // Analyze individual tweets
      const tweetSentiments = tweets.map(tweet => this.analyzeTweetSentiment(tweet));

      // Calculate overall sentiment metrics
      const overallSentimentScore = this.calculateOverallSentiment(tweetSentiments);
      const overallSentiment = this.categorizeSentiment(overallSentimentScore);

      // Analyze emotional tone
      const emotionalTone = this.analyzeEmotionalTone(tweets);

      // Categorize content
      const contentCategories = this.categorizeContent(tweets);

      // Analyze topic-specific sentiments
      const topicSentiments = this.analyzeTopicSentiments(tweets, tweetSentiments);

      // Determine sentiment trend
      const sentimentTrend = this.determineSentimentTrend(tweetSentiments);

      // Calculate positivity ratio
      const positivityRatio = this.calculatePositivityRatio(tweetSentiments);

      // Analyze engagement sentiment correlation
      const engagementSentiment = this.analyzeEngagementSentiment(tweets, tweetSentiments);

      // Assess brand safety
      const brandSafety = this.assessBrandSafety(tweets, profileBio);

      // Calculate toxicity score
      const toxicityScore = this.calculateToxicityScore(tweets);

      // Calculate professionalism score
      const professionalismScore = this.calculateProfessionalismScore(tweets, profileBio);

      const analysis: SentimentAnalysis = {
        overallSentiment,
        sentimentScore: overallSentimentScore,
        emotionalTone,
        contentCategories,
        topicSentiments,
        sentimentTrend,
        positivityRatio,
        engagementSentiment,
        brandSafety,
        toxicityScore,
        professionalismScore,
      };

      return analysis;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Analyze sentiment of individual tweet
   */
  private analyzeTweetSentiment(tweet: TweetData): {
    score: number;
    sentiment: string;
    confidence: number;
    emotions: string[];
  } {
    const text = tweet.text.toLowerCase();
    const words = text.split(/\s+/);
    
    let positiveScore = 0;
    let negativeScore = 0;
    let emotionalWords: string[] = [];

    // Count sentiment words
    for (const word of words) {
      const cleanWord = word.replace(/[^\w]/g, '');
      
      if (this.positiveWords.has(cleanWord)) {
        positiveScore++;
        emotionalWords.push(cleanWord);
      } else if (this.negativeWords.has(cleanWord)) {
        negativeScore++;
        emotionalWords.push(cleanWord);
      }
    }

    // Apply context modifiers
    const modifiers = this.analyzeContextModifiers(text);
    positiveScore *= modifiers.positiveMultiplier;
    negativeScore *= modifiers.negativeMultiplier;

    // Calculate final score (-100 to +100)
    const totalWords = Math.max(1, words.length);
    const score = ((positiveScore - negativeScore) / totalWords) * 100;
    const normalizedScore = Math.max(-100, Math.min(100, score));

    // Determine confidence based on emotional word density
    const emotionalDensity = emotionalWords.length / totalWords;
    const confidence = Math.min(100, emotionalDensity * 200);

    // Extract emotions
    const emotions = this.extractEmotions(text);

    return {
      score: normalizedScore,
      sentiment: this.categorizeSentiment(normalizedScore),
      confidence,
      emotions,
    };
  }

  /**
   * Analyze context modifiers (negations, intensifiers)
   */
  private analyzeContextModifiers(text: string): {
    positiveMultiplier: number;
    negativeMultiplier: number;
  } {
    let positiveMultiplier = 1;
    let negativeMultiplier = 1;

    // Negation words
    const negations = ['not', 'no', 'never', 'none', 'nothing', 'nowhere', 'neither'];
    const hasNegation = negations.some(neg => text.includes(neg));
    
    if (hasNegation) {
      // Flip sentiment when negation is present
      positiveMultiplier = 0.3;
      negativeMultiplier = 1.5;
    }

    // Intensifiers
    const intensifiers = ['very', 'extremely', 'incredibly', 'absolutely', 'totally', 'completely'];
    const hasIntensifier = intensifiers.some(int => text.includes(int));
    
    if (hasIntensifier) {
      positiveMultiplier *= 1.5;
      negativeMultiplier *= 1.5;
    }

    // Question marks might indicate uncertainty
    if (text.includes('?')) {
      positiveMultiplier *= 0.8;
      negativeMultiplier *= 0.8;
    }

    // Exclamation marks indicate strong emotion
    const exclamationCount = (text.match(/!/g) || []).length;
    if (exclamationCount > 0) {
      const multiplier = 1 + (exclamationCount * 0.2);
      positiveMultiplier *= multiplier;
      negativeMultiplier *= multiplier;
    }

    return { positiveMultiplier, negativeMultiplier };
  }

  /**
   * Extract emotions from text
   */
  private extractEmotions(text: string): string[] {
    const emotions: string[] = [];
    
    // Joy/Happiness
    if (/\b(happy|joy|excited|thrilled|delighted|cheerful)\b/.test(text)) {
      emotions.push('joy');
    }
    
    // Anger
    if (/\b(angry|furious|mad|rage|annoyed|frustrated)\b/.test(text)) {
      emotions.push('anger');
    }
    
    // Sadness
    if (/\b(sad|depressed|disappointed|upset|heartbroken)\b/.test(text)) {
      emotions.push('sadness');
    }
    
    // Fear/Anxiety
    if (/\b(scared|afraid|worried|anxious|nervous|concerned)\b/.test(text)) {
      emotions.push('fear');
    }
    
    // Surprise
    if (/\b(surprised|shocked|amazed|astonished|stunned)\b/.test(text)) {
      emotions.push('surprise');
    }
    
    // Disgust
    if (/\b(disgusted|revolted|sick|nauseated|repulsed)\b/.test(text)) {
      emotions.push('disgust');
    }

    return emotions;
  }

  /**
   * Calculate overall sentiment from individual tweet sentiments
   */
  private calculateOverallSentiment(tweetSentiments: any[]): number {
    if (tweetSentiments.length === 0) return 0;

    // Weight recent tweets more heavily
    let weightedSum = 0;
    let totalWeight = 0;

    tweetSentiments.forEach((sentiment, index) => {
      // More recent tweets (lower index) get higher weight
      const weight = Math.exp(-index * 0.1);
      weightedSum += sentiment.score * weight * (sentiment.confidence / 100);
      totalWeight += weight;
    });

    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }

  /**
   * Categorize sentiment score
   */
  private categorizeSentiment(score: number): 'very_positive' | 'positive' | 'neutral' | 'negative' | 'very_negative' {
    if (score > 50) return 'very_positive';
    if (score > 10) return 'positive';
    if (score > -10) return 'neutral';
    if (score > -50) return 'negative';
    return 'very_negative';
  }

  /**
   * Analyze emotional tone across all tweets
   */
  private analyzeEmotionalTone(tweets: TweetData[]): string[] {
    const emotionCounts = new Map<string, number>();
    
    tweets.forEach(tweet => {
      const emotions = this.extractEmotions(tweet.text.toLowerCase());
      emotions.forEach(emotion => {
        emotionCounts.set(emotion, (emotionCounts.get(emotion) || 0) + 1);
      });
    });

    // Return top 3 emotions
    return Array.from(emotionCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([emotion]) => emotion);
  }

  /**
   * Categorize content based on topics and themes
   */
  private categorizeContent(tweets: TweetData[]): string[] {
    const categories = new Set<string>();
    
    tweets.forEach(tweet => {
      const text = tweet.text.toLowerCase();
      
      // Technology
      if (/\b(tech|technology|ai|software|code|programming|developer)\b/.test(text)) {
        categories.add('Technology');
      }
      
      // Business
      if (/\b(business|startup|entrepreneur|marketing|sales|revenue)\b/.test(text)) {
        categories.add('Business');
      }
      
      // Personal
      if (/\b(personal|life|family|friends|weekend|vacation)\b/.test(text)) {
        categories.add('Personal');
      }
      
      // News/Politics
      if (/\b(news|politics|government|election|policy|breaking)\b/.test(text)) {
        categories.add('News');
      }
      
      // Entertainment
      if (/\b(movie|music|game|entertainment|fun|party|concert)\b/.test(text)) {
        categories.add('Entertainment');
      }
      
      // Sports
      if (/\b(sports|football|basketball|soccer|baseball|game|team)\b/.test(text)) {
        categories.add('Sports');
      }
    });

    return Array.from(categories);
  }

  /**
   * Analyze sentiment for specific topics
   */
  private analyzeTopicSentiments(tweets: TweetData[], tweetSentiments: any[]): { [topic: string]: number } {
    const topicSentiments: { [topic: string]: number } = {};
    const topicCounts: { [topic: string]: number } = {};

    tweets.forEach((tweet, index) => {
      const text = tweet.text.toLowerCase();
      const sentiment = tweetSentiments[index];
      
      // Check for various topics and accumulate sentiment
      const topics = ['technology', 'business', 'politics', 'sports', 'entertainment'];
      
      topics.forEach(topic => {
        if (text.includes(topic)) {
          topicSentiments[topic] = (topicSentiments[topic] || 0) + sentiment.score;
          topicCounts[topic] = (topicCounts[topic] || 0) + 1;
        }
      });
    });

    // Calculate average sentiment per topic
    Object.keys(topicSentiments).forEach(topic => {
      topicSentiments[topic] = topicSentiments[topic] / topicCounts[topic];
    });

    return topicSentiments;
  }

  /**
   * Determine sentiment trend over time
   */
  private determineSentimentTrend(tweetSentiments: any[]): 'improving' | 'stable' | 'declining' {
    if (tweetSentiments.length < 5) return 'stable';

    // Compare first half vs second half
    const midpoint = Math.floor(tweetSentiments.length / 2);
    const firstHalf = tweetSentiments.slice(0, midpoint);
    const secondHalf = tweetSentiments.slice(midpoint);

    const firstHalfAvg = firstHalf.reduce((sum, s) => sum + s.score, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, s) => sum + s.score, 0) / secondHalf.length;

    const change = secondHalfAvg - firstHalfAvg;

    if (change > 10) return 'improving';
    if (change < -10) return 'declining';
    return 'stable';
  }

  /**
   * Calculate positivity ratio
   */
  private calculatePositivityRatio(tweetSentiments: any[]): number {
    if (tweetSentiments.length === 0) return 0;

    const positiveTweets = tweetSentiments.filter(s => s.score > 0).length;
    return (positiveTweets / tweetSentiments.length) * 100;
  }

  /**
   * Analyze correlation between sentiment and engagement
   */
  private analyzeEngagementSentiment(tweets: TweetData[], tweetSentiments: any[]): number {
    let correlationSum = 0;
    let validTweets = 0;

    tweets.forEach((tweet, index) => {
      const sentiment = tweetSentiments[index];
      const engagement = (tweet.likes || 0) + (tweet.retweets || 0) + (tweet.replies || 0);
      
      if (engagement > 0) {
        // Normalize engagement and sentiment for correlation
        const normalizedEngagement = Math.log10(engagement + 1);
        const normalizedSentiment = (sentiment.score + 100) / 200; // 0-1 scale
        
        correlationSum += normalizedEngagement * normalizedSentiment;
        validTweets++;
      }
    });

    return validTweets > 0 ? (correlationSum / validTweets) * 100 : 50;
  }

  /**
   * Assess brand safety
   */
  private assessBrandSafety(tweets: TweetData[], profileBio?: string): 'safe' | 'moderate' | 'risky' {
    let riskScore = 0;

    // Check tweets for risky content
    tweets.forEach(tweet => {
      const text = tweet.text.toLowerCase();
      
      // Toxic language
      this.toxicWords.forEach(word => {
        if (text.includes(word)) riskScore += 10;
      });
      
      // Controversial topics
      if (/\b(controversial|scandal|lawsuit|fraud|illegal)\b/.test(text)) {
        riskScore += 5;
      }
      
      // Excessive profanity
      const profanityCount = (text.match(/\b(damn|hell|crap)\b/g) || []).length;
      riskScore += profanityCount * 2;
    });

    // Check profile bio
    if (profileBio) {
      const bioText = profileBio.toLowerCase();
      this.toxicWords.forEach(word => {
        if (bioText.includes(word)) riskScore += 15;
      });
    }

    if (riskScore > 50) return 'risky';
    if (riskScore > 20) return 'moderate';
    return 'safe';
  }

  /**
   * Calculate toxicity score
   */
  private calculateToxicityScore(tweets: TweetData[]): number {
    let toxicityScore = 0;
    let totalWords = 0;

    tweets.forEach(tweet => {
      const words = tweet.text.toLowerCase().split(/\s+/);
      totalWords += words.length;
      
      words.forEach(word => {
        const cleanWord = word.replace(/[^\w]/g, '');
        if (this.toxicWords.has(cleanWord)) {
          toxicityScore += 10;
        }
      });
    });

    return totalWords > 0 ? Math.min(100, (toxicityScore / totalWords) * 1000) : 0;
  }

  /**
   * Calculate professionalism score
   */
  private calculateProfessionalismScore(tweets: TweetData[], profileBio?: string): number {
    let professionalismScore = 50; // Base score
    let totalWords = 0;
    let professionalWords = 0;

    // Analyze tweets
    tweets.forEach(tweet => {
      const words = tweet.text.toLowerCase().split(/\s+/);
      totalWords += words.length;
      
      words.forEach(word => {
        const cleanWord = word.replace(/[^\w]/g, '');
        if (this.professionalWords.has(cleanWord)) {
          professionalWords++;
        }
      });
      
      // Professional indicators
      if (tweet.text.includes('LinkedIn') || tweet.text.includes('conference') || 
          tweet.text.includes('presentation') || tweet.text.includes('meeting')) {
        professionalismScore += 2;
      }
      
      // Unprofessional indicators
      if (this.toxicWords.has(tweet.text.toLowerCase())) {
        professionalismScore -= 5;
      }
    });

    // Professional word density
    if (totalWords > 0) {
      const professionalDensity = (professionalWords / totalWords) * 100;
      professionalismScore += professionalDensity;
    }

    // Analyze bio
    if (profileBio) {
      const bioWords = profileBio.toLowerCase().split(/\s+/);
      bioWords.forEach(word => {
        const cleanWord = word.replace(/[^\w]/g, '');
        if (this.professionalWords.has(cleanWord)) {
          professionalismScore += 3;
        }
      });
    }

    return Math.min(100, Math.max(0, professionalismScore));
  }
}
