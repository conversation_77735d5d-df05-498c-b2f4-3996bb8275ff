#!/bin/bash

# Security Implementation Test Script
# Tests that direct service access is properly blocked

set -e

API_GATEWAY_URL="http://localhost:3010"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔒 Security Implementation Test Suite${NC}"
echo -e "${BLUE}====================================${NC}"
echo ""

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to test direct access (should be blocked)
test_direct_access() {
    local service_name="$1"
    local port="$2"
    local endpoint="$3"
    local expected_result="$4" # "blocked" or "allowed"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing direct access to $service_name:$port$endpoint ... "
    
    response=$(curl -s -w "%{http_code}" -o /tmp/direct_response.json "http://localhost:$port$endpoint" 2>/dev/null || echo "000")
    
    if [ "$expected_result" = "blocked" ]; then
        if [ "$response" = "403" ]; then
            echo -e "${GREEN}✅ BLOCKED (403 Forbidden)${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        elif [ "$response" = "000" ]; then
            echo -e "${GREEN}✅ BLOCKED (Connection Refused)${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}❌ NOT BLOCKED (Got: $response)${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            if [ -f /tmp/direct_response.json ]; then
                echo -e "${YELLOW}Response:${NC} $(cat /tmp/direct_response.json | head -c 100)..."
            fi
        fi
    else
        if [ "$response" = "200" ]; then
            echo -e "${GREEN}✅ ALLOWED (200 OK)${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}❌ UNEXPECTED (Got: $response)${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    fi
}

# Function to test gateway access (should work)
test_gateway_access() {
    local endpoint="$1"
    local expected_status="${2:-200}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing gateway access to $endpoint ... "
    
    response=$(curl -s -w "%{http_code}" -o /tmp/gateway_response.json "$API_GATEWAY_URL$endpoint" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ WORKING (${response})${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAILED (Expected: $expected_status, Got: $response)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        if [ -f /tmp/gateway_response.json ]; then
            echo -e "${YELLOW}Response:${NC} $(cat /tmp/gateway_response.json | head -c 100)..."
        fi
    fi
}

echo -e "${BLUE}🔍 Step 1: Test Direct Service Access (Should be blocked in production)${NC}"
echo -e "${YELLOW}Note: Currently ALLOW_DIRECT_ACCESS=true in development${NC}"
echo ""

# Test direct access to services
# In development with ALLOW_DIRECT_ACCESS=true, these should still work
# In production with ALLOW_DIRECT_ACCESS=false, these should be blocked

test_direct_access "user-service" "3011" "/api/health" "allowed"
test_direct_access "user-service" "3011" "/api/users/health" "allowed"
test_direct_access "profile-analysis-service" "3002" "/api/health" "allowed"
test_direct_access "nft-generation-service" "3003" "/api/health" "allowed"
test_direct_access "project-service" "3005" "/api/health" "allowed"
test_direct_access "blockchain-service" "3004" "/api/health" "allowed"
test_direct_access "marketplace-service" "3006" "/api/health" "allowed"
test_direct_access "analytics-service" "3009" "/api/health" "allowed"
test_direct_access "notification-service" "3008" "/api/health" "allowed"

echo -e "\n${BLUE}🚪 Step 2: Test API Gateway Access (Should always work)${NC}"

# Test gateway access
test_gateway_access "/api/health"
test_gateway_access "/api/users/health"
test_gateway_access "/api/analysis/health"
test_gateway_access "/api/nft-generation/health"
test_gateway_access "/api/blockchain/health"
test_gateway_access "/api/analytics/health"

echo -e "\n${BLUE}🔧 Step 3: Test Security Configuration${NC}"

# Test environment info
test_gateway_access "/api/environment/info"

echo -e "\n${BLUE}📊 Step 4: Security Configuration Status${NC}"

echo -e "${YELLOW}Current Security Settings:${NC}"
echo -e "  ALLOW_DIRECT_ACCESS: ${ALLOW_DIRECT_ACCESS:-true}"
echo -e "  GATEWAY_SECRET: ${GATEWAY_SECRET:+[SET]} ${GATEWAY_SECRET:-[NOT SET]}"
echo -e "  NODE_ENV: ${NODE_ENV:-development}"

echo -e "\n${BLUE}🔒 Step 5: Production Security Recommendations${NC}"

echo -e "${YELLOW}To enable full security in production:${NC}"
echo -e "  1. Set ALLOW_DIRECT_ACCESS=false"
echo -e "  2. Change GATEWAY_SECRET to a strong secret"
echo -e "  3. Set NODE_ENV=production"
echo -e "  4. Configure firewall rules to block direct service ports"
echo -e "  5. Use Docker networks or service mesh for isolation"

echo -e "\n${BLUE}📈 Test Results Summary${NC}"
echo -e "${BLUE}======================${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 Security implementation is working correctly!${NC}"
    echo -e "${GREEN}🔒 All tests passed for current configuration.${NC}"
    
    if [ "${ALLOW_DIRECT_ACCESS:-true}" = "true" ]; then
        echo -e "\n${YELLOW}⚠️  Note: Direct access is currently ALLOWED in development mode.${NC}"
        echo -e "${YELLOW}   Set ALLOW_DIRECT_ACCESS=false to test production security.${NC}"
    fi
    
    exit 0
else
    echo -e "\n${RED}❌ Some security tests failed.${NC}"
    echo -e "${RED}🔧 Please review the security configuration.${NC}"
    exit 1
fi
