'use client'

import React, { useState } from 'react'
import {
  ArrowPathIcon,
  PlusIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  TrashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import {
  useCreateBulkOperation,
  useExecuteBulkOperation,
  useCancelBulkOperation,
  useDeleteBulkOperation
} from '@/hooks/useMarketplaceIntegration'
import { BulkOperation, BulkOperationType } from '@/types/marketplace-integration.types'

interface BulkOperationsManagerProps {
  operations?: BulkOperation[]
  isLoading: boolean
  className?: string
}

export default function BulkOperationsManager({
  operations = [],
  isLoading,
  className = ''
}: BulkOperationsManagerProps) {
  const [showCreateOperation, setShowCreateOperation] = useState(false)
  const [newOperation, setNewOperation] = useState({
    type: BulkOperationType.UPDATE_PRICING,
    name: '',
    description: '',
    targetIds: [] as string[],
    targetType: 'campaigns' as const,
    operationData: {}
  })

  const createOperationMutation = useCreateBulkOperation()
  const executeOperationMutation = useExecuteBulkOperation()
  const cancelOperationMutation = useCancelBulkOperation()
  const deleteOperationMutation = useDeleteBulkOperation()

  const handleCreateOperation = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await createOperationMutation.mutateAsync(newOperation)
      setShowCreateOperation(false)
      setNewOperation({
        type: BulkOperationType.UPDATE_PRICING,
        name: '',
        description: '',
        targetIds: [],
        targetType: 'campaigns',
        operationData: {}
      })
    } catch (error) {
      console.error('Failed to create bulk operation:', error)
    }
  }

  const handleExecuteOperation = async (id: string) => {
    try {
      await executeOperationMutation.mutateAsync(id)
    } catch (error) {
      console.error('Failed to execute operation:', error)
    }
  }

  const handleCancelOperation = async (id: string) => {
    try {
      await cancelOperationMutation.mutateAsync(id)
    } catch (error) {
      console.error('Failed to cancel operation:', error)
    }
  }

  const handleDeleteOperation = async (id: string) => {
    if (confirm('Are you sure you want to delete this operation?')) {
      try {
        await deleteOperationMutation.mutateAsync(id)
      } catch (error) {
        console.error('Failed to delete operation:', error)
      }
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-gray-600 bg-gray-100'
      case 'processing': return 'text-blue-600 bg-blue-100'
      case 'completed': return 'text-green-600 bg-green-100'
      case 'failed': return 'text-red-600 bg-red-100'
      case 'cancelled': return 'text-orange-600 bg-orange-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <ClockIcon className="h-4 w-4" />
      case 'processing': return <ArrowPathIcon className="h-4 w-4 animate-spin" />
      case 'completed': return <CheckCircleIcon className="h-4 w-4" />
      case 'failed': return <ExclamationTriangleIcon className="h-4 w-4" />
      case 'cancelled': return <StopIcon className="h-4 w-4" />
      default: return <ClockIcon className="h-4 w-4" />
    }
  }

  const operationTypes = [
    { value: BulkOperationType.UPDATE_PRICING, label: 'Update Pricing' },
    { value: BulkOperationType.CHANGE_STATUS, label: 'Change Status' },
    { value: BulkOperationType.UPDATE_METADATA, label: 'Update Metadata' },
    { value: BulkOperationType.BATCH_LIST, label: 'Batch List' },
    { value: BulkOperationType.BATCH_DELIST, label: 'Batch Delist' },
    { value: BulkOperationType.UPDATE_ROYALTIES, label: 'Update Royalties' },
    { value: BulkOperationType.BULK_APPROVE, label: 'Bulk Approve' },
    { value: BulkOperationType.BULK_REJECT, label: 'Bulk Reject' }
  ]

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Bulk Operations</h2>
          <p className="text-sm text-gray-600">Manage multiple listings efficiently</p>
        </div>

        <button
          onClick={() => setShowCreateOperation(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Operation
        </button>
      </div>

      {/* Operations List */}
      {operations.length > 0 ? (
        <div className="space-y-4">
          {operations.map((operation) => (
            <div key={operation.id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(operation.status)}`}>
                    {getStatusIcon(operation.status)}
                    <span className="ml-1">{operation.status}</span>
                  </span>
                  <h3 className="text-lg font-medium text-gray-900">{operation.name}</h3>
                </div>

                <div className="flex items-center space-x-2">
                  {operation.status === 'pending' && (
                    <button
                      onClick={() => handleExecuteOperation(operation.id)}
                      disabled={executeOperationMutation.isPending}
                      className="p-2 text-green-600 hover:text-green-700 disabled:opacity-50"
                      title="Execute Operation"
                    >
                      <PlayIcon className="h-4 w-4" />
                    </button>
                  )}

                  {operation.status === 'processing' && (
                    <button
                      onClick={() => handleCancelOperation(operation.id)}
                      disabled={cancelOperationMutation.isPending}
                      className="p-2 text-orange-600 hover:text-orange-700 disabled:opacity-50"
                      title="Cancel Operation"
                    >
                      <PauseIcon className="h-4 w-4" />
                    </button>
                  )}

                  {['completed', 'failed', 'cancelled'].includes(operation.status) && (
                    <button
                      onClick={() => handleDeleteOperation(operation.id)}
                      disabled={deleteOperationMutation.isPending}
                      className="p-2 text-red-600 hover:text-red-700 disabled:opacity-50"
                      title="Delete Operation"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div>
                  <span className="text-sm text-gray-600">Type:</span>
                  <div className="font-medium">{operation.type.replace('_', ' ')}</div>
                </div>
                <div>
                  <span className="text-sm text-gray-600">Targets:</span>
                  <div className="font-medium">{operation.targetIds.length} {operation.targetType}</div>
                </div>
                <div>
                  <span className="text-sm text-gray-600">Progress:</span>
                  <div className="font-medium">{operation.progress}%</div>
                </div>
                <div>
                  <span className="text-sm text-gray-600">Success Rate:</span>
                  <div className="font-medium">
                    {operation.successCount}/{operation.successCount + operation.failureCount}
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                  <span>Progress</span>
                  <span>{operation.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      operation.status === 'completed' ? 'bg-green-600' :
                      operation.status === 'failed' ? 'bg-red-600' :
                      operation.status === 'processing' ? 'bg-blue-600' : 'bg-gray-400'
                    }`}
                    style={{ width: `${operation.progress}%` }}
                  ></div>
                </div>
              </div>

              {operation.description && (
                <p className="text-sm text-gray-600 mb-4">{operation.description}</p>
              )}

              {/* Timestamps */}
              <div className="flex items-center justify-between text-sm text-gray-500">
                <div>
                  Created: {new Date(operation.createdAt).toLocaleString()}
                </div>
                {operation.completedAt && (
                  <div>
                    Completed: {new Date(operation.completedAt).toLocaleString()}
                  </div>
                )}
              </div>

              {/* Errors */}
              {operation.errors && operation.errors.length > 0 && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="text-sm font-medium text-red-900 mb-2">Errors ({operation.errors.length})</h4>
                  <div className="space-y-1">
                    {operation.errors.slice(0, 3).map((error, index) => (
                      <div key={index} className="text-sm text-red-700">
                        {error.targetId}: {error.error}
                      </div>
                    ))}
                    {operation.errors.length > 3 && (
                      <div className="text-sm text-red-600">
                        +{operation.errors.length - 3} more errors
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <ArrowPathIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No bulk operations</h3>
          <p className="mt-1 text-sm text-gray-500">
            Create bulk operations to manage multiple listings efficiently.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowCreateOperation(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Operation
            </button>
          </div>
        </div>
      )}

      {/* Create Operation Modal */}
      {showCreateOperation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Create Bulk Operation</h3>
              <button
                onClick={() => setShowCreateOperation(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleCreateOperation} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Operation Name
                </label>
                <input
                  type="text"
                  value={newOperation.name}
                  onChange={(e) => setNewOperation(prev => ({ ...prev, name: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Update Summer Campaign Pricing"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Operation Type
                </label>
                <select
                  value={newOperation.type}
                  onChange={(e) => setNewOperation(prev => ({ ...prev, type: e.target.value as BulkOperationType }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {operationTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Target Type
                </label>
                <select
                  value={newOperation.targetType}
                  onChange={(e) => setNewOperation(prev => ({ ...prev, targetType: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="campaigns">Campaigns</option>
                  <option value="collections">Collections</option>
                  <option value="nfts">NFTs</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Target IDs (comma-separated)
                </label>
                <textarea
                  value={newOperation.targetIds.join(', ')}
                  onChange={(e) => setNewOperation(prev => ({ 
                    ...prev, 
                    targetIds: e.target.value.split(',').map(id => id.trim()).filter(Boolean)
                  }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="id1, id2, id3..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description (optional)
                </label>
                <textarea
                  value={newOperation.description}
                  onChange={(e) => setNewOperation(prev => ({ ...prev, description: e.target.value }))}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe what this operation will do..."
                />
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateOperation(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createOperationMutation.isPending}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {createOperationMutation.isPending ? 'Creating...' : 'Create Operation'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
