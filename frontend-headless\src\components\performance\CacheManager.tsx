'use client'

import React, { useState } from 'react'
import {
  CubeIcon,
  TrashIcon,
  ArrowPathIcon,
  PlusIcon,
  EyeIcon,
  FireIcon,
  ClockIcon,
  ChartBarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import {
  useCacheStrategies,
  useCacheMetrics,
  useCreateCacheStrategy,
  useUpdateCacheStrategy,
  useClearCache,
  useWarmupCache
} from '@/hooks/usePerformance'
import {
  CacheStrategy,
  CacheType,
  EvictionPolicy,
  SerializationType
} from '@/types/performance.types'

interface CacheManagerProps {
  className?: string
}

export default function CacheManager({
  className = ''
}: CacheManagerProps) {
  const [selectedType, setSelectedType] = useState<CacheType | 'all'>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showWarmupModal, setShowWarmupModal] = useState(false)
  const [warmupKeys, setWarmupKeys] = useState('')
  const [newStrategy, setNewStrategy] = useState<Partial<CacheStrategy>>({
    type: CacheType.REDIS,
    ttl: 3600,
    maxSize: 1000,
    evictionPolicy: EvictionPolicy.LRU,
    compression: false,
    serialization: SerializationType.JSON,
    tags: [],
    invalidationRules: []
  })

  const { data: strategies = [], isLoading: strategiesLoading } = useCacheStrategies()
  const { data: metrics, isLoading: metricsLoading } = useCacheMetrics(
    selectedType === 'all' ? undefined : selectedType
  )
  const createStrategyMutation = useCreateCacheStrategy()
  const updateStrategyMutation = useUpdateCacheStrategy()
  const clearCacheMutation = useClearCache()
  const warmupCacheMutation = useWarmupCache()

  // Mock data if not available
  const mockStrategies: CacheStrategy[] = [
    {
      type: CacheType.REDIS,
      ttl: 3600,
      maxSize: 10000,
      evictionPolicy: EvictionPolicy.LRU,
      compression: true,
      serialization: SerializationType.JSON,
      tags: ['nft', 'metadata'],
      invalidationRules: [
        {
          trigger: 'event_based',
          pattern: 'nft:*',
          cascade: true
        }
      ]
    },
    {
      type: CacheType.MEMORY,
      ttl: 300,
      maxSize: 1000,
      evictionPolicy: EvictionPolicy.LFU,
      compression: false,
      serialization: SerializationType.JSON,
      tags: ['user', 'session'],
      invalidationRules: []
    },
    {
      type: CacheType.CDN,
      ttl: 86400,
      maxSize: 50000,
      evictionPolicy: EvictionPolicy.TTL,
      compression: true,
      serialization: SerializationType.JSON,
      tags: ['static', 'images'],
      invalidationRules: [
        {
          trigger: 'manual',
          pattern: 'images/*',
          cascade: false
        }
      ]
    }
  ]

  const mockMetrics = {
    overall: {
      hitRate: 94.5,
      missRate: 5.5,
      evictionRate: 0.2,
      memory: {
        used: 2048,
        total: 4096,
        percentage: 50.0
      },
      operations: {
        gets: 150000,
        sets: 15000,
        deletes: 500
      },
      latency: {
        get: 0.8,
        set: 1.2
      }
    },
    byType: {
      [CacheType.REDIS]: {
        hitRate: 96.2,
        missRate: 3.8,
        evictionRate: 0.1,
        memory: { used: 1536, total: 2048, percentage: 75.0 },
        operations: { gets: 100000, sets: 10000, deletes: 300 },
        latency: { get: 0.5, set: 0.8 }
      },
      [CacheType.MEMORY]: {
        hitRate: 89.5,
        missRate: 10.5,
        evictionRate: 0.5,
        memory: { used: 256, total: 512, percentage: 50.0 },
        operations: { gets: 30000, sets: 3000, deletes: 100 },
        latency: { get: 0.1, set: 0.2 }
      },
      [CacheType.CDN]: {
        hitRate: 98.1,
        missRate: 1.9,
        evictionRate: 0.0,
        memory: { used: 256, total: 1536, percentage: 16.7 },
        operations: { gets: 20000, sets: 2000, deletes: 100 },
        latency: { get: 15.0, set: 25.0 }
      }
    }
  }

  const displayStrategies = strategies.length > 0 ? strategies : mockStrategies
  const displayMetrics = metrics || mockMetrics

  const cacheTypes = [
    { value: 'all', label: 'All Cache Types' },
    { value: CacheType.REDIS, label: 'Redis' },
    { value: CacheType.MEMORY, label: 'Memory' },
    { value: CacheType.CDN, label: 'CDN' },
    { value: CacheType.BROWSER, label: 'Browser' },
    { value: CacheType.DATABASE, label: 'Database' }
  ]

  const evictionPolicies = [
    { value: EvictionPolicy.LRU, label: 'LRU (Least Recently Used)' },
    { value: EvictionPolicy.LFU, label: 'LFU (Least Frequently Used)' },
    { value: EvictionPolicy.FIFO, label: 'FIFO (First In, First Out)' },
    { value: EvictionPolicy.TTL, label: 'TTL (Time To Live)' },
    { value: EvictionPolicy.RANDOM, label: 'Random' }
  ]

  const serializationTypes = [
    { value: SerializationType.JSON, label: 'JSON' },
    { value: SerializationType.MSGPACK, label: 'MessagePack' },
    { value: SerializationType.PROTOBUF, label: 'Protocol Buffers' },
    { value: SerializationType.AVRO, label: 'Apache Avro' }
  ]

  const getCacheTypeIcon = (type: CacheType) => {
    switch (type) {
      case CacheType.REDIS:
        return <CubeIcon className="h-5 w-5 text-red-600" />
      case CacheType.MEMORY:
        return <ChartBarIcon className="h-5 w-5 text-blue-600" />
      case CacheType.CDN:
        return <FireIcon className="h-5 w-5 text-orange-600" />
      case CacheType.BROWSER:
        return <EyeIcon className="h-5 w-5 text-green-600" />
      case CacheType.DATABASE:
        return <CubeIcon className="h-5 w-5 text-purple-600" />
      default:
        return <CubeIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getHitRateColor = (hitRate: number) => {
    if (hitRate >= 95) return 'text-green-600'
    if (hitRate >= 85) return 'text-yellow-600'
    return 'text-red-600'
  }

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const handleCreateStrategy = () => {
    if (newStrategy.type) {
      createStrategyMutation.mutate(newStrategy as Omit<CacheStrategy, 'id'>)
      setShowCreateModal(false)
      setNewStrategy({
        type: CacheType.REDIS,
        ttl: 3600,
        maxSize: 1000,
        evictionPolicy: EvictionPolicy.LRU,
        compression: false,
        serialization: SerializationType.JSON,
        tags: [],
        invalidationRules: []
      })
    }
  }

  const handleClearCache = (type?: CacheType, pattern?: string) => {
    clearCacheMutation.mutate({ type, pattern })
  }

  const handleWarmupCache = () => {
    const keys = warmupKeys.split('\n').map(k => k.trim()).filter(k => k.length > 0)
    if (keys.length > 0) {
      warmupCacheMutation.mutate(keys)
      setShowWarmupModal(false)
      setWarmupKeys('')
    }
  }

  if (strategiesLoading || metricsLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900">Cache Management</h2>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowWarmupModal(true)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <FireIcon className="h-4 w-4 mr-2" />
            Warmup Cache
          </button>
          
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Strategy
          </button>
        </div>
      </div>

      {/* Cache Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Hit Rate</p>
              <p className={`text-2xl font-bold ${getHitRateColor(displayMetrics.overall.hitRate)}`}>
                {displayMetrics.overall.hitRate.toFixed(1)}%
              </p>
            </div>
            <ChartBarIcon className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Memory Usage</p>
              <p className="text-2xl font-bold text-blue-600">
                {displayMetrics.overall.memory.percentage.toFixed(1)}%
              </p>
            </div>
            <CubeIcon className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Operations/sec</p>
              <p className="text-2xl font-bold text-purple-600">
                {Math.round(displayMetrics.overall.operations.gets / 60).toLocaleString()}
              </p>
            </div>
            <ArrowPathIcon className="h-8 w-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Latency</p>
              <p className="text-2xl font-bold text-orange-600">
                {displayMetrics.overall.latency.get.toFixed(1)}ms
              </p>
            </div>
            <ClockIcon className="h-8 w-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Cache Type Filter */}
      <div className="flex items-center space-x-4">
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {cacheTypes.map((type) => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>

        <button
          onClick={() => handleClearCache()}
          className="inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
        >
          <TrashIcon className="h-4 w-4 mr-2" />
          Clear All Cache
        </button>
      </div>

      {/* Cache Strategies */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Cache Strategies</h3>
        
        {displayStrategies.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {displayStrategies.map((strategy, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {getCacheTypeIcon(strategy.type)}
                    <span className="text-sm font-medium text-gray-900 capitalize">
                      {strategy.type}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => handleClearCache(strategy.type)}
                      className="text-red-600 hover:text-red-700"
                      title="Clear cache"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">TTL:</span>
                    <span className="text-gray-900">{strategy.ttl}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Size:</span>
                    <span className="text-gray-900">{strategy.maxSize.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Eviction:</span>
                    <span className="text-gray-900">{strategy.evictionPolicy}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Compression:</span>
                    <span className="text-gray-900">{strategy.compression ? 'Yes' : 'No'}</span>
                  </div>
                </div>

                {/* Performance Metrics */}
                {displayMetrics.byType && displayMetrics.byType[strategy.type] && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="text-center">
                        <div className={`font-semibold ${getHitRateColor(displayMetrics.byType[strategy.type].hitRate)}`}>
                          {displayMetrics.byType[strategy.type].hitRate.toFixed(1)}%
                        </div>
                        <div className="text-gray-600">Hit Rate</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-gray-900">
                          {displayMetrics.byType[strategy.type].memory.percentage.toFixed(0)}%
                        </div>
                        <div className="text-gray-600">Memory</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Tags */}
                {strategy.tags.length > 0 && (
                  <div className="mt-3">
                    <div className="flex flex-wrap gap-1">
                      {strategy.tags.map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
            <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No cache strategies</h3>
            <p className="mt-1 text-sm text-gray-500">
              Create your first cache strategy to optimize performance.
            </p>
          </div>
        )}
      </div>

      {/* Create Strategy Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Create Cache Strategy</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cache Type
                  </label>
                  <select
                    value={newStrategy.type}
                    onChange={(e) => setNewStrategy(prev => ({ ...prev, type: e.target.value as CacheType }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {cacheTypes.slice(1).map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      TTL (seconds)
                    </label>
                    <input
                      type="number"
                      value={newStrategy.ttl}
                      onChange={(e) => setNewStrategy(prev => ({ ...prev, ttl: parseInt(e.target.value) }))}
                      min="1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Max Size
                    </label>
                    <input
                      type="number"
                      value={newStrategy.maxSize}
                      onChange={(e) => setNewStrategy(prev => ({ ...prev, maxSize: parseInt(e.target.value) }))}
                      min="1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Eviction Policy
                  </label>
                  <select
                    value={newStrategy.evictionPolicy}
                    onChange={(e) => setNewStrategy(prev => ({ ...prev, evictionPolicy: e.target.value as EvictionPolicy }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {evictionPolicies.map((policy) => (
                      <option key={policy.value} value={policy.value}>
                        {policy.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Serialization
                  </label>
                  <select
                    value={newStrategy.serialization}
                    onChange={(e) => setNewStrategy(prev => ({ ...prev, serialization: e.target.value as SerializationType }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {serializationTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newStrategy.compression}
                    onChange={(e) => setNewStrategy(prev => ({ ...prev, compression: e.target.checked }))}
                    className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label className="text-sm text-gray-700">Enable compression</label>
                </div>
              </div>
              
              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateStrategy}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                >
                  Create Strategy
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Warmup Modal */}
      {showWarmupModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Warmup Cache</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cache Keys (one per line)
                  </label>
                  <textarea
                    value={warmupKeys}
                    onChange={(e) => setWarmupKeys(e.target.value)}
                    placeholder="nft:metadata:123&#10;user:profile:456&#10;collection:stats:789"
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowWarmupModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleWarmupCache}
                  disabled={!warmupKeys.trim()}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  Warmup Cache
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
