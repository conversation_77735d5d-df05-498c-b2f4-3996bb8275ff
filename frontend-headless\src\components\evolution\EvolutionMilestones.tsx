'use client'

import React from 'react'
import {
  TrophyIcon,
  CheckCircleIcon,
  ClockIcon,
  GiftIcon,
  StarIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline'
import { CheckCircleIcon as CheckCircleSolidIcon } from '@heroicons/react/24/solid'
import { useEvolutionMilestones, useClaimMilestoneReward } from '@/hooks/useEvolution'

interface EvolutionMilestonesProps {
  nftId: string
  showClaimable?: boolean
  maxMilestones?: number
  className?: string
}

export default function EvolutionMilestones({
  nftId,
  showClaimable = true,
  maxMilestones = 10,
  className = ''
}: EvolutionMilestonesProps) {
  const { data: milestones, isLoading, refetch } = useEvolutionMilestones(nftId)
  const claimRewardMutation = useClaimMilestoneReward()

  const handleClaimReward = async (milestoneId: string) => {
    try {
      await claimRewardMutation.mutateAsync({ nftId, milestoneId })
      refetch()
    } catch (error) {
      console.error('Failed to claim reward:', error)
    }
  }

  const getMilestoneIcon = (achieved: boolean, progress: number) => {
    if (achieved) {
      return <CheckCircleSolidIcon className="h-6 w-6 text-green-500" />
    }
    if (progress > 0) {
      return <ClockIcon className="h-6 w-6 text-yellow-500" />
    }
    return <LockClosedIcon className="h-6 w-6 text-gray-400" />
  }

  const getMilestoneColor = (achieved: boolean, progress: number) => {
    if (achieved) return 'border-green-200 bg-green-50'
    if (progress > 0) return 'border-yellow-200 bg-yellow-50'
    return 'border-gray-200 bg-gray-50'
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return 'bg-green-500'
    if (progress >= 75) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    if (progress >= 25) return 'bg-orange-500'
    return 'bg-gray-300'
  }

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (!milestones || milestones.length === 0) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center py-8">
          <TrophyIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No milestones available</h3>
          <p className="mt-1 text-sm text-gray-500">
            Milestones will appear as your NFT evolves and reaches new achievements.
          </p>
        </div>
      </div>
    )
  }

  const displayMilestones = milestones.slice(0, maxMilestones)
  const achievedCount = milestones.filter(m => m.achieved).length
  const claimableCount = milestones.filter(m => m.achieved && m.reward && !m.achievedAt).length

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <TrophyIcon className="h-5 w-5 mr-2 text-gray-500" />
              Evolution Milestones
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {achievedCount} of {milestones.length} milestones achieved
            </p>
          </div>
          
          {claimableCount > 0 && showClaimable && (
            <div className="text-right">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                <GiftIcon className="h-4 w-4 mr-1" />
                {claimableCount} rewards to claim
              </span>
            </div>
          )}
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Overall Progress</span>
            <span>{achievedCount}/{milestones.length}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(achievedCount / milestones.length) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Milestones List */}
      <div className="p-6">
        <div className="space-y-4">
          {displayMilestones.map((milestone, index) => (
            <div
              key={milestone.id}
              className={`border rounded-lg p-4 transition-all duration-200 ${getMilestoneColor(milestone.achieved, milestone.progress)}`}
            >
              <div className="flex items-start space-x-4">
                {/* Icon */}
                <div className="flex-shrink-0 mt-1">
                  {getMilestoneIcon(milestone.achieved, milestone.progress)}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-base font-medium text-gray-900">
                      {milestone.name}
                    </h4>
                    {milestone.achieved && milestone.achievedAt && (
                      <span className="text-xs text-gray-500">
                        {new Date(milestone.achievedAt).toLocaleDateString()}
                      </span>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 mb-3">
                    {milestone.description}
                  </p>

                  {/* Requirements */}
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
                    <span>Requirement: {milestone.requirement}</span>
                    {milestone.scoreRequired && (
                      <span>Score needed: {milestone.scoreRequired}</span>
                    )}
                  </div>

                  {/* Progress Bar */}
                  {!milestone.achieved && (
                    <div className="mb-3">
                      <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                        <span>Progress</span>
                        <span>{milestone.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(milestone.progress)}`}
                          style={{ width: `${Math.min(100, milestone.progress)}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Reward */}
                  {milestone.reward && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <GiftIcon className="h-4 w-4 text-purple-500" />
                        <span className="text-sm font-medium text-purple-700">
                          Reward: {milestone.reward}
                        </span>
                      </div>

                      {milestone.achieved && showClaimable && !milestone.achievedAt && (
                        <button
                          onClick={() => handleClaimReward(milestone.id)}
                          disabled={claimRewardMutation.isPending}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 transition-colors"
                        >
                          {claimRewardMutation.isPending ? 'Claiming...' : 'Claim Reward'}
                        </button>
                      )}
                    </div>
                  )}

                  {/* Badge */}
                  {milestone.badge && milestone.achieved && (
                    <div className="mt-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                        <StarIcon className="h-3 w-3 mr-1" />
                        {milestone.badge}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Show More */}
        {milestones.length > maxMilestones && (
          <div className="mt-6 text-center">
            <button className="text-sm text-blue-600 hover:text-blue-500 font-medium">
              View {milestones.length - maxMilestones} more milestones
            </button>
          </div>
        )}

        {/* Summary Stats */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-green-600">{achievedCount}</p>
              <p className="text-sm text-gray-600">Achieved</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-yellow-600">
                {milestones.filter(m => m.progress > 0 && !m.achieved).length}
              </p>
              <p className="text-sm text-gray-600">In Progress</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-600">
                {milestones.filter(m => m.progress === 0).length}
              </p>
              <p className="text-sm text-gray-600">Locked</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
