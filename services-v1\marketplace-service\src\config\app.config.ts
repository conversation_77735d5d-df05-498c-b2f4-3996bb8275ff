import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>E<PERSON>, IsOptional, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';

enum Environment {
  DEVELOPMENT = 'development',
  PRODUCTION = 'production',
  TEST = 'test',
}

enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

enum LogFormat {
  JSON = 'json',
  SIMPLE = 'simple',
  COMBINED = 'combined',
}

export class AppConfig {
  @IsString()
  SERVICE_NAME: string = 'marketplace-service';

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  SERVICE_PORT: number = 3006;

  @IsEnum(Environment)
  NODE_ENV: Environment = Environment.DEVELOPMENT;

  @IsString()
  DATABASE_URL: string;

  @IsString()
  JWT_SECRET: string;

  @IsString()
  JWT_EXPIRES_IN: string = '24h';

  @IsString()
  API_PREFIX: string = 'api';

  @IsEnum(LogLevel)
  LOG_LEVEL: LogLevel = LogLevel.INFO;

  @IsEnum(LogFormat)
  LOG_FORMAT: LogFormat = LogFormat.JSON;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  HEALTH_CHECK_TIMEOUT: number = 5000;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  HEALTH_CHECK_INTERVAL: number = 30000;

  @IsString()
  CORS_ORIGIN: string = '*';

  // Marketplace Configuration
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @IsOptional()
  MARKETPLACE_FEE_PERCENTAGE?: number = 2.5;

  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @IsOptional()
  MIN_LISTING_PRICE?: number = 0.001;

  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @IsOptional()
  MAX_LISTING_PRICE?: number = 1000000;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @IsOptional()
  AUCTION_MIN_DURATION?: number = 3600;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @IsOptional()
  AUCTION_MAX_DURATION?: number = 604800;

  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @IsOptional()
  BID_INCREMENT_PERCENTAGE?: number = 5;

  // Payment Configuration
  @IsString()
  @IsOptional()
  PAYMENT_PROCESSOR?: string = 'stripe';

  @IsString()
  @IsOptional()
  STRIPE_SECRET_KEY?: string;

  @IsString()
  @IsOptional()
  STRIPE_WEBHOOK_SECRET?: string;

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  @IsOptional()
  CRYPTO_PAYMENT_ENABLED?: boolean = true;

  // File Upload Configuration
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @IsOptional()
  MAX_FILE_SIZE?: number = 10485760;

  @IsString()
  @IsOptional()
  ALLOWED_FILE_TYPES?: string = 'image/jpeg,image/png,image/gif,image/webp';

  @IsString()
  @IsOptional()
  UPLOAD_DESTINATION?: string = './uploads/marketplace';

  // External service URLs (Industry Standard - Service Discovery)
  @IsString()
  @IsOptional()
  USER_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  PROJECT_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  NFT_GENERATION_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  BLOCKCHAIN_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  ANALYTICS_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  PROFILE_ANALYSIS_SERVICE_URL?: string;

  @IsString()
  @IsOptional()
  NOTIFICATION_SERVICE_URL?: string;

  // Validation method
  static validate(config: Record<string, unknown>): AppConfig {
    const validatedConfig = new AppConfig();
    Object.assign(validatedConfig, config);
    return validatedConfig;
  }
}
