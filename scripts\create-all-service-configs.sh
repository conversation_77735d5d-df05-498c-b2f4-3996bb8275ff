#!/bin/bash

# Create All Service Configurations
# This script creates standardized configuration classes for all services

set -e

echo "🔧 Creating Standardized Configuration Classes for All Services"
echo "=============================================================="

# Services configuration
declare -A SERVICES=(
    ["api-gateway"]="3010:api_gateway"
    ["user-service"]="3011:user_service"
    ["profile-analysis-service"]="3002:profile_analysis_service"
    ["nft-generation-service"]="3003:nft_generation_service"
    ["blockchain-service"]="3004:blockchain_service"
    ["project-service"]="3005:project_service"
    ["marketplace-service"]="3006:marketplace_service"
    ["notification-service"]="3008:notification_service"
    ["analytics-service"]="3009:analytics_service"
)

# Function to create service configuration
create_service_config() {
    local service_name="$1"
    local port_and_db="$2"
    local service_port=$(echo "$port_and_db" | cut -d: -f1)
    local db_name=$(echo "$port_and_db" | cut -d: -f2)
    
    echo "🔧 Creating configuration for $service_name..."
    
    # Check if service directory exists
    if [ ! -d "services/$service_name" ]; then
        echo "❌ Service directory not found: services/$service_name"
        return 1
    fi
    
    # Create configuration using template script
    ./scripts/create-service-config-template.sh "$service_name" "$service_port" "$db_name"
    
    echo "✅ Configuration created for $service_name"
}

# Main execution
echo "🚀 Starting configuration creation..."

for service_name in "${!SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service_name..."
    echo "------------------------"
    
    create_service_config "$service_name" "${SERVICES[$service_name]}"
done

echo ""
echo "🎉 All Service Configurations Created Successfully!"
echo "================================================="
echo ""
echo "📋 Summary:"
echo "- Created standardized configuration classes for ${#SERVICES[@]} services"
echo "- Each service now has src/config/app.config.ts with validation"
echo "- Configuration classes implement type safety and validation"
echo ""
echo "📁 Files Created:"
echo "services/[service]/src/config/app.config.ts    # Service configuration class"
echo "services/[service]/src/config/index.ts         # Configuration exports"
echo ""
echo "🔍 Next Steps:"
echo "1. Install dependencies: npm install class-validator class-transformer"
echo "2. Update app.module.ts files to use StandardizedConfigModule"
echo "3. Test configuration validation"
echo "4. Restart services to apply new configuration"
