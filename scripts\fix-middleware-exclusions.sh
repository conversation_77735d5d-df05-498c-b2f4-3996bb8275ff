#!/bin/bash

# Fix Middleware Exclusions for Health Endpoints
# This script fixes the middleware exclusion paths to properly exclude health endpoints

set -e

echo "🔧 Fixing Middleware Exclusions for Health Endpoints"
echo "==================================================="

# Services that need middleware exclusion fixes
SERVICES=(
    "profile-analysis-service"
    "blockchain-service" 
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Function to fix middleware exclusions in app.module.ts
fix_middleware_exclusions() {
    local service=$1
    local app_module="services/$service/src/app.module.ts"
    
    echo "🔧 Fixing middleware exclusions for $service..."
    
    if [ ! -f "$app_module" ]; then
        echo "⚠️  app.module.ts not found for $service"
        return
    fi
    
    # Create a clean app.module.ts with correct exclusions
    cat > "$app_module" << 'EOF'
import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { EnterpriseModule } from './enterprise/enterprise.module';
import { HealthModule } from './health/health.module';

// Security Middleware
import { GatewayAuthMiddleware } from './common/middleware/gateway-auth.middleware';
import { CorrelationIdMiddleware } from './common/middleware/correlation-id.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Enterprise Module
    EnterpriseModule,
    
    // Health Module
    HealthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply security middleware first (before correlation ID)
    // Apply to all routes except health checks
    consumer.apply(GatewayAuthMiddleware)
      .exclude(
        // Health endpoints with global prefix
        { path: 'api/health', method: RequestMethod.GET },
        { path: 'api/health/simple', method: RequestMethod.GET },
        { path: 'api/health/database', method: RequestMethod.GET },
        // Health endpoints without global prefix (fallback)
        { path: 'health', method: RequestMethod.GET },
        { path: 'health/simple', method: RequestMethod.GET },
        { path: 'health/database', method: RequestMethod.GET },
        // Other status endpoints
        { path: 'status', method: RequestMethod.GET },
      )
      .forRoutes('*');
    consumer.apply(CorrelationIdMiddleware).forRoutes('*');
  }
}
EOF
    
    echo "✅ Fixed middleware exclusions for $service"
}

# Main execution
echo "🚀 Starting middleware exclusion fixes..."

for service in "${SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service" ]; then
        echo "❌ Service directory not found: services/$service"
        continue
    fi
    
    # Fix middleware exclusions
    fix_middleware_exclusions "$service"
    
    echo "✅ $service middleware exclusion fixes complete"
done

echo ""
echo "🎉 Middleware Exclusion Fixes Completed!"
echo "======================================="
echo ""
echo "📋 Summary:"
echo "- Fixed middleware exclusions for ${#SERVICES[@]} services"
echo "- Added proper health endpoint exclusions with and without global prefix"
echo "- Health endpoints are now accessible without gateway authentication"
echo "- All other endpoints still require gateway authentication"
echo ""
echo "🏥 Excluded Health Endpoints:"
echo "- /api/health (comprehensive health check)"
echo "- /api/health/simple (simple status check)"
echo "- /api/health/database (database-only check)"
echo ""
echo "🔄 Services will restart automatically due to watch mode"
