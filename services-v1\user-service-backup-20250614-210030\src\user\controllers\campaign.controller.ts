import { Controller, Get, Post, Put, Delete, Body, Param, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { CampaignService } from '../services/campaign.service';
import { 
  CreateCampaignDto, 
  UpdateCampaignDto, 
  CampaignType, 
  CampaignStatus 
} from '../dto/campaign.dto';
import { 
  JoinCampaignDto, 
  SubmitRequirementDto, 
  ReviewSubmissionDto,
  ClaimRewardDto,
  CampaignParticipationResponseDto 
} from '../dto/campaign-participation.dto';
import { CampaignAnalyticsDto } from '../dto/campaign-analytics.dto';
import { ErrorResponseDto } from '../../common/dto/error-response.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RBACGuard } from '../guards/rbac.guard';
import { 
  RequireCampaignRead,
  RequireCampaignWrite,
  RequireCampaignDelete,
  RequireCampaignAdmin,
  RequireAnalyticsRead,
  Authenticated 
} from '../decorators/permissions.decorator';

@ApiTags('Campaign Management')
@Controller('campaigns')
@UseGuards(JwtAuthGuard, RBACGuard)
@ApiBearerAuth()
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  @Post()
  @RequireCampaignWrite()
  @ApiOperation({ summary: 'Create a new campaign' })
  @ApiBody({ type: CreateCampaignDto })
  @ApiResponse({ status: 201, description: 'Campaign created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async createCampaign(@Body() createCampaignDto: CreateCampaignDto, @Req() request: any) {
    const result = await this.campaignService.createCampaign(createCampaignDto, request.context);
    return result;
  }

  @Get()
  @RequireCampaignRead()
  @ApiOperation({ summary: 'Get all campaigns with filtering and pagination' })
  @ApiQuery({ name: 'status', enum: CampaignStatus, required: false, description: 'Filter by campaign status' })
  @ApiQuery({ name: 'type', enum: CampaignType, required: false, description: 'Filter by campaign type' })
  @ApiQuery({ name: 'featured', type: Boolean, required: false, description: 'Filter by featured campaigns' })
  @ApiQuery({ name: 'tags', type: [String], required: false, description: 'Filter by tags' })
  @ApiQuery({ name: 'search', type: String, required: false, description: 'Search in name and description' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiResponse({ status: 200, description: 'Campaigns retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getCampaigns(
    @Query('status') status: CampaignStatus | undefined,
    @Query('type') type: CampaignType | undefined,
    @Query('featured') featured: boolean | undefined,
    @Query('tags') tags: string | string[] | undefined,
    @Query('search') search: string | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Req() request: any
  ) {
    const filters = {
      status,
      type,
      featured,
      tags: Array.isArray(tags) ? tags : tags ? [tags] : undefined,
      search,
      page,
      limit,
    };

    const result = await this.campaignService.getCampaigns(filters, request.context);
    return result;
  }

  @Get(':id')
  @RequireCampaignRead()
  @ApiOperation({ summary: 'Get campaign by ID with full details' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: 200, description: 'Campaign retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getCampaignById(@Param('id') id: string, @Req() request: any) {
    const result = await this.campaignService.getCampaignById(id, request.context);
    return result;
  }

  @Put(':id')
  @RequireCampaignWrite()
  @ApiOperation({ summary: 'Update campaign' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiBody({ type: UpdateCampaignDto })
  @ApiResponse({ status: 200, description: 'Campaign updated successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found', type: ErrorResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input data', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async updateCampaign(
    @Param('id') id: string,
    @Body() updateCampaignDto: UpdateCampaignDto,
    @Req() request: any
  ) {
    // TODO: Implement campaign update
    return {
      success: true,
      message: 'Campaign update not yet implemented',
    };
  }

  @Delete(':id')
  @RequireCampaignDelete()
  @ApiOperation({ summary: 'Delete campaign' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: 200, description: 'Campaign deleted successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async deleteCampaign(@Param('id') id: string, @Req() request: any) {
    // TODO: Implement campaign deletion
    return {
      success: true,
      message: 'Campaign deletion not yet implemented',
    };
  }

  @Post(':id/join')
  @Authenticated()
  @ApiOperation({ summary: 'Join a campaign' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiBody({ type: JoinCampaignDto })
  @ApiResponse({ status: 200, description: 'Successfully joined campaign' })
  @ApiResponse({ status: 400, description: 'Invalid request or already participating', type: ErrorResponseDto })
  @ApiResponse({ status: 404, description: 'Campaign not found', type: ErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async joinCampaign(@Param('id') id: string, @Body() joinCampaignDto: JoinCampaignDto, @Req() request: any) {
    // Override campaignId from URL parameter
    joinCampaignDto.campaignId = id;
    const userId = request.user.sub;
    
    const result = await this.campaignService.joinCampaign(joinCampaignDto, userId, request.context);
    return result;
  }

  @Get(':id/participation')
  @Authenticated()
  @ApiOperation({ summary: 'Get current user participation in campaign' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: 200, description: 'Participation retrieved successfully', type: CampaignParticipationResponseDto })
  @ApiResponse({ status: 404, description: 'Participation not found', type: ErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async getUserParticipation(@Param('id') id: string, @Req() request: any) {
    const userId = request.user.sub;
    const result = await this.campaignService.getUserParticipation(id, userId, request.context);
    return result;
  }

  @Post('participation/:participationId/submit')
  @Authenticated()
  @ApiOperation({ summary: 'Submit requirement for campaign participation' })
  @ApiParam({ name: 'participationId', description: 'Participation ID' })
  @ApiBody({ type: SubmitRequirementDto })
  @ApiResponse({ status: 200, description: 'Requirement submitted successfully' })
  @ApiResponse({ status: 400, description: 'Invalid submission', type: ErrorResponseDto })
  @ApiResponse({ status: 404, description: 'Participation not found', type: ErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async submitRequirement(
    @Param('participationId') participationId: string,
    @Body() submitRequirementDto: SubmitRequirementDto,
    @Req() request: any
  ) {
    // TODO: Implement requirement submission
    return {
      success: true,
      message: 'Requirement submission not yet implemented',
    };
  }

  @Post('submissions/:submissionId/review')
  @RequireCampaignWrite()
  @ApiOperation({ summary: 'Review requirement submission' })
  @ApiParam({ name: 'submissionId', description: 'Submission ID' })
  @ApiBody({ type: ReviewSubmissionDto })
  @ApiResponse({ status: 200, description: 'Submission reviewed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid review', type: ErrorResponseDto })
  @ApiResponse({ status: 404, description: 'Submission not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async reviewSubmission(
    @Param('submissionId') submissionId: string,
    @Body() reviewSubmissionDto: ReviewSubmissionDto,
    @Req() request: any
  ) {
    // TODO: Implement submission review
    return {
      success: true,
      message: 'Submission review not yet implemented',
    };
  }

  @Post('rewards/:rewardId/claim')
  @Authenticated()
  @ApiOperation({ summary: 'Claim earned reward' })
  @ApiParam({ name: 'rewardId', description: 'Reward ID' })
  @ApiBody({ type: ClaimRewardDto })
  @ApiResponse({ status: 200, description: 'Reward claimed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid claim request', type: ErrorResponseDto })
  @ApiResponse({ status: 404, description: 'Reward not found', type: ErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async claimReward(
    @Param('rewardId') rewardId: string,
    @Body() claimRewardDto: ClaimRewardDto,
    @Req() request: any
  ) {
    // TODO: Implement reward claiming
    return {
      success: true,
      message: 'Reward claiming not yet implemented',
    };
  }

  @Get(':id/analytics')
  @RequireAnalyticsRead()
  @ApiOperation({ summary: 'Get campaign analytics and performance metrics' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: 200, description: 'Campaign analytics retrieved successfully', type: CampaignAnalyticsDto })
  @ApiResponse({ status: 404, description: 'Campaign not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getCampaignAnalytics(@Param('id') id: string, @Req() request: any) {
    // TODO: Implement campaign analytics
    return {
      success: true,
      message: 'Campaign analytics not yet implemented',
    };
  }

  @Get(':id/participants')
  @RequireCampaignRead()
  @ApiOperation({ summary: 'Get campaign participants with pagination' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiQuery({ name: 'status', type: String, required: false, description: 'Filter by participation status' })
  @ApiResponse({ status: 200, description: 'Participants retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found', type: ErrorResponseDto })
  @ApiResponse({ status: 403, description: 'Insufficient permissions', type: ErrorResponseDto })
  async getCampaignParticipants(
    @Param('id') id: string,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Query('status') status: string | undefined,
    @Req() request: any
  ) {
    // TODO: Implement get campaign participants
    return {
      success: true,
      message: 'Get campaign participants not yet implemented',
    };
  }

  @Get('my/participations')
  @Authenticated()
  @ApiOperation({ summary: 'Get current user campaign participations' })
  @ApiQuery({ name: 'status', type: String, required: false, description: 'Filter by participation status' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiResponse({ status: 200, description: 'User participations retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required', type: ErrorResponseDto })
  async getMyParticipations(
    @Query('status') status: string | undefined,
    @Query('page') page: number | undefined,
    @Query('limit') limit: number | undefined,
    @Req() request: any
  ) {
    // TODO: Implement get user participations
    return {
      success: true,
      message: 'Get user participations not yet implemented',
    };
  }
}
