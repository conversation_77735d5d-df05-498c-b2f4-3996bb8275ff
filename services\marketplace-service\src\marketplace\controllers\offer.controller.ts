import { Controller, Get, Post, Param, Body, Res, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { OfferManagementService } from '../services/offer-management.service';

@ApiTags('Offers')
@Controller('offers')
export class OfferController {
  private readonly logger = new Logger(OfferController.name);

  constructor(private readonly offerManagementService: OfferManagementService) {}

  @Post()
  @ApiOperation({ summary: 'Make offer', description: 'Make an offer on an NFT' })
  @ApiBody({
    description: 'Offer data',
    schema: {
      type: 'object',
      properties: {
        nftId: { type: 'string', example: 'nft-12345' },
        amount: { type: 'string', example: '1.2' },
        currency: { type: 'string', example: 'ETH' },
        expiresIn: { type: 'number', example: 86400 }
      },
      required: ['nftId', 'amount', 'currency']
    }
  })
  @ApiResponse({ status: 201, description: 'Offer created successfully' })
  async makeOffer(@Body() offerData: any, @Res() res: Response) {
    try {
      this.logger.log('Creating new offer');
      const result = await this.offerManagementService.makeOffer(offerData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Offer creation failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('nft/:nftId')
  @ApiOperation({ summary: 'Get NFT offers', description: 'Get all offers for a specific NFT' })
  @ApiParam({ name: 'nftId', description: 'NFT ID' })
  @ApiResponse({ status: 200, description: 'Offers retrieved successfully' })
  async getNFTOffers(@Param('nftId') nftId: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting offers for NFT: ${nftId}`);
      const result = await this.offerManagementService.getNFTOffers(nftId);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Offers retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
