export enum ListingType {
  FIXED_PRICE = 'fixed_price',
  AUCTION = 'auction',
  DUTCH_AUCTION = 'dutch_auction'
}

export enum ListingStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  SOLD = 'sold',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired'
}

export enum OfferStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum TransactionType {
  PURCHASE = 'purchase',
  OFFER_ACCEPTANCE = 'offer_acceptance',
  AUCTION_WIN = 'auction_win'
}

export enum PaymentMethod {
  ETH = 'ETH',
  MATIC = 'MATIC',
  USDC = 'USDC',
  USDT = 'USDT'
}

export interface MarketplaceListing {
  id: string
  nftId: string
  sellerId: string
  sellerAddress: string
  sellerUsername?: string
  sellerAvatar?: string
  listingType: ListingType
  status: ListingStatus
  price: number
  currency: PaymentMethod
  startingPrice?: number
  reservePrice?: number
  buyNowPrice?: number
  title: string
  description: string
  tags: string[]
  acceptOffers: boolean
  minOfferAmount?: number
  auctionEndTime?: string
  externalListingId?: string
  marketplaceUrl?: string
  viewCount: number
  favoriteCount: number
  offerCount: number
  createdAt: string
  updatedAt: string
  expiresAt?: string
  
  // NFT Details
  nft?: {
    id: string
    name: string
    imageUrl: string
    rarity: string
    currentScore: number
    blockchain: string
    tokenId?: string
    contractAddress?: string
    metadata?: any
  }
  
  // Pricing Analytics
  priceHistory?: Array<{
    price: number
    currency: PaymentMethod
    date: string
    eventType: 'listed' | 'price_change' | 'offer' | 'sale'
  }>
  
  // Market Data
  marketData?: {
    floorPrice: number
    lastSalePrice?: number
    averagePrice: number
    priceChange24h: number
    volume24h: number
  }
}

export interface MarketplaceOffer {
  id: string
  listingId: string
  nftId: string
  buyerId: string
  buyerAddress: string
  buyerUsername?: string
  buyerAvatar?: string
  amount: number
  currency: PaymentMethod
  status: OfferStatus
  message?: string
  expiresAt: string
  createdAt: string
  updatedAt: string
  
  // Listing Details
  listing?: {
    id: string
    title: string
    price: number
    currency: PaymentMethod
    nft?: {
      name: string
      imageUrl: string
      rarity: string
    }
  }
}

export interface MarketplaceTransaction {
  id: string
  listingId: string
  nftId: string
  buyerId: string
  sellerId: string
  buyerAddress: string
  sellerAddress: string
  amount: number
  currency: PaymentMethod
  platformFee: number
  royaltyFee: number
  sellerAmount: number
  status: TransactionStatus
  transactionType: TransactionType
  blockchainTxHash?: string
  blockNumber?: number
  gasUsed?: number
  gasFee?: number
  createdAt: string
  updatedAt: string
  completedAt?: string
  
  // Related Data
  nft?: {
    name: string
    imageUrl: string
    rarity: string
    tokenId?: string
  }
  
  buyer?: {
    username: string
    avatar?: string
  }
  
  seller?: {
    username: string
    avatar?: string
  }
}

export interface CreateListingRequest {
  nftId: string
  listingType: ListingType
  price: number
  currency: PaymentMethod
  title: string
  description?: string
  tags?: string[]
  acceptOffers?: boolean
  minOfferAmount?: number
  auctionEndTime?: string
  startingPrice?: number
  reservePrice?: number
  buyNowPrice?: number
}

export interface UpdateListingRequest {
  price?: number
  currency?: PaymentMethod
  title?: string
  description?: string
  tags?: string[]
  acceptOffers?: boolean
  minOfferAmount?: number
  auctionEndTime?: string
}

export interface CreateOfferRequest {
  listingId: string
  amount: number
  currency: PaymentMethod
  message?: string
  expiresAt: string
}

export interface PurchaseRequest {
  listingId: string
  paymentMethod: PaymentMethod
  paymentTxHash?: string
}

export interface MarketplaceFilters {
  search?: string
  minPrice?: number
  maxPrice?: number
  currency?: PaymentMethod[]
  listingType?: ListingType[]
  status?: ListingStatus[]
  rarity?: string[]
  blockchain?: string[]
  sellerId?: string
  tags?: string[]
  sortBy?: 'price' | 'created_at' | 'ending_soon' | 'most_viewed' | 'most_offers'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface MarketplaceAnalytics {
  totalListings: number
  activeListings: number
  totalSales: number
  totalVolume: number
  averagePrice: number
  floorPrice: number
  ceilingPrice: number
  priceChange24h: number
  volumeChange24h: number
  
  // Time Series Data
  priceHistory: Array<{
    date: string
    averagePrice: number
    volume: number
    sales: number
  }>
  
  // Distribution Data
  priceDistribution: Array<{
    range: string
    count: number
    percentage: number
  }>
  
  rarityDistribution: Array<{
    rarity: string
    count: number
    averagePrice: number
    totalVolume: number
  }>
  
  // Top Performers
  topSellers: Array<{
    userId: string
    username: string
    avatar?: string
    salesCount: number
    totalVolume: number
  }>
  
  topBuyers: Array<{
    userId: string
    username: string
    avatar?: string
    purchaseCount: number
    totalSpent: number
  }>
  
  // Recent Activity
  recentSales: Array<{
    nftName: string
    price: number
    currency: PaymentMethod
    buyer: string
    seller: string
    date: string
  }>
}

export interface UserMarketplaceData {
  // User's Listings
  activeListings: MarketplaceListing[]
  soldListings: MarketplaceListing[]
  
  // User's Offers
  madeOffers: MarketplaceOffer[]
  receivedOffers: MarketplaceOffer[]
  
  // User's Transactions
  purchases: MarketplaceTransaction[]
  sales: MarketplaceTransaction[]
  
  // User Stats
  stats: {
    totalSales: number
    totalPurchases: number
    totalVolumeSold: number
    totalVolumeSpent: number
    averageSalePrice: number
    averagePurchasePrice: number
    successfulOffers: number
    totalOffersMade: number
    totalOffersReceived: number
  }
}

export interface MarketplaceNotification {
  id: string
  userId: string
  type: 'offer_received' | 'offer_accepted' | 'offer_rejected' | 'listing_sold' | 'auction_ending' | 'price_drop'
  title: string
  message: string
  data: {
    listingId?: string
    offerId?: string
    transactionId?: string
    nftId?: string
    amount?: number
    currency?: PaymentMethod
  }
  read: boolean
  createdAt: string
}
