'use client'

import React, { useState } from 'react'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  CalendarIcon,
  StarIcon
} from '@heroicons/react/24/outline'
import { CampaignStatus, CampaignType } from '@/types/campaign.types'

interface CampaignFiltersProps {
  filters: {
    search: string
    status: CampaignStatus[]
    type: CampaignType[]
    featured?: boolean
    sortBy: string
    sortOrder: 'asc' | 'desc'
  }
  onFiltersChange: (filters: any) => void
  className?: string
}

export default function CampaignFilters({
  filters,
  onFiltersChange,
  className = ''
}: CampaignFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const statusOptions = [
    { value: CampaignStatus.ACTIVE, label: 'Active', color: 'text-green-600 bg-green-100' },
    { value: CampaignStatus.DRAFT, label: 'Draft', color: 'text-gray-600 bg-gray-100' },
    { value: CampaignStatus.PAUSED, label: 'Paused', color: 'text-yellow-600 bg-yellow-100' },
    { value: CampaignStatus.SCHEDULED, label: 'Scheduled', color: 'text-purple-600 bg-purple-100' },
    { value: CampaignStatus.COMPLETED, label: 'Completed', color: 'text-blue-600 bg-blue-100' },
    { value: CampaignStatus.CANCELLED, label: 'Cancelled', color: 'text-red-600 bg-red-100' }
  ]

  const typeOptions = [
    { value: CampaignType.SOCIAL_ENGAGEMENT, label: 'Social Engagement', icon: '📱' },
    { value: CampaignType.CONTENT_CREATION, label: 'Content Creation', icon: '🎨' },
    { value: CampaignType.COMMUNITY_BUILDING, label: 'Community Building', icon: '👥' },
    { value: CampaignType.TRADING_ACTIVITY, label: 'Trading Activity', icon: '💰' },
    { value: CampaignType.REFERRAL_PROGRAM, label: 'Referral Program', icon: '🔗' },
    { value: CampaignType.MILESTONE_ACHIEVEMENT, label: 'Milestone Achievement', icon: '🏆' },
    { value: CampaignType.SEASONAL_EVENT, label: 'Seasonal Event', icon: '🎉' },
    { value: CampaignType.BRAND_COLLABORATION, label: 'Brand Collaboration', icon: '🤝' }
  ]

  const updateFilters = (updates: any) => {
    onFiltersChange({ ...filters, ...updates })
  }

  const toggleStatus = (status: CampaignStatus) => {
    const newStatus = filters.status.includes(status)
      ? filters.status.filter(s => s !== status)
      : [...filters.status, status]
    updateFilters({ status: newStatus })
  }

  const toggleType = (type: CampaignType) => {
    const newTypes = filters.type.includes(type)
      ? filters.type.filter(t => t !== type)
      : [...filters.type, type]
    updateFilters({ type: newTypes })
  }

  const clearAllFilters = () => {
    updateFilters({
      search: '',
      status: [],
      type: [],
      featured: undefined
    })
  }

  const hasActiveFilters = filters.search || filters.status.length > 0 || filters.type.length > 0 || filters.featured !== undefined

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Quick Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search */}
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            value={filters.search}
            onChange={(e) => updateFilters({ search: e.target.value })}
            placeholder="Search campaigns..."
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Featured Toggle */}
        <button
          onClick={() => updateFilters({ featured: filters.featured === true ? undefined : true })}
          className={`inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
            filters.featured === true
              ? 'border-yellow-300 bg-yellow-100 text-yellow-700'
              : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
          }`}
        >
          <StarIcon className="h-4 w-4 mr-2" />
          Featured
        </button>

        {/* Advanced Filters Toggle */}
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={`inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
            showAdvanced
              ? 'border-blue-300 bg-blue-100 text-blue-700'
              : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
          }`}
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          Filters
          {hasActiveFilters && (
            <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
              {filters.status.length + filters.type.length + (filters.featured ? 1 : 0)}
            </span>
          )}
        </button>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <XMarkIcon className="h-4 w-4 mr-2" />
            Clear
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <div className="space-y-4">
            {/* Status Filters */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Status
              </label>
              <div className="flex flex-wrap gap-2">
                {statusOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => toggleStatus(option.value)}
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border transition-colors ${
                      filters.status.includes(option.value)
                        ? `${option.color} border-current`
                        : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {option.label}
                    {filters.status.includes(option.value) && (
                      <XMarkIcon className="h-3 w-3 ml-1" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Type Filters */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Type
              </label>
              <div className="flex flex-wrap gap-2">
                {typeOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => toggleType(option.value)}
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border transition-colors ${
                      filters.type.includes(option.value)
                        ? 'border-blue-300 bg-blue-100 text-blue-700'
                        : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <span className="mr-1">{option.icon}</span>
                    {option.label}
                    {filters.type.includes(option.value) && (
                      <XMarkIcon className="h-3 w-3 ml-1" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Date Range Filters */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quick Date Filters
              </label>
              <div className="flex flex-wrap gap-2">
                <button className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                  <CalendarIcon className="h-3 w-3 mr-1" />
                  Starting This Week
                </button>
                <button className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                  <CalendarIcon className="h-3 w-3 mr-1" />
                  Ending Soon
                </button>
                <button className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                  <CalendarIcon className="h-3 w-3 mr-1" />
                  Recently Created
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="flex flex-wrap items-center gap-2 text-sm">
          <span className="text-gray-600">Active filters:</span>
          
          {filters.search && (
            <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-700">
              Search: "{filters.search}"
              <button
                onClick={() => updateFilters({ search: '' })}
                className="ml-1 text-blue-500 hover:text-blue-700"
              >
                <XMarkIcon className="h-3 w-3" />
              </button>
            </span>
          )}

          {filters.status.map((status) => {
            const option = statusOptions.find(opt => opt.value === status)
            return (
              <span
                key={status}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${option?.color}`}
              >
                {option?.label}
                <button
                  onClick={() => toggleStatus(status)}
                  className="ml-1 hover:text-current"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )
          })}

          {filters.type.map((type) => {
            const option = typeOptions.find(opt => opt.value === type)
            return (
              <span
                key={type}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700"
              >
                <span className="mr-1">{option?.icon}</span>
                {option?.label}
                <button
                  onClick={() => toggleType(type)}
                  className="ml-1 text-blue-500 hover:text-blue-700"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )
          })}

          {filters.featured && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700">
              <StarIcon className="h-3 w-3 mr-1" />
              Featured
              <button
                onClick={() => updateFilters({ featured: undefined })}
                className="ml-1 text-yellow-500 hover:text-yellow-700"
              >
                <XMarkIcon className="h-3 w-3" />
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  )
}
