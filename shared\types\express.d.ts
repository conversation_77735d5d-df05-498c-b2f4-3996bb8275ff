/**
 * Express Request Extensions
 */

declare global {
  namespace Express {
    interface Request {
      correlationId?: string;
      startTime?: number;
      session?: {
        sessionId?: string;
        userId?: string;
        [key: string]: any;
      };
      user?: {
        id: string;
        email?: string;
        roles?: string[];
        permissions?: string[];
        [key: string]: any;
      };
    }
  }
}

export {};
