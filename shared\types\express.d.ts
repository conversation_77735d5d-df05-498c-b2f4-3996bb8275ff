/**
 * Express Request Extensions
 */

declare global {
  namespace Express {
    interface Request {
      correlationId?: string;
      startTime?: number;
      tokenInfo?: any;
      serviceContext?: any;
      session?: {
        sessionId?: string;
        userId?: string;
        [key: string]: any;
      };
      user?: {
        id: string;
        email?: string;
        roles?: string[];
        permissions?: string[];
        isActive?: boolean;
        isVerified?: boolean;
        [key: string]: any;
      };
    }
  }
}

export {};
