'use client'

import React, { useState } from 'react'
import {
  ChatBubbleOvalLeftIcon,
  HeartIcon,
  ReplyIcon,
  EllipsisHorizontalIcon,
  FlagIcon,
  TrashIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid'
import {
  usePostComments,
  useCreateComment,
  useLikeComment,
  useUnlikeComment,
  useDeleteComment,
  useUpdateComment
} from '@/hooks/useSocial'
import { SocialComment, CreateCommentRequest } from '@/types/social.types'

interface PostCommentsProps {
  postId: string
  className?: string
}

export default function PostComments({
  postId,
  className = ''
}: PostCommentsProps) {
  const [newComment, setNewComment] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyText, setReplyText] = useState('')
  const [editingComment, setEditingComment] = useState<string | null>(null)
  const [editText, setEditText] = useState('')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest')

  const { data: commentsData, isLoading } = usePostComments(postId, 50, 0, sortBy)
  const createCommentMutation = useCreateComment()
  const likeCommentMutation = useLikeComment()
  const unlikeCommentMutation = useUnlikeComment()
  const deleteCommentMutation = useDeleteComment()
  const updateCommentMutation = useUpdateComment()

  const comments = commentsData?.comments || []

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!newComment.trim()) return

    try {
      const commentRequest: CreateCommentRequest = {
        postId,
        content: newComment.trim(),
        parentCommentId: undefined
      }

      await createCommentMutation.mutateAsync(commentRequest)
      setNewComment('')
    } catch (error) {
      console.error('Failed to create comment:', error)
    }
  }

  const handleSubmitReply = async (parentCommentId: string) => {
    if (!replyText.trim()) return

    try {
      const replyRequest: CreateCommentRequest = {
        postId,
        content: replyText.trim(),
        parentCommentId
      }

      await createCommentMutation.mutateAsync(replyRequest)
      setReplyText('')
      setReplyingTo(null)
    } catch (error) {
      console.error('Failed to create reply:', error)
    }
  }

  const handleLikeComment = (commentId: string, isLiked: boolean) => {
    if (isLiked) {
      unlikeCommentMutation.mutate(commentId)
    } else {
      likeCommentMutation.mutate(commentId)
    }
  }

  const handleDeleteComment = (commentId: string) => {
    if (confirm('Are you sure you want to delete this comment?')) {
      deleteCommentMutation.mutate(commentId)
    }
  }

  const handleEditComment = (comment: SocialComment) => {
    setEditingComment(comment.id)
    setEditText(comment.content)
  }

  const handleSaveEdit = async (commentId: string) => {
    if (!editText.trim()) return

    try {
      await updateCommentMutation.mutateAsync({
        commentId,
        content: editText.trim()
      })
      setEditingComment(null)
      setEditText('')
    } catch (error) {
      console.error('Failed to update comment:', error)
    }
  }

  const handleCancelEdit = () => {
    setEditingComment(null)
    setEditText('')
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  const sortOptions = [
    { value: 'newest', label: 'Newest first' },
    { value: 'oldest', label: 'Oldest first' },
    { value: 'popular', label: 'Most popular' }
  ]

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-start space-x-3 p-4">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <ChatBubbleOvalLeftIcon className="h-5 w-5 mr-2" />
          Comments ({comments.length})
        </h3>
        
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {sortOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* New Comment Form */}
      <form onSubmit={handleSubmitComment} className="space-y-3">
        <div className="flex items-start space-x-3">
          <img
            src="/default-avatar.png" // Current user avatar
            alt="Your avatar"
            className="w-8 h-8 rounded-full"
          />
          <div className="flex-1">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Write a comment..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 resize-none"
            />
            <div className="flex items-center justify-between mt-2">
              <div className="text-xs text-gray-500">
                {newComment.length}/500 characters
              </div>
              <button
                type="submit"
                disabled={!newComment.trim() || createCommentMutation.isPending}
                className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {createCommentMutation.isPending ? 'Posting...' : 'Post Comment'}
              </button>
            </div>
          </div>
        </div>
      </form>

      {/* Comments List */}
      {comments.length > 0 ? (
        <div className="space-y-4">
          {comments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              onLike={(isLiked) => handleLikeComment(comment.id, isLiked)}
              onReply={() => setReplyingTo(comment.id)}
              onEdit={() => handleEditComment(comment)}
              onDelete={() => handleDeleteComment(comment.id)}
              isReplying={replyingTo === comment.id}
              replyText={replyText}
              setReplyText={setReplyText}
              onSubmitReply={() => handleSubmitReply(comment.id)}
              onCancelReply={() => setReplyingTo(null)}
              isEditing={editingComment === comment.id}
              editText={editText}
              setEditText={setEditText}
              onSaveEdit={() => handleSaveEdit(comment.id)}
              onCancelEdit={handleCancelEdit}
              formatTimeAgo={formatTimeAgo}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <ChatBubbleOvalLeftIcon className="mx-auto h-12 w-12 mb-2" />
          <h3 className="text-sm font-medium text-gray-900">No comments yet</h3>
          <p className="text-sm">Be the first to comment on this post!</p>
        </div>
      )}
    </div>
  )
}

interface CommentItemProps {
  comment: SocialComment
  onLike: (isLiked: boolean) => void
  onReply: () => void
  onEdit: () => void
  onDelete: () => void
  isReplying: boolean
  replyText: string
  setReplyText: (text: string) => void
  onSubmitReply: () => void
  onCancelReply: () => void
  isEditing: boolean
  editText: string
  setEditText: (text: string) => void
  onSaveEdit: () => void
  onCancelEdit: () => void
  formatTimeAgo: (date: string) => string
}

function CommentItem({
  comment,
  onLike,
  onReply,
  onEdit,
  onDelete,
  isReplying,
  replyText,
  setReplyText,
  onSubmitReply,
  onCancelReply,
  isEditing,
  editText,
  setEditText,
  onSaveEdit,
  onCancelEdit,
  formatTimeAgo
}: CommentItemProps) {
  const [showReplies, setShowReplies] = useState(false)
  const isCurrentUserComment = comment.author.id === 'current-user-id'

  return (
    <div className="space-y-3">
      {/* Main Comment */}
      <div className="flex items-start space-x-3">
        <img
          src={comment.author.avatar || '/default-avatar.png'}
          alt={comment.author.displayName}
          className="w-8 h-8 rounded-full"
        />
        
        <div className="flex-1">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <h4 className="text-sm font-medium text-gray-900">{comment.author.displayName}</h4>
                <span className="text-xs text-gray-500">@{comment.author.username}</span>
                <span className="text-xs text-gray-500">•</span>
                <span className="text-xs text-gray-500">{formatTimeAgo(comment.createdAt)}</span>
                {comment.isEdited && (
                  <span className="text-xs text-gray-500">(edited)</span>
                )}
              </div>
              
              <div className="flex items-center space-x-1">
                {isCurrentUserComment && (
                  <>
                    <button
                      onClick={onEdit}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={onDelete}
                      className="text-gray-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </>
                )}
                <button className="text-gray-400 hover:text-gray-600">
                  <EllipsisHorizontalIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
            
            {isEditing ? (
              <div className="space-y-2">
                <textarea
                  value={editText}
                  onChange={(e) => setEditText(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 resize-none"
                  rows={2}
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={onSaveEdit}
                    className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <CheckIcon className="h-3 w-3 mr-1" />
                    Save
                  </button>
                  <button
                    onClick={onCancelEdit}
                    className="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <XMarkIcon className="h-3 w-3 mr-1" />
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-sm text-gray-900">{comment.content}</p>
            )}
          </div>
          
          {/* Comment Actions */}
          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
            <button
              onClick={() => onLike(comment.isLikedByCurrentUser)}
              className={`flex items-center space-x-1 hover:text-red-600 ${
                comment.isLikedByCurrentUser ? 'text-red-600' : ''
              }`}
            >
              {comment.isLikedByCurrentUser ? (
                <HeartIconSolid className="h-4 w-4" />
              ) : (
                <HeartIcon className="h-4 w-4" />
              )}
              <span>{comment.likesCount}</span>
            </button>
            
            <button
              onClick={onReply}
              className="flex items-center space-x-1 hover:text-blue-600"
            >
              <ReplyIcon className="h-4 w-4" />
              <span>Reply</span>
            </button>
            
            <button className="flex items-center space-x-1 hover:text-gray-700">
              <FlagIcon className="h-4 w-4" />
              <span>Report</span>
            </button>
          </div>
        </div>
      </div>

      {/* Reply Form */}
      {isReplying && (
        <div className="ml-11 space-y-2">
          <div className="flex items-start space-x-3">
            <img
              src="/default-avatar.png" // Current user avatar
              alt="Your avatar"
              className="w-6 h-6 rounded-full"
            />
            <div className="flex-1">
              <textarea
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder={`Reply to @${comment.author.username}...`}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 resize-none"
              />
              <div className="flex items-center space-x-2 mt-2">
                <button
                  onClick={onSubmitReply}
                  disabled={!replyText.trim()}
                  className="px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  Reply
                </button>
                <button
                  onClick={onCancelReply}
                  className="px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="ml-11">
          <button
            onClick={() => setShowReplies(!showReplies)}
            className="text-xs text-blue-600 hover:text-blue-700 mb-2"
          >
            {showReplies ? 'Hide' : 'Show'} {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}
          </button>
          
          {showReplies && (
            <div className="space-y-3 border-l-2 border-gray-200 pl-4">
              {comment.replies.map((reply) => (
                <div key={reply.id} className="flex items-start space-x-3">
                  <img
                    src={reply.author.avatar || '/default-avatar.png'}
                    alt={reply.author.displayName}
                    className="w-6 h-6 rounded-full"
                  />
                  <div className="flex-1">
                    <div className="bg-gray-50 rounded-lg p-2">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="text-xs font-medium text-gray-900">{reply.author.displayName}</h5>
                        <span className="text-xs text-gray-500">@{reply.author.username}</span>
                        <span className="text-xs text-gray-500">•</span>
                        <span className="text-xs text-gray-500">{formatTimeAgo(reply.createdAt)}</span>
                      </div>
                      <p className="text-xs text-gray-900">{reply.content}</p>
                    </div>
                    
                    <div className="flex items-center space-x-3 mt-1 text-xs text-gray-500">
                      <button
                        className={`flex items-center space-x-1 hover:text-red-600 ${
                          reply.isLikedByCurrentUser ? 'text-red-600' : ''
                        }`}
                      >
                        {reply.isLikedByCurrentUser ? (
                          <HeartIconSolid className="h-3 w-3" />
                        ) : (
                          <HeartIcon className="h-3 w-3" />
                        )}
                        <span>{reply.likesCount}</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
