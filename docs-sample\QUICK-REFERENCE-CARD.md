# 🚀 **QUICK REFERENCE CARD - SOCIAL COMMERCE V2**

## **📋 MANDATORY FIRST STEPS FOR ALL DEVELOPERS & AI AGENTS**

### **🔧 INTELLIGENT MIGRATION & IMPROVEMENT APPROACH**
**This project follows the "KEEP, FIX, IMPROVE, EN<PERSON><PERSON><PERSON>" methodology for V1 to V2 transformation**

---

## 🔍 **BEFORE ANY CODING - READ THESE FILES IN ORDER:**

```bash
1. docs/COMPREHENSIVE-V1-TO-V2-MIGRATION-PLAN.md  # MIGRATION STRATEGY (CRITICAL)
2. docs/DEVELOPMENT-RULES-AND-STANDARDS.md        # DEVELOPMENT RULES (CRITICAL)
3. docs/AI-AGENT-GUIDELINES.md                    # AI AGENT RULES (CRITICAL)
4. docs/IMPLEMENTATION-STATUS.md                  # Current progress & phase
5. README.md                                      # Project overview
6. .env.example                                   # Environment standards
```

## 🔧 **MIGRATION METHODOLOGY - MA<PERSON>ATORY FOR ALL IMPLEMENTATIONS:**

### **KEEP** - Preserve V1 Business Logic
- ✅ **Working business logic** (registration workflows, authentication flows)
- ✅ **Functional behavior** (same inputs → same outputs)
- ✅ **Proven patterns** (successful V1 patterns that deliver business value)

### **FIX** - Resolve V1 Design Issues
- 🚨 **Hardcoded configurations** → Dynamic configuration management
- 🚨 **Basic error handling** → Comprehensive error categorization
- 🚨 **Security vulnerabilities** → Strong authentication and validation

### **IMPROVE** - Enhance Implementation Quality
- 🔄 **Performance optimization** → Caching, query optimization, connection pooling
- 🔄 **Code structure** → Better patterns, organization, maintainability
- 🔄 **Monitoring** → Comprehensive observability and metrics

### **ENHANCE** - Add Enterprise Features
- 🚀 **Advanced security** → MFA, OAuth2, API key management
- 🚀 **Scalability features** → Load balancing, auto-scaling, multi-region
- 🚀 **Real-time features** → WebSocket integration, live notifications

---

## 🏗️ **SHARED INFRASTRUCTURE - ALWAYS USE THESE:**

### **Authentication:**
```typescript
import { StandardizedJwtAuthGuard } from '@shared/auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '@shared/auth/guards/permissions.guard';
import { RequireUserRead, CurrentUser } from '@shared/auth/decorators/auth.decorator';
```

### **Response Handling:**
```typescript
import { ResponseService } from '@shared/responses/services/response.service';
// Use: responseService.success(), responseService.error(), etc.
```

### **Logging:**
```typescript
import { ServiceLoggerService } from '@shared/logging/services/service-logger.service';
// Use: logger.logBusinessEvent(), logger.logPerformanceMetric(), etc.
```

### **Configuration:**
```typescript
import { StandardizedConfigService } from '@shared/config/services/config.service';
// Use: configService.getServiceConfig(), configService.getDatabaseConfig(), etc.
```

### **Data Access:**
```typescript
import { BaseRepository } from '@shared/data/repositories/base.repository';
// Extend BaseRepository for all data access
```

---

## 📁 **MANDATORY SERVICE STRUCTURE:**

```
services/[service-name]/
├── src/
│   ├── features/[entity]/
│   │   ├── controllers/[entity].controller.ts
│   │   ├── services/[entity].service.ts
│   │   ├── repositories/[entity].repository.ts
│   │   ├── dto/create-[entity].dto.ts
│   │   └── entities/[entity].entity.ts
│   ├── health/health.controller.ts
│   ├── app.module.ts
│   └── main.ts
├── package.json
└── Dockerfile
```

---

## ✅ **CORRECT PATTERNS:**

### **Controller:**
```typescript
@Controller('users')
@UseGuards(StandardizedJwtAuthGuard, PermissionsGuard)
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly responseService: ResponseService
  ) {}

  @Get()
  @RequireUserRead()
  async findAll(@CurrentUser() user: AuthenticatedUser) {
    const users = await this.userService.findAll();
    return this.responseService.success(users, 'Users retrieved successfully');
  }
}
```

### **Service:**
```typescript
@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly logger: ServiceLoggerService
  ) {}

  async findAll(): Promise<User[]> {
    const operation = this.logger.startOperation('user_find_all');
    try {
      const users = await this.userRepository.findAll();
      operation.end();
      return users;
    } catch (error) {
      this.logger.error('Failed to find users', error);
      throw error;
    }
  }
}
```

### **Repository:**
```typescript
@Injectable()
export class UserRepository extends BaseRepository<User, CreateUserDto, UpdateUserDto> {
  constructor(
    private readonly prisma: PrismaService,
    logger: ServiceLoggerService
  ) {
    super(logger, 'User');
  }

  protected getModel() {
    return this.prisma.user;
  }

  protected toEntity(data: any): User {
    return new User(data);
  }
}
```

---

## 🚫 **FORBIDDEN PATTERNS:**

```typescript
// ❌ NEVER: Custom authentication
@UseGuards(CustomAuthGuard)

// ❌ NEVER: Custom response format
return { data: users };

// ❌ NEVER: Console.log
console.log('Debug message');

// ❌ NEVER: Direct database access
this.prisma.user.findMany();

// ❌ NEVER: Custom error handling
throw new HttpException('Error', 500);
```

---

## 📋 **MANDATORY MIGRATION CHECKLIST:**

```
🔍 MIGRATION ANALYSIS (REQUIRED FIRST):
□ Read docs/COMPREHENSIVE-V1-TO-V2-MIGRATION-PLAN.md
□ Read docs/DEVELOPMENT-RULES-AND-STANDARDS.md
□ Analyzed V1 implementation in ../social-commerce-refined/
□ Identified business logic to KEEP
□ Identified design issues to FIX
□ Identified improvements to IMPROVE
□ Identified enterprise features to ENHANCE
□ Documented migration decisions and rationale

💻 MIGRATION IMPLEMENTATION:
□ Preserves V1 business logic and functional behavior
□ Fixes identified V1 design issues
□ Improves implementation quality over V1
□ Uses V2 shared infrastructure components
□ Follows established V2 patterns
□ Includes proper error handling with ResponseService
□ Has correlation ID tracking in all operations
□ Uses standardized response format
□ Includes comprehensive logging with ServiceLoggerService
□ Adds appropriate enterprise features
□ Includes comprehensive tests

📝 MIGRATION VALIDATION:
□ No business logic regression from V1
□ Improved implementation quality over V1
□ Enterprise features work correctly
□ Updates migration documentation
□ No duplicate functionality
□ No temporary fixes or workarounds
```

---

## 🎯 **QUICK COMMANDS:**

```bash
# Check current status
cat docs/IMPLEMENTATION-STATUS.md

# Review shared infrastructure
ls -la shared/

# Check service structure
ls -la services/

# Run tests
npm test

# Check code quality
npm run lint
```

---

## 🚨 **CRITICAL MIGRATION RULES:**

### **🔧 MIGRATION APPROACH:**
1. **ALWAYS** analyze V1 implementation before starting migration
2. **NEVER** copy-paste V1 code without analysis and improvement
3. **ALWAYS** preserve proven V1 business logic (KEEP)
4. **NEVER** ignore V1 design issues (must FIX)
5. **ALWAYS** improve implementation quality (IMPROVE)
6. **NEVER** lose V1 functionality during migration
7. **ALWAYS** add appropriate enterprise features (ENHANCE)
8. **NEVER** start from scratch without preserving business value

### **🏗️ IMPLEMENTATION QUALITY:**
9. **ALWAYS** use V2 shared infrastructure components
10. **NEVER** create custom patterns without approval
11. **ALWAYS** include correlation ID tracking in all operations
12. **NEVER** bypass authentication/authorization
13. **ALWAYS** use ResponseService for all responses
14. **NEVER** use console.log for logging (use ServiceLoggerService)
15. **ALWAYS** extend BaseRepository for data access
16. **NEVER** access database directly without repositories

### **📝 DOCUMENTATION:**
17. **ALWAYS** document migration decisions and rationale
18. **NEVER** skip migration documentation updates
19. **ALWAYS** update implementation status
20. **NEVER** leave temporary fixes or TODO comments
21. **ALWAYS** commit regularly after each major progress
22. **NEVER** make large commits without incremental progress
23. **ALWAYS** use descriptive commit messages with context
24. **NEVER** use generic commit messages like "fix" or "update"

---

**🚀 REMEMBER: Intelligent Migration = Preserve Value + Improve Quality + Add Enterprise Features!**

**📖 SUCCESS = KEEP what works, FIX what's broken, IMPROVE what's basic, ENHANCE with enterprise features!**
