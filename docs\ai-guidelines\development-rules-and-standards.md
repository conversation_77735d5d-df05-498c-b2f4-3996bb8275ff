# Development Rules and Standards

## 🎯 **Comprehensive Development Rules for Social NFT Platform**

**Version:** 1.0
**Date:** 2025-05-29
**Scope:** All developers and AI agents working on the Social NFT Platform
**Status:** Mandatory compliance required

### **📋 Purpose:**
This document defines mandatory rules and standards for all development and implementation processes on the Social NFT Platform, based on proven best practices and learnings from successful implementations.

### **⚖️ Compliance:**
- **MANDATORY:** All developers and AI agents MUST follow these rules
- **NO EXCEPTIONS:** Rules apply to all development phases and components
- **ENFORCEMENT:** Code reviews must verify compliance with these standards
- **UPDATES:** Rules may only be updated through formal review process

## 🔧 **RULE CATEGORY 1: SYSTEMATIC PROBLEM SOLVING**

### **Rule 1.1: Root Cause Identification Priority**
**MANDATORY REQUIREMENT:** Always identify root cause before implementing any solution

#### **Implementation Standards:**
- ✅ **MUST DO:** Analyze the underlying cause of every issue before fixing
- ✅ **MUST DO:** Document root cause analysis in issue description
- ✅ **MUST DO:** Create analysis tools when needed to understand problems
- ❌ **NEVER DO:** Implement quick fixes without understanding root cause
- ❌ **NEVER DO:** Apply temporary workarounds as permanent solutions

### **Rule 1.2: Template-First Methodology**
**MANDATORY REQUIREMENT:** Use Template-First development approach for all complex operations

#### **Implementation Standards:**
All developers and Ai Agents should read and undrestand Template-First Methodology in twq different erea (read/write files vs vevelopment/implementation) described in below files:

1. **`/docs/methodology/template-first-Solutions-for-Termination-Errors.txt`** - technique for reading and writng files while avoiding termination error
2. **`/docs/methodology/template-first-development-approach.md`** - strategy for systematic development eg creating new service
3. **`/docs/methodology/template-first-vs-service-templating-clarification.md`** Critical clarification to prevent confusion between two different development approaches

- ✅ **MUST DO:** apply TRUE Template-First Approach for file reading/writing strategy
- ✅ **MUST DO:** apply Template-First development for complex development and implementations eg creating new services

### **Both Can Be Used Together**
✅ **Correct**: yes
❌ **Wrong**: Confuse the two or use only one approach

### **Rule 1.3: Comprehensive Analysis Before Action**
**MANDATORY REQUIREMENT:** Understand system state before making changes

#### **Implementation Standards:**
- ✅ **MUST DO:** Create debug and analysis tools when needed
- ✅ **MUST DO:** Verify current system state before implementing changes
- ✅ **MUST DO:** Test solutions in isolated environments first
- ✅ **MUST DO:** Document analysis findings before proceeding
- ❌ **NEVER DO:** Make changes without understanding current state
- ❌ **NEVER DO:** Skip analysis phase to save time

## 🏭 **RULE CATEGORY 2: PRODUCTION-LIKE DEVELOPMENT**

### **Rule 2.1: No Mixing Mock and Real Data**
**MANDATORY REQUIREMENT:** Maintain clear separation between mock and real data

### **Documentation References**
- `docs/methodology/production-like-architecture-guide.md`
- `docs/methodology/production-architecture-quick-reference.md`
- `docs/methodology/production-architecture-checklist.md`

 also so we comoletely solve separation between mock and real data we have implemented Independent Mock Services architecture, you can dind related info here:

- `docs/architecture/Independent-Mock-Services-architecture/mock-real-separation-dialog.md`
- `docs/architecture/Independent-Mock-Services-architecture/mock-services-implementation-guide.md`
- `docs/architecture/Independent-Mock-Services-architecture/approach-3-Independent Mock Services architecture-implementation-summary.md`
- `docs/architecture/Independent-Mock-Services-architecture/approach-3-Independent Mock Services architecture-implementation-guide.md`
- `docs/architecture/Independent-Mock-Services-architecture/approach-3-Independent Mock Services architecture-implementation-checklist.md`
- `docs/architecture/Independent-Mock-Services-architecture/environment-switching-guide-mock-real.md`

#### **Implementation Standards:**
- ✅ **MUST DO:** Treat mock data like production data with proper business rule compliance
- ✅ **MUST DO:** Use single data source per component (either real API or compliant mock)
- ✅ **MUST DO:** Implement graceful fallback from real to mock data
- ✅ **MUST DO:** Log data source clearly in console for debugging
- ❌ **NEVER DO:** Mix localStorage mock data with real authentication
- ❌ **NEVER DO:** Use different data sources simultaneously in same component

### **Rule 2.2: Production API Integration Pattern**
**MANDATORY REQUIREMENT:** Always use API Gateway routing for production-like behavior

#### **Implementation Standards:**
- ✅ **MUST DO:** Route all API calls through API Gateway (port 3010)
- ✅ **MUST DO:** Use correct service endpoints via API Gateway routing
- ✅ **MUST DO:** Implement proper error handling for API failures
- ✅ **MUST DO:** Provide business rule compliant fallback data
- ❌ **NEVER DO:** Call microservices directly bypassing API Gateway
- ❌ **NEVER DO:** Hardcode service ports in frontend components

### **Rule 2.3: Production-Ready Error Handling**
**MANDATORY REQUIREMENT:** Implement comprehensive error handling with graceful fallbacks

#### **Implementation Standards:**
- ✅ **MUST DO:** Use try-catch blocks for all API calls and async operations
- ✅ **MUST DO:** Log errors with clear context and error source identification
- ✅ **MUST DO:** Provide user-friendly error messages and fallback functionality
- ✅ **MUST DO:** Implement graceful degradation when services are unavailable
- ❌ **NEVER DO:** Let unhandled errors crash components or break user experience
- ❌ **NEVER DO:** Show technical error messages to end users

## ⚖️ **RULE CATEGORY 3: BUSINESS RULE ENFORCEMENT**

### **Rule 3.1: Consistent Business Rule Validation**
**MANDATORY REQUIREMENT:** Enforce platform business rules consistently across all layers

#### **Implementation Standards:**
- ✅ **MUST DO:** Validate "One NFT Per Campaign" rule in both frontend and backend
- ✅ **MUST DO:** Implement business rule compliance in all mock data
- ✅ **MUST DO:** Create validation functions for critical business rules
- ✅ **MUST DO:** Display appropriate warnings when business rules are violated
- ❌ **NEVER DO:** Allow business rule violations in any data source
- ❌ **NEVER DO:** Skip business rule validation for development convenience

## 📚 **RULE CATEGORY 4: IMPLEMENT-FIRST DEVELOPMENT**

### **Rule 4.1: Implementation-First Methodology**
**MANDATORY REQUIREMENT:** Implement working solutions first, then document successful implementations

#### **Implementation Standards:**
- ✅ **MUST DO:** Focus on creating working implementations before extensive documentation
- ✅ **MUST DO:** Test and verify functionality before documenting
- ✅ **MUST DO:** Document only proven, working solutions
- ✅ **MUST DO:** Create concise documentation after successful implementation
- ❌ **NEVER DO:** Create extensive documentation before implementation
- ❌ **NEVER DO:** Document theoretical solutions without testing

### **Rule 4.2: Working Implementation Documentation**
**MANDATORY REQUIREMENT:** Document only after successful implementation and testing

#### **Implementation Standards:**
- ✅ **MUST DO:** Document working implementations with proven results
- ✅ **MUST DO:** Include actual code examples from successful implementations
- ✅ **MUST DO:** Document issues only after they are resolved
- ✅ **MUST DO:** Focus on practical, actionable documentation
- ❌ **NEVER DO:** Document incomplete or untested implementations
- ❌ **NEVER DO:** Create documentation that slows down development progress

## 🧩 **RULE CATEGORY 5: COMPONENT AND CODE QUALITY**

### **Rule 5.1: Framework Compatibility Management**
**MANDATORY REQUIREMENT:** Ensure all components are compatible with current framework versions

#### **Implementation Standards:**
- ✅ **MUST DO:** Create compatibility matrix for all UI framework components
- ✅ **MUST DO:** Implement custom alternatives for unsupported components
- ✅ **MUST DO:** Test component compatibility before implementation
- ✅ **MUST DO:** Document component alternatives and migration patterns
- ❌ **NEVER DO:** Use components without verifying framework compatibility
- ❌ **NEVER DO:** Ignore deprecation warnings or compatibility issues

### **Rule 5.2: Consistent Code Patterns**
**MANDATORY REQUIREMENT:** Follow established patterns and conventions consistently

#### **Implementation Standards:**
- ✅ **MUST DO:** Use established patterns from existing working implementations
- ✅ **MUST DO:** Follow naming conventions and file structure standards
- ✅ **MUST DO:** Implement consistent error handling patterns across components
- ✅ **MUST DO:** Use TypeScript types and interfaces consistently
- ❌ **NEVER DO:** Create new patterns without documenting and justifying them
- ❌ **NEVER DO:** Mix different coding styles within the same project

## 🛡️ **RULE CATEGORY 6: ENFORCEMENT AND COMPLIANCE**

### **Rule 6.1: Mandatory Compliance Verification**
**MANDATORY REQUIREMENT:** All code must be verified for compliance before integration

#### **Implementation Standards:**
- ✅ **MUST DO:** Review all code changes against these development rules
- ✅ **MUST DO:** Verify business rule compliance in all implementations
- ✅ **MUST DO:** Test error handling and fallback mechanisms
- ✅ **MUST DO:** Confirm documentation is complete before marking tasks done
- ❌ **NEVER DO:** Merge code without compliance verification
- ❌ **NEVER DO:** Skip rule verification for "urgent" or "small" changes

### **Rule 6.2: AI Agent Compliance**
**MANDATORY REQUIREMENT:** All AI agents must follow these rules without exception

#### **Implementation Standards:**
- ✅ **MUST DO:** Read and acknowledge these rules before starting any development task
- ✅ **MUST DO:** Apply Template-First methodology for all complex operations
- ✅ **MUST DO:** Document every issue and solution immediately
- ✅ **MUST DO:** Implement production-like behavior in all solutions
- ❌ **NEVER DO:** Deviate from established rules without explicit user permission
- ❌ **NEVER DO:** Use shortcuts that violate systematic problem-solving principles

### ⭐ MANDATORY RULE FOR ALL AI AGENTS
**EVERY issue encountered and resolved MUST be documented:**
1. **Document immediately** when issue is resolved
2. **Create file** in `docs/issues/` directory
3. **create index for created ducuments**
4. **Update this index** with new documentation
5. **Follow format** specified in DEVELOPMENT_DOCUMENTATION_RULE.md
- `docs/ai-guidelines/DEVELOPMENT_DOCUMENTATION_RULE.md`

**This rule is NON-NEGOTIABLE and applies to ALL development work.**

## Documentation Standards

### File Naming
- Use kebab-case for file names
- Include descriptive suffixes (-guide, -results, -overview)
- Maintain consistent structure across documents

### Content Structure
- Start with overview and current status
- Include practical examples and code snippets
- Provide step-by-step procedures
- Document both problems and solutions

### Update Frequency
- Update after each major integration phase
- Document new issues and solutions immediately
- Review and update setup guides with environment changes

### For Updates
- Follow established documentation patterns
- Update index when adding new files
- Maintain consistent formatting and structure

## 📋 **QUICK REFERENCE CHECKLIST**

### **Before Starting Any Development Task:**
- [ ] Read and understand these development rules
- [ ] Identify root cause of any issues before implementing solutions
- [ ] Plan Template-First approach for complex operations
- [ ] Verify current system state and dependencies

### **During Development:**
- [ ] Use API Gateway routing for all API calls
- [ ] Implement proper error handling with graceful fallbacks
- [ ] Ensure business rule compliance in all data
- [ ] Focus on working implementation over documentation

### **Before Completing Any Task:**
- [ ] Verify compliance with all development rules
- [ ] Test error handling and fallback mechanisms
- [ ] Confirm production-like behavior implementation
- [ ] Document only after successful implementation and testing

## 🎯 **RULE SUMMARY**

**These rules ensure:**
- ✅ **Systematic Problem Solving** → Root cause identification and Template-First methodology
- ✅ **Production-Like Development** → No mixing data sources, API Gateway routing
- ✅ **Business Rule Enforcement** → Consistent validation across all layers
- ✅ **Implement-First Development** → Working implementations before documentation
- ✅ **Code Quality** → Framework compatibility and consistent patterns
- ✅ **Compliance** → Mandatory verification and AI agent adherence

**Result: Maintainable, production-ready, well-documented Social NFT Platform** 🚀
