/**
 * Queue Service - Profile Analysis Service V2
 * 
 * Manages analysis queue and processing
 */

import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

interface QueueAnalysisPayload {
  analysisId: string;
  userId: string;
  requestId: string;
  type: string;
  sourceUrl?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class QueueService {
  constructor(private readonly prisma: PrismaService) {}

  async queueAnalysis(payload: QueueAnalysisPayload) {
    const queueItem = await this.prisma.analysisQueue.create({
      data: {
        userId: payload.userId,
        requestId: payload.requestId,
        type: payload.type,
        priority: this.calculatePriority(payload),
        status: 'queued',
        payload: payload as any,
      },
    });

    console.log(`📋 Analysis queued: ${queueItem.id} (Priority: ${queueItem.priority})`);
    return queueItem;
  }

  async getNextQueueItem() {
    // Get highest priority queued item
    const queueItem = await this.prisma.analysisQueue.findFirst({
      where: {
        status: 'queued',
        attempts: { lt: 3 }, // Max 3 attempts
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' },
      ],
    });

    if (queueItem) {
      // Mark as processing
      await this.prisma.analysisQueue.update({
        where: { id: queueItem.id },
        data: {
          status: 'processing',
          startedAt: new Date(),
          attempts: { increment: 1 },
        },
      });
    }

    return queueItem;
  }

  async completeQueueItem(queueId: string) {
    await this.prisma.analysisQueue.update({
      where: { id: queueId },
      data: {
        status: 'completed',
        completedAt: new Date(),
      },
    });

    console.log(`✅ Queue item completed: ${queueId}`);
  }

  async failQueueItem(queueId: string, error: string) {
    const queueItem = await this.prisma.analysisQueue.findUnique({
      where: { id: queueId },
    });

    if (!queueItem) return;

    const shouldRetry = queueItem.attempts < queueItem.maxAttempts;

    await this.prisma.analysisQueue.update({
      where: { id: queueId },
      data: {
        status: shouldRetry ? 'queued' : 'failed',
        lastError: error,
        errorCount: { increment: 1 },
        ...(shouldRetry && { assignedWorker: null, startedAt: null }),
      },
    });

    console.log(`❌ Queue item ${shouldRetry ? 'retried' : 'failed'}: ${queueId} - ${error}`);
  }

  async getQueueStats() {
    const [
      totalQueued,
      processing,
      completed,
      failed,
      avgWaitTime,
    ] = await Promise.all([
      this.prisma.analysisQueue.count({ where: { status: 'queued' } }),
      this.prisma.analysisQueue.count({ where: { status: 'processing' } }),
      this.prisma.analysisQueue.count({ where: { status: 'completed' } }),
      this.prisma.analysisQueue.count({ where: { status: 'failed' } }),
      this.calculateAverageWaitTime(),
    ]);

    return {
      queued: totalQueued,
      processing,
      completed,
      failed,
      averageWaitTime: avgWaitTime,
      timestamp: new Date().toISOString(),
    };
  }

  async cleanupOldQueueItems(olderThanDays: number = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const deleted = await this.prisma.analysisQueue.deleteMany({
      where: {
        status: { in: ['completed', 'failed'] },
        completedAt: { lt: cutoffDate },
      },
    });

    console.log(`🧹 Cleaned up ${deleted.count} old queue items`);
    return deleted.count;
  }

  private calculatePriority(payload: QueueAnalysisPayload): number {
    let priority = 5; // Default priority

    // Increase priority based on analysis type
    const typePriorities = {
      twitter: 7,
      instagram: 6,
      linkedin: 5,
      tiktok: 4,
      youtube: 4,
      general: 3,
    };

    priority = typePriorities[payload.type] || 5;

    // Increase priority based on metadata
    if (payload.metadata?.priority === 'high') {
      priority += 2;
    } else if (payload.metadata?.priority === 'urgent') {
      priority += 3;
    }

    // Cap priority at 10
    return Math.min(priority, 10);
  }

  private async calculateAverageWaitTime(): Promise<number> {
    const completedItems = await this.prisma.analysisQueue.findMany({
      where: {
        status: 'completed',
        startedAt: { not: null },
        completedAt: { not: null },
      },
      select: {
        createdAt: true,
        startedAt: true,
      },
      take: 100, // Last 100 items
      orderBy: { completedAt: 'desc' },
    });

    if (completedItems.length === 0) return 0;

    const totalWaitTime = completedItems.reduce((sum, item) => {
      const waitTime = item.startedAt.getTime() - item.createdAt.getTime();
      return sum + waitTime;
    }, 0);

    return totalWaitTime / completedItems.length; // Average in milliseconds
  }

  async retryFailedItems() {
    const failedItems = await this.prisma.analysisQueue.findMany({
      where: {
        status: 'failed',
        attempts: { lt: 3 },
      },
    });

    for (const item of failedItems) {
      await this.prisma.analysisQueue.update({
        where: { id: item.id },
        data: {
          status: 'queued',
          lastError: null,
          assignedWorker: null,
          startedAt: null,
        },
      });
    }

    console.log(`🔄 Retried ${failedItems.length} failed queue items`);
    return failedItems.length;
  }
}
