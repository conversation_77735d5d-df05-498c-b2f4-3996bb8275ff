import { Controller, Get, Post, Param, Body, Res, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { AuctionManagementService } from '../services/auction-management.service';

@ApiTags('Auctions')
@Controller('auctions')
export class AuctionController {
  private readonly logger = new Logger(AuctionController.name);

  constructor(private readonly auctionManagementService: AuctionManagementService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get auction details', description: 'Get details of a specific auction' })
  @ApiParam({ name: 'id', description: 'Auction ID' })
  @ApiResponse({ status: 200, description: 'Auction details retrieved successfully' })
  async getAuction(@Param('id') id: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting auction details for: ${id}`);
      const result = await this.auctionManagementService.getAuction(id);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Auction retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Post(':id/bid')
  @ApiOperation({ summary: 'Place bid', description: 'Place a bid on an auction' })
  @ApiParam({ name: 'id', description: 'Auction ID' })
  @ApiBody({
    description: 'Bid data',
    schema: {
      type: 'object',
      properties: {
        amount: { type: 'string', example: '2.5' },
        bidderAddress: { type: 'string', example: '0x1234567890123456789012345678901234567890' }
      },
      required: ['amount', 'bidderAddress']
    }
  })
  @ApiResponse({ status: 201, description: 'Bid placed successfully' })
  async placeBid(@Param('id') id: string, @Body() bidData: any, @Res() res: Response) {
    try {
      this.logger.log(`Placing bid on auction: ${id}`);
      const result = await this.auctionManagementService.placeBid(id, bidData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Bid placement failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }
}
