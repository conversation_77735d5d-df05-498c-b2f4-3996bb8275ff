/**
 * Shared Infrastructure Module
 * 
 * Implements the Microservice Chassi<PERSON> - provides standardized infrastructure
 * components for cross-cutting concerns while preserving service independence.
 * 
 * Based on industry best practices from Netflix, Uber, Google, and Amazon.
 */

import { Module, DynamicModule, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { APP_GUARD, APP_INTERCEPTOR, APP_FILTER } from '@nestjs/core';

// Authentication Infrastructure
import { StandardizedJwtAuthGuard, OptionalJwtAuthGuard, ApiKeyAuthGuard } from './auth/guards/jwt-auth.guard';
import { StandardizedRBACGuard } from './auth/guards/rbac.guard';

// Response Infrastructure
import { ResponseTransformInterceptor } from './responses/interceptors/response-transform.interceptor';
import { ResponseService } from './responses/services/response.service';

// Logging Infrastructure
import { ServiceLoggerService } from './logging/services/service-logger.service';
import { LoggingInterceptor } from './logging/interceptors/logging.interceptor';

// Configuration Infrastructure
import { StandardizedConfigService } from './config/services/standardized-config.service';

// Data Infrastructure
import { StandardizedPrismaService } from './data/services/standardized-prisma.service';

// Error Handling
import { GlobalExceptionFilter } from './filters/global-exception.filter';

// Validation
import { ValidationPipe } from '@nestjs/common';

/**
 * Shared Infrastructure Configuration Options
 */
export interface SharedInfrastructureOptions {
  serviceName: string;
  version: string;
  enableAuth?: boolean;
  enableResponseTransform?: boolean;
  enableLogging?: boolean;
  enableDatabase?: boolean;
  enableGlobalErrorHandling?: boolean;
  authServiceProvider?: any;
  customConfigValidation?: any;
}

/**
 * Shared Infrastructure Module
 * 
 * Provides standardized infrastructure components for microservices
 */
@Global()
@Module({})
export class SharedInfrastructureModule {
  /**
   * Configure shared infrastructure for a service
   */
  static forRoot(options: SharedInfrastructureOptions): DynamicModule {
    const {
      serviceName,
      version,
      enableAuth = true,
      enableResponseTransform = true,
      enableLogging = true,
      enableDatabase = true,
      enableGlobalErrorHandling = true,
      authServiceProvider,
      customConfigValidation,
    } = options;

    const providers = [
      // Configuration Service
      {
        provide: StandardizedConfigService,
        useFactory: (configService: ConfigService) => {
          return new StandardizedConfigService(configService, {
            serviceName,
            version,
            customValidation: customConfigValidation,
          });
        },
        inject: [ConfigService],
      },

      // Response Service
      {
        provide: ResponseService,
        useFactory: (configService: StandardizedConfigService) => {
          return new ResponseService(configService);
        },
        inject: [StandardizedConfigService],
      },

      // Logger Service
      {
        provide: ServiceLoggerService,
        useFactory: (configService: StandardizedConfigService) => {
          return new ServiceLoggerService(configService);
        },
        inject: [StandardizedConfigService],
      },
    ];

    const imports = [
      // Base configuration
      ConfigModule.forRoot({
        isGlobal: true,
        envFilePath: ['.env.local', '.env'],
        cache: true,
      }),
    ];

    const exports = [
      StandardizedConfigService,
      ResponseService,
      ServiceLoggerService,
    ];

    // Add JWT Module if authentication is enabled
    if (enableAuth) {
      imports.push(
        JwtModule.registerAsync({
          useFactory: async (configService: StandardizedConfigService) => {
            const jwtConfig = configService.getJWTConfig();
            return {
              secret: jwtConfig.secret,
              signOptions: {
                expiresIn: jwtConfig.expiresIn,
              },
            };
          },
          inject: [StandardizedConfigService],
        }) as any
      );

      // Add authentication providers
      providers.push(
        StandardizedJwtAuthGuard,
        OptionalJwtAuthGuard,
        ApiKeyAuthGuard,
        StandardizedRBACGuard,
      );

      // Add auth service provider if provided
      if (authServiceProvider) {
        providers.push({
          provide: 'AUTH_SERVICE',
          useClass: authServiceProvider,
        });
      }

      exports.push(
        StandardizedJwtAuthGuard,
        OptionalJwtAuthGuard,
        ApiKeyAuthGuard,
        StandardizedRBACGuard,
        JwtModule,
      );
    }

    // Add database service if enabled
    if (enableDatabase) {
      providers.push(StandardizedPrismaService);
      exports.push(StandardizedPrismaService);
    }

    // Add global providers
    const globalProviders = [];

    // Global response transformation
    if (enableResponseTransform) {
      globalProviders.push({
        provide: APP_INTERCEPTOR,
        useFactory: (responseService: ResponseService) => {
          return new ResponseTransformInterceptor(responseService as any);
        },
        inject: [ResponseService],
      });
    }

    // Global logging
    if (enableLogging) {
      globalProviders.push({
        provide: APP_INTERCEPTOR,
        useFactory: (logger: ServiceLoggerService, metricsService: MetricsService, configService: StandardizedConfigService) => {
          return new LoggingInterceptor(logger, metricsService, configService as any);
        },
        inject: [ServiceLoggerService, MetricsService, StandardizedConfigService],

      });
    }

    // Global error handling
    if (enableGlobalErrorHandling) {
      globalProviders.push({
        provide: APP_FILTER,
        useFactory: (responseService: ResponseService, logger: ServiceLoggerService) => {
          return new GlobalExceptionFilter(responseService, logger);
        },
        inject: [ResponseService, ServiceLoggerService],
      });
    }

    // Global authentication guard (if enabled)
    if (enableAuth) {
      globalProviders.push({
        provide: APP_GUARD,
        useClass: StandardizedJwtAuthGuard,
      });

      globalProviders.push({
        provide: APP_GUARD,
        useClass: StandardizedRBACGuard,
      });
    }

    return {
      module: SharedInfrastructureModule,
      imports,
      providers: [...providers, ...globalProviders],
      exports,
    };
  }

  /**
   * Configure shared infrastructure for testing
   */
  static forTesting(options: Partial<SharedInfrastructureOptions> = {}): DynamicModule {
    const testOptions: SharedInfrastructureOptions = {
      serviceName: 'test-service',
      version: '1.0.0',
      enableAuth: false,
      enableResponseTransform: true,
      enableLogging: false,
      enableDatabase: false,
      enableGlobalErrorHandling: true,
      ...options,
    };

    return this.forRoot(testOptions);
  }

  /**
   * Configure minimal shared infrastructure (for lightweight services)
   */
  static forMinimal(serviceName: string, version: string = '1.0.0'): DynamicModule {
    return this.forRoot({
      serviceName,
      version,
      enableAuth: false,
      enableResponseTransform: true,
      enableLogging: true,
      enableDatabase: false,
      enableGlobalErrorHandling: true,
    });
  }

  /**
   * Configure full shared infrastructure (for complete services)
   */
  static forComplete(
    serviceName: string, 
    version: string = '1.0.0',
    authServiceProvider?: any
  ): DynamicModule {
    return this.forRoot({
      serviceName,
      version,
      enableAuth: true,
      enableResponseTransform: true,
      enableLogging: true,
      enableDatabase: true,
      enableGlobalErrorHandling: true,
      authServiceProvider,
    });
  }
}

/**
 * Shared Infrastructure Configuration Helper
 */
export class SharedInfrastructureConfig {
  /**
   * Get default configuration for a service type
   */
  static getConfigForServiceType(serviceType: 'api-gateway' | 'business-service' | 'data-service' | 'utility-service') {
    const baseConfig = {
      enableResponseTransform: true,
      enableLogging: true,
      enableGlobalErrorHandling: true,
    };

    switch (serviceType) {
      case 'api-gateway':
        return {
          ...baseConfig,
          enableAuth: true,
          enableDatabase: false,
        };

      case 'business-service':
        return {
          ...baseConfig,
          enableAuth: true,
          enableDatabase: true,
        };

      case 'data-service':
        return {
          ...baseConfig,
          enableAuth: true,
          enableDatabase: true,
        };

      case 'utility-service':
        return {
          ...baseConfig,
          enableAuth: false,
          enableDatabase: false,
        };

      default:
        return baseConfig;
    }
  }

  /**
   * Validate configuration options
   */
  static validateConfig(options: SharedInfrastructureOptions): void {
    if (!options.serviceName) {
      throw new Error('Service name is required');
    }

    if (!options.version) {
      throw new Error('Service version is required');
    }

    if (options.enableAuth && !options.authServiceProvider) {
      console.warn('Authentication enabled but no auth service provider specified');
    }
  }
}
