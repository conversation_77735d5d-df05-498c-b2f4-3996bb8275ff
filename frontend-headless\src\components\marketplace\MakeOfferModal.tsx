'use client'

import React, { useState, Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon, CurrencyDollarIcon, TagIcon, ClockIcon } from '@heroicons/react/24/outline'
import { MarketplaceListing, CreateOfferRequest, PaymentMethod } from '@/types/marketplace.types'
import { useCreateOffer } from '@/hooks/useMarketplace'

interface MakeOfferModalProps {
  listing: MarketplaceListing | null
  isOpen: boolean
  onClose: () => void
  onSuccess?: (offer: any) => void
}

export default function MakeOfferModal({
  listing,
  isOpen,
  onClose,
  onSuccess
}: MakeOfferModalProps) {
  const [formData, setFormData] = useState({
    amount: '',
    currency: PaymentMethod.ETH,
    message: '',
    expirationDays: 7
  })

  const createOfferMutation = useCreateOffer()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!listing || !formData.amount) {
      return
    }

    try {
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + formData.expirationDays)

      const offerData: CreateOfferRequest = {
        listingId: listing.id,
        amount: parseFloat(formData.amount),
        currency: formData.currency,
        message: formData.message || undefined,
        expiresAt: expiresAt.toISOString()
      }

      const result = await createOfferMutation.mutateAsync(offerData)
      onSuccess?.(result)
      onClose()
      resetForm()
    } catch (error) {
      console.error('Failed to create offer:', error)
    }
  }

  const resetForm = () => {
    setFormData({
      amount: '',
      currency: PaymentMethod.ETH,
      message: '',
      expirationDays: 7
    })
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const getOfferPercentage = () => {
    if (!listing || !formData.amount) return 0
    return ((parseFloat(formData.amount) / listing.price) * 100).toFixed(1)
  }

  const isValidOffer = () => {
    if (!listing || !formData.amount) return false
    const amount = parseFloat(formData.amount)
    return amount > 0 && (!listing.minOfferAmount || amount >= listing.minOfferAmount)
  }

  if (!listing) return null

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                      <TagIcon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <Dialog.Title as="h3" className="text-lg font-semibold text-gray-900">
                        Make an Offer
                      </Dialog.Title>
                      <p className="text-sm text-gray-600">Submit your best offer</p>
                    </div>
                  </div>
                  <button
                    onClick={handleClose}
                    className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="p-6 space-y-6">
                  {/* NFT Preview */}
                  <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <img
                      src={listing.nft?.imageUrl || '/api/placeholder/80/80'}
                      alt={listing.title}
                      className="w-20 h-20 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{listing.title}</h4>
                      <p className="text-sm text-gray-600">by {listing.sellerUsername || 'Unknown'}</p>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-gray-500">
                          Listed for {listing.price} {listing.currency}
                        </p>
                        {listing.minOfferAmount && (
                          <p className="text-xs text-gray-500">
                            Min: {listing.minOfferAmount} {listing.currency}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Offer Amount */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Offer Amount
                      </label>
                      <div className="relative">
                        <CurrencyDollarIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="number"
                          step="0.001"
                          min="0"
                          value={formData.amount}
                          onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                          placeholder="0.00"
                          required
                        />
                      </div>
                      {formData.amount && (
                        <p className="text-xs text-gray-500 mt-1">
                          {getOfferPercentage()}% of listing price
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Currency
                      </label>
                      <select
                        value={formData.currency}
                        onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value as PaymentMethod }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      >
                        {Object.values(PaymentMethod).map(currency => (
                          <option key={currency} value={currency}>{currency}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Validation Messages */}
                  {formData.amount && !isValidOffer() && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-800">
                        {listing.minOfferAmount && parseFloat(formData.amount) < listing.minOfferAmount
                          ? `Minimum offer amount is ${listing.minOfferAmount} ${listing.currency}`
                          : 'Please enter a valid offer amount'
                        }
                      </p>
                    </div>
                  )}

                  {/* Offer Expiration */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Offer Expiration
                    </label>
                    <select
                      value={formData.expirationDays}
                      onChange={(e) => setFormData(prev => ({ ...prev, expirationDays: parseInt(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value={1}>1 day</option>
                      <option value={3}>3 days</option>
                      <option value={7}>7 days</option>
                      <option value={14}>14 days</option>
                      <option value={30}>30 days</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      Your offer will expire on {new Date(Date.now() + formData.expirationDays * 24 * 60 * 60 * 1000).toLocaleDateString()}
                    </p>
                  </div>

                  {/* Message */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Message to Seller (Optional)
                    </label>
                    <textarea
                      value={formData.message}
                      onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      placeholder="Add a personal message to increase your chances..."
                      maxLength={500}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {formData.message.length}/500 characters
                    </p>
                  </div>

                  {/* Offer Summary */}
                  {formData.amount && isValidOffer() && (
                    <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                      <h4 className="font-medium text-purple-900 mb-2">Offer Summary</h4>
                      <div className="space-y-1 text-sm text-purple-800">
                        <div className="flex justify-between">
                          <span>Offer Amount:</span>
                          <span className="font-medium">{formData.amount} {formData.currency}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Percentage of List Price:</span>
                          <span className="font-medium">{getOfferPercentage()}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Expires:</span>
                          <span className="font-medium">
                            {new Date(Date.now() + formData.expirationDays * 24 * 60 * 60 * 1000).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Info */}
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <ClockIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm text-blue-800">
                        <p className="font-medium">How offers work</p>
                        <p>Your offer will be sent to the seller. If accepted, the transaction will be processed automatically. You can cancel your offer anytime before it's accepted.</p>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={handleClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={createOfferMutation.isPending || !isValidOffer()}
                      className="px-6 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {createOfferMutation.isPending ? 'Submitting...' : 'Submit Offer'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
