/**
 * Logging Interfaces
 */

/**
 * Log levels
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose',
}

/**
 * Log context interface
 */
export interface LogContext {
  requestId?: string;
  userId?: string;
  sessionId?: string;
  correlationId?: string;
  traceId?: string;
  spanId?: string;
  service?: string;
  version?: string;
  environment?: string;
  timestamp?: string;
  metadata?: Record<string, any>;
}

/**
 * Business context interface
 */
export interface BusinessContext {
  domain: string;
  entity: string;
  entityId?: string;
  action: string;
  outcome: BusinessOutcome;
  attributes?: Record<string, any>;
  metadata?: Record<string, any>;
}

/**
 * Business outcome enum
 */
export enum BusinessOutcome {
  SUCCESS = 'success',
  FAILURE = 'failure',
  PARTIAL = 'partial',
  PENDING = 'pending',
  CANCELLED = 'cancelled',
}

/**
 * Log entry interface
 */
export interface LogEntry {
  level: LogLevel;
  message: string;
  context?: LogContext;
  business?: BusinessContext;
  error?: Error | string;
  timestamp: string;
  service: string;
  version: string;
}

/**
 * Logger configuration interface
 */
export interface LoggerConfiguration {
  level: LogLevel;
  format: 'json' | 'simple' | 'combined';
  transports: LoggerTransport[];
  enableConsole: boolean;
  enableFile: boolean;
  enableRemote: boolean;
  metadata: Record<string, any>;
}

/**
 * Logger transport interface
 */
export interface LoggerTransport {
  type: 'console' | 'file' | 'http' | 'database';
  level?: LogLevel;
  format?: string;
  options?: Record<string, any>;
}

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  duration: number;
  memory: {
    used: number;
    total: number;
  };
  cpu: {
    user: number;
    system: number;
  };
  timestamp: string;
}

/**
 * Audit log interface
 */
export interface AuditLog extends LogEntry {
  actor: {
    id: string;
    type: 'user' | 'system' | 'service';
    name?: string;
  };
  resource: {
    type: string;
    id: string;
    attributes?: Record<string, any>;
  };
  action: string;
  outcome: BusinessOutcome;
  changes?: Array<{
    field: string;
    oldValue: any;
    newValue: any;
  }>;
}

/**
 * Security log interface
 */
export interface SecurityLog extends LogEntry {
  event: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: {
    ip?: string;
    userAgent?: string;
    userId?: string;
  };
  target?: {
    resource: string;
    action: string;
  };
  outcome: 'success' | 'failure' | 'blocked';
}

/**
 * Error log interface
 */
export interface ErrorLog extends LogEntry {
  error: {
    name: string;
    message: string;
    stack?: string;
    code?: string | number;
  };
  request?: {
    method: string;
    url: string;
    headers?: Record<string, string>;
    body?: any;
  };
  response?: {
    statusCode: number;
    headers?: Record<string, string>;
  };
}
