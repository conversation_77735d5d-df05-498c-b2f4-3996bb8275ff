export enum MarketplaceStatus {
  DRAFT = 'draft',
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  LIVE = 'live',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ARCHIVED = 'archived'
}

export enum ListingType {
  CAMPAIGN = 'campaign',
  NFT_COLLECTION = 'nft_collection',
  INDIVIDUAL_NFT = 'individual_nft',
  BUNDLE = 'bundle',
  AUCTION = 'auction',
  FIXED_PRICE = 'fixed_price'
}

export enum MarketplaceCategory {
  ART = 'art',
  GAMING = 'gaming',
  MUSIC = 'music',
  SPORTS = 'sports',
  COLLECTIBLES = 'collectibles',
  UTILITY = 'utility',
  METAVERSE = 'metaverse',
  PHOTOGRAPHY = 'photography',
  MEMES = 'memes',
  PFPS = 'pfps'
}

export enum SortOption {
  NEWEST = 'newest',
  OLDEST = 'oldest',
  PRICE_LOW_HIGH = 'price_low_high',
  PRICE_HIGH_LOW = 'price_high_low',
  MOST_POPULAR = 'most_popular',
  ENDING_SOON = 'ending_soon',
  RECENTLY_SOLD = 'recently_sold',
  MOST_VIEWED = 'most_viewed'
}

export enum BulkOperationType {
  UPDATE_PRICING = 'update_pricing',
  CHANGE_STATUS = 'change_status',
  UPDATE_METADATA = 'update_metadata',
  TRANSFER_OWNERSHIP = 'transfer_ownership',
  BATCH_LIST = 'batch_list',
  BATCH_DELIST = 'batch_delist',
  UPDATE_ROYALTIES = 'update_royalties',
  BULK_APPROVE = 'bulk_approve',
  BULK_REJECT = 'bulk_reject'
}

export interface CampaignMarketplaceListing {
  id: string
  campaignId: string
  campaignName: string
  
  // Listing Details
  title: string
  description: string
  category: MarketplaceCategory
  tags: string[]
  
  // Pricing
  basePrice: number
  currency: string
  royaltyPercentage: number
  creatorRoyalty: number
  
  // Status and Visibility
  status: MarketplaceStatus
  isPublic: boolean
  isFeatured: boolean
  
  // Marketplace Metadata
  listingType: ListingType
  totalSupply: number
  availableSupply: number
  soldCount: number
  
  // Media and Assets
  coverImage: string
  previewImages: string[]
  bannerImage?: string
  
  // Campaign Integration
  campaignData: CampaignMarketplaceData
  nftCollections: NFTCollectionListing[]
  
  // Performance Metrics
  views: number
  favorites: number
  shares: number
  
  // Timestamps
  createdAt: string
  updatedAt: string
  publishedAt?: string
  endDate?: string
  
  // Creator Information
  creatorId: string
  creatorName: string
  creatorAvatar?: string
  creatorVerified: boolean
}

export interface CampaignMarketplaceData {
  // Campaign Overview
  participantCount: number
  completionRate: number
  averageQuality: number
  
  // Generation Stats
  totalNFTsGenerated: number
  uniqueStyles: number
  rarityDistribution: Record<string, number>
  
  // Engagement Metrics
  socialShares: number
  communitySize: number
  engagementRate: number
  
  // Rewards and Incentives
  totalRewardsDistributed: number
  averageRewardValue: number
  bonusRewards: BonusReward[]
  
  // Timeline
  campaignDuration: number
  timeRemaining?: number
  milestones: CampaignMilestone[]
}

export interface NFTCollectionListing {
  id: string
  campaignId: string
  collectionName: string
  
  // Collection Details
  description: string
  totalSupply: number
  mintedCount: number
  floorPrice: number
  
  // Rarity and Traits
  rarityLevels: RarityLevel[]
  traitCategories: TraitCategory[]
  
  // Performance
  totalVolume: number
  averagePrice: number
  priceChange24h: number
  
  // Media
  collectionImage: string
  sampleNFTs: NFTPreview[]
  
  // Status
  status: MarketplaceStatus
  isVerified: boolean
  
  // Timestamps
  createdAt: string
  lastSaleAt?: string
}

export interface BonusReward {
  id: string
  name: string
  description: string
  value: number
  currency: string
  criteria: string
  claimedCount: number
  totalAvailable: number
}

export interface CampaignMilestone {
  id: string
  name: string
  description: string
  targetValue: number
  currentValue: number
  isCompleted: boolean
  completedAt?: string
  reward?: string
}

export interface RarityLevel {
  level: string
  percentage: number
  count: number
  floorPrice: number
  traits: string[]
}

export interface TraitCategory {
  category: string
  traits: TraitValue[]
  rarity: number
}

export interface TraitValue {
  value: string
  count: number
  percentage: number
  floorPrice: number
}

export interface NFTPreview {
  id: string
  tokenId: string
  name: string
  image: string
  rarity: string
  price?: number
  lastSale?: number
}

export interface MarketplaceFilters {
  // Basic Filters
  category?: MarketplaceCategory[]
  status?: MarketplaceStatus[]
  listingType?: ListingType[]
  
  // Price Filters
  minPrice?: number
  maxPrice?: number
  currency?: string
  
  // Campaign Filters
  minParticipants?: number
  maxParticipants?: number
  completionRate?: number
  qualityScore?: number
  
  // Feature Filters
  isFeatured?: boolean
  isVerified?: boolean
  hasRoyalties?: boolean
  
  // Date Filters
  createdAfter?: string
  createdBefore?: string
  endingAfter?: string
  endingBefore?: string
  
  // Search
  searchQuery?: string
  tags?: string[]
  
  // Sorting and Pagination
  sortBy?: SortOption
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface BulkOperation {
  id: string
  type: BulkOperationType
  name: string
  description: string
  
  // Target Selection
  targetIds: string[]
  targetType: 'campaigns' | 'collections' | 'nfts'
  
  // Operation Data
  operationData: Record<string, any>
  
  // Execution
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  
  // Results
  successCount: number
  failureCount: number
  errors: BulkOperationError[]
  
  // Metadata
  createdBy: string
  createdAt: string
  startedAt?: string
  completedAt?: string
  estimatedDuration?: number
}

export interface BulkOperationError {
  targetId: string
  error: string
  details?: any
}

export interface MarketplaceDiscovery {
  // Featured Content
  featuredCampaigns: CampaignMarketplaceListing[]
  trendingCollections: NFTCollectionListing[]
  newListings: CampaignMarketplaceListing[]
  
  // Categories
  popularCategories: CategoryStats[]
  
  // Performance
  topPerformers: PerformanceMetric[]
  
  // Recommendations
  recommendedForYou: CampaignMarketplaceListing[]
  similarCampaigns: CampaignMarketplaceListing[]
  
  // Market Insights
  marketStats: MarketStats
  priceInsights: PriceInsight[]
}

export interface CategoryStats {
  category: MarketplaceCategory
  listingCount: number
  totalVolume: number
  averagePrice: number
  growth: number
}

export interface PerformanceMetric {
  id: string
  name: string
  type: 'campaign' | 'collection' | 'creator'
  metric: string
  value: number
  change: number
  rank: number
}

export interface MarketStats {
  totalListings: number
  totalVolume: number
  averagePrice: number
  activeUsers: number
  
  // Growth Metrics
  listingsGrowth: number
  volumeGrowth: number
  userGrowth: number
  
  // Time Series
  volumeHistory: Array<{
    date: string
    volume: number
    listings: number
  }>
}

export interface PriceInsight {
  category: MarketplaceCategory
  averagePrice: number
  priceChange: number
  volume: number
  recommendation: 'buy' | 'sell' | 'hold'
  confidence: number
}

export interface CampaignIntegrationSettings {
  campaignId: string
  
  // Marketplace Settings
  autoListOnCompletion: boolean
  defaultCategory: MarketplaceCategory
  defaultRoyalty: number
  
  // Pricing Strategy
  pricingStrategy: 'fixed' | 'auction' | 'dynamic'
  basePrice: number
  reservePrice?: number
  
  // Visibility Settings
  isPublicListing: boolean
  allowEarlyAccess: boolean
  featuredDuration?: number
  
  // Distribution Settings
  distributionMethod: 'immediate' | 'scheduled' | 'manual'
  distributionDate?: string
  
  // Quality Controls
  qualityThreshold: number
  requireManualApproval: boolean
  
  // Notifications
  notifyOnListing: boolean
  notifyOnSale: boolean
  notifyOnMilestone: boolean
  
  // Integration Features
  enableSocialSharing: boolean
  enableCommunityFeatures: boolean
  enableAnalytics: boolean
}

export interface MarketplaceTransaction {
  id: string
  type: 'purchase' | 'sale' | 'transfer' | 'mint'
  
  // Transaction Details
  itemId: string
  itemType: 'campaign' | 'collection' | 'nft'
  
  // Parties
  buyerId?: string
  sellerId?: string
  creatorId: string
  
  // Financial
  price: number
  currency: string
  royaltyAmount: number
  platformFee: number
  netAmount: number
  
  // Blockchain
  transactionHash: string
  blockNumber: number
  gasUsed: number
  gasCost: number
  
  // Status
  status: 'pending' | 'confirmed' | 'failed'
  confirmations: number
  
  // Timestamps
  createdAt: string
  confirmedAt?: string
}

export interface CampaignMarketplaceAnalytics {
  campaignId: string
  timeframe: string
  
  // Listing Performance
  views: number
  uniqueViews: number
  favorites: number
  shares: number
  
  // Sales Metrics
  totalSales: number
  totalVolume: number
  averageSalePrice: number
  conversionRate: number
  
  // Market Position
  categoryRank: number
  overallRank: number
  competitorComparison: CompetitorComparison[]
  
  // User Engagement
  engagementRate: number
  returnVisitorRate: number
  timeOnPage: number
  
  // Geographic Data
  topCountries: CountryMetric[]
  
  // Time Series
  performanceOverTime: Array<{
    date: string
    views: number
    sales: number
    volume: number
  }>
}

export interface CompetitorComparison {
  competitorId: string
  competitorName: string
  metric: string
  ourValue: number
  theirValue: number
  difference: number
}

export interface CountryMetric {
  country: string
  views: number
  sales: number
  volume: number
  percentage: number
}

// API Request/Response Types
export interface CreateMarketplaceListingRequest {
  campaignId: string
  title: string
  description: string
  category: MarketplaceCategory
  tags: string[]
  basePrice: number
  currency: string
  royaltyPercentage: number
  listingType: ListingType
  isPublic: boolean
  endDate?: string
  integrationSettings?: Partial<CampaignIntegrationSettings>
}

export interface UpdateMarketplaceListingRequest {
  title?: string
  description?: string
  category?: MarketplaceCategory
  tags?: string[]
  basePrice?: number
  royaltyPercentage?: number
  status?: MarketplaceStatus
  isPublic?: boolean
  isFeatured?: boolean
  endDate?: string
}

export interface BulkOperationRequest {
  type: BulkOperationType
  name: string
  description?: string
  targetIds: string[]
  targetType: 'campaigns' | 'collections' | 'nfts'
  operationData: Record<string, any>
}

export interface MarketplaceSearchRequest {
  query?: string
  filters?: MarketplaceFilters
  sortBy?: SortOption
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface MarketplaceSearchResponse {
  listings: CampaignMarketplaceListing[]
  total: number
  page: number
  limit: number
  hasMore: boolean
  facets: SearchFacets
  suggestions: string[]
}

export interface SearchFacets {
  categories: Array<{ category: MarketplaceCategory; count: number }>
  priceRanges: Array<{ range: string; count: number }>
  creators: Array<{ creatorId: string; creatorName: string; count: number }>
  tags: Array<{ tag: string; count: number }>
}
