/**
 * Structured Logger Service
 * Provides enterprise-grade structured logging with Winston
 */

import { Injectable, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as path from 'path';
import * as fs from 'fs';
import {
  IStructuredLogger,
  LogContext,
  LogLevel,
  LogEntry,
  LoggerConfig,
  LogTransportType,
  PerformanceMetrics,
  SecurityContext,
  BusinessContext,
} from '../interfaces/logger.interface';

@Injectable({ scope: Scope.TRANSIENT })
export class StructuredLoggerService implements IStructuredLogger {
  private readonly logger: winston.Logger;
  private readonly serviceName: string;
  private readonly environment: string;
  private readonly version: string;
  private context: LogContext;

  constructor(private readonly configService: ConfigService) {
    this.serviceName = this.configService.get<string>('SERVICE_NAME') || 'unknown-service';
    this.environment = this.configService.get<string>('NODE_ENV') || 'development';
    this.version = this.configService.get<string>('SERVICE_VERSION') || '1.0.0';

    this.context = {
      service: this.serviceName,
    };

    this.logger = this.createLogger();
  }

  /**
   * Log with specific level
   */
  log(level: LogLevel, message: string, context: LogContext = {}): void {
    const mergedContext = this.mergeContext(context);
    const logEntry = this.buildLogEntry(level, message, mergedContext);
    
    this.logger.log(level, message, logEntry);
  }

  /**
   * Info level logging
   */
  info(message: string, context: LogContext = {}): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Warning level logging
   */
  warn(message: string, context: LogContext = {}): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Error level logging
   */
  error(message: string, error?: Error, context: LogContext = {}): void {
    const errorContext = {
      ...context,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
        cause: error.cause,
      } : undefined,
    };
    this.log(LogLevel.ERROR, message, errorContext);
  }

  /**
   * Debug level logging
   */
  debug(message: string, context: LogContext = {}): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Trace level logging
   */
  trace(message: string, context: LogContext = {}): void {
    this.log(LogLevel.TRACE, message, context);
  }

  /**
   * Fatal level logging
   */
  fatal(message: string, error?: Error, context: LogContext = {}): void {
    const errorContext = {
      ...context,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
        cause: error.cause,
      } : undefined,
    };
    this.log(LogLevel.FATAL, message, errorContext);
  }

  /**
   * Set persistent context
   */
  setContext(context: Partial<LogContext>): void {
    this.context = { ...this.context, ...context };
  }

  /**
   * Get current context
   */
  getContext(): LogContext {
    return { ...this.context };
  }

  /**
   * Clear context
   */
  clearContext(): void {
    this.context = {
      service: this.serviceName,
    };
  }

  /**
   * Create child logger with additional context
   */
  child(context: Partial<LogContext>): IStructuredLogger {
    const childLogger = new StructuredLoggerService(this.configService);
    childLogger.setContext({ ...this.context, ...context });
    return childLogger;
  }

  /**
   * Create Winston logger instance
   */
  private createLogger(): winston.Logger {
    const logLevel = this.configService.get<string>('LOG_LEVEL') || 'info';
    
    return winston.createLogger({
      level: logLevel,
      format: this.createLogFormat(),
      transports: this.createTransports(),
      defaultMeta: {
        service: this.serviceName,
        environment: this.environment,
        version: this.version,
        hostname: require('os').hostname(),
        pid: process.pid,
      },
      exitOnError: false,
    });
  }

  /**
   * Create log format
   */
  private createLogFormat(): winston.Logform.Format {
    const isDevelopment = this.environment === 'development';

    if (isDevelopment) {
      return winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.colorize(),
        winston.format.printf((info) => {
          const { timestamp, level, message, service, correlationId, operation, ...meta } = info;
          const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
          const correlationStr = correlationId ? ` [${correlationId}]` : '';
          const operationStr = operation ? ` [${operation}]` : '';
          
          return `${timestamp} ${level} [${service}]${correlationStr}${operationStr}: ${message} ${metaStr}`;
        })
      );
    }

    return winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf((info) => {
        return JSON.stringify({
          '@timestamp': info.timestamp,
          level: info.level,
          message: info.message,
          service: info.service,
          environment: info.environment,
          version: info.version,
          hostname: info.hostname,
          pid: info.pid,
          ...this.sanitizeLogData(info),
        });
      })
    );
  }

  /**
   * Create log transports
   */
  private createTransports(): winston.transport[] {
    const transports: winston.transport[] = [];
    const isDevelopment = this.environment === 'development';
    const isProduction = this.environment === 'production';

    // Console transport
    if (isDevelopment || this.configService.get<boolean>('ENABLE_CONSOLE_LOGGING', true)) {
      transports.push(
        new winston.transports.Console({
          level: isDevelopment ? 'debug' : 'info',
        })
      );
    }

    // File transports for production
    if (isProduction || this.configService.get<boolean>('ENABLE_FILE_LOGGING', false)) {
      const logDir = this.configService.get<string>('LOG_DIRECTORY') || 'logs';
      this.ensureLogDirectory(logDir);

      // Error log file
      transports.push(
        new winston.transports.File({
          filename: path.join(logDir, `${this.serviceName}-error.log`),
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 10,
          tailable: true,
        })
      );

      // Combined log file
      transports.push(
        new winston.transports.File({
          filename: path.join(logDir, `${this.serviceName}-combined.log`),
          maxsize: 5242880, // 5MB
          maxFiles: 10,
          tailable: true,
        })
      );

      // Audit log file for security events
      transports.push(
        new winston.transports.File({
          filename: path.join(logDir, `${this.serviceName}-audit.log`),
          level: 'info',
          maxsize: 5242880, // 5MB
          maxFiles: 20,
          tailable: true,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
            winston.format((info) => {
              // Only log entries with security context
              return info.security ? info : false;
            })()
          ),
        })
      );
    }

    return transports;
  }

  /**
   * Merge context with instance context
   */
  private mergeContext(context: LogContext): LogContext {
    return {
      ...this.context,
      ...context,
      service: this.serviceName, // Always use service name from config
    };
  }

  /**
   * Build log entry
   */
  private buildLogEntry(level: LogLevel, message: string, context: LogContext): any {
    const entry: any = {
      correlationId: context.correlationId,
      traceId: context.traceId,
      spanId: context.spanId,
      userId: context.userId,
      sessionId: context.sessionId,
      operation: context.operation,
      metadata: context.metadata,
    };

    // Add performance metrics
    if (context.performance) {
      entry.performance = this.sanitizePerformanceMetrics(context.performance);
    }

    // Add request context
    if (context.request) {
      entry.request = {
        method: context.request.method,
        path: context.request.path,
        url: context.request.url,
        userAgent: context.request.userAgent,
        ipAddress: context.request.ipAddress,
        contentType: context.request.contentType,
        contentLength: context.request.contentLength,
        referer: context.request.referer,
        // Sanitize headers and body
        headers: this.sanitizeHeaders(context.request.headers),
        query: context.request.query,
        params: context.request.params,
        body: this.sanitizeRequestBody(context.request.body),
      };
    }

    // Add response context
    if (context.response) {
      entry.response = {
        statusCode: context.response.statusCode,
        contentType: context.response.contentType,
        contentLength: context.response.contentLength,
        cached: context.response.cached,
        compressed: context.response.compressed,
        headers: this.sanitizeHeaders(context.response.headers),
      };
    }

    // Add error context
    if (context.error) {
      entry.error = {
        name: context.error.name,
        message: context.error.message,
        code: context.error.code,
        statusCode: context.error.statusCode,
        fingerprint: context.error.fingerprint,
        tags: context.error.tags,
        details: context.error.details,
        // Include stack trace only in development
        stack: this.environment === 'development' ? context.error.stack : undefined,
      };
    }

    // Add security context
    if (context.security) {
      entry.security = {
        eventType: context.security.eventType,
        severity: context.security.severity,
        actor: context.security.actor,
        resource: context.security.resource,
        action: context.security.action,
        outcome: context.security.outcome,
        riskScore: context.security.riskScore,
        details: context.security.details,
      };
    }

    // Add business context
    if (context.business) {
      entry.business = {
        domain: context.business.domain,
        entity: context.business.entity,
        entityId: context.business.entityId,
        action: context.business.action,
        outcome: context.business.outcome,
        metrics: context.business.metrics,
        attributes: context.business.attributes,
        tags: context.business.tags,
      };
    }

    return entry;
  }

  /**
   * Sanitize performance metrics
   */
  private sanitizePerformanceMetrics(metrics: PerformanceMetrics): any {
    return {
      startTime: metrics.startTime,
      endTime: metrics.endTime,
      duration: metrics.duration,
      dbQueryCount: metrics.dbQueryCount,
      dbQueryTime: metrics.dbQueryTime,
      cacheHits: metrics.cacheHits,
      cacheMisses: metrics.cacheMisses,
      externalApiCalls: metrics.externalApiCalls,
      externalApiTime: metrics.externalApiTime,
      // Exclude detailed memory/CPU usage in production
      memoryUsage: this.environment === 'development' ? metrics.memoryUsage : undefined,
      cpuUsage: this.environment === 'development' ? metrics.cpuUsage : undefined,
    };
  }

  /**
   * Sanitize headers
   */
  private sanitizeHeaders(headers?: Record<string, string>): Record<string, string> | undefined {
    if (!headers) return undefined;

    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
    
    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  /**
   * Sanitize request body
   */
  private sanitizeRequestBody(body?: any): any {
    if (!body) return undefined;

    const sensitiveFields = ['password', 'secret', 'token', 'key', 'apiKey', 'privateKey'];
    return this.deepSanitize(body, sensitiveFields);
  }

  /**
   * Deep sanitize object
   */
  private deepSanitize(obj: any, sensitiveFields: string[]): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepSanitize(item, sensitiveFields));
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = this.deepSanitize(value, sensitiveFields);
      }
    }

    return sanitized;
  }

  /**
   * Sanitize log data
   */
  private sanitizeLogData(data: any): any {
    const { timestamp, level, message, service, environment, version, hostname, pid, ...rest } = data;
    return rest;
  }

  /**
   * Ensure log directory exists
   */
  private ensureLogDirectory(logDir: string): void {
    try {
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
    } catch (error) {
      console.error(`Failed to create log directory ${logDir}:`, error);
    }
  }
}
