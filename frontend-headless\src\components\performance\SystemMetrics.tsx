'use client'

import React, { useState } from 'react'
import {
  CpuChipIcon,
  ServerIcon,
  CircleStackIcon,
  GlobeAltIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline'
import { SystemPerformance } from '@/types/performance.types'

interface SystemMetricsProps {
  systemData?: any
  className?: string
}

export default function SystemMetrics({
  systemData,
  className = ''
}: SystemMetricsProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h')

  const timeRanges = [
    { value: '15m', label: '15 minutes' },
    { value: '1h', label: '1 hour' },
    { value: '6h', label: '6 hours' },
    { value: '24h', label: '24 hours' },
    { value: '7d', label: '7 days' }
  ]

  // Mock data if systemData is not available
  const mockSystemData = {
    current: {
      cpu: {
        usage: 45.2,
        cores: 8,
        loadAverage: [1.2, 1.5, 1.8],
        processes: 156,
        threads: 892
      },
      memory: {
        total: 16384,
        used: 8192,
        free: 8192,
        percentage: 50.0,
        swap: {
          total: 4096,
          used: 512,
          free: 3584
        }
      },
      disk: {
        total: 512000,
        used: 256000,
        free: 256000,
        percentage: 50.0,
        iops: {
          read: 150,
          write: 75
        },
        latency: {
          read: 2.5,
          write: 4.2
        }
      },
      network: {
        bytesIn: 1024000,
        bytesOut: 512000,
        packetsIn: 5000,
        packetsOut: 3000,
        connectionsActive: 125,
        requestsPerSecond: 450,
        bandwidth: {
          upload: 100,
          download: 200
        }
      },
      database: {
        connections: {
          active: 25,
          idle: 15,
          total: 40
        },
        queries: {
          total: 15000,
          slow: 5,
          failed: 2
        },
        performance: {
          queryTime: 12.5,
          lockTime: 0.8,
          indexHitRatio: 98.5
        },
        storage: {
          size: 2048,
          growth: 5.2
        }
      },
      cache: {
        hitRate: 94.5,
        missRate: 5.5,
        evictionRate: 0.2,
        memory: {
          used: 1024,
          total: 2048,
          percentage: 50.0
        },
        operations: {
          gets: 10000,
          sets: 1000,
          deletes: 100
        },
        latency: {
          get: 0.5,
          set: 1.2
        }
      },
      application: {
        responseTime: {
          average: 125,
          p50: 100,
          p95: 250,
          p99: 500
        },
        throughput: {
          requestsPerSecond: 450,
          transactionsPerSecond: 200
        },
        errors: {
          rate: 0.5,
          count: 25,
          types: {
            '4xx': 15,
            '5xx': 10
          }
        },
        availability: {
          uptime: 99.95,
          downtime: 0.05,
          percentage: 99.95
        }
      },
      timestamp: new Date().toISOString()
    }
  }

  const data = systemData || mockSystemData

  const getStatusColor = (percentage: number, isInverted = false) => {
    if (isInverted) {
      if (percentage <= 20) return 'text-green-600 bg-green-100'
      if (percentage <= 50) return 'text-yellow-600 bg-yellow-100'
      return 'text-red-600 bg-red-100'
    } else {
      if (percentage >= 80) return 'text-green-600 bg-green-100'
      if (percentage >= 60) return 'text-yellow-600 bg-yellow-100'
      return 'text-red-600 bg-red-100'
    }
  }

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900">System Metrics</h2>
        
        <select
          value={selectedTimeRange}
          onChange={(e) => setSelectedTimeRange(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {timeRanges.map((range) => (
            <option key={range.value} value={range.value}>
              {range.label}
            </option>
          ))}
        </select>
      </div>

      {/* CPU Metrics */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <CpuChipIcon className="h-5 w-5 mr-2 text-blue-600" />
            CPU Performance
          </h3>
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            getStatusColor(data.current.cpu.usage, true)
          }`}>
            {data.current.cpu.usage.toFixed(1)}% Usage
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.cpu.cores}</div>
            <div className="text-sm text-gray-600">CPU Cores</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.cpu.loadAverage[0].toFixed(1)}</div>
            <div className="text-sm text-gray-600">Load Average (1m)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{formatNumber(data.current.cpu.processes)}</div>
            <div className="text-sm text-gray-600">Processes</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{formatNumber(data.current.cpu.threads)}</div>
            <div className="text-sm text-gray-600">Threads</div>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>CPU Usage</span>
            <span>{data.current.cpu.usage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${
                data.current.cpu.usage > 80 ? 'bg-red-500' :
                data.current.cpu.usage > 60 ? 'bg-yellow-500' :
                'bg-green-500'
              }`}
              style={{ width: `${data.current.cpu.usage}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Memory Metrics */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <ServerIcon className="h-5 w-5 mr-2 text-green-600" />
            Memory Usage
          </h3>
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            getStatusColor(data.current.memory.percentage, true)
          }`}>
            {data.current.memory.percentage.toFixed(1)}% Used
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{formatBytes(data.current.memory.total * 1024 * 1024)}</div>
            <div className="text-sm text-gray-600">Total Memory</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{formatBytes(data.current.memory.used * 1024 * 1024)}</div>
            <div className="text-sm text-gray-600">Used Memory</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{formatBytes(data.current.memory.free * 1024 * 1024)}</div>
            <div className="text-sm text-gray-600">Free Memory</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{formatBytes(data.current.memory.swap.used * 1024 * 1024)}</div>
            <div className="text-sm text-gray-600">Swap Used</div>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Memory Usage</span>
            <span>{data.current.memory.percentage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${
                data.current.memory.percentage > 80 ? 'bg-red-500' :
                data.current.memory.percentage > 60 ? 'bg-yellow-500' :
                'bg-green-500'
              }`}
              style={{ width: `${data.current.memory.percentage}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Disk Metrics */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <CircleStackIcon className="h-5 w-5 mr-2 text-purple-600" />
            Disk Performance
          </h3>
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            getStatusColor(data.current.disk.percentage, true)
          }`}>
            {data.current.disk.percentage.toFixed(1)}% Used
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{formatBytes(data.current.disk.total * 1024 * 1024)}</div>
            <div className="text-sm text-gray-600">Total Space</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.disk.iops.read}</div>
            <div className="text-sm text-gray-600">Read IOPS</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.disk.iops.write}</div>
            <div className="text-sm text-gray-600">Write IOPS</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.disk.latency.read.toFixed(1)}ms</div>
            <div className="text-sm text-gray-600">Read Latency</div>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Disk Usage</span>
            <span>{data.current.disk.percentage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${
                data.current.disk.percentage > 80 ? 'bg-red-500' :
                data.current.disk.percentage > 60 ? 'bg-yellow-500' :
                'bg-green-500'
              }`}
              style={{ width: `${data.current.disk.percentage}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Network Metrics */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <GlobeAltIcon className="h-5 w-5 mr-2 text-orange-600" />
            Network Performance
          </h3>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {data.current.network.requestsPerSecond} req/s
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{formatBytes(data.current.network.bytesIn)}</div>
            <div className="text-sm text-gray-600">Bytes In</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{formatBytes(data.current.network.bytesOut)}</div>
            <div className="text-sm text-gray-600">Bytes Out</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.network.connectionsActive}</div>
            <div className="text-sm text-gray-600">Active Connections</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.network.bandwidth.download} Mbps</div>
            <div className="text-sm text-gray-600">Download Speed</div>
          </div>
        </div>
      </div>

      {/* Application Metrics */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <ClockIcon className="h-5 w-5 mr-2 text-indigo-600" />
            Application Performance
          </h3>
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            getStatusColor(data.current.application.availability.percentage)
          }`}>
            {data.current.application.availability.percentage.toFixed(2)}% Uptime
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.application.responseTime.average}ms</div>
            <div className="text-sm text-gray-600">Avg Response Time</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.application.responseTime.p95}ms</div>
            <div className="text-sm text-gray-600">95th Percentile</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.application.throughput.requestsPerSecond}</div>
            <div className="text-sm text-gray-600">Requests/sec</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{data.current.application.errors.rate.toFixed(2)}%</div>
            <div className="text-sm text-gray-600">Error Rate</div>
          </div>
        </div>
      </div>

      {/* System Status Summary */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2" />
            <div>
              <div className="text-sm font-medium text-gray-900">System Health Status</div>
              <div className="text-xs text-gray-600">
                All systems operating within normal parameters • Last updated: {new Date(data.current.timestamp).toLocaleTimeString()}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 text-xs text-gray-600">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              CPU: Normal
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Memory: Normal
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Disk: Normal
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Network: Normal
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
