import { api } from '@/lib/api'
import {
  DynamicMetadata,
  MetadataSchema,
  MetadataTemplate,
  MetadataSync,
  MetadataAnalytics,
  CreateMetadataRequest,
  UpdateMetadataRequest,
  ValidateMetadataRequest,
  MigrateMetadataRequest,
  MetadataSearchRequest,
  MetadataSearchResponse,
  ValidationResult,
  StorageProvider,
  MetadataStandard
} from '@/types/dynamic-metadata.types'

export class DynamicMetadataService {
  // ===== METADATA MANAGEMENT =====
  
  async getMetadata(id: string): Promise<DynamicMetadata> {
    try {
      const response = await api.get(`/dynamic-metadata/metadata/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch metadata:', error)
      throw new Error('Failed to load metadata')
    }
  }

  async getNFTMetadata(nftId: string): Promise<DynamicMetadata> {
    try {
      const response = await api.get(`/dynamic-metadata/nfts/${nftId}/metadata`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch NFT metadata:', error)
      throw new Error('Failed to load NFT metadata')
    }
  }

  async searchMetadata(request: MetadataSearchRequest): Promise<MetadataSearchResponse> {
    try {
      const response = await api.post('/dynamic-metadata/metadata/search', request)
      return response.data
    } catch (error) {
      console.error('Failed to search metadata:', error)
      throw new Error('Failed to search metadata')
    }
  }

  async createMetadata(request: CreateMetadataRequest): Promise<DynamicMetadata> {
    try {
      const response = await api.post('/dynamic-metadata/metadata', request)
      return response.data
    } catch (error) {
      console.error('Failed to create metadata:', error)
      throw new Error('Failed to create metadata')
    }
  }

  async updateMetadata(id: string, request: UpdateMetadataRequest): Promise<DynamicMetadata> {
    try {
      const response = await api.patch(`/dynamic-metadata/metadata/${id}`, request)
      return response.data
    } catch (error) {
      console.error('Failed to update metadata:', error)
      throw new Error('Failed to update metadata')
    }
  }

  async deleteMetadata(id: string): Promise<void> {
    try {
      await api.delete(`/dynamic-metadata/metadata/${id}`)
    } catch (error) {
      console.error('Failed to delete metadata:', error)
      throw new Error('Failed to delete metadata')
    }
  }

  // ===== SCHEMA MANAGEMENT =====

  async getSchemas(): Promise<MetadataSchema[]> {
    try {
      const response = await api.get('/dynamic-metadata/schemas')
      return response.data
    } catch (error) {
      console.error('Failed to fetch schemas:', error)
      throw new Error('Failed to load metadata schemas')
    }
  }

  async getSchema(id: string): Promise<MetadataSchema> {
    try {
      const response = await api.get(`/dynamic-metadata/schemas/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch schema:', error)
      throw new Error('Failed to load schema details')
    }
  }

  async createSchema(schema: Omit<MetadataSchema, 'id' | 'createdAt' | 'updatedAt'>): Promise<MetadataSchema> {
    try {
      const response = await api.post('/dynamic-metadata/schemas', schema)
      return response.data
    } catch (error) {
      console.error('Failed to create schema:', error)
      throw new Error('Failed to create metadata schema')
    }
  }

  async updateSchema(id: string, updates: Partial<MetadataSchema>): Promise<MetadataSchema> {
    try {
      const response = await api.patch(`/dynamic-metadata/schemas/${id}`, updates)
      return response.data
    } catch (error) {
      console.error('Failed to update schema:', error)
      throw new Error('Failed to update metadata schema')
    }
  }

  async deleteSchema(id: string): Promise<void> {
    try {
      await api.delete(`/dynamic-metadata/schemas/${id}`)
    } catch (error) {
      console.error('Failed to delete schema:', error)
      throw new Error('Failed to delete metadata schema')
    }
  }

  async getSchemasByStandard(standard: MetadataStandard): Promise<MetadataSchema[]> {
    try {
      const response = await api.get(`/dynamic-metadata/schemas/by-standard/${standard}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch schemas by standard:', error)
      throw new Error('Failed to load schemas by standard')
    }
  }

  // ===== VALIDATION =====

  async validateMetadata(request: ValidateMetadataRequest): Promise<ValidationResult> {
    try {
      const response = await api.post('/dynamic-metadata/validate', request)
      return response.data
    } catch (error) {
      console.error('Failed to validate metadata:', error)
      throw new Error('Failed to validate metadata')
    }
  }

  async validateMetadataById(id: string): Promise<ValidationResult> {
    try {
      const response = await api.post(`/dynamic-metadata/metadata/${id}/validate`)
      return response.data
    } catch (error) {
      console.error('Failed to validate metadata by ID:', error)
      throw new Error('Failed to validate metadata')
    }
  }

  async getValidationHistory(metadataId: string): Promise<ValidationResult[]> {
    try {
      const response = await api.get(`/dynamic-metadata/metadata/${metadataId}/validation-history`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch validation history:', error)
      throw new Error('Failed to load validation history')
    }
  }

  // ===== MIGRATION =====

  async migrateMetadata(id: string, request: MigrateMetadataRequest): Promise<DynamicMetadata> {
    try {
      const response = await api.post(`/dynamic-metadata/metadata/${id}/migrate`, request)
      return response.data
    } catch (error) {
      console.error('Failed to migrate metadata:', error)
      throw new Error('Failed to migrate metadata')
    }
  }

  async previewMigration(id: string, request: MigrateMetadataRequest): Promise<{
    previewData: Record<string, any>
    migrationPlan: any[]
    estimatedChanges: number
    potentialIssues: string[]
  }> {
    try {
      const response = await api.post(`/dynamic-metadata/metadata/${id}/migrate/preview`, request)
      return response.data
    } catch (error) {
      console.error('Failed to preview migration:', error)
      throw new Error('Failed to preview migration')
    }
  }

  async rollbackMigration(id: string, targetVersion: string): Promise<DynamicMetadata> {
    try {
      const response = await api.post(`/dynamic-metadata/metadata/${id}/rollback`, {
        targetVersion
      })
      return response.data
    } catch (error) {
      console.error('Failed to rollback migration:', error)
      throw new Error('Failed to rollback migration')
    }
  }

  // ===== STORAGE MANAGEMENT =====

  async updateStorageProvider(id: string, provider: StorageProvider): Promise<DynamicMetadata> {
    try {
      const response = await api.patch(`/dynamic-metadata/metadata/${id}/storage`, {
        storageProvider: provider
      })
      return response.data
    } catch (error) {
      console.error('Failed to update storage provider:', error)
      throw new Error('Failed to update storage provider')
    }
  }

  async pinToIPFS(id: string): Promise<{ ipfsHash: string; pinned: boolean }> {
    try {
      const response = await api.post(`/dynamic-metadata/metadata/${id}/pin-ipfs`)
      return response.data
    } catch (error) {
      console.error('Failed to pin to IPFS:', error)
      throw new Error('Failed to pin metadata to IPFS')
    }
  }

  async uploadToArweave(id: string): Promise<{ arweaveId: string; uploaded: boolean }> {
    try {
      const response = await api.post(`/dynamic-metadata/metadata/${id}/upload-arweave`)
      return response.data
    } catch (error) {
      console.error('Failed to upload to Arweave:', error)
      throw new Error('Failed to upload metadata to Arweave')
    }
  }

  async getStorageStatus(id: string): Promise<{
    providers: Array<{
      provider: StorageProvider
      status: 'available' | 'syncing' | 'failed'
      url?: string
      hash?: string
      lastUpdated: string
    }>
    primaryProvider: StorageProvider
    redundancyLevel: number
  }> {
    try {
      const response = await api.get(`/dynamic-metadata/metadata/${id}/storage-status`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch storage status:', error)
      throw new Error('Failed to load storage status')
    }
  }

  // ===== TEMPLATES =====

  async getTemplates(): Promise<MetadataTemplate[]> {
    try {
      const response = await api.get('/dynamic-metadata/templates')
      return response.data
    } catch (error) {
      console.error('Failed to fetch templates:', error)
      throw new Error('Failed to load metadata templates')
    }
  }

  async getTemplate(id: string): Promise<MetadataTemplate> {
    try {
      const response = await api.get(`/dynamic-metadata/templates/${id}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch template:', error)
      throw new Error('Failed to load template details')
    }
  }

  async createTemplate(template: Omit<MetadataTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageCount' | 'rating'>): Promise<MetadataTemplate> {
    try {
      const response = await api.post('/dynamic-metadata/templates', template)
      return response.data
    } catch (error) {
      console.error('Failed to create template:', error)
      throw new Error('Failed to create metadata template')
    }
  }

  async updateTemplate(id: string, updates: Partial<MetadataTemplate>): Promise<MetadataTemplate> {
    try {
      const response = await api.patch(`/dynamic-metadata/templates/${id}`, updates)
      return response.data
    } catch (error) {
      console.error('Failed to update template:', error)
      throw new Error('Failed to update metadata template')
    }
  }

  async deleteTemplate(id: string): Promise<void> {
    try {
      await api.delete(`/dynamic-metadata/templates/${id}`)
    } catch (error) {
      console.error('Failed to delete template:', error)
      throw new Error('Failed to delete metadata template')
    }
  }

  async applyTemplate(templateId: string, nftId: string, customizations?: Record<string, any>): Promise<DynamicMetadata> {
    try {
      const response = await api.post(`/dynamic-metadata/templates/${templateId}/apply`, {
        nftId,
        customizations
      })
      return response.data
    } catch (error) {
      console.error('Failed to apply template:', error)
      throw new Error('Failed to apply metadata template')
    }
  }

  // ===== SYNCHRONIZATION =====

  async getSyncConfigurations(nftId: string): Promise<MetadataSync[]> {
    try {
      const response = await api.get(`/dynamic-metadata/nfts/${nftId}/sync`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch sync configurations:', error)
      throw new Error('Failed to load sync configurations')
    }
  }

  async createSyncConfiguration(sync: Omit<MetadataSync, 'id' | 'createdAt' | 'updatedAt' | 'syncResults'>): Promise<MetadataSync> {
    try {
      const response = await api.post('/dynamic-metadata/sync', sync)
      return response.data
    } catch (error) {
      console.error('Failed to create sync configuration:', error)
      throw new Error('Failed to create sync configuration')
    }
  }

  async updateSyncConfiguration(id: string, updates: Partial<MetadataSync>): Promise<MetadataSync> {
    try {
      const response = await api.patch(`/dynamic-metadata/sync/${id}`, updates)
      return response.data
    } catch (error) {
      console.error('Failed to update sync configuration:', error)
      throw new Error('Failed to update sync configuration')
    }
  }

  async deleteSyncConfiguration(id: string): Promise<void> {
    try {
      await api.delete(`/dynamic-metadata/sync/${id}`)
    } catch (error) {
      console.error('Failed to delete sync configuration:', error)
      throw new Error('Failed to delete sync configuration')
    }
  }

  async triggerSync(id: string, targetIds?: string[]): Promise<MetadataSync> {
    try {
      const response = await api.post(`/dynamic-metadata/sync/${id}/trigger`, {
        targetIds
      })
      return response.data
    } catch (error) {
      console.error('Failed to trigger sync:', error)
      throw new Error('Failed to trigger synchronization')
    }
  }

  async getSyncStatus(id: string): Promise<{
    status: string
    progress: number
    currentTarget?: string
    estimatedCompletion?: string
    errors: any[]
  }> {
    try {
      const response = await api.get(`/dynamic-metadata/sync/${id}/status`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch sync status:', error)
      throw new Error('Failed to load sync status')
    }
  }

  // ===== ANALYTICS =====

  async getMetadataAnalytics(nftId: string, timeframe: string = '30d'): Promise<MetadataAnalytics> {
    try {
      const response = await api.get(`/dynamic-metadata/nfts/${nftId}/analytics`, {
        params: { timeframe }
      })
      return response.data
    } catch (error) {
      console.error('Failed to fetch metadata analytics:', error)
      throw new Error('Failed to load metadata analytics')
    }
  }

  async getGlobalMetadataStats(): Promise<{
    totalMetadata: number
    totalSchemas: number
    totalTemplates: number
    averageQualityScore: number
    popularStandards: Array<{ standard: MetadataStandard; count: number }>
    storageDistribution: Record<StorageProvider, number>
  }> {
    try {
      const response = await api.get('/dynamic-metadata/analytics/global')
      return response.data
    } catch (error) {
      console.error('Failed to fetch global metadata stats:', error)
      throw new Error('Failed to load global metadata statistics')
    }
  }

  async getQualityReport(metadataId: string): Promise<{
    overallScore: number
    completenessScore: number
    validationScore: number
    consistencyScore: number
    recommendations: Array<{
      category: string
      issue: string
      suggestion: string
      impact: 'low' | 'medium' | 'high'
    }>
    comparison: {
      peerAverage: number
      industryAverage: number
      percentileRank: number
    }
  }> {
    try {
      const response = await api.get(`/dynamic-metadata/metadata/${metadataId}/quality-report`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch quality report:', error)
      throw new Error('Failed to load quality report')
    }
  }

  // ===== UTILITY METHODS =====

  async exportMetadata(id: string, format: 'json' | 'csv' | 'xml' = 'json'): Promise<Blob> {
    try {
      const response = await api.get(`/dynamic-metadata/metadata/${id}/export`, {
        params: { format },
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to export metadata:', error)
      throw new Error('Failed to export metadata')
    }
  }

  async importMetadata(file: File, schemaId: string): Promise<DynamicMetadata> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('schemaId', schemaId)

      const response = await api.post('/dynamic-metadata/metadata/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('Failed to import metadata:', error)
      throw new Error('Failed to import metadata')
    }
  }

  async duplicateMetadata(id: string, targetNftId: string): Promise<DynamicMetadata> {
    try {
      const response = await api.post(`/dynamic-metadata/metadata/${id}/duplicate`, {
        targetNftId
      })
      return response.data
    } catch (error) {
      console.error('Failed to duplicate metadata:', error)
      throw new Error('Failed to duplicate metadata')
    }
  }

  async getMetadataVersions(id: string): Promise<Array<{
    version: string
    createdAt: string
    changesSummary: string
    isActive: boolean
    canRollback: boolean
  }>> {
    try {
      const response = await api.get(`/dynamic-metadata/metadata/${id}/versions`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch metadata versions:', error)
      throw new Error('Failed to load metadata versions')
    }
  }

  async compareVersions(id: string, version1: string, version2: string): Promise<{
    differences: Array<{
      field: string
      type: 'added' | 'removed' | 'modified'
      oldValue?: any
      newValue?: any
    }>
    summary: {
      fieldsAdded: number
      fieldsRemoved: number
      fieldsModified: number
      significantChanges: boolean
    }
  }> {
    try {
      const response = await api.post(`/dynamic-metadata/metadata/${id}/compare`, {
        version1,
        version2
      })
      return response.data
    } catch (error) {
      console.error('Failed to compare versions:', error)
      throw new Error('Failed to compare metadata versions')
    }
  }
}

export const dynamicMetadataService = new DynamicMetadataService()
