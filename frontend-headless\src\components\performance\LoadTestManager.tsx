'use client'

import React, { useState } from 'react'
import {
  CpuChipIcon,
  PlayIcon,
  StopIcon,
  ClockIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlusIcon,
  EyeIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'
import {
  useLoadTests,
  useRunLoadTest,
  useLoadTestResult,
  useStopLoadTest,
  useScheduleLoadTest
} from '@/hooks/usePerformance'
import {
  LoadTestResult,
  LoadTestConfig,
  TestStatus,
  RunLoadTestRequest
} from '@/types/performance.types'

interface LoadTestManagerProps {
  className?: string
}

export default function LoadTestManager({
  className = ''
}: LoadTestManagerProps) {
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedTest, setSelectedTest] = useState<LoadTestResult | null>(null)
  const [newTestConfig, setNewTestConfig] = useState<Partial<RunLoadTestRequest>>({
    name: '',
    configuration: {
      targetUrl: '',
      virtualUsers: 10,
      duration: 300,
      rampUpTime: 60,
      scenarios: [],
      thresholds: []
    }
  })

  const { data: loadTests = [], isLoading } = useLoadTests()
  const runLoadTestMutation = useRunLoadTest()
  const stopLoadTestMutation = useStopLoadTest()
  const scheduleLoadTestMutation = useScheduleLoadTest()

  // Mock load tests if none provided
  const mockLoadTests: LoadTestResult[] = [
    {
      id: '1',
      name: 'NFT Marketplace Load Test',
      configuration: {
        targetUrl: 'https://api.nftplatform.com',
        virtualUsers: 100,
        duration: 600,
        rampUpTime: 120,
        scenarios: [
          {
            name: 'Browse NFTs',
            weight: 60,
            requests: [
              {
                method: 'GET',
                url: '/api/nfts',
                headers: {},
                assertions: [
                  { type: 'response_time', property: 'duration', operator: '<', value: 500 },
                  { type: 'status_code', property: 'status', operator: '==', value: 200 }
                ]
              }
            ]
          },
          {
            name: 'View NFT Details',
            weight: 30,
            requests: [
              {
                method: 'GET',
                url: '/api/nfts/{id}',
                headers: {},
                assertions: [
                  { type: 'response_time', property: 'duration', operator: '<', value: 300 },
                  { type: 'status_code', property: 'status', operator: '==', value: 200 }
                ]
              }
            ]
          },
          {
            name: 'Search NFTs',
            weight: 10,
            requests: [
              {
                method: 'GET',
                url: '/api/nfts/search',
                headers: {},
                assertions: [
                  { type: 'response_time', property: 'duration', operator: '<', value: 800 },
                  { type: 'status_code', property: 'status', operator: '==', value: 200 }
                ]
              }
            ]
          }
        ],
        thresholds: [
          { warning: 500, critical: 1000 },
          { warning: 1, critical: 5 }
        ]
      },
      results: {
        requests: {
          total: 15000,
          successful: 14850,
          failed: 150,
          rate: 25
        },
        responseTime: {
          min: 45,
          max: 1200,
          average: 285,
          median: 250,
          p95: 650,
          p99: 950
        },
        throughput: {
          requestsPerSecond: 25,
          bytesPerSecond: 1024000
        },
        errors: {
          rate: 1.0,
          types: {
            'timeout': 80,
            '500': 45,
            '503': 25
          }
        },
        resources: {
          cpu: 45,
          memory: 60,
          network: 30
        }
      },
      status: TestStatus.COMPLETED,
      startTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      endTime: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString(),
      duration: 1800,
      errors: [
        {
          type: 'timeout',
          message: 'Request timeout after 30 seconds',
          count: 80,
          percentage: 0.53,
          examples: ['/api/nfts/search?q=rare', '/api/nfts/12345']
        },
        {
          type: '500',
          message: 'Internal server error',
          count: 45,
          percentage: 0.30,
          examples: ['/api/nfts/analytics', '/api/users/profile']
        }
      ],
      recommendations: [
        'Optimize database queries for search endpoint',
        'Implement caching for frequently accessed NFT data',
        'Add rate limiting to prevent server overload',
        'Scale up server resources during peak hours'
      ]
    },
    {
      id: '2',
      name: 'API Stress Test',
      configuration: {
        targetUrl: 'https://api.nftplatform.com',
        virtualUsers: 500,
        duration: 300,
        rampUpTime: 60,
        scenarios: [],
        thresholds: []
      },
      results: {
        requests: {
          total: 0,
          successful: 0,
          failed: 0,
          rate: 0
        },
        responseTime: {
          min: 0,
          max: 0,
          average: 0,
          median: 0,
          p95: 0,
          p99: 0
        },
        throughput: {
          requestsPerSecond: 0,
          bytesPerSecond: 0
        },
        errors: {
          rate: 0,
          types: {}
        },
        resources: {
          cpu: 0,
          memory: 0,
          network: 0
        }
      },
      status: TestStatus.RUNNING,
      startTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      endTime: '',
      duration: 0,
      errors: [],
      recommendations: []
    }
  ]

  const displayTests = loadTests.length > 0 ? loadTests : mockLoadTests

  const getStatusIcon = (status: TestStatus) => {
    switch (status) {
      case TestStatus.RUNNING:
        return <PlayIcon className="h-4 w-4 text-blue-600 animate-pulse" />
      case TestStatus.COMPLETED:
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />
      case TestStatus.FAILED:
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-600" />
      case TestStatus.CANCELLED:
        return <StopIcon className="h-4 w-4 text-gray-600" />
      default:
        return <ClockIcon className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: TestStatus) => {
    switch (status) {
      case TestStatus.RUNNING:
        return 'bg-blue-100 text-blue-800'
      case TestStatus.COMPLETED:
        return 'bg-green-100 text-green-800'
      case TestStatus.FAILED:
        return 'bg-red-100 text-red-800'
      case TestStatus.CANCELLED:
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-yellow-100 text-yellow-800'
    }
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) return `${hours}h ${minutes}m ${secs}s`
    if (minutes > 0) return `${minutes}m ${secs}s`
    return `${secs}s`
  }

  const handleRunTest = () => {
    if (newTestConfig.name && newTestConfig.configuration) {
      runLoadTestMutation.mutate(newTestConfig as RunLoadTestRequest)
      setShowCreateModal(false)
      setNewTestConfig({
        name: '',
        configuration: {
          targetUrl: '',
          virtualUsers: 10,
          duration: 300,
          rampUpTime: 60,
          scenarios: [],
          thresholds: []
        }
      })
    }
  }

  const handleStopTest = (testId: string) => {
    stopLoadTestMutation.mutate(testId)
  }

  const stats = {
    total: displayTests.length,
    running: displayTests.filter(t => t.status === TestStatus.RUNNING).length,
    completed: displayTests.filter(t => t.status === TestStatus.COMPLETED).length,
    failed: displayTests.filter(t => t.status === TestStatus.FAILED).length
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg mb-4"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900">Load Test Manager</h2>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Load Test
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <CpuChipIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{stats.total}</div>
              <div className="text-sm text-gray-600">Total Tests</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <PlayIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{stats.running}</div>
              <div className="text-sm text-gray-600">Running</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{stats.completed}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
            <div className="ml-3">
              <div className="text-lg font-semibold text-gray-900">{stats.failed}</div>
              <div className="text-sm text-gray-600">Failed</div>
            </div>
          </div>
        </div>
      </div>

      {/* Load Tests List */}
      {displayTests.length > 0 ? (
        <div className="space-y-4">
          {displayTests.map((test) => (
            <div key={test.id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className="flex-shrink-0">
                    <CpuChipIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-sm font-medium text-gray-900">{test.name}</h3>
                      
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        getStatusColor(test.status)
                      }`}>
                        {getStatusIcon(test.status)}
                        <span className="ml-1">{test.status}</span>
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-900">
                          {test.configuration.virtualUsers}
                        </div>
                        <div className="text-xs text-gray-600">Virtual Users</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-900">
                          {formatDuration(test.configuration.duration)}
                        </div>
                        <div className="text-xs text-gray-600">Duration</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-900">
                          {test.results.responseTime.average}ms
                        </div>
                        <div className="text-xs text-gray-600">Avg Response</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-900">
                          {test.results.errors.rate.toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-600">Error Rate</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Target: {test.configuration.targetUrl}</span>
                      {test.startTime && (
                        <span>Started: {new Date(test.startTime).toLocaleString()}</span>
                      )}
                      {test.duration > 0 && (
                        <span>Duration: {formatDuration(test.duration)}</span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => setSelectedTest(test)}
                    className="text-blue-600 hover:text-blue-700"
                    title="View Details"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  
                  {test.status === TestStatus.RUNNING && (
                    <button
                      onClick={() => handleStopTest(test.id)}
                      className="inline-flex items-center px-3 py-1 border border-red-600 text-xs font-medium rounded text-red-600 bg-white hover:bg-red-50"
                    >
                      <StopIcon className="h-3 w-3 mr-1" />
                      Stop
                    </button>
                  )}
                  
                  {test.status === TestStatus.COMPLETED && (
                    <button
                      onClick={() => setSelectedTest(test)}
                      className="inline-flex items-center px-3 py-1 border border-blue-600 text-xs font-medium rounded text-blue-600 bg-white hover:bg-blue-50"
                    >
                      <ChartBarIcon className="h-3 w-3 mr-1" />
                      View Results
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <CpuChipIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No load tests</h3>
          <p className="mt-1 text-sm text-gray-500">
            Create your first load test to validate system performance under load.
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Create Load Test
          </button>
        </div>
      )}

      {/* Create Test Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Create Load Test</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Test Name
                  </label>
                  <input
                    type="text"
                    value={newTestConfig.name}
                    onChange={(e) => setNewTestConfig(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter test name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target URL
                  </label>
                  <input
                    type="url"
                    value={newTestConfig.configuration?.targetUrl}
                    onChange={(e) => setNewTestConfig(prev => ({
                      ...prev,
                      configuration: { ...prev.configuration!, targetUrl: e.target.value }
                    }))}
                    placeholder="https://api.example.com"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Virtual Users
                    </label>
                    <input
                      type="number"
                      value={newTestConfig.configuration?.virtualUsers}
                      onChange={(e) => setNewTestConfig(prev => ({
                        ...prev,
                        configuration: { ...prev.configuration!, virtualUsers: parseInt(e.target.value) }
                      }))}
                      min="1"
                      max="1000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Duration (seconds)
                    </label>
                    <input
                      type="number"
                      value={newTestConfig.configuration?.duration}
                      onChange={(e) => setNewTestConfig(prev => ({
                        ...prev,
                        configuration: { ...prev.configuration!, duration: parseInt(e.target.value) }
                      }))}
                      min="60"
                      max="3600"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ramp-up Time (seconds)
                  </label>
                  <input
                    type="number"
                    value={newTestConfig.configuration?.rampUpTime}
                    onChange={(e) => setNewTestConfig(prev => ({
                      ...prev,
                      configuration: { ...prev.configuration!, rampUpTime: parseInt(e.target.value) }
                    }))}
                    min="0"
                    max="600"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRunTest}
                  disabled={!newTestConfig.name || !newTestConfig.configuration?.targetUrl}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  Run Test
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Test Details Modal */}
      {selectedTest && (
        <LoadTestDetailsModal
          test={selectedTest}
          onClose={() => setSelectedTest(null)}
        />
      )}
    </div>
  )
}

interface LoadTestDetailsModalProps {
  test: LoadTestResult
  onClose: () => void
}

function LoadTestDetailsModal({ test, onClose }: LoadTestDetailsModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">{test.name}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          
          <div className="space-y-6">
            {/* Test Results */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Test Results</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded">
                  <div className="text-2xl font-bold text-gray-900">{test.results.requests.total.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Total Requests</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded">
                  <div className="text-2xl font-bold text-green-600">{test.results.requests.successful.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Successful</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded">
                  <div className="text-2xl font-bold text-red-600">{test.results.requests.failed.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Failed</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded">
                  <div className="text-2xl font-bold text-blue-600">{test.results.throughput.requestsPerSecond}</div>
                  <div className="text-sm text-gray-600">Req/sec</div>
                </div>
              </div>
            </div>

            {/* Response Time Metrics */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Response Time</h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">{test.results.responseTime.min}ms</div>
                  <div className="text-sm text-gray-600">Min</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">{test.results.responseTime.average}ms</div>
                  <div className="text-sm text-gray-600">Average</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">{test.results.responseTime.median}ms</div>
                  <div className="text-sm text-gray-600">Median</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">{test.results.responseTime.p95}ms</div>
                  <div className="text-sm text-gray-600">95th %ile</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">{test.results.responseTime.max}ms</div>
                  <div className="text-sm text-gray-600">Max</div>
                </div>
              </div>
            </div>

            {/* Errors */}
            {test.errors.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Errors</h3>
                <div className="space-y-3">
                  {test.errors.map((error, index) => (
                    <div key={index} className="p-3 bg-red-50 border border-red-200 rounded">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-red-900">{error.type}</span>
                        <span className="text-sm text-red-700">{error.count} occurrences ({error.percentage.toFixed(2)}%)</span>
                      </div>
                      <div className="text-sm text-red-800">{error.message}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recommendations */}
            {test.recommendations.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Recommendations</h3>
                <ul className="space-y-2">
                  {test.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span className="text-sm text-gray-700">{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
