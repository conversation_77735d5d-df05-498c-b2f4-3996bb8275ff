import { Injectable } from '@nestjs/common';
import { ServiceLoggerService, MetricsService } from '../../../../../shared';

export enum CircuitState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half-open',
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
  bulkheadEnabled: boolean;
  maxConcurrentRequests: number;
  successThreshold: number; // For half-open to closed transition
}

export interface CircuitBreakerStats {
  state: CircuitState;
  failureCount: number;
  successCount: number;
  lastFailureTime?: Date;
  nextAttemptTime?: Date;
  concurrentRequests: number;
  totalRequests: number;
  totalFailures: number;
  totalSuccesses: number;
}

/**
 * Circuit Breaker Service
 * 
 * Implements the Circuit Breaker pattern to prevent cascading failures.
 * Provides bulkhead isolation and intelligent failure detection.
 * 
 * @example
 * ```typescript
 * const result = await circuitBreaker.execute(
 *   'user-service',
 *   () => this.httpService.get('/users'),
 *   { failureThreshold: 5, resetTimeout: 60000 }
 * );
 * ```
 */
@Injectable()
export class CircuitBreakerService {
  private circuits = new Map<string, CircuitBreakerStats>();
  private configs = new Map<string, CircuitBreakerConfig>();

  constructor(
    private readonly logger: ServiceLoggerService,
    private readonly metrics: MetricsService
  ) {}

  /**
   * Execute an operation with circuit breaker protection
   */
  async execute<T>(
    serviceKey: string,
    operation: () => Promise<T>,
    config?: Partial<CircuitBreakerConfig>
  ): Promise<T> {
    const circuitConfig = this.getOrCreateConfig(serviceKey, config);
    const stats = this.getOrCreateStats(serviceKey);

    // Check if circuit is open
    if (stats.state === CircuitState.OPEN) {
      if (Date.now() < (stats.nextAttemptTime?.getTime() || 0)) {
        this.metrics.increment('circuit_breaker_rejections_total', {
          service: serviceKey,
          reason: 'circuit_open',
        });
        throw new Error(`Circuit breaker is OPEN for service: ${serviceKey}. Next attempt at: ${stats.nextAttemptTime}`);
      } else {
        // Transition to half-open
        this.transitionState(serviceKey, stats, CircuitState.HALF_OPEN);
      }
    }

    // Check bulkhead limit
    if (circuitConfig.bulkheadEnabled && 
        stats.concurrentRequests >= circuitConfig.maxConcurrentRequests) {
      this.metrics.increment('circuit_breaker_rejections_total', {
        service: serviceKey,
        reason: 'bulkhead_full',
      });
      throw new Error(`Bulkhead limit exceeded for service: ${serviceKey}. Current: ${stats.concurrentRequests}, Max: ${circuitConfig.maxConcurrentRequests}`);
    }

    stats.concurrentRequests++;
    stats.totalRequests++;
    const startTime = Date.now();

    try {
      const result = await operation();
      
      // Success handling
      stats.successCount++;
      stats.totalSuccesses++;
      stats.concurrentRequests--;
      
      // Reset failure count on success
      if (stats.state === CircuitState.CLOSED) {
        stats.failureCount = 0;
      }
      
      // Check if we should close the circuit from half-open
      if (stats.state === CircuitState.HALF_OPEN && 
          stats.successCount >= circuitConfig.successThreshold) {
        this.transitionState(serviceKey, stats, CircuitState.CLOSED);
        stats.failureCount = 0;
        stats.successCount = 0;
      }

      this.metrics.observe('circuit_breaker_request_duration', Date.now() - startTime, {
        service: serviceKey,
        status: 'success',
        state: stats.state,
      });

      this.metrics.increment('circuit_breaker_requests_total', {
        service: serviceKey,
        status: 'success',
        state: stats.state,
      });

      return result;
      
    } catch (error) {
      // Failure handling
      stats.failureCount++;
      stats.totalFailures++;
      stats.concurrentRequests--;
      stats.lastFailureTime = new Date();

      // Log the failure
      this.logger.warn(`Circuit breaker operation failed for ${serviceKey}`, {
        error: error.message,
        failureCount: stats.failureCount,
        state: stats.state,
      });

      // Check if we should open the circuit
      if ((stats.state === CircuitState.CLOSED || stats.state === CircuitState.HALF_OPEN) &&
          stats.failureCount >= circuitConfig.failureThreshold) {
        this.transitionState(serviceKey, stats, CircuitState.OPEN);
        stats.nextAttemptTime = new Date(Date.now() + circuitConfig.resetTimeout);
        stats.successCount = 0; // Reset success count when opening
      }

      this.metrics.observe('circuit_breaker_request_duration', Date.now() - startTime, {
        service: serviceKey,
        status: 'failure',
        state: stats.state,
      });

      this.metrics.increment('circuit_breaker_requests_total', {
        service: serviceKey,
        status: 'failure',
        state: stats.state,
      });

      throw error;
    }
  }

  /**
   * Get circuit breaker statistics for a service
   */
  getCircuitStats(serviceKey: string): CircuitBreakerStats | undefined {
    return this.circuits.get(serviceKey);
  }

  /**
   * Get all circuit breaker statistics
   */
  getAllCircuitStats(): Map<string, CircuitBreakerStats> {
    return new Map(this.circuits);
  }

  /**
   * Get circuit breaker configuration for a service
   */
  getCircuitConfig(serviceKey: string): CircuitBreakerConfig | undefined {
    return this.configs.get(serviceKey);
  }

  /**
   * Manually reset a circuit breaker
   */
  resetCircuit(serviceKey: string): void {
    const stats = this.circuits.get(serviceKey);
    if (stats) {
      this.transitionState(serviceKey, stats, CircuitState.CLOSED);
      stats.failureCount = 0;
      stats.successCount = 0;
      stats.nextAttemptTime = undefined;
      
      this.logger.logBusinessEvent('circuit-breaker', 'manual-reset', 'SUCCESS', {
        business: {
          domain: 'circuit-breaker',
          entity: 'circuit',
          action: 'manual-reset',
          outcome: 'SUCCESS',
          attributes: {
            service: serviceKey,
          },
        },
      });
    }
  }

  /**
   * Check if a circuit is healthy (closed or half-open with low failure rate)
   */
  isCircuitHealthy(serviceKey: string): boolean {
    const stats = this.circuits.get(serviceKey);
    if (!stats) return true; // No circuit means healthy
    
    return stats.state === CircuitState.CLOSED || 
           (stats.state === CircuitState.HALF_OPEN && stats.failureCount < 3);
  }

  private getOrCreateConfig(serviceKey: string, config?: Partial<CircuitBreakerConfig>): CircuitBreakerConfig {
    if (!this.configs.has(serviceKey)) {
      this.configs.set(serviceKey, {
        failureThreshold: config?.failureThreshold || 5,
        resetTimeout: config?.resetTimeout || 60000, // 1 minute
        monitoringPeriod: config?.monitoringPeriod || 10000, // 10 seconds
        bulkheadEnabled: config?.bulkheadEnabled ?? true,
        maxConcurrentRequests: config?.maxConcurrentRequests || 10,
        successThreshold: config?.successThreshold || 3, // 3 successes to close from half-open
      });
    }
    return this.configs.get(serviceKey)!;
  }

  private getOrCreateStats(serviceKey: string): CircuitBreakerStats {
    if (!this.circuits.has(serviceKey)) {
      this.circuits.set(serviceKey, {
        state: CircuitState.CLOSED,
        failureCount: 0,
        successCount: 0,
        concurrentRequests: 0,
        totalRequests: 0,
        totalFailures: 0,
        totalSuccesses: 0,
      });
    }
    return this.circuits.get(serviceKey)!;
  }

  private transitionState(serviceKey: string, stats: CircuitBreakerStats, newState: CircuitState): void {
    const oldState = stats.state;
    stats.state = newState;
    
    this.logger.logBusinessEvent('circuit-breaker', 'state-change', 'SUCCESS', {
      business: {
        domain: 'circuit-breaker',
        entity: 'circuit',
        action: 'state-change',
        outcome: 'SUCCESS',
        attributes: {
          service: serviceKey,
          fromState: oldState,
          toState: newState,
          failureCount: stats.failureCount,
          successCount: stats.successCount,
        },
      },
    });

    this.metrics.increment('circuit_breaker_state_changes_total', {
      service: serviceKey,
      from_state: oldState,
      to_state: newState,
    });

    this.metrics.gauge('circuit_breaker_state', this.stateToNumber(newState), {
      service: serviceKey,
    });
  }

  private stateToNumber(state: CircuitState): number {
    switch (state) {
      case CircuitState.CLOSED: return 0;
      case CircuitState.HALF_OPEN: return 1;
      case CircuitState.OPEN: return 2;
      default: return -1;
    }
  }
}
