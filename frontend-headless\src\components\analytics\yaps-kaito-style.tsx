'use client';

import React, { useState } from 'react';
import {
  TrendingUpIcon,
  TrendingDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  UserIcon,
  CurrencyDollarIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

// Types for the data structures
interface GainerLoser {
  id: string;
  name: string;
  current_score: number;
  previous_score: number;
  change: string;
  change_percentage: string;
  rarity: string;
  volume_24h: number;
  trend: 'up' | 'down';
}

interface Collection {
  id: string;
  name: string;
  value: number;
  size: number;
  change_24h: string;
  color: string;
  rarity: string;
}

interface Transaction {
  id: string;
  type: string;
  nft_name: string;
  username: string;
  value_eth: number;
  timestamp: string;
  rarity: string;
}

// Section 1: Top Gainers/Losers with Bubble Map
interface TopGainersLosersProps {
  gainers: GainerLoser[];
  losers: GainerLoser[];
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
}

export const TopGainersLosersSection: React.FC<TopGainersLosersProps> = ({
  gainers,
  losers,
  selectedPeriod,
  onPeriodChange,
}) => {
  const [activeView, setActiveView] = useState<'gainers' | 'losers'>('gainers');
  const currentData = activeView === 'gainers' ? gainers : losers;

  const extractTwitterHandle = (name: string) => {
    const match = name.match(/@(\w+)/);
    return match ? `@${match[1]}` : '@user';
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary': return 'bg-yellow-500';
      case 'epic': return 'bg-purple-500';
      case 'rare': return 'bg-blue-500';
      case 'common': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  const getBubbleSize = (score: number) => {
    // Scale bubble size based on score (40-120px)
    const minSize = 40;
    const maxSize = 120;
    const normalizedScore = Math.min(Math.max(score, 0), 100);
    return minSize + (normalizedScore / 100) * (maxSize - minSize);
  };

  return (
    <div className="bg-white shadow rounded-lg p-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Top Gainers & Losers</h2>
          <p className="text-sm text-gray-500">Users' final score status and performance tracking</p>
        </div>
        
        {/* Period Selector */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">Period:</span>
          {['7d', '30d'].map((period) => (
            <button
              key={period}
              onClick={() => onPeriodChange(period)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                selectedPeriod === period
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              {period}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Side: List View */}
        <div>
          {/* Toggle Buttons */}
          <div className="flex mb-4">
            <button
              onClick={() => setActiveView('gainers')}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-l-lg border ${
                activeView === 'gainers'
                  ? 'bg-green-50 text-green-700 border-green-200'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              <TrendingUpIcon className="h-4 w-4 inline mr-2" />
              Top Gainers ({gainers.length})
            </button>
            <button
              onClick={() => setActiveView('losers')}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-r-lg border-l-0 border ${
                activeView === 'losers'
                  ? 'bg-red-50 text-red-700 border-red-200'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              <TrendingDownIcon className="h-4 w-4 inline mr-2" />
              Top Losers ({losers.length})
            </button>
          </div>

          {/* List Items */}
          <div className="space-y-3">
            {currentData.map((item, index) => (
              <div key={item.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <UserIcon className="h-6 w-6 text-gray-500" />
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{extractTwitterHandle(item.name)}</span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRarityColor(item.rarity)} text-white`}>
                        {item.rarity}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      Score: {item.current_score} | Volume: {item.volume_24h} ETH
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`flex items-center space-x-1 ${
                    item.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {item.trend === 'up' ? (
                      <ArrowUpIcon className="h-4 w-4" />
                    ) : (
                      <ArrowDownIcon className="h-4 w-4" />
                    )}
                    <span className="font-bold">{item.change_percentage}</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {selectedPeriod} change
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Side: Bubble Map */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Bubble Map</h3>
          <div className="relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden">
            {/* Bubble Map Container */}
            <div className="relative w-full h-full">
              {currentData.map((item, index) => {
                const size = getBubbleSize(item.current_score);
                const x = (index % 3) * 30 + 10; // Distribute horizontally
                const y = Math.floor(index / 3) * 25 + 10; // Distribute vertically
                
                return (
                  <div
                    key={item.id}
                    className="absolute cursor-pointer transition-all duration-300 hover:scale-110"
                    style={{
                      left: `${x}%`,
                      top: `${y}%`,
                      width: `${size}px`,
                      height: `${size}px`,
                    }}
                  >
                    <div
                      className={`w-full h-full rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg ${
                        item.trend === 'up' ? 'bg-green-500' : 'bg-red-500'
                      }`}
                      title={`${extractTwitterHandle(item.name)} - Score: ${item.current_score} (${item.change_percentage})`}
                    >
                      <div className="text-center">
                        <div className="text-xs">{extractTwitterHandle(item.name).slice(0, 6)}</div>
                        <div className="text-xs font-bold">{item.current_score}</div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            
            {/* Legend */}
            <div className="absolute bottom-2 right-2 bg-white p-2 rounded shadow">
              <div className="text-xs text-gray-600">
                <div className="flex items-center space-x-1 mb-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Gainers</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Losers</span>
                </div>
                <div className="text-xs mt-1">Size = Score</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Section 2: Collection Market Value Bubble Map
interface CollectionMarketValueProps {
  collections: Collection[];
  totalMarketValue: number;
  marketCapChange: string;
  onCollectionClick: (collectionId: string) => void;
}

export const CollectionMarketValueSection: React.FC<CollectionMarketValueProps> = ({
  collections,
  totalMarketValue,
  marketCapChange,
  onCollectionClick,
}) => {
  const getCollectionBubbleSize = (value: number) => {
    // Scale bubble size based on collection value (60-150px)
    const minSize = 60;
    const maxSize = 150;
    const maxValue = Math.max(...collections.map(c => c.value));
    const normalizedValue = value / maxValue;
    return minSize + normalizedValue * (maxSize - minSize);
  };

  return (
    <div className="bg-white shadow rounded-lg p-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Collection Market Value</h2>
          <p className="text-sm text-gray-500">Project collections growth and decline tracking</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900">{totalMarketValue.toFixed(2)} ETH</div>
          <div className={`text-sm font-medium ${
            marketCapChange.startsWith('+') ? 'text-green-600' : 'text-red-600'
          }`}>
            {marketCapChange} (24h)
          </div>
        </div>
      </div>

      {/* Bubble Map */}
      <div className="relative h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6 overflow-hidden">
        <div className="relative w-full h-full">
          {collections.map((collection, index) => {
            const size = getCollectionBubbleSize(collection.value);
            const x = (index % 2) * 40 + 10; // Distribute horizontally
            const y = Math.floor(index / 2) * 35 + 10; // Distribute vertically
            
            return (
              <div
                key={collection.id}
                className="absolute cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl"
                style={{
                  left: `${x}%`,
                  top: `${y}%`,
                  width: `${size}px`,
                  height: `${size}px`,
                }}
                onClick={() => onCollectionClick(collection.id)}
              >
                <div
                  className="w-full h-full rounded-lg flex flex-col items-center justify-center text-white font-bold shadow-lg border-2 border-white"
                  style={{ backgroundColor: collection.color }}
                >
                  <div className="text-center p-2">
                    <div className="text-xs mb-1">{collection.name}</div>
                    <div className="text-lg font-bold">{collection.value} ETH</div>
                    <div className="text-xs">{collection.size} NFTs</div>
                    <div className={`text-xs font-bold mt-1 ${
                      collection.change_24h.startsWith('+') ? 'text-green-200' : 'text-red-200'
                    }`}>
                      {collection.change_24h}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Legend */}
        <div className="absolute bottom-4 right-4 bg-white p-3 rounded shadow">
          <div className="text-xs text-gray-600">
            <div className="font-medium mb-2">Collection Value</div>
            <div className="space-y-1">
              {collections.map((collection) => (
                <div key={collection.id} className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded" 
                    style={{ backgroundColor: collection.color }}
                  ></div>
                  <span>{collection.rarity}</span>
                </div>
              ))}
            </div>
            <div className="text-xs mt-2">Click to view collection</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Section 3: Recent Transactions
interface RecentTransactionsProps {
  transactions: Transaction[];
}

export const RecentTransactionsSection: React.FC<RecentTransactionsProps> = ({
  transactions,
}) => {
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const transactionTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - transactionTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'generation':
        return '🎨';
      case 'share':
        return '📤';
      case 'favorite':
        return '❤️';
      default:
        return '💎';
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'generation':
        return 'bg-blue-100 text-blue-800';
      case 'share':
        return 'bg-green-100 text-green-800';
      case 'favorite':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary': return 'text-yellow-600 bg-yellow-100';
      case 'epic': return 'text-purple-600 bg-purple-100';
      case 'rare': return 'text-blue-600 bg-blue-100';
      case 'common': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="bg-white shadow rounded-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Recent Transactions</h2>
          <p className="text-sm text-gray-500">Latest NFT activities, purchases and interactions</p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <ClockIcon className="h-4 w-4" />
          <span>Real-time updates</span>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="overflow-hidden">
        <div className="space-y-3">
          {transactions.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {/* Left Side: Transaction Info */}
              <div className="flex items-center space-x-4">
                {/* Transaction Type Icon */}
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getTransactionColor(transaction.type)}`}>
                  <span className="text-lg">{getTransactionIcon(transaction.type)}</span>
                </div>

                {/* Transaction Details */}
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{transaction.nft_name}</span>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRarityColor(transaction.rarity)}`}>
                      {transaction.rarity}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>User: @{transaction.username}</span>
                    <span>•</span>
                    <span className="capitalize">{transaction.type}</span>
                    <span>•</span>
                    <span>{formatTimeAgo(transaction.timestamp)}</span>
                  </div>
                </div>
              </div>

              {/* Right Side: Value */}
              <div className="text-right">
                <div className="flex items-center space-x-2">
                  <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                  <span className="font-bold text-gray-900">{transaction.value_eth.toFixed(3)} ETH</span>
                </div>
                <div className="text-sm text-gray-500">
                  ${(transaction.value_eth * 2000).toFixed(0)} USD
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        <div className="mt-6 text-center">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            Load More Transactions
          </button>
        </div>
      </div>
    </div>
  );
};
