import { Module } from '@nestjs/common';
import { CacheService } from './cache.service';
import { CacheController } from './cache.controller';

/**
 * Cache Module
 * 
 * Provides multi-level caching for API Gateway with
 * in-memory caching, TTL support, and LRU eviction.
 */
@Module({
  providers: [CacheService],
  controllers: [CacheController],
  exports: [CacheService],
})
export class CacheModule {}
