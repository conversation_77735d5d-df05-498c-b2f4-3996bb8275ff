import { Controller, Get, Post, Param } from '@nestjs/common';
import { 
  ResponseService, 
  RequirePermissions, 
  Permission, 
  Public 
} from '../../shared';
import { CircuitBreakerService } from './circuit-breaker.service';

/**
 * Circuit Breaker Controller
 * 
 * Provides REST API for circuit breaker monitoring and management.
 * Allows viewing circuit states and manual circuit resets.
 */
@Controller('circuit-breaker')
export class CircuitBreakerController {
  constructor(
    private readonly circuitBreaker: CircuitBreakerService,
    private readonly responseService: ResponseService
  ) {}

  /**
   * Get all circuit breaker statistics
   */
  @Get('stats')
  @RequirePermissions(Permission.ADMIN)
  async getAllStats() {
    const stats = this.circuitBreaker.getAllCircuitStats();
    const statsArray = Array.from(stats.entries()).map(([service, stat]) => ({
      service,
      ...stat,
      healthStatus: this.circuitBreaker.isCircuitHealthy(service) ? 'healthy' : 'unhealthy',
    }));

    return this.responseService.success(statsArray, 'Circuit breaker statistics retrieved');
  }

  /**
   * Get circuit breaker statistics for a specific service
   */
  @Get('stats/:serviceKey')
  @Public() // Allow internal monitoring
  async getServiceStats(@Param('serviceKey') serviceKey: string) {
    const stats = this.circuitBreaker.getCircuitStats(serviceKey);
    const config = this.circuitBreaker.getCircuitConfig(serviceKey);
    
    if (!stats) {
      return this.responseService.notFound('Circuit breaker', serviceKey);
    }

    return this.responseService.success({
      service: serviceKey,
      stats,
      config,
      healthStatus: this.circuitBreaker.isCircuitHealthy(serviceKey) ? 'healthy' : 'unhealthy',
    }, `Circuit breaker statistics for ${serviceKey}`);
  }

  /**
   * Reset a circuit breaker manually
   */
  @Post('reset/:serviceKey')
  @RequirePermissions(Permission.ADMIN)
  async resetCircuit(@Param('serviceKey') serviceKey: string) {
    this.circuitBreaker.resetCircuit(serviceKey);
    return this.responseService.success(
      { service: serviceKey, action: 'reset' },
      `Circuit breaker for ${serviceKey} has been reset`
    );
  }

  /**
   * Health check for circuit breaker system
   */
  @Get('health')
  @Public()
  async health() {
    const allStats = this.circuitBreaker.getAllCircuitStats();
    const totalCircuits = allStats.size;
    const healthyCircuits = Array.from(allStats.keys())
      .filter(service => this.circuitBreaker.isCircuitHealthy(service)).length;
    const openCircuits = Array.from(allStats.values())
      .filter(stat => stat.state === 'open').length;

    return this.responseService.success({
      status: 'healthy',
      totalCircuits,
      healthyCircuits,
      openCircuits,
      timestamp: new Date().toISOString(),
    }, 'Circuit breaker system is healthy');
  }

  /**
   * Get circuit breaker dashboard data
   */
  @Get('dashboard')
  @RequirePermissions(Permission.ADMIN)
  async getDashboard() {
    const allStats = this.circuitBreaker.getAllCircuitStats();
    const dashboard = {
      summary: {
        totalCircuits: allStats.size,
        closedCircuits: 0,
        halfOpenCircuits: 0,
        openCircuits: 0,
        totalRequests: 0,
        totalFailures: 0,
        totalSuccesses: 0,
      },
      circuits: [] as any[],
    };

    for (const [service, stats] of allStats.entries()) {
      // Update summary
      dashboard.summary.totalRequests += stats.totalRequests;
      dashboard.summary.totalFailures += stats.totalFailures;
      dashboard.summary.totalSuccesses += stats.totalSuccesses;

      switch (stats.state) {
        case 'closed':
          dashboard.summary.closedCircuits++;
          break;
        case 'half-open':
          dashboard.summary.halfOpenCircuits++;
          break;
        case 'open':
          dashboard.summary.openCircuits++;
          break;
      }

      // Add circuit details
      dashboard.circuits.push({
        service,
        state: stats.state,
        failureCount: stats.failureCount,
        successCount: stats.successCount,
        concurrentRequests: stats.concurrentRequests,
        totalRequests: stats.totalRequests,
        successRate: stats.totalRequests > 0 ? 
          ((stats.totalSuccesses / stats.totalRequests) * 100).toFixed(2) + '%' : '0%',
        lastFailureTime: stats.lastFailureTime,
        nextAttemptTime: stats.nextAttemptTime,
        isHealthy: this.circuitBreaker.isCircuitHealthy(service),
      });
    }

    return this.responseService.success(dashboard, 'Circuit breaker dashboard data retrieved');
  }
}
