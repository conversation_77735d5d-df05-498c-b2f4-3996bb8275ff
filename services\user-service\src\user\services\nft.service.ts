import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../common/services/cache.service';
import { AuditService } from '../../common/services/audit.service';
import { RBACService } from './rbac.service';
import { 
  GenerateNFTDto, 
  UpdateNFTDto, 
  MintNFTDto, 
  TransferNFTDto,
  NFTResponseDto,
  NFTRarity,
  NFTStatus,
  BlockchainNetwork,
  NFTMetadataDto 
} from '../dto/nft.dto';
import { Permission } from '../dto/rbac.dto';
import { RequestContext } from '../interfaces/request-context.interface';
import { firstValueFrom } from 'rxjs';

export interface NFTResult {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export interface ExternalNFTGenerationRequest {
  userId: string;
  campaignId: string;
  engagementScore: number;
  rarity: NFTRarity;
  template?: string;
  generationParams?: Record<string, any>;
  blockchain: BlockchainNetwork;
}

export interface ExternalNFTGenerationResponse {
  success: boolean;
  nftId: string;
  metadata: NFTMetadataDto;
  imageUrl: string;
  status: NFTStatus;
  estimatedCompletionTime?: number;
  error?: string;
}

@Injectable()
export class NFTService {
  private readonly logger = new Logger(NFTService.name);
  private readonly nftGeneratorServiceUrl: string;
  private readonly blockchainServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
    private readonly auditService: AuditService,
    private readonly rbacService: RBACService,
  ) {
    this.nftGeneratorServiceUrl = this.configService.get<string>('NFT_GENERATOR_SERVICE_URL') || 'http://localhost:3013';
    this.blockchainServiceUrl = this.configService.get<string>('BLOCKCHAIN_SERVICE_URL') || 'http://localhost:3014';
  }

  /**
   * Generate NFT for user based on engagement score
   */
  async generateNFT(generateNFTDto: GenerateNFTDto, context: RequestContext): Promise<NFTResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Generating NFT for user: ${generateNFTDto.userId}`, { correlationId });

      // Check permissions
      const canGenerateNFTs = await this.rbacService.hasPermission(context.userId, Permission.NFT_WRITE);
      if (!canGenerateNFTs) {
        throw new ForbiddenException('Insufficient permissions to generate NFTs');
      }

      // Validate user exists and is active
      const user = await this.prisma.userQuery.findUnique({
        where: { id: generateNFTDto.userId },
        select: { id: true, isActive: true, username: true, displayName: true },
      });

      if (!user || !user.isActive) {
        throw new BadRequestException('User not found or inactive');
      }

      // Validate campaign exists
      const campaign = await this.prisma.campaign.findUnique({
        where: { id: generateNFTDto.campaignId },
        select: { id: true, name: true, status: true },
      });

      if (!campaign) {
        throw new BadRequestException('Campaign not found');
      }

      // Calculate NFT rarity based on engagement score if not specified
      const calculatedRarity = this.calculateNFTRarity(generateNFTDto.engagementScore);
      const finalRarity = generateNFTDto.rarity || calculatedRarity;

      // Create NFT record in database first
      const nft = await this.prisma.nFT.create({
        data: {
          tokenId: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: `NFT for Campaign ${generateNFTDto.campaignId}`,
          ownerId: generateNFTDto.userId,
          userId: generateNFTDto.userId,
          campaignId: generateNFTDto.campaignId,
          rarity: finalRarity,
          status: NFTStatus.PENDING,
          blockchain: generateNFTDto.blockchain || BlockchainNetwork.POLYGON,
          engagementScore: generateNFTDto.engagementScore,
          generationParams: generateNFTDto.generationParams || {},
          metadata: {},
          createdBy: context.userId,
        },
      });

      // Call external NFT generation service
      const generationRequest: ExternalNFTGenerationRequest = {
        userId: generateNFTDto.userId,
        campaignId: generateNFTDto.campaignId,
        engagementScore: generateNFTDto.engagementScore,
        rarity: finalRarity,
        template: generateNFTDto.template,
        generationParams: generateNFTDto.generationParams,
        blockchain: generateNFTDto.blockchain || BlockchainNetwork.POLYGON,
      };

      let externalResponse: ExternalNFTGenerationResponse;

      try {
        // In production, this would call the actual NFT Generator Service
        // For now, we'll simulate the response
        externalResponse = await this.callExternalNFTGenerator(generationRequest, correlationId);
      } catch (error) {
        // Update NFT status to failed
        await this.prisma.nFT.update({
          where: { id: nft.id },
          data: { 
            status: NFTStatus.FAILED,
            updatedBy: context.userId,
          },
        });

        throw new BadRequestException(`NFT generation failed: ${error.message}`);
      }

      // Update NFT with generation results
      const updatedNft = await this.prisma.nFT.update({
        where: { id: nft.id },
        data: {
          status: externalResponse.status,
          metadata: externalResponse.metadata as any,
          imageUrl: externalResponse.imageUrl,
          externalNftId: externalResponse.nftId,
          updatedBy: context.userId,
        },
      });

      // Auto-mint if requested and generation is complete
      if (generateNFTDto.autoMint && externalResponse.status === NFTStatus.COMPLETED) {
        const mintResult = await this.mintNFT({
          nftId: nft.id,
          recipientAddress: generateNFTDto.recipientAddress || user.id, // Use user ID as placeholder
          blockchain: generateNFTDto.blockchain,
        }, context);

        if (!mintResult.success) {
          this.logger.warn(`Auto-minting failed for NFT ${nft.id}: ${mintResult.error}`, { correlationId });
        }
      }

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'NFT',
        entityId: nft.id,
        action: 'CREATE',
        resource: 'nfts',
        newValues: updatedNft,
        userId: context.userId,
        correlationId: context.correlationId,
        metadata: { 
          campaignId: generateNFTDto.campaignId,
          engagementScore: generateNFTDto.engagementScore,
          rarity: finalRarity,
        },
      });

      this.logger.log(`NFT generated successfully: ${nft.id}`, {
        correlationId,
        nftId: nft.id,
        rarity: finalRarity,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: this.mapToNFTResponse(updatedNft),
        message: 'NFT generation initiated successfully',
      };

    } catch (error) {
      this.logger.error(`NFT generation failed: ${error.message}`, {
        correlationId,
        generateNFTDto,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof BadRequestException || error instanceof ForbiddenException) {
        throw error;
      }

      return { success: false, error: 'NFT generation failed' };
    }
  }

  /**
   * Get NFT by ID
   */
  async getNFTById(nftId: string, context: RequestContext): Promise<NFTResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Getting NFT: ${nftId}`, { correlationId });

      // Check permissions
      const canReadNFTs = await this.rbacService.hasPermission(context.userId, Permission.NFT_READ);
      if (!canReadNFTs) {
        throw new ForbiddenException('Insufficient permissions to view NFTs');
      }

      // Try cache first
      const cacheKey = `nft:${nftId}`;
      let nft = await this.cacheService.get<any>(cacheKey);

      if (!nft) {
        nft = await this.prisma.nFT.findUnique({
          where: { id: nftId },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
              },
            },
            campaign: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        });

        if (!nft) {
          throw new NotFoundException(`NFT with ID ${nftId} not found`);
        }

        // Cache for 5 minutes
        await this.cacheService.set(cacheKey, nft, 300);
      }

      this.logger.log(`NFT retrieved successfully: ${nftId}`, {
        correlationId,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: this.mapToNFTResponse(nft),
      };

    } catch (error) {
      this.logger.error(`Failed to get NFT ${nftId}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }

      return { success: false, error: 'Failed to retrieve NFT' };
    }
  }

  /**
   * Mint NFT on blockchain
   */
  async mintNFT(mintNFTDto: MintNFTDto, context: RequestContext): Promise<NFTResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Minting NFT: ${mintNFTDto.nftId}`, { correlationId });

      // Check permissions
      const canMintNFTs = await this.rbacService.hasPermission(context.userId, Permission.NFT_WRITE);
      if (!canMintNFTs) {
        throw new ForbiddenException('Insufficient permissions to mint NFTs');
      }

      // Get NFT
      const nft = await this.prisma.nFT.findUnique({
        where: { id: mintNFTDto.nftId },
      });

      if (!nft) {
        throw new NotFoundException(`NFT with ID ${mintNFTDto.nftId} not found`);
      }

      if (nft.status === NFTStatus.MINTED) {
        throw new BadRequestException('NFT is already minted');
      }

      if (nft.status !== NFTStatus.COMPLETED) {
        throw new BadRequestException('NFT must be completed before minting');
      }

      // Call blockchain service for minting
      const mintingResult = await this.callBlockchainService('mint', {
        nftId: mintNFTDto.nftId,
        metadata: nft.metadata,
        recipientAddress: mintNFTDto.recipientAddress,
        blockchain: mintNFTDto.blockchain || nft.blockchain,
        gasPrice: mintNFTDto.gasPrice,
        priorityMinting: mintNFTDto.priorityMinting,
      }, correlationId);

      // Update NFT with minting results
      const updatedNft = await this.prisma.nFT.update({
        where: { id: mintNFTDto.nftId },
        data: {
          status: NFTStatus.MINTED,
          tokenId: mintingResult.tokenId,
          contractAddress: mintingResult.contractAddress,
          transactionHash: mintingResult.transactionHash,
          ownerAddress: mintNFTDto.recipientAddress,
          mintedAt: new Date(),
          updatedBy: context.userId,
        },
      });

      // Invalidate cache
      await this.invalidateNFTCache(mintNFTDto.nftId);

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'NFT',
        entityId: mintNFTDto.nftId,
        action: 'UPDATE',
        resource: 'nfts',
        oldValues: { status: nft.status },
        newValues: { status: NFTStatus.MINTED, tokenId: mintingResult.tokenId },
        userId: context.userId,
        correlationId: context.correlationId,
        metadata: { 
          action: 'nft_minting',
          blockchain: mintNFTDto.blockchain || nft.blockchain,
          recipientAddress: mintNFTDto.recipientAddress,
        },
      });

      this.logger.log(`NFT minted successfully: ${mintNFTDto.nftId}`, {
        correlationId,
        tokenId: mintingResult.tokenId,
        transactionHash: mintingResult.transactionHash,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: this.mapToNFTResponse(updatedNft),
        message: 'NFT minted successfully',
      };

    } catch (error) {
      this.logger.error(`NFT minting failed: ${error.message}`, {
        correlationId,
        mintNFTDto,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof NotFoundException || error instanceof BadRequestException || error instanceof ForbiddenException) {
        throw error;
      }

      return { success: false, error: 'NFT minting failed' };
    }
  }

  /**
   * Calculate NFT rarity based on engagement score
   */
  private calculateNFTRarity(engagementScore: number): NFTRarity {
    if (engagementScore >= 95) return NFTRarity.MYTHIC;
    if (engagementScore >= 85) return NFTRarity.LEGENDARY;
    if (engagementScore >= 70) return NFTRarity.EPIC;
    if (engagementScore >= 50) return NFTRarity.RARE;
    return NFTRarity.COMMON;
  }

  /**
   * Call external NFT generation service
   */
  private async callExternalNFTGenerator(
    request: ExternalNFTGenerationRequest, 
    correlationId: string
  ): Promise<ExternalNFTGenerationResponse> {
    try {
      // In production, this would make an actual HTTP call to the NFT Generator Service
      // For now, we'll simulate the response
      this.logger.log(`Calling NFT Generator Service for user ${request.userId}`, { correlationId });

      // Simulate external service call
      const response = await this.simulateNFTGeneration(request);
      
      return response;
    } catch (error) {
      this.logger.error(`NFT Generator Service call failed: ${error.message}`, { correlationId });
      throw error;
    }
  }

  /**
   * Simulate NFT generation (placeholder for actual service integration)
   */
  private async simulateNFTGeneration(request: ExternalNFTGenerationRequest): Promise<ExternalNFTGenerationResponse> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 100));

    const rarityNames = {
      [NFTRarity.COMMON]: 'Common',
      [NFTRarity.RARE]: 'Rare',
      [NFTRarity.EPIC]: 'Epic',
      [NFTRarity.LEGENDARY]: 'Legendary',
      [NFTRarity.MYTHIC]: 'Mythic',
    };

    const metadata: NFTMetadataDto = {
      name: `${rarityNames[request.rarity]} Campaign NFT #${Math.floor(Math.random() * 10000)}`,
      description: `A ${request.rarity} NFT generated from campaign participation with ${request.engagementScore}% engagement score.`,
      image: `https://nft-storage.example.com/images/${request.rarity}-${Date.now()}.png`,
      external_url: `https://platform.example.com/nft/${request.userId}`,
      attributes: [
        { trait_type: 'Rarity', value: rarityNames[request.rarity] },
        { trait_type: 'Engagement Score', value: request.engagementScore, display_type: 'number' },
        { trait_type: 'Campaign', value: request.campaignId },
        { trait_type: 'Generation Date', value: new Date().toISOString().split('T')[0] },
        { trait_type: 'Blockchain', value: request.blockchain },
      ],
      background_color: this.getRarityColor(request.rarity),
    };

    return {
      success: true,
      nftId: `ext_nft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      metadata,
      imageUrl: metadata.image,
      status: NFTStatus.COMPLETED,
      estimatedCompletionTime: 0,
    };
  }

  /**
   * Get rarity background color
   */
  private getRarityColor(rarity: NFTRarity): string {
    const colors = {
      [NFTRarity.COMMON]: '#808080',
      [NFTRarity.RARE]: '#0070f3',
      [NFTRarity.EPIC]: '#7c3aed',
      [NFTRarity.LEGENDARY]: '#f59e0b',
      [NFTRarity.MYTHIC]: '#ef4444',
    };
    return colors[rarity];
  }

  /**
   * Call blockchain service
   */
  private async callBlockchainService(action: string, data: any, correlationId: string): Promise<any> {
    try {
      // In production, this would make an actual HTTP call to the Blockchain Service
      // For now, we'll simulate the response
      this.logger.log(`Calling Blockchain Service for ${action}`, { correlationId });

      // Simulate blockchain interaction
      return {
        tokenId: Math.floor(Math.random() * 1000000).toString(),
        contractAddress: '0x' + Math.random().toString(16).substr(2, 40),
        transactionHash: '0x' + Math.random().toString(16).substr(2, 64),
        blockNumber: Math.floor(Math.random() * 1000000),
        gasUsed: Math.floor(Math.random() * 100000),
      };
    } catch (error) {
      this.logger.error(`Blockchain Service call failed: ${error.message}`, { correlationId });
      throw error;
    }
  }

  /**
   * Map database NFT to response DTO
   */
  private mapToNFTResponse(nft: any): NFTResponseDto {
    return {
      id: nft.id,
      userId: nft.userId,
      campaignId: nft.campaignId,
      metadata: nft.metadata as NFTMetadataDto,
      rarity: nft.rarity as NFTRarity,
      status: nft.status as NFTStatus,
      blockchain: nft.blockchain as BlockchainNetwork,
      engagementScore: nft.engagementScore,
      tokenId: nft.tokenId,
      contractAddress: nft.contractAddress,
      transactionHash: nft.transactionHash,
      ownerAddress: nft.ownerAddress,
      createdAt: nft.createdAt.toISOString(),
      mintedAt: nft.mintedAt?.toISOString(),
      lastTransferredAt: nft.lastTransferredAt?.toISOString(),
      generationParams: nft.generationParams,
      additionalMetadata: nft.additionalMetadata,
    };
  }

  /**
   * Invalidate NFT cache
   */
  async invalidateNFTCache(nftId: string): Promise<void> {
    const cacheKey = `nft:${nftId}`;
    await this.cacheService.del(cacheKey);
    this.logger.debug(`Invalidated cache for NFT: ${nftId}`);
  }
}
