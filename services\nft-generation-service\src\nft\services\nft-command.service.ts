// Enterprise NFT Command Service (Write Side)
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../shared/prisma.service';
import { AuditService } from '../shared/audit.service';
import { EventService } from '../shared/event.service';
import { ExternalStorageService, NFTMetadata } from './external-storage.service';
import { NFTImageGeneratorService, NFTImageGenerationParams } from './nft-image-generator.service';
import { CreateNftCommandDto, UpdateNftCommandDto, GenerationStatus } from '../models/nft-command.model';
import { EntityType, AuditAction, EventType } from '../models/audit-event.model';

export interface RequestContext {
  correlationId?: string;
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

@Injectable()
export class NftCommandService {
  private readonly logger = new Logger(NftCommandService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly auditService: AuditService,
    private readonly eventService: EventService,
    private readonly externalStorageService: ExternalStorageService,
    private readonly nftImageGenerator: NFTImageGeneratorService
  ) {}

  async createNft(createNftDto: CreateNftCommandDto, context: RequestContext) {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Creating NFT for user ${createNftDto.userId}`, context.correlationId);

      // Create NFT in command model
      const nft = await this.prisma.nftCommand.create({
        data: {
          ...createNftDto,
          generationStatus: GenerationStatus.PENDING,
          createdBy: context.userId,
          updatedBy: context.userId
        }
      });

      // Create corresponding query model entry
      await this.prisma.nftQuery.create({
        data: {
          id: nft.id,
          displayName: nft.name,
          displayDescription: nft.description,
          userId: nft.userId,
          projectId: nft.projectId,
          campaignId: nft.campaignId,
          status: 'draft',
          viewCount: 0,
          likeCount: 0,
          shareCount: 0,
          isListed: false,
          totalSales: 0,
          popularityScore: 0.0,
          createdAt: nft.createdAt,
          lastUpdated: new Date()
        }
      });

      // Log audit trail
      await this.auditService.logAction({
        entityType: EntityType.NFT,
        entityId: nft.id,
        action: AuditAction.CREATE,
        newValues: nft,
        userId: context.userId,
        correlationId: context.correlationId,
        sessionId: context.sessionId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent
      });

      // Emit event
      await this.eventService.emitEvent({
        eventType: EventType.NFT_CREATED,
        aggregateId: nft.id,
        eventData: { nft, context },
        correlationId: context.correlationId,
        userId: context.userId
      });

      const responseTime = Date.now() - startTime;
      this.logger.log(`NFT created successfully in ${responseTime}ms`, context.correlationId);

      return nft;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`Failed to create NFT after ${responseTime}ms: ${error.message}`, error.stack, context.correlationId);
      throw error;
    }
  }

  async updateNft(id: string, updateNftDto: UpdateNftCommandDto, context: RequestContext) {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Updating NFT ${id}`, context.correlationId);

      // Get current NFT for audit trail
      const currentNft = await this.prisma.nftCommand.findUnique({ where: { id } });
      if (!currentNft) {
        throw { status: 404, code: 'NftNotFoundException', message: 'NFT not found' };
      }

      // Update command model
      const updatedNft = await this.prisma.nftCommand.update({
        where: { id },
        data: {
          ...updateNftDto,
          updatedBy: context.userId,
          version: { increment: 1 }
        }
      });

      // Update query model
      await this.prisma.nftQuery.update({
        where: { id },
        data: {
          displayName: updatedNft.name,
          displayDescription: updatedNft.description,
          status: this.mapGenerationStatusToNftStatus(updatedNft.generationStatus),
          lastUpdated: new Date()
        }
      });

      // Log audit trail
      await this.auditService.logAction({
        entityType: EntityType.NFT,
        entityId: id,
        action: AuditAction.UPDATE,
        oldValues: currentNft,
        newValues: updatedNft,
        userId: context.userId,
        correlationId: context.correlationId
      });

      // Emit event
      await this.eventService.emitEvent({
        eventType: EventType.NFT_UPDATED,
        aggregateId: id,
        eventData: { oldValues: currentNft, newValues: updatedNft, context },
        correlationId: context.correlationId,
        userId: context.userId
      });

      const responseTime = Date.now() - startTime;
      this.logger.log(`NFT updated successfully in ${responseTime}ms`, context.correlationId);

      return updatedNft;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`Failed to update NFT after ${responseTime}ms: ${error.message}`, error.stack, context.correlationId);
      throw error;
    }
  }

  async startGeneration(id: string, context: RequestContext) {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Starting generation for NFT ${id}`, context.correlationId);

      // Update status to generating
      const updatedNft = await this.prisma.nftCommand.update({
        where: { id },
        data: {
          generationStatus: GenerationStatus.GENERATING,
          updatedBy: context.userId
        }
      });

      // Update query model
      await this.prisma.nftQuery.update({
        where: { id },
        data: {
          status: 'generated',
          lastUpdated: new Date()
        }
      });

      // Emit event
      await this.eventService.emitEvent({
        eventType: EventType.GENERATION_STARTED,
        aggregateId: id,
        eventData: { nft: updatedNft, context },
        correlationId: context.correlationId,
        userId: context.userId
      });

      const responseTime = Date.now() - startTime;
      this.logger.log(`Generation started successfully in ${responseTime}ms`, context.correlationId);

      return { id, status: 'generation_started', message: 'NFT generation has been queued' };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`Failed to start generation after ${responseTime}ms: ${error.message}`, error.stack, context.correlationId);
      throw error;
    }
  }

  /**
   * Generate NFT with external storage integration
   */
  async generateNftWithExternalStorage(id: string, analysisData: any, context: RequestContext) {
    const startTime = Date.now();

    try {
      this.logger.log(`Generating NFT with external storage for ${id}`, context.correlationId);

      // Get NFT data
      const nft = await this.prisma.nftCommand.findUnique({ where: { id } });
      if (!nft) {
        throw { status: 404, code: 'NftNotFoundException', message: 'NFT not found' };
      }

      // Update status to generating
      await this.prisma.nftCommand.update({
        where: { id },
        data: {
          generationStatus: GenerationStatus.GENERATING,
          updatedBy: context.userId
        }
      });

      // Generate NFT image (placeholder for now - in production this would be actual image generation)
      const imageBuffer = await this.generateNftImage(nft, analysisData);
      const filename = `nft_${id}_${Date.now()}.png`;

      // Upload image to external storage
      const imageUploadResult = await this.externalStorageService.uploadImage(imageBuffer, filename);

      if (!imageUploadResult.success) {
        throw new Error(`Failed to upload image: ${imageUploadResult.error}`);
      }

      // Create NFT metadata
      const metadata: NFTMetadata = {
        name: nft.name,
        description: nft.description,
        image: imageUploadResult.imageUri,
        attributes: this.generateNftAttributes(analysisData),
        external_url: `${process.env.FRONTEND_URL}/nft/${id}`
      };

      // Upload metadata to external storage
      const metadataUploadResult = await this.externalStorageService.uploadMetadata(metadata);

      if (!metadataUploadResult.success) {
        throw new Error(`Failed to upload metadata: ${metadataUploadResult.error}`);
      }

      // Update NFT with external storage URLs
      const updatedNft = await this.prisma.nftCommand.update({
        where: { id },
        data: {
          imageUrl: imageUploadResult.imageUri,
          metadataUrl: metadataUploadResult.metadataUri,
          ipfsHash: metadataUploadResult.ipfsHash,
          generationStatus: GenerationStatus.COMPLETED,
          updatedBy: context.userId,
          version: { increment: 1 }
        }
      });

      // Update query model
      await this.prisma.nftQuery.update({
        where: { id },
        data: {
          status: 'generated',
          lastUpdated: new Date()
        }
      });

      // Emit completion event
      await this.eventService.emitEvent({
        eventType: EventType.GENERATION_COMPLETED,
        aggregateId: id,
        eventData: {
          nft: updatedNft,
          imageUri: imageUploadResult.imageUri,
          metadataUri: metadataUploadResult.metadataUri,
          context
        },
        correlationId: context.correlationId,
        userId: context.userId
      });

      const responseTime = Date.now() - startTime;
      this.logger.log(`NFT generated with external storage successfully in ${responseTime}ms`, context.correlationId);

      return {
        id,
        imageUri: imageUploadResult.imageUri,
        metadataUri: metadataUploadResult.metadataUri,
        ipfsHash: metadataUploadResult.ipfsHash,
        status: 'completed'
      };
    } catch (error) {
      // Update status to failed
      await this.prisma.nftCommand.update({
        where: { id },
        data: {
          generationStatus: GenerationStatus.FAILED,
          updatedBy: context.userId
        }
      });

      const responseTime = Date.now() - startTime;
      this.logger.error(`Failed to generate NFT with external storage after ${responseTime}ms: ${error.message}`, error.stack, context.correlationId);
      throw error;
    }
  }

  /**
   * Generate NFT image using real image generation service
   */
  private async generateNftImage(nft: any, analysisData: any): Promise<Buffer> {
    try {
      this.logger.log(`Generating real NFT image for ${nft.name}`);

      // Extract Twitter handle from analysis data
      const twitterHandle = analysisData.twitterHandle ||
                           analysisData.analysisData?.twitterHandle ||
                           'unknown';

      // Prepare image generation parameters
      const imageParams: NFTImageGenerationParams = {
        rarity: nft.rarity || analysisData.analysisData?.nftRecommendation?.rarity || 'common',
        score: analysisData.score || 50,
        twitterHandle: twitterHandle,
        analysisData: {
          profile: {
            followerCount: analysisData.analysisData?.profile?.followerCount || 0,
            followingCount: analysisData.analysisData?.profile?.followingCount || 0,
            tweetCount: analysisData.analysisData?.profile?.tweetCount || 0,
            engagementRate: analysisData.analysisData?.profile?.engagementRate || 0,
            isVerified: analysisData.analysisData?.profile?.isVerified || false,
            hasProfileImage: analysisData.analysisData?.profile?.hasProfileImage || false,
          },
          metrics: {
            contentQuality: analysisData.analysisData?.metrics?.contentQuality || 50,
            activityLevel: analysisData.analysisData?.metrics?.activityLevel || 50,
            influenceScore: analysisData.analysisData?.metrics?.influenceScore || 50,
            authenticity: analysisData.analysisData?.metrics?.authenticity || 50,
            engagement: analysisData.analysisData?.metrics?.engagement || 50,
          },
          breakdown: {
            followerScore: analysisData.analysisData?.breakdown?.followerScore || 50,
            engagementScore: analysisData.analysisData?.breakdown?.engagementScore || 50,
            contentScore: analysisData.analysisData?.breakdown?.contentScore || 50,
            activityScore: analysisData.analysisData?.breakdown?.activityScore || 50,
            profileScore: analysisData.analysisData?.breakdown?.profileScore || 50,
          },
        },
        customization: {
          style: 'modern',
          theme: 'social',
          colorScheme: 'auto',
        },
      };

      // Generate the actual NFT image
      const generatedImage = await this.nftImageGenerator.generateNFTImage(imageParams);

      this.logger.log(`NFT image generated successfully: ${generatedImage.filename} (${generatedImage.imageBuffer.length} bytes)`);

      return generatedImage.imageBuffer;

    } catch (error) {
      this.logger.error(`Failed to generate NFT image: ${error.message}`, error.stack);

      // Fallback to a simple placeholder if image generation fails
      this.logger.warn('Falling back to placeholder image due to generation failure');
      return this.generateFallbackImage(nft, analysisData);
    }
  }

  /**
   * Generate a simple fallback image if real generation fails
   */
  private async generateFallbackImage(nft: any, analysisData: any): Promise<Buffer> {
    // Create a simple colored rectangle as fallback
    const { createCanvas } = require('canvas');
    const canvas = createCanvas(400, 400);
    const ctx = canvas.getContext('2d');

    // Get rarity color
    const rarityColors = {
      common: '#6B7280',
      rare: '#3B82F6',
      epic: '#8B5CF6',
      legendary: '#F59E0B',
      mythic: '#EF4444',
    };

    const rarity = nft.rarity || 'common';
    const color = rarityColors[rarity.toLowerCase()] || rarityColors.common;

    // Fill background
    ctx.fillStyle = color;
    ctx.fillRect(0, 0, 400, 400);

    // Add text
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(rarity.toUpperCase(), 200, 180);
    ctx.fillText('NFT', 200, 220);

    // Add score if available
    if (analysisData.score) {
      ctx.font = 'bold 32px Arial';
      ctx.fillText(analysisData.score.toString(), 200, 260);
    }

    return canvas.toBuffer('image/png');
  }

  /**
   * Generate NFT attributes based on analysis data
   */
  private generateNftAttributes(analysisData: any): Array<{ trait_type: string; value: string | number }> {
    const attributes = [];

    if (analysisData.engagementScore) {
      attributes.push({
        trait_type: 'Engagement Score',
        value: analysisData.engagementScore
      });
    }

    if (analysisData.influenceLevel) {
      attributes.push({
        trait_type: 'Influence Level',
        value: analysisData.influenceLevel
      });
    }

    if (analysisData.rarity) {
      attributes.push({
        trait_type: 'Rarity',
        value: analysisData.rarity
      });
    }

    // Add timestamp
    attributes.push({
      trait_type: 'Generated At',
      value: new Date().toISOString()
    });

    return attributes;
  }

  private mapGenerationStatusToNftStatus(generationStatus: string): string {
    switch (generationStatus) {
      case GenerationStatus.PENDING: return 'draft';
      case GenerationStatus.GENERATING: return 'generated';
      case GenerationStatus.COMPLETED: return 'generated';
      case GenerationStatus.FAILED: return 'draft';
      default: return 'draft';
    }
  }
}
