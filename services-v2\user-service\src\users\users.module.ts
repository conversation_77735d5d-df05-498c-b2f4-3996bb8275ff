/**
 * Users Module - User Service V2
 * 
 * Implements shared infrastructure following SHARED-INFRASTRUCTURE-IMPLEMENTATION-PLAN.md
 * Uses StandardizedPrismaService, ResponseService, ServiceLoggerService from shared infrastructure
 */

import { Module } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';

@Module({
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
