{"name": "@social-nft-platform/shared-infrastructure", "version": "1.0.0", "description": "Shared infrastructure components implementing the Microservice Chassis Pattern for the Social NFT Platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["microservices", "<PERSON><PERSON><PERSON>", "shared-infrastructure", "chassis-pattern", "authentication", "logging", "response-standardization", "social-nft-platform"], "author": "Social NFT Platform Team", "license": "MIT", "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@prisma/client": "^5.0.0", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0"}, "devDependencies": {"@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@types/passport-jwt": "^3.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "rimraf": "^5.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0"}, "files": ["dist/**/*", "README.md", "package.json"], "repository": {"type": "git", "url": "https://github.com/social-nft-platform/shared-infrastructure.git"}, "bugs": {"url": "https://github.com/social-nft-platform/shared-infrastructure/issues"}, "homepage": "https://github.com/social-nft-platform/shared-infrastructure#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "publishConfig": {"access": "restricted"}, "dependencies": {"@nestjs/core": "^10.4.19", "@nestjs/swagger": "^11.2.0", "@nestjs/testing": "^11.1.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "prom-client": "^15.1.3", "winston": "^3.17.0"}}