import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo() {
    return {
      name: 'Mock Twitter Service',
      version: '1.0.0',
      description: 'Mock Twitter API for Social NFT Platform Development',
      port: 3020,
      environment: process.env.NODE_ENV || 'development',
      type: 'mock',
      endpoints: {
        health: '/health',
        docs: '/api/docs',
        auth: '/auth',
        users: '/users',
        analytics: '/analytics'
      },
      timestamp: new Date().toISOString()
    };
  }

  healthCheck() {
    return {
      status: 'healthy',
      service: 'mock-twitter-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0'
    };
  }
}
