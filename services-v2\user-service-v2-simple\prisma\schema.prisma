// User Service V2 Simple Database Schema
// Database Per Service Pattern - This is the USER SERVICE database only

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management Tables (User Service Domain)
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  
  // Profile Information
  firstName String?
  lastName  String?
  avatar    String?
  bio       String?
  
  // Account Status
  isActive     Boolean @default(true)
  isVerified   Boolean @default(false)
  emailVerified <PERSON>olean @default(false)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLogin DateTime?
  
  @@map("users")
}
