// User Service V2 Simple Database Schema
// Database Per Service Pattern - This is the USER SERVICE database only

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management Tables (User Service Domain)
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String

  // Profile Information
  firstName String? @map("first_name")
  lastName  String? @map("last_name")
  avatar    String?
  bio       String?

  // Account Status
  isActive     Boolean @default(true) @map("is_active")
  isVerified   Boolean @default(false) @map("is_verified")
  emailVerified Boolean @default(false) @map("email_verified")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  lastLogin DateTime? @map("last_login")

  @@map("users")
}
