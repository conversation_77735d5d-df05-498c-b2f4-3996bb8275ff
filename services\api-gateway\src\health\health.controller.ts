import { <PERSON>, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { 
  HealthCheckService, 
  HealthCheck, 
  HttpHealthIndicator,
  HealthCheckResult 
} from '@nestjs/terminus';
import { GatewayHealthService } from './gateway-health.service';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    private readonly health: HealthCheckService,
    private readonly http: HttpHealthIndicator,
    private readonly gatewayHealth: GatewayHealthService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Comprehensive health check' })
  @ApiResponse({ 
    status: 200, 
    description: 'Health check completed successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        info: { type: 'object' },
        error: { type: 'object' },
        details: { type: 'object' }
      }
    }
  })
  @HealthCheck()
  check(): Promise<HealthCheckResult> {
    this.logger.log('Comprehensive health check requested');
    
    return this.health.check([
      () => this.gatewayHealth.isHealthy('gateway'),
      () => this.gatewayHealth.checkAllServices(),
    ]);
  }

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check' })
  @ApiResponse({ 
    status: 200, 
    description: 'Simple health status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        uptime: { type: 'number', example: 12345 }
      }
    }
  })
  simpleCheck() {
    this.logger.log('Simple health check requested');
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      service: 'api-gateway',
      version: '1.0.0',
    };
  }

  @Get('services')
  @ApiOperation({ summary: 'Check all downstream services health' })
  @ApiResponse({ 
    status: 200, 
    description: 'Services health check completed',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        services: { type: 'object' }
      }
    }
  })
  @HealthCheck()
  checkServices(): Promise<HealthCheckResult> {
    this.logger.log('Services health check requested');
    
    return this.health.check([
      () => this.gatewayHealth.checkAllServices(),
    ]);
  }
}
