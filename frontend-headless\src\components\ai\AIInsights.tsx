'use client'

import React, { useState } from 'react'
import {
  LightBulbIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  CubeIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  MinusIcon
} from '@heroicons/react/24/outline'

interface AIInsightsProps {
  userId: string
  insights?: any
  isLoading?: boolean
  marketSentiment?: any
  portfolioOptimization?: any
  riskAssessment?: any
  className?: string
}

export default function AIInsights({
  userId,
  insights,
  isLoading = false,
  marketSentiment,
  portfolioOptimization,
  riskAssessment,
  className = ''
}: AIInsightsProps) {
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'portfolio' | 'market' | 'social' | 'risk'>('all')

  const categories = [
    { value: 'all', label: 'All Insights' },
    { value: 'portfolio', label: 'Portfolio' },
    { value: 'market', label: 'Market' },
    { value: 'social', label: 'Social' },
    { value: 'risk', label: 'Risk' }
  ]

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity':
        return <TrendingUpIcon className="h-5 w-5 text-green-600" />
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
      case 'risk':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
      case 'tip':
        return <LightBulbIcon className="h-5 w-5 text-blue-600" />
      case 'achievement':
        return <CheckCircleIcon className="h-5 w-5 text-purple-600" />
      default:
        return <InformationCircleIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'opportunity':
        return 'border-green-200 bg-green-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      case 'risk':
        return 'border-red-200 bg-red-50'
      case 'tip':
        return 'border-blue-200 bg-blue-50'
      case 'achievement':
        return 'border-purple-200 bg-purple-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUpIcon className="h-4 w-4 text-green-600" />
      case 'down':
        return <ArrowDownIcon className="h-4 w-4 text-red-600" />
      default:
        return <MinusIcon className="h-4 w-4 text-gray-600" />
    }
  }

  // Mock insights data if not provided
  const mockInsights = [
    {
      id: '1',
      type: 'opportunity',
      category: 'portfolio',
      title: 'Portfolio Diversification Opportunity',
      description: 'Your portfolio is heavily concentrated in art NFTs. Consider diversifying into gaming or utility NFTs.',
      confidence: 85,
      impact: 'high',
      actionable: true,
      action: 'View Recommendations',
      createdAt: new Date().toISOString()
    },
    {
      id: '2',
      type: 'warning',
      category: 'market',
      title: 'Market Volatility Alert',
      description: 'Increased volatility detected in the NFT market. Consider reducing position sizes.',
      confidence: 92,
      impact: 'medium',
      actionable: true,
      action: 'Adjust Portfolio',
      createdAt: new Date().toISOString()
    },
    {
      id: '3',
      type: 'tip',
      category: 'social',
      title: 'Engagement Optimization',
      description: 'Your posts perform 40% better when posted between 2-4 PM. Consider scheduling content during this time.',
      confidence: 78,
      impact: 'medium',
      actionable: true,
      action: 'Schedule Posts',
      createdAt: new Date().toISOString()
    },
    {
      id: '4',
      type: 'achievement',
      category: 'portfolio',
      title: 'Portfolio Milestone',
      description: 'Congratulations! Your portfolio has outperformed the market by 15% this month.',
      confidence: 100,
      impact: 'high',
      actionable: false,
      createdAt: new Date().toISOString()
    }
  ]

  const displayInsights = insights?.insights || mockInsights
  const filteredInsights = selectedCategory === 'all' 
    ? displayInsights 
    : displayInsights.filter((insight: any) => insight.category === selectedCategory)

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <LightBulbIcon className="h-6 w-6 mr-2 text-blue-600" />
            AI Insights
          </h2>
          <p className="text-sm text-gray-600">
            Intelligent analysis and actionable recommendations
          </p>
        </div>
        
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
        >
          {categories.map((category) => (
            <option key={category.value} value={category.value}>
              {category.label}
            </option>
          ))}
        </select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Portfolio Score */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Portfolio Score</h3>
            <ChartBarIcon className="h-5 w-5 text-blue-600" />
          </div>
          
          <div className="space-y-2">
            <div className="text-2xl font-bold text-gray-900">
              {portfolioOptimization?.score || 85}/100
            </div>
            
            <div className="flex items-center text-sm text-green-600">
              {getTrendIcon('up')}
              <span className="ml-1">+5.2% this week</span>
            </div>
            
            <p className="text-xs text-gray-600">
              {portfolioOptimization?.recommendation || 'Well diversified portfolio'}
            </p>
          </div>
        </div>

        {/* Market Sentiment */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Market Sentiment</h3>
            <CubeIcon className="h-5 w-5 text-purple-600" />
          </div>
          
          <div className="space-y-2">
            <div className="text-2xl font-bold text-gray-900">
              {marketSentiment?.overall || 'Bullish'}
            </div>
            
            <div className="flex items-center text-sm text-green-600">
              {getTrendIcon(marketSentiment?.trend || 'up')}
              <span className="ml-1">{marketSentiment?.change || '+12%'}</span>
            </div>
            
            <p className="text-xs text-gray-600">
              Based on {marketSentiment?.sources || 1247} data points
            </p>
          </div>
        </div>

        {/* Risk Level */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Risk Level</h3>
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
          </div>
          
          <div className="space-y-2">
            <div className="text-2xl font-bold text-gray-900">
              {riskAssessment?.overallRisk || 'Medium'}
            </div>
            
            <div className="flex items-center text-sm text-gray-600">
              {getTrendIcon('stable')}
              <span className="ml-1">Stable</span>
            </div>
            
            <p className="text-xs text-gray-600">
              {riskAssessment?.recommendation || 'Within acceptable range'}
            </p>
          </div>
        </div>

        {/* Social Score */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Social Score</h3>
            <UserGroupIcon className="h-5 w-5 text-green-600" />
          </div>
          
          <div className="space-y-2">
            <div className="text-2xl font-bold text-gray-900">
              {insights?.socialScore || 78}/100
            </div>
            
            <div className="flex items-center text-sm text-green-600">
              {getTrendIcon('up')}
              <span className="ml-1">+8.3% this month</span>
            </div>
            
            <p className="text-xs text-gray-600">
              Strong community engagement
            </p>
          </div>
        </div>
      </div>

      {/* Insights List */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">
          Insights & Recommendations ({filteredInsights.length})
        </h3>
        
        {filteredInsights.length > 0 ? (
          <div className="space-y-4">
            {filteredInsights.map((insight: any) => (
              <InsightCard
                key={insight.id}
                insight={insight}
                getIcon={getInsightIcon}
                getColor={getInsightColor}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
            <LightBulbIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No insights available</h3>
            <p className="mt-1 text-sm text-gray-500">
              {selectedCategory !== 'all'
                ? `No ${selectedCategory} insights found. Try selecting a different category.`
                : 'Check back later for AI-generated insights and recommendations.'}
            </p>
          </div>
        )}
      </div>

      {/* AI Analysis Summary */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <LightBulbIcon className="h-5 w-5 mr-2 text-blue-600" />
          AI Analysis Summary
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Key Strengths</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Strong portfolio diversification</li>
              <li>• Consistent social engagement</li>
              <li>• Good risk management</li>
              <li>• Active community participation</li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Improvement Areas</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Consider emerging NFT categories</li>
              <li>• Optimize posting schedule</li>
              <li>• Monitor market volatility</li>
              <li>• Explore new communities</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-4 pt-4 border-t border-blue-200">
          <div className="flex items-center justify-between text-sm">
            <div className="text-gray-600">
              Analysis based on {insights?.dataPoints || 15847} data points
            </div>
            <div className="text-gray-600">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface InsightCardProps {
  insight: any
  getIcon: (type: string) => React.ReactNode
  getColor: (type: string) => string
}

function InsightCard({ insight, getIcon, getColor }: InsightCardProps) {
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    return `${Math.floor(diffInHours / 24)}d ago`
  }

  return (
    <div className={`border rounded-lg p-4 ${getColor(insight.type)}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex-shrink-0">
            {getIcon(insight.type)}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-sm font-medium text-gray-900">{insight.title}</h3>
              
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                insight.confidence >= 80 ? 'bg-green-100 text-green-800' :
                insight.confidence >= 60 ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {insight.confidence}% confidence
              </span>
              
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                insight.impact === 'high' ? 'bg-red-100 text-red-800' :
                insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {insight.impact} impact
              </span>
            </div>
            
            <p className="text-sm text-gray-700 mb-2">{insight.description}</p>
            
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <span className="capitalize">{insight.category}</span>
              <span>{formatTimeAgo(insight.createdAt)}</span>
            </div>
          </div>
        </div>

        {/* Action Button */}
        {insight.actionable && insight.action && (
          <button className="ml-4 inline-flex items-center px-3 py-1 border border-blue-600 text-xs font-medium rounded text-blue-600 bg-white hover:bg-blue-50">
            {insight.action}
          </button>
        )}
      </div>
    </div>
  )
}
