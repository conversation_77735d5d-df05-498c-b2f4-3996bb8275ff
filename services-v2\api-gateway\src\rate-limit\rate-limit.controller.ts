import { Controller, Get, Post, Delete, Param } from '@nestjs/common';
import {
  ResponseService,
  RequirePermissions,
  Permission,
  Public
} from '../../../../shared';
import { RateLimitService } from './rate-limit.service';

/**
 * Rate Limit Controller
 * 
 * Provides REST API for rate limit monitoring and management.
 */
@Controller('rate-limit')
export class RateLimitController {
  constructor(
    private readonly rateLimitService: RateLimitService,
    private readonly responseService: ResponseService
  ) {}

  /**
   * Get rate limit statistics
   */
  @Get('stats')
  @RequirePermissions(Permission.SYSTEM_ADMIN)
  async getStats() {
    const stats = this.rateLimitService.getStats();
    return this.responseService.success(stats, 'Rate limit statistics retrieved');
  }

  /**
   * Get all rate limit entries
   */
  @Get('limits')
  @RequirePermissions(Permission.SYSTEM_ADMIN)
  async getAllLimits() {
    const limits = this.rateLimitService.getAllLimits();
    const limitsArray = Array.from(limits.entries()).map(([key, entry]) => ({
      key,
      ...entry,
    }));

    return this.responseService.success(limitsArray, 'Rate limit entries retrieved');
  }

  /**
   * Get rate limit status for a specific key
   */
  @Get('status/:key')
  @Public() // Allow internal monitoring
  async getStatus(@Param('key') key: string) {
    const status = await this.rateLimitService.getStatus(key);
    
    if (!status) {
      return this.responseService.notFound('Rate limit entry', key);
    }

    return this.responseService.success(status, `Rate limit status for ${key}`);
  }

  /**
   * Reset rate limit for a specific key
   */
  @Post('reset/:key')
  @RequirePermissions(Permission.SYSTEM_ADMIN)
  async resetLimit(@Param('key') key: string) {
    await this.rateLimitService.resetLimit(key);
    return this.responseService.success(
      { key, action: 'reset' },
      `Rate limit for ${key} has been reset`
    );
  }

  /**
   * Clear all rate limits
   */
  @Post('clear')
  @RequirePermissions(Permission.SYSTEM_ADMIN)
  async clearAll() {
    await this.rateLimitService.clearAll();
    return this.responseService.success(
      { action: 'clear', scope: 'all' },
      'All rate limits have been cleared'
    );
  }

  /**
   * Health check for rate limit system
   */
  @Get('health')
  @Public()
  async health() {
    const healthStatus = this.rateLimitService.getHealthStatus();
    return this.responseService.success(healthStatus, 'Rate limit system is healthy');
  }
}
