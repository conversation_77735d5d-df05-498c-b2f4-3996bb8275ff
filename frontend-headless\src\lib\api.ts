import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { storage } from './utils'

// API Configuration - All requests go through API Gateway
const API_GATEWAY_URL = process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://localhost:3010'

// All requests route through API Gateway - Single Entry Point
const API_BASE_URL = API_GATEWAY_URL + '/api'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = storage.get('auth_token', null)
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Clear auth token on unauthorized
      storage.remove('auth_token')
      storage.remove('user')
      // Redirect to login if in browser
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
    }
    return Promise.reject(error)
  }
)

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  success: boolean
  data: {
    results: T[]
    totalCount: number
    page: number
    limit: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Generic API methods
export const api = {
  // GET request
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.get(url, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },

  // POST request
  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.post(url, data, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },

  // PUT request
  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.put(url, data, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },

  // PATCH request
  patch: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.patch(url, data, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },

  // DELETE request
  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.delete(url, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },
}

// Auth API
export const authApi = {
  register: (data: {
    username: string
    email: string
    password: string
    confirmPassword: string
    displayName?: string
  }) => api.post('/auth/register', data),

  login: (data: {
    emailOrUsername: string
    password: string
  }) => api.post('/auth/login', data),

  // Twitter OAuth endpoints
  twitterAuth: () => api.get('/auth/twitter'),
  twitterCallback: (code: string, state: string) => api.post('/auth/twitter/callback', { code, state }),

  // Token validation
  validateToken: (token: string) => api.post('/auth/validate-token', { token }),

  logout: () => api.post('/auth/logout'),

  refreshToken: () => api.post('/auth/refresh'),

  getProfile: () => api.get('/auth/profile'),

  updateProfile: (data: any) => api.patch('/auth/profile', data),
}

// User API
export const userApi = {
  getUsers: (params?: {
    page?: number
    limit?: number
    search?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }) => api.get('/users', { params }),

  getUser: (id: string) => api.get(`/users/${id}`),

  updateUser: (id: string, data: any) => api.patch(`/users/${id}`, data),

  deleteUser: (id: string) => api.delete(`/users/${id}`),

  getUserStats: (id: string) => api.get(`/users/${id}/stats`),
}

// Campaign API
export const campaignApi = {
  getCampaigns: (params?: {
    page?: number
    limit?: number
    search?: string
    status?: string
    type?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }) => api.get('/campaigns', { params }),

  getCampaign: (id: string) => api.get(`/campaigns/${id}`),

  createCampaign: (data: any) => api.post('/campaigns', data),

  updateCampaign: (id: string, data: any) => api.patch(`/campaigns/${id}`, data),

  deleteCampaign: (id: string) => api.delete(`/campaigns/${id}`),

  joinCampaign: (id: string) => api.post(`/campaigns/${id}/join`),

  leaveCampaign: (id: string) => api.post(`/campaigns/${id}/leave`),

  getCampaignParticipants: (id: string) => api.get(`/campaigns/${id}/participants`),
}

// NFT API - All requests through API Gateway
export const nftApi = {
  // Enhanced NFT Collection Management
  getUserCollection: (userId: string, params?: {
    rarity?: string
    status?: string
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }) => api.get(`/users/${userId}/nfts`, { params }),

  getNFTDetails: (userId: string, nftId: string) => api.get(`/users/${userId}/nfts/${nftId}`),

  updateNFT: (userId: string, nftId: string, updates: any) => api.put(`/users/${userId}/nfts/${nftId}`, updates),

  getNFTAnalytics: (userId: string) => api.get(`/users/${userId}/nfts/analytics`),

  // My Collection (authenticated user)
  getMyCollection: (params?: {
    rarity?: string
    status?: string
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }) => api.get('/nfts/my/collection', { params }),

  // Legacy endpoints (keep for backward compatibility)
  getUserNFTs: (userId: string) => api.get(`/nfts/user/${userId}/history?limit=50`),

  // Generate NFT from analysis via API Gateway
  generateNFTFromAnalysis: (data: {
    userId: string
    analysisId: string
    customization?: {
      style?: string
      theme?: string
    }
  }) => api.post('/nfts/generate-from-analysis', data),

  // Standard NFT operations via API Gateway
  getNFTs: (params?: any) => api.get('/nfts', { params }),
  getNFT: (id: string) => api.get(`/nfts/${id}`),
  generateNFT: (data: any) => api.post('/nfts/generate', data),
  updateNFTById: (id: string, data: any) => api.patch(`/nfts/${id}`, data),
  deleteNFT: (id: string) => api.delete(`/nfts/${id}`),
  getCampaignNFTs: (campaignId: string) => api.get(`/nfts/campaign/${campaignId}`),

  // NFT Minting
  mintNFT: (nftId: string, data: {
    blockchain: string
    contractAddress?: string
    gasPrice?: string
  }) => api.post(`/nfts/${nftId}/mint`, data),

  // NFT Evolution and Score Updates
  updateNFTScore: (nftId: string, newScore: number) => api.patch(`/nfts/${nftId}/score`, { score: newScore }),

  getEvolutionHistory: (nftId: string) => api.get(`/nfts/${nftId}/evolution`),
}

// Enhanced Marketplace API
export const marketplaceApi = {
  // Listings
  getListings: (params?: {
    page?: number
    limit?: number
    search?: string
    minPrice?: number
    maxPrice?: number
    currency?: string
    rarity?: string
    listingType?: string
    status?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }) => api.get('/marketplace/listings', { params }),

  getListing: (id: string) => api.get(`/marketplace/listings/${id}`),

  createListing: (data: {
    nftId: string
    listingType: string
    price: number
    currency: string
    title: string
    description?: string
    tags?: string[]
    acceptOffers?: boolean
    minOfferAmount?: number
    auctionEndTime?: string
    startingPrice?: number
    reservePrice?: number
    buyNowPrice?: number
  }) => api.post('/marketplace/listings', data),

  updateListing: (id: string, data: any) => api.patch(`/marketplace/listings/${id}`, data),

  cancelListing: (id: string) => api.patch(`/marketplace/listings/${id}/cancel`),

  deleteListing: (id: string) => api.delete(`/marketplace/listings/${id}`),

  // Offers
  getOffers: (listingId: string) => api.get(`/marketplace/listings/${listingId}/offers`),

  createOffer: (data: {
    listingId: string
    amount: number
    currency: string
    message?: string
    expiresAt: string
  }) => api.post('/marketplace/offers', data),

  acceptOffer: (offerId: string) => api.patch(`/marketplace/offers/${offerId}/accept`),

  rejectOffer: (offerId: string) => api.patch(`/marketplace/offers/${offerId}/reject`),

  cancelOffer: (offerId: string) => api.patch(`/marketplace/offers/${offerId}/cancel`),

  // Transactions
  purchaseListing: (data: {
    listingId: string
    paymentMethod: string
    paymentTxHash?: string
  }) => api.post('/marketplace/transactions/purchase', data),

  getTransaction: (id: string) => api.get(`/marketplace/transactions/${id}`),

  getTransactions: (params?: {
    userId?: string
    type?: string
    status?: string
    page?: number
    limit?: number
  }) => api.get('/marketplace/transactions', { params }),

  // User Data
  getUserMarketplaceData: (userId?: string) => {
    const endpoint = userId ? `/marketplace/users/${userId}` : '/marketplace/user/me'
    return api.get(endpoint)
  },

  getUserListings: (userId?: string, status?: string) => {
    const endpoint = userId ? `/marketplace/users/${userId}/listings` : '/marketplace/user/me/listings'
    return api.get(endpoint, { params: { status } })
  },

  getUserOffers: (userId?: string, type?: string) => {
    const endpoint = userId ? `/marketplace/users/${userId}/offers` : '/marketplace/user/me/offers'
    return api.get(endpoint, { params: { type } })
  },

  // Analytics
  getAnalytics: (timeframe?: string) => api.get('/marketplace/analytics', { params: { timeframe } }),

  getNFTHistory: (nftId: string) => api.get(`/marketplace/nft/${nftId}/history`),

  // Utility
  searchListings: (query: string, filters?: any) => api.get('/marketplace/search', {
    params: { q: query, ...filters }
  }),

  getFeaturedListings: (limit = 10) => api.get('/marketplace/featured', { params: { limit } }),

  getTrendingListings: (timeframe = '24h', limit = 10) => api.get('/marketplace/trending', {
    params: { timeframe, limit }
  }),

  getRecentSales: (limit = 20) => api.get('/marketplace/recent-sales', { params: { limit } }),

  estimatePrice: (nftId: string) => api.get(`/marketplace/nft/${nftId}/price-estimate`),

  // Notifications
  getNotifications: () => api.get('/marketplace/notifications'),

  markNotificationAsRead: (notificationId: string) => api.patch(`/marketplace/notifications/${notificationId}/read`),
}

// Evolution API
export const evolutionApi = {
  // Timeline and Events
  getTimeline: (nftId: string, filters?: any) => api.get(`/evolution/nft/${nftId}/timeline`, { params: filters }),

  getEvents: (nftId: string, filters?: any) => api.get(`/evolution/nft/${nftId}/events`, { params: filters }),

  getEvent: (eventId: string) => api.get(`/evolution/events/${eventId}`),

  createEvent: (data: any) => api.post('/evolution/events', data),

  // Analytics
  getAnalytics: (nftId: string, timeframe?: string) => api.get(`/evolution/nft/${nftId}/analytics`, {
    params: { timeframe }
  }),

  getStats: (nftId: string) => api.get(`/evolution/nft/${nftId}/stats`),

  getInsights: (nftId: string) => api.get(`/evolution/nft/${nftId}/insights`),

  // Predictions
  getPrediction: (nftId: string) => api.get(`/evolution/nft/${nftId}/prediction`),

  updatePrediction: (nftId: string) => api.post(`/evolution/nft/${nftId}/prediction/update`),

  // Evolution Triggers
  triggerEvolution: (nftId: string, data: any) => api.post(`/evolution/nft/${nftId}/trigger`, data),

  updateScore: (nftId: string, data: any) => api.patch(`/evolution/nft/${nftId}/score`, data),

  // Comparative Analytics
  compareEvolutions: (nftIds: string[]) => api.post('/evolution/compare', { nftIds }),

  getGlobalStats: () => api.get('/evolution/global/stats'),

  // Notifications
  getNotifications: (userId?: string) => {
    const endpoint = userId ? `/evolution/users/${userId}/notifications` : '/evolution/notifications'
    return api.get(endpoint)
  },

  markNotificationAsRead: (notificationId: string) => api.patch(`/evolution/notifications/${notificationId}/read`),

  // History and Search
  getHistory: (filters?: any) => api.get('/evolution/history', { params: filters }),

  exportData: (nftId: string, format: string) => api.get(`/evolution/nft/${nftId}/export`, {
    params: { format },
    responseType: 'blob'
  }),

  searchEvents: (query: string, filters?: any) => api.get('/evolution/search', {
    params: { q: query, ...filters }
  }),

  // Milestones
  getMilestones: (nftId: string) => api.get(`/evolution/nft/${nftId}/milestones`),

  claimReward: (nftId: string, milestoneId: string) => api.post(`/evolution/nft/${nftId}/milestones/${milestoneId}/claim`),

  // Leaderboard
  getLeaderboard: (timeframe = '30d', limit = 50) => api.get('/evolution/leaderboard', {
    params: { timeframe, limit }
  }),
}

// Campaign API
export const campaignApi = {
  // CRUD Operations
  getCampaigns: (filters?: any) => api.get('/campaigns', { params: filters }),

  getCampaign: (id: string) => api.get(`/campaigns/${id}`),

  createCampaign: (data: any) => api.post('/campaigns', data),

  updateCampaign: (id: string, data: any) => api.patch(`/campaigns/${id}`, data),

  deleteCampaign: (id: string) => api.delete(`/campaigns/${id}`),

  // Status Management
  startCampaign: (id: string) => api.patch(`/campaigns/${id}/start`),

  pauseCampaign: (id: string) => api.patch(`/campaigns/${id}/pause`),

  resumeCampaign: (id: string) => api.patch(`/campaigns/${id}/resume`),

  completeCampaign: (id: string) => api.patch(`/campaigns/${id}/complete`),

  cancelCampaign: (id: string, reason?: string) => api.patch(`/campaigns/${id}/cancel`, { reason }),

  // Participation
  getParticipants: (campaignId: string, filters?: any) => api.get(`/campaigns/${campaignId}/participants`, { params: filters }),

  joinCampaign: (campaignId: string, data: any) => api.post(`/campaigns/${campaignId}/join`, data),

  leaveCampaign: (campaignId: string) => api.delete(`/campaigns/${campaignId}/leave`),

  getUserParticipation: (campaignId: string, userId?: string) => {
    const endpoint = userId
      ? `/campaigns/${campaignId}/participants/${userId}`
      : `/campaigns/${campaignId}/participation`
    return api.get(endpoint)
  },

  // Submissions
  submitRequirement: (campaignId: string, requirementId: string, data: any) =>
    api.post(`/campaigns/${campaignId}/requirements/${requirementId}/submit`, data),

  getSubmissions: (campaignId: string, filters?: any) =>
    api.get(`/campaigns/${campaignId}/submissions`, { params: filters }),

  reviewSubmission: (submissionId: string, data: any) =>
    api.patch(`/submissions/${submissionId}/review`, data),

  // Analytics
  getAnalytics: (campaignId: string, timeframe?: string) =>
    api.get(`/campaigns/${campaignId}/analytics`, { params: { timeframe } }),

  getMetrics: (campaignId: string) => api.get(`/campaigns/${campaignId}/metrics`),

  exportData: (campaignId: string, format: string) => api.get(`/campaigns/${campaignId}/export`, {
    params: { format },
    responseType: 'blob'
  }),

  // Templates
  getTemplates: (filters?: any) => api.get('/campaigns/templates', { params: filters }),

  createFromTemplate: (templateId: string, data: any) =>
    api.post(`/campaigns/templates/${templateId}/create`, data),

  // Utility
  searchCampaigns: (query: string, filters?: any) => api.get('/campaigns/search', {
    params: { q: query, ...filters }
  }),

  getFeatured: (limit = 10) => api.get('/campaigns/featured', { params: { limit } }),

  getActive: (limit = 20) => api.get('/campaigns/active', { params: { limit } }),

  getProjectCampaigns: (projectId: string, filters?: any) =>
    api.get(`/projects/${projectId}/campaigns`, { params: filters }),

  validateData: (data: any) => api.post('/campaigns/validate', data),

  previewCampaign: (data: any) => api.post('/campaigns/preview', data),
}

// Search API
export const searchApi = {
  search: (params: {
    query?: string
    type: 'global' | 'users' | 'campaigns' | 'nfts' | 'marketplace'
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    includeFacets?: boolean
    includeSuggestions?: boolean
  }) => api.get('/search', { params }),

  autocomplete: (params: {
    query: string
    type: 'global' | 'users' | 'campaigns' | 'nfts' | 'marketplace'
    limit?: number
  }) => api.get('/search/autocomplete', { params }),

  getPopularSearches: (params?: {
    type?: string
    limit?: number
  }) => api.get('/search/popular', { params }),

  getSearchAnalytics: (params?: {
    timeframe?: string
    type?: string
  }) => api.get('/search/analytics', { params }),
}

// Profile Analysis API - All requests through API Gateway
export const analysisApi = {
  // Analyze Twitter profile via API Gateway
  analyzeTwitterProfile: (data: {
    twitterHandle: string
    userId: string
    analysisType?: string
  }) => api.post('/analysis/twitter-profile', data),

  // Get user's analysis history via API Gateway
  getUserAnalysisHistory: (userId: string, limit: number = 10) =>
    api.get(`/analysis/history?userId=${userId}&limit=${limit}`),
}

// Analytics API
export const analyticsApi = {
  getDashboardStats: () => api.get('/analytics/dashboard'),

  getUserAnalytics: (userId: string, timeframe?: string) =>
    api.get(`/analytics/users/${userId}`, { params: { timeframe } }),

  getCampaignAnalytics: (campaignId: string, timeframe?: string) =>
    api.get(`/analytics/campaigns/${campaignId}`, { params: { timeframe } }),

  getPlatformAnalytics: (timeframe?: string) =>
    api.get('/analytics/platform', { params: { timeframe } }),

  getRealtimeMetrics: () => api.get('/analytics/realtime'),

  // New comprehensive analytics endpoints
  getPlatformOverview: () => api.get('/analytics/platform-overview'),

  getUserInsights: (userId: string) => api.get(`/analytics/user/${userId}/insights`),

  getNFTPerformance: (period: string = '30d', rarity?: string) =>
    api.get('/analytics/nft-performance', { params: { period, rarity } }),

  getEngagementMetrics: (period: string = '30d') =>
    api.get('/analytics/engagement-metrics', { params: { period } }),

  getTopGainersLosers: (period: string = '24h', limit: number = 5) =>
    api.get('/analytics/top-gainers-losers', { params: { period, limit } }),

  getCollectionMarketValue: () => api.get('/analytics/collection-market-value'),

  getRecentTransactions: (limit: number = 20) =>
    api.get('/analytics/recent-transactions', { params: { limit } }),

  trackEvent: (eventData: any) => api.post('/analytics/track-event', eventData),
}

export default apiClient
