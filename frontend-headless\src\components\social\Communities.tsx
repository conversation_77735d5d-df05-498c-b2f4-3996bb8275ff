'use client'

import React, { useState } from 'react'
import {
  UserGroupIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckCircleIcon,
  LockClosedIcon,
  GlobeAltIcon,
  EyeSlashIcon,
  UserPlusIcon,
  UserMinusIcon,
  Cog6ToothIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline'
import {
  useCommunities,
  useCommunity,
  useCreateCommunity,
  useJoinCommunity,
  useLeaveCommunity,
  useUserCommunities,
  useCommunityPosts
} from '@/hooks/useSocial'
import { Community, CommunityCategory, CommunityType, CommunityVisibility } from '@/types/social.types'
import CreateCommunityModal from './CreateCommunityModal'
import CommunityDetailModal from './CommunityDetailModal'

interface CommunitiesProps {
  className?: string
}

export default function Communities({
  className = ''
}: CommunitiesProps) {
  const [activeTab, setActiveTab] = useState<'discover' | 'my-communities' | 'joined'>('discover')
  const [selectedCategory, setSelectedCategory] = useState<CommunityCategory | 'all'>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedCommunity, setSelectedCommunity] = useState<Community | null>(null)

  const { data: communities, isLoading: communitiesLoading } = useCommunities(
    selectedCategory === 'all' ? undefined : selectedCategory,
    20,
    0
  )
  
  const { data: userCommunities, isLoading: userCommunitiesLoading } = useUserCommunities(undefined, 20, 0)
  
  const createCommunityMutation = useCreateCommunity()
  const joinCommunityMutation = useJoinCommunity()
  const leaveCommunityMutation = useLeaveCommunity()

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: CommunityCategory.GENERAL, label: 'General' },
    { value: CommunityCategory.TRADING, label: 'Trading' },
    { value: CommunityCategory.COLLECTING, label: 'Collecting' },
    { value: CommunityCategory.ART, label: 'Art' },
    { value: CommunityCategory.GAMING, label: 'Gaming' },
    { value: CommunityCategory.MUSIC, label: 'Music' },
    { value: CommunityCategory.SPORTS, label: 'Sports' },
    { value: CommunityCategory.TECHNOLOGY, label: 'Technology' },
    { value: CommunityCategory.EDUCATION, label: 'Education' },
    { value: CommunityCategory.ENTERTAINMENT, label: 'Entertainment' }
  ]

  const tabs = [
    { id: 'discover', name: 'Discover', count: communities?.totalCount },
    { id: 'my-communities', name: 'My Communities', count: userCommunities?.communities.filter(c => c.creator.id === 'current-user').length },
    { id: 'joined', name: 'Joined', count: userCommunities?.totalCount }
  ]

  const handleJoinCommunity = (communityId: string) => {
    joinCommunityMutation.mutate(communityId)
  }

  const handleLeaveCommunity = (communityId: string) => {
    if (confirm('Are you sure you want to leave this community?')) {
      leaveCommunityMutation.mutate(communityId)
    }
  }

  const getTypeIcon = (type: CommunityType) => {
    switch (type) {
      case CommunityType.PUBLIC:
        return <GlobeAltIcon className="h-4 w-4 text-green-600" />
      case CommunityType.PRIVATE:
        return <LockClosedIcon className="h-4 w-4 text-red-600" />
      case CommunityType.INVITE_ONLY:
        return <EyeSlashIcon className="h-4 w-4 text-yellow-600" />
      default:
        return <GlobeAltIcon className="h-4 w-4 text-gray-600" />
    }
  }

  const getVisibilityIcon = (visibility: CommunityVisibility) => {
    switch (visibility) {
      case CommunityVisibility.PUBLIC:
        return <GlobeAltIcon className="h-4 w-4 text-green-600" />
      case CommunityVisibility.UNLISTED:
        return <EyeSlashIcon className="h-4 w-4 text-yellow-600" />
      case CommunityVisibility.PRIVATE:
        return <LockClosedIcon className="h-4 w-4 text-red-600" />
      default:
        return <GlobeAltIcon className="h-4 w-4 text-gray-600" />
    }
  }

  const filteredCommunities = communities?.communities.filter(community =>
    searchQuery === '' || 
    community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    community.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    community.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  ) || []

  if (communitiesLoading && !communities) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Communities</h2>
          <p className="text-sm text-gray-600">Discover and join NFT communities</p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Community
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
              {tab.count !== undefined && (
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center justify-between space-x-4">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search communities..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {categories.map((category) => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>

        <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
          <FunnelIcon className="h-4 w-4 mr-2" />
          Filters
        </button>
      </div>

      {/* Communities Grid */}
      {activeTab === 'discover' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredCommunities.map((community) => (
            <CommunityCard
              key={community.id}
              community={community}
              onJoin={() => handleJoinCommunity(community.id)}
              onLeave={() => handleLeaveCommunity(community.id)}
              onViewDetails={() => setSelectedCommunity(community)}
            />
          ))}
        </div>
      )}

      {activeTab === 'my-communities' && userCommunities && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {userCommunities.communities
            .filter(community => community.creator.id === 'current-user')
            .map((community) => (
              <CommunityCard
                key={community.id}
                community={community}
                isOwner={true}
                onViewDetails={() => setSelectedCommunity(community)}
              />
            ))}
        </div>
      )}

      {activeTab === 'joined' && userCommunities && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {userCommunities.communities.map((community) => (
            <CommunityCard
              key={community.id}
              community={community}
              isMember={true}
              onLeave={() => handleLeaveCommunity(community.id)}
              onViewDetails={() => setSelectedCommunity(community)}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {((activeTab === 'discover' && filteredCommunities.length === 0) ||
        (activeTab === 'my-communities' && userCommunities?.communities.filter(c => c.creator.id === 'current-user').length === 0) ||
        (activeTab === 'joined' && userCommunities?.communities.length === 0)) && (
        <div className="text-center py-12">
          <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            {activeTab === 'discover' ? 'No communities found' :
             activeTab === 'my-communities' ? 'No communities created' :
             'No communities joined'}
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {activeTab === 'discover' ? 'Try adjusting your search or filters.' :
             activeTab === 'my-communities' ? 'Create your first community to get started.' :
             'Join communities to connect with other NFT enthusiasts.'}
          </p>
          {activeTab !== 'discover' && (
            <div className="mt-6">
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                {activeTab === 'my-communities' ? 'Create Community' : 'Discover Communities'}
              </button>
            </div>
          )}
        </div>
      )}

      {/* Create Community Modal */}
      {showCreateModal && (
        <CreateCommunityModal
          onClose={() => setShowCreateModal(false)}
          onCommunityCreated={(community) => {
            setShowCreateModal(false)
            setSelectedCommunity(community)
          }}
        />
      )}

      {/* Community Detail Modal */}
      {selectedCommunity && (
        <CommunityDetailModal
          community={selectedCommunity}
          onClose={() => setSelectedCommunity(null)}
          onJoin={() => handleJoinCommunity(selectedCommunity.id)}
          onLeave={() => handleLeaveCommunity(selectedCommunity.id)}
        />
      )}
    </div>
  )
}

interface CommunityCardProps {
  community: Community
  isOwner?: boolean
  isMember?: boolean
  onJoin?: () => void
  onLeave?: () => void
  onViewDetails: () => void
}

function CommunityCard({ 
  community, 
  isOwner = false, 
  isMember = false, 
  onJoin, 
  onLeave, 
  onViewDetails 
}: CommunityCardProps) {
  const getTypeIcon = (type: CommunityType) => {
    switch (type) {
      case CommunityType.PUBLIC:
        return <GlobeAltIcon className="h-4 w-4 text-green-600" />
      case CommunityType.PRIVATE:
        return <LockClosedIcon className="h-4 w-4 text-red-600" />
      case CommunityType.INVITE_ONLY:
        return <EyeSlashIcon className="h-4 w-4 text-yellow-600" />
      default:
        return <GlobeAltIcon className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer">
      <div onClick={onViewDetails}>
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <img
              src={community.avatar || '/default-community.png'}
              alt={community.name}
              className="w-12 h-12 rounded-lg"
            />
            <div>
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                {community.name}
                {community.isVerified && (
                  <CheckCircleIcon className="h-5 w-5 text-blue-500 ml-2" />
                )}
              </h3>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                {getTypeIcon(community.type)}
                <span className="capitalize">{community.category}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-700 text-sm mb-4 line-clamp-2">{community.description}</p>

        {/* Tags */}
        {community.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {community.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                {tag}
              </span>
            ))}
            {community.tags.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                +{community.tags.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{community.memberCount}</div>
            <div className="text-xs text-gray-600">Members</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{community.postCount}</div>
            <div className="text-xs text-gray-600">Posts</div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="text-xs text-gray-500">
          Created by @{community.creator.username}
        </div>
        
        <div className="flex items-center space-x-2">
          {isOwner ? (
            <button className="text-sm text-blue-600 hover:text-blue-700">
              <Cog6ToothIcon className="h-4 w-4" />
            </button>
          ) : isMember ? (
            <>
              <button className="text-sm text-blue-600 hover:text-blue-700">
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onLeave?.()
                }}
                className="text-sm text-red-600 hover:text-red-700"
              >
                <UserMinusIcon className="h-4 w-4" />
              </button>
            </>
          ) : (
            <button
              onClick={(e) => {
                e.stopPropagation()
                onJoin?.()
              }}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              <UserPlusIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
