import { useState, useEffect, useCallback, useMemo } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { nftService } from '../services/nftService'
import {
  NFT,
  NFTCollection,
  NFTFilters,
  NFTSortOptions,
  NFTAnalytics,
  NFTRarity,
  NFTStatus,
  BlockchainNetwork
} from '../types/nft.types'

// Query Keys
export const NFT_QUERY_KEYS = {
  collection: (userId: string, filters?: NFTFilters, sort?: NFTSortOptions, page?: number) => 
    ['nft-collection', userId, filters, sort, page],
  myCollection: (filters?: NFTFilters, sort?: NFTSortOptions, page?: number) => 
    ['my-nft-collection', filters, sort, page],
  nftDetails: (userId: string, nftId: string) => ['nft-details', userId, nftId],
  analytics: (userId: string, timeframe?: string) => ['nft-analytics', userId, timeframe],
  evolution: (nftId: string) => ['nft-evolution', nftId],
  trending: () => ['trending-nfts'],
  search: (query: string, filters?: NFTFilters) => ['nft-search', query, filters]
}

// Hook for managing user's NFT collection
export function useNFTCollection(
  userId?: string,
  options?: {
    filters?: NFTFilters
    sort?: NFTSortOptions
    page?: number
    limit?: number
    enabled?: boolean
  }
) {
  const queryClient = useQueryClient()
  const [localFilters, setLocalFilters] = useState<NFTFilters>(options?.filters || {})
  const [localSort, setLocalSort] = useState<NFTSortOptions>(
    options?.sort || { field: 'createdAt', order: 'desc' }
  )
  const [currentPage, setCurrentPage] = useState(options?.page || 1)

  // Query for NFT collection
  const {
    data: collectionData,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useQuery({
    queryKey: NFT_QUERY_KEYS.collection(userId!, localFilters, localSort, currentPage),
    queryFn: () => userId 
      ? nftService.getUserCollection(userId, {
          filters: localFilters,
          sort: localSort,
          page: currentPage,
          limit: options?.limit || 20
        })
      : nftService.getMyCollection({
          filters: localFilters,
          sort: localSort,
          page: currentPage,
          limit: options?.limit || 20
        }),
    enabled: options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  })

  // Extract data from response
  const collection = collectionData?.success ? collectionData.data : null
  const nfts = collection?.nfts || []
  const stats = collection?.stats
  const pagination = collection?.pagination

  // Filter and sort handlers
  const updateFilters = useCallback((newFilters: Partial<NFTFilters>) => {
    setLocalFilters(prev => ({ ...prev, ...newFilters }))
    setCurrentPage(1) // Reset to first page when filters change
  }, [])

  const updateSort = useCallback((newSort: NFTSortOptions) => {
    setLocalSort(newSort)
    setCurrentPage(1) // Reset to first page when sort changes
  }, [])

  const clearFilters = useCallback(() => {
    setLocalFilters({})
    setCurrentPage(1)
  }, [])

  // Pagination handlers
  const goToPage = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])

  const nextPage = useCallback(() => {
    if (pagination?.hasNext) {
      setCurrentPage(prev => prev + 1)
    }
  }, [pagination?.hasNext])

  const prevPage = useCallback(() => {
    if (pagination?.hasPrev) {
      setCurrentPage(prev => prev - 1)
    }
  }, [pagination?.hasPrev])

  // Refresh collection
  const refresh = useCallback(() => {
    refetch()
  }, [refetch])

  // Computed values
  const hasFilters = useMemo(() => {
    return Object.keys(localFilters).some(key => {
      const value = localFilters[key as keyof NFTFilters]
      return Array.isArray(value) ? value.length > 0 : value !== undefined && value !== ''
    })
  }, [localFilters])

  const isEmpty = useMemo(() => {
    return !isLoading && nfts.length === 0
  }, [isLoading, nfts.length])

  const isRefreshing = isFetching && !isLoading

  return {
    // Data
    nfts,
    collection,
    stats,
    pagination,
    
    // State
    isLoading,
    isError,
    error: isError ? error : null,
    isEmpty,
    isRefreshing,
    hasFilters,
    
    // Current state
    filters: localFilters,
    sort: localSort,
    currentPage,
    
    // Actions
    updateFilters,
    updateSort,
    clearFilters,
    goToPage,
    nextPage,
    prevPage,
    refresh
  }
}

// Hook for NFT details
export function useNFTDetails(userId: string, nftId: string, enabled: boolean = true) {
  const {
    data: nftData,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: NFT_QUERY_KEYS.nftDetails(userId, nftId),
    queryFn: () => nftService.getNFTDetails(userId, nftId),
    enabled: enabled && !!userId && !!nftId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })

  const nft = nftData?.success ? nftData.data : null

  return {
    nft,
    isLoading,
    isError,
    error: isError ? error : null,
    refetch
  }
}

// Hook for NFT analytics
export function useNFTAnalytics(userId: string, timeframe?: string) {
  const {
    data: analyticsData,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: NFT_QUERY_KEYS.analytics(userId, timeframe),
    queryFn: () => nftService.getNFTAnalytics(userId, timeframe),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  const analytics = analyticsData?.success ? analyticsData.data : null

  return {
    analytics,
    isLoading,
    isError,
    error: isError ? error : null,
    refetch
  }
}

// Hook for NFT mutations (update, mint, etc.)
export function useNFTMutations(userId?: string) {
  const queryClient = useQueryClient()

  // Update NFT mutation
  const updateNFTMutation = useMutation({
    mutationFn: ({ nftId, updates }: { nftId: string; updates: any }) =>
      nftService.updateNFT(userId!, nftId, updates),
    onSuccess: (data, variables) => {
      // Invalidate and refetch collection
      queryClient.invalidateQueries({ queryKey: ['nft-collection'] })
      queryClient.invalidateQueries({ queryKey: ['my-nft-collection'] })
      queryClient.invalidateQueries({ 
        queryKey: NFT_QUERY_KEYS.nftDetails(userId!, variables.nftId) 
      })
    }
  })

  // Mint NFT mutation
  const mintNFTMutation = useMutation({
    mutationFn: (request: { nftId: string; blockchain: BlockchainNetwork; contractAddress?: string }) =>
      nftService.mintNFT(request),
    onSuccess: () => {
      // Invalidate collection queries
      queryClient.invalidateQueries({ queryKey: ['nft-collection'] })
      queryClient.invalidateQueries({ queryKey: ['my-nft-collection'] })
    }
  })

  // Update score mutation
  const updateScoreMutation = useMutation({
    mutationFn: ({ nftId, score }: { nftId: string; score: number }) =>
      nftService.updateNFTScore(nftId, score),
    onSuccess: () => {
      // Invalidate collection and analytics
      queryClient.invalidateQueries({ queryKey: ['nft-collection'] })
      queryClient.invalidateQueries({ queryKey: ['my-nft-collection'] })
      queryClient.invalidateQueries({ queryKey: ['nft-analytics'] })
    }
  })

  return {
    updateNFT: updateNFTMutation,
    mintNFT: mintNFTMutation,
    updateScore: updateScoreMutation,
    
    // Loading states
    isUpdating: updateNFTMutation.isPending,
    isMinting: mintNFTMutation.isPending,
    isUpdatingScore: updateScoreMutation.isPending,
    
    // Error states
    updateError: updateNFTMutation.error,
    mintError: mintNFTMutation.error,
    scoreError: updateScoreMutation.error
  }
}

// Hook for NFT search
export function useNFTSearch() {
  const [query, setQuery] = useState('')
  const [filters, setFilters] = useState<NFTFilters>({})
  const [debouncedQuery, setDebouncedQuery] = useState('')

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query)
    }, 300)

    return () => clearTimeout(timer)
  }, [query])

  const {
    data: searchData,
    isLoading,
    isError,
    error
  } = useQuery({
    queryKey: NFT_QUERY_KEYS.search(debouncedQuery, filters),
    queryFn: () => nftService.searchNFTs(debouncedQuery, filters),
    enabled: debouncedQuery.length > 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })

  const results = searchData?.success ? searchData.data : []

  return {
    query,
    setQuery,
    filters,
    setFilters,
    results,
    isLoading,
    isError,
    error: isError ? error : null,
    hasResults: results.length > 0
  }
}

// Hook for filter options
export function useNFTFilterOptions() {
  return useMemo(() => ({
    rarities: Object.values(NFTRarity),
    statuses: Object.values(NFTStatus),
    blockchains: Object.values(BlockchainNetwork)
  }), [])
}
