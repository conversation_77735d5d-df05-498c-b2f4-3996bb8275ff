import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus, Lo<PERSON> } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { Response } from 'express';
import { TransactionMonitorService } from '../services/transaction-monitor.service';

@ApiTags('Transactions')
@Controller('transactions')
export class TransactionController {
  private readonly logger = new Logger(TransactionController.name);

  constructor(private readonly transactionMonitorService: TransactionMonitorService) {}

  @Get(':hash')
  @ApiOperation({ summary: 'Get transaction details', description: 'Get details of a specific transaction' })
  @ApiParam({ name: 'hash', description: 'Transaction hash' })
  @ApiResponse({ status: 200, description: 'Transaction details retrieved successfully' })
  async getTransaction(@Param('hash') hash: string, @Res() res: Response) {
    try {
      this.logger.log(`Getting transaction details for: ${hash}`);
      const result = await this.transactionMonitorService.getTransaction(hash);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Transaction retrieval failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
