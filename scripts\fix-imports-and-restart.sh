#!/bin/bash

# Fix duplicate imports and restart services
set -e

echo "🔧 Fixing duplicate imports and restarting services..."
echo "====================================================="

# Services to fix
SERVICES=(
    "nft-generation-service"
    "blockchain-service" 
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Function to fix imports in app.module.ts
fix_imports() {
    local service=$1
    local app_module="services/$service/src/app.module.ts"
    
    echo "🔧 Fixing imports for $service..."
    
    # Create a clean version
    cat > "$app_module" << EOF
import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { EnterpriseModule } from './enterprise/enterprise.module';

// Security Middleware
import { GatewayAuthMiddleware } from './common/middleware/gateway-auth.middleware';
import { CorrelationIdMiddleware } from './common/middleware/correlation-id.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Enterprise Module
    EnterpriseModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply security middleware first (before correlation ID)
    // Apply to all routes except health checks
    consumer.apply(GatewayAuthMiddleware)
      .exclude(
        { path: 'health', method: RequestMethod.GET },
        { path: 'health/simple', method: RequestMethod.GET },
        { path: 'status', method: RequestMethod.GET },
      )
      .forRoutes('*');
    consumer.apply(CorrelationIdMiddleware).forRoutes('*');
  }
}
EOF
    
    echo "✅ Fixed imports for $service"
}

# Fix imports for all services
for service in "${SERVICES[@]}"; do
    if [ -f "services/$service/src/app.module.ts" ]; then
        fix_imports "$service"
    else
        echo "⚠️  app.module.ts not found for $service"
    fi
done

echo ""
echo "✅ All imports fixed!"
echo ""
echo "🔄 Services will restart automatically due to watch mode"
echo "⏳ Waiting 10 seconds for services to restart..."
sleep 10

echo ""
echo "🎉 Import fixes completed!"
echo "========================"
