'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { nftApi } from '@/lib/api'
import {
  FunnelIcon,
  MagnifyingGlassIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  TrashIcon,
  TagIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import EnhancedNFTCard from './enhanced-nft-card'
import NFTDetailModal from './nft-detail-modal'
import EnhancedShareModal from '@/components/ui/enhanced-share-modal'
import CreateListingModal from '@/components/marketplace/CreateListingModal'
import PurchaseModal from '@/components/marketplace/PurchaseModal'
import MakeOfferModal from '@/components/marketplace/MakeOfferModal'
import { useNFTCollection, useNFTMutations, useNFTFilterOptions } from '@/hooks/useNFTCollection'
import { NFTFilters, NFTSortOptions, NFTRarity, NFTStatus, BlockchainNetwork, NFT } from '@/types/nft.types'
import { MarketplaceListing } from '@/types/marketplace.types'

interface NFTCollectionManagerProps {
  userId?: string
  refreshTrigger?: number
  className?: string
}

export default function NFTCollectionManager({ userId, refreshTrigger, className }: NFTCollectionManagerProps) {
  const { user } = useAuth()

  // UI State
  const [selectedNFT, setSelectedNFT] = useState<NFT | null>(null)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [showListingModal, setShowListingModal] = useState(false)
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [showOfferModal, setShowOfferModal] = useState(false)
  const [selectedListing, setSelectedListing] = useState<MarketplaceListing | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedNFTs, setSelectedNFTs] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')

  // Get filter options
  const { rarities, statuses, blockchains } = useNFTFilterOptions()

  // Use NFT collection hook with enhanced functionality
  const {
    nfts,
    collection,
    stats,
    pagination,
    isLoading,
    isError,
    error,
    isEmpty,
    isRefreshing,
    hasFilters,
    filters,
    sort,
    currentPage,
    updateFilters,
    updateSort,
    clearFilters,
    goToPage,
    nextPage,
    prevPage,
    refresh
  } = useNFTCollection(userId || user?.id, {
    limit: 20,
    enabled: !!(userId || user?.id)
  })

  // NFT mutations
  const { updateNFT, mintNFT, updateScore, isUpdating, isMinting } = useNFTMutations(userId || user?.id)

  // Update search filter when search query changes
  useEffect(() => {
    const timer = setTimeout(() => {
      updateFilters({ searchQuery: searchQuery || undefined })
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery, updateFilters])

  // Refresh when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger) {
      refresh()
    }
  }, [refreshTrigger, refresh])

  // Enhanced filter handlers
  const handleRarityFilter = (rarity: string) => {
    if (rarity === 'all') {
      updateFilters({ rarity: undefined })
    } else {
      updateFilters({ rarity: [rarity as NFTRarity] })
    }
  }

  const handleStatusFilter = (status: string) => {
    if (status === 'all') {
      updateFilters({ status: undefined })
    } else {
      updateFilters({ status: [status as NFTStatus] })
    }
  }

  const handleSortChange = (sortValue: string) => {
    const [field, order] = sortValue.split('-')
    updateSort({
      field: field as NFTSortOptions['field'],
      order: (order as 'asc' | 'desc') || 'desc'
    })
  }

  const handleMintNFT = async (nftId: string, blockchain: BlockchainNetwork) => {
    try {
      await mintNFT.mutateAsync({
        nftId,
        blockchain
      })
    } catch (error) {
      console.error('Failed to mint NFT:', error)
    }
  }

  const handleNFTClick = (nft: any) => {
    setSelectedNFT(nft)
    setShowDetailModal(true)
  }

  const handleShare = (nft: NFT) => {
    setSelectedNFT(nft)
    setShowShareModal(true)
  }

  const handleListForSale = (nft: NFT) => {
    setSelectedNFT(nft)
    setShowListingModal(true)
  }

  const handlePurchase = (listing: MarketplaceListing) => {
    setSelectedListing(listing)
    setShowPurchaseModal(true)
  }

  const handleMakeOffer = (listing: MarketplaceListing) => {
    setSelectedListing(listing)
    setShowOfferModal(true)
  }

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} on NFTs:`, selectedNFTs)
    // TODO: Implement bulk actions like bulk mint, bulk list, etc.
  }

  const toggleNFTSelection = (nftId: string) => {
    setSelectedNFTs(prev =>
      prev.includes(nftId)
        ? prev.filter(id => id !== nftId)
        : [...prev, nftId]
    )
  }

  const getRarityStats = () => {
    if (!stats?.rarityBreakdown) {
      // Fallback to manual calculation if stats not available
      const rarityStats = nfts.reduce((acc, nft) => {
        const rarity = nft.rarity?.toLowerCase() || 'unknown'
        acc[rarity] = (acc[rarity] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      return rarityStats
    }

    return Object.entries(stats.rarityBreakdown).reduce((acc, [rarity, count]) => {
      acc[rarity.toLowerCase()] = count
      return acc
    }, {} as Record<string, number>)
  }

  const getStatusStats = () => {
    if (!stats?.statusBreakdown) {
      // Fallback to manual calculation
      const statusStats = nfts.reduce((acc, nft) => {
        const status = nft.status?.toLowerCase() || 'unknown'
        acc[status] = (acc[status] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      return statusStats
    }

    return Object.entries(stats.statusBreakdown).reduce((acc, [status, count]) => {
      acc[status.toLowerCase()] = count
      return acc
    }, {} as Record<string, number>)
  }

  if (isLoading) {
    return (
      <div className={`bg-white shadow rounded-lg p-6 ${className || ''}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className={`bg-white shadow rounded-lg p-6 ${className || ''}`}>
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading NFTs</h3>
          <p className="mt-1 text-sm text-gray-500">
            {error?.message || 'Failed to load your NFT collection. Please try again.'}
          </p>
          <button
            onClick={refresh}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white shadow rounded-lg ${className || ''}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              {userId ? 'NFT Collection' : 'My NFT Collection'}
            </h3>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>{stats?.totalNFTs || nfts.length} NFTs total</span>
              {stats?.totalValue && (
                <span>Total Value: ${stats.totalValue.toLocaleString()}</span>
              )}
              {isRefreshing && (
                <span className="flex items-center">
                  <ArrowPathIcon className="h-4 w-4 mr-1 animate-spin" />
                  Refreshing...
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {selectedNFTs.length > 0 && (
              <div className="flex items-center space-x-2 mr-4">
                <span className="text-sm text-gray-600">{selectedNFTs.length} selected</span>
                <button
                  onClick={() => handleBulkAction('share')}
                  className="p-2 text-gray-400 hover:text-gray-600"
                  title="Share selected"
                >
                  <ShareIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleBulkAction('download')}
                  className="p-2 text-gray-400 hover:text-gray-600"
                  title="Download selected"
                >
                  <ArrowDownTrayIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleBulkAction('mint')}
                  className="p-2 text-gray-400 hover:text-gray-600"
                  title="Mint selected"
                  disabled={isMinting}
                >
                  <TagIcon className="h-4 w-4" />
                </button>
              </div>
            )}

            <button
              onClick={refresh}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="Refresh collection"
              disabled={isRefreshing}
            >
              <ArrowPathIcon className={`h-5 w-5 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>

            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="p-2 text-gray-400 hover:text-gray-600"
              title={`Switch to ${viewMode === 'grid' ? 'list' : 'grid'} view`}
            >
              {viewMode === 'grid' ? (
                <ListBulletIcon className="h-5 w-5" />
              ) : (
                <Squares2X2Icon className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search NFTs by name, handle, or traits..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex gap-2">
            <select
              value={filters.rarity?.[0] || 'all'}
              onChange={(e) => handleRarityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Rarities</option>
              {rarities.map(rarity => (
                <option key={rarity} value={rarity}>
                  {rarity.charAt(0).toUpperCase() + rarity.slice(1)}
                </option>
              ))}
            </select>

            <select
              value={filters.status?.[0] || 'all'}
              onChange={(e) => handleStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>

            <select
              value={`${sort.field}-${sort.order}`}
              onChange={(e) => handleSortChange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="createdAt-desc">Newest First</option>
              <option value="createdAt-asc">Oldest First</option>
              <option value="currentScore-desc">Highest Score</option>
              <option value="currentScore-asc">Lowest Score</option>
              <option value="rarity-desc">Rarity (High to Low)</option>
              <option value="name-asc">Name (A-Z)</option>
            </select>

            {hasFilters && (
              <button
                onClick={clearFilters}
                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
                title="Clear all filters"
              >
                Clear
              </button>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="mt-4 flex flex-wrap gap-2">
          {Object.entries(getRarityStats()).map(([rarity, count]) => (
            <span
              key={rarity}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
            >
              <TagIcon className="h-3 w-3 mr-1" />
              {rarity}: {count}
            </span>
          ))}
        </div>

        {/* Pagination Info */}
        {pagination && pagination.totalPages > 1 && (
          <div className="mt-4 flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of {pagination.totalCount} NFTs
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={prevPage}
                disabled={!pagination.hasPrev}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <span className="text-sm text-gray-500">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <button
                onClick={nextPage}
                disabled={!pagination.hasNext}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* NFT Grid/List */}
      <div className="p-6">
        {isEmpty ? (
          <div className="text-center py-12">
            <Squares2X2Icon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No NFTs found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {hasFilters
                ? 'Try adjusting your filters or search terms.'
                : 'Start by analyzing Twitter profiles to generate your first NFT!'
              }
            </p>
            {hasFilters && (
              <button
                onClick={clearFilters}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200"
              >
                Clear Filters
              </button>
            )}
          </div>
        ) : (
          <div className={`grid gap-4 ${
            viewMode === 'grid'
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          }`}>
            {nfts.map((nft) => (
              <div key={nft.id} className="relative">
                {/* Selection checkbox for bulk actions */}
                <div className="absolute top-2 left-2 z-10">
                  <input
                    type="checkbox"
                    checked={selectedNFTs.includes(nft.id)}
                    onChange={() => toggleNFTSelection(nft.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>

                <EnhancedNFTCard
                  nft={nft}
                  onClick={() => handleNFTClick(nft)}
                  onView={() => handleNFTClick(nft)}
                  onShare={() => handleShare(nft)}
                  onList={() => handleListForSale(nft)}
                  onMint={(nft) => handleMintNFT(nft.id, BlockchainNetwork.ETHEREUM)}
                  showActions={true}
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      <NFTDetailModal
        nft={selectedNFT}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
      />

      <EnhancedShareModal
        nft={selectedNFT}
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
      />

      <CreateListingModal
        nft={selectedNFT}
        isOpen={showListingModal}
        onClose={() => setShowListingModal(false)}
        onSuccess={() => {
          setShowListingModal(false)
          refresh() // Refresh the collection to show updated status
        }}
      />

      <PurchaseModal
        listing={selectedListing}
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        onSuccess={() => {
          setShowPurchaseModal(false)
          refresh() // Refresh the collection
        }}
      />

      <MakeOfferModal
        listing={selectedListing}
        isOpen={showOfferModal}
        onClose={() => setShowOfferModal(false)}
        onSuccess={() => {
          setShowOfferModal(false)
        }}
      />
    </div>
  )
}
