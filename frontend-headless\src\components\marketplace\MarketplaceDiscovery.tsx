'use client'

import React from 'react'
import {
  FireIcon,
  TrendingUpIcon,
  SparklesIcon,
  EyeIcon,
  HeartIcon,
  ShareIcon,
  ChartBarIcon,
  StarIcon
} from '@heroicons/react/24/outline'
import { MarketplaceDiscovery } from '@/types/marketplace-integration.types'

interface MarketplaceDiscoveryProps {
  discovery?: MarketplaceDiscovery
  isLoading: boolean
  onViewListing?: (id: string) => void
  className?: string
}

export default function MarketplaceDiscoveryComponent({
  discovery,
  isLoading,
  onViewListing,
  className = ''
}: MarketplaceDiscoveryProps) {
  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!discovery) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No discovery data available</h3>
        <p className="mt-1 text-sm text-gray-500">Discovery content will appear here once data is loaded.</p>
      </div>
    )
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Featured Campaigns */}
      {discovery.featuredCampaigns && discovery.featuredCampaigns.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <StarIcon className="h-6 w-6 mr-2 text-yellow-500" />
              Featured Campaigns
            </h2>
            <button className="text-sm text-blue-600 hover:text-blue-700">
              View All
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {discovery.featuredCampaigns.slice(0, 6).map((campaign) => (
              <div
                key={campaign.id}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
                onClick={() => onViewListing?.(campaign.id)}
              >
                <div className="relative">
                  <img
                    src={campaign.coverImage}
                    alt={campaign.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-3 left-3">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <StarIcon className="h-3 w-3 mr-1" />
                      Featured
                    </span>
                  </div>
                  <div className="absolute top-3 right-3">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {campaign.category}
                    </span>
                  </div>
                </div>
                
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-1">{campaign.title}</h3>
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">{campaign.description}</p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <EyeIcon className="h-4 w-4" />
                        <span>{campaign.views.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <HeartIcon className="h-4 w-4" />
                        <span>{campaign.favorites.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {campaign.basePrice} {campaign.currency}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <img
                        src={campaign.campaignData.creatorAvatar || '/default-avatar.png'}
                        alt={campaign.creatorName}
                        className="w-6 h-6 rounded-full"
                      />
                      <span className="text-sm text-gray-600">{campaign.creatorName}</span>
                      {campaign.creatorVerified && (
                        <span className="text-blue-500">✓</span>
                      )}
                    </div>
                    <div className="text-xs text-gray-500">
                      {campaign.campaignData.participantCount} participants
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Trending Collections */}
      {discovery.trendingCollections && discovery.trendingCollections.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <TrendingUpIcon className="h-6 w-6 mr-2 text-green-500" />
              Trending Collections
            </h2>
            <button className="text-sm text-blue-600 hover:text-blue-700">
              View All
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {discovery.trendingCollections.slice(0, 8).map((collection) => (
              <div
                key={collection.id}
                className="bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
                onClick={() => onViewListing?.(collection.id)}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <img
                    src={collection.collectionImage}
                    alt={collection.collectionName}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 truncate">{collection.collectionName}</h3>
                    <p className="text-sm text-gray-600">{collection.totalSupply} items</p>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Floor Price:</span>
                    <span className="font-medium">{collection.floorPrice} ETH</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Volume:</span>
                    <span className="font-medium">{collection.totalVolume.toLocaleString()} ETH</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">24h Change:</span>
                    <span className={`font-medium ${
                      collection.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {collection.priceChange24h >= 0 ? '+' : ''}{collection.priceChange24h.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Popular Categories */}
      {discovery.popularCategories && discovery.popularCategories.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <FireIcon className="h-6 w-6 mr-2 text-red-500" />
              Popular Categories
            </h2>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {discovery.popularCategories.map((category) => (
              <div
                key={category.category}
                className="bg-white border border-gray-200 rounded-lg p-4 text-center hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
              >
                <div className="text-2xl mb-2">
                  {getCategoryIcon(category.category)}
                </div>
                <h3 className="font-medium text-gray-900 mb-1 capitalize">
                  {category.category.replace('_', ' ')}
                </h3>
                <p className="text-sm text-gray-600">{category.listingCount} listings</p>
                <p className="text-xs text-gray-500">
                  {category.growth > 0 ? '+' : ''}{category.growth.toFixed(1)}% growth
                </p>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Market Insights */}
      {discovery.marketStats && (
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <ChartBarIcon className="h-6 w-6 mr-2 text-blue-500" />
              Market Insights
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {discovery.marketStats.totalListings.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 mb-2">Total Listings</div>
              <div className="flex items-center text-sm">
                <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+{discovery.marketStats.listingsGrowth.toFixed(1)}%</span>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="text-2xl font-bold text-gray-900 mb-1">
                ${discovery.marketStats.totalVolume.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 mb-2">Total Volume</div>
              <div className="flex items-center text-sm">
                <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+{discovery.marketStats.volumeGrowth.toFixed(1)}%</span>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="text-2xl font-bold text-gray-900 mb-1">
                ${discovery.marketStats.averagePrice.toFixed(2)}
              </div>
              <div className="text-sm text-gray-600 mb-2">Average Price</div>
              <div className="text-sm text-gray-500">Across all categories</div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {discovery.marketStats.activeUsers.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 mb-2">Active Users</div>
              <div className="flex items-center text-sm">
                <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+{discovery.marketStats.userGrowth.toFixed(1)}%</span>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* New Listings */}
      {discovery.newListings && discovery.newListings.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <SparklesIcon className="h-6 w-6 mr-2 text-purple-500" />
              New Listings
            </h2>
            <button className="text-sm text-blue-600 hover:text-blue-700">
              View All
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {discovery.newListings.slice(0, 8).map((listing) => (
              <div
                key={listing.id}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
                onClick={() => onViewListing?.(listing.id)}
              >
                <img
                  src={listing.coverImage}
                  alt={listing.title}
                  className="w-full h-32 object-cover"
                />
                <div className="p-3">
                  <h3 className="font-medium text-gray-900 mb-1 line-clamp-1">{listing.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{listing.creatorName}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">
                      {listing.basePrice} {listing.currency}
                    </span>
                    <span className="text-xs text-gray-500">
                      {new Date(listing.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      )}
    </div>
  )
}

function getCategoryIcon(category: string): string {
  const icons: Record<string, string> = {
    art: '🎨',
    gaming: '🎮',
    music: '🎵',
    sports: '⚽',
    collectibles: '🏆',
    utility: '🔧',
    metaverse: '🌐',
    photography: '📸',
    memes: '😂',
    pfps: '👤'
  }
  return icons[category] || '🖼️'
}
