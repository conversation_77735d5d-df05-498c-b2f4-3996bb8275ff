// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model NftCollection {
  id          String   @id @default(cuid())
  name        String
  description String?
  projectId   String
  size        Int
  status      String   @default("pending")
  baseImageUrl String?
  outputDir   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  nfts        Nft[]
  jobs        GenerationJob[]

  @@map("nft_collections")
}

model Nft {
  id           String   @id @default(cuid())
  name         String
  description  String?
  collectionId String
  tokenId      Int?
  imageUrl     String?
  metadataUrl  String?
  traits       Json?
  rarity       String?
  status       String   @default("pending")
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  collection   NftCollection @relation(fields: [collectionId], references: [id], onDelete: Cascade)

  @@map("nfts")
}

model GenerationJob {
  id           String   @id @default(cuid())
  collectionId String
  status       String   @default("pending")
  progress     Int      @default(0)
  totalItems   Int
  completedItems Int    @default(0)
  errors       Json?
  startedAt    DateTime @default(now())
  completedAt  DateTime?
  estimatedCompletion DateTime?

  // Relations
  collection   NftCollection @relation(fields: [collectionId], references: [id], onDelete: Cascade)

  @@map("generation_jobs")
}

model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  baseImageUrl String?
  traits      Json?
  isPublic    Boolean  @default(true)
  createdBy   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("templates")
}

model ProcessedImage {
  id          String   @id @default(cuid())
  originalUrl String
  processedUrl String
  operations  Json?
  parameters  Json?
  fileSize    Int?
  dimensions  Json?
  processingTime String?
  createdAt   DateTime @default(now())

  @@map("processed_images")
}
