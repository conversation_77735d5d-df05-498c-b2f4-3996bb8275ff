import { ApiProperty } from '@nestjs/swagger';

export class ErrorResponseDto {
  @ApiProperty({ example: false })
  success: boolean;

  @ApiProperty({ example: 'Error message' })
  message: string;

  @ApiProperty({ example: 400 })
  statusCode: number;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z' })
  timestamp: string;

  @ApiProperty({ example: 'user-service' })
  service: string;

  @ApiProperty({ example: 'correlation-id-123', required: false })
  correlationId?: string;

  @ApiProperty({ example: { field: 'validation error' }, required: false })
  details?: any;
}
