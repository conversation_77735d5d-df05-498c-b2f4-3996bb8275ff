// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Notification {
  id          String   @id @default(cuid())
  userId      String
  type        String   // email, sms, push, in_app
  category    String   // trade, bid, listing, account, system, marketing
  priority    String   // low, medium, high, urgent
  status      String   // pending, sent, delivered, failed, cancelled
  title       String
  message     String
  data        Json?
  channels    Json?    // Channel-specific data
  scheduling  Json?    // Scheduling information
  tracking    Json?    // Tracking information
  metadata    Json?    // Additional metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("notifications")
}

model NotificationTemplate {
  id          String   @id @default(cuid())
  name        String
  eventType   String
  channels    String[] // Array of channel types
  priority    String
  category    String
  templates   Json     // Template content for each channel
  conditions  Json?    // Conditions for sending
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("notification_templates")
}

model NotificationPreference {
  id          String   @id @default(cuid())
  userId      String
  category    String
  channels    String[] // Enabled channels for this category
  isEnabled   Boolean  @default(true)
  quietHours  Json?    // Quiet hours configuration
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([userId, category])
  @@map("notification_preferences")
}

model NotificationEvent {
  id            String   @id @default(cuid())
  eventType     String
  userId        String
  data          Json
  source        String
  correlationId String?
  processedAt   DateTime?
  createdAt     DateTime @default(now())

  @@map("notification_events")
}
