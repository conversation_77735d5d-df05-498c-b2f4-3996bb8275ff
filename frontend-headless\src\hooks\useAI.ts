import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { aiService } from '@/services/aiService'
import {
  AIRecommendation,
  PersonalizationProfile,
  PredictiveAnalytics,
  AutomationRule,
  MLModel,
  GetRecommendationsRequest,
  UpdatePersonalizationRequest,
  CreateAutomationRequest,
  UpdateAutomationRequest,
  RecommendationType,
  PredictionType,
  AutomationType
} from '@/types/ai.types'

// Recommendation Hooks
export function useRecommendations(request: GetRecommendationsRequest) {
  return useQuery({
    queryKey: ['recommendations', request],
    queryFn: () => aiService.getRecommendations(request),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  })
}

export function usePersonalizedRecommendations(userId: string, limit = 10) {
  return useQuery({
    queryKey: ['recommendations', 'personalized', userId, limit],
    queryFn: () => aiService.getPersonalizedRecommendations(userId, limit),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useMarkRecommendationViewed() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: aiService.markRecommendationViewed,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
    }
  })
}

export function useMarkRecommendationActioned() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ recommendationId, actionData }: { recommendationId: string; actionData?: Record<string, any> }) =>
      aiService.markRecommendationActioned(recommendationId, actionData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
    }
  })
}

export function useDismissRecommendation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ recommendationId, reason }: { recommendationId: string; reason?: string }) =>
      aiService.dismissRecommendation(recommendationId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
    }
  })
}

// Personalization Hooks
export function usePersonalizationProfile(userId: string) {
  return useQuery({
    queryKey: ['personalization', userId],
    queryFn: () => aiService.getPersonalizationProfile(userId),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false
  })
}

export function useUpdatePersonalizationProfile() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: aiService.updatePersonalizationProfile,
    onSuccess: (data) => {
      queryClient.setQueryData(['personalization', data.userId], data)
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
    }
  })
}

export function useTrackUserBehavior() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, behaviorData }: { userId: string; behaviorData: Record<string, any> }) =>
      aiService.trackUserBehavior(userId, behaviorData),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['personalization', variables.userId] })
    }
  })
}

export function usePersonalizedContent(userId: string, contentType: string, limit = 20) {
  return useQuery({
    queryKey: ['personalized-content', userId, contentType, limit],
    queryFn: () => aiService.getPersonalizedContent(userId, contentType, limit),
    enabled: !!userId && !!contentType,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

// Prediction Hooks
export function usePredictions(request: { types?: PredictionType[]; targets?: string[]; timeframe?: string; limit?: number }) {
  return useQuery({
    queryKey: ['predictions', request],
    queryFn: () => aiService.getPredictions(request),
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false
  })
}

export function usePricePrediction(nftId: string, timeframe = '7d') {
  return useQuery({
    queryKey: ['prediction', 'price', nftId, timeframe],
    queryFn: () => aiService.getPricePrediction(nftId, timeframe),
    enabled: !!nftId,
    staleTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false
  })
}

export function useMarketTrendPrediction(timeframe = '30d') {
  return useQuery({
    queryKey: ['prediction', 'market-trend', timeframe],
    queryFn: () => aiService.getMarketTrendPrediction(timeframe),
    staleTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false
  })
}

export function useUserBehaviorPrediction(userId: string) {
  return useQuery({
    queryKey: ['prediction', 'user-behavior', userId],
    queryFn: () => aiService.getUserBehaviorPrediction(userId),
    enabled: !!userId,
    staleTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false
  })
}

// Automation Hooks
export function useAutomationRules(userId: string, type?: AutomationType) {
  return useQuery({
    queryKey: ['automation', 'rules', userId, type],
    queryFn: () => aiService.getAutomationRules(userId, type),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useCreateAutomationRule() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, request }: { userId: string; request: CreateAutomationRequest }) =>
      aiService.createAutomationRule(userId, request),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'rules', variables.userId] })
    }
  })
}

export function useUpdateAutomationRule() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, request }: { userId: string; request: UpdateAutomationRequest }) =>
      aiService.updateAutomationRule(userId, request),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'rules', variables.userId] })
    }
  })
}

export function useDeleteAutomationRule() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, ruleId }: { userId: string; ruleId: string }) =>
      aiService.deleteAutomationRule(userId, ruleId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'rules', variables.userId] })
    }
  })
}

export function useToggleAutomationRule() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, ruleId, isActive }: { userId: string; ruleId: string; isActive: boolean }) =>
      aiService.toggleAutomationRule(userId, ruleId, isActive),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'rules', variables.userId] })
    }
  })
}

export function useAutomationExecutionHistory(userId: string, ruleId: string, limit = 50) {
  return useQuery({
    queryKey: ['automation', 'history', userId, ruleId, limit],
    queryFn: () => aiService.getAutomationExecutionHistory(userId, ruleId, limit),
    enabled: !!userId && !!ruleId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false
  })
}

// ML Model Hooks
export function useMLModels() {
  return useQuery({
    queryKey: ['ml-models'],
    queryFn: aiService.getMLModels,
    staleTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false
  })
}

export function useMLModel(modelId: string) {
  return useQuery({
    queryKey: ['ml-model', modelId],
    queryFn: () => aiService.getMLModel(modelId),
    enabled: !!modelId,
    staleTime: 15 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useTriggerModelTraining() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: aiService.triggerModelTraining,
    onSuccess: (_, modelId) => {
      queryClient.invalidateQueries({ queryKey: ['ml-model', modelId] })
      queryClient.invalidateQueries({ queryKey: ['ml-models'] })
    }
  })
}

// Analytics & Insights Hooks
export function useAIInsights(userId: string, timeframe = '30d') {
  return useQuery({
    queryKey: ['ai-insights', userId, timeframe],
    queryFn: () => aiService.getAIInsights(userId, timeframe),
    enabled: !!userId,
    staleTime: 15 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function usePortfolioOptimization(userId: string) {
  return useQuery({
    queryKey: ['portfolio-optimization', userId],
    queryFn: () => aiService.getPortfolioOptimization(userId),
    enabled: !!userId,
    staleTime: 30 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useRiskAssessment(userId: string) {
  return useQuery({
    queryKey: ['risk-assessment', userId],
    queryFn: () => aiService.getRiskAssessment(userId),
    enabled: !!userId,
    staleTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false
  })
}

export function useMarketSentiment() {
  return useQuery({
    queryKey: ['market-sentiment'],
    queryFn: aiService.getMarketSentiment,
    staleTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useTrendingTopics(limit = 10) {
  return useQuery({
    queryKey: ['trending-topics', limit],
    queryFn: () => aiService.getTrendingTopics(limit),
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useInfluencerRecommendations(userId: string, limit = 5) {
  return useQuery({
    queryKey: ['influencer-recommendations', userId, limit],
    queryFn: () => aiService.getInfluencerRecommendations(userId, limit),
    enabled: !!userId,
    staleTime: 30 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useCommunityRecommendations(userId: string, limit = 5) {
  return useQuery({
    queryKey: ['community-recommendations', userId, limit],
    queryFn: () => aiService.getCommunityRecommendations(userId, limit),
    enabled: !!userId,
    staleTime: 30 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

export function useContentRecommendations(userId: string, contentType: string, limit = 10) {
  return useQuery({
    queryKey: ['content-recommendations', userId, contentType, limit],
    queryFn: () => aiService.getContentRecommendations(userId, contentType, limit),
    enabled: !!userId && !!contentType,
    staleTime: 15 * 60 * 1000,
    refetchOnWindowFocus: false
  })
}

// Smart Notifications Hooks
export function useSmartNotifications(userId: string) {
  return useQuery({
    queryKey: ['smart-notifications', userId],
    queryFn: () => aiService.getSmartNotifications(userId),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true
  })
}

export function useOptimizeNotificationTiming(userId: string) {
  return useQuery({
    queryKey: ['notification-timing', userId],
    queryFn: () => aiService.optimizeNotificationTiming(userId),
    enabled: !!userId,
    staleTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false
  })
}
