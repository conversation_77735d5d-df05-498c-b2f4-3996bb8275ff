#!/bin/bash

# Implement Standardized Configuration Across All Services
# This script creates standardized configuration classes and updates all services

set -e

echo "🔧 Implementing Standardized Configuration Across All Services"
echo "============================================================="

# Services to standardize
SERVICES=(
    "api-gateway"
    "user-service"
    "profile-analysis-service"
    "nft-generation-service"
    "blockchain-service"
    "project-service"
    "marketplace-service"
    "notification-service"
    "analytics-service"
)

# Function to install required dependencies
install_dependencies() {
    local service_name="$1"
    local service_dir="services/$service_name"
    
    echo "📦 Installing configuration dependencies for $service_name..."
    
    cd "$service_dir"
    
    # Install class-validator and class-transformer if not already installed
    if ! npm list class-validator &>/dev/null; then
        npm install class-validator class-transformer
    fi
    
    cd - > /dev/null
    
    echo "✅ Dependencies installed for $service_name"
}

# Function to create service configuration class
create_service_config_class() {
    local service_name="$1"
    local service_dir="services/$service_name"
    local service_port=""
    local db_name=""

    # Determine service port and database name
    case $service_name in
        "api-gateway")
            service_port="3010"
            db_name="api_gateway"
            ;;
        "user-service")
            service_port="3011"
            db_name="user_service"
            ;;
        "profile-analysis-service")
            service_port="3002"
            db_name="profile_analysis_service"
            ;;
        "nft-generation-service")
            service_port="3003"
            db_name="nft_generation_service"
            ;;
        "blockchain-service")
            service_port="3004"
            db_name="blockchain_service"
            ;;
        "project-service")
            service_port="3005"
            db_name="project_service"
            ;;
        "marketplace-service")
            service_port="3006"
            db_name="marketplace_service"
            ;;
        "notification-service")
            service_port="3008"
            db_name="notification_service"
            ;;
        "analytics-service")
            service_port="3009"
            db_name="analytics_service"
            ;;
        *)
            service_port="3000"
            db_name="unknown_service"
            ;;
    esac

    echo "🔧 Creating configuration class for $service_name..."

    # Use the template script to create configuration
    ./scripts/create-service-config-template.sh "$service_name" "$service_port" "$db_name"
}

# Function to update app.module.ts to use standardized config
update_app_module() {
    local service_name="$1"
    local service_dir="services/$service_name"
    local app_module="$service_dir/src/app.module.ts"
    
    echo "🔧 Updating app.module.ts for $service_name..."
    
    # Create backup
    cp "$app_module" "$app_module.backup.$(date +%Y%m%d_%H%M%S)"
    
    # Create updated app.module.ts with standardized configuration
    cat > "$app_module" << EOF
import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { EnterpriseModule } from './enterprise/enterprise.module';
import { HealthModule } from './health/health.module';

// Standardized Configuration
import { StandardizedConfigModule } from '../../../shared/config/standardized-config.module';
import { AppConfig } from './config/app.config';

// Security Middleware
import { GatewayAuthMiddleware } from './common/middleware/gateway-auth.middleware';
import { CorrelationIdMiddleware } from './common/middleware/correlation-id.middleware';

@Module({
  imports: [
    // Standardized Configuration with validation
    StandardizedConfigModule.forService('$service_name', {
      configClass: AppConfig,
      validateOnStartup: true,
      failFast: true,
    }),

    // Enterprise Module
    EnterpriseModule,
    
    // Health Module
    HealthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply security middleware first (before correlation ID)
    // Apply to all routes except health checks
    consumer.apply(GatewayAuthMiddleware)
      .exclude(
        // Health endpoints with global prefix
        { path: 'api/health', method: RequestMethod.GET },
        { path: 'api/health/simple', method: RequestMethod.GET },
        { path: 'api/health/database', method: RequestMethod.GET },
        // Health endpoints without global prefix (fallback)
        { path: 'health', method: RequestMethod.GET },
        { path: 'health/simple', method: RequestMethod.GET },
        { path: 'health/database', method: RequestMethod.GET },
        // Other status endpoints
        { path: 'status', method: RequestMethod.GET },
      )
      .forRoutes('*');
    consumer.apply(CorrelationIdMiddleware).forRoutes('*');
  }
}
EOF

    echo "✅ app.module.ts updated for $service_name"
}

# Main execution
echo "🚀 Starting standardized configuration implementation..."

for service_name in "${SERVICES[@]}"; do
    echo ""
    echo "🔄 Processing $service_name..."
    echo "------------------------"
    
    # Check if service directory exists
    if [ ! -d "services/$service_name" ]; then
        echo "❌ Service directory not found: services/$service_name"
        continue
    fi
    
    # Install dependencies
    install_dependencies "$service_name"
    
    # Create configuration class
    create_service_config_class "$service_name"
    
    # Update app.module.ts
    update_app_module "$service_name"
    
    echo "✅ $service_name configuration standardization complete"
done

echo ""
echo "🎉 Standardized Configuration Implementation Completed!"
echo "====================================================="
echo ""
echo "📋 Summary:"
echo "- Created standardized configuration classes for ${#SERVICES[@]} services"
echo "- Updated app.module.ts files to use StandardizedConfigModule"
echo "- Installed required dependencies (class-validator, class-transformer)"
echo "- Applied configuration validation and type safety"
echo ""
echo "📁 Files Created:"
echo "services/[service]/src/config/app.config.ts    # Service configuration class"
echo "services/[service]/src/config/index.ts         # Configuration exports"
echo "services/[service]/src/app.module.ts.backup.*  # Backup of original files"
echo ""
echo "🔍 Next Steps:"
echo "1. Review generated configuration classes"
echo "2. Test configuration validation"
echo "3. Restart services to apply new configuration"
echo "4. Verify configuration validation works correctly"
echo ""
echo "⚠️  Important Notes:"
echo "• Services will validate configuration on startup"
echo "• Invalid configurations will prevent service startup"
echo "• Configuration validation errors will be clearly displayed"
echo "• All configuration values are now type-safe"
