# Component Architecture

## Directory Structure Overview

### Layout Components
```
src/components/layout/
├── Header.tsx          🔄 NEXT
├── Footer.tsx          🔄 NEXT  
├── Navigation.tsx      🔄 NEXT
└── Layout.tsx          🔄 NEXT
```

### Home Components
```
src/components/home/
├── HeroSection.tsx     ✅ COMPLETED
├── HowItWorks.tsx      ✅ COMPLETED
└── PopularProjects.tsx ✅ COMPLETED
```

### Projects Components
```
src/components/projects/
├── ProjectCard.tsx     ⏳ PENDING
├── ProjectList.tsx     ⏳ PENDING
├── ProjectDetail.tsx   ⏳ PENDING
├── ProjectFilters.tsx  ⏳ PENDING
└── JoinCampaignModal.tsx ⏳ PENDING
```

### Dashboard Components
```
src/components/dashboard/
├── DashboardStats.tsx  ✅ EXISTS
├── CampaignProgress.tsx ⏳ PENDING
├── NFTPreview.tsx      ⏳ PENDING
└── ActivityTimeline.tsx ⏳ PENDING
```

### NFT Components
```
src/components/nft/
├── NFTCard.tsx         ⏳ PENDING
├── NFTGallery.tsx      ⏳ PENDING
├── NFTDetail.tsx       ⏳ PENDING
└── EvolutionTracker.tsx ⏳ PENDING
```

## Implementation Priority
1. Layout components (Header, Footer, Navigation)
2. Projects system (List, Detail, Filters)
3. Dashboard enhancements
4. NFT management system
5. Marketplace functionality
