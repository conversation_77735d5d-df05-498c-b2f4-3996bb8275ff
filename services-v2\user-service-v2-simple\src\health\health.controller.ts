/**
 * Health Controller - User Service V2 Simple
 */

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PrismaService } from '../prisma/prisma.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(private readonly prisma: PrismaService) {}

  @Get()
  @ApiOperation({ summary: 'Simple health check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async getHealth() {
    const dbHealth = await this.prisma.healthCheck();
    
    return {
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        service: 'user-service-v2-simple',
        version: '2.0.0',
        database: dbHealth,
      },
      message: 'User Service V2 Simple is healthy',
    };
  }

  @Get('ready')
  @ApiOperation({ summary: 'Readiness probe' })
  @ApiResponse({ status: 200, description: 'Service is ready' })
  async getReadiness() {
    const dbHealth = await this.prisma.healthCheck();
    const isReady = dbHealth.status === 'healthy';
    
    return {
      success: isReady,
      data: {
        status: isReady ? 'ready' : 'not-ready',
        timestamp: new Date().toISOString(),
        checks: {
          database: dbHealth.status === 'healthy',
        },
      },
      message: isReady ? 'Service is ready' : 'Service is not ready',
    };
  }

  @Get('live')
  @ApiOperation({ summary: 'Liveness probe' })
  @ApiResponse({ status: 200, description: 'Service is alive' })
  async getLiveness() {
    return {
      success: true,
      data: {
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      },
      message: 'Service is alive',
    };
  }
}
