import { <PERSON>, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';
import { Public } from './auth/public.decorator';

@ApiTags('app')
@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(private readonly appService: AppService) {}

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get service information' })
  @ApiResponse({ status: 200, description: 'Service information retrieved successfully' })
  getServiceInfo() {
    this.logger.log('Service info requested');
    return this.appService.getServiceInfo();
  }

  @Get('health')
  @Public()
  @ApiOperation({ summary: 'Basic health check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  getHealth() {
    this.logger.debug('Basic health check performed');
    return {
      status: 'ok',
      service: 'profile-analysis-service',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      port: process.env.SERVICE_PORT || 3002,
    };
  }
}
