// NFT Generation Service V2 Database Schema
// Database Per Service Pattern - This is the NFT GENERATION SERVICE database only

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NFT Generation Tables (NFT Generation Service Domain)
model NFTGeneration {
  id       String @id @default(cuid())
  userId   String // Reference to user in User Service (via API calls)
  
  // Generation Request Details
  requestId     String  @unique
  title         String
  description   String?
  
  // Generation Configuration
  style         String  // realistic, cartoon, abstract, pixel_art, etc.
  theme         String? // nature, tech, fantasy, portrait, etc.
  dimensions    String  @default("1024x1024") // 512x512, 1024x1024, etc.
  
  // AI Generation Parameters
  prompt        String
  negativePrompt String?
  seed          Int?
  steps         Int     @default(50)
  guidance      Float   @default(7.5)
  
  // Generation Status
  status        String @default("pending") // pending, processing, completed, failed
  progress      Int    @default(0) // 0-100
  
  // Generated Content
  imageUrl      String?
  thumbnailUrl  String?
  metadata      Json?   // Generation metadata and parameters
  
  // NFT Metadata
  nftMetadata   Json?   // Standard NFT metadata format
  
  // File Information
  fileSize      Int?
  fileFormat    String? // png, jpg, gif, etc.
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  completedAt   DateTime?
  
  // Relationships within NFT Generation Service
  variations    NFTVariation[]
  collections   NFTCollectionItem[]
  
  @@map("nft_generations")
}

model NFTVariation {
  id           String @id @default(cuid())
  generationId String
  
  // Variation Details
  variationType String // style_variation, color_variation, composition_variation
  title         String
  description   String?
  
  // Variation Parameters
  prompt        String
  seed          Int?
  
  // Generated Content
  imageUrl      String?
  thumbnailUrl  String?
  
  // Variation Status
  status        String @default("pending")
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relationships
  generation NFTGeneration @relation(fields: [generationId], references: [id], onDelete: Cascade)
  
  @@map("nft_variations")
}

model NFTTemplate {
  id          String @id @default(cuid())
  
  // Template Details
  name        String
  description String?
  category    String // portrait, landscape, abstract, etc.
  style       String
  
  // Template Configuration
  basePrompt  String
  parameters  Json   // Default generation parameters
  
  // Template Assets
  previewUrl  String?
  exampleUrls Json?  // Array of example URLs
  
  // Template Status
  isActive    Boolean @default(true)
  isPublic    Boolean @default(true)
  isPremium   Boolean @default(false)
  
  // Usage Statistics
  usageCount  Int @default(0)
  rating      Float?
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("nft_templates")
}

model NFTCollection {
  id          String @id @default(cuid())
  userId      String
  
  // Collection Details
  name        String
  description String?
  symbol      String? // Collection symbol for blockchain
  
  // Collection Configuration
  maxSupply   Int?
  royalty     Float? // Royalty percentage
  
  // Collection Status
  isPublic    Boolean @default(false)
  isPublished Boolean @default(false)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  items       NFTCollectionItem[]
  
  @@map("nft_collections")
}

model NFTCollectionItem {
  id           String @id @default(cuid())
  collectionId String
  generationId String
  
  // Item Details
  tokenId      Int?
  name         String
  description  String?
  
  // Item Attributes
  attributes   Json?  // NFT attributes/traits
  rarity       String? // common, rare, epic, legendary
  
  // Item Status
  isMinted     Boolean @default(false)
  isListed     Boolean @default(false)
  
  // Timestamps
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  // Relationships
  collection   NFTCollection @relation(fields: [collectionId], references: [id], onDelete: Cascade)
  generation   NFTGeneration @relation(fields: [generationId], references: [id], onDelete: Cascade)
  
  @@unique([collectionId, tokenId])
  @@map("nft_collection_items")
}

model GenerationQueue {
  id        String @id @default(cuid())
  
  // Queue Details
  userId    String
  requestId String @unique
  type      String // single, batch, variation
  priority  Int    @default(5) // 1-10, higher = more priority
  
  // Queue Status
  status    String @default("queued") // queued, processing, completed, failed
  attempts  Int    @default(0)
  maxAttempts Int  @default(3)
  
  // Processing Details
  assignedWorker String?
  startedAt      DateTime?
  completedAt    DateTime?
  
  // Error Handling
  lastError      String?
  errorCount     Int @default(0)
  
  // Queue Data
  payload   Json   // Generation request data
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("generation_queue")
}

model AIModel {
  id          String @id @default(cuid())
  
  // Model Details
  name        String
  description String?
  type        String // stable_diffusion, dalle, midjourney, custom
  version     String
  
  // Model Configuration
  config      Json   // Model parameters and settings
  endpoint    String? // API endpoint if external
  
  // Model Capabilities
  maxDimensions String @default("1024x1024")
  supportedFormats Json @default("[]") // ["png", "jpg", "gif"]
  
  // Model Performance
  avgGenerationTime Float? // Average generation time in seconds
  successRate       Float? // Success rate percentage
  
  // Model Status
  isActive    Boolean @default(true)
  isDefault   Boolean @default(false)
  
  // Pricing
  costPerGeneration Float? // Cost per generation
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("ai_models")
}

model GenerationCache {
  id        String @id @default(cuid())
  
  // Cache Key
  cacheKey  String @unique
  userId    String?
  
  // Cache Data
  imageUrl  String
  metadata  Json
  
  // Cache Metadata
  size      Int?
  hits      Int @default(0)
  
  // Cache Expiry
  expiresAt DateTime
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastAccessed DateTime @default(now())
  
  @@map("generation_cache")
}

model GenerationStats {
  id        String @id @default(cuid())
  
  // Stats Period
  date      DateTime @db.Date
  userId    String?
  
  // Generation Statistics
  totalGenerations    Int @default(0)
  successfulGenerations Int @default(0)
  failedGenerations   Int @default(0)
  
  // Performance Statistics
  avgGenerationTime   Float?
  totalProcessingTime Float?
  
  // Usage Statistics
  uniqueUsers         Int @default(0)
  totalCost          Float?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([date, userId])
  @@map("generation_stats")
}
