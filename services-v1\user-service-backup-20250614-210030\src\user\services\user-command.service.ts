import { Injectable, Logger, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AuditService } from '../../common/services/audit.service';
import { CreateUserDto, UpdateUserDto } from '../dto';
import { CompleteProfileDto } from '../dto/complete-profile.dto';
import { LinkSocialAccountDto, UnlinkSocialAccountDto, UpdateSocialAccountDto, SocialPlatform } from '../dto/link-social-account.dto';
import { NotificationPreferencesDto } from '../dto/complete-profile.dto';
import { RequestContext } from '../interfaces/request-context.interface';

export interface UserCommandResult {
  success: boolean;
  data?: any;
  error?: string;
  fallback?: boolean;
}

@Injectable()
export class UserCommandService {
  private readonly logger = new Logger(UserCommandService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly auditService: AuditService,
  ) {}

  async createUser(createUserDto: CreateUserDto, context: RequestContext): Promise<UserCommandResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;
    
    try {
      this.logger.log(`Creating user: ${createUserDto.email}`, { correlationId });
      
      // Business rule validation
      await this.validateBusinessRules(createUserDto);
      
      // Create user with enterprise transaction
      const user = await this.prisma.$transaction(async (tx) => {
        // Create user command
        const newUser = await tx.userCommand.create({
          data: {
            ...createUserDto,
            createdBy: context.userId,
            updatedBy: context.userId,
            version: 1,
          },
        });
        
        // Create corresponding query model
        await tx.userQuery.create({
          data: {
            id: newUser.id,
            username: newUser.username,
            email: newUser.email,
            displayName: newUser.username,
            role: newUser.role,
            isActive: newUser.isActive,
            isEmailVerified: newUser.isEmailVerified,
            createdAt: newUser.createdAt,
            lastUpdated: new Date(),
          },
        });
        
        // Create audit log
        await this.auditService.logAction({
          entityType: 'User',
          entityId: newUser.id,
          action: 'CREATE',
          resource: 'users',
          newValues: newUser,
          userId: context.userId,
          sessionId: context.sessionId,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          correlationId,
        });
        
        return newUser;
      });
      
      // TODO: Publish domain event when event bus is configured
      this.logger.debug(`User created event would be published for: ${user.id}`, { correlationId });
      
      this.logger.log(`User created successfully: ${user.id}`, { 
        correlationId, 
        duration: Date.now() - startTime 
      });
      
      return { success: true, data: user };
      
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`, { 
        correlationId, 
        error: error.stack,
        duration: Date.now() - startTime
      });
      
      if (error.code === 'P2002') {
        throw new ConflictException('User with this email or username already exists');
      }
      
      throw error;
    }
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto, context: RequestContext): Promise<UserCommandResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;
    
    try {
      this.logger.log(`Updating user: ${id}`, { correlationId });
      
      // Get existing user for audit trail
      const existingUser = await this.prisma.userCommand.findUnique({ where: { id } });
      if (!existingUser) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      
      // Update with enterprise transaction
      const updatedUser = await this.prisma.$transaction(async (tx) => {
        // Update command model
        const user = await tx.userCommand.update({
          where: { id },
          data: {
            ...updateUserDto,
            updatedBy: context.userId,
            version: { increment: 1 },
          },
        });
        
        // Update query model
        await tx.userQuery.update({
          where: { id },
          data: {
            username: user.username,
            email: user.email,
            role: user.role,
            isActive: user.isActive,
            isEmailVerified: user.isEmailVerified,
            lastUpdated: new Date(),
          },
        });
        
        // Create audit log
        await this.auditService.logAction({
          entityType: 'User',
          entityId: user.id,
          action: 'UPDATE',
          resource: 'users',
          oldValues: existingUser,
          newValues: user,
          changedFields: Object.keys(updateUserDto),
          userId: context.userId,
          sessionId: context.sessionId,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          correlationId,
        });
        
        return user;
      });
      
      // TODO: Publish domain event when event bus is configured
      this.logger.debug(`User updated event would be published for: ${updatedUser.id}`, { correlationId });
      
      this.logger.log(`User updated successfully: ${id}`, { 
        correlationId, 
        duration: Date.now() - startTime 
      });
      
      return { success: true, data: updatedUser };
      
    } catch (error) {
      this.logger.error(`Failed to update user: ${error.message}`, { 
        correlationId, 
        userId: id,
        error: error.stack,
        duration: Date.now() - startTime
      });
      
      throw error;
    }
  }

  /**
   * Complete user profile with enhanced information
   */
  async completeUserProfile(userId: string, profileData: CompleteProfileDto, context: RequestContext): Promise<UserCommandResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Completing profile for user: ${userId}`, { correlationId });

      // Get existing user
      const existingUser = await this.prisma.userCommand.findUnique({ where: { id: userId } });
      if (!existingUser) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Update user with profile completion
      const updatedUser = await this.prisma.$transaction(async (tx) => {
        // Update command model
        const user = await tx.userCommand.update({
          where: { id: userId },
          data: {
            displayName: profileData.displayName,
            bio: profileData.bio,
            profileImage: profileData.profileImage,
            location: profileData.location,
            website: profileData.website,
            interests: profileData.interests,
            isProfileComplete: true,
            updatedBy: context.userId,
            version: { increment: 1 },
          },
        });

        // Update query model
        await tx.userQuery.update({
          where: { id: userId },
          data: {
            displayName: profileData.displayName,
            bio: profileData.bio,
            profileImage: profileData.profileImage,
            location: profileData.location,
            website: profileData.website,
            isProfileComplete: true,
            lastUpdated: new Date(),
          },
        });

        return user;
      });

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: userId,
        action: 'PROFILE_COMPLETED',
        resource: 'users',
        oldValues: existingUser,
        newValues: updatedUser,
        userId: context.userId,
        correlationId: context.correlationId,
        metadata: { profileData },
      });

      this.logger.log(`Profile completed successfully for user: ${userId}`, {
        correlationId,
        duration: Date.now() - startTime
      });

      return { success: true, data: updatedUser };

    } catch (error) {
      this.logger.error(`Failed to complete profile for user ${userId}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });

      throw error;
    }
  }

  /**
   * Link social media account to user
   */
  async linkSocialAccount(userId: string, socialData: LinkSocialAccountDto, context: RequestContext): Promise<UserCommandResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Linking ${socialData.platform} account for user: ${userId}`, { correlationId });

      // Verify user exists
      const user = await this.prisma.userCommand.findUnique({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Update user's Twitter fields if linking Twitter (for backward compatibility)
      if (socialData.platform === SocialPlatform.TWITTER) {
        await this.prisma.userCommand.update({
          where: { id: userId },
          data: {
            twitterUsername: socialData.username,
            twitterId: socialData.platformUserId,
            updatedBy: context.userId,
            version: { increment: 1 },
          },
        });

        await this.prisma.userQuery.update({
          where: { id: userId },
          data: {
            twitterUsername: socialData.username,
            lastUpdated: new Date(),
          },
        });
      }

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: userId,
        action: 'SOCIAL_ACCOUNT_LINKED',
        resource: 'users',
        newValues: socialData,
        userId: context.userId,
        correlationId: context.correlationId,
        metadata: { platform: socialData.platform },
      });

      this.logger.log(`${socialData.platform} account linked successfully for user: ${userId}`, {
        correlationId,
        duration: Date.now() - startTime
      });

      return { success: true, data: socialData };

    } catch (error) {
      this.logger.error(`Failed to link ${socialData.platform} account for user ${userId}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });

      throw error;
    }
  }

  /**
   * Update notification preferences for user
   */
  async updateNotificationPreferences(userId: string, preferences: NotificationPreferencesDto, context: RequestContext): Promise<UserCommandResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Updating notification preferences for user: ${userId}`, { correlationId });

      // Verify user exists
      const user = await this.prisma.userCommand.findUnique({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'User',
        entityId: userId,
        action: 'NOTIFICATION_PREFERENCES_UPDATED',
        resource: 'users',
        newValues: preferences,
        userId: context.userId,
        correlationId: context.correlationId,
      });

      this.logger.log(`Notification preferences updated successfully for user: ${userId}`, {
        correlationId,
        duration: Date.now() - startTime
      });

      return { success: true, data: preferences };

    } catch (error) {
      this.logger.error(`Failed to update notification preferences for user ${userId}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime
      });

      throw error;
    }
  }

  private async validateBusinessRules(createUserDto: CreateUserDto): Promise<void> {
    // Enterprise business rule validation
    if (!createUserDto.email.includes('@')) {
      throw new Error('Invalid email format');
    }
    
    if (createUserDto.username.length < 3) {
      throw new Error('Username must be at least 3 characters');
    }
    
    // Add more business rules as needed
  }
}
