/**
 * Standardized Configuration Service
 * 
 * Provides type-safe, validated configuration management across all microservices
 * Implements consistent configuration patterns and validation
 */

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Service Configuration Interface
 */
export interface ServiceConfig {
  serviceName: string;
  port: number;
  nodeEnv: 'development' | 'production' | 'test';
  version: string;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
}

/**
 * Database Configuration Interface
 */
export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  url: string;
  ssl: boolean;
  maxConnections: number;
  connectionTimeout: number;
}

/**
 * JWT Configuration Interface
 */
export interface JWTConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
  issuer: string;
  audience: string;
}

/**
 * Redis Configuration Interface
 */
export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  maxRetries: number;
  retryDelayOnFailover: number;
}

/**
 * API Gateway Configuration Interface
 */
export interface APIGatewayConfig {
  url: string;
  timeout: number;
  retries: number;
  circuitBreakerThreshold: number;
  circuitBreakerTimeout: number;
}

/**
 * Security Configuration Interface
 */
export interface SecurityConfig {
  corsOrigins: string[];
  rateLimitWindow: number;
  rateLimitMax: number;
  enableHelmet: boolean;
  enableCsrf: boolean;
  trustedProxies: string[];
}

/**
 * Monitoring Configuration Interface
 */
export interface MonitoringConfig {
  enableMetrics: boolean;
  enableTracing: boolean;
  enableHealthChecks: boolean;
  metricsPort: number;
  tracingEndpoint?: string;
}

/**
 * Configuration Options
 */
export interface ConfigurationOptions {
  serviceName: string;
  version: string;
  customValidation?: (config: any) => boolean;
}

/**
 * Standardized Configuration Service
 */
@Injectable()
export class StandardizedConfigService {
  private readonly options: ConfigurationOptions;

  constructor(
    private readonly configService: ConfigService,
    options: ConfigurationOptions
  ) {
    this.options = options;
    this.validateConfiguration();
  }

  /**
   * Get service configuration
   */
  getServiceConfig(): ServiceConfig {
    return {
      serviceName: this.options.serviceName,
      port: this.configService.get<number>('SERVICE_PORT') || this.getDefaultPort(),
      nodeEnv: this.configService.get<'development' | 'production' | 'test'>('NODE_ENV') || 'development',
      version: this.options.version,
      logLevel: this.configService.get<'error' | 'warn' | 'info' | 'debug'>('LOG_LEVEL') || 'info',
    };
  }

  /**
   * Get database configuration
   */
  getDatabaseConfig(): DatabaseConfig {
    const databaseUrl = this.configService.get<string>('DATABASE_URL');
    
    if (databaseUrl) {
      // Parse DATABASE_URL if provided
      const parsed = this.parseDatabaseUrl(databaseUrl);
      return {
        url: databaseUrl,
        host: parsed.host!,
        port: parsed.port!,
        database: parsed.database!,
        username: parsed.username!,
        password: parsed.password!,
        ssl: this.configService.get<boolean>('DB_SSL') || false,
        maxConnections: this.configService.get<number>('DB_MAX_CONNECTIONS') || 10,
        connectionTimeout: this.configService.get<number>('DB_CONNECTION_TIMEOUT') || 30000,
      };
    }

    // Use individual environment variables
    return {
      host: this.configService.get<string>('DB_HOST') || 'localhost',
      port: this.configService.get<number>('DB_PORT') || 5432,
      database: this.configService.get<string>('DB_DATABASE') || this.options.serviceName.replace('-', '_'),
      username: this.configService.get<string>('DB_USERNAME') || 'postgres',
      password: this.configService.get<string>('DB_PASSWORD') || '',
      url: this.buildDatabaseUrl(),
      ssl: this.configService.get<boolean>('DB_SSL') || false,
      maxConnections: this.configService.get<number>('DB_MAX_CONNECTIONS') || 10,
      connectionTimeout: this.configService.get<number>('DB_CONNECTION_TIMEOUT') || 30000,
    };
  }

  /**
   * Get JWT configuration
   */
  getJWTConfig(): JWTConfig {
    return {
      secret: this.configService.get<string>('JWT_SECRET') || this.generateDefaultSecret(),
      expiresIn: this.configService.get<string>('JWT_EXPIRES_IN') || '24h',
      refreshExpiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN') || '7d',
      issuer: this.configService.get<string>('JWT_ISSUER') || this.options.serviceName,
      audience: this.configService.get<string>('JWT_AUDIENCE') || 'social-nft-platform',
    };
  }

  /**
   * Get Redis configuration
   */
  getRedisConfig(): RedisConfig {
    return {
      host: this.configService.get<string>('REDIS_HOST') || 'localhost',
      port: this.configService.get<number>('REDIS_PORT') || 6379,
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_DB') || 0,
      maxRetries: this.configService.get<number>('REDIS_MAX_RETRIES') || 3,
      retryDelayOnFailover: this.configService.get<number>('REDIS_RETRY_DELAY') || 100,
    };
  }

  /**
   * Get API Gateway configuration
   */
  getAPIGatewayConfig(): APIGatewayConfig {
    return {
      url: this.configService.get<string>('API_GATEWAY_URL') || 'http://localhost:3010',
      timeout: this.configService.get<number>('API_GATEWAY_TIMEOUT') || 30000,
      retries: this.configService.get<number>('API_GATEWAY_RETRIES') || 3,
      circuitBreakerThreshold: this.configService.get<number>('API_GATEWAY_CB_THRESHOLD') || 5,
      circuitBreakerTimeout: this.configService.get<number>('API_GATEWAY_CB_TIMEOUT') || 60000,
    };
  }

  /**
   * Get security configuration
   */
  getSecurityConfig(): SecurityConfig {
    const corsOrigins = this.configService.get<string>('CORS_ORIGINS');
    
    return {
      corsOrigins: corsOrigins ? corsOrigins.split(',') : ['http://localhost:3000'],
      rateLimitWindow: this.configService.get<number>('RATE_LIMIT_WINDOW') || 60000,
      rateLimitMax: this.configService.get<number>('RATE_LIMIT_MAX') || 100,
      enableHelmet: this.configService.get<boolean>('ENABLE_HELMET') || true,
      enableCsrf: this.configService.get<boolean>('ENABLE_CSRF') || false,
      trustedProxies: this.configService.get<string>('TRUSTED_PROXIES')?.split(',') || [],
    };
  }

  /**
   * Get monitoring configuration
   */
  getMonitoringConfig(): MonitoringConfig {
    return {
      enableMetrics: this.configService.get<boolean>('ENABLE_METRICS') || true,
      enableTracing: this.configService.get<boolean>('ENABLE_TRACING') || true,
      enableHealthChecks: this.configService.get<boolean>('ENABLE_HEALTH_CHECKS') || true,
      metricsPort: this.configService.get<number>('METRICS_PORT') || 9090,
      tracingEndpoint: this.configService.get<string>('TRACING_ENDPOINT'),
    };
  }

  /**
   * Get environment-specific value
   */
  get<T = any>(key: string, defaultValue?: T): T {
    return this.configService.get<T>(key, defaultValue);
  }

  /**
   * Check if running in development
   */
  isDevelopment(): boolean {
    return this.getServiceConfig().nodeEnv === 'development';
  }

  /**
   * Check if running in production
   */
  isProduction(): boolean {
    return this.getServiceConfig().nodeEnv === 'production';
  }

  /**
   * Check if running in test
   */
  isTest(): boolean {
    return this.getServiceConfig().nodeEnv === 'test';
  }

  /**
   * Validate configuration
   */
  private validateConfiguration(): void {
    const serviceConfig = this.getServiceConfig();
    
    // Validate required fields
    if (!serviceConfig.serviceName) {
      throw new Error('SERVICE_NAME is required');
    }

    if (!serviceConfig.port || serviceConfig.port < 1 || serviceConfig.port > 65535) {
      throw new Error('Valid SERVICE_PORT is required (1-65535)');
    }

    // Validate JWT secret in production
    if (this.isProduction()) {
      const jwtConfig = this.getJWTConfig();
      if (!jwtConfig.secret || jwtConfig.secret.length < 32) {
        throw new Error('JWT_SECRET must be at least 32 characters in production');
      }
    }

    // Run custom validation if provided
    if (this.options.customValidation) {
      const allConfig = {
        service: serviceConfig,
        database: this.getDatabaseConfig(),
        jwt: this.getJWTConfig(),
        redis: this.getRedisConfig(),
        apiGateway: this.getAPIGatewayConfig(),
        security: this.getSecurityConfig(),
        monitoring: this.getMonitoringConfig(),
      };

      if (!this.options.customValidation(allConfig)) {
        throw new Error('Custom configuration validation failed');
      }
    }
  }

  /**
   * Get default port based on service name
   */
  private getDefaultPort(): number {
    const portMap: Record<string, number> = {
      'user-service': 3001,
      'profile-analysis-service': 3002,
      'nft-generation-service': 3003,
      'blockchain-service': 3004,
      'marketplace-service': 3005,
      'project-service': 3006,
      'analytics-service': 3007,
      'notification-service': 3008,
      'api-gateway': 3010,
    };

    return portMap[this.options.serviceName] || 3000;
  }

  /**
   * Parse database URL
   */
  private parseDatabaseUrl(url: string): Partial<DatabaseConfig> {
    try {
      const parsed = new URL(url);
      return {
        host: parsed.hostname || 'localhost',
        port: parseInt(parsed.port) || 5432,
        database: parsed.pathname.substring(1) || 'defaultdb',
        username: parsed.username || 'postgres',
        password: parsed.password || '',
      };
    } catch (error) {
      throw new Error(`Invalid DATABASE_URL format: ${error.message}`);
    }
  }

  /**
   * Build database URL from individual components
   */
  private buildDatabaseUrl(): string {
    const host = this.configService.get<string>('DB_HOST') || 'localhost';
    const port = this.configService.get<number>('DB_PORT') || 5432;
    const database = this.configService.get<string>('DB_DATABASE') || this.options.serviceName.replace('-', '_');
    const username = this.configService.get<string>('DB_USERNAME') || 'postgres';
    const password = this.configService.get<string>('DB_PASSWORD') || '';

    return `postgresql://${username}:${password}@${host}:${port}/${database}`;
  }

  /**
   * Generate default JWT secret (for development only)
   */
  private generateDefaultSecret(): string {
    if (this.isProduction()) {
      throw new Error('JWT_SECRET must be explicitly set in production');
    }

    return `dev-secret-${this.options.serviceName}-${Date.now()}`;
  }
}
