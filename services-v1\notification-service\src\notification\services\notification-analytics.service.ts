import { Injectable, Logger } from '@nestjs/common';
import { NotificationManagementService, Notification } from './notification-management.service';

export interface NotificationAnalytics {
  overview: {
    totalNotifications: number;
    sentNotifications: number;
    deliveredNotifications: number;
    failedNotifications: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
  };
  byChannel: {
    email: ChannelMetrics;
    sms: ChannelMetrics;
    push: ChannelMetrics;
    inApp: ChannelMetrics;
  };
  byCategory: Record<string, CategoryMetrics>;
  trends: {
    daily: Array<{ date: string; sent: number; delivered: number; opened: number; clicked: number }>;
    hourly: Array<{ hour: number; sent: number; delivered: number }>;
  };
  topPerformers: Array<{
    type: string;
    category: string;
    title: string;
    sentCount: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
  }>;
  userEngagement: {
    activeUsers: number;
    averageNotificationsPerUser: number;
    topEngagedUsers: Array<{
      userId: string;
      notificationCount: number;
      openRate: number;
      clickRate: number;
    }>;
  };
}

export interface ChannelMetrics {
  total: number;
  sent: number;
  delivered: number;
  failed: number;
  opened: number;
  clicked: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  averageDeliveryTime: number; // in seconds
}

export interface CategoryMetrics {
  total: number;
  sent: number;
  delivered: number;
  openRate: number;
  clickRate: number;
  averageEngagementTime: number; // time from delivery to click
}

export interface NotificationPerformance {
  notificationId: string;
  type: string;
  category: string;
  title: string;
  sentAt?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  clickedAt?: Date;
  deliveryTime?: number; // seconds from sent to delivered
  engagementTime?: number; // seconds from delivered to read
  clickTime?: number; // seconds from read to click
  performance: 'excellent' | 'good' | 'average' | 'poor';
}

export interface CampaignAnalytics {
  campaignId: string;
  name: string;
  startDate: Date;
  endDate?: Date;
  totalNotifications: number;
  uniqueRecipients: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  conversionRate: number;
  roi: number;
  costPerNotification: number;
  revenueGenerated: number;
}

@Injectable()
export class NotificationAnalyticsService {
  private readonly logger = new Logger(NotificationAnalyticsService.name);

  constructor(private readonly notificationManagement: NotificationManagementService) {}

  /**
   * Get comprehensive notification analytics
   */
  async getNotificationAnalytics(
    dateFrom?: Date,
    dateTo?: Date,
    userId?: string
  ): Promise<NotificationAnalytics> {
    try {
      this.logger.log('Generating notification analytics', { dateFrom, dateTo, userId });

      // Get notifications with filters
      const { notifications } = await this.notificationManagement.getNotifications(
        {
          userId,
          dateFrom,
          dateTo,
        },
        10000 // Get all for analytics
      );

      // Calculate overview metrics
      const overview = this.calculateOverviewMetrics(notifications);

      // Calculate channel metrics
      const byChannel = this.calculateChannelMetrics(notifications);

      // Calculate category metrics
      const byCategory = this.calculateCategoryMetrics(notifications);

      // Calculate trends
      const trends = this.calculateTrends(notifications);

      // Calculate top performers
      const topPerformers = this.calculateTopPerformers(notifications);

      // Calculate user engagement
      const userEngagement = this.calculateUserEngagement(notifications);

      return {
        overview,
        byChannel,
        byCategory,
        trends,
        topPerformers,
        userEngagement,
      };
    } catch (error) {
      this.logger.error(`Failed to get notification analytics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get notification performance analysis
   */
  async getNotificationPerformance(
    limit: number = 100,
    sortBy: 'deliveryTime' | 'engagementTime' | 'clickTime' = 'engagementTime'
  ): Promise<NotificationPerformance[]> {
    try {
      const { notifications } = await this.notificationManagement.getNotifications({}, limit * 2);

      const performances: NotificationPerformance[] = notifications
        .filter(n => n.tracking.sentAt && n.tracking.deliveredAt)
        .map(notification => {
          const deliveryTime = notification.tracking.deliveredAt && notification.tracking.sentAt
            ? (notification.tracking.deliveredAt.getTime() - notification.tracking.sentAt.getTime()) / 1000
            : undefined;

          const engagementTime = notification.tracking.readAt && notification.tracking.deliveredAt
            ? (notification.tracking.readAt.getTime() - notification.tracking.deliveredAt.getTime()) / 1000
            : undefined;

          const clickTime = notification.tracking.clickedAt && notification.tracking.readAt
            ? (notification.tracking.clickedAt.getTime() - notification.tracking.readAt.getTime()) / 1000
            : undefined;

          // Calculate performance score
          let performance: NotificationPerformance['performance'] = 'poor';
          if (notification.tracking.clickedAt) {
            performance = 'excellent';
          } else if (notification.tracking.readAt) {
            performance = 'good';
          } else if (notification.tracking.deliveredAt) {
            performance = 'average';
          }

          return {
            notificationId: notification.id,
            type: notification.type,
            category: notification.category,
            title: notification.title,
            sentAt: notification.tracking.sentAt,
            deliveredAt: notification.tracking.deliveredAt,
            readAt: notification.tracking.readAt,
            clickedAt: notification.tracking.clickedAt,
            deliveryTime,
            engagementTime,
            clickTime,
            performance,
          };
        });

      // Sort by specified criteria
      performances.sort((a, b) => {
        const aValue = a[sortBy] || 0;
        const bValue = b[sortBy] || 0;
        return aValue - bValue;
      });

      return performances.slice(0, limit);
    } catch (error) {
      this.logger.error(`Failed to get notification performance: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get real-time notification dashboard
   */
  async getNotificationDashboard(): Promise<{
    realTime: {
      notificationsSentLast24h: number;
      notificationsDeliveredLast24h: number;
      currentDeliveryRate: number;
      averageDeliveryTime: number;
    };
    alerts: Array<{
      type: 'delivery_failure' | 'low_engagement' | 'high_volume';
      message: string;
      severity: 'low' | 'medium' | 'high';
      timestamp: Date;
    }>;
    recentActivity: Array<{
      type: 'sent' | 'delivered' | 'opened' | 'clicked';
      notificationId: string;
      userId: string;
      timestamp: Date;
    }>;
  }> {
    try {
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const { notifications } = await this.notificationManagement.getNotifications(
        { dateFrom: yesterday },
        1000
      );

      // Calculate real-time metrics
      const sentLast24h = notifications.filter(n => n.tracking.sentAt).length;
      const deliveredLast24h = notifications.filter(n => n.tracking.deliveredAt).length;
      const currentDeliveryRate = sentLast24h > 0 ? (deliveredLast24h / sentLast24h) * 100 : 0;

      const deliveryTimes = notifications
        .filter(n => n.tracking.sentAt && n.tracking.deliveredAt)
        .map(n => (n.tracking.deliveredAt!.getTime() - n.tracking.sentAt!.getTime()) / 1000);
      
      const averageDeliveryTime = deliveryTimes.length > 0
        ? deliveryTimes.reduce((sum, time) => sum + time, 0) / deliveryTimes.length
        : 0;

      // Generate alerts
      const alerts = [];
      if (currentDeliveryRate < 90) {
        alerts.push({
          type: 'delivery_failure' as const,
          message: `Delivery rate is below 90% (${currentDeliveryRate.toFixed(1)}%)`,
          severity: 'high' as const,
          timestamp: new Date(),
        });
      }

      if (sentLast24h > 1000) {
        alerts.push({
          type: 'high_volume' as const,
          message: `High notification volume: ${sentLast24h} notifications sent in last 24h`,
          severity: 'medium' as const,
          timestamp: new Date(),
        });
      }

      // Recent activity
      const recentActivity = notifications
        .slice(0, 20)
        .flatMap(notification => {
          const activities = [];
          if (notification.tracking.sentAt) {
            activities.push({
              type: 'sent' as const,
              notificationId: notification.id,
              userId: notification.userId,
              timestamp: notification.tracking.sentAt,
            });
          }
          if (notification.tracking.deliveredAt) {
            activities.push({
              type: 'delivered' as const,
              notificationId: notification.id,
              userId: notification.userId,
              timestamp: notification.tracking.deliveredAt,
            });
          }
          if (notification.tracking.readAt) {
            activities.push({
              type: 'opened' as const,
              notificationId: notification.id,
              userId: notification.userId,
              timestamp: notification.tracking.readAt,
            });
          }
          if (notification.tracking.clickedAt) {
            activities.push({
              type: 'clicked' as const,
              notificationId: notification.id,
              userId: notification.userId,
              timestamp: notification.tracking.clickedAt,
            });
          }
          return activities;
        })
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 50);

      return {
        realTime: {
          notificationsSentLast24h: sentLast24h,
          notificationsDeliveredLast24h: deliveredLast24h,
          currentDeliveryRate,
          averageDeliveryTime,
        },
        alerts,
        recentActivity,
      };
    } catch (error) {
      this.logger.error(`Failed to get notification dashboard: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate overview metrics
   */
  private calculateOverviewMetrics(notifications: Notification[]) {
    const total = notifications.length;
    const sent = notifications.filter(n => n.tracking.sentAt).length;
    const delivered = notifications.filter(n => n.tracking.deliveredAt).length;
    const failed = notifications.filter(n => n.status === 'failed').length;
    const opened = notifications.filter(n => n.tracking.readAt).length;
    const clicked = notifications.filter(n => n.tracking.clickedAt).length;

    return {
      totalNotifications: total,
      sentNotifications: sent,
      deliveredNotifications: delivered,
      failedNotifications: failed,
      deliveryRate: sent > 0 ? (delivered / sent) * 100 : 0,
      openRate: delivered > 0 ? (opened / delivered) * 100 : 0,
      clickRate: opened > 0 ? (clicked / opened) * 100 : 0,
    };
  }

  /**
   * Calculate channel metrics
   */
  private calculateChannelMetrics(notifications: Notification[]) {
    const channels = ['email', 'sms', 'push', 'in_app'] as const;
    const metrics: any = {};

    channels.forEach(channel => {
      const channelNotifications = notifications.filter(n => n.type === channel);
      const sent = channelNotifications.filter(n => n.tracking.sentAt).length;
      const delivered = channelNotifications.filter(n => n.tracking.deliveredAt).length;
      const failed = channelNotifications.filter(n => n.status === 'failed').length;
      const opened = channelNotifications.filter(n => n.tracking.readAt).length;
      const clicked = channelNotifications.filter(n => n.tracking.clickedAt).length;

      const deliveryTimes = channelNotifications
        .filter(n => n.tracking.sentAt && n.tracking.deliveredAt)
        .map(n => (n.tracking.deliveredAt!.getTime() - n.tracking.sentAt!.getTime()) / 1000);

      metrics[channel] = {
        total: channelNotifications.length,
        sent,
        delivered,
        failed,
        opened,
        clicked,
        deliveryRate: sent > 0 ? (delivered / sent) * 100 : 0,
        openRate: delivered > 0 ? (opened / delivered) * 100 : 0,
        clickRate: opened > 0 ? (clicked / opened) * 100 : 0,
        averageDeliveryTime: deliveryTimes.length > 0
          ? deliveryTimes.reduce((sum, time) => sum + time, 0) / deliveryTimes.length
          : 0,
      };
    });

    return metrics;
  }

  /**
   * Calculate category metrics
   */
  private calculateCategoryMetrics(notifications: Notification[]) {
    const categories = [...new Set(notifications.map(n => n.category))];
    const metrics: Record<string, CategoryMetrics> = {};

    categories.forEach(category => {
      const categoryNotifications = notifications.filter(n => n.category === category);
      const sent = categoryNotifications.filter(n => n.tracking.sentAt).length;
      const delivered = categoryNotifications.filter(n => n.tracking.deliveredAt).length;
      const opened = categoryNotifications.filter(n => n.tracking.readAt).length;
      const clicked = categoryNotifications.filter(n => n.tracking.clickedAt).length;

      const engagementTimes = categoryNotifications
        .filter(n => n.tracking.deliveredAt && n.tracking.readAt)
        .map(n => (n.tracking.readAt!.getTime() - n.tracking.deliveredAt!.getTime()) / 1000);

      metrics[category] = {
        total: categoryNotifications.length,
        sent,
        delivered,
        openRate: delivered > 0 ? (opened / delivered) * 100 : 0,
        clickRate: opened > 0 ? (clicked / opened) * 100 : 0,
        averageEngagementTime: engagementTimes.length > 0
          ? engagementTimes.reduce((sum, time) => sum + time, 0) / engagementTimes.length
          : 0,
      };
    });

    return metrics;
  }

  /**
   * Calculate trends
   */
  private calculateTrends(notifications: Notification[]) {
    // Daily trends for last 7 days
    const daily = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const dayNotifications = notifications.filter(n => {
        const notificationDate = n.metadata.createdAt.toISOString().split('T')[0];
        return notificationDate === dateStr;
      });

      daily.push({
        date: dateStr,
        sent: dayNotifications.filter(n => n.tracking.sentAt).length,
        delivered: dayNotifications.filter(n => n.tracking.deliveredAt).length,
        opened: dayNotifications.filter(n => n.tracking.readAt).length,
        clicked: dayNotifications.filter(n => n.tracking.clickedAt).length,
      });
    }

    // Hourly trends for last 24 hours
    const hourly = [];
    for (let i = 23; i >= 0; i--) {
      const hour = new Date();
      hour.setHours(hour.getHours() - i);
      const hourValue = hour.getHours();

      const hourNotifications = notifications.filter(n => {
        return n.metadata.createdAt.getHours() === hourValue &&
               n.metadata.createdAt.toDateString() === hour.toDateString();
      });

      hourly.push({
        hour: hourValue,
        sent: hourNotifications.filter(n => n.tracking.sentAt).length,
        delivered: hourNotifications.filter(n => n.tracking.deliveredAt).length,
      });
    }

    return { daily, hourly };
  }

  /**
   * Calculate top performers
   */
  private calculateTopPerformers(notifications: Notification[]) {
    const performers = new Map<string, any>();

    notifications.forEach(notification => {
      const key = `${notification.type}_${notification.category}_${notification.title}`;
      
      if (!performers.has(key)) {
        performers.set(key, {
          type: notification.type,
          category: notification.category,
          title: notification.title,
          total: 0,
          sent: 0,
          delivered: 0,
          opened: 0,
          clicked: 0,
        });
      }

      const performer = performers.get(key);
      performer.total += 1;
      if (notification.tracking.sentAt) performer.sent += 1;
      if (notification.tracking.deliveredAt) performer.delivered += 1;
      if (notification.tracking.readAt) performer.opened += 1;
      if (notification.tracking.clickedAt) performer.clicked += 1;
    });

    return Array.from(performers.values())
      .map(performer => ({
        ...performer,
        sentCount: performer.sent,
        deliveryRate: performer.sent > 0 ? (performer.delivered / performer.sent) * 100 : 0,
        openRate: performer.delivered > 0 ? (performer.opened / performer.delivered) * 100 : 0,
        clickRate: performer.opened > 0 ? (performer.clicked / performer.opened) * 100 : 0,
      }))
      .sort((a, b) => b.clickRate - a.clickRate)
      .slice(0, 10);
  }

  /**
   * Calculate user engagement
   */
  private calculateUserEngagement(notifications: Notification[]) {
    const userStats = new Map<string, any>();

    notifications.forEach(notification => {
      if (!userStats.has(notification.userId)) {
        userStats.set(notification.userId, {
          userId: notification.userId,
          total: 0,
          delivered: 0,
          opened: 0,
          clicked: 0,
        });
      }

      const stats = userStats.get(notification.userId);
      stats.total += 1;
      if (notification.tracking.deliveredAt) stats.delivered += 1;
      if (notification.tracking.readAt) stats.opened += 1;
      if (notification.tracking.clickedAt) stats.clicked += 1;
    });

    const userEngagementData = Array.from(userStats.values()).map(stats => ({
      userId: stats.userId,
      notificationCount: stats.total,
      openRate: stats.delivered > 0 ? (stats.opened / stats.delivered) * 100 : 0,
      clickRate: stats.opened > 0 ? (stats.clicked / stats.opened) * 100 : 0,
    }));

    const activeUsers = userEngagementData.filter(user => user.notificationCount > 0).length;
    const averageNotificationsPerUser = userEngagementData.length > 0
      ? userEngagementData.reduce((sum, user) => sum + user.notificationCount, 0) / userEngagementData.length
      : 0;

    const topEngagedUsers = userEngagementData
      .sort((a, b) => b.clickRate - a.clickRate)
      .slice(0, 10);

    return {
      activeUsers,
      averageNotificationsPerUser,
      topEngagedUsers,
    };
  }
}
