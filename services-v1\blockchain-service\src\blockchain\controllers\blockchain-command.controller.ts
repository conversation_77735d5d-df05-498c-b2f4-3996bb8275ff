import {
  <PERSON>,
  Post,
  Body,
  Res,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { BlockchainCommandService } from '../services/blockchain-command.service';

@ApiTags('Blockchain')
@Controller('blockchain')
export class BlockchainCommandController {
  private readonly logger = new Logger(BlockchainCommandController.name);

  constructor(private readonly blockchainCommandService: BlockchainCommandService) {}

  @Post('deploy-contract')
  @ApiOperation({
    summary: 'Deploy smart contract',
    description: 'Deploy a new smart contract to the blockchain'
  })
  @ApiBody({
    description: 'Contract deployment data',
    schema: {
      type: 'object',
      properties: {
        contractType: { type: 'string', example: 'ERC721' },
        name: { type: 'string', example: 'My NFT Collection' },
        symbol: { type: 'string', example: 'MNC' },
        network: { type: 'string', example: 'ethereum' },
        parameters: { type: 'object' }
      },
      required: ['contractType', 'name', 'symbol', 'network']
    }
  })
  @ApiResponse({ status: 201, description: 'Contract deployment initiated successfully' })
  async deployContract(@Body() deploymentData: any, @Res() res: Response) {
    try {
      this.logger.log('Deploying smart contract');
      
      const result = await this.blockchainCommandService.deployContract(deploymentData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Contract deployment failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Post('send-transaction')
  @ApiOperation({
    summary: 'Send blockchain transaction',
    description: 'Send a transaction to the blockchain'
  })
  @ApiBody({
    description: 'Transaction data',
    schema: {
      type: 'object',
      properties: {
        to: { type: 'string', example: '******************************************' },
        value: { type: 'string', example: '1000000000000000000' },
        data: { type: 'string', example: '0x' },
        network: { type: 'string', example: 'ethereum' },
        gasLimit: { type: 'number', example: 21000 }
      },
      required: ['to', 'network']
    }
  })
  @ApiResponse({ status: 201, description: 'Transaction sent successfully' })
  async sendTransaction(@Body() transactionData: any, @Res() res: Response) {
    try {
      this.logger.log('Sending blockchain transaction');
      
      const result = await this.blockchainCommandService.sendTransaction(transactionData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Transaction failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }
}
