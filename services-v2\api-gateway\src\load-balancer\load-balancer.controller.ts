import { Controller, Get, Post, Param } from '@nestjs/common';
import {
  ResponseService,
  RequirePermissions,
  Permission,
  Public
} from '../../../../shared';
import { LoadBalancerService } from './load-balancer.service';

/**
 * Load Balancer Controller
 * 
 * Provides REST API for load balancer monitoring and management.
 */
@Controller('load-balancer')
export class LoadBalancerController {
  constructor(
    private readonly loadBalancer: LoadBalancerService,
    private readonly responseService: ResponseService
  ) {}

  /**
   * Get connection statistics for all instances
   */
  @Get('stats')
  @RequirePermissions(Permission.SYSTEM_ADMIN)
  async getConnectionStats() {
    const stats = this.loadBalancer.getConnectionStats();
    const statsArray = Array.from(stats.entries()).map(([instanceId, stat]) => ({
      instanceId,
      ...stat,
    }));

    return this.responseService.success(statsArray, 'Load balancer statistics retrieved');
  }

  /**
   * Get connection statistics for a specific instance
   */
  @Get('stats/:instanceId')
  @Public() // Allow internal monitoring
  async getInstanceStats(@Param('instanceId') instanceId: string) {
    const stats = this.loadBalancer.getInstanceConnectionStats(instanceId);
    
    if (!stats) {
      return this.responseService.notFound('Instance statistics', instanceId);
    }

    return this.responseService.success(stats, `Statistics for instance ${instanceId}`);
  }

  /**
   * Reset connection statistics
   */
  @Post('reset')
  @RequirePermissions(Permission.SYSTEM_ADMIN)
  async resetAllStats() {
    this.loadBalancer.resetConnectionStats();
    return this.responseService.success(
      { action: 'reset', scope: 'all' },
      'All connection statistics have been reset'
    );
  }

  /**
   * Reset connection statistics for a specific instance
   */
  @Post('reset/:instanceId')
  @RequirePermissions(Permission.SYSTEM_ADMIN)
  async resetInstanceStats(@Param('instanceId') instanceId: string) {
    this.loadBalancer.resetConnectionStats(instanceId);
    return this.responseService.success(
      { action: 'reset', scope: 'instance', instanceId },
      `Connection statistics for instance ${instanceId} have been reset`
    );
  }

  /**
   * Health check for load balancer
   */
  @Get('health')
  @Public()
  async health() {
    const healthStatus = this.loadBalancer.getHealthStatus();
    return this.responseService.success(healthStatus, 'Load balancer is healthy');
  }
}
