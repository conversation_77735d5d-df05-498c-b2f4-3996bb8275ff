'use client'

import React, { useState } from 'react'
import {
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  EyeIcon,
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  ShareIcon,
  UserGroupIcon,
  ClockIcon,
  CalendarIcon,
  ArrowDownTrayIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { useSocialAnalytics, useLeaderboard } from '@/hooks/useSocial'
import { LeaderboardType } from '@/types/social.types'

interface SocialAnalyticsProps {
  className?: string
}

export default function SocialAnalytics({
  className = ''
}: SocialAnalyticsProps) {
  const [timeframe, setTimeframe] = useState('30d')
  const [selectedMetric, setSelectedMetric] = useState<'engagement' | 'reach' | 'growth'>('engagement')

  const { data: analytics, isLoading } = useSocialAnalytics(timeframe)
  const { data: leaderboard } = useLeaderboard(LeaderboardType.MOST_ENGAGING, 'weekly')

  const timeframeOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ]

  const metricOptions = [
    { value: 'engagement', label: 'Engagement', icon: HeartIcon },
    { value: 'reach', label: 'Reach', icon: EyeIcon },
    { value: 'growth', label: 'Growth', icon: TrendingUpIcon }
  ]

  const formatNumber = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    return value.toLocaleString()
  }

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  if (isLoading && !analytics) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <ChartBarIcon className="h-6 w-6 mr-2" />
            Social Analytics
          </h2>
          <p className="text-sm text-gray-600">Track your social performance and engagement</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            {timeframeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <HeartIcon className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">
                  {formatNumber(analytics.metrics.totalLikes)}
                </div>
                <div className="text-sm text-gray-600">Total Likes</div>
                <div className={`text-xs flex items-center mt-1 ${
                  analytics.engagement.engagementGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {analytics.engagement.engagementGrowth >= 0 ? (
                    <TrendingUpIcon className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDownIcon className="h-3 w-3 mr-1" />
                  )}
                  {formatPercentage(analytics.engagement.engagementGrowth)}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChatBubbleOvalLeftIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">
                  {formatNumber(analytics.metrics.totalComments)}
                </div>
                <div className="text-sm text-gray-600">Total Comments</div>
                <div className="text-xs text-blue-600 mt-1">
                  {analytics.engagement.commentsPerPost.toFixed(1)} avg per post
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <EyeIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">
                  {formatNumber(analytics.metrics.reachCount)}
                </div>
                <div className="text-sm text-gray-600">Total Reach</div>
                <div className={`text-xs flex items-center mt-1 ${
                  analytics.growth.reachGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {analytics.growth.reachGrowth >= 0 ? (
                    <TrendingUpIcon className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDownIcon className="h-3 w-3 mr-1" />
                  )}
                  {formatPercentage(analytics.growth.reachGrowth)}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserGroupIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">
                  {analytics.engagement.engagementRate.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">Engagement Rate</div>
                <div className="text-xs text-purple-600 mt-1">
                  {formatNumber(analytics.metrics.averageEngagement)} avg engagement
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Metric Selection */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Performance Overview</h3>
          
          <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg">
            {metricOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setSelectedMetric(option.value as any)}
                className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  selectedMetric === option.value
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <option.icon className="h-4 w-4 mr-2" />
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Chart Placeholder */}
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div className="text-center">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">
              {selectedMetric.charAt(0).toUpperCase() + selectedMetric.slice(1)} chart
            </p>
            <p className="text-xs text-gray-500">Interactive chart would be implemented here</p>
          </div>
        </div>
      </div>

      {/* Detailed Analytics */}
      {analytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Engagement Breakdown */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Engagement Breakdown</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <HeartIcon className="h-5 w-5 text-red-500 mr-2" />
                  <span className="text-sm text-gray-700">Likes per Post</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.engagement.likesPerPost.toFixed(1)}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <ChatBubbleOvalLeftIcon className="h-5 w-5 text-blue-500 mr-2" />
                  <span className="text-sm text-gray-700">Comments per Post</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.engagement.commentsPerPost.toFixed(1)}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <ShareIcon className="h-5 w-5 text-green-500 mr-2" />
                  <span className="text-sm text-gray-700">Shares per Post</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.engagement.sharesPerPost.toFixed(1)}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <ClockIcon className="h-5 w-5 text-purple-500 mr-2" />
                  <span className="text-sm text-gray-700">Best Posting Time</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.engagement.bestPostingTime}
                </span>
              </div>
            </div>
          </div>

          {/* Growth Metrics */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Growth Metrics</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Followers Growth</span>
                <div className={`flex items-center text-sm font-medium ${
                  analytics.growth.followersGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {analytics.growth.followersGrowth >= 0 ? (
                    <TrendingUpIcon className="h-4 w-4 mr-1" />
                  ) : (
                    <TrendingDownIcon className="h-4 w-4 mr-1" />
                  )}
                  {formatPercentage(analytics.growth.followersGrowth)}
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Engagement Growth</span>
                <div className={`flex items-center text-sm font-medium ${
                  analytics.growth.engagementGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {analytics.growth.engagementGrowth >= 0 ? (
                    <TrendingUpIcon className="h-4 w-4 mr-1" />
                  ) : (
                    <TrendingDownIcon className="h-4 w-4 mr-1" />
                  )}
                  {formatPercentage(analytics.growth.engagementGrowth)}
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Reach Growth</span>
                <div className={`flex items-center text-sm font-medium ${
                  analytics.growth.reachGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {analytics.growth.reachGrowth >= 0 ? (
                    <TrendingUpIcon className="h-4 w-4 mr-1" />
                  ) : (
                    <TrendingDownIcon className="h-4 w-4 mr-1" />
                  )}
                  {formatPercentage(analytics.growth.reachGrowth)}
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Period Comparison</span>
                <div className={`flex items-center text-sm font-medium ${
                  analytics.growth.periodComparison >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {analytics.growth.periodComparison >= 0 ? (
                    <TrendingUpIcon className="h-4 w-4 mr-1" />
                  ) : (
                    <TrendingDownIcon className="h-4 w-4 mr-1" />
                  )}
                  {formatPercentage(analytics.growth.periodComparison)}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Top Content and Insights */}
      {analytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Posts */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performing Posts</h3>
            
            <div className="space-y-4">
              {analytics.topPosts.slice(0, 3).map((post, index) => (
                <div key={post.id} className="flex items-start space-x-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    index === 0 ? 'bg-yellow-100 text-yellow-800' :
                    index === 1 ? 'bg-gray-100 text-gray-800' :
                    'bg-orange-100 text-orange-800'
                  }`}>
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900 line-clamp-2">{post.content}</p>
                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                      <span>{post.engagement.likesCount} likes</span>
                      <span>{post.engagement.commentsCount} comments</span>
                      <span>{post.engagement.sharesCount} shares</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Insights */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Insights & Recommendations</h3>
            
            <div className="space-y-4">
              {analytics.insights.map((insight, index) => (
                <div key={index} className={`p-3 rounded-lg border-l-4 ${
                  insight.type === 'tip' ? 'border-blue-500 bg-blue-50' :
                  insight.type === 'warning' ? 'border-yellow-500 bg-yellow-50' :
                  insight.type === 'opportunity' ? 'border-green-500 bg-green-50' :
                  'border-purple-500 bg-purple-50'
                }`}>
                  <div className="flex items-start">
                    <InformationCircleIcon className={`h-5 w-5 mr-2 mt-0.5 ${
                      insight.type === 'tip' ? 'text-blue-600' :
                      insight.type === 'warning' ? 'text-yellow-600' :
                      insight.type === 'opportunity' ? 'text-green-600' :
                      'text-purple-600'
                    }`} />
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{insight.title}</h4>
                      <p className="text-sm text-gray-700 mt-1">{insight.description}</p>
                      {insight.actionable && insight.action && (
                        <button className="text-xs text-blue-600 hover:text-blue-700 mt-2">
                          {insight.action}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Leaderboard */}
      {leaderboard && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Weekly Engagement Leaderboard</h3>
          
          <div className="space-y-3">
            {leaderboard.entries.slice(0, 5).map((entry) => (
              <div key={entry.user.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                    entry.rank === 1 ? 'bg-yellow-100 text-yellow-800' :
                    entry.rank === 2 ? 'bg-gray-100 text-gray-800' :
                    entry.rank === 3 ? 'bg-orange-100 text-orange-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {entry.rank}
                  </div>
                  <img
                    src={entry.user.avatar || '/default-avatar.png'}
                    alt={entry.user.displayName}
                    className="w-8 h-8 rounded-full"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{entry.user.displayName}</div>
                    <div className="text-xs text-gray-600">@{entry.user.username}</div>
                  </div>
                </div>
                
                <div className="text-sm font-medium text-gray-600">
                  {formatNumber(entry.score)} points
                </div>
              </div>
            ))}
          </div>
          
          {leaderboard.userRank && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                Your rank: <span className="font-medium">#{leaderboard.userRank}</span> of {leaderboard.totalParticipants}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
