import { <PERSON>, <PERSON>, Req, <PERSON><PERSON>, <PERSON>m, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { 
  ResponseService, 
  RequirePermissions, 
  Permission, 
  Public 
} from '@shared';
import { ProxyV2Service } from './proxy-v2.service';

@Controller('proxy-v2')
export class ProxyV2Controller {
  private readonly logger = new Logger(ProxyV2Controller.name);

  constructor(
    private readonly proxyV2Service: ProxyV2Service,
    private readonly responseService: ResponseService,
  ) {}

  @Public()
  @All(':serviceName/*')
  async proxyRequest(
    @Param('serviceName') serviceName: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      await this.proxyV2Service.proxyRequest(serviceName, req, res);
    } catch (error) {
      this.logger.error(`Proxy error for ${serviceName}:`, error);
      return this.responseService.error(res, 'Proxy request failed', error.message);
    }
  }

  @RequirePermissions(Permission.SYSTEM_ADMIN)
  @All('stats')
  async getProxyStats() {
    const stats = await this.proxyV2Service.getProxyStats();
    return this.responseService.success(stats, 'Proxy statistics retrieved');
  }
}
