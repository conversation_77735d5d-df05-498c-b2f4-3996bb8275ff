'use client'

import React, { useState } from 'react'
import {
  XMarkIcon,
  UserGroupIcon,
  DocumentTextIcon,
  UserIcon,
  Cog6ToothIcon,
  ShareIcon,
  FlagIcon,
  CheckCircleIcon,
  GlobeAltIcon,
  LockClosedIcon,
  EyeSlashIcon,
  CalendarIcon,
  MapPinIcon,
  HashtagIcon
} from '@heroicons/react/24/outline'
import { Community, CommunityType, CommunityVisibility } from '@/types/social.types'
import { useCommunityPosts, useCommunityMembers } from '@/hooks/useSocial'

interface CommunityDetailModalProps {
  community: Community
  onClose: () => void
  onJoin?: () => void
  onLeave?: () => void
  className?: string
}

export default function CommunityDetailModal({
  community,
  onClose,
  onJoin,
  onLeave,
  className = ''
}: CommunityDetailModalProps) {
  const [activeTab, setActiveTab] = useState<'about' | 'posts' | 'members' | 'rules'>('about')
  const [showFullDescription, setShowFullDescription] = useState(false)

  const { data: posts } = useCommunityPosts(community.id, 10, 0)
  const { data: members } = useCommunityMembers(community.id, 20, 0)

  const tabs = [
    { id: 'about', name: 'About', icon: DocumentTextIcon },
    { id: 'posts', name: 'Posts', icon: DocumentTextIcon, count: community.postCount },
    { id: 'members', name: 'Members', icon: UserIcon, count: community.memberCount },
    { id: 'rules', name: 'Rules', icon: FlagIcon, count: community.rules?.length || 0 }
  ]

  const getTypeIcon = (type: CommunityType) => {
    switch (type) {
      case CommunityType.PUBLIC:
        return <GlobeAltIcon className="h-4 w-4 text-green-600" />
      case CommunityType.PRIVATE:
        return <LockClosedIcon className="h-4 w-4 text-red-600" />
      case CommunityType.INVITE_ONLY:
        return <EyeSlashIcon className="h-4 w-4 text-yellow-600" />
      default:
        return <GlobeAltIcon className="h-4 w-4 text-gray-600" />
    }
  }

  const getVisibilityIcon = (visibility: CommunityVisibility) => {
    switch (visibility) {
      case CommunityVisibility.PUBLIC:
        return <GlobeAltIcon className="h-4 w-4 text-green-600" />
      case CommunityVisibility.UNLISTED:
        return <EyeSlashIcon className="h-4 w-4 text-yellow-600" />
      case CommunityVisibility.PRIVATE:
        return <LockClosedIcon className="h-4 w-4 text-red-600" />
      default:
        return <GlobeAltIcon className="h-4 w-4 text-gray-600" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const isCurrentUserMember = true // This would come from context or props
  const isCurrentUserOwner = community.creator.id === 'current-user-id'

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden ${className}`}>
        {/* Header */}
        <div className="relative">
          {/* Banner */}
          {community.banner && (
            <div className="h-32 bg-gradient-to-r from-blue-500 to-purple-600 overflow-hidden">
              <img
                src={community.banner}
                alt={`${community.name} banner`}
                className="w-full h-full object-cover"
              />
            </div>
          )}
          
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>

          {/* Community Info */}
          <div className="px-6 pb-4">
            <div className="flex items-start space-x-4 -mt-8">
              <img
                src={community.avatar || '/default-community.png'}
                alt={community.name}
                className="w-16 h-16 rounded-lg border-4 border-white bg-white"
              />
              
              <div className="flex-1 mt-8">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900 flex items-center">
                      {community.name}
                      {community.isVerified && (
                        <CheckCircleIcon className="h-5 w-5 text-blue-500 ml-2" />
                      )}
                    </h2>
                    <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                      <div className="flex items-center">
                        {getTypeIcon(community.type)}
                        <span className="ml-1 capitalize">{community.type}</span>
                      </div>
                      <div className="flex items-center">
                        {getVisibilityIcon(community.visibility)}
                        <span className="ml-1 capitalize">{community.visibility}</span>
                      </div>
                      <span className="capitalize">{community.category}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {isCurrentUserOwner ? (
                      <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <Cog6ToothIcon className="h-4 w-4 mr-2" />
                        Manage
                      </button>
                    ) : isCurrentUserMember ? (
                      <button
                        onClick={onLeave}
                        className="inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                      >
                        Leave Community
                      </button>
                    ) : (
                      <button
                        onClick={onJoin}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        Join Community
                      </button>
                    )}
                    
                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      <ShareIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 px-6">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {/* About Tab */}
          {activeTab === 'about' && (
            <div className="space-y-6">
              {/* Description */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">About</h3>
                <div className="text-gray-700">
                  {showFullDescription || community.description.length <= 200 ? (
                    <p>{community.description}</p>
                  ) : (
                    <>
                      <p>{community.description.substring(0, 200)}...</p>
                      <button
                        onClick={() => setShowFullDescription(true)}
                        className="text-blue-600 hover:text-blue-700 text-sm mt-1"
                      >
                        Show more
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{community.memberCount}</div>
                  <div className="text-sm text-gray-600">Members</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{community.postCount}</div>
                  <div className="text-sm text-gray-600">Posts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {community.analytics?.engagementRate.toFixed(1) || '0.0'}%
                  </div>
                  <div className="text-sm text-gray-600">Engagement</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {community.analytics?.growthRate.toFixed(1) || '0.0'}%
                  </div>
                  <div className="text-sm text-gray-600">Growth</div>
                </div>
              </div>

              {/* Details */}
              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Created {formatDate(community.createdAt)}
                </div>
                
                <div className="flex items-center text-sm text-gray-600">
                  <UserIcon className="h-4 w-4 mr-2" />
                  Created by @{community.creator.username}
                </div>
                
                {community.location && (
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPinIcon className="h-4 w-4 mr-2" />
                    {community.location}
                  </div>
                )}
              </div>

              {/* Tags */}
              {community.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {community.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        <HashtagIcon className="h-3 w-3 mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Posts Tab */}
          {activeTab === 'posts' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Recent Posts</h3>
              
              {posts?.posts.length ? (
                <div className="space-y-4">
                  {posts.posts.map((post) => (
                    <div key={post.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <img
                          src={post.author.avatar || '/default-avatar.png'}
                          alt={post.author.displayName}
                          className="w-10 h-10 rounded-full"
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="text-sm font-medium text-gray-900">{post.author.displayName}</h4>
                            <span className="text-sm text-gray-500">@{post.author.username}</span>
                            <span className="text-sm text-gray-500">•</span>
                            <span className="text-sm text-gray-500">
                              {new Date(post.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                          
                          <p className="text-sm text-gray-900 mb-2">{post.content}</p>
                          
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span>{post.engagement.likesCount} likes</span>
                            <span>{post.engagement.commentsCount} comments</span>
                            <span>{post.engagement.sharesCount} shares</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <DocumentTextIcon className="mx-auto h-12 w-12 mb-2" />
                  <p>No posts yet</p>
                </div>
              )}
            </div>
          )}

          {/* Members Tab */}
          {activeTab === 'members' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Members</h3>
              
              {members?.members.length ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {members.members.map((member) => (
                    <div key={member.user.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                      <img
                        src={member.user.avatar || '/default-avatar.png'}
                        alt={member.user.displayName}
                        className="w-10 h-10 rounded-full"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">{member.user.displayName}</div>
                        <div className="text-xs text-gray-600">@{member.user.username}</div>
                        <div className="text-xs text-gray-500 capitalize">{member.role}</div>
                      </div>
                      {member.role === 'owner' && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Owner
                        </span>
                      )}
                      {member.role === 'moderator' && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Mod
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <UserGroupIcon className="mx-auto h-12 w-12 mb-2" />
                  <p>No members to display</p>
                </div>
              )}
            </div>
          )}

          {/* Rules Tab */}
          {activeTab === 'rules' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Community Rules</h3>
              
              {community.rules && community.rules.length > 0 ? (
                <div className="space-y-3">
                  {community.rules.map((rule, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </div>
                      <p className="text-sm text-gray-900 flex-1">{rule}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FlagIcon className="mx-auto h-12 w-12 mb-2" />
                  <p>No rules have been set</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
