import { Module } from '@nestjs/common';
import { ProxyV2Service } from './proxy-v2.service';
import { ProxyV2Controller } from './proxy-v2.controller';
import { ServiceDiscoveryModule } from '../service-discovery/service-discovery.module';
import { CircuitBreakerModule } from '../circuit-breaker/circuit-breaker.module';

/**
 * Enhanced Proxy Module V2
 * 
 * Provides enterprise-grade proxy functionality with:
 * - Service discovery integration
 * - Circuit breaker protection
 * - Load balancing
 * - Comprehensive monitoring
 */
@Module({
  imports: [
    ServiceDiscoveryModule,
    CircuitBreakerModule,
  ],
  providers: [ProxyV2Service],
  controllers: [ProxyV2Controller],
  exports: [ProxyV2Service],
})
export class ProxyV2Module {}
