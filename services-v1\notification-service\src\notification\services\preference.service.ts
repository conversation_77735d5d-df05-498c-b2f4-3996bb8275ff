import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

export interface NotificationPreference {
  id?: string;
  userId: string;
  category: string;
  channels: string[];
  isEnabled: boolean;
  quietHours?: {
    enabled: boolean;
    startTime: string; // HH:MM format
    endTime: string;   // HH:MM format
    timezone: string;
  };
}

export interface UserPreferences {
  userId: string;
  preferences: NotificationPreference[];
  globalSettings: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    pushEnabled: boolean;
    inAppEnabled: boolean;
    marketingEnabled: boolean;
  };
}

@Injectable()
export class PreferenceService {
  private readonly logger = new Logger(PreferenceService.name);

  constructor(private readonly prisma: PrismaService) {}

  async getUserPreferences(userId: string): Promise<UserPreferences> {
    try {
      this.logger.log(`Getting preferences for user: ${userId}`);

      // Mock implementation
      const mockPreferences: UserPreferences = {
        userId,
        preferences: [
          {
            id: `pref_${userId}_account`,
            userId,
            category: 'account',
            channels: ['email', 'push'],
            isEnabled: true,
            quietHours: {
              enabled: true,
              startTime: '22:00',
              endTime: '08:00',
              timezone: 'UTC',
            },
          },
          {
            id: `pref_${userId}_nft`,
            userId,
            category: 'nft',
            channels: ['email', 'push', 'sms'],
            isEnabled: true,
          },
          {
            id: `pref_${userId}_marketplace`,
            userId,
            category: 'marketplace',
            channels: ['email', 'push'],
            isEnabled: true,
          },
          {
            id: `pref_${userId}_marketing`,
            userId,
            category: 'marketing',
            channels: ['email'],
            isEnabled: false,
          },
        ],
        globalSettings: {
          emailEnabled: true,
          smsEnabled: true,
          pushEnabled: true,
          inAppEnabled: true,
          marketingEnabled: false,
        },
      };

      return mockPreferences;

      // Real implementation would be:
      // const preferences = await this.prisma.notificationPreference.findMany({
      //   where: { userId },
      // });
      // 
      // return {
      //   userId,
      //   preferences,
      //   globalSettings: this.getGlobalSettings(preferences),
      // };
    } catch (error) {
      this.logger.error(`Failed to get user preferences: ${error.message}`, error);
      throw error;
    }
  }

  async updateUserPreference(userId: string, category: string, preference: Partial<NotificationPreference>): Promise<NotificationPreference> {
    try {
      this.logger.log(`Updating preference for user ${userId}, category: ${category}`);

      // Mock implementation
      const updatedPreference: NotificationPreference = {
        id: `pref_${userId}_${category}`,
        userId,
        category,
        channels: preference.channels || ['email'],
        isEnabled: preference.isEnabled !== undefined ? preference.isEnabled : true,
        quietHours: preference.quietHours,
      };

      this.logger.log(`Preference updated successfully for user ${userId}`);
      return updatedPreference;

      // Real implementation would be:
      // return await this.prisma.notificationPreference.upsert({
      //   where: {
      //     userId_category: {
      //       userId,
      //       category,
      //     },
      //   },
      //   update: preference,
      //   create: {
      //     userId,
      //     category,
      //     ...preference,
      //   },
      // });
    } catch (error) {
      this.logger.error(`Failed to update user preference: ${error.message}`, error);
      throw error;
    }
  }

  async setGlobalPreference(userId: string, setting: string, enabled: boolean): Promise<boolean> {
    try {
      this.logger.log(`Setting global preference for user ${userId}: ${setting} = ${enabled}`);

      // Mock implementation
      this.logger.log(`Global preference updated successfully for user ${userId}`);
      return true;

      // Real implementation would update all relevant preferences
      // based on the global setting
    } catch (error) {
      this.logger.error(`Failed to set global preference: ${error.message}`, error);
      return false;
    }
  }

  async shouldSendNotification(userId: string, category: string, channel: string): Promise<boolean> {
    try {
      const userPreferences = await this.getUserPreferences(userId);
      
      // Check global settings first
      const globalEnabled = this.isChannelGloballyEnabled(userPreferences.globalSettings, channel);
      if (!globalEnabled) {
        return false;
      }

      // Check category-specific preferences
      const categoryPreference = userPreferences.preferences.find(p => p.category === category);
      if (!categoryPreference) {
        // Default to enabled if no specific preference is set
        return true;
      }

      if (!categoryPreference.isEnabled) {
        return false;
      }

      if (!categoryPreference.channels.includes(channel)) {
        return false;
      }

      // Check quiet hours
      if (categoryPreference.quietHours?.enabled) {
        const isQuietTime = this.isQuietTime(categoryPreference.quietHours);
        if (isQuietTime) {
          this.logger.log(`Notification blocked due to quiet hours for user ${userId}`);
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to check notification permission: ${error.message}`, error);
      // Default to allowing notifications if there's an error
      return true;
    }
  }

  async getDefaultPreferences(userId: string): Promise<NotificationPreference[]> {
    return [
      {
        userId,
        category: 'account',
        channels: ['email', 'push'],
        isEnabled: true,
        quietHours: {
          enabled: true,
          startTime: '22:00',
          endTime: '08:00',
          timezone: 'UTC',
        },
      },
      {
        userId,
        category: 'nft',
        channels: ['email', 'push'],
        isEnabled: true,
      },
      {
        userId,
        category: 'marketplace',
        channels: ['email', 'push'],
        isEnabled: true,
      },
      {
        userId,
        category: 'system',
        channels: ['email'],
        isEnabled: true,
      },
      {
        userId,
        category: 'marketing',
        channels: ['email'],
        isEnabled: false,
      },
    ];
  }

  private isChannelGloballyEnabled(globalSettings: UserPreferences['globalSettings'], channel: string): boolean {
    switch (channel) {
      case 'email':
        return globalSettings.emailEnabled;
      case 'sms':
        return globalSettings.smsEnabled;
      case 'push':
        return globalSettings.pushEnabled;
      case 'in_app':
        return globalSettings.inAppEnabled;
      default:
        return true;
    }
  }

  private isQuietTime(quietHours: NotificationPreference['quietHours']): boolean {
    if (!quietHours || !quietHours.enabled) {
      return false;
    }

    try {
      const now = new Date();
      const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

      const startTime = quietHours.startTime;
      const endTime = quietHours.endTime;

      // Handle overnight quiet hours (e.g., 22:00 to 08:00)
      if (startTime > endTime) {
        return currentTime >= startTime || currentTime <= endTime;
      } else {
        return currentTime >= startTime && currentTime <= endTime;
      }
    } catch (error) {
      this.logger.error(`Failed to check quiet time: ${error.message}`, error);
      return false;
    }
  }

  private getGlobalSettings(preferences: NotificationPreference[]): UserPreferences['globalSettings'] {
    // Derive global settings from individual preferences
    const hasEmailEnabled = preferences.some(p => p.isEnabled && p.channels.includes('email'));
    const hasSMSEnabled = preferences.some(p => p.isEnabled && p.channels.includes('sms'));
    const hasPushEnabled = preferences.some(p => p.isEnabled && p.channels.includes('push'));
    const hasInAppEnabled = preferences.some(p => p.isEnabled && p.channels.includes('in_app'));
    const hasMarketingEnabled = preferences.some(p => p.category === 'marketing' && p.isEnabled);

    return {
      emailEnabled: hasEmailEnabled,
      smsEnabled: hasSMSEnabled,
      pushEnabled: hasPushEnabled,
      inAppEnabled: hasInAppEnabled,
      marketingEnabled: hasMarketingEnabled,
    };
  }
}
