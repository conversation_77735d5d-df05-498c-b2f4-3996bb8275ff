-- User Service V2 Database Setup
-- Database Per Service Pattern - Creates user_service_v2 database

-- Create database for User Service V2
CREATE DATABASE user_service_v2;

-- Connect to the database
\c user_service_v2;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE user_service_v2 TO postgres;

-- Create schema if needed (optional)
-- CREATE SCHEMA IF NOT EXISTS user_service;

-- Note: Tables will be created by Prisma migrations
-- This script only sets up the database and basic configuration

COMMENT ON DATABASE user_service_v2 IS 'User Service V2 Database - Database Per Service Pattern';
