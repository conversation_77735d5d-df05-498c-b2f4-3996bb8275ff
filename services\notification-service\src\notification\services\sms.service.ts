import { Injectable, Logger } from '@nestjs/common';
import { AppConfig } from '../../config/app.config';

export interface SMSNotification {
  to: string;
  message: string;
  from?: string;
}

@Injectable()
export class SMSService {
  private readonly logger = new Logger(SMSService.name);
  private twilioClient: any;

  constructor(private readonly config: AppConfig) {
    this.initializeTwilioClient();
  }

  private initializeTwilioClient() {
    try {
      const smsConfig = this.config.sms;
      
      if (!smsConfig.accountSid || !smsConfig.authToken) {
        this.logger.warn('SMS configuration not complete, using mock client');
        this.twilioClient = null;
        return;
      }

      // In a real implementation, you would import and initialize Twilio client here
      // const twilio = require('twilio');
      // this.twilioClient = twilio(smsConfig.accountSid, smsConfig.authToken);
      
      this.logger.log('SMS client initialized successfully (mock)');
    } catch (error) {
      this.logger.error('Failed to initialize SMS client', error);
      this.twilioClient = null;
    }
  }

  async sendSMS(notification: SMSNotification): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      this.logger.log(`Sending SMS to: ${notification.to}`);

      if (!this.twilioClient) {
        // Mock SMS sending for development
        this.logger.log(`Mock SMS sent to ${notification.to}: ${notification.message}`);
        return {
          success: true,
          messageId: `mock_sms_${Date.now()}`,
        };
      }

      // Real Twilio implementation would be:
      // const message = await this.twilioClient.messages.create({
      //   body: notification.message,
      //   from: notification.from || this.config.sms.phoneNumber,
      //   to: notification.to,
      // });

      const mockMessageId = `mock_sms_${Date.now()}`;
      this.logger.log(`SMS sent successfully: ${mockMessageId}`);
      
      return {
        success: true,
        messageId: mockMessageId,
      };
    } catch (error) {
      this.logger.error(`Failed to send SMS: ${error.message}`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async sendBulkSMS(notifications: SMSNotification[]): Promise<{
    success: boolean;
    sent: number;
    failed: number;
    results: Array<{ success: boolean; messageId?: string; error?: string }>;
  }> {
    this.logger.log(`Sending bulk SMS: ${notifications.length} messages`);

    const results = await Promise.allSettled(
      notifications.map(notification => this.sendSMS(notification))
    );

    const processedResults = results.map(result => 
      result.status === 'fulfilled' ? result.value : { success: false, error: 'Promise rejected' }
    );

    const sent = processedResults.filter(r => r.success).length;
    const failed = processedResults.filter(r => !r.success).length;

    this.logger.log(`Bulk SMS results: ${sent} sent, ${failed} failed`);

    return {
      success: failed === 0,
      sent,
      failed,
      results: processedResults,
    };
  }

  async sendVerificationCode(phoneNumber: string, code: string): Promise<{ success: boolean; error?: string }> {
    const smsNotification: SMSNotification = {
      to: phoneNumber,
      message: `Your Social NFT Platform verification code is: ${code}. This code expires in 10 minutes.`,
    };

    return await this.sendSMS(smsNotification);
  }

  async sendNFTAlert(phoneNumber: string, message: string): Promise<{ success: boolean; error?: string }> {
    const smsNotification: SMSNotification = {
      to: phoneNumber,
      message: `Social NFT Platform Alert: ${message}`,
    };

    return await this.sendSMS(smsNotification);
  }

  async sendSecurityAlert(phoneNumber: string, alertType: string): Promise<{ success: boolean; error?: string }> {
    const smsNotification: SMSNotification = {
      to: phoneNumber,
      message: `Security Alert: ${alertType} detected on your Social NFT Platform account. If this wasn't you, please secure your account immediately.`,
    };

    return await this.sendSMS(smsNotification);
  }

  async sendMarketplaceNotification(phoneNumber: string, type: 'bid' | 'sale' | 'listing', details: string): Promise<{ success: boolean; error?: string }> {
    let message = '';
    
    switch (type) {
      case 'bid':
        message = `New bid received: ${details}`;
        break;
      case 'sale':
        message = `NFT sold: ${details}`;
        break;
      case 'listing':
        message = `NFT listed: ${details}`;
        break;
    }

    const smsNotification: SMSNotification = {
      to: phoneNumber,
      message: `Social NFT Platform: ${message}`,
    };

    return await this.sendSMS(smsNotification);
  }

  isConfigured(): boolean {
    const smsConfig = this.config.sms;
    return !!(smsConfig.accountSid && smsConfig.authToken && smsConfig.phoneNumber);
  }
}
