# Development Environment Setup Guide

## Overview
This guide provides step-by-step instructions for setting up the Social NFT Platform v2 development environment.

**Last Updated:** May 25, 2025
**Platform:** Windows 10/11
**Architecture:** Microservices with API Gateway

## Prerequisites

### Required Software

#### 1. Node.js
- **Version:** 18.x or higher
- **Download:** https://nodejs.org/
- **Verification:**
  ```bash
  node --version  # Should show v18.x.x or higher
  npm --version   # Should show 9.x.x or higher
  ```

#### 2. PostgreSQL
- **Version:** 14.x or higher
- **Download:** https://www.postgresql.org/download/
- **Configuration:**
  - Default port: 5432
  - Create database: `social_nft_platform`
  - Create user with full permissions
- **Verification:**
  ```bash
  psql --version  # Should show PostgreSQL 14.x
  ```

#### 3. Git
- **Version:** Latest stable
- **Download:** https://git-scm.com/
- **Verification:**
  ```bash
  git --version  # Should show git version 2.x.x
  ```

#### 4. Code Editor
- **Recommended:** Visual Studio Code
- **Extensions:**
  - TypeScript and JavaScript Language Features
  - ESLint
  - Prettier
  - REST Client (for API testing)

### Optional Tools

#### 1. Database Management
- **pgAdmin 4** - PostgreSQL administration
- **DBeaver** - Universal database tool

#### 2. API Testing
- **Postman** - API development environment
- **Insomnia** - REST client
- **curl** - Command line HTTP client

#### 3. Development Tools
- **Docker** - For containerization (future use)
- **Turbo** - Monorepo build system

## Project Structure Overview

```
social-nft-platform-v2/
├── services/                    # Microservices
│   ├── api-gateway/            # API Gateway (Port 3010)
│   ├── user-service/           # User management (Port 3011)
│   ├── profile-analysis-service/ # Twitter analysis (Port 3002)
│   ├── project-service/        # Project management (Port 3005)
│   ├── nft-generation-service/ # NFT creation (Port 3003)
│   ├── blockchain-service/     # Blockchain integration (Port 3004)
│   ├── marketplace-service/    # NFT trading (Port 3006)
│   ├── notification-service/   # Notifications (Port 3008)
│   └── analytics-service/      # Platform analytics (Port 3009)
├── frontend/                   # React frontend (Port 3100)
├── docs/                       # Documentation
│   ├── development/           # Development guides
│   ├── api/                   # API documentation
│   └── architecture/          # Architecture docs
├── shared/                     # Shared libraries
└── tools/                      # Development tools
```

## Environment Variables Setup

### Global Environment Variables
Create `.env` file in project root:
```bash
# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=social_nft_platform
DATABASE_USERNAME=your_db_user
DATABASE_PASSWORD=your_db_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Environment
NODE_ENV=development

# API Configuration
API_GATEWAY_PORT=3010
FRONTEND_PORT=3100

# Rate Limiting
THROTTLE_TTL=60000
THROTTLE_LIMIT=100

# CORS Origins
CORS_ORIGINS=http://localhost:3000,http://localhost:3100,http://localhost:3001,http://localhost:3002
```

### Service-Specific Environment Variables

#### API Gateway (.env)
```bash
PORT=3010
SERVICE_NAME=api-gateway
USER_SERVICE_URL=http://localhost:3001
PROFILE_ANALYSIS_SERVICE_URL=http://localhost:3002
```

#### User Service (.env)
```bash
PORT=3001
SERVICE_NAME=user-service
DATABASE_URL=postgresql://username:password@localhost:5432/social_nft_platform
```

#### Profile Analysis Service (.env)
```bash
PORT=3002
SERVICE_NAME=profile-analysis-service
DATABASE_URL=postgresql://username:password@localhost:5432/social_nft_platform
```

#### Frontend (.env.local)
```bash
NEXT_PUBLIC_API_URL=http://localhost:3010
NEXT_PUBLIC_APP_NAME=Social NFT Platform
```

## Database Setup

### 1. Create Database
```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE social_nft_platform;
CREATE USER social_nft_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE social_nft_platform TO social_nft_user;
```

### 2. Verify Connection
```bash
psql -h localhost -p 5432 -U social_nft_user -d social_nft_platform
```

### 3. Database Schema
The services will automatically create tables using TypeORM migrations when first started.

## Port Allocation

### Current Services (✅ ALL CONFLICTS RESOLVED)
- **3002:** Profile Analysis Service ✅
- **3003:** NFT Generation Service ✅
- **3004:** Blockchain Service ✅
- **3005:** Project Service ✅
- **3006:** Marketplace Service ✅
- **3008:** Notification Service ✅
- **3009:** Analytics Service ✅
- **3010:** API Gateway ✅
- **3011:** User Service ✅
- **3100:** Frontend ✅

### ✅ SUCCESS: All Port Conflicts Resolved!
All services now have unique ports and can run simultaneously.

### Reserved Ports
- **3000:** Available for future use
- **5432:** PostgreSQL Database
- **5000-5999:** Reserved for additional services

## Development Workflow

### 1. Terminal Setup
Recommended to use 4 terminal windows:
- **Terminal 1:** API Gateway
- **Terminal 2:** User Service
- **Terminal 3:** Profile Analysis Service
- **Terminal 4:** Frontend

### 2. Service Start Order
1. Start database (PostgreSQL)
2. Start User Service
3. Start Profile Analysis Service
4. Start API Gateway
5. Start Frontend

### 3. Health Check Verification
After starting all services, verify:
```bash
curl http://localhost:3010/api/health
```

Should return status of all services.

## Quick Start Commands

### One-Time Setup
```bash
# 1. Clone and navigate
git clone <repository-url>
cd social-nft-platform-v2

# 2. Install all dependencies
npm run install:all

# 3. Setup environment files
npm run setup:env

# 4. Setup database
npm run setup:db
```

### Daily Development Startup
```bash
# Start all services (requires 4 terminals)
npm run dev:user-service      # Terminal 1
npm run dev:analysis-service  # Terminal 2
npm run dev:api-gateway      # Terminal 3
npm run dev:frontend         # Terminal 4
```

### Verification Commands
```bash
# Check all services health
npm run health:check

# Test API endpoints
npm run test:api

# Check service logs
npm run logs:all
```

## Development Scripts

### Package.json Scripts (Root Level)
```json
{
  "scripts": {
    "install:all": "npm run install:services && npm run install:frontend",
    "install:services": "cd services/api-gateway && npm install && cd ../user-service && npm install && cd ../profile-analysis-service && npm install",
    "install:frontend": "cd frontend && npm install",
    "dev:user-service": "cd services/user-service && npm run start:dev",
    "dev:analysis-service": "cd services/profile-analysis-service && npm run start:dev",
    "dev:api-gateway": "cd services/api-gateway && npm run start:dev",
    "dev:frontend": "cd frontend && npm start",
    "health:check": "curl http://localhost:3010/api/health",
    "test:api": "npm run test:registration && npm run test:login",
    "test:registration": "curl -X POST http://localhost:3010/api/users/register -H 'Content-Type: application/json' -d '{\"username\":\"testuser\",\"email\":\"<EMAIL>\",\"password\":\"password123\"}'",
    "test:login": "curl -X POST http://localhost:3010/api/users/login -H 'Content-Type: application/json' -d '{\"email\":\"<EMAIL>\",\"password\":\"password123\"}'",
    "setup:env": "echo 'Copy .env.example files to .env in each service directory'",
    "setup:db": "echo 'Create PostgreSQL database: social_nft_platform'"
  }
}
```

## Installation Instructions

### Step 1: Clone Repository
```bash
git clone <repository-url>
cd social-nft-platform-v2
```

### Step 2: Install Dependencies

#### Install All Service Dependencies
```bash
# API Gateway
cd services/api-gateway
npm install
cd ../..

# User Service
cd services/user-service
npm install
cd ../..

# Profile Analysis Service
cd services/profile-analysis-service
npm install
cd ../..

# Frontend
cd frontend
npm install
cd ..
```

### Step 3: Configure Environment Variables
1. Copy environment templates:
   ```bash
   cp .env.example .env
   cp services/api-gateway/.env.example services/api-gateway/.env
   cp services/user-service/.env.example services/user-service/.env
   cp services/profile-analysis-service/.env.example services/profile-analysis-service/.env
   cp frontend/.env.example frontend/.env.local
   ```

2. Edit each `.env` file with your configuration

### Step 4: Database Setup
1. Start PostgreSQL service
2. Create database and user (see Database Setup section)
3. Verify connection

### Step 5: Start Services

#### Terminal 1 - User Service
```bash
cd services/user-service
npm run start:dev
```
Wait for: "User Service is running on port 3001"

#### Terminal 2 - Profile Analysis Service
```bash
cd services/profile-analysis-service
npm run start:dev
```
Wait for: "Profile Analysis Service is running on port 3002"

#### Terminal 3 - API Gateway
```bash
cd services/api-gateway
npm run start:dev
```
Wait for: "API Gateway running on port 3010"

#### Terminal 4 - Frontend
```bash
cd frontend
npm start
```
Wait for: "Local: http://localhost:3100"

### Step 6: Verify Installation
1. **Health Check:**
   ```bash
   curl http://localhost:3010/api/health
   ```

2. **Frontend Access:**
   Open browser: http://localhost:3100

3. **API Documentation:**
   - API Gateway: http://localhost:3010/api/docs
   - User Service: http://localhost:3001/api/docs
   - Profile Analysis: http://localhost:3002/api/docs

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
**Error:** `EADDRINUSE: address already in use :::3001`
**Solution:**
```bash
# Find process using port
netstat -ano | grep :3001
# Kill process (Windows)
taskkill /PID <process-id> /F
```

#### 2. Database Connection Failed
**Error:** `Connection terminated unexpectedly`
**Solution:**
1. Verify PostgreSQL is running
2. Check database credentials in .env
3. Ensure database exists
4. Test connection manually

#### 3. CORS Errors
**Error:** `blocked by CORS policy`
**Solution:**
1. Verify API Gateway CORS configuration
2. Check frontend origin in CORS_ORIGINS
3. Restart API Gateway after changes

#### 4. Module Not Found
**Error:** `Cannot find module`
**Solution:**
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### 5. TypeScript Compilation Errors
**Error:** Various TypeScript errors
**Solution:**
```bash
# Clear TypeScript cache
rm -rf dist/
npm run build
```
