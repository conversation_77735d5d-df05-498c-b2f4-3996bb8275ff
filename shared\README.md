# 🏗️ **SHARED INFRASTRUCTURE - MICROSERVICE CHASSIS**

## **📋 OVERVIEW**

This shared infrastructure implements the **Microservice Chassis Pattern** - an industry-standard approach used by Netflix, Uber, Google, and Amazon for handling cross-cutting concerns in microservices architectures.

**Purpose**: Provide reusable, standardized infrastructure components while preserving service independence.

---

## 🎯 **DESIGN PRINCIPLES**

### **✅ WHAT WE SHARE (Cross-cutting Concerns)**
- Authentication guards and decorators
- Response formatting and interceptors
- Structured logging services
- Configuration management patterns
- Base repository patterns
- Health check implementations
- Error handling patterns

### **❌ WHAT WE DON'T SHARE (Business Logic)**
- Domain models and entities
- Business rules and logic
- Service-specific APIs
- Database schemas
- Service-specific configuration

---

## 📁 **DIRECTORY STRUCTURE**

```
shared/
├── auth/                  # Authentication infrastructure
│   ├── guards/           # JWT and permission guards
│   ├── decorators/       # Auth decorators (@Public, @RequirePermissions)
│   ├── interfaces/       # Auth interfaces and types
│   └── strategies/       # Authentication strategies
├── responses/            # Response standardization
│   ├── interfaces/       # Response interfaces
│   ├── services/         # Response formatting service
│   ├── interceptors/     # Response transformation interceptor
│   └── dto/              # Standard response DTOs
├── logging/              # Structured logging
│   ├── interfaces/       # Logging interfaces
│   ├── services/         # Logger service with business events
│   └── interceptors/     # Logging interceptors
├── config/               # Configuration management
│   ├── interfaces/       # Config interfaces
│   ├── services/         # Standardized config service
│   └── validation/       # Config validation schemas
├── data/                 # Data layer infrastructure
│   ├── repositories/     # Base repository patterns
│   ├── services/         # Prisma service wrapper
│   └── interfaces/       # Data interfaces
└── utils/                # Shared utilities
    ├── validation/       # Common validation decorators
    ├── transformers/     # Data transformers
    └── helpers/          # Utility functions
```

---

## 🚀 **USAGE IN SERVICES**

### **1. Install Shared Infrastructure**
```bash
# In each service directory
npm install ../shared
```

### **2. Import in Service Module**
```typescript
import { SharedInfrastructureModule } from '../../../shared';

@Module({
  imports: [
    SharedInfrastructureModule.forRoot({
      serviceName: 'user-service',
      version: '1.0.0',
    }),
    // ... other imports
  ],
})
export class AppModule {}
```

### **3. Use in Controllers**
```typescript
import { 
  StandardizedJwtAuthGuard,
  RequirePermissions,
  ResponseService,
  CurrentUser 
} from '../../../shared';

@Controller('users')
@UseGuards(StandardizedJwtAuthGuard)
export class UsersController {
  constructor(private readonly responseService: ResponseService) {}

  @Get()
  @RequirePermissions(Permission.USER_READ)
  async getUsers(@CurrentUser() user: AuthenticatedUser) {
    const users = await this.usersService.findAll();
    return this.responseService.success(users, 'Users retrieved successfully');
  }
}
```

---

## 🔧 **FEATURES PROVIDED**

### **Authentication & Authorization**
- JWT authentication guards
- Permission-based authorization
- Role-based access control
- Public route decorators
- User context injection

### **Response Standardization**
- Consistent response format across all services
- Automatic correlation ID generation
- Success/error response helpers
- Pagination support
- Response transformation interceptor

### **Structured Logging**
- Business event logging
- Performance metrics tracking
- Correlation ID tracking
- Service-specific context
- JSON structured format

### **Configuration Management**
- Environment-based configuration
- Type-safe configuration interfaces
- Validation schemas
- Service-specific config sections

### **Data Layer**
- Base repository patterns
- Standardized Prisma service
- Connection management
- Query performance tracking
- Transaction support

---

## 📊 **BENEFITS**

### **For Development**
- ✅ Faster service creation (hours vs days)
- ✅ Consistent patterns across services
- ✅ Reduced code duplication
- ✅ Built-in best practices
- ✅ Type-safe interfaces

### **For Maintenance**
- ✅ Centralized updates to cross-cutting concerns
- ✅ Consistent debugging experience
- ✅ Standardized monitoring and logging
- ✅ Easier onboarding for new developers

### **For Operations**
- ✅ Consistent health checks
- ✅ Standardized metrics collection
- ✅ Unified logging format
- ✅ Predictable service behavior

---

## 🔄 **VERSIONING STRATEGY**

### **Semantic Versioning**
- **Major**: Breaking changes to interfaces
- **Minor**: New features, backward compatible
- **Patch**: Bug fixes, no interface changes

### **Update Process**
1. Release new version of shared infrastructure
2. Update services incrementally
3. Test compatibility thoroughly
4. Deploy with zero downtime

---

## 🧪 **TESTING APPROACH**

### **Shared Infrastructure Tests**
- Unit tests for all components
- Integration tests for complex interactions
- Mock implementations for testing
- Performance benchmarks

### **Service Integration Tests**
- Test service with shared infrastructure
- Verify response formats
- Test authentication flows
- Validate logging output

---

**🎯 This shared infrastructure enables rapid, consistent microservice development while preserving service independence!**
