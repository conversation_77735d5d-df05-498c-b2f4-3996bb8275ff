import { 
  <PERSON>, 
  <PERSON>, 
  Req, 
  <PERSON><PERSON>, 
  Logger, 
  HttpException, 
  HttpStatus 
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ProxyService } from './proxy.service';

@ApiTags('Proxy')
@Controller()
export class ProxyController {
  private readonly logger = new Logger(ProxyController.name);

  constructor(private readonly proxyService: ProxyService) {}

  @All('users/*')
  @ApiOperation({ summary: 'Proxy requests to User Service' })
  @ApiResponse({ status: 200, description: 'Request proxied successfully' })
  @ApiResponse({ status: 503, description: 'User Service unavailable' })
  async proxyToUserService(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'user');
  }

  @All('projects/*')
  @ApiOperation({ summary: 'Proxy requests to Project Service' })
  @ApiResponse({ status: 200, description: 'Request proxied successfully' })
  @ApiResponse({ status: 503, description: 'Project Service unavailable' })
  async proxyToProjectService(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'project');
  }

  @All('nfts/*')
  @ApiOperation({ summary: 'Proxy requests to NFT Generator Service' })
  @ApiResponse({ status: 200, description: 'Request proxied successfully' })
  @ApiResponse({ status: 503, description: 'NFT Generator Service unavailable' })
  async proxyToNFTService(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'nft');
  }

  @All('blockchain/*')
  @ApiOperation({ summary: 'Proxy requests to Blockchain Service' })
  @ApiResponse({ status: 200, description: 'Request proxied successfully' })
  @ApiResponse({ status: 503, description: 'Blockchain Service unavailable' })
  async proxyToBlockchainService(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'blockchain');
  }

  @All('marketplace/*')
  @ApiOperation({ summary: 'Proxy requests to Marketplace Service' })
  @ApiResponse({ status: 200, description: 'Request proxied successfully' })
  @ApiResponse({ status: 503, description: 'Marketplace Service unavailable' })
  async proxyToMarketplaceService(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace');
  }

  @All('analytics/*')
  @ApiOperation({ summary: 'Proxy requests to Analytics Service' })
  @ApiResponse({ status: 200, description: 'Request proxied successfully' })
  @ApiResponse({ status: 503, description: 'Analytics Service unavailable' })
  async proxyToAnalyticsService(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'analytics');
  }

  @All('profile/*')
  @ApiOperation({ summary: 'Proxy requests to Profile Analysis Service' })
  @ApiResponse({ status: 200, description: 'Request proxied successfully' })
  @ApiResponse({ status: 503, description: 'Profile Analysis Service unavailable' })
  async proxyToProfileService(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'profile');
  }

  @All('notifications/*')
  @ApiOperation({ summary: 'Proxy requests to Notification Service' })
  @ApiResponse({ status: 200, description: 'Request proxied successfully' })
  @ApiResponse({ status: 503, description: 'Notification Service unavailable' })
  async proxyToNotificationService(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'notification');
  }

  /**
   * Generic proxy request handler
   */
  private async handleProxyRequest(req: Request, res: Response, serviceType: string) {
    const startTime = Date.now();
    
    try {
      // Extract the path after the service prefix
      const originalUrl = req.originalUrl;
      const apiPrefix = '/api/';
      const servicePath = originalUrl.substring(originalUrl.indexOf(apiPrefix) + apiPrefix.length);
      
      // Remove the service prefix to get the actual endpoint
      const pathParts = servicePath.split('/');
      pathParts.shift(); // Remove service name (users, projects, etc.)
      const targetPath = '/api/' + pathParts.join('/');

      this.logger.log(`Proxying ${req.method} ${originalUrl} -> ${serviceType}-service${targetPath}`);

      // Prepare proxy request
      const proxyRequest = {
        method: req.method,
        url: targetPath,
        headers: req.headers as Record<string, string>,
        body: req.body,
        query: req.query as Record<string, string>,
      };

      // Route to appropriate service
      let proxyResponse;
      switch (serviceType) {
        case 'user':
          proxyResponse = await this.proxyService.proxyToUserService(proxyRequest);
          break;
        case 'project':
          proxyResponse = await this.proxyService.proxyToProjectService(proxyRequest);
          break;
        case 'nft':
          proxyResponse = await this.proxyService.proxyToNFTService(proxyRequest);
          break;
        case 'blockchain':
          proxyResponse = await this.proxyService.proxyToBlockchainService(proxyRequest);
          break;
        case 'marketplace':
          proxyResponse = await this.proxyService.proxyToMarketplaceService(proxyRequest);
          break;
        case 'analytics':
          proxyResponse = await this.proxyService.proxyToAnalyticsService(proxyRequest);
          break;
        case 'profile':
          proxyResponse = await this.proxyService.proxyToProfileService(proxyRequest);
          break;
        case 'notification':
          proxyResponse = await this.proxyService.proxyToNotificationService(proxyRequest);
          break;
        default:
          throw new HttpException(`Unknown service type: ${serviceType}`, HttpStatus.BAD_REQUEST);
      }

      const duration = Date.now() - startTime;

      // Set response headers
      if (proxyResponse.headers) {
        Object.entries(proxyResponse.headers).forEach(([key, value]) => {
          if (key.toLowerCase() !== 'transfer-encoding' && key.toLowerCase() !== 'content-encoding') {
            res.set(key, value as string);
          }
        });
      }

      // Log the response
      this.logger.log(`Proxy completed: ${req.method} ${originalUrl} -> ${proxyResponse.status} (${duration}ms)`);

      // Send response
      res.status(proxyResponse.status).json(proxyResponse.data);

    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.error(`Proxy error: ${req.method} ${req.originalUrl} (${duration}ms)`, error.stack);

      res.status(503).json({
        error: 'Service unavailable',
        message: error.message,
        service: serviceType,
        timestamp: new Date().toISOString(),
      });
    }
  }
}
