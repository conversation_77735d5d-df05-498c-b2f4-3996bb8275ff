import {
  Controller,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Res,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { CampaignCommandService } from '../services/campaign-command.service';

@ApiTags('Campaigns')
@Controller('campaigns')
export class CampaignCommandController {
  private readonly logger = new Logger(CampaignCommandController.name);

  constructor(private readonly campaignCommandService: CampaignCommandService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new campaign',
    description: 'Create a new campaign for a project'
  })
  @ApiBody({
    description: 'Campaign creation data',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Summer NFT Campaign' },
        description: { type: 'string', example: 'A special summer campaign' },
        projectId: { type: 'string', example: 'project-123' },
        startDate: { type: 'string', format: 'date-time' },
        endDate: { type: 'string', format: 'date-time' },
      },
      required: ['name', 'description', 'projectId']
    }
  })
  @ApiResponse({ status: 201, description: 'Campaign created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid campaign data' })
  async createCampaign(@Body() campaignData: any, @Res() res: Response) {
    try {
      this.logger.log('Creating new campaign');
      
      const result = await this.campaignCommandService.createCampaign(campaignData);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      this.logger.error(`Campaign creation failed: ${error.message}`, error.stack);
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update campaign',
    description: 'Update campaign details and configuration'
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiBody({
    description: 'Campaign update data',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        description: { type: 'string' },
        status: { type: 'string' },
        startDate: { type: 'string', format: 'date-time' },
        endDate: { type: 'string', format: 'date-time' },
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Campaign updated successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  async updateCampaign(
    @Param('id') id: string,
    @Body() updateData: any,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Updating campaign: ${id}`);
      
      const result = await this.campaignCommandService.updateCampaign(id, updateData);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Campaign update failed for ID ${id}: ${error.message}`, error.stack);
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete campaign',
    description: 'Delete a campaign and all associated data'
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: 200, description: 'Campaign deleted successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  async deleteCampaign(@Param('id') id: string, @Res() res: Response) {
    try {
      this.logger.log(`Deleting campaign: ${id}`);
      
      const result = await this.campaignCommandService.deleteCampaign(id);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(`Campaign deletion failed for ID ${id}: ${error.message}`, error.stack);
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }
}
